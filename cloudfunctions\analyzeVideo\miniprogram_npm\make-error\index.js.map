{"version": 3, "sources": ["index.js"], "names": [], "mappings": ";;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.js", "sourcesContent": ["// ISC @ Julien Fontanet\n\n\n\n// ===================================================================\n\nvar construct = typeof Reflect !== \"undefined\" ? Reflect.construct : undefined;\nvar defineProperty = Object.defineProperty;\n\n// -------------------------------------------------------------------\n\nvar captureStackTrace = Error.captureStackTrace;\nif (captureStackTrace === undefined) {\n  captureStackTrace = function captureStackTrace(error) {\n    var container = new Error();\n\n    defineProperty(error, \"stack\", {\n      configurable: true,\n      get: function getStack() {\n        var stack = container.stack;\n\n        // Replace property with value for faster future accesses.\n        defineProperty(this, \"stack\", {\n          configurable: true,\n          value: stack,\n          writable: true,\n        });\n\n        return stack;\n      },\n      set: function setStack(stack) {\n        defineProperty(error, \"stack\", {\n          configurable: true,\n          value: stack,\n          writable: true,\n        });\n      },\n    });\n  };\n}\n\n// -------------------------------------------------------------------\n\nfunction BaseError(message) {\n  if (message !== undefined) {\n    defineProperty(this, \"message\", {\n      configurable: true,\n      value: message,\n      writable: true,\n    });\n  }\n\n  var cname = this.constructor.name;\n  if (cname !== undefined && cname !== this.name) {\n    defineProperty(this, \"name\", {\n      configurable: true,\n      value: cname,\n      writable: true,\n    });\n  }\n\n  captureStackTrace(this, this.constructor);\n}\n\nBaseError.prototype = Object.create(Error.prototype, {\n  // See: https://github.com/JsCommunity/make-error/issues/4\n  constructor: {\n    configurable: true,\n    value: BaseError,\n    writable: true,\n  },\n});\n\n// -------------------------------------------------------------------\n\n// Sets the name of a function if possible (depends of the JS engine).\nvar setFunctionName = (function() {\n  function setFunctionName(fn, name) {\n    return defineProperty(fn, \"name\", {\n      configurable: true,\n      value: name,\n    });\n  }\n  try {\n    var f = function() {};\n    setFunctionName(f, \"foo\");\n    if (f.name === \"foo\") {\n      return setFunctionName;\n    }\n  } catch (_) {}\n})();\n\n// -------------------------------------------------------------------\n\nfunction makeError(constructor, super_) {\n  if (super_ == null || super_ === Error) {\n    super_ = BaseError;\n  } else if (typeof super_ !== \"function\") {\n    throw new TypeError(\"super_ should be a function\");\n  }\n\n  var name;\n  if (typeof constructor === \"string\") {\n    name = constructor;\n    constructor =\n      construct !== undefined\n        ? function() {\n            return construct(super_, arguments, this.constructor);\n          }\n        : function() {\n            super_.apply(this, arguments);\n          };\n\n    // If the name can be set, do it once and for all.\n    if (setFunctionName !== undefined) {\n      setFunctionName(constructor, name);\n      name = undefined;\n    }\n  } else if (typeof constructor !== \"function\") {\n    throw new TypeError(\"constructor should be either a string or a function\");\n  }\n\n  // Also register the super constructor also as `constructor.super_` just\n  // like Node's `util.inherits()`.\n  //\n  // eslint-disable-next-line dot-notation\n  constructor.super_ = constructor[\"super\"] = super_;\n\n  var properties = {\n    constructor: {\n      configurable: true,\n      value: constructor,\n      writable: true,\n    },\n  };\n\n  // If the name could not be set on the constructor, set it on the\n  // prototype.\n  if (name !== undefined) {\n    properties.name = {\n      configurable: true,\n      value: name,\n      writable: true,\n    };\n  }\n  constructor.prototype = Object.create(super_.prototype, properties);\n\n  return constructor;\n}\nexports = module.exports = makeError;\nexports.BaseError = BaseError;\n"]}