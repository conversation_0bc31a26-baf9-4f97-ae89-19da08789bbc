/* 🚀 自定义导航栏 - Skyline渲染引擎要求 */
.custom-navbar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  height: 88rpx;
  background: #fff;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
  border-bottom: 1rpx solid #e5e5e5;
  transform: translate3d(0, 0, 0);
  will-change: transform;
}

.navbar-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
}

.content {
  display: flex;
  flex-direction: column;
  align-items: center;
  height: 100%;
  padding-top: 88rpx; /* 为自定义导航栏留出空间 */
}

.device-content {
  margin-top: 0rpx;
}

.device-list {
  display: flex;
  width: 93.3%;
  flex-direction: row;
  align-items: flex-start;
  justify-content: flex-start;
  box-sizing: border-box;
  margin-top: 20rpx;
  background-color: #fff;
}

.device-image {
  flex: 0 0 32%;
  box-sizing: border-box;
  min-height: 242rpx;
}

.device-image image {
  height: 242rpx;
  width: 242rpx;
}

.device-info {
  background-color: #fff;
  flex: 0 0 66%;
  box-sizing: border-box;
  height: 242rpx;
  margin-left: 20rpx;
}

.device-info-style view {
  font-size: 30rpx;
  margin-bottom: 15rpx;
  width: 460rpx;
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
  margin-top: 10rpx;
}

.device-info-style {
  height: 244rpx;
  background-color: white;
}

.line {
  width: 100%;
  border: 1px solid #eee;
}

.device-intro {
  width: 95%;
}

.device-title {
  display: block;
  font-size: 30rpx;
  background-color: #E5E5E5;
  color: #323232;
  height: 100%;
  border-left: 1px solid red;
  border-bottom: 1px solid #E5E5E5;
  width: 720rpx;
}

.space-line-30 {
  display: block;
  height: 30px;
}

.device-msg {
  font-size: 30rpx;
  text-indent: 50rpx;
  color: #323232;
  width: 40%;
}

.btn-area {
  position: fixed;
  left: 35rpx;
  bottom: 0;
  width: 90%;
}

.btn-area button {
  margin-top: 20rpx;
  margin-bottom: 20rpx;
  width: 100%;
}

.scroll-content {
  width: 720rpx;
  position: fixed;
  left: 15rpx;
  top: 300rpx;
}

.add-device-container {
  display: block;
}

.add-device-container label {
  float: left;
  height: 55rpx;
  line-height: 55rpx;
}

.add-device-qty {
  border: 1px solid orange;
  padding-left: 20rpx;
  width: 40%;
  float: left;
}

/* 容器样式 */
.container {
  padding: 20rpx;
  background-color: #f5f6fa;
  min-height: 100vh;
}

/* 设备基本信息卡片 */
.device-card {
  background: #fff;
  border-radius: 12rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
  margin-bottom: 20rpx;
  overflow: hidden;
}

.device-header {
  position: relative;
  padding: 30rpx;
  background: linear-gradient(135deg, #007aff, #5856d6);
  color: #fff;
}

.device-title {
  font-size: 36rpx;
  font-weight: bold;
  margin-bottom: 20rpx;
}

.device-subtitle {
  font-size: 28rpx;
  opacity: 0.9;
}

.device-status {
  position: absolute;
  top: 30rpx;
  right: 30rpx;
  padding: 8rpx 24rpx;
  border-radius: 30rpx;
  font-size: 24rpx;
  background: rgba(255, 255, 255, 0.2);
}

.device-image {
  width: 100%;
  height: 400rpx;
  background: #f5f6fa;
}

.device-image image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

/* 设备详细信息 */
.info-section {
  padding: 30rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
  display: flex;
  align-items: center;
}

.section-title::before {
  content: '';
  width: 6rpx;
  height: 32rpx;
  background: #007aff;
  margin-right: 16rpx;
  border-radius: 3rpx;
}

.info-row {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
  font-size: 28rpx;
}

.info-label {
  width: 180rpx;
  color: #666;
}

.info-value {
  flex: 1;
  color: #333;
}

/* 操作按钮 */
.action-buttons {
  display: flex;
  padding: 30rpx;
  border-top: 2rpx solid #f5f6fa;
}

.action-button {
  flex: 1;
  height: 80rpx;
  line-height: 80rpx;
  text-align: center;
  border-radius: 8rpx;
  font-size: 28rpx;
  margin: 0 10rpx;
}

.btn-primary {
  background: #007aff;
  color: #fff;
}

.btn-secondary {
  background: #5856d6;
  color: #fff;
}

.btn-warning {
  background: #ff9500;
  color: #fff;
}

/* 维护记录卡片 */
.maintenance-card {
  background: #fff;
  border-radius: 12rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
  margin-bottom: 20rpx;
}

.maintenance-header {
  padding: 20rpx 30rpx;
  border-bottom: 2rpx solid #f5f6fa;
}

.maintenance-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.maintenance-list {
  padding: 0 30rpx;
}

.maintenance-item {
  padding: 20rpx 0;
  border-bottom: 2rpx solid #f5f6fa;
}

.maintenance-item:last-child {
  border-bottom: none;
}

.maintenance-date {
  font-size: 24rpx;
  color: #999;
  margin-bottom: 10rpx;
}

.maintenance-desc {
  font-size: 28rpx;
  color: #333;
}

/* 告警记录卡片 */
.alert-card {
  background: #fff;
  border-radius: 12rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
  margin-bottom: 20rpx;
}

.alert-item {
  padding: 20rpx 30rpx;
  border-bottom: 2rpx solid #f5f6fa;
  display: flex;
  align-items: center;
}

.alert-icon {
  width: 48rpx;
  height: 48rpx;
  margin-right: 20rpx;
}

.alert-info {
  flex: 1;
}

.alert-title {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 8rpx;
}

.alert-time {
  font-size: 24rpx;
  color: #999;
}

.alert-level {
  padding: 4rpx 16rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
  margin-left: 20rpx;
}

.level-high {
  background: #ffebee;
  color: #f44336;
}

.level-medium {
  background: #fff3e0;
  color: #ff9800;
}

.level-low {
  background: #e8f5e9;
  color: #4caf50;
}

/* 底部固定按钮 */
.bottom-button {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 20rpx;
  background: #fff;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.bottom-button button {
  width: 100%;
  height: 88rpx;
  line-height: 88rpx;
  background: #007aff;
  color: #fff;
  font-size: 32rpx;
  border-radius: 8rpx;
}



