// 云开发环境配置
module.exports = {
  // 云开发环境ID，需要替换为实际的环境ID
  envId: 'your-env-id',
  
  // 存储路径配置
  storagePaths: {
    analysis: 'analysis_results',
    parameters: 'parameters',
    images: 'analysis_images',
    users: 'users'
  },
  
  // 视频分析配置
  analysis: {
    // C区坐标
    cArea: {
      x: 455,
      y: 220,
      width: 500,
      height: 500
    },
    // T区坐标
    tArea: {
      x: 940,
      y: 220,
      width: 500,
      height: 500
    },
    // 帧提取间隔（毫秒）
    frameInterval: 200
  },
  
  // 参数默认值
  defaultParameters: {
    brightness: 50,
    contrast: 50,
    saturation: 50,
    sharpness: 50,
    gamma: 1.0,
    whiteBalance: 5500,
    backlight: 50,
    exposure: 0.5
  }
} 