{"version": 3, "sources": ["index.js"], "names": [], "mappings": ";;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.js", "sourcesContent": ["\n\nvar isNative = /\\.node$/;\n\nfunction forEach(obj, callback) {\n    for ( var key in obj ) {\n        if (!Object.prototype.hasOwnProperty.call(obj, key)) {\n            continue;\n        }\n        callback(key);\n    }\n}\n\nfunction assign(target, source) {\n    forEach(source, function (key) {\n        target[key] = source[key];\n    });\n    return target;\n}\n\nfunction clearCache(requireCache) {\n    forEach(requireCache, function (resolvedPath) {\n        if (!isNative.test(resolvedPath)) {\n            delete requireCache[resolvedPath];\n        }\n    });\n}\n\nmodule.exports = function (requireCache, callback, callbackForModulesToKeep, module) {\n\n    var originalCache = assign({}, requireCache);\n    clearCache(requireCache);\n\n    if (callbackForModulesToKeep) {\n\n        var originalModuleChildren = module.children ? module.children.slice() : false; // Creates a shallow copy of module.children\n\n        callbackForModulesToKeep();\n\n        // Lists the cache entries made by callbackForModulesToKeep()\n        var modulesToKeep = [];\n        forEach(requireCache, function (key) {\n            modulesToKeep.push(key);\n        });\n\n        // Discards the modules required in callbackForModulesToKeep()\n        clearCache(requireCache);\n\n        if (module.children) { // Only true for node.js\n            module.children = originalModuleChildren; // Removes last references to modules required in callbackForModulesToKeep() -> No memory leak\n        }\n\n        // Takes the cache entries of the original cache in case the modules where required before\n        for ( var i = 0; i < modulesToKeep.length; i+=1 ) {\n            if (originalCache[modulesToKeep[i]]) {\n                requireCache[modulesToKeep[i]] = originalCache[modulesToKeep[i]];\n            }\n        }\n\n    }\n\n    var freshModule = callback();\n\n    var stealthCache = callbackForModulesToKeep ? assign({}, requireCache) : false;\n\n    clearCache(requireCache);\n\n    if (callbackForModulesToKeep) {\n        // In case modules to keep were required inside the stealthy require for the first time, copy them to the restored cache\n        for ( var k = 0; k < modulesToKeep.length; k+=1 ) {\n            if (stealthCache[modulesToKeep[k]]) {\n                requireCache[modulesToKeep[k]] = stealthCache[modulesToKeep[k]];\n            }\n        }\n    }\n\n    assign(requireCache, originalCache);\n\n    return freshModule;\n\n};\n"]}