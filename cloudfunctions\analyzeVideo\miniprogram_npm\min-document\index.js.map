{"version": 3, "sources": ["index.js", "document.js", "dom-comment.js", "dom-text.js", "dom-element.js", "event/dispatch-event.js", "event/add-event-listener.js", "event/remove-event-listener.js", "serialize.js", "dom-fragment.js", "event.js"], "names": [], "mappings": ";;;;;;;AAAA;AACA;AACA;ACFA,ADGA;ACFA;AACA;AACA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA,ACHA;AFOA,ACHA,ACHA;AFOA,ACHA,ACHA;AFOA,ACHA,AENA,ADGA;AFOA,ACHA,AENA,ADGA;AFOA,ACHA,AENA,ADGA;AFOA,ACHA,AENA,ADGA,AENA;AJaA,ACHA,AENA,ADGA,AENA;AJaA,ACHA,AENA,ADGA,AENA;AJaA,ACHA,AENA,ADGA,AGTA,ADGA;AJaA,ACHA,AENA,ADGA,AGTA,ADGA;AJaA,ACHA,AENA,ADGA,AGTA,ADGA;AJaA,ACHA,AENA,ADGA,AGTA,ADGA,AENA;ANmBA,ACHA,AENA,ADGA,AGTA,ADGA,AENA;ANmBA,ACHA,AENA,ADGA,AGTA,ADGA,AENA;ANmBA,ACHA,AENA,ADGA,AGTA,ADGA,AENA,ACHA;APsBA,ACHA,AENA,ADGA,AGTA,ADGA,AENA,ACHA;APsBA,AGTA,ADGA,AGTA,ADGA,AENA,ACHA;APsBA,AGTA,AKfA,ANkBA,AGTA,ADGA,AENA,ACHA;APsBA,AGTA,AKfA,ANkBA,AGTA,ADGA,AENA,ACHA;APsBA,AGTA,AKfA,ANkBA,AGTA,ADGA,AENA,ACHA;APsBA,AGTA,AKfA,ANkBA,AOrBA,AJYA,ADGA,AENA,ACHA;APsBA,AGTA,AKfA,ANkBA,AOrBA,AJYA,ADGA,AENA,ACHA;APsBA,AGTA,AKfA,ANkBA,AOrBA,AJYA,ADGA,AENA,ACHA;APsBA,AGTA,AKfA,ANkBA,AOrBA,AJYA,ADGA,AENA,ACHA;APsBA,AGTA,AKfA,ANkBA,AOrBA,AJYA,ADGA,AENA,ACHA;APsBA,AGTA,AKfA,ANkBA,AOrBA,AJYA,ADGA,AENA,ACHA;APsBA,AGTA,AKfA,ANkBA,AOrBA,ALeA,AENA,ACHA;APsBA,AGTA,AKfA,ACHA,ALeA,AENA,ACHA;APsBA,AGTA,AKfA,ACHA,ALeA,AENA,ACHA;APsBA,AGTA,AKfA,ACHA,ALeA,AENA,ACHA;APsBA,AGTA,AKfA,ACHA,ALeA,AENA,ACHA;APsBA,AGTA,AKfA,ACHA,ALeA,AGTA;APsBA,AGTA,AKfA,ACHA,ALeA,AGTA;APsBA,AGTA,AKfA,ACHA,ALeA,AGTA;APsBA,AGTA,AKfA,AJYA,AGTA;APsBA,AGTA,AKfA,AJYA,AGTA;APsBA,AGTA,AKfA,AJYA,AGTA;APsBA,AGTA,AKfA,ADGA;APsBA,AGTA,AKfA,ADGA;APsBA,AGTA,AKfA,ADGA;APsBA,AGTA,AKfA,ADGA;APsBA,AGTA,AKfA,ADGA;APsBA,AGTA,AKfA,ADGA;APsBA,AGTA,AKfA,ADGA;APsBA,AGTA,AKfA,ADGA;APsBA,AGTA,AKfA,ADGA;APsBA,AGTA,AIZA;APsBA,AGTA,AIZA;APsBA,AGTA,AIZA;APsBA,AGTA,AIZA;APsBA,AGTA,AIZA;APsBA,AGTA,AIZA;APsBA,AGTA,AIZA;APsBA,AGTA,AIZA;APsBA,AGTA,AIZA;APsBA,AGTA,AIZA;APsBA,AGTA,AIZA;APsBA,AGTA,AIZA;APsBA,AGTA,AIZA;APsBA,AGTA,AIZA;APsBA,AGTA,AIZA;APsBA,AGTA,AIZA;APsBA,AGTA,AIZA;APsBA,AGTA,AIZA;APsBA,AGTA,AIZA;APsBA,AGTA,AIZA;AJaA,AIZA;AJaA,AIZA;AJaA,AIZA;AJaA,AIZA;AJaA,AIZA;AJaA,AIZA;AJaA,AIZA;AJaA,AIZA;AJaA,AIZA;AJaA,AIZA;AJaA,AIZA;AJaA,AIZA;AJaA,AIZA;AJaA,AIZA;AJaA,AIZA;AJaA,AIZA;AJaA,AIZA;AJaA,AIZA;AJaA,AIZA;AJaA,AIZA;AJaA,AIZA;AJaA,AIZA;AJaA,AIZA;AJaA,AIZA;AJaA,AIZA;AJaA,AIZA;AJaA,AIZA;AJaA,AIZA;AJaA,AIZA;AJaA,AIZA;AJaA,AIZA;AJaA,AIZA;AJaA,AIZA;AJaA,AIZA;AJaA,AIZA;AJaA,AIZA;AJaA,AIZA;AJaA,AIZA;AJaA,AIZA;AJaA,AIZA;AJaA,AIZA;AJaA,AIZA;AJaA,AIZA;AJaA,AIZA;AJaA,AIZA;AJaA,AIZA;AJaA,AIZA;AJaA,AIZA;AJaA,AIZA;AJaA,AIZA;AJaA,AIZA;AJaA,AIZA;AJaA,AIZA;AJaA,AIZA;AJaA,AIZA;AJaA,AIZA;AJaA,AIZA;AJaA,AIZA;AJaA,AIZA;AJaA,AIZA;AJaA,AIZA;AJaA,AIZA;AJaA,AIZA;AJaA,AIZA;AJaA,AIZA;AJaA,AIZA;AJaA,AIZA;AJaA,AIZA;AJaA,AIZA;AJaA,AIZA;AJaA,AIZA;AJaA,AIZA;AJaA,AIZA;AJaA,AIZA;AJaA,AIZA;AJaA,AIZA;AJaA,AIZA;AJaA,AIZA;AJaA,AIZA;AJaA,AIZA;AJaA,AIZA;AJaA,AIZA;AJaA,AIZA;AJaA,AIZA;AJaA,AIZA;AJaA,AIZA;AJaA,AIZA;AJaA,AIZA;AJaA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.js", "sourcesContent": ["var Document = require('./document.js');\n\nmodule.exports = new Document();\n", "var domWalk = require(\"dom-walk\")\n\nvar Comment = require(\"./dom-comment.js\")\nvar DOMText = require(\"./dom-text.js\")\nvar DOMElement = require(\"./dom-element.js\")\nvar DocumentFragment = require(\"./dom-fragment.js\")\nvar Event = require(\"./event.js\")\nvar dispatchEvent = require(\"./event/dispatch-event.js\")\nvar addEventListener = require(\"./event/add-event-listener.js\")\nvar removeEventListener = require(\"./event/remove-event-listener.js\")\n\nmodule.exports = Document;\n\nfunction Document() {\n    if (!(this instanceof Document)) {\n        return new Document();\n    }\n\n    this.head = this.createElement(\"head\")\n    this.body = this.createElement(\"body\")\n    this.documentElement = this.createElement(\"html\")\n    this.documentElement.appendChild(this.head)\n    this.documentElement.appendChild(this.body)\n    this.childNodes = [this.documentElement]\n    this.nodeType = 9\n}\n\nvar proto = Document.prototype;\nproto.createTextNode = function createTextNode(value) {\n    return new DOMText(value, this)\n}\n\nproto.createElementNS = function createElementNS(namespace, tagName) {\n    var ns = namespace === null ? null : String(namespace)\n    return new DOMElement(tagName, this, ns)\n}\n\nproto.createElement = function createElement(tagName) {\n    return new DOMElement(tagName, this)\n}\n\nproto.createDocumentFragment = function createDocumentFragment() {\n    return new DocumentFragment(this)\n}\n\nproto.createEvent = function createEvent(family) {\n    return new Event(family)\n}\n\nproto.createComment = function createComment(data) {\n    return new Comment(data, this)\n}\n\nproto.getElementById = function getElementById(id) {\n    id = String(id)\n\n    var result = domWalk(this.childNodes, function (node) {\n        if (String(node.id) === id) {\n            return node\n        }\n    })\n\n    return result || null\n}\n\nproto.getElementsByClassName = DOMElement.prototype.getElementsByClassName\nproto.getElementsByTagName = DOMElement.prototype.getElementsByTagName\nproto.contains = DOMElement.prototype.contains\n\nproto.removeEventListener = removeEventListener\nproto.addEventListener = addEventListener\nproto.dispatchEvent = dispatchEvent\n", "module.exports = Comment\n\nfunction Comment(data, owner) {\n    if (!(this instanceof Comment)) {\n        return new Comment(data, owner)\n    }\n\n    this.data = data\n    this.nodeValue = data\n    this.length = data.length\n    this.ownerDocument = owner || null\n}\n\nComment.prototype.nodeType = 8\nComment.prototype.nodeName = \"#comment\"\n\nComment.prototype.toString = function _Comment_toString() {\n    return \"[object Comment]\"\n}\n", "module.exports = DOMText\n\nfunction DOMText(value, owner) {\n    if (!(this instanceof DOMText)) {\n        return new DOMText(value)\n    }\n\n    this.data = value || \"\"\n    this.length = this.data.length\n    this.ownerDocument = owner || null\n}\n\nDOMText.prototype.type = \"DOMTextNode\"\nDOMText.prototype.nodeType = 3\nDOMText.prototype.nodeName = \"#text\"\n\nDOMText.prototype.toString = function _Text_toString() {\n    return this.data\n}\n\nDOMText.prototype.replaceData = function replaceData(index, length, value) {\n    var current = this.data\n    var left = current.substring(0, index)\n    var right = current.substring(index + length, current.length)\n    this.data = left + value + right\n    this.length = this.data.length\n}\n", "var domWalk = require(\"dom-walk\")\nvar dispatchEvent = require(\"./event/dispatch-event.js\")\nvar addEventListener = require(\"./event/add-event-listener.js\")\nvar removeEventListener = require(\"./event/remove-event-listener.js\")\nvar serializeNode = require(\"./serialize.js\")\n\nvar htmlns = \"http://www.w3.org/1999/xhtml\"\n\nmodule.exports = DOMElement\n\nfunction DOMElement(tagName, owner, namespace) {\n    if (!(this instanceof DOMElement)) {\n        return new DOMElement(tagName)\n    }\n\n    var ns = namespace === undefined ? htmlns : (namespace || null)\n\n    this.tagName = ns === htmlns ? String(tagName).toUpperCase() : tagName\n    this.nodeName = this.tagName\n    this.className = \"\"\n    this.dataset = {}\n    this.childNodes = []\n    this.parentNode = null\n    this.style = {}\n    this.ownerDocument = owner || null\n    this.namespaceURI = ns\n    this._attributes = {}\n\n    if (this.tagName === 'INPUT') {\n      this.type = 'text'\n    }\n}\n\nDOMElement.prototype.type = \"DOMElement\"\nDOMElement.prototype.nodeType = 1\n\nDOMElement.prototype.appendChild = function _Element_appendChild(child) {\n    if (child.parentNode) {\n        child.parentNode.removeChild(child)\n    }\n\n    this.childNodes.push(child)\n    child.parentNode = this\n\n    return child\n}\n\nDOMElement.prototype.replaceChild =\n    function _Element_replaceChild(elem, needle) {\n        // TODO: Throw NotFoundError if needle.parentNode !== this\n\n        if (elem.parentNode) {\n            elem.parentNode.removeChild(elem)\n        }\n\n        var index = this.childNodes.indexOf(needle)\n\n        needle.parentNode = null\n        this.childNodes[index] = elem\n        elem.parentNode = this\n\n        return needle\n    }\n\nDOMElement.prototype.removeChild = function _Element_removeChild(elem) {\n    // TODO: Throw NotFoundError if elem.parentNode !== this\n\n    var index = this.childNodes.indexOf(elem)\n    this.childNodes.splice(index, 1)\n\n    elem.parentNode = null\n    return elem\n}\n\nDOMElement.prototype.insertBefore =\n    function _Element_insertBefore(elem, needle) {\n        // TODO: Throw NotFoundError if referenceElement is a dom node\n        // and parentNode !== this\n\n        if (elem.parentNode) {\n            elem.parentNode.removeChild(elem)\n        }\n\n        var index = needle === null || needle === undefined ?\n            -1 :\n            this.childNodes.indexOf(needle)\n\n        if (index > -1) {\n            this.childNodes.splice(index, 0, elem)\n        } else {\n            this.childNodes.push(elem)\n        }\n\n        elem.parentNode = this\n        return elem\n    }\n\nDOMElement.prototype.setAttributeNS =\n    function _Element_setAttributeNS(namespace, name, value) {\n        var prefix = null\n        var localName = name\n        var colonPosition = name.indexOf(\":\")\n        if (colonPosition > -1) {\n            prefix = name.substr(0, colonPosition)\n            localName = name.substr(colonPosition + 1)\n        }\n        if (this.tagName === 'INPUT' && name === 'type') {\n          this.type = value;\n        }\n        else {\n          var attributes = this._attributes[namespace] || (this._attributes[namespace] = {})\n          attributes[localName] = {value: value, prefix: prefix}\n        }\n    }\n\nDOMElement.prototype.getAttributeNS =\n    function _Element_getAttributeNS(namespace, name) {\n        var attributes = this._attributes[namespace];\n        var value = attributes && attributes[name] && attributes[name].value\n        if (this.tagName === 'INPUT' && name === 'type') {\n          return this.type;\n        }\n        if (typeof value !== \"string\") {\n            return null\n        }\n        return value\n    }\n\nDOMElement.prototype.removeAttributeNS =\n    function _Element_removeAttributeNS(namespace, name) {\n        var attributes = this._attributes[namespace];\n        if (attributes) {\n            delete attributes[name]\n        }\n    }\n\nDOMElement.prototype.hasAttributeNS =\n    function _Element_hasAttributeNS(namespace, name) {\n        var attributes = this._attributes[namespace]\n        return !!attributes && name in attributes;\n    }\n\nDOMElement.prototype.setAttribute = function _Element_setAttribute(name, value) {\n    return this.setAttributeNS(null, name, value)\n}\n\nDOMElement.prototype.getAttribute = function _Element_getAttribute(name) {\n    return this.getAttributeNS(null, name)\n}\n\nDOMElement.prototype.removeAttribute = function _Element_removeAttribute(name) {\n    return this.removeAttributeNS(null, name)\n}\n\nDOMElement.prototype.hasAttribute = function _Element_hasAttribute(name) {\n    return this.hasAttributeNS(null, name)\n}\n\nDOMElement.prototype.removeEventListener = removeEventListener\nDOMElement.prototype.addEventListener = addEventListener\nDOMElement.prototype.dispatchEvent = dispatchEvent\n\n// Un-implemented\nDOMElement.prototype.focus = function _Element_focus() {\n    return void 0\n}\n\nDOMElement.prototype.toString = function _Element_toString() {\n    return serializeNode(this)\n}\n\nDOMElement.prototype.getElementsByClassName = function _Element_getElementsByClassName(classNames) {\n    var classes = classNames.split(\" \");\n    var elems = []\n\n    domWalk(this, function (node) {\n        if (node.nodeType === 1) {\n            var nodeClassName = node.className || \"\"\n            var nodeClasses = nodeClassName.split(\" \")\n\n            if (classes.every(function (item) {\n                return nodeClasses.indexOf(item) !== -1\n            })) {\n                elems.push(node)\n            }\n        }\n    })\n\n    return elems\n}\n\nDOMElement.prototype.getElementsByTagName = function _Element_getElementsByTagName(tagName) {\n    tagName = tagName.toLowerCase()\n    var elems = []\n\n    domWalk(this.childNodes, function (node) {\n        if (node.nodeType === 1 && (tagName === '*' || node.tagName.toLowerCase() === tagName)) {\n            elems.push(node)\n        }\n    })\n\n    return elems\n}\n\nDOMElement.prototype.contains = function _Element_contains(element) {\n    return domWalk(this, function (node) {\n        return element === node\n    }) || false\n}\n", "module.exports = dispatchEvent\n\nfunction dispatchEvent(ev) {\n    var elem = this\n    var type = ev.type\n\n    if (!ev.target) {\n        ev.target = elem\n    }\n\n    if (!elem.listeners) {\n        elem.listeners = {}\n    }\n\n    var listeners = elem.listeners[type]\n\n    if (listeners) {\n        return listeners.forEach(function (listener) {\n            ev.currentTarget = elem\n            if (typeof listener === 'function') {\n                listener(ev)\n            } else {\n                listener.handleEvent(ev)\n            }\n        })\n    }\n\n    if (elem.parentNode) {\n        elem.parentNode.dispatchEvent(ev)\n    }\n}\n", "module.exports = addEventListener\n\nfunction addEventListener(type, listener) {\n    var elem = this\n\n    if (!elem.listeners) {\n        elem.listeners = {}\n    }\n\n    if (!elem.listeners[type]) {\n        elem.listeners[type] = []\n    }\n\n    if (elem.listeners[type].indexOf(listener) === -1) {\n        elem.listeners[type].push(listener)\n    }\n}\n", "module.exports = removeEventListener\n\nfunction removeEventListener(type, listener) {\n    var elem = this\n\n    if (!elem.listeners) {\n        return\n    }\n\n    if (!elem.listeners[type]) {\n        return\n    }\n\n    var list = elem.listeners[type]\n    var index = list.indexOf(listener)\n    if (index !== -1) {\n        list.splice(index, 1)\n    }\n}\n", "module.exports = serializeNode\n\nvar voidElements = [\"area\",\"base\",\"br\",\"col\",\"embed\",\"hr\",\"img\",\"input\",\"keygen\",\"link\",\"menuitem\",\"meta\",\"param\",\"source\",\"track\",\"wbr\"];\n\nfunction serializeNode(node) {\n    switch (node.nodeType) {\n        case 3:\n            return escapeText(node.data)\n        case 8:\n            return \"<!--\" + node.data + \"-->\"\n        default:\n            return serializeElement(node)\n    }\n}\n\nfunction serializeElement(elem) {\n    var strings = []\n\n    var tagname = elem.tagName\n\n    if (elem.namespaceURI === \"http://www.w3.org/1999/xhtml\") {\n        tagname = tagname.toLowerCase()\n    }\n\n    strings.push(\"<\" + tagname + properties(elem) + datasetify(elem))\n\n    if (voidElements.indexOf(tagname) > -1) {\n        strings.push(\" />\")\n    } else {\n        strings.push(\">\")\n\n        if (elem.childNodes.length) {\n            strings.push.apply(strings, elem.childNodes.map(serializeNode))\n        } else if (elem.textContent || elem.innerText) {\n            strings.push(escapeText(elem.textContent || elem.innerText))\n        } else if (elem.innerHTML) {\n            strings.push(elem.innerHTML)\n        }\n\n        strings.push(\"</\" + tagname + \">\")\n    }\n\n    return strings.join(\"\")\n}\n\nfunction isProperty(elem, key) {\n    var type = typeof elem[key]\n\n    if (key === \"style\" && Object.keys(elem.style).length > 0) {\n      return true\n    }\n\n    return elem.hasOwnProperty(key) &&\n        (type === \"string\" || type === \"boolean\" || type === \"number\") &&\n        key !== \"nodeName\" && key !== \"className\" && key !== \"tagName\" &&\n        key !== \"textContent\" && key !== \"innerText\" && key !== \"namespaceURI\" &&  key !== \"innerHTML\"\n}\n\nfunction stylify(styles) {\n    if (typeof styles === 'string') return styles\n    var attr = \"\"\n    Object.keys(styles).forEach(function (key) {\n        var value = styles[key]\n        key = key.replace(/[A-Z]/g, function(c) {\n            return \"-\" + c.toLowerCase();\n        })\n        attr += key + \":\" + value + \";\"\n    })\n    return attr\n}\n\nfunction datasetify(elem) {\n    var ds = elem.dataset\n    var props = []\n\n    for (var key in ds) {\n        props.push({ name: \"data-\" + key, value: ds[key] })\n    }\n\n    return props.length ? stringify(props) : \"\"\n}\n\nfunction stringify(list) {\n    var attributes = []\n    list.forEach(function (tuple) {\n        var name = tuple.name\n        var value = tuple.value\n\n        if (name === \"style\") {\n            value = stylify(value)\n        }\n\n        attributes.push(name + \"=\" + \"\\\"\" + escapeAttributeValue(value) + \"\\\"\")\n    })\n\n    return attributes.length ? \" \" + attributes.join(\" \") : \"\"\n}\n\nfunction properties(elem) {\n    var props = []\n    for (var key in elem) {\n        if (isProperty(elem, key)) {\n            props.push({ name: key, value: elem[key] })\n        }\n    }\n\n    for (var ns in elem._attributes) {\n      for (var attribute in elem._attributes[ns]) {\n        var prop = elem._attributes[ns][attribute]\n        var name = (prop.prefix ? prop.prefix + \":\" : \"\") + attribute\n        props.push({ name: name, value: prop.value })\n      }\n    }\n\n    if (elem.className) {\n        props.push({ name: \"class\", value: elem.className })\n    }\n\n    return props.length ? stringify(props) : \"\"\n}\n\nfunction escapeText(s) {\n    var str = '';\n\n    if (typeof(s) === 'string') { \n        str = s; \n    } else if (s) {\n        str = s.toString();\n    }\n\n    return str\n        .replace(/&/g, \"&amp;\")\n        .replace(/</g, \"&lt;\")\n        .replace(/>/g, \"&gt;\")\n}\n\nfunction escapeAttributeValue(str) {\n    return escapeText(str).replace(/\"/g, \"&quot;\")\n}\n", "var DOMElement = require(\"./dom-element.js\")\n\nmodule.exports = DocumentFragment\n\nfunction DocumentFragment(owner) {\n    if (!(this instanceof DocumentFragment)) {\n        return new DocumentFragment()\n    }\n\n    this.childNodes = []\n    this.parentNode = null\n    this.ownerDocument = owner || null\n}\n\nDocumentFragment.prototype.type = \"DocumentFragment\"\nDocumentFragment.prototype.nodeType = 11\nDocumentFragment.prototype.nodeName = \"#document-fragment\"\n\nDocumentFragment.prototype.appendChild  = DOMElement.prototype.appendChild\nDocumentFragment.prototype.replaceChild = DOMElement.prototype.replaceChild\nDocumentFragment.prototype.removeChild  = DOMElement.prototype.removeChild\n\nDocumentFragment.prototype.toString =\n    function _DocumentFragment_toString() {\n        return this.childNodes.map(function (node) {\n            return String(node)\n        }).join(\"\")\n    }\n", "module.exports = Event\n\nfunction Event(family) {}\n\nEvent.prototype.initEvent = function _Event_initEvent(type, bubbles, cancelable) {\n    this.type = type\n    this.bubbles = bubbles\n    this.cancelable = cancelable\n}\n\nEvent.prototype.preventDefault = function _Event_preventDefault() {\n    \n}\n"]}