/* addDevice.wxss */
/* 🚀 自定义导航栏 - Skyline渲染引擎要求，支持动态适配 */
.custom-navbar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  /* height通过内联样式动态设置 */
  min-height: 88rpx; /* 最小高度保证兼容性 */
  background: #fff;
  display: flex;
  align-items: flex-end; /* 改为底部对齐，确保标题在导航栏底部 */
  justify-content: center;
  z-index: 9999;
  border-bottom: 1rpx solid #e5e5e5;
  transform: translate3d(0, 0, 0);
  will-change: transform;
  /* 添加安全区域适配 */
  box-sizing: border-box;
}

.navbar-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
  /* 确保标题在导航栏底部正确显示 */
  padding-bottom: 12rpx;
  line-height: 1.2;
}

page {
  background-color: #f7f8fa;
}

.page__bd_spacing {
  padding: 30rpx;
  padding-bottom: 140rpx;
  padding-top: 118rpx; /* 为自定义导航栏留出空间 */
}

/* 卡片样式 */
.weui-cells {
  margin: 20rpx 0;
  border-radius: 16rpx;
  background: #fff;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.05);
  overflow: hidden;
}

.weui-cells__title {
  font-size: 28rpx;
  color: #666;
  padding: 30rpx 0 20rpx;
  font-weight: 500;
}

/* 开关样式 */
.weui-cell_switch {
  padding: 24rpx 30rpx;
  background: #fff;
  position: relative;
}

.weui-cell_switch::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 30rpx;
  right: 30rpx;
  height: 1rpx;
  background: rgba(0, 0, 0, 0.05);
}

.weui-cell_switch:last-child::after {
  display: none;
}

.weui-cell__bd {
  font-size: 30rpx;
  color: #333;
  font-weight: 500;
}

/* 输入框样式 */
.weui-cell_input {
  padding: 24rpx 30rpx;
  display: flex;
  align-items: center;
  background: #fff;
  position: relative;
}

.weui-cell_input::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 30rpx;
  right: 30rpx;
  height: 1rpx;
  background: rgba(0, 0, 0, 0.05);
}

.weui-cell_input:last-child::after {
  display: none;
}

.weui-label {
  width: 180rpx;
  font-size: 28rpx;
  color: #666;
}

.weui-input {
  flex: 1;
  font-size: 28rpx;
  color: #333;
  padding: 8rpx 0;
}

.weui-input::placeholder {
  color: #999;
}

/* 按钮样式 */
.weui-btn-area {
  padding: 40rpx 30rpx;
}

.weui-btn {
  background: linear-gradient(135deg, #4a90e2, #7c4dff);
  color: #fff;
  border-radius: 12rpx;
  font-size: 32rpx;
  font-weight: 500;
  padding: 24rpx 0;
  box-shadow: 0 4rpx 12rpx rgba(74, 144, 226, 0.3);
  transition: all 0.3s ease;
  border: none;
  position: relative;
  overflow: hidden;
}

.weui-btn::after {
  display: none;
}

.weui-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0));
  opacity: 0;
  transition: opacity 0.3s ease;
}

/* 已禁用按钮点击效果，避免颜色滞留问题 */
/*
.weui-btn:active::before {
  opacity: 1;
}

.weui-btn:active {
  transform: scale(0.98);
  box-shadow: 0 2rpx 6rpx rgba(74, 144, 226, 0.2);
}
*/

/* switch 样式优化 */
switch {
  transform: scale(0.9);
}

/* 禁用状态样式 */
.weui-cell_switch.disabled {
  opacity: 0.6;
}

/* 表单动画 */
form {
  animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(20rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 输入框聚焦状态 */
.weui-input:focus {
  position: relative;
}

.weui-input:focus::after {
  content: '';
  position: absolute;
  left: -8rpx;
  right: -8rpx;
  bottom: -8rpx;
  height: 2rpx;
  background: #4a90e2;
  animation: focusLine 0.3s ease-out;
}

@keyframes focusLine {
  from {
    opacity: 0;
    transform: scaleX(0.8);
  }
  to {
    opacity: 1;
    transform: scaleX(1);
  }
}

/* 自定义底部导航栏 */
.custom-tabbar {
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 110rpx;
  display: flex;
  background-color: #ffffff;
  box-shadow: 0 -4rpx 16rpx rgba(0, 0, 0, 0.08);
  z-index: 9999;
  border-top: 1rpx solid rgba(0, 0, 0, 0.03);
  padding-bottom: env(safe-area-inset-bottom);
}

.tab-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  position: relative;
  padding: 12rpx 0;
}

.tab-text {
  font-size: 22rpx;
  color: #999;
  margin-top: 10rpx;
  font-weight: 500;
  transition: color 0.3s ease;
}

.tab-item.active .tab-text {
  color: #4a90e2;
}

.tab-item::after {
  content: '';
  position: absolute;
  bottom: 6rpx;
  left: 50%;
  transform: translateX(-50%) scaleX(0);
  width: 36rpx;
  height: 4rpx;
  background: #4a90e2;
  border-radius: 4rpx;
  opacity: 0;
  transition: all 0.3s ease;
}

.tab-item.active::after {
  opacity: 1;
  transform: translateX(-50%) scaleX(1);
}

/* CSS 图标 - 主界面 */
.tab-icon-home {
  position: relative;
  width: 54rpx;
  height: 48rpx;
  display: flex;
  justify-content: center;
  align-items: center;
}

/* 房子屋顶 */
.tab-icon-home::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 0;
  height: 0;
  border-left: 27rpx solid transparent;
  border-right: 27rpx solid transparent;
  border-bottom: 24rpx solid #8a8a8a;
  transition: border-bottom-color 0.3s ease;
}

/* 房子主体 */
.tab-icon-home::after {
  content: '';
  position: absolute;
  top: 20rpx;
  left: 6rpx;
  width: 42rpx;
  height: 28rpx;
  background-color: #8a8a8a;
  border-radius: 0 0 4rpx 4rpx;
  transition: background-color 0.3s ease;
}

/* 房子门 */
.home-door {
  position: absolute;
  bottom: 0;
  left: 18rpx;
  width: 18rpx;
  height: 18rpx;
  background-color: white;
  border-radius: 3rpx 3rpx 0 0;
  z-index: 1;
}

/* 房子窗户 */
.home-window {
  position: absolute;
  top: 24rpx;
  left: 30rpx;
  width: 10rpx;
  height: 10rpx;
  background-color: white;
  border-radius: 2rpx;
  z-index: 1;
}

/* 烟囱 */
.home-chimney {
  position: absolute;
  top: 5rpx;
  right: 10rpx;
  width: 8rpx;
  height: 14rpx;
  background-color: #8a8a8a;
  border-radius: 2rpx 2rpx 0 0;
  z-index: 0;
  transition: background-color 0.3s ease;
}

/* 激活状态 */
.tab-item.active .tab-icon-home::before {
  border-bottom-color: #4a90e2;
}

.tab-item.active .tab-icon-home::after,
.tab-item.active .home-chimney {
  background-color: #4a90e2;
}

/* CSS 图标 - 设备录入 */
.tab-icon-add {
  position: relative;
  width: 54rpx;
  height: 48rpx;
  display: flex;
  justify-content: center;
  align-items: center;
}

/* 设备外壳 */
.tab-icon-add::before {
  content: '';
  position: absolute;
  top: 6rpx;
  left: 10rpx;
  width: 34rpx;
  height: 40rpx;
  border-radius: 10rpx;
  background-color: #8a8a8a;
  transition: all 0.3s ease;
}

/* 设备屏幕 */
.tab-icon-add::after {
  content: '';
  position: absolute;
  top: 12rpx;
  left: 16rpx;
  width: 22rpx;
  height: 18rpx;
  border-radius: 4rpx;
  background-color: white;
  transition: all 0.3s ease;
}

/* 设备按钮 */
.add-connector {
  position: absolute;
  bottom: 12rpx;
  left: 24rpx;
  width: 6rpx;
  height: 6rpx;
  border-radius: 50%;
  background-color: white;
  transition: all 0.3s ease;
}

/* 横向按钮 */
.add-plus-horizontal {
  position: absolute;
  top: 22rpx;
  left: 20rpx;
  width: 14rpx;
  height: 2rpx;
  background-color: #8a8a8a;
  border-radius: 1rpx;
  transition: all 0.3s ease;
}

/* 纵向按钮 */
.add-plus-vertical {
  position: absolute;
  top: 16rpx;
  left: 26rpx;
  width: 2rpx;
  height: 14rpx;
  background-color: #8a8a8a;
  border-radius: 1rpx;
  transition: all 0.3s ease;
}

/* CSS 图标 - 我的 */
.tab-icon-me {
  position: relative;
  width: 54rpx;
  height: 48rpx;
  display: flex;
  justify-content: center;
  align-items: center;
}

/* 主圆 - 形成头像主体 */
.tab-icon-me::before {
  content: '';
  position: absolute;
  top: 3rpx;
  left: 12rpx;
  width: 30rpx;
  height: 30rpx;
  border-radius: 50%;
  background: linear-gradient(135deg, #9e9e9e, #707070);
  box-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.2);
  transition: all 0.3s ease;
}

/* 内部光晕 - 增加质感 */
.tab-icon-me::after {
  content: '';
  position: absolute;
  top: 7rpx;
  left: 16rpx;
  width: 10rpx;
  height: 10rpx;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.6);
  filter: blur(1rpx);
  transition: all 0.3s ease;
}

/* 装饰元素左 - 星形光点 */
.me-ear-left {
  position: absolute;
  top: 8rpx;
  left: 36rpx;
  width: 4rpx;
  height: 4rpx;
  border-radius: 50%;
  background-color: white;
  box-shadow: 0 0 4rpx rgba(255, 255, 255, 0.9);
  transition: all 0.3s ease;
}

/* 装饰元素右 - 更小的光点 */
.me-ear-right {
  position: absolute;
  top: 16rpx;
  left: 8rpx;
  width: 3rpx;
  height: 3rpx;
  border-radius: 50%;
  background-color: white;
  box-shadow: 0 0 3rpx rgba(255, 255, 255, 0.8);
  transition: all 0.3s ease;
}

/* 底部弧线 - 衬托头像 */
.me-arm-left {
  position: absolute;
  bottom: 4rpx;
  left: 14rpx;
  width: 26rpx;
  height: 14rpx;
  border-radius: 50%;
  border-bottom: 3rpx solid #8a8a8a;
  border-left: 3rpx solid transparent;
  border-right: 3rpx solid transparent;
  border-top: 3rpx solid transparent;
  box-sizing: border-box;
  transition: all 0.3s ease;
}

/* 底部装饰元素 - 增加层次感 */
.me-arm-right {
  position: absolute;
  bottom: 11rpx;
  left: 19rpx;
  width: 16rpx;
  height: 2rpx;
  background-color: #8a8a8a;
  border-radius: 2rpx;
  transition: all 0.3s ease;
}

/* 激活状态 */
.tab-item.active .tab-icon-me::before {
  background: linear-gradient(135deg, #4a90e2, #3674d0);
  box-shadow: 0 2rpx 8rpx rgba(74, 144, 226, 0.4);
}

.tab-item.active .tab-icon-me::after {
  background: rgba(255, 255, 255, 0.8);
  box-shadow: 0 0 6rpx rgba(255, 255, 255, 0.4);
}

.tab-item.active .me-ear-left,
.tab-item.active .me-ear-right {
  background-color: white;
  box-shadow: 0 0 6rpx rgba(255, 255, 255, 1);
}

.tab-item.active .me-arm-left {
  border-bottom-color: #4a90e2;
  box-shadow: 0 2rpx 4rpx rgba(74, 144, 226, 0.2);
}

.tab-item.active .me-arm-right {
  background-color: #4a90e2;
  box-shadow: 0 1rpx 3rpx rgba(74, 144, 226, 0.2);
}

.tab-item.active .tab-icon-add::before {
  background-color: #4a90e2;
}

.tab-item.active .add-connector,
.tab-item.active .tab-icon-add::after {
  background-color: white;
  box-shadow: 0 0 4rpx rgba(255, 255, 255, 0.8);
}

.tab-item.active .add-plus-horizontal,
.tab-item.active .add-plus-vertical {
  background-color: #4a90e2;
}