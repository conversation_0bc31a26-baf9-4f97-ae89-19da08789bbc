{"version": 3, "sources": ["index.js"], "names": [], "mappings": ";;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.js", "sourcesContent": ["var HEADER = [66, 77, 70]\n\nmodule.exports = function readBMFontBinary(buf) {\n  if (buf.length < 6)\n    throw new Error('invalid buffer length for BMFont')\n\n  var header = HEADER.every(function(byte, i) {\n    return buf.readUInt8(i) === byte\n  })\n\n  if (!header)\n    throw new Error('<PERSON><PERSON><PERSON> missing BMF byte header')\n\n  var i = 3\n  var vers = buf.readUInt8(i++)\n  if (vers > 3)\n    throw new Error('Only supports BMFont Binary v3 (BMFont App v1.10)')\n  \n  var target = { kernings: [], chars: [] }\n  for (var b=0; b<5; b++)\n    i += readBlock(target, buf, i)\n  return target\n}\n\nfunction readBlock(target, buf, i) {\n  if (i > buf.length-1)\n    return 0\n\n  var blockID = buf.readUInt8(i++)\n  var blockSize = buf.readInt32LE(i)\n  i += 4\n\n  switch(blockID) {\n    case 1: \n      target.info = readInfo(buf, i)\n      break\n    case 2:\n      target.common = readCommon(buf, i)\n      break\n    case 3:\n      target.pages = readPages(buf, i, blockSize)\n      break\n    case 4:\n      target.chars = readChars(buf, i, blockSize)\n      break\n    case 5:\n      target.kernings = readKernings(buf, i, blockSize)\n      break\n  }\n  return 5 + blockSize\n}\n\nfunction readInfo(buf, i) {\n  var info = {}\n  info.size = buf.readInt16LE(i)\n\n  var bitField = buf.readUInt8(i+2)\n  info.smooth = (bitField >> 7) & 1\n  info.unicode = (bitField >> 6) & 1\n  info.italic = (bitField >> 5) & 1\n  info.bold = (bitField >> 4) & 1\n  \n  //fixedHeight is only mentioned in binary spec \n  if ((bitField >> 3) & 1)\n    info.fixedHeight = 1\n  \n  info.charset = buf.readUInt8(i+3) || ''\n  info.stretchH = buf.readUInt16LE(i+4)\n  info.aa = buf.readUInt8(i+6)\n  info.padding = [\n    buf.readInt8(i+7),\n    buf.readInt8(i+8),\n    buf.readInt8(i+9),\n    buf.readInt8(i+10)\n  ]\n  info.spacing = [\n    buf.readInt8(i+11),\n    buf.readInt8(i+12)\n  ]\n  info.outline = buf.readUInt8(i+13)\n  info.face = readStringNT(buf, i+14)\n  return info\n}\n\nfunction readCommon(buf, i) {\n  var common = {}\n  common.lineHeight = buf.readUInt16LE(i)\n  common.base = buf.readUInt16LE(i+2)\n  common.scaleW = buf.readUInt16LE(i+4)\n  common.scaleH = buf.readUInt16LE(i+6)\n  common.pages = buf.readUInt16LE(i+8)\n  var bitField = buf.readUInt8(i+10)\n  common.packed = 0\n  common.alphaChnl = buf.readUInt8(i+11)\n  common.redChnl = buf.readUInt8(i+12)\n  common.greenChnl = buf.readUInt8(i+13)\n  common.blueChnl = buf.readUInt8(i+14)\n  return common\n}\n\nfunction readPages(buf, i, size) {\n  var pages = []\n  var text = readNameNT(buf, i)\n  var len = text.length+1\n  var count = size / len\n  for (var c=0; c<count; c++) {\n    pages[c] = buf.slice(i, i+text.length).toString('utf8')\n    i += len\n  }\n  return pages\n}\n\nfunction readChars(buf, i, blockSize) {\n  var chars = []\n\n  var count = blockSize / 20\n  for (var c=0; c<count; c++) {\n    var char = {}\n    var off = c*20\n    char.id = buf.readUInt32LE(i + 0 + off)\n    char.x = buf.readUInt16LE(i + 4 + off)\n    char.y = buf.readUInt16LE(i + 6 + off)\n    char.width = buf.readUInt16LE(i + 8 + off)\n    char.height = buf.readUInt16LE(i + 10 + off)\n    char.xoffset = buf.readInt16LE(i + 12 + off)\n    char.yoffset = buf.readInt16LE(i + 14 + off)\n    char.xadvance = buf.readInt16LE(i + 16 + off)\n    char.page = buf.readUInt8(i + 18 + off)\n    char.chnl = buf.readUInt8(i + 19 + off)\n    chars[c] = char\n  }\n  return chars\n}\n\nfunction readKernings(buf, i, blockSize) {\n  var kernings = []\n  var count = blockSize / 10\n  for (var c=0; c<count; c++) {\n    var kern = {}\n    var off = c*10\n    kern.first = buf.readUInt32LE(i + 0 + off)\n    kern.second = buf.readUInt32LE(i + 4 + off)\n    kern.amount = buf.readInt16LE(i + 8 + off)\n    kernings[c] = kern\n  }\n  return kernings\n}\n\nfunction readNameNT(buf, offset) {\n  var pos=offset\n  for (; pos<buf.length; pos++) {\n    if (buf[pos] === 0x00) \n      break\n  }\n  return buf.slice(offset, pos)\n}\n\nfunction readStringNT(buf, offset) {\n  return readNameNT(buf, offset).toString('utf8')\n}"]}