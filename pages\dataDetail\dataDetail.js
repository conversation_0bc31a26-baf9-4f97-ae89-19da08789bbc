Page({
  data: {
    detailData: null,
    cAreaImage: null,
    tAreaImage: null
  },

  onLoad: function(options) {
    const eventChannel = this.getOpenerEventChannel()
    
    // 获取上一页面传递的数据
    eventChannel.on('acceptDataFromOpenerPage', (data) => {
      const detail = data.data
      this.setData({
        detailData: detail,
        cAreaImage: detail.cAreaImage,
        tAreaImage: detail.tAreaImage
      })

      // 如果有图片数据，绘制图片
      if (detail.cAreaImage || detail.tAreaImage) {
        this.drawImages()
      }
    })
  },

  // 绘制图片
  drawImages: async function() {
    if (this.data.cAreaImage) {
      const cCanvas = await this.getCanvasContext('cAreaDetailCanvas')
      await this.drawImage(cCanvas, this.data.cAreaImage)
    }

    if (this.data.tAreaImage) {
      const tCanvas = await this.getCanvasContext('tAreaDetailCanvas')
      await this.drawImage(tCanvas, this.data.tAreaImage)
    }
  },

  // 获取Canvas上下文
  getCanvasContext: function(canvasId) {
    return new Promise((resolve, reject) => {
      const query = wx.createSelectorQuery()
      query.select('#' + canvasId)
        .fields({ node: true, size: true })
        .exec((res) => {
          if (res[0]) {
            resolve(res[0].node)
          } else {
            reject(new Error('获取Canvas上下文失败'))
          }
        })
    })
  },

  // 绘制单个图片
  drawImage: function(canvas, imageUrl) {
    return new Promise((resolve, reject) => {
      const ctx = canvas.getContext('2d')
      const img = canvas.createImage()
      
      // 设置画布尺寸 - 使用与CSS样式一致的尺寸
      const dpr = wx.getSystemInfoSync().pixelRatio || 1
      canvas.width = 300 * dpr  // 详情页面的canvas宽度为300rpx
      canvas.height = 300 * dpr // 详情页面的canvas高度为300rpx
      
      // 缩放上下文以适应DPR
      ctx.scale(dpr, dpr)
      
      // 清空画布并设置背景色
      ctx.fillStyle = '#f8f9fa'
      ctx.fillRect(0, 0, 300, 300)
      
      img.onload = () => {
        ctx.drawImage(img, 0, 0, 300, 300)
        
        // 强制更新画布显示
        wx.nextTick(() => {
          console.log('画布更新完成')
        })
        
        resolve()
      }
      
      img.onerror = () => reject(new Error('图片加载失败'))
      img.src = imageUrl
    })
  }
})