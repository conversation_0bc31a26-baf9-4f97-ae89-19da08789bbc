{"version": 3, "sources": ["index.js"], "names": [], "mappings": ";;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.js", "sourcesContent": ["var crypto = require('crypto')\n\nfunction sha (key, body, algorithm) {\n  return crypto.createHmac(algorithm, key).update(body).digest('base64')\n}\n\nfunction rsa (key, body) {\n  return crypto.createSign('RSA-SHA1').update(body).sign(key, 'base64')\n}\n\nfunction rfc3986 (str) {\n  return encodeURIComponent(str)\n    .replace(/!/g,'%21')\n    .replace(/\\*/g,'%2A')\n    .replace(/\\(/g,'%28')\n    .replace(/\\)/g,'%29')\n    .replace(/'/g,'%27')\n}\n\n// Maps object to bi-dimensional array\n// Converts { foo: 'A', bar: [ 'b', 'B' ]} to\n// [ ['foo', 'A'], ['bar', 'b'], ['bar', 'B'] ]\nfunction map (obj) {\n  var key, val, arr = []\n  for (key in obj) {\n    val = obj[key]\n    if (Array.isArray(val))\n      for (var i = 0; i < val.length; i++)\n        arr.push([key, val[i]])\n    else if (typeof val === 'object')\n      for (var prop in val)\n        arr.push([key + '[' + prop + ']', val[prop]])\n    else\n      arr.push([key, val])\n  }\n  return arr\n}\n\n// Compare function for sort\nfunction compare (a, b) {\n  return a > b ? 1 : a < b ? -1 : 0\n}\n\nfunction generateBase (httpMethod, base_uri, params) {\n  // adapted from https://dev.twitter.com/docs/auth/oauth and \n  // https://dev.twitter.com/docs/auth/creating-signature\n\n  // Parameter normalization\n  // http://tools.ietf.org/html/rfc5849#section-3.4.1.3.2\n  var normalized = map(params)\n  // 1.  First, the name and value of each parameter are encoded\n  .map(function (p) {\n    return [ rfc3986(p[0]), rfc3986(p[1] || '') ]\n  })\n  // 2.  The parameters are sorted by name, using ascending byte value\n  //     ordering.  If two or more parameters share the same name, they\n  //     are sorted by their value.\n  .sort(function (a, b) {\n    return compare(a[0], b[0]) || compare(a[1], b[1])\n  })\n  // 3.  The name of each parameter is concatenated to its corresponding\n  //     value using an \"=\" character (ASCII code 61) as a separator, even\n  //     if the value is empty.\n  .map(function (p) { return p.join('=') })\n   // 4.  The sorted name/value pairs are concatenated together into a\n   //     single string by using an \"&\" character (ASCII code 38) as\n   //     separator.\n  .join('&')\n\n  var base = [\n    rfc3986(httpMethod ? httpMethod.toUpperCase() : 'GET'),\n    rfc3986(base_uri),\n    rfc3986(normalized)\n  ].join('&')\n\n  return base\n}\n\nfunction hmacsign (httpMethod, base_uri, params, consumer_secret, token_secret) {\n  var base = generateBase(httpMethod, base_uri, params)\n  var key = [\n    consumer_secret || '',\n    token_secret || ''\n  ].map(rfc3986).join('&')\n\n  return sha(key, base, 'sha1')\n}\n\nfunction hmacsign256 (httpMethod, base_uri, params, consumer_secret, token_secret) {\n  var base = generateBase(httpMethod, base_uri, params)\n  var key = [\n    consumer_secret || '',\n    token_secret || ''\n  ].map(rfc3986).join('&')\n\n  return sha(key, base, 'sha256')\n}\n\nfunction rsasign (httpMethod, base_uri, params, private_key, token_secret) {\n  var base = generateBase(httpMethod, base_uri, params)\n  var key = private_key || ''\n\n  return rsa(key, base)\n}\n\nfunction plaintext (consumer_secret, token_secret) {\n  var key = [\n    consumer_secret || '',\n    token_secret || ''\n  ].map(rfc3986).join('&')\n\n  return key\n}\n\nfunction sign (signMethod, httpMethod, base_uri, params, consumer_secret, token_secret) {\n  var method\n  var skipArgs = 1\n\n  switch (signMethod) {\n    case 'RSA-SHA1':\n      method = rsasign\n      break\n    case 'HMAC-SHA1':\n      method = hmacsign\n      break\n    case 'HMAC-SHA256':\n      method = hmacsign256\n      break\n    case 'PLAINTEXT':\n      method = plaintext\n      skipArgs = 4\n      break\n    default:\n     throw new Error('Signature method not supported: ' + signMethod)\n  }\n\n  return method.apply(null, [].slice.call(arguments, skipArgs))\n}\n\nexports.hmacsign = hmacsign\nexports.hmacsign256 = hmacsign256\nexports.rsasign = rsasign\nexports.plaintext = plaintext\nexports.sign = sign\nexports.rfc3986 = rfc3986\nexports.generateBase = generateBase"]}