const config = require('./config');

// 日期时间格式化
function formatTime(ms) {
  const newDate = new Date();
  newDate.setTime(ms);
  return newDate.toLocaleDateString();
}

function formatNumber(n) {
  n = n.toString();
  return n[1] ? n : '0' + n;
}

// UI相关工具函数
const ui = {
  showLoading(title = '加载中') {
    wx.showToast({
      title: title,
      icon: 'loading',
      duration: 10000
    });
  },

  hideLoading() {
    wx.hideToast();
  },

  showError(msg, duration = 2000) {
    wx.showToast({
      title: msg,
      icon: 'none',
      duration: duration
    });
  },

  showSuccess(msg, duration = config.defaults.showSuccessTime) {
    return new Promise((resolve) => {
      wx.showToast({
        title: msg,
        icon: 'success',
        duration: duration
      });
      setTimeout(resolve, duration);
    });
  },

  showModal(title, content, showCancel = true) {
    return new Promise((resolve, reject) => {
      wx.showModal({
        title,
        content,
        showCancel,
        success: (res) => {
          if (res.confirm) {
            resolve(true);
          } else {
            resolve(false);
          }
        },
        fail: reject
      });
    });
  }
};

// 数据处理工具函数
const data = {
  genDeviceData(res) {
    return Array.isArray(res.data) ? res.data : [];
  },

  getObjectKeys(obj) {
    return Object.keys(obj);
  },

  getObjectValues(obj) {
    return Object.values(obj);
  },

  getDeviceId() {
    return 'D' + Math.random().toString(36).substr(2, 18);
  },

  isValidData(data) {
    return data !== null && data !== undefined;
  }
};

// 存储相关工具函数
const storage = {
  get(key) {
    try {
      return wx.getStorageSync(key);
    } catch (e) {
      console.error(`获取存储键${key}失败:`, e);
      return null;
    }
  },

  set(key, value) {
    try {
      wx.setStorageSync(key, value);
      return true;
    } catch (e) {
      console.error(`设置存储键${key}失败:`, e);
      return false;
    }
  },

  remove(key) {
    try {
      wx.removeStorageSync(key);
      return true;
    } catch (e) {
      console.error(`删除存储键${key}失败:`, e);
      return false;
    }
  }
};

module.exports = {
  formatTime,
  formatNumber,
  ui,
  data,
  storage
};