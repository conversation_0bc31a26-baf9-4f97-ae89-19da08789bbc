{"version": 3, "sources": ["index.js"], "names": [], "mappings": ";;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.js", "sourcesContent": ["var slice = Array.prototype.slice\n\nmodule.exports = iterativelyWalk\n\nfunction iterativelyWalk(nodes, cb) {\n    if (!('length' in nodes)) {\n        nodes = [nodes]\n    }\n    \n    nodes = slice.call(nodes)\n\n    while(nodes.length) {\n        var node = nodes.shift(),\n            ret = cb(node)\n\n        if (ret) {\n            return ret\n        }\n\n        if (node.childNodes && node.childNodes.length) {\n            nodes = slice.call(node.childNodes).concat(nodes)\n        }\n    }\n}\n"]}