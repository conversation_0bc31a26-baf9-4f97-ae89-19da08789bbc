{"version": 3, "sources": ["timm.js"], "names": [], "mappings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file": "index.js", "sourcesContent": ["\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.clone = clone;\nexports.addLast = addLast;\nexports.addFirst = addFirst;\nexports.removeLast = removeLast;\nexports.removeFirst = removeFirst;\nexports.insert = insert;\nexports.removeAt = removeAt;\nexports.replaceAt = replaceAt;\nexports.getIn = getIn;\nexports.set = set;\nexports.setIn = setIn;\nexports.update = update;\nexports.updateIn = updateIn;\nexports.merge = merge;\nexports.mergeDeep = mergeDeep;\nexports.mergeIn = mergeIn;\nexports.omit = omit;\nexports.addDefaults = addDefaults;\nexports.default = void 0;\n\n/* eslint-disable @typescript-eslint/ban-types */\n\n/*!\n * Timm\n *\n * Immutability helpers with fast reads and acceptable writes.\n *\n * @copyright Guillermo Grau Panea 2016\n * @license MIT\n */\nconst INVALID_ARGS = 'INVALID_ARGS';\nconst IS_DEV = process.env.NODE_ENV !== 'production';\n\n// ===============================================\n// ### Helpers\n// ===============================================\nfunction throwStr(msg) {\n  throw new Error(msg);\n}\n\nfunction getKeysAndSymbols(obj) {\n  const keys = Object.keys(obj);\n\n  if (Object.getOwnPropertySymbols) {\n    // @ts-ignore\n    return keys.concat(Object.getOwnPropertySymbols(obj));\n  }\n\n  return keys;\n}\n\nconst hasOwnProperty = {}.hasOwnProperty;\n\nfunction clone(obj0) {\n  // As array\n  if (Array.isArray(obj0)) return obj0.slice(); // As object\n\n  const obj = obj0;\n  const keys = getKeysAndSymbols(obj);\n  const out = {};\n\n  for (let i = 0; i < keys.length; i++) {\n    const key = keys[i];\n    out[key] = obj[key];\n  } // @ts-ignore (see type tests)\n\n\n  return out;\n} // Custom guard\n\n\nfunction isObject(o) {\n  return o != null && typeof o === 'object';\n} // _deepFreeze = (obj) ->\n//   Object.freeze obj\n//   for key in Object.getOwnPropertyNames obj\n//     val = obj[key]\n//     if isObject(val) and not Object.isFrozen val\n//       _deepFreeze val\n//   obj\n// ===============================================\n// -- ### Arrays\n// ===============================================\n// -- #### addLast()\n// -- Returns a new array with an appended item or items.\n// --\n// -- Usage: `addLast(array, val)`\n// --\n// -- ```js\n// -- arr = ['a', 'b']\n// -- arr2 = addLast(arr, 'c')\n// -- // ['a', 'b', 'c']\n// -- arr2 === arr\n// -- // false\n// -- arr3 = addLast(arr, ['c', 'd'])\n// -- // ['a', 'b', 'c', 'd']\n// -- ```\n// `array.concat(val)` also handles the scalar case,\n// but is apparently very slow\n\n\nfunction addLast(array, val) {\n  if (Array.isArray(val)) return array.concat(val);\n  return array.concat([val]);\n} // -- #### addFirst()\n// -- Returns a new array with a prepended item or items.\n// --\n// -- Usage: `addFirst(array, val)`\n// --\n// -- ```js\n// -- arr = ['a', 'b']\n// -- arr2 = addFirst(arr, 'c')\n// -- // ['c', 'a', 'b']\n// -- arr2 === arr\n// -- // false\n// -- arr3 = addFirst(arr, ['c', 'd'])\n// -- // ['c', 'd', 'a', 'b']\n// -- ```\n\n\nfunction addFirst(array, val) {\n  if (Array.isArray(val)) return val.concat(array);\n  return [val].concat(array);\n} // -- #### removeLast()\n// -- Returns a new array removing the last item.\n// --\n// -- Usage: `removeLast(array)`\n// --\n// -- ```js\n// -- arr = ['a', 'b']\n// -- arr2 = removeLast(arr)\n// -- // ['a']\n// -- arr2 === arr\n// -- // false\n// --\n// -- // The same array is returned if there are no changes:\n// -- arr3 = []\n// -- removeLast(arr3) === arr3\n// -- // true\n// -- ```\n\n\nfunction removeLast(array) {\n  if (!array.length) return array;\n  return array.slice(0, array.length - 1);\n} // -- #### removeFirst()\n// -- Returns a new array removing the first item.\n// --\n// -- Usage: `removeFirst(array)`\n// --\n// -- ```js\n// -- arr = ['a', 'b']\n// -- arr2 = removeFirst(arr)\n// -- // ['b']\n// -- arr2 === arr\n// -- // false\n// --\n// -- // The same array is returned if there are no changes:\n// -- arr3 = []\n// -- removeFirst(arr3) === arr3\n// -- // true\n// -- ```\n\n\nfunction removeFirst(array) {\n  if (!array.length) return array;\n  return array.slice(1);\n} // -- #### insert()\n// -- Returns a new array obtained by inserting an item or items\n// -- at a specified index.\n// --\n// -- Usage: `insert(array, idx, val)`\n// --\n// -- ```js\n// -- arr = ['a', 'b', 'c']\n// -- arr2 = insert(arr, 1, 'd')\n// -- // ['a', 'd', 'b', 'c']\n// -- arr2 === arr\n// -- // false\n// -- insert(arr, 1, ['d', 'e'])\n// -- // ['a', 'd', 'e', 'b', 'c']\n// -- ```\n\n\nfunction insert(array, idx, val) {\n  return array.slice(0, idx).concat(Array.isArray(val) ? val : [val]).concat(array.slice(idx));\n} // -- #### removeAt()\n// -- Returns a new array obtained by removing an item at\n// -- a specified index.\n// --\n// -- Usage: `removeAt(array, idx)`\n// --\n// -- ```js\n// -- arr = ['a', 'b', 'c']\n// -- arr2 = removeAt(arr, 1)\n// -- // ['a', 'c']\n// -- arr2 === arr\n// -- // false\n// --\n// -- // The same array is returned if there are no changes:\n// -- removeAt(arr, 4) === arr\n// -- // true\n// -- ```\n\n\nfunction removeAt(array, idx) {\n  if (idx >= array.length || idx < 0) return array;\n  return array.slice(0, idx).concat(array.slice(idx + 1));\n} // -- #### replaceAt()\n// -- Returns a new array obtained by replacing an item at\n// -- a specified index. If the provided item is the same as\n// -- (*referentially equal to*) the previous item at that position,\n// -- the original array is returned.\n// --\n// -- Usage: `replaceAt(array, idx, newItem)`\n// --\n// -- ```js\n// -- arr = ['a', 'b', 'c']\n// -- arr2 = replaceAt(arr, 1, 'd')\n// -- // ['a', 'd', 'c']\n// -- arr2 === arr\n// -- // false\n// --\n// -- // The same object is returned if there are no changes:\n// -- replaceAt(arr, 1, 'b') === arr\n// -- // true\n// -- ```\n\n\nfunction replaceAt(array, idx, newItem) {\n  if (array[idx] === newItem) return array;\n  const len = array.length;\n  const result = Array(len);\n\n  for (let i = 0; i < len; i++) {\n    result[i] = array[i];\n  }\n\n  result[idx] = newItem;\n  return result;\n} // ===============================================\n// -- ### Collections (objects and arrays)\n// ===============================================\n// -- #### getIn()\n// -- Returns a value from an object at a given path. Works with\n// -- nested arrays and objects. If the path does not exist, it returns\n// -- `undefined`.\n// --\n// -- Usage: `getIn(obj, path)`\n// --\n// -- ```js\n// -- obj = { a: 1, b: 2, d: { d1: 3, d2: 4 }, e: ['a', 'b', 'c'] }\n// -- getIn(obj, ['d', 'd1'])\n// -- // 3\n// -- getIn(obj, ['e', 1])\n// -- // 'b'\n// -- ```\n\n\nfunction getIn(obj, path) {\n  if (!Array.isArray(path)) {\n    throwStr(IS_DEV ? 'A path array should be provided when calling getIn()' : INVALID_ARGS);\n  }\n\n  if (obj == null) return undefined;\n  let ptr = obj;\n\n  for (let i = 0; i < path.length; i++) {\n    const key = path[i];\n    ptr = ptr != null ? ptr[key] : undefined;\n    if (ptr === undefined) return ptr;\n  }\n\n  return ptr;\n} // -- #### set()\n// -- Returns a new object with a modified attribute.\n// -- If the provided value is the same as (*referentially equal to*)\n// -- the previous value, the original object is returned.\n// --\n// -- Usage: `set(obj, key, val)`\n// --\n// -- ```js\n// -- obj = { a: 1, b: 2, c: 3 }\n// -- obj2 = set(obj, 'b', 5)\n// -- // { a: 1, b: 5, c: 3 }\n// -- obj2 === obj\n// -- // false\n// --\n// -- // The same object is returned if there are no changes:\n// -- set(obj, 'b', 2) === obj\n// -- // true\n// -- ```\n// When called with an undefined/null `obj`, `set()` returns either\n// a single-element array, or a single-key object\n\n\n// Implementation\nfunction set(obj0, key, val) {\n  let obj = obj0;\n  if (obj == null) obj = typeof key === 'number' ? [] : {};\n  if (obj[key] === val) return obj;\n  const obj2 = clone(obj);\n  obj2[key] = val;\n  return obj2;\n} // -- #### setIn()\n// -- Returns a new object with a modified **nested** attribute.\n// --\n// -- Notes:\n// --\n// -- * If the provided value is the same as (*referentially equal to*)\n// -- the previous value, the original object is returned.\n// -- * If the path does not exist, it will be created before setting\n// -- the new value.\n// --\n// -- Usage: `setIn(obj, path, val)`\n// --\n// -- ```js\n// -- obj = { a: 1, b: 2, d: { d1: 3, d2: 4 }, e: { e1: 'foo', e2: 'bar' } }\n// -- obj2 = setIn(obj, ['d', 'd1'], 4)\n// -- // { a: 1, b: 2, d: { d1: 4, d2: 4 }, e: { e1: 'foo', e2: 'bar' } }\n// -- obj2 === obj\n// -- // false\n// -- obj2.d === obj.d\n// -- // false\n// -- obj2.e === obj.e\n// -- // true\n// --\n// -- // The same object is returned if there are no changes:\n// -- obj3 = setIn(obj, ['d', 'd1'], 3)\n// -- // { a: 1, b: 2, d: { d1: 3, d2: 4 }, e: { e1: 'foo', e2: 'bar' } }\n// -- obj3 === obj\n// -- // true\n// -- obj3.d === obj.d\n// -- // true\n// -- obj3.e === obj.e\n// -- // true\n// --\n// -- // ... unknown paths create intermediate keys. Numeric segments are treated as array indices:\n// -- setIn({ a: 3 }, ['unknown', 0, 'path'], 4)\n// -- // { a: 3, unknown: [{ path: 4 }] }\n// -- ```\n\n\nfunction setIn(obj, path, val) {\n  if (!path.length) return val;\n  return doSetIn(obj, path, val, 0);\n}\n\nfunction doSetIn(obj, path, val, idx) {\n  let newValue;\n  const key = path[idx];\n\n  if (idx === path.length - 1) {\n    newValue = val;\n  } else {\n    const nestedObj = isObject(obj) && isObject(obj[key]) ? obj[key] : typeof path[idx + 1] === 'number' ? [] : {};\n    newValue = doSetIn(nestedObj, path, val, idx + 1);\n  }\n\n  return set(obj, key, newValue);\n} // -- #### update()\n// -- Returns a new object with a modified attribute,\n// -- calculated via a user-provided callback based on the current value.\n// -- If the calculated value is the same as (*referentially equal to*)\n// -- the previous value, the original object is returned.\n// --\n// -- Usage: `update(obj, key, fnUpdate)`\n// --\n// -- ```js\n// -- obj = { a: 1, b: 2, c: 3 }\n// -- obj2 = update(obj, 'b', (val) => val + 1)\n// -- // { a: 1, b: 3, c: 3 }\n// -- obj2 === obj\n// -- // false\n// --\n// -- // The same object is returned if there are no changes:\n// -- update(obj, 'b', (val) => val) === obj\n// -- // true\n// -- ```\n\n\nfunction update(obj, key, fnUpdate) {\n  const prevVal = obj == null ? undefined : obj[key];\n  const nextVal = fnUpdate(prevVal);\n  return set(obj, key, nextVal);\n} // -- #### updateIn()\n// -- Returns a new object with a modified **nested** attribute,\n// -- calculated via a user-provided callback based on the current value.\n// -- If the calculated value is the same as (*referentially equal to*)\n// -- the previous value, the original object is returned.\n// --\n// -- Usage: `updateIn<T: ArrayOrObject>(obj: T, path: Array<Key>,\n// -- fnUpdate: (prevValue: any) => any): T`\n// --\n// -- ```js\n// -- obj = { a: 1, d: { d1: 3, d2: 4 } }\n// -- obj2 = updateIn(obj, ['d', 'd1'], (val) => val + 1)\n// -- // { a: 1, d: { d1: 4, d2: 4 } }\n// -- obj2 === obj\n// -- // false\n// --\n// -- // The same object is returned if there are no changes:\n// -- obj3 = updateIn(obj, ['d', 'd1'], (val) => val)\n// -- // { a: 1, d: { d1: 3, d2: 4 } }\n// -- obj3 === obj\n// -- // true\n// -- ```\n\n\nfunction updateIn(obj, path, fnUpdate) {\n  const prevVal = getIn(obj, path);\n  const nextVal = fnUpdate(prevVal);\n  return setIn(obj, path, nextVal);\n} // -- #### merge()\n// -- Returns a new object built as follows: the overlapping keys from the\n// -- second one overwrite the corresponding entries from the first one.\n// -- Similar to `Object.assign()`, but immutable.\n// --\n// -- Usage:\n// --\n// -- * `merge(obj1, obj2)`\n// -- * `merge(obj1, ...objects)`\n// --\n// -- The unmodified `obj1` is returned if `obj2` does not *provide something\n// -- new to* `obj1`, i.e. if either of the following\n// -- conditions are true:\n// --\n// -- * `obj2` is `null` or `undefined`\n// -- * `obj2` is an object, but it is empty\n// -- * All attributes of `obj2` are `undefined`\n// -- * All attributes of `obj2` are referentially equal to the\n// --   corresponding attributes of `obj1`\n// --\n// -- Note that `undefined` attributes in `obj2` do not modify the\n// -- corresponding attributes in `obj1`.\n// --\n// -- ```js\n// -- obj1 = { a: 1, b: 2, c: 3 }\n// -- obj2 = { c: 4, d: 5 }\n// -- obj3 = merge(obj1, obj2)\n// -- // { a: 1, b: 2, c: 4, d: 5 }\n// -- obj3 === obj1\n// -- // false\n// --\n// -- // The same object is returned if there are no changes:\n// -- merge(obj1, { c: 3 }) === obj1\n// -- // true\n// -- ```\n// Signatures:\n// - 1 arg\n\n\n// Implementation\nfunction merge(a, b, c, d, e, f, ...rest) {\n  return rest.length ? doMerge.call(null, false, false, a, b, c, d, e, f, ...rest) : doMerge(false, false, a, b, c, d, e, f);\n} // -- #### mergeDeep()\n// -- Returns a new object built as follows: the overlapping keys from the\n// -- second one overwrite the corresponding entries from the first one.\n// -- If both the first and second entries are objects they are merged recursively.\n// -- Similar to `Object.assign()`, but immutable, and deeply merging.\n// --\n// -- Usage:\n// --\n// -- * `mergeDeep(obj1, obj2)`\n// -- * `mergeDeep(obj1, ...objects)`\n// --\n// -- The unmodified `obj1` is returned if `obj2` does not *provide something\n// -- new to* `obj1`, i.e. if either of the following\n// -- conditions are true:\n// --\n// -- * `obj2` is `null` or `undefined`\n// -- * `obj2` is an object, but it is empty\n// -- * All attributes of `obj2` are `undefined`\n// -- * All attributes of `obj2` are referentially equal to the\n// --   corresponding attributes of `obj1`\n// --\n// -- Note that `undefined` attributes in `obj2` do not modify the\n// -- corresponding attributes in `obj1`.\n// --\n// -- ```js\n// -- obj1 = { a: 1, b: 2, c: { a: 1 } }\n// -- obj2 = { b: 3, c: { b: 2 } }\n// -- obj3 = mergeDeep(obj1, obj2)\n// -- // { a: 1, b: 3, c: { a: 1, b: 2 }  }\n// -- obj3 === obj1\n// -- // false\n// --\n// -- // The same object is returned if there are no changes:\n// -- mergeDeep(obj1, { c: { a: 1 } }) === obj1\n// -- // true\n// -- ```\n\n\nfunction mergeDeep(a, b, c, d, e, f, ...rest) {\n  return rest.length ? doMerge.call(null, false, true, a, b, c, d, e, f, ...rest) : doMerge(false, true, a, b, c, d, e, f);\n} // -- #### mergeIn()\n// -- Similar to `merge()`, but merging the value at a given nested path.\n// --\n// -- Usage examples:\n// --\n// -- * `mergeIn(obj1, path, obj2)`\n// -- * `mergeIn(obj1, path, ...objects)`\n// --\n// -- ```js\n// -- obj1 = { a: 1, d: { b: { d1: 3, d2: 4 } } }\n// -- obj2 = { d3: 5 }\n// -- obj3 = mergeIn(obj1, ['d', 'b'], obj2)\n// -- // { a: 1, d: { b: { d1: 3, d2: 4, d3: 5 } } }\n// -- obj3 === obj1\n// -- // false\n// --\n// -- // The same object is returned if there are no changes:\n// -- mergeIn(obj1, ['d', 'b'], { d2: 4 }) === obj1\n// -- // true\n// -- ```\n\n\nfunction mergeIn(a, path, b, c, d, e, f, ...rest) {\n  let prevVal = getIn(a, path);\n  if (prevVal == null) prevVal = {};\n  let nextVal;\n\n  if (rest.length) {\n    nextVal = doMerge.call(null, false, false, prevVal, b, c, d, e, f, ...rest);\n  } else {\n    nextVal = doMerge(false, false, prevVal, b, c, d, e, f);\n  }\n\n  return setIn(a, path, nextVal);\n} // -- #### omit()\n// -- Returns an object excluding one or several attributes.\n// --\n// -- Usage: `omit(obj, attrs)`\n//\n// -- ```js\n// -- obj = { a: 1, b: 2, c: 3, d: 4 }\n// -- omit(obj, 'a')\n// -- // { b: 2, c: 3, d: 4 }\n// -- omit(obj, ['b', 'c'])\n// -- // { a: 1, d: 4 }\n// --\n// -- // The same object is returned if there are no changes:\n// -- omit(obj, 'z') === obj1\n// -- // true\n// -- ```\n\n\nfunction omit(obj, attrs) {\n  const omitList = Array.isArray(attrs) ? attrs : [attrs];\n  let fDoSomething = false;\n\n  for (let i = 0; i < omitList.length; i++) {\n    if (hasOwnProperty.call(obj, omitList[i])) {\n      fDoSomething = true;\n      break;\n    }\n  }\n\n  if (!fDoSomething) return obj;\n  const out = {};\n  const keys = getKeysAndSymbols(obj);\n\n  for (let i = 0; i < keys.length; i++) {\n    const key = keys[i];\n    if (omitList.indexOf(key) >= 0) continue;\n    out[key] = obj[key];\n  }\n\n  return out;\n} // -- #### addDefaults()\n// -- Returns a new object built as follows: `undefined` keys in the first one\n// -- are filled in with the corresponding values from the second one\n// -- (even if they are `null`).\n// --\n// -- Usage:\n// --\n// -- * `addDefaults(obj, defaults)`\n// -- * `addDefaults(obj, ...defaultObjects)`\n// --\n// -- ```js\n// -- obj1 = { a: 1, b: 2, c: 3 }\n// -- obj2 = { c: 4, d: 5, e: null }\n// -- obj3 = addDefaults(obj1, obj2)\n// -- // { a: 1, b: 2, c: 3, d: 5, e: null }\n// -- obj3 === obj1\n// -- // false\n// --\n// -- // The same object is returned if there are no changes:\n// -- addDefaults(obj1, { c: 4 }) === obj1\n// -- // true\n// -- ```\n// Signatures:\n// - 2 args\n\n\n// Implementation and catch-all\nfunction addDefaults(a, b, c, d, e, f, ...rest) {\n  return rest.length ? doMerge.call(null, true, false, a, b, c, d, e, f, ...rest) : doMerge(true, false, a, b, c, d, e, f);\n}\n\nfunction doMerge(fAddDefaults, fDeep, first, ...rest) {\n  let out = first;\n\n  if (!(out != null)) {\n    throwStr(IS_DEV ? 'At least one object should be provided to merge()' : INVALID_ARGS);\n  }\n\n  let fChanged = false;\n\n  for (let idx = 0; idx < rest.length; idx++) {\n    const obj = rest[idx];\n    if (obj == null) continue;\n    const keys = getKeysAndSymbols(obj);\n    if (!keys.length) continue;\n\n    for (let j = 0; j <= keys.length; j++) {\n      const key = keys[j];\n      if (fAddDefaults && out[key] !== undefined) continue;\n      let nextVal = obj[key];\n\n      if (fDeep && isObject(out[key]) && isObject(nextVal)) {\n        nextVal = doMerge(fAddDefaults, fDeep, out[key], nextVal);\n      }\n\n      if (nextVal === undefined || nextVal === out[key]) continue;\n\n      if (!fChanged) {\n        fChanged = true;\n        out = clone(out);\n      }\n\n      out[key] = nextVal;\n    }\n  }\n\n  return out;\n} // ===============================================\n// ### Public API\n// ===============================================\n\n\nconst timm = {\n  clone,\n  addLast,\n  addFirst,\n  removeLast,\n  removeFirst,\n  insert,\n  removeAt,\n  replaceAt,\n  getIn,\n  set,\n  setIn,\n  update,\n  updateIn,\n  merge,\n  mergeDeep,\n  mergeIn,\n  omit,\n  addDefaults\n};\nvar _default = timm;\nexports.default = _default;"]}