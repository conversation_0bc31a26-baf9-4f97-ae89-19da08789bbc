var config = require('../../utils/config');
var url = config.url;
var util = require('../../utils/util');
var db = require('../../utils/db');
var empno = 'FE717'; // 暂时hard code，应该是从登陆用户找到对应的工号

Page({
  data: {
    deviceList: []
  },
  onLoad: function (options) {
    this.queryAllBorrowDevices();
  },
  onReady: function () {
    // 页面渲染完成
  },
  onShow: function () {
    // 页面显示
  },
  onHide: function () {
    // 页面隐藏
  },
  onUnload: function () {
    // 页面关闭
  },
  queryAllBorrowDevices: function () {
    var that = this;
    var options = {
      url: config.clubApi.get,
      data: {
        appkey: config.appKey,
        key: empno,
        type: 'deviceBorrow'
      }
    };

    util.request(options, function (res) {
      if (res.statusCode === 200 && res.data && res.data.result) {
        var devices = [];
        var results = res.data.result.value;
        if (results && Array.isArray(results)) {
          results.forEach(result => {
            var option1 = {
              url: config.clubApi.get,
              data: {
                appkey: config.appKey,
                type: 'deviceLibrary',
                key: result.deviceId
              }
            };

            util.request(option1, function (res1, err1) {
              if (res1 && res1.data && res1.data.result) {
                var device = res1.data.result.value;
                device["borrowDate"] = util.formatTime(result.borrowDate);
                device["shouldBackDate"] = util.formatTime(result.borrowDate + 86400000 * 30);
                devices.push(device);
                that.setData({
                  deviceList: devices
                });
              } else {
                console.error('No device found or invalid response:', res1);
              }
            }, function (err1) {
              console.error('Error querying device:', err1);
            });
          });
        }
      } else {
        console.error('No borrow devices found or invalid response:', res);
      }
    }, function (err) {
      console.error('Error loading borrow devices:', err);
    });
  },
  queryOneDevice: function (key) {
    var that = this;

    var options = {
      url: config.clubApi.list,
      data: {
        appkey: config.appKey,
        type: 'deviceLibrary',
        key: key
      }
    };

    util.request(options, function (res, err) {
      if (res.statusCode === 200 && res.data && res.data.result) {
        var devices = res.data.result.map(item => item.value);
        that.setData({
          deviceList: devices
        });
      } else {
        console.error('No device found or invalid response:', res);
      }
    }, function (err) {
      console.error('Error querying device:', err);
    });
  }
});