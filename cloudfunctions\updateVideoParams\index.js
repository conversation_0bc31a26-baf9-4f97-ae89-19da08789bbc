// 云函数入口文件
const cloud = require('wx-server-sdk')

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})

const db = cloud.database()

// 云函数入口函数
exports.main = async (event, context) => {
  const wxContext = cloud.getWXContext()
  const openid = wxContext.OPENID

  try {
    const { deviceId, parameters } = event

    // 验证设备是否存在且属于当前用户
    const deviceResult = await db.collection('devices').doc(deviceId).get()
    
    if (!deviceResult.data || deviceResult.data.owner !== openid) {
      throw new Error('设备不存在或无权限')
    }

    // 更新参数设置
    const paramData = {
      userId: openid,
      deviceId: deviceId,
      ...parameters,
      updateTime: db.serverDate()
    }

    // 查找是否存在参数记录
    const paramResult = await db.collection('parameters').where({
      userId: openid,
      deviceId: deviceId
    }).get()

    let result
    if (paramResult.data.length > 0) {
      // 更新现有记录
      result = await db.collection('parameters').doc(paramResult.data[0]._id).update({
        data: paramData
      })
    } else {
      // 创建新记录
      paramData.createTime = db.serverDate()
      paramData.isDefault = false
      result = await db.collection('parameters').add({
        data: paramData
      })
    }

    // 这里应该调用实际的设备参数更新逻辑
    // 目前使用模拟函数
    await updateDeviceParameters(deviceId, parameters)

    return {
      success: true,
      data: {
        parameterId: result._id || paramResult.data[0]._id
      }
    }
  } catch (error) {
    console.error('更新视频参数失败:', error)
    return {
      success: false,
      error: error
    }
  }
}

// 模拟更新设备参数
async function updateDeviceParameters(deviceId, parameters) {
  // 这里应该是实际的设备参数更新逻辑
  // 目前仅作为示例
  console.log(`更新设备 ${deviceId} 的参数:`, parameters)
  return true
} 