{"version": 3, "sources": ["tinycolor.js"], "names": [], "mappings": ";;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.js", "sourcesContent": ["// This file is autogenerated. It's used to publish CJS to npm.\n(function (global, factory) {\n  typeof exports === 'object' && typeof module !== 'undefined' ? module.exports = factory() :\n  typeof define === 'function' && define.amd ? define(factory) :\n  (global = typeof globalThis !== 'undefined' ? globalThis : global || self, global.tinycolor = factory());\n})(this, (function () { \n\n  function _typeof(obj) {\n    \"@babel/helpers - typeof\";\n\n    return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (obj) {\n      return typeof obj;\n    } : function (obj) {\n      return obj && \"function\" == typeof Symbol && obj.constructor === Symbol && obj !== Symbol.prototype ? \"symbol\" : typeof obj;\n    }, _typeof(obj);\n  }\n\n  // https://github.com/bgrins/TinyColor\n  // <PERSON>, MIT License\n\n  var trimLeft = /^\\s+/;\n  var trimRight = /\\s+$/;\n  function tinycolor(color, opts) {\n    color = color ? color : \"\";\n    opts = opts || {};\n\n    // If input is already a tinycolor, return itself\n    if (color instanceof tinycolor) {\n      return color;\n    }\n    // If we are called as a function, call using new instead\n    if (!(this instanceof tinycolor)) {\n      return new tinycolor(color, opts);\n    }\n    var rgb = inputToRGB(color);\n    this._originalInput = color, this._r = rgb.r, this._g = rgb.g, this._b = rgb.b, this._a = rgb.a, this._roundA = Math.round(100 * this._a) / 100, this._format = opts.format || rgb.format;\n    this._gradientType = opts.gradientType;\n\n    // Don't let the range of [0,255] come back in [0,1].\n    // Potentially lose a little bit of precision here, but will fix issues where\n    // .5 gets interpreted as half of the total, instead of half of 1\n    // If it was supposed to be 128, this was already taken care of by `inputToRgb`\n    if (this._r < 1) this._r = Math.round(this._r);\n    if (this._g < 1) this._g = Math.round(this._g);\n    if (this._b < 1) this._b = Math.round(this._b);\n    this._ok = rgb.ok;\n  }\n  tinycolor.prototype = {\n    isDark: function isDark() {\n      return this.getBrightness() < 128;\n    },\n    isLight: function isLight() {\n      return !this.isDark();\n    },\n    isValid: function isValid() {\n      return this._ok;\n    },\n    getOriginalInput: function getOriginalInput() {\n      return this._originalInput;\n    },\n    getFormat: function getFormat() {\n      return this._format;\n    },\n    getAlpha: function getAlpha() {\n      return this._a;\n    },\n    getBrightness: function getBrightness() {\n      //http://www.w3.org/TR/AERT#color-contrast\n      var rgb = this.toRgb();\n      return (rgb.r * 299 + rgb.g * 587 + rgb.b * 114) / 1000;\n    },\n    getLuminance: function getLuminance() {\n      //http://www.w3.org/TR/2008/REC-WCAG20-20081211/#relativeluminancedef\n      var rgb = this.toRgb();\n      var RsRGB, GsRGB, BsRGB, R, G, B;\n      RsRGB = rgb.r / 255;\n      GsRGB = rgb.g / 255;\n      BsRGB = rgb.b / 255;\n      if (RsRGB <= 0.03928) R = RsRGB / 12.92;else R = Math.pow((RsRGB + 0.055) / 1.055, 2.4);\n      if (GsRGB <= 0.03928) G = GsRGB / 12.92;else G = Math.pow((GsRGB + 0.055) / 1.055, 2.4);\n      if (BsRGB <= 0.03928) B = BsRGB / 12.92;else B = Math.pow((BsRGB + 0.055) / 1.055, 2.4);\n      return 0.2126 * R + 0.7152 * G + 0.0722 * B;\n    },\n    setAlpha: function setAlpha(value) {\n      this._a = boundAlpha(value);\n      this._roundA = Math.round(100 * this._a) / 100;\n      return this;\n    },\n    toHsv: function toHsv() {\n      var hsv = rgbToHsv(this._r, this._g, this._b);\n      return {\n        h: hsv.h * 360,\n        s: hsv.s,\n        v: hsv.v,\n        a: this._a\n      };\n    },\n    toHsvString: function toHsvString() {\n      var hsv = rgbToHsv(this._r, this._g, this._b);\n      var h = Math.round(hsv.h * 360),\n        s = Math.round(hsv.s * 100),\n        v = Math.round(hsv.v * 100);\n      return this._a == 1 ? \"hsv(\" + h + \", \" + s + \"%, \" + v + \"%)\" : \"hsva(\" + h + \", \" + s + \"%, \" + v + \"%, \" + this._roundA + \")\";\n    },\n    toHsl: function toHsl() {\n      var hsl = rgbToHsl(this._r, this._g, this._b);\n      return {\n        h: hsl.h * 360,\n        s: hsl.s,\n        l: hsl.l,\n        a: this._a\n      };\n    },\n    toHslString: function toHslString() {\n      var hsl = rgbToHsl(this._r, this._g, this._b);\n      var h = Math.round(hsl.h * 360),\n        s = Math.round(hsl.s * 100),\n        l = Math.round(hsl.l * 100);\n      return this._a == 1 ? \"hsl(\" + h + \", \" + s + \"%, \" + l + \"%)\" : \"hsla(\" + h + \", \" + s + \"%, \" + l + \"%, \" + this._roundA + \")\";\n    },\n    toHex: function toHex(allow3Char) {\n      return rgbToHex(this._r, this._g, this._b, allow3Char);\n    },\n    toHexString: function toHexString(allow3Char) {\n      return \"#\" + this.toHex(allow3Char);\n    },\n    toHex8: function toHex8(allow4Char) {\n      return rgbaToHex(this._r, this._g, this._b, this._a, allow4Char);\n    },\n    toHex8String: function toHex8String(allow4Char) {\n      return \"#\" + this.toHex8(allow4Char);\n    },\n    toRgb: function toRgb() {\n      return {\n        r: Math.round(this._r),\n        g: Math.round(this._g),\n        b: Math.round(this._b),\n        a: this._a\n      };\n    },\n    toRgbString: function toRgbString() {\n      return this._a == 1 ? \"rgb(\" + Math.round(this._r) + \", \" + Math.round(this._g) + \", \" + Math.round(this._b) + \")\" : \"rgba(\" + Math.round(this._r) + \", \" + Math.round(this._g) + \", \" + Math.round(this._b) + \", \" + this._roundA + \")\";\n    },\n    toPercentageRgb: function toPercentageRgb() {\n      return {\n        r: Math.round(bound01(this._r, 255) * 100) + \"%\",\n        g: Math.round(bound01(this._g, 255) * 100) + \"%\",\n        b: Math.round(bound01(this._b, 255) * 100) + \"%\",\n        a: this._a\n      };\n    },\n    toPercentageRgbString: function toPercentageRgbString() {\n      return this._a == 1 ? \"rgb(\" + Math.round(bound01(this._r, 255) * 100) + \"%, \" + Math.round(bound01(this._g, 255) * 100) + \"%, \" + Math.round(bound01(this._b, 255) * 100) + \"%)\" : \"rgba(\" + Math.round(bound01(this._r, 255) * 100) + \"%, \" + Math.round(bound01(this._g, 255) * 100) + \"%, \" + Math.round(bound01(this._b, 255) * 100) + \"%, \" + this._roundA + \")\";\n    },\n    toName: function toName() {\n      if (this._a === 0) {\n        return \"transparent\";\n      }\n      if (this._a < 1) {\n        return false;\n      }\n      return hexNames[rgbToHex(this._r, this._g, this._b, true)] || false;\n    },\n    toFilter: function toFilter(secondColor) {\n      var hex8String = \"#\" + rgbaToArgbHex(this._r, this._g, this._b, this._a);\n      var secondHex8String = hex8String;\n      var gradientType = this._gradientType ? \"GradientType = 1, \" : \"\";\n      if (secondColor) {\n        var s = tinycolor(secondColor);\n        secondHex8String = \"#\" + rgbaToArgbHex(s._r, s._g, s._b, s._a);\n      }\n      return \"progid:DXImageTransform.Microsoft.gradient(\" + gradientType + \"startColorstr=\" + hex8String + \",endColorstr=\" + secondHex8String + \")\";\n    },\n    toString: function toString(format) {\n      var formatSet = !!format;\n      format = format || this._format;\n      var formattedString = false;\n      var hasAlpha = this._a < 1 && this._a >= 0;\n      var needsAlphaFormat = !formatSet && hasAlpha && (format === \"hex\" || format === \"hex6\" || format === \"hex3\" || format === \"hex4\" || format === \"hex8\" || format === \"name\");\n      if (needsAlphaFormat) {\n        // Special case for \"transparent\", all other non-alpha formats\n        // will return rgba when there is transparency.\n        if (format === \"name\" && this._a === 0) {\n          return this.toName();\n        }\n        return this.toRgbString();\n      }\n      if (format === \"rgb\") {\n        formattedString = this.toRgbString();\n      }\n      if (format === \"prgb\") {\n        formattedString = this.toPercentageRgbString();\n      }\n      if (format === \"hex\" || format === \"hex6\") {\n        formattedString = this.toHexString();\n      }\n      if (format === \"hex3\") {\n        formattedString = this.toHexString(true);\n      }\n      if (format === \"hex4\") {\n        formattedString = this.toHex8String(true);\n      }\n      if (format === \"hex8\") {\n        formattedString = this.toHex8String();\n      }\n      if (format === \"name\") {\n        formattedString = this.toName();\n      }\n      if (format === \"hsl\") {\n        formattedString = this.toHslString();\n      }\n      if (format === \"hsv\") {\n        formattedString = this.toHsvString();\n      }\n      return formattedString || this.toHexString();\n    },\n    clone: function clone() {\n      return tinycolor(this.toString());\n    },\n    _applyModification: function _applyModification(fn, args) {\n      var color = fn.apply(null, [this].concat([].slice.call(args)));\n      this._r = color._r;\n      this._g = color._g;\n      this._b = color._b;\n      this.setAlpha(color._a);\n      return this;\n    },\n    lighten: function lighten() {\n      return this._applyModification(_lighten, arguments);\n    },\n    brighten: function brighten() {\n      return this._applyModification(_brighten, arguments);\n    },\n    darken: function darken() {\n      return this._applyModification(_darken, arguments);\n    },\n    desaturate: function desaturate() {\n      return this._applyModification(_desaturate, arguments);\n    },\n    saturate: function saturate() {\n      return this._applyModification(_saturate, arguments);\n    },\n    greyscale: function greyscale() {\n      return this._applyModification(_greyscale, arguments);\n    },\n    spin: function spin() {\n      return this._applyModification(_spin, arguments);\n    },\n    _applyCombination: function _applyCombination(fn, args) {\n      return fn.apply(null, [this].concat([].slice.call(args)));\n    },\n    analogous: function analogous() {\n      return this._applyCombination(_analogous, arguments);\n    },\n    complement: function complement() {\n      return this._applyCombination(_complement, arguments);\n    },\n    monochromatic: function monochromatic() {\n      return this._applyCombination(_monochromatic, arguments);\n    },\n    splitcomplement: function splitcomplement() {\n      return this._applyCombination(_splitcomplement, arguments);\n    },\n    // Disabled until https://github.com/bgrins/TinyColor/issues/254\n    // polyad: function (number) {\n    //   return this._applyCombination(polyad, [number]);\n    // },\n    triad: function triad() {\n      return this._applyCombination(polyad, [3]);\n    },\n    tetrad: function tetrad() {\n      return this._applyCombination(polyad, [4]);\n    }\n  };\n\n  // If input is an object, force 1 into \"1.0\" to handle ratios properly\n  // String input requires \"1.0\" as input, so 1 will be treated as 1\n  tinycolor.fromRatio = function (color, opts) {\n    if (_typeof(color) == \"object\") {\n      var newColor = {};\n      for (var i in color) {\n        if (color.hasOwnProperty(i)) {\n          if (i === \"a\") {\n            newColor[i] = color[i];\n          } else {\n            newColor[i] = convertToPercentage(color[i]);\n          }\n        }\n      }\n      color = newColor;\n    }\n    return tinycolor(color, opts);\n  };\n\n  // Given a string or object, convert that input to RGB\n  // Possible string inputs:\n  //\n  //     \"red\"\n  //     \"#f00\" or \"f00\"\n  //     \"#ff0000\" or \"ff0000\"\n  //     \"#ff000000\" or \"ff000000\"\n  //     \"rgb 255 0 0\" or \"rgb (255, 0, 0)\"\n  //     \"rgb 1.0 0 0\" or \"rgb (1, 0, 0)\"\n  //     \"rgba (255, 0, 0, 1)\" or \"rgba 255, 0, 0, 1\"\n  //     \"rgba (1.0, 0, 0, 1)\" or \"rgba 1.0, 0, 0, 1\"\n  //     \"hsl(0, 100%, 50%)\" or \"hsl 0 100% 50%\"\n  //     \"hsla(0, 100%, 50%, 1)\" or \"hsla 0 100% 50%, 1\"\n  //     \"hsv(0, 100%, 100%)\" or \"hsv 0 100% 100%\"\n  //\n  function inputToRGB(color) {\n    var rgb = {\n      r: 0,\n      g: 0,\n      b: 0\n    };\n    var a = 1;\n    var s = null;\n    var v = null;\n    var l = null;\n    var ok = false;\n    var format = false;\n    if (typeof color == \"string\") {\n      color = stringInputToObject(color);\n    }\n    if (_typeof(color) == \"object\") {\n      if (isValidCSSUnit(color.r) && isValidCSSUnit(color.g) && isValidCSSUnit(color.b)) {\n        rgb = rgbToRgb(color.r, color.g, color.b);\n        ok = true;\n        format = String(color.r).substr(-1) === \"%\" ? \"prgb\" : \"rgb\";\n      } else if (isValidCSSUnit(color.h) && isValidCSSUnit(color.s) && isValidCSSUnit(color.v)) {\n        s = convertToPercentage(color.s);\n        v = convertToPercentage(color.v);\n        rgb = hsvToRgb(color.h, s, v);\n        ok = true;\n        format = \"hsv\";\n      } else if (isValidCSSUnit(color.h) && isValidCSSUnit(color.s) && isValidCSSUnit(color.l)) {\n        s = convertToPercentage(color.s);\n        l = convertToPercentage(color.l);\n        rgb = hslToRgb(color.h, s, l);\n        ok = true;\n        format = \"hsl\";\n      }\n      if (color.hasOwnProperty(\"a\")) {\n        a = color.a;\n      }\n    }\n    a = boundAlpha(a);\n    return {\n      ok: ok,\n      format: color.format || format,\n      r: Math.min(255, Math.max(rgb.r, 0)),\n      g: Math.min(255, Math.max(rgb.g, 0)),\n      b: Math.min(255, Math.max(rgb.b, 0)),\n      a: a\n    };\n  }\n\n  // Conversion Functions\n  // --------------------\n\n  // `rgbToHsl`, `rgbToHsv`, `hslToRgb`, `hsvToRgb` modified from:\n  // <http://mjijackson.com/2008/02/rgb-to-hsl-and-rgb-to-hsv-color-model-conversion-algorithms-in-javascript>\n\n  // `rgbToRgb`\n  // Handle bounds / percentage checking to conform to CSS color spec\n  // <http://www.w3.org/TR/css3-color/>\n  // *Assumes:* r, g, b in [0, 255] or [0, 1]\n  // *Returns:* { r, g, b } in [0, 255]\n  function rgbToRgb(r, g, b) {\n    return {\n      r: bound01(r, 255) * 255,\n      g: bound01(g, 255) * 255,\n      b: bound01(b, 255) * 255\n    };\n  }\n\n  // `rgbToHsl`\n  // Converts an RGB color value to HSL.\n  // *Assumes:* r, g, and b are contained in [0, 255] or [0, 1]\n  // *Returns:* { h, s, l } in [0,1]\n  function rgbToHsl(r, g, b) {\n    r = bound01(r, 255);\n    g = bound01(g, 255);\n    b = bound01(b, 255);\n    var max = Math.max(r, g, b),\n      min = Math.min(r, g, b);\n    var h,\n      s,\n      l = (max + min) / 2;\n    if (max == min) {\n      h = s = 0; // achromatic\n    } else {\n      var d = max - min;\n      s = l > 0.5 ? d / (2 - max - min) : d / (max + min);\n      switch (max) {\n        case r:\n          h = (g - b) / d + (g < b ? 6 : 0);\n          break;\n        case g:\n          h = (b - r) / d + 2;\n          break;\n        case b:\n          h = (r - g) / d + 4;\n          break;\n      }\n      h /= 6;\n    }\n    return {\n      h: h,\n      s: s,\n      l: l\n    };\n  }\n\n  // `hslToRgb`\n  // Converts an HSL color value to RGB.\n  // *Assumes:* h is contained in [0, 1] or [0, 360] and s and l are contained [0, 1] or [0, 100]\n  // *Returns:* { r, g, b } in the set [0, 255]\n  function hslToRgb(h, s, l) {\n    var r, g, b;\n    h = bound01(h, 360);\n    s = bound01(s, 100);\n    l = bound01(l, 100);\n    function hue2rgb(p, q, t) {\n      if (t < 0) t += 1;\n      if (t > 1) t -= 1;\n      if (t < 1 / 6) return p + (q - p) * 6 * t;\n      if (t < 1 / 2) return q;\n      if (t < 2 / 3) return p + (q - p) * (2 / 3 - t) * 6;\n      return p;\n    }\n    if (s === 0) {\n      r = g = b = l; // achromatic\n    } else {\n      var q = l < 0.5 ? l * (1 + s) : l + s - l * s;\n      var p = 2 * l - q;\n      r = hue2rgb(p, q, h + 1 / 3);\n      g = hue2rgb(p, q, h);\n      b = hue2rgb(p, q, h - 1 / 3);\n    }\n    return {\n      r: r * 255,\n      g: g * 255,\n      b: b * 255\n    };\n  }\n\n  // `rgbToHsv`\n  // Converts an RGB color value to HSV\n  // *Assumes:* r, g, and b are contained in the set [0, 255] or [0, 1]\n  // *Returns:* { h, s, v } in [0,1]\n  function rgbToHsv(r, g, b) {\n    r = bound01(r, 255);\n    g = bound01(g, 255);\n    b = bound01(b, 255);\n    var max = Math.max(r, g, b),\n      min = Math.min(r, g, b);\n    var h,\n      s,\n      v = max;\n    var d = max - min;\n    s = max === 0 ? 0 : d / max;\n    if (max == min) {\n      h = 0; // achromatic\n    } else {\n      switch (max) {\n        case r:\n          h = (g - b) / d + (g < b ? 6 : 0);\n          break;\n        case g:\n          h = (b - r) / d + 2;\n          break;\n        case b:\n          h = (r - g) / d + 4;\n          break;\n      }\n      h /= 6;\n    }\n    return {\n      h: h,\n      s: s,\n      v: v\n    };\n  }\n\n  // `hsvToRgb`\n  // Converts an HSV color value to RGB.\n  // *Assumes:* h is contained in [0, 1] or [0, 360] and s and v are contained in [0, 1] or [0, 100]\n  // *Returns:* { r, g, b } in the set [0, 255]\n  function hsvToRgb(h, s, v) {\n    h = bound01(h, 360) * 6;\n    s = bound01(s, 100);\n    v = bound01(v, 100);\n    var i = Math.floor(h),\n      f = h - i,\n      p = v * (1 - s),\n      q = v * (1 - f * s),\n      t = v * (1 - (1 - f) * s),\n      mod = i % 6,\n      r = [v, q, p, p, t, v][mod],\n      g = [t, v, v, q, p, p][mod],\n      b = [p, p, t, v, v, q][mod];\n    return {\n      r: r * 255,\n      g: g * 255,\n      b: b * 255\n    };\n  }\n\n  // `rgbToHex`\n  // Converts an RGB color to hex\n  // Assumes r, g, and b are contained in the set [0, 255]\n  // Returns a 3 or 6 character hex\n  function rgbToHex(r, g, b, allow3Char) {\n    var hex = [pad2(Math.round(r).toString(16)), pad2(Math.round(g).toString(16)), pad2(Math.round(b).toString(16))];\n\n    // Return a 3 character hex if possible\n    if (allow3Char && hex[0].charAt(0) == hex[0].charAt(1) && hex[1].charAt(0) == hex[1].charAt(1) && hex[2].charAt(0) == hex[2].charAt(1)) {\n      return hex[0].charAt(0) + hex[1].charAt(0) + hex[2].charAt(0);\n    }\n    return hex.join(\"\");\n  }\n\n  // `rgbaToHex`\n  // Converts an RGBA color plus alpha transparency to hex\n  // Assumes r, g, b are contained in the set [0, 255] and\n  // a in [0, 1]. Returns a 4 or 8 character rgba hex\n  function rgbaToHex(r, g, b, a, allow4Char) {\n    var hex = [pad2(Math.round(r).toString(16)), pad2(Math.round(g).toString(16)), pad2(Math.round(b).toString(16)), pad2(convertDecimalToHex(a))];\n\n    // Return a 4 character hex if possible\n    if (allow4Char && hex[0].charAt(0) == hex[0].charAt(1) && hex[1].charAt(0) == hex[1].charAt(1) && hex[2].charAt(0) == hex[2].charAt(1) && hex[3].charAt(0) == hex[3].charAt(1)) {\n      return hex[0].charAt(0) + hex[1].charAt(0) + hex[2].charAt(0) + hex[3].charAt(0);\n    }\n    return hex.join(\"\");\n  }\n\n  // `rgbaToArgbHex`\n  // Converts an RGBA color to an ARGB Hex8 string\n  // Rarely used, but required for \"toFilter()\"\n  function rgbaToArgbHex(r, g, b, a) {\n    var hex = [pad2(convertDecimalToHex(a)), pad2(Math.round(r).toString(16)), pad2(Math.round(g).toString(16)), pad2(Math.round(b).toString(16))];\n    return hex.join(\"\");\n  }\n\n  // `equals`\n  // Can be called with any tinycolor input\n  tinycolor.equals = function (color1, color2) {\n    if (!color1 || !color2) return false;\n    return tinycolor(color1).toRgbString() == tinycolor(color2).toRgbString();\n  };\n  tinycolor.random = function () {\n    return tinycolor.fromRatio({\n      r: Math.random(),\n      g: Math.random(),\n      b: Math.random()\n    });\n  };\n\n  // Modification Functions\n  // ----------------------\n  // Thanks to less.js for some of the basics here\n  // <https://github.com/cloudhead/less.js/blob/master/lib/less/functions.js>\n\n  function _desaturate(color, amount) {\n    amount = amount === 0 ? 0 : amount || 10;\n    var hsl = tinycolor(color).toHsl();\n    hsl.s -= amount / 100;\n    hsl.s = clamp01(hsl.s);\n    return tinycolor(hsl);\n  }\n  function _saturate(color, amount) {\n    amount = amount === 0 ? 0 : amount || 10;\n    var hsl = tinycolor(color).toHsl();\n    hsl.s += amount / 100;\n    hsl.s = clamp01(hsl.s);\n    return tinycolor(hsl);\n  }\n  function _greyscale(color) {\n    return tinycolor(color).desaturate(100);\n  }\n  function _lighten(color, amount) {\n    amount = amount === 0 ? 0 : amount || 10;\n    var hsl = tinycolor(color).toHsl();\n    hsl.l += amount / 100;\n    hsl.l = clamp01(hsl.l);\n    return tinycolor(hsl);\n  }\n  function _brighten(color, amount) {\n    amount = amount === 0 ? 0 : amount || 10;\n    var rgb = tinycolor(color).toRgb();\n    rgb.r = Math.max(0, Math.min(255, rgb.r - Math.round(255 * -(amount / 100))));\n    rgb.g = Math.max(0, Math.min(255, rgb.g - Math.round(255 * -(amount / 100))));\n    rgb.b = Math.max(0, Math.min(255, rgb.b - Math.round(255 * -(amount / 100))));\n    return tinycolor(rgb);\n  }\n  function _darken(color, amount) {\n    amount = amount === 0 ? 0 : amount || 10;\n    var hsl = tinycolor(color).toHsl();\n    hsl.l -= amount / 100;\n    hsl.l = clamp01(hsl.l);\n    return tinycolor(hsl);\n  }\n\n  // Spin takes a positive or negative amount within [-360, 360] indicating the change of hue.\n  // Values outside of this range will be wrapped into this range.\n  function _spin(color, amount) {\n    var hsl = tinycolor(color).toHsl();\n    var hue = (hsl.h + amount) % 360;\n    hsl.h = hue < 0 ? 360 + hue : hue;\n    return tinycolor(hsl);\n  }\n\n  // Combination Functions\n  // ---------------------\n  // Thanks to jQuery xColor for some of the ideas behind these\n  // <https://github.com/infusion/jQuery-xcolor/blob/master/jquery.xcolor.js>\n\n  function _complement(color) {\n    var hsl = tinycolor(color).toHsl();\n    hsl.h = (hsl.h + 180) % 360;\n    return tinycolor(hsl);\n  }\n  function polyad(color, number) {\n    if (isNaN(number) || number <= 0) {\n      throw new Error(\"Argument to polyad must be a positive number\");\n    }\n    var hsl = tinycolor(color).toHsl();\n    var result = [tinycolor(color)];\n    var step = 360 / number;\n    for (var i = 1; i < number; i++) {\n      result.push(tinycolor({\n        h: (hsl.h + i * step) % 360,\n        s: hsl.s,\n        l: hsl.l\n      }));\n    }\n    return result;\n  }\n  function _splitcomplement(color) {\n    var hsl = tinycolor(color).toHsl();\n    var h = hsl.h;\n    return [tinycolor(color), tinycolor({\n      h: (h + 72) % 360,\n      s: hsl.s,\n      l: hsl.l\n    }), tinycolor({\n      h: (h + 216) % 360,\n      s: hsl.s,\n      l: hsl.l\n    })];\n  }\n  function _analogous(color, results, slices) {\n    results = results || 6;\n    slices = slices || 30;\n    var hsl = tinycolor(color).toHsl();\n    var part = 360 / slices;\n    var ret = [tinycolor(color)];\n    for (hsl.h = (hsl.h - (part * results >> 1) + 720) % 360; --results;) {\n      hsl.h = (hsl.h + part) % 360;\n      ret.push(tinycolor(hsl));\n    }\n    return ret;\n  }\n  function _monochromatic(color, results) {\n    results = results || 6;\n    var hsv = tinycolor(color).toHsv();\n    var h = hsv.h,\n      s = hsv.s,\n      v = hsv.v;\n    var ret = [];\n    var modification = 1 / results;\n    while (results--) {\n      ret.push(tinycolor({\n        h: h,\n        s: s,\n        v: v\n      }));\n      v = (v + modification) % 1;\n    }\n    return ret;\n  }\n\n  // Utility Functions\n  // ---------------------\n\n  tinycolor.mix = function (color1, color2, amount) {\n    amount = amount === 0 ? 0 : amount || 50;\n    var rgb1 = tinycolor(color1).toRgb();\n    var rgb2 = tinycolor(color2).toRgb();\n    var p = amount / 100;\n    var rgba = {\n      r: (rgb2.r - rgb1.r) * p + rgb1.r,\n      g: (rgb2.g - rgb1.g) * p + rgb1.g,\n      b: (rgb2.b - rgb1.b) * p + rgb1.b,\n      a: (rgb2.a - rgb1.a) * p + rgb1.a\n    };\n    return tinycolor(rgba);\n  };\n\n  // Readability Functions\n  // ---------------------\n  // <http://www.w3.org/TR/2008/REC-WCAG20-20081211/#contrast-ratiodef (WCAG Version 2)\n\n  // `contrast`\n  // Analyze the 2 colors and returns the color contrast defined by (WCAG Version 2)\n  tinycolor.readability = function (color1, color2) {\n    var c1 = tinycolor(color1);\n    var c2 = tinycolor(color2);\n    return (Math.max(c1.getLuminance(), c2.getLuminance()) + 0.05) / (Math.min(c1.getLuminance(), c2.getLuminance()) + 0.05);\n  };\n\n  // `isReadable`\n  // Ensure that foreground and background color combinations meet WCAG2 guidelines.\n  // The third argument is an optional Object.\n  //      the 'level' property states 'AA' or 'AAA' - if missing or invalid, it defaults to 'AA';\n  //      the 'size' property states 'large' or 'small' - if missing or invalid, it defaults to 'small'.\n  // If the entire object is absent, isReadable defaults to {level:\"AA\",size:\"small\"}.\n\n  // *Example*\n  //    tinycolor.isReadable(\"#000\", \"#111\") => false\n  //    tinycolor.isReadable(\"#000\", \"#111\",{level:\"AA\",size:\"large\"}) => false\n  tinycolor.isReadable = function (color1, color2, wcag2) {\n    var readability = tinycolor.readability(color1, color2);\n    var wcag2Parms, out;\n    out = false;\n    wcag2Parms = validateWCAG2Parms(wcag2);\n    switch (wcag2Parms.level + wcag2Parms.size) {\n      case \"AAsmall\":\n      case \"AAAlarge\":\n        out = readability >= 4.5;\n        break;\n      case \"AAlarge\":\n        out = readability >= 3;\n        break;\n      case \"AAAsmall\":\n        out = readability >= 7;\n        break;\n    }\n    return out;\n  };\n\n  // `mostReadable`\n  // Given a base color and a list of possible foreground or background\n  // colors for that base, returns the most readable color.\n  // Optionally returns Black or White if the most readable color is unreadable.\n  // *Example*\n  //    tinycolor.mostReadable(tinycolor.mostReadable(\"#123\", [\"#124\", \"#125\"],{includeFallbackColors:false}).toHexString(); // \"#112255\"\n  //    tinycolor.mostReadable(tinycolor.mostReadable(\"#123\", [\"#124\", \"#125\"],{includeFallbackColors:true}).toHexString();  // \"#ffffff\"\n  //    tinycolor.mostReadable(\"#a8015a\", [\"#faf3f3\"],{includeFallbackColors:true,level:\"AAA\",size:\"large\"}).toHexString(); // \"#faf3f3\"\n  //    tinycolor.mostReadable(\"#a8015a\", [\"#faf3f3\"],{includeFallbackColors:true,level:\"AAA\",size:\"small\"}).toHexString(); // \"#ffffff\"\n  tinycolor.mostReadable = function (baseColor, colorList, args) {\n    var bestColor = null;\n    var bestScore = 0;\n    var readability;\n    var includeFallbackColors, level, size;\n    args = args || {};\n    includeFallbackColors = args.includeFallbackColors;\n    level = args.level;\n    size = args.size;\n    for (var i = 0; i < colorList.length; i++) {\n      readability = tinycolor.readability(baseColor, colorList[i]);\n      if (readability > bestScore) {\n        bestScore = readability;\n        bestColor = tinycolor(colorList[i]);\n      }\n    }\n    if (tinycolor.isReadable(baseColor, bestColor, {\n      level: level,\n      size: size\n    }) || !includeFallbackColors) {\n      return bestColor;\n    } else {\n      args.includeFallbackColors = false;\n      return tinycolor.mostReadable(baseColor, [\"#fff\", \"#000\"], args);\n    }\n  };\n\n  // Big List of Colors\n  // ------------------\n  // <https://www.w3.org/TR/css-color-4/#named-colors>\n  var names = tinycolor.names = {\n    aliceblue: \"f0f8ff\",\n    antiquewhite: \"faebd7\",\n    aqua: \"0ff\",\n    aquamarine: \"7fffd4\",\n    azure: \"f0ffff\",\n    beige: \"f5f5dc\",\n    bisque: \"ffe4c4\",\n    black: \"000\",\n    blanchedalmond: \"ffebcd\",\n    blue: \"00f\",\n    blueviolet: \"8a2be2\",\n    brown: \"a52a2a\",\n    burlywood: \"deb887\",\n    burntsienna: \"ea7e5d\",\n    cadetblue: \"5f9ea0\",\n    chartreuse: \"7fff00\",\n    chocolate: \"d2691e\",\n    coral: \"ff7f50\",\n    cornflowerblue: \"6495ed\",\n    cornsilk: \"fff8dc\",\n    crimson: \"dc143c\",\n    cyan: \"0ff\",\n    darkblue: \"00008b\",\n    darkcyan: \"008b8b\",\n    darkgoldenrod: \"b8860b\",\n    darkgray: \"a9a9a9\",\n    darkgreen: \"006400\",\n    darkgrey: \"a9a9a9\",\n    darkkhaki: \"bdb76b\",\n    darkmagenta: \"8b008b\",\n    darkolivegreen: \"556b2f\",\n    darkorange: \"ff8c00\",\n    darkorchid: \"9932cc\",\n    darkred: \"8b0000\",\n    darksalmon: \"e9967a\",\n    darkseagreen: \"8fbc8f\",\n    darkslateblue: \"483d8b\",\n    darkslategray: \"2f4f4f\",\n    darkslategrey: \"2f4f4f\",\n    darkturquoise: \"00ced1\",\n    darkviolet: \"9400d3\",\n    deeppink: \"ff1493\",\n    deepskyblue: \"00bfff\",\n    dimgray: \"696969\",\n    dimgrey: \"696969\",\n    dodgerblue: \"1e90ff\",\n    firebrick: \"b22222\",\n    floralwhite: \"fffaf0\",\n    forestgreen: \"228b22\",\n    fuchsia: \"f0f\",\n    gainsboro: \"dcdcdc\",\n    ghostwhite: \"f8f8ff\",\n    gold: \"ffd700\",\n    goldenrod: \"daa520\",\n    gray: \"808080\",\n    green: \"008000\",\n    greenyellow: \"adff2f\",\n    grey: \"808080\",\n    honeydew: \"f0fff0\",\n    hotpink: \"ff69b4\",\n    indianred: \"cd5c5c\",\n    indigo: \"4b0082\",\n    ivory: \"fffff0\",\n    khaki: \"f0e68c\",\n    lavender: \"e6e6fa\",\n    lavenderblush: \"fff0f5\",\n    lawngreen: \"7cfc00\",\n    lemonchiffon: \"fffacd\",\n    lightblue: \"add8e6\",\n    lightcoral: \"f08080\",\n    lightcyan: \"e0ffff\",\n    lightgoldenrodyellow: \"fafad2\",\n    lightgray: \"d3d3d3\",\n    lightgreen: \"90ee90\",\n    lightgrey: \"d3d3d3\",\n    lightpink: \"ffb6c1\",\n    lightsalmon: \"ffa07a\",\n    lightseagreen: \"20b2aa\",\n    lightskyblue: \"87cefa\",\n    lightslategray: \"789\",\n    lightslategrey: \"789\",\n    lightsteelblue: \"b0c4de\",\n    lightyellow: \"ffffe0\",\n    lime: \"0f0\",\n    limegreen: \"32cd32\",\n    linen: \"faf0e6\",\n    magenta: \"f0f\",\n    maroon: \"800000\",\n    mediumaquamarine: \"66cdaa\",\n    mediumblue: \"0000cd\",\n    mediumorchid: \"ba55d3\",\n    mediumpurple: \"9370db\",\n    mediumseagreen: \"3cb371\",\n    mediumslateblue: \"7b68ee\",\n    mediumspringgreen: \"00fa9a\",\n    mediumturquoise: \"48d1cc\",\n    mediumvioletred: \"c71585\",\n    midnightblue: \"191970\",\n    mintcream: \"f5fffa\",\n    mistyrose: \"ffe4e1\",\n    moccasin: \"ffe4b5\",\n    navajowhite: \"ffdead\",\n    navy: \"000080\",\n    oldlace: \"fdf5e6\",\n    olive: \"808000\",\n    olivedrab: \"6b8e23\",\n    orange: \"ffa500\",\n    orangered: \"ff4500\",\n    orchid: \"da70d6\",\n    palegoldenrod: \"eee8aa\",\n    palegreen: \"98fb98\",\n    paleturquoise: \"afeeee\",\n    palevioletred: \"db7093\",\n    papayawhip: \"ffefd5\",\n    peachpuff: \"ffdab9\",\n    peru: \"cd853f\",\n    pink: \"ffc0cb\",\n    plum: \"dda0dd\",\n    powderblue: \"b0e0e6\",\n    purple: \"800080\",\n    rebeccapurple: \"663399\",\n    red: \"f00\",\n    rosybrown: \"bc8f8f\",\n    royalblue: \"4169e1\",\n    saddlebrown: \"8b4513\",\n    salmon: \"fa8072\",\n    sandybrown: \"f4a460\",\n    seagreen: \"2e8b57\",\n    seashell: \"fff5ee\",\n    sienna: \"a0522d\",\n    silver: \"c0c0c0\",\n    skyblue: \"87ceeb\",\n    slateblue: \"6a5acd\",\n    slategray: \"708090\",\n    slategrey: \"708090\",\n    snow: \"fffafa\",\n    springgreen: \"00ff7f\",\n    steelblue: \"4682b4\",\n    tan: \"d2b48c\",\n    teal: \"008080\",\n    thistle: \"d8bfd8\",\n    tomato: \"ff6347\",\n    turquoise: \"40e0d0\",\n    violet: \"ee82ee\",\n    wheat: \"f5deb3\",\n    white: \"fff\",\n    whitesmoke: \"f5f5f5\",\n    yellow: \"ff0\",\n    yellowgreen: \"9acd32\"\n  };\n\n  // Make it easy to access colors via `hexNames[hex]`\n  var hexNames = tinycolor.hexNames = flip(names);\n\n  // Utilities\n  // ---------\n\n  // `{ 'name1': 'val1' }` becomes `{ 'val1': 'name1' }`\n  function flip(o) {\n    var flipped = {};\n    for (var i in o) {\n      if (o.hasOwnProperty(i)) {\n        flipped[o[i]] = i;\n      }\n    }\n    return flipped;\n  }\n\n  // Return a valid alpha value [0,1] with all invalid values being set to 1\n  function boundAlpha(a) {\n    a = parseFloat(a);\n    if (isNaN(a) || a < 0 || a > 1) {\n      a = 1;\n    }\n    return a;\n  }\n\n  // Take input from [0, n] and return it as [0, 1]\n  function bound01(n, max) {\n    if (isOnePointZero(n)) n = \"100%\";\n    var processPercent = isPercentage(n);\n    n = Math.min(max, Math.max(0, parseFloat(n)));\n\n    // Automatically convert percentage into number\n    if (processPercent) {\n      n = parseInt(n * max, 10) / 100;\n    }\n\n    // Handle floating point rounding errors\n    if (Math.abs(n - max) < 0.000001) {\n      return 1;\n    }\n\n    // Convert into [0, 1] range if it isn't already\n    return n % max / parseFloat(max);\n  }\n\n  // Force a number between 0 and 1\n  function clamp01(val) {\n    return Math.min(1, Math.max(0, val));\n  }\n\n  // Parse a base-16 hex value into a base-10 integer\n  function parseIntFromHex(val) {\n    return parseInt(val, 16);\n  }\n\n  // Need to handle 1.0 as 100%, since once it is a number, there is no difference between it and 1\n  // <http://stackoverflow.com/questions/7422072/javascript-how-to-detect-number-as-a-decimal-including-1-0>\n  function isOnePointZero(n) {\n    return typeof n == \"string\" && n.indexOf(\".\") != -1 && parseFloat(n) === 1;\n  }\n\n  // Check to see if string passed in is a percentage\n  function isPercentage(n) {\n    return typeof n === \"string\" && n.indexOf(\"%\") != -1;\n  }\n\n  // Force a hex value to have 2 characters\n  function pad2(c) {\n    return c.length == 1 ? \"0\" + c : \"\" + c;\n  }\n\n  // Replace a decimal with it's percentage value\n  function convertToPercentage(n) {\n    if (n <= 1) {\n      n = n * 100 + \"%\";\n    }\n    return n;\n  }\n\n  // Converts a decimal to a hex value\n  function convertDecimalToHex(d) {\n    return Math.round(parseFloat(d) * 255).toString(16);\n  }\n  // Converts a hex value to a decimal\n  function convertHexToDecimal(h) {\n    return parseIntFromHex(h) / 255;\n  }\n  var matchers = function () {\n    // <http://www.w3.org/TR/css3-values/#integers>\n    var CSS_INTEGER = \"[-\\\\+]?\\\\d+%?\";\n\n    // <http://www.w3.org/TR/css3-values/#number-value>\n    var CSS_NUMBER = \"[-\\\\+]?\\\\d*\\\\.\\\\d+%?\";\n\n    // Allow positive/negative integer/number.  Don't capture the either/or, just the entire outcome.\n    var CSS_UNIT = \"(?:\" + CSS_NUMBER + \")|(?:\" + CSS_INTEGER + \")\";\n\n    // Actual matching.\n    // Parentheses and commas are optional, but not required.\n    // Whitespace can take the place of commas or opening paren\n    var PERMISSIVE_MATCH3 = \"[\\\\s|\\\\(]+(\" + CSS_UNIT + \")[,|\\\\s]+(\" + CSS_UNIT + \")[,|\\\\s]+(\" + CSS_UNIT + \")\\\\s*\\\\)?\";\n    var PERMISSIVE_MATCH4 = \"[\\\\s|\\\\(]+(\" + CSS_UNIT + \")[,|\\\\s]+(\" + CSS_UNIT + \")[,|\\\\s]+(\" + CSS_UNIT + \")[,|\\\\s]+(\" + CSS_UNIT + \")\\\\s*\\\\)?\";\n    return {\n      CSS_UNIT: new RegExp(CSS_UNIT),\n      rgb: new RegExp(\"rgb\" + PERMISSIVE_MATCH3),\n      rgba: new RegExp(\"rgba\" + PERMISSIVE_MATCH4),\n      hsl: new RegExp(\"hsl\" + PERMISSIVE_MATCH3),\n      hsla: new RegExp(\"hsla\" + PERMISSIVE_MATCH4),\n      hsv: new RegExp(\"hsv\" + PERMISSIVE_MATCH3),\n      hsva: new RegExp(\"hsva\" + PERMISSIVE_MATCH4),\n      hex3: /^#?([0-9a-fA-F]{1})([0-9a-fA-F]{1})([0-9a-fA-F]{1})$/,\n      hex6: /^#?([0-9a-fA-F]{2})([0-9a-fA-F]{2})([0-9a-fA-F]{2})$/,\n      hex4: /^#?([0-9a-fA-F]{1})([0-9a-fA-F]{1})([0-9a-fA-F]{1})([0-9a-fA-F]{1})$/,\n      hex8: /^#?([0-9a-fA-F]{2})([0-9a-fA-F]{2})([0-9a-fA-F]{2})([0-9a-fA-F]{2})$/\n    };\n  }();\n\n  // `isValidCSSUnit`\n  // Take in a single string / number and check to see if it looks like a CSS unit\n  // (see `matchers` above for definition).\n  function isValidCSSUnit(color) {\n    return !!matchers.CSS_UNIT.exec(color);\n  }\n\n  // `stringInputToObject`\n  // Permissive string parsing.  Take in a number of formats, and output an object\n  // based on detected format.  Returns `{ r, g, b }` or `{ h, s, l }` or `{ h, s, v}`\n  function stringInputToObject(color) {\n    color = color.replace(trimLeft, \"\").replace(trimRight, \"\").toLowerCase();\n    var named = false;\n    if (names[color]) {\n      color = names[color];\n      named = true;\n    } else if (color == \"transparent\") {\n      return {\n        r: 0,\n        g: 0,\n        b: 0,\n        a: 0,\n        format: \"name\"\n      };\n    }\n\n    // Try to match string input using regular expressions.\n    // Keep most of the number bounding out of this function - don't worry about [0,1] or [0,100] or [0,360]\n    // Just return an object and let the conversion functions handle that.\n    // This way the result will be the same whether the tinycolor is initialized with string or object.\n    var match;\n    if (match = matchers.rgb.exec(color)) {\n      return {\n        r: match[1],\n        g: match[2],\n        b: match[3]\n      };\n    }\n    if (match = matchers.rgba.exec(color)) {\n      return {\n        r: match[1],\n        g: match[2],\n        b: match[3],\n        a: match[4]\n      };\n    }\n    if (match = matchers.hsl.exec(color)) {\n      return {\n        h: match[1],\n        s: match[2],\n        l: match[3]\n      };\n    }\n    if (match = matchers.hsla.exec(color)) {\n      return {\n        h: match[1],\n        s: match[2],\n        l: match[3],\n        a: match[4]\n      };\n    }\n    if (match = matchers.hsv.exec(color)) {\n      return {\n        h: match[1],\n        s: match[2],\n        v: match[3]\n      };\n    }\n    if (match = matchers.hsva.exec(color)) {\n      return {\n        h: match[1],\n        s: match[2],\n        v: match[3],\n        a: match[4]\n      };\n    }\n    if (match = matchers.hex8.exec(color)) {\n      return {\n        r: parseIntFromHex(match[1]),\n        g: parseIntFromHex(match[2]),\n        b: parseIntFromHex(match[3]),\n        a: convertHexToDecimal(match[4]),\n        format: named ? \"name\" : \"hex8\"\n      };\n    }\n    if (match = matchers.hex6.exec(color)) {\n      return {\n        r: parseIntFromHex(match[1]),\n        g: parseIntFromHex(match[2]),\n        b: parseIntFromHex(match[3]),\n        format: named ? \"name\" : \"hex\"\n      };\n    }\n    if (match = matchers.hex4.exec(color)) {\n      return {\n        r: parseIntFromHex(match[1] + \"\" + match[1]),\n        g: parseIntFromHex(match[2] + \"\" + match[2]),\n        b: parseIntFromHex(match[3] + \"\" + match[3]),\n        a: convertHexToDecimal(match[4] + \"\" + match[4]),\n        format: named ? \"name\" : \"hex8\"\n      };\n    }\n    if (match = matchers.hex3.exec(color)) {\n      return {\n        r: parseIntFromHex(match[1] + \"\" + match[1]),\n        g: parseIntFromHex(match[2] + \"\" + match[2]),\n        b: parseIntFromHex(match[3] + \"\" + match[3]),\n        format: named ? \"name\" : \"hex\"\n      };\n    }\n    return false;\n  }\n  function validateWCAG2Parms(parms) {\n    // return valid WCAG2 parms for isReadable.\n    // If input parms are invalid, return {\"level\":\"AA\", \"size\":\"small\"}\n    var level, size;\n    parms = parms || {\n      level: \"AA\",\n      size: \"small\"\n    };\n    level = (parms.level || \"AA\").toUpperCase();\n    size = (parms.size || \"small\").toLowerCase();\n    if (level !== \"AA\" && level !== \"AAA\") {\n      level = \"AA\";\n    }\n    if (size !== \"small\" && size !== \"large\") {\n      size = \"small\";\n    }\n    return {\n      level: level,\n      size: size\n    };\n  }\n\n  return tinycolor;\n\n}));\n"]}