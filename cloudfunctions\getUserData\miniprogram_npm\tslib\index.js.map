{"version": 3, "sources": ["tslib.js"], "names": [], "mappings": ";;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.js", "sourcesContent": ["/*! *****************************************************************************\r\nCopyright (c) Microsoft Corporation.\r\n\r\nPermission to use, copy, modify, and/or distribute this software for any\r\npurpose with or without fee is hereby granted.\r\n\r\nTHE SOFTWARE IS PROVIDED \"AS IS\" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH\r\nREGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY\r\nAND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,\r\nINDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM\r\nLOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR\r\nOTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR\r\nPERFORMANCE OF THIS SOFTWARE.\r\n***************************************************************************** */\r\n\r\n/* global global, define, System, Reflect, Promise */\r\nvar __extends;\r\nvar __assign;\r\nvar __rest;\r\nvar __decorate;\r\nvar __param;\r\nvar __metadata;\r\nvar __awaiter;\r\nvar __generator;\r\nvar __exportStar;\r\nvar __values;\r\nvar __read;\r\nvar __spread;\r\nvar __spreadArrays;\r\nvar __await;\r\nvar __asyncGenerator;\r\nvar __asyncDelegator;\r\nvar __asyncValues;\r\nvar __makeTemplateObject;\r\nvar __importStar;\r\nvar __importDefault;\r\nvar __classPrivateFieldGet;\r\nvar __classPrivateFieldSet;\r\nvar __createBinding;\r\n(function (factory) {\r\n    var root = typeof global === \"object\" ? global : typeof self === \"object\" ? self : typeof this === \"object\" ? this : {};\r\n    if (typeof define === \"function\" && define.amd) {\r\n        define(\"tslib\", [\"exports\"], function (exports) { factory(createExporter(root, createExporter(exports))); });\r\n    }\r\n    else if (typeof module === \"object\" && typeof module.exports === \"object\") {\r\n        factory(createExporter(root, createExporter(module.exports)));\r\n    }\r\n    else {\r\n        factory(createExporter(root));\r\n    }\r\n    function createExporter(exports, previous) {\r\n        if (exports !== root) {\r\n            if (typeof Object.create === \"function\") {\r\n                Object.defineProperty(exports, \"__esModule\", { value: true });\r\n            }\r\n            else {\r\n                exports.__esModule = true;\r\n            }\r\n        }\r\n        return function (id, v) { return exports[id] = previous ? previous(id, v) : v; };\r\n    }\r\n})\r\n(function (exporter) {\r\n    var extendStatics = Object.setPrototypeOf ||\r\n        ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\r\n        function (d, b) { for (var p in b) if (b.hasOwnProperty(p)) d[p] = b[p]; };\r\n\r\n    __extends = function (d, b) {\r\n        extendStatics(d, b);\r\n        function __() { this.constructor = d; }\r\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\r\n    };\r\n\r\n    __assign = Object.assign || function (t) {\r\n        for (var s, i = 1, n = arguments.length; i < n; i++) {\r\n            s = arguments[i];\r\n            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p)) t[p] = s[p];\r\n        }\r\n        return t;\r\n    };\r\n\r\n    __rest = function (s, e) {\r\n        var t = {};\r\n        for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0)\r\n            t[p] = s[p];\r\n        if (s != null && typeof Object.getOwnPropertySymbols === \"function\")\r\n            for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\r\n                if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i]))\r\n                    t[p[i]] = s[p[i]];\r\n            }\r\n        return t;\r\n    };\r\n\r\n    __decorate = function (decorators, target, key, desc) {\r\n        var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;\r\n        if (typeof Reflect === \"object\" && typeof Reflect.decorate === \"function\") r = Reflect.decorate(decorators, target, key, desc);\r\n        else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;\r\n        return c > 3 && r && Object.defineProperty(target, key, r), r;\r\n    };\r\n\r\n    __param = function (paramIndex, decorator) {\r\n        return function (target, key) { decorator(target, key, paramIndex); }\r\n    };\r\n\r\n    __metadata = function (metadataKey, metadataValue) {\r\n        if (typeof Reflect === \"object\" && typeof Reflect.metadata === \"function\") return Reflect.metadata(metadataKey, metadataValue);\r\n    };\r\n\r\n    __awaiter = function (thisArg, _arguments, P, generator) {\r\n        function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }\r\n        return new (P || (P = Promise))(function (resolve, reject) {\r\n            function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\r\n            function rejected(value) { try { step(generator[\"throw\"](value)); } catch (e) { reject(e); } }\r\n            function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }\r\n            step((generator = generator.apply(thisArg, _arguments || [])).next());\r\n        });\r\n    };\r\n\r\n    __generator = function (thisArg, body) {\r\n        var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;\r\n        return g = { next: verb(0), \"throw\": verb(1), \"return\": verb(2) }, typeof Symbol === \"function\" && (g[Symbol.iterator] = function() { return this; }), g;\r\n        function verb(n) { return function (v) { return step([n, v]); }; }\r\n        function step(op) {\r\n            if (f) throw new TypeError(\"Generator is already executing.\");\r\n            while (_) try {\r\n                if (f = 1, y && (t = op[0] & 2 ? y[\"return\"] : op[0] ? y[\"throw\"] || ((t = y[\"return\"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;\r\n                if (y = 0, t) op = [op[0] & 2, t.value];\r\n                switch (op[0]) {\r\n                    case 0: case 1: t = op; break;\r\n                    case 4: _.label++; return { value: op[1], done: false };\r\n                    case 5: _.label++; y = op[1]; op = [0]; continue;\r\n                    case 7: op = _.ops.pop(); _.trys.pop(); continue;\r\n                    default:\r\n                        if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }\r\n                        if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }\r\n                        if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }\r\n                        if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }\r\n                        if (t[2]) _.ops.pop();\r\n                        _.trys.pop(); continue;\r\n                }\r\n                op = body.call(thisArg, _);\r\n            } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }\r\n            if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };\r\n        }\r\n    };\r\n\r\n    __createBinding = function(o, m, k, k2) {\r\n        if (k2 === undefined) k2 = k;\r\n        o[k2] = m[k];\r\n    };\r\n\r\n    __exportStar = function (m, exports) {\r\n        for (var p in m) if (p !== \"default\" && !exports.hasOwnProperty(p)) exports[p] = m[p];\r\n    };\r\n\r\n    __values = function (o) {\r\n        var s = typeof Symbol === \"function\" && Symbol.iterator, m = s && o[s], i = 0;\r\n        if (m) return m.call(o);\r\n        if (o && typeof o.length === \"number\") return {\r\n            next: function () {\r\n                if (o && i >= o.length) o = void 0;\r\n                return { value: o && o[i++], done: !o };\r\n            }\r\n        };\r\n        throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\r\n    };\r\n\r\n    __read = function (o, n) {\r\n        var m = typeof Symbol === \"function\" && o[Symbol.iterator];\r\n        if (!m) return o;\r\n        var i = m.call(o), r, ar = [], e;\r\n        try {\r\n            while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);\r\n        }\r\n        catch (error) { e = { error: error }; }\r\n        finally {\r\n            try {\r\n                if (r && !r.done && (m = i[\"return\"])) m.call(i);\r\n            }\r\n            finally { if (e) throw e.error; }\r\n        }\r\n        return ar;\r\n    };\r\n\r\n    __spread = function () {\r\n        for (var ar = [], i = 0; i < arguments.length; i++)\r\n            ar = ar.concat(__read(arguments[i]));\r\n        return ar;\r\n    };\r\n\r\n    __spreadArrays = function () {\r\n        for (var s = 0, i = 0, il = arguments.length; i < il; i++) s += arguments[i].length;\r\n        for (var r = Array(s), k = 0, i = 0; i < il; i++)\r\n            for (var a = arguments[i], j = 0, jl = a.length; j < jl; j++, k++)\r\n                r[k] = a[j];\r\n        return r;\r\n    };\r\n\r\n    __await = function (v) {\r\n        return this instanceof __await ? (this.v = v, this) : new __await(v);\r\n    };\r\n\r\n    __asyncGenerator = function (thisArg, _arguments, generator) {\r\n        if (!Symbol.asyncIterator) throw new TypeError(\"Symbol.asyncIterator is not defined.\");\r\n        var g = generator.apply(thisArg, _arguments || []), i, q = [];\r\n        return i = {}, verb(\"next\"), verb(\"throw\"), verb(\"return\"), i[Symbol.asyncIterator] = function () { return this; }, i;\r\n        function verb(n) { if (g[n]) i[n] = function (v) { return new Promise(function (a, b) { q.push([n, v, a, b]) > 1 || resume(n, v); }); }; }\r\n        function resume(n, v) { try { step(g[n](v)); } catch (e) { settle(q[0][3], e); } }\r\n        function step(r) { r.value instanceof __await ? Promise.resolve(r.value.v).then(fulfill, reject) : settle(q[0][2], r);  }\r\n        function fulfill(value) { resume(\"next\", value); }\r\n        function reject(value) { resume(\"throw\", value); }\r\n        function settle(f, v) { if (f(v), q.shift(), q.length) resume(q[0][0], q[0][1]); }\r\n    };\r\n\r\n    __asyncDelegator = function (o) {\r\n        var i, p;\r\n        return i = {}, verb(\"next\"), verb(\"throw\", function (e) { throw e; }), verb(\"return\"), i[Symbol.iterator] = function () { return this; }, i;\r\n        function verb(n, f) { i[n] = o[n] ? function (v) { return (p = !p) ? { value: __await(o[n](v)), done: n === \"return\" } : f ? f(v) : v; } : f; }\r\n    };\r\n\r\n    __asyncValues = function (o) {\r\n        if (!Symbol.asyncIterator) throw new TypeError(\"Symbol.asyncIterator is not defined.\");\r\n        var m = o[Symbol.asyncIterator], i;\r\n        return m ? m.call(o) : (o = typeof __values === \"function\" ? __values(o) : o[Symbol.iterator](), i = {}, verb(\"next\"), verb(\"throw\"), verb(\"return\"), i[Symbol.asyncIterator] = function () { return this; }, i);\r\n        function verb(n) { i[n] = o[n] && function (v) { return new Promise(function (resolve, reject) { v = o[n](v), settle(resolve, reject, v.done, v.value); }); }; }\r\n        function settle(resolve, reject, d, v) { Promise.resolve(v).then(function(v) { resolve({ value: v, done: d }); }, reject); }\r\n    };\r\n\r\n    __makeTemplateObject = function (cooked, raw) {\r\n        if (Object.defineProperty) { Object.defineProperty(cooked, \"raw\", { value: raw }); } else { cooked.raw = raw; }\r\n        return cooked;\r\n    };\r\n\r\n    __importStar = function (mod) {\r\n        if (mod && mod.__esModule) return mod;\r\n        var result = {};\r\n        if (mod != null) for (var k in mod) if (Object.hasOwnProperty.call(mod, k)) result[k] = mod[k];\r\n        result[\"default\"] = mod;\r\n        return result;\r\n    };\r\n\r\n    __importDefault = function (mod) {\r\n        return (mod && mod.__esModule) ? mod : { \"default\": mod };\r\n    };\r\n\r\n    __classPrivateFieldGet = function (receiver, privateMap) {\r\n        if (!privateMap.has(receiver)) {\r\n            throw new TypeError(\"attempted to get private field on non-instance\");\r\n        }\r\n        return privateMap.get(receiver);\r\n    };\r\n\r\n    __classPrivateFieldSet = function (receiver, privateMap, value) {\r\n        if (!privateMap.has(receiver)) {\r\n            throw new TypeError(\"attempted to set private field on non-instance\");\r\n        }\r\n        privateMap.set(receiver, value);\r\n        return value;\r\n    };\r\n\r\n    exporter(\"__extends\", __extends);\r\n    exporter(\"__assign\", __assign);\r\n    exporter(\"__rest\", __rest);\r\n    exporter(\"__decorate\", __decorate);\r\n    exporter(\"__param\", __param);\r\n    exporter(\"__metadata\", __metadata);\r\n    exporter(\"__awaiter\", __awaiter);\r\n    exporter(\"__generator\", __generator);\r\n    exporter(\"__exportStar\", __exportStar);\r\n    exporter(\"__createBinding\", __createBinding);\r\n    exporter(\"__values\", __values);\r\n    exporter(\"__read\", __read);\r\n    exporter(\"__spread\", __spread);\r\n    exporter(\"__spreadArrays\", __spreadArrays);\r\n    exporter(\"__await\", __await);\r\n    exporter(\"__asyncGenerator\", __asyncGenerator);\r\n    exporter(\"__asyncDelegator\", __asyncDelegator);\r\n    exporter(\"__asyncValues\", __asyncValues);\r\n    exporter(\"__makeTemplateObject\", __makeTemplateObject);\r\n    exporter(\"__importStar\", __importStar);\r\n    exporter(\"__importDefault\", __importDefault);\r\n    exporter(\"__classPrivateFieldGet\", __classPrivateFieldGet);\r\n    exporter(\"__classPrivateFieldSet\", __classPrivateFieldSet);\r\n});\r\n"]}