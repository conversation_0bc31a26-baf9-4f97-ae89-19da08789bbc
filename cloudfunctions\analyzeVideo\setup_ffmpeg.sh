#!/bin/bash

# 创建临时目录
TEMP_DIR=$(mktemp -d)
cd $TEMP_DIR

echo "正在下载FFmpeg静态二进制文件..."
# 下载FFmpeg静态二进制文件
wget https://johnvansickle.com/ffmpeg/releases/ffmpeg-release-amd64-static.tar.xz

echo "解压FFmpeg文件..."
# 解压文件
tar -xf ffmpeg-release-amd64-static.tar.xz

# 获取解压后的目录名
FFMPEG_DIR=$(ls -d ffmpeg-*-static)

echo "移动FFmpeg二进制文件到云函数bin目录..."
# 创建bin目录（如果不存在）
mkdir -p ../../bin

# 复制FFmpeg二进制文件
cp $FFMPEG_DIR/ffmpeg ../../bin/

# 设置权限
chmod +x ../../bin/ffmpeg

echo "清理临时文件..."
# 清理
cd ..
rm -rf $TEMP_DIR

echo "FFmpeg安装完成!"
echo "FFmpeg版本:"
../../bin/ffmpeg -version | head -n 1

echo "请确保在部署云函数时包含bin目录" 