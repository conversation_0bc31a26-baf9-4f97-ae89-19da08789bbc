{"version": 3, "sources": ["rp.js"], "names": [], "mappings": ";;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.js", "sourcesContent": ["\n\nvar Bluebird = require('bluebird').getNewLibraryCopy(),\n    configure = require('request-promise-core/configure/request2'),\n    stealthyRequire = require('stealthy-require');\n\ntry {\n\n    // Load Request freshly - so that users can require an unaltered request instance!\n    var request = stealthyRequire(require.cache, function () {\n        return require('request');\n    },\n    function () {\n        require('tough-cookie');\n    }, module);\n\n} catch (err) {\n    /* istanbul ignore next */\n    var EOL = require('os').EOL;\n    /* istanbul ignore next */\n    console.error(EOL + '###' + EOL + '### The \"request\" library is not installed automatically anymore.' + EOL + '### But is a dependency of \"request-promise\".' + EOL + '### Please install it with:' + EOL + '### npm install request --save' + EOL + '###' + EOL);\n    /* istanbul ignore next */\n    throw err;\n}\n\nBluebird.config({cancellation: true});\n\nconfigure({\n    request: request,\n    PromiseImpl: Bluebird,\n    expose: [\n        'then',\n        'catch',\n        'finally',\n        'cancel',\n        'promise'\n        // Would you like to expose more Bluebird methods? Try e.g. `rp(...).promise().tap(...)` first. `.promise()` returns the full-fledged Bluebird promise.\n    ],\n    constructorMixin: function (resolve, reject, onCancel) {\n        var self = this;\n        onCancel(function () {\n            self.abort();\n        });\n    }\n});\n\nrequest.bindCLS = function RP$bindCLS() {\n    throw new Error('CLS support was dropped. To get it back read: https://github.com/request/request-promise/wiki/Getting-Back-Support-for-Continuation-Local-Storage');\n};\n\n\nmodule.exports = request;\n"]}