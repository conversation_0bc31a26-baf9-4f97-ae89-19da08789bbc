{"version": 3, "sources": ["source-map-support.js"], "names": [], "mappings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file": "index.js", "sourcesContent": ["var SourceMapConsumer = require('source-map').SourceMapConsumer;\nvar path = require('path');\n\nvar fs;\ntry {\n  fs = require('fs');\n  if (!fs.existsSync || !fs.readFileSync) {\n    // fs doesn't have all methods we need\n    fs = null;\n  }\n} catch (err) {\n  /* nop */\n}\n\nvar bufferFrom = require('buffer-from');\n\n/**\n * Requires a module which is protected against bundler minification.\n *\n * @param {NodeModule} mod\n * @param {string} request\n */\nfunction dynamicRequire(mod, request) {\n  return mod.require(request);\n}\n\n// Only install once if called multiple times\nvar errorFormatterInstalled = false;\nvar uncaughtShimInstalled = false;\n\n// If true, the caches are reset before a stack trace formatting operation\nvar emptyCacheBetweenOperations = false;\n\n// Supports {browser, node, auto}\nvar environment = \"auto\";\n\n// Maps a file path to a string containing the file contents\nvar fileContentsCache = {};\n\n// Maps a file path to a source map for that file\nvar sourceMapCache = {};\n\n// Regex for detecting source maps\nvar reSourceMap = /^data:application\\/json[^,]+base64,/;\n\n// Priority list of retrieve handlers\nvar retrieveFileHandlers = [];\nvar retrieveMapHandlers = [];\n\nfunction isInBrowser() {\n  if (environment === \"browser\")\n    return true;\n  if (environment === \"node\")\n    return false;\n  return ((typeof window !== 'undefined') && (typeof XMLHttpRequest === 'function') && !(window.require && window.module && window.process && window.process.type === \"renderer\"));\n}\n\nfunction hasGlobalProcessEventEmitter() {\n  return ((typeof process === 'object') && (process !== null) && (typeof process.on === 'function'));\n}\n\nfunction globalProcessVersion() {\n  if ((typeof process === 'object') && (process !== null)) {\n    return process.version;\n  } else {\n    return '';\n  }\n}\n\nfunction globalProcessStderr() {\n  if ((typeof process === 'object') && (process !== null)) {\n    return process.stderr;\n  }\n}\n\nfunction globalProcessExit(code) {\n  if ((typeof process === 'object') && (process !== null) && (typeof process.exit === 'function')) {\n    return process.exit(code);\n  }\n}\n\nfunction handlerExec(list) {\n  return function(arg) {\n    for (var i = 0; i < list.length; i++) {\n      var ret = list[i](arg);\n      if (ret) {\n        return ret;\n      }\n    }\n    return null;\n  };\n}\n\nvar retrieveFile = handlerExec(retrieveFileHandlers);\n\nretrieveFileHandlers.push(function(path) {\n  // Trim the path to make sure there is no extra whitespace.\n  path = path.trim();\n  if (/^file:/.test(path)) {\n    // existsSync/readFileSync can't handle file protocol, but once stripped, it works\n    path = path.replace(/file:\\/\\/\\/(\\w:)?/, function(protocol, drive) {\n      return drive ?\n        '' : // file:///C:/dir/file -> C:/dir/file\n        '/'; // file:///root-dir/file -> /root-dir/file\n    });\n  }\n  if (path in fileContentsCache) {\n    return fileContentsCache[path];\n  }\n\n  var contents = '';\n  try {\n    if (!fs) {\n      // Use SJAX if we are in the browser\n      var xhr = new XMLHttpRequest();\n      xhr.open('GET', path, /** async */ false);\n      xhr.send(null);\n      if (xhr.readyState === 4 && xhr.status === 200) {\n        contents = xhr.responseText;\n      }\n    } else if (fs.existsSync(path)) {\n      // Otherwise, use the filesystem\n      contents = fs.readFileSync(path, 'utf8');\n    }\n  } catch (er) {\n    /* ignore any errors */\n  }\n\n  return fileContentsCache[path] = contents;\n});\n\n// Support URLs relative to a directory, but be careful about a protocol prefix\n// in case we are in the browser (i.e. directories may start with \"http://\" or \"file:///\")\nfunction supportRelativeURL(file, url) {\n  if (!file) return url;\n  var dir = path.dirname(file);\n  var match = /^\\w+:\\/\\/[^\\/]*/.exec(dir);\n  var protocol = match ? match[0] : '';\n  var startPath = dir.slice(protocol.length);\n  if (protocol && /^\\/\\w\\:/.test(startPath)) {\n    // handle file:///C:/ paths\n    protocol += '/';\n    return protocol + path.resolve(dir.slice(protocol.length), url).replace(/\\\\/g, '/');\n  }\n  return protocol + path.resolve(dir.slice(protocol.length), url);\n}\n\nfunction retrieveSourceMapURL(source) {\n  var fileData;\n\n  if (isInBrowser()) {\n     try {\n       var xhr = new XMLHttpRequest();\n       xhr.open('GET', source, false);\n       xhr.send(null);\n       fileData = xhr.readyState === 4 ? xhr.responseText : null;\n\n       // Support providing a sourceMappingURL via the SourceMap header\n       var sourceMapHeader = xhr.getResponseHeader(\"SourceMap\") ||\n                             xhr.getResponseHeader(\"X-SourceMap\");\n       if (sourceMapHeader) {\n         return sourceMapHeader;\n       }\n     } catch (e) {\n     }\n  }\n\n  // Get the URL of the source map\n  fileData = retrieveFile(source);\n  var re = /(?:\\/\\/[@#][\\s]*sourceMappingURL=([^\\s'\"]+)[\\s]*$)|(?:\\/\\*[@#][\\s]*sourceMappingURL=([^\\s*'\"]+)[\\s]*(?:\\*\\/)[\\s]*$)/mg;\n  // Keep executing the search to find the *last* sourceMappingURL to avoid\n  // picking up sourceMappingURLs from comments, strings, etc.\n  var lastMatch, match;\n  while (match = re.exec(fileData)) lastMatch = match;\n  if (!lastMatch) return null;\n  return lastMatch[1];\n};\n\n// Can be overridden by the retrieveSourceMap option to install. Takes a\n// generated source filename; returns a {map, optional url} object, or null if\n// there is no source map.  The map field may be either a string or the parsed\n// JSON object (ie, it must be a valid argument to the SourceMapConsumer\n// constructor).\nvar retrieveSourceMap = handlerExec(retrieveMapHandlers);\nretrieveMapHandlers.push(function(source) {\n  var sourceMappingURL = retrieveSourceMapURL(source);\n  if (!sourceMappingURL) return null;\n\n  // Read the contents of the source map\n  var sourceMapData;\n  if (reSourceMap.test(sourceMappingURL)) {\n    // Support source map URL as a data url\n    var rawData = sourceMappingURL.slice(sourceMappingURL.indexOf(',') + 1);\n    sourceMapData = bufferFrom(rawData, \"base64\").toString();\n    sourceMappingURL = source;\n  } else {\n    // Support source map URLs relative to the source URL\n    sourceMappingURL = supportRelativeURL(source, sourceMappingURL);\n    sourceMapData = retrieveFile(sourceMappingURL);\n  }\n\n  if (!sourceMapData) {\n    return null;\n  }\n\n  return {\n    url: sourceMappingURL,\n    map: sourceMapData\n  };\n});\n\nfunction mapSourcePosition(position) {\n  var sourceMap = sourceMapCache[position.source];\n  if (!sourceMap) {\n    // Call the (overrideable) retrieveSourceMap function to get the source map.\n    var urlAndMap = retrieveSourceMap(position.source);\n    if (urlAndMap) {\n      sourceMap = sourceMapCache[position.source] = {\n        url: urlAndMap.url,\n        map: new SourceMapConsumer(urlAndMap.map)\n      };\n\n      // Load all sources stored inline with the source map into the file cache\n      // to pretend like they are already loaded. They may not exist on disk.\n      if (sourceMap.map.sourcesContent) {\n        sourceMap.map.sources.forEach(function(source, i) {\n          var contents = sourceMap.map.sourcesContent[i];\n          if (contents) {\n            var url = supportRelativeURL(sourceMap.url, source);\n            fileContentsCache[url] = contents;\n          }\n        });\n      }\n    } else {\n      sourceMap = sourceMapCache[position.source] = {\n        url: null,\n        map: null\n      };\n    }\n  }\n\n  // Resolve the source URL relative to the URL of the source map\n  if (sourceMap && sourceMap.map && typeof sourceMap.map.originalPositionFor === 'function') {\n    var originalPosition = sourceMap.map.originalPositionFor(position);\n\n    // Only return the original position if a matching line was found. If no\n    // matching line is found then we return position instead, which will cause\n    // the stack trace to print the path and line for the compiled file. It is\n    // better to give a precise location in the compiled file than a vague\n    // location in the original file.\n    if (originalPosition.source !== null) {\n      originalPosition.source = supportRelativeURL(\n        sourceMap.url, originalPosition.source);\n      return originalPosition;\n    }\n  }\n\n  return position;\n}\n\n// Parses code generated by FormatEvalOrigin(), a function inside V8:\n// https://code.google.com/p/v8/source/browse/trunk/src/messages.js\nfunction mapEvalOrigin(origin) {\n  // Most eval() calls are in this format\n  var match = /^eval at ([^(]+) \\((.+):(\\d+):(\\d+)\\)$/.exec(origin);\n  if (match) {\n    var position = mapSourcePosition({\n      source: match[2],\n      line: +match[3],\n      column: match[4] - 1\n    });\n    return 'eval at ' + match[1] + ' (' + position.source + ':' +\n      position.line + ':' + (position.column + 1) + ')';\n  }\n\n  // Parse nested eval() calls using recursion\n  match = /^eval at ([^(]+) \\((.+)\\)$/.exec(origin);\n  if (match) {\n    return 'eval at ' + match[1] + ' (' + mapEvalOrigin(match[2]) + ')';\n  }\n\n  // Make sure we still return useful information if we didn't find anything\n  return origin;\n}\n\n// This is copied almost verbatim from the V8 source code at\n// https://code.google.com/p/v8/source/browse/trunk/src/messages.js. The\n// implementation of wrapCallSite() used to just forward to the actual source\n// code of CallSite.prototype.toString but unfortunately a new release of V8\n// did something to the prototype chain and broke the shim. The only fix I\n// could find was copy/paste.\nfunction CallSiteToString() {\n  var fileName;\n  var fileLocation = \"\";\n  if (this.isNative()) {\n    fileLocation = \"native\";\n  } else {\n    fileName = this.getScriptNameOrSourceURL();\n    if (!fileName && this.isEval()) {\n      fileLocation = this.getEvalOrigin();\n      fileLocation += \", \";  // Expecting source position to follow.\n    }\n\n    if (fileName) {\n      fileLocation += fileName;\n    } else {\n      // Source code does not originate from a file and is not native, but we\n      // can still get the source position inside the source string, e.g. in\n      // an eval string.\n      fileLocation += \"<anonymous>\";\n    }\n    var lineNumber = this.getLineNumber();\n    if (lineNumber != null) {\n      fileLocation += \":\" + lineNumber;\n      var columnNumber = this.getColumnNumber();\n      if (columnNumber) {\n        fileLocation += \":\" + columnNumber;\n      }\n    }\n  }\n\n  var line = \"\";\n  var functionName = this.getFunctionName();\n  var addSuffix = true;\n  var isConstructor = this.isConstructor();\n  var isMethodCall = !(this.isToplevel() || isConstructor);\n  if (isMethodCall) {\n    var typeName = this.getTypeName();\n    // Fixes shim to be backward compatable with Node v0 to v4\n    if (typeName === \"[object Object]\") {\n      typeName = \"null\";\n    }\n    var methodName = this.getMethodName();\n    if (functionName) {\n      if (typeName && functionName.indexOf(typeName) != 0) {\n        line += typeName + \".\";\n      }\n      line += functionName;\n      if (methodName && functionName.indexOf(\".\" + methodName) != functionName.length - methodName.length - 1) {\n        line += \" [as \" + methodName + \"]\";\n      }\n    } else {\n      line += typeName + \".\" + (methodName || \"<anonymous>\");\n    }\n  } else if (isConstructor) {\n    line += \"new \" + (functionName || \"<anonymous>\");\n  } else if (functionName) {\n    line += functionName;\n  } else {\n    line += fileLocation;\n    addSuffix = false;\n  }\n  if (addSuffix) {\n    line += \" (\" + fileLocation + \")\";\n  }\n  return line;\n}\n\nfunction cloneCallSite(frame) {\n  var object = {};\n  Object.getOwnPropertyNames(Object.getPrototypeOf(frame)).forEach(function(name) {\n    object[name] = /^(?:is|get)/.test(name) ? function() { return frame[name].call(frame); } : frame[name];\n  });\n  object.toString = CallSiteToString;\n  return object;\n}\n\nfunction wrapCallSite(frame, state) {\n  // provides interface backward compatibility\n  if (state === undefined) {\n    state = { nextPosition: null, curPosition: null }\n  }\n  if(frame.isNative()) {\n    state.curPosition = null;\n    return frame;\n  }\n\n  // Most call sites will return the source file from getFileName(), but code\n  // passed to eval() ending in \"//# sourceURL=...\" will return the source file\n  // from getScriptNameOrSourceURL() instead\n  var source = frame.getFileName() || frame.getScriptNameOrSourceURL();\n  if (source) {\n    var line = frame.getLineNumber();\n    var column = frame.getColumnNumber() - 1;\n\n    // Fix position in Node where some (internal) code is prepended.\n    // See https://github.com/evanw/node-source-map-support/issues/36\n    // Header removed in node at ^10.16 || >=11.11.0\n    // v11 is not an LTS candidate, we can just test the one version with it.\n    // Test node versions for: 10.16-19, 10.20+, 12-19, 20-99, 100+, or 11.11\n    var noHeader = /^v(10\\.1[6-9]|10\\.[2-9][0-9]|10\\.[0-9]{3,}|1[2-9]\\d*|[2-9]\\d|\\d{3,}|11\\.11)/;\n    var headerLength = noHeader.test(globalProcessVersion()) ? 0 : 62;\n    if (line === 1 && column > headerLength && !isInBrowser() && !frame.isEval()) {\n      column -= headerLength;\n    }\n\n    var position = mapSourcePosition({\n      source: source,\n      line: line,\n      column: column\n    });\n    state.curPosition = position;\n    frame = cloneCallSite(frame);\n    var originalFunctionName = frame.getFunctionName;\n    frame.getFunctionName = function() {\n      if (state.nextPosition == null) {\n        return originalFunctionName();\n      }\n      return state.nextPosition.name || originalFunctionName();\n    };\n    frame.getFileName = function() { return position.source; };\n    frame.getLineNumber = function() { return position.line; };\n    frame.getColumnNumber = function() { return position.column + 1; };\n    frame.getScriptNameOrSourceURL = function() { return position.source; };\n    return frame;\n  }\n\n  // Code called using eval() needs special handling\n  var origin = frame.isEval() && frame.getEvalOrigin();\n  if (origin) {\n    origin = mapEvalOrigin(origin);\n    frame = cloneCallSite(frame);\n    frame.getEvalOrigin = function() { return origin; };\n    return frame;\n  }\n\n  // If we get here then we were unable to change the source position\n  return frame;\n}\n\n// This function is part of the V8 stack trace API, for more info see:\n// https://v8.dev/docs/stack-trace-api\nfunction prepareStackTrace(error, stack) {\n  if (emptyCacheBetweenOperations) {\n    fileContentsCache = {};\n    sourceMapCache = {};\n  }\n\n  var name = error.name || 'Error';\n  var message = error.message || '';\n  var errorString = name + \": \" + message;\n\n  var state = { nextPosition: null, curPosition: null };\n  var processedStack = [];\n  for (var i = stack.length - 1; i >= 0; i--) {\n    processedStack.push('\\n    at ' + wrapCallSite(stack[i], state));\n    state.nextPosition = state.curPosition;\n  }\n  state.curPosition = state.nextPosition = null;\n  return errorString + processedStack.reverse().join('');\n}\n\n// Generate position and snippet of original source with pointer\nfunction getErrorSource(error) {\n  var match = /\\n    at [^(]+ \\((.*):(\\d+):(\\d+)\\)/.exec(error.stack);\n  if (match) {\n    var source = match[1];\n    var line = +match[2];\n    var column = +match[3];\n\n    // Support the inline sourceContents inside the source map\n    var contents = fileContentsCache[source];\n\n    // Support files on disk\n    if (!contents && fs && fs.existsSync(source)) {\n      try {\n        contents = fs.readFileSync(source, 'utf8');\n      } catch (er) {\n        contents = '';\n      }\n    }\n\n    // Format the line from the original source code like node does\n    if (contents) {\n      var code = contents.split(/(?:\\r\\n|\\r|\\n)/)[line - 1];\n      if (code) {\n        return source + ':' + line + '\\n' + code + '\\n' +\n          new Array(column).join(' ') + '^';\n      }\n    }\n  }\n  return null;\n}\n\nfunction printErrorAndExit (error) {\n  var source = getErrorSource(error);\n\n  // Ensure error is printed synchronously and not truncated\n  var stderr = globalProcessStderr();\n  if (stderr && stderr._handle && stderr._handle.setBlocking) {\n    stderr._handle.setBlocking(true);\n  }\n\n  if (source) {\n    console.error();\n    console.error(source);\n  }\n\n  console.error(error.stack);\n  globalProcessExit(1);\n}\n\nfunction shimEmitUncaughtException () {\n  var origEmit = process.emit;\n\n  process.emit = function (type) {\n    if (type === 'uncaughtException') {\n      var hasStack = (arguments[1] && arguments[1].stack);\n      var hasListeners = (this.listeners(type).length > 0);\n\n      if (hasStack && !hasListeners) {\n        return printErrorAndExit(arguments[1]);\n      }\n    }\n\n    return origEmit.apply(this, arguments);\n  };\n}\n\nvar originalRetrieveFileHandlers = retrieveFileHandlers.slice(0);\nvar originalRetrieveMapHandlers = retrieveMapHandlers.slice(0);\n\nexports.wrapCallSite = wrapCallSite;\nexports.getErrorSource = getErrorSource;\nexports.mapSourcePosition = mapSourcePosition;\nexports.retrieveSourceMap = retrieveSourceMap;\n\nexports.install = function(options) {\n  options = options || {};\n\n  if (options.environment) {\n    environment = options.environment;\n    if ([\"node\", \"browser\", \"auto\"].indexOf(environment) === -1) {\n      throw new Error(\"environment \" + environment + \" was unknown. Available options are {auto, browser, node}\")\n    }\n  }\n\n  // Allow sources to be found by methods other than reading the files\n  // directly from disk.\n  if (options.retrieveFile) {\n    if (options.overrideRetrieveFile) {\n      retrieveFileHandlers.length = 0;\n    }\n\n    retrieveFileHandlers.unshift(options.retrieveFile);\n  }\n\n  // Allow source maps to be found by methods other than reading the files\n  // directly from disk.\n  if (options.retrieveSourceMap) {\n    if (options.overrideRetrieveSourceMap) {\n      retrieveMapHandlers.length = 0;\n    }\n\n    retrieveMapHandlers.unshift(options.retrieveSourceMap);\n  }\n\n  // Support runtime transpilers that include inline source maps\n  if (options.hookRequire && !isInBrowser()) {\n    // Use dynamicRequire to avoid including in browser bundles\n    var Module = dynamicRequire(module, 'module');\n    var $compile = Module.prototype._compile;\n\n    if (!$compile.__sourceMapSupport) {\n      Module.prototype._compile = function(content, filename) {\n        fileContentsCache[filename] = content;\n        sourceMapCache[filename] = undefined;\n        return $compile.call(this, content, filename);\n      };\n\n      Module.prototype._compile.__sourceMapSupport = true;\n    }\n  }\n\n  // Configure options\n  if (!emptyCacheBetweenOperations) {\n    emptyCacheBetweenOperations = 'emptyCacheBetweenOperations' in options ?\n      options.emptyCacheBetweenOperations : false;\n  }\n\n  // Install the error reformatter\n  if (!errorFormatterInstalled) {\n    errorFormatterInstalled = true;\n    Error.prepareStackTrace = prepareStackTrace;\n  }\n\n  if (!uncaughtShimInstalled) {\n    var installHandler = 'handleUncaughtExceptions' in options ?\n      options.handleUncaughtExceptions : true;\n\n    // Do not override 'uncaughtException' with our own handler in Node.js\n    // Worker threads. Workers pass the error to the main thread as an event,\n    // rather than printing something to stderr and exiting.\n    try {\n      // We need to use `dynamicRequire` because `require` on it's own will be optimized by WebPack/Browserify.\n      var worker_threads = dynamicRequire(module, 'worker_threads');\n      if (worker_threads.isMainThread === false) {\n        installHandler = false;\n      }\n    } catch(e) {}\n\n    // Provide the option to not install the uncaught exception handler. This is\n    // to support other uncaught exception handlers (in test frameworks, for\n    // example). If this handler is not installed and there are no other uncaught\n    // exception handlers, uncaught exceptions will be caught by node's built-in\n    // exception handler and the process will still be terminated. However, the\n    // generated JavaScript code will be shown above the stack trace instead of\n    // the original source code.\n    if (installHandler && hasGlobalProcessEventEmitter()) {\n      uncaughtShimInstalled = true;\n      shimEmitUncaughtException();\n    }\n  }\n};\n\nexports.resetRetrieveHandlers = function() {\n  retrieveFileHandlers.length = 0;\n  retrieveMapHandlers.length = 0;\n\n  retrieveFileHandlers = originalRetrieveFileHandlers.slice(0);\n  retrieveMapHandlers = originalRetrieveMapHandlers.slice(0);\n\n  retrieveSourceMap = handlerExec(retrieveMapHandlers);\n  retrieveFile = handlerExec(retrieveFileHandlers);\n}\n"]}