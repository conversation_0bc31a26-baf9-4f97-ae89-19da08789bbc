const db = wx.cloud.database()

// 设备集合操作
const deviceCollection = db.collection('devices')

// 获取设备列表
const getDeviceList = async () => {
  try {
    const res = await deviceCollection.get()
    return {
      success: true,
      data: res.data
    }
  } catch (error) {
    console.error('获取设备列表失败:', error)
    return {
      success: false,
      error: error
    }
  }
}

// 获取设备详情
const getDeviceDetail = async (deviceId) => {
  try {
    const res = await deviceCollection.doc(deviceId).get()
    return {
      success: true,
      data: res.data
    }
  } catch (error) {
    console.error('获取设备详情失败:', error)
    return {
      success: false,
      error: error
    }
  }
}

// 更新设备信息
const updateDevice = async (deviceId, data) => {
  try {
    await deviceCollection.doc(deviceId).update({
      data: data
    })
    return {
      success: true
    }
  } catch (error) {
    console.error('更新设备信息失败:', error)
    return {
      success: false,
      error: error
    }
  }
}

// 添加设备
const addDevice = async (deviceData) => {
  try {
    const res = await deviceCollection.add({
      data: deviceData
    })
    return {
      success: true,
      _id: res._id
    }
  } catch (error) {
    console.error('添加设备失败:', error)
    return {
      success: false,
      error: error
    }
  }
}

// 删除设备
const deleteDevice = async (deviceId) => {
  try {
    await deviceCollection.doc(deviceId).remove()
    return {
      success: true
    }
  } catch (error) {
    console.error('删除设备失败:', error)
    return {
      success: false,
      error: error
    }
  }
}

// 搜索设备
const searchDevices = async (keyword) => {
  try {
    const res = await deviceCollection.where({
      name: db.RegExp({
        regexp: keyword,
        options: 'i'
      })
    }).get()
    return {
      success: true,
      data: res.data
    }
  } catch (error) {
    console.error('搜索设备失败:', error)
    return {
      success: false,
      error: error
    }
  }
}

module.exports = {
  getDeviceList,
  getDeviceDetail,
  updateDevice,
  addDevice,
  deleteDevice,
  searchDevices
} 