{"version": 3, "sources": ["index.js"], "names": [], "mappings": ";;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.js", "sourcesContent": ["(function(){\n\n    // Copyright (c) 2005  Tom Wu\n    // All Rights Reserved.\n    // See \"LICENSE\" for details.\n\n    // Basic JavaScript BN library - subset useful for RSA encryption.\n\n    // Bits per digit\n    var dbits;\n\n    // JavaScript engine analysis\n    var canary = 0xdeadbeefcafe;\n    var j_lm = ((canary&0xffffff)==0xefcafe);\n\n    // (public) Constructor\n    function BigInteger(a,b,c) {\n      if(a != null)\n        if(\"number\" == typeof a) this.fromNumber(a,b,c);\n        else if(b == null && \"string\" != typeof a) this.fromString(a,256);\n        else this.fromString(a,b);\n    }\n\n    // return new, unset BigInteger\n    function nbi() { return new BigInteger(null); }\n\n    // am: Compute w_j += (x*this_i), propagate carries,\n    // c is initial carry, returns final carry.\n    // c < 3*dvalue, x < 2*dvalue, this_i < dvalue\n    // We need to select the fastest one that works in this environment.\n\n    // am1: use a single mult and divide to get the high bits,\n    // max digit bits should be 26 because\n    // max internal value = 2*dvalue^2-2*dvalue (< 2^53)\n    function am1(i,x,w,j,c,n) {\n      while(--n >= 0) {\n        var v = x*this[i++]+w[j]+c;\n        c = Math.floor(v/0x4000000);\n        w[j++] = v&0x3ffffff;\n      }\n      return c;\n    }\n    // am2 avoids a big mult-and-extract completely.\n    // Max digit bits should be <= 30 because we do bitwise ops\n    // on values up to 2*hdvalue^2-hdvalue-1 (< 2^31)\n    function am2(i,x,w,j,c,n) {\n      var xl = x&0x7fff, xh = x>>15;\n      while(--n >= 0) {\n        var l = this[i]&0x7fff;\n        var h = this[i++]>>15;\n        var m = xh*l+h*xl;\n        l = xl*l+((m&0x7fff)<<15)+w[j]+(c&0x3fffffff);\n        c = (l>>>30)+(m>>>15)+xh*h+(c>>>30);\n        w[j++] = l&0x3fffffff;\n      }\n      return c;\n    }\n    // Alternately, set max digit bits to 28 since some\n    // browsers slow down when dealing with 32-bit numbers.\n    function am3(i,x,w,j,c,n) {\n      var xl = x&0x3fff, xh = x>>14;\n      while(--n >= 0) {\n        var l = this[i]&0x3fff;\n        var h = this[i++]>>14;\n        var m = xh*l+h*xl;\n        l = xl*l+((m&0x3fff)<<14)+w[j]+c;\n        c = (l>>28)+(m>>14)+xh*h;\n        w[j++] = l&0xfffffff;\n      }\n      return c;\n    }\n    var inBrowser = typeof navigator !== \"undefined\";\n    if(inBrowser && j_lm && (navigator.appName == \"Microsoft Internet Explorer\")) {\n      BigInteger.prototype.am = am2;\n      dbits = 30;\n    }\n    else if(inBrowser && j_lm && (navigator.appName != \"Netscape\")) {\n      BigInteger.prototype.am = am1;\n      dbits = 26;\n    }\n    else { // Mozilla/Netscape seems to prefer am3\n      BigInteger.prototype.am = am3;\n      dbits = 28;\n    }\n\n    BigInteger.prototype.DB = dbits;\n    BigInteger.prototype.DM = ((1<<dbits)-1);\n    BigInteger.prototype.DV = (1<<dbits);\n\n    var BI_FP = 52;\n    BigInteger.prototype.FV = Math.pow(2,BI_FP);\n    BigInteger.prototype.F1 = BI_FP-dbits;\n    BigInteger.prototype.F2 = 2*dbits-BI_FP;\n\n    // Digit conversions\n    var BI_RM = \"0123456789abcdefghijklmnopqrstuvwxyz\";\n    var BI_RC = new Array();\n    var rr,vv;\n    rr = \"0\".charCodeAt(0);\n    for(vv = 0; vv <= 9; ++vv) BI_RC[rr++] = vv;\n    rr = \"a\".charCodeAt(0);\n    for(vv = 10; vv < 36; ++vv) BI_RC[rr++] = vv;\n    rr = \"A\".charCodeAt(0);\n    for(vv = 10; vv < 36; ++vv) BI_RC[rr++] = vv;\n\n    function int2char(n) { return BI_RM.charAt(n); }\n    function intAt(s,i) {\n      var c = BI_RC[s.charCodeAt(i)];\n      return (c==null)?-1:c;\n    }\n\n    // (protected) copy this to r\n    function bnpCopyTo(r) {\n      for(var i = this.t-1; i >= 0; --i) r[i] = this[i];\n      r.t = this.t;\n      r.s = this.s;\n    }\n\n    // (protected) set from integer value x, -DV <= x < DV\n    function bnpFromInt(x) {\n      this.t = 1;\n      this.s = (x<0)?-1:0;\n      if(x > 0) this[0] = x;\n      else if(x < -1) this[0] = x+this.DV;\n      else this.t = 0;\n    }\n\n    // return bigint initialized to value\n    function nbv(i) { var r = nbi(); r.fromInt(i); return r; }\n\n    // (protected) set from string and radix\n    function bnpFromString(s,b) {\n      var k;\n      if(b == 16) k = 4;\n      else if(b == 8) k = 3;\n      else if(b == 256) k = 8; // byte array\n      else if(b == 2) k = 1;\n      else if(b == 32) k = 5;\n      else if(b == 4) k = 2;\n      else { this.fromRadix(s,b); return; }\n      this.t = 0;\n      this.s = 0;\n      var i = s.length, mi = false, sh = 0;\n      while(--i >= 0) {\n        var x = (k==8)?s[i]&0xff:intAt(s,i);\n        if(x < 0) {\n          if(s.charAt(i) == \"-\") mi = true;\n          continue;\n        }\n        mi = false;\n        if(sh == 0)\n          this[this.t++] = x;\n        else if(sh+k > this.DB) {\n          this[this.t-1] |= (x&((1<<(this.DB-sh))-1))<<sh;\n          this[this.t++] = (x>>(this.DB-sh));\n        }\n        else\n          this[this.t-1] |= x<<sh;\n        sh += k;\n        if(sh >= this.DB) sh -= this.DB;\n      }\n      if(k == 8 && (s[0]&0x80) != 0) {\n        this.s = -1;\n        if(sh > 0) this[this.t-1] |= ((1<<(this.DB-sh))-1)<<sh;\n      }\n      this.clamp();\n      if(mi) BigInteger.ZERO.subTo(this,this);\n    }\n\n    // (protected) clamp off excess high words\n    function bnpClamp() {\n      var c = this.s&this.DM;\n      while(this.t > 0 && this[this.t-1] == c) --this.t;\n    }\n\n    // (public) return string representation in given radix\n    function bnToString(b) {\n      if(this.s < 0) return \"-\"+this.negate().toString(b);\n      var k;\n      if(b == 16) k = 4;\n      else if(b == 8) k = 3;\n      else if(b == 2) k = 1;\n      else if(b == 32) k = 5;\n      else if(b == 4) k = 2;\n      else return this.toRadix(b);\n      var km = (1<<k)-1, d, m = false, r = \"\", i = this.t;\n      var p = this.DB-(i*this.DB)%k;\n      if(i-- > 0) {\n        if(p < this.DB && (d = this[i]>>p) > 0) { m = true; r = int2char(d); }\n        while(i >= 0) {\n          if(p < k) {\n            d = (this[i]&((1<<p)-1))<<(k-p);\n            d |= this[--i]>>(p+=this.DB-k);\n          }\n          else {\n            d = (this[i]>>(p-=k))&km;\n            if(p <= 0) { p += this.DB; --i; }\n          }\n          if(d > 0) m = true;\n          if(m) r += int2char(d);\n        }\n      }\n      return m?r:\"0\";\n    }\n\n    // (public) -this\n    function bnNegate() { var r = nbi(); BigInteger.ZERO.subTo(this,r); return r; }\n\n    // (public) |this|\n    function bnAbs() { return (this.s<0)?this.negate():this; }\n\n    // (public) return + if this > a, - if this < a, 0 if equal\n    function bnCompareTo(a) {\n      var r = this.s-a.s;\n      if(r != 0) return r;\n      var i = this.t;\n      r = i-a.t;\n      if(r != 0) return (this.s<0)?-r:r;\n      while(--i >= 0) if((r=this[i]-a[i]) != 0) return r;\n      return 0;\n    }\n\n    // returns bit length of the integer x\n    function nbits(x) {\n      var r = 1, t;\n      if((t=x>>>16) != 0) { x = t; r += 16; }\n      if((t=x>>8) != 0) { x = t; r += 8; }\n      if((t=x>>4) != 0) { x = t; r += 4; }\n      if((t=x>>2) != 0) { x = t; r += 2; }\n      if((t=x>>1) != 0) { x = t; r += 1; }\n      return r;\n    }\n\n    // (public) return the number of bits in \"this\"\n    function bnBitLength() {\n      if(this.t <= 0) return 0;\n      return this.DB*(this.t-1)+nbits(this[this.t-1]^(this.s&this.DM));\n    }\n\n    // (protected) r = this << n*DB\n    function bnpDLShiftTo(n,r) {\n      var i;\n      for(i = this.t-1; i >= 0; --i) r[i+n] = this[i];\n      for(i = n-1; i >= 0; --i) r[i] = 0;\n      r.t = this.t+n;\n      r.s = this.s;\n    }\n\n    // (protected) r = this >> n*DB\n    function bnpDRShiftTo(n,r) {\n      for(var i = n; i < this.t; ++i) r[i-n] = this[i];\n      r.t = Math.max(this.t-n,0);\n      r.s = this.s;\n    }\n\n    // (protected) r = this << n\n    function bnpLShiftTo(n,r) {\n      var bs = n%this.DB;\n      var cbs = this.DB-bs;\n      var bm = (1<<cbs)-1;\n      var ds = Math.floor(n/this.DB), c = (this.s<<bs)&this.DM, i;\n      for(i = this.t-1; i >= 0; --i) {\n        r[i+ds+1] = (this[i]>>cbs)|c;\n        c = (this[i]&bm)<<bs;\n      }\n      for(i = ds-1; i >= 0; --i) r[i] = 0;\n      r[ds] = c;\n      r.t = this.t+ds+1;\n      r.s = this.s;\n      r.clamp();\n    }\n\n    // (protected) r = this >> n\n    function bnpRShiftTo(n,r) {\n      r.s = this.s;\n      var ds = Math.floor(n/this.DB);\n      if(ds >= this.t) { r.t = 0; return; }\n      var bs = n%this.DB;\n      var cbs = this.DB-bs;\n      var bm = (1<<bs)-1;\n      r[0] = this[ds]>>bs;\n      for(var i = ds+1; i < this.t; ++i) {\n        r[i-ds-1] |= (this[i]&bm)<<cbs;\n        r[i-ds] = this[i]>>bs;\n      }\n      if(bs > 0) r[this.t-ds-1] |= (this.s&bm)<<cbs;\n      r.t = this.t-ds;\n      r.clamp();\n    }\n\n    // (protected) r = this - a\n    function bnpSubTo(a,r) {\n      var i = 0, c = 0, m = Math.min(a.t,this.t);\n      while(i < m) {\n        c += this[i]-a[i];\n        r[i++] = c&this.DM;\n        c >>= this.DB;\n      }\n      if(a.t < this.t) {\n        c -= a.s;\n        while(i < this.t) {\n          c += this[i];\n          r[i++] = c&this.DM;\n          c >>= this.DB;\n        }\n        c += this.s;\n      }\n      else {\n        c += this.s;\n        while(i < a.t) {\n          c -= a[i];\n          r[i++] = c&this.DM;\n          c >>= this.DB;\n        }\n        c -= a.s;\n      }\n      r.s = (c<0)?-1:0;\n      if(c < -1) r[i++] = this.DV+c;\n      else if(c > 0) r[i++] = c;\n      r.t = i;\n      r.clamp();\n    }\n\n    // (protected) r = this * a, r != this,a (HAC 14.12)\n    // \"this\" should be the larger one if appropriate.\n    function bnpMultiplyTo(a,r) {\n      var x = this.abs(), y = a.abs();\n      var i = x.t;\n      r.t = i+y.t;\n      while(--i >= 0) r[i] = 0;\n      for(i = 0; i < y.t; ++i) r[i+x.t] = x.am(0,y[i],r,i,0,x.t);\n      r.s = 0;\n      r.clamp();\n      if(this.s != a.s) BigInteger.ZERO.subTo(r,r);\n    }\n\n    // (protected) r = this^2, r != this (HAC 14.16)\n    function bnpSquareTo(r) {\n      var x = this.abs();\n      var i = r.t = 2*x.t;\n      while(--i >= 0) r[i] = 0;\n      for(i = 0; i < x.t-1; ++i) {\n        var c = x.am(i,x[i],r,2*i,0,1);\n        if((r[i+x.t]+=x.am(i+1,2*x[i],r,2*i+1,c,x.t-i-1)) >= x.DV) {\n          r[i+x.t] -= x.DV;\n          r[i+x.t+1] = 1;\n        }\n      }\n      if(r.t > 0) r[r.t-1] += x.am(i,x[i],r,2*i,0,1);\n      r.s = 0;\n      r.clamp();\n    }\n\n    // (protected) divide this by m, quotient and remainder to q, r (HAC 14.20)\n    // r != q, this != m.  q or r may be null.\n    function bnpDivRemTo(m,q,r) {\n      var pm = m.abs();\n      if(pm.t <= 0) return;\n      var pt = this.abs();\n      if(pt.t < pm.t) {\n        if(q != null) q.fromInt(0);\n        if(r != null) this.copyTo(r);\n        return;\n      }\n      if(r == null) r = nbi();\n      var y = nbi(), ts = this.s, ms = m.s;\n      var nsh = this.DB-nbits(pm[pm.t-1]);   // normalize modulus\n      if(nsh > 0) { pm.lShiftTo(nsh,y); pt.lShiftTo(nsh,r); }\n      else { pm.copyTo(y); pt.copyTo(r); }\n      var ys = y.t;\n      var y0 = y[ys-1];\n      if(y0 == 0) return;\n      var yt = y0*(1<<this.F1)+((ys>1)?y[ys-2]>>this.F2:0);\n      var d1 = this.FV/yt, d2 = (1<<this.F1)/yt, e = 1<<this.F2;\n      var i = r.t, j = i-ys, t = (q==null)?nbi():q;\n      y.dlShiftTo(j,t);\n      if(r.compareTo(t) >= 0) {\n        r[r.t++] = 1;\n        r.subTo(t,r);\n      }\n      BigInteger.ONE.dlShiftTo(ys,t);\n      t.subTo(y,y);  // \"negative\" y so we can replace sub with am later\n      while(y.t < ys) y[y.t++] = 0;\n      while(--j >= 0) {\n        // Estimate quotient digit\n        var qd = (r[--i]==y0)?this.DM:Math.floor(r[i]*d1+(r[i-1]+e)*d2);\n        if((r[i]+=y.am(0,qd,r,j,0,ys)) < qd) {   // Try it out\n          y.dlShiftTo(j,t);\n          r.subTo(t,r);\n          while(r[i] < --qd) r.subTo(t,r);\n        }\n      }\n      if(q != null) {\n        r.drShiftTo(ys,q);\n        if(ts != ms) BigInteger.ZERO.subTo(q,q);\n      }\n      r.t = ys;\n      r.clamp();\n      if(nsh > 0) r.rShiftTo(nsh,r); // Denormalize remainder\n      if(ts < 0) BigInteger.ZERO.subTo(r,r);\n    }\n\n    // (public) this mod a\n    function bnMod(a) {\n      var r = nbi();\n      this.abs().divRemTo(a,null,r);\n      if(this.s < 0 && r.compareTo(BigInteger.ZERO) > 0) a.subTo(r,r);\n      return r;\n    }\n\n    // Modular reduction using \"classic\" algorithm\n    function Classic(m) { this.m = m; }\n    function cConvert(x) {\n      if(x.s < 0 || x.compareTo(this.m) >= 0) return x.mod(this.m);\n      else return x;\n    }\n    function cRevert(x) { return x; }\n    function cReduce(x) { x.divRemTo(this.m,null,x); }\n    function cMulTo(x,y,r) { x.multiplyTo(y,r); this.reduce(r); }\n    function cSqrTo(x,r) { x.squareTo(r); this.reduce(r); }\n\n    Classic.prototype.convert = cConvert;\n    Classic.prototype.revert = cRevert;\n    Classic.prototype.reduce = cReduce;\n    Classic.prototype.mulTo = cMulTo;\n    Classic.prototype.sqrTo = cSqrTo;\n\n    // (protected) return \"-1/this % 2^DB\"; useful for Mont. reduction\n    // justification:\n    //         xy == 1 (mod m)\n    //         xy =  1+km\n    //   xy(2-xy) = (1+km)(1-km)\n    // x[y(2-xy)] = 1-k^2m^2\n    // x[y(2-xy)] == 1 (mod m^2)\n    // if y is 1/x mod m, then y(2-xy) is 1/x mod m^2\n    // should reduce x and y(2-xy) by m^2 at each step to keep size bounded.\n    // JS multiply \"overflows\" differently from C/C++, so care is needed here.\n    function bnpInvDigit() {\n      if(this.t < 1) return 0;\n      var x = this[0];\n      if((x&1) == 0) return 0;\n      var y = x&3;       // y == 1/x mod 2^2\n      y = (y*(2-(x&0xf)*y))&0xf; // y == 1/x mod 2^4\n      y = (y*(2-(x&0xff)*y))&0xff;   // y == 1/x mod 2^8\n      y = (y*(2-(((x&0xffff)*y)&0xffff)))&0xffff;    // y == 1/x mod 2^16\n      // last step - calculate inverse mod DV directly;\n      // assumes 16 < DB <= 32 and assumes ability to handle 48-bit ints\n      y = (y*(2-x*y%this.DV))%this.DV;       // y == 1/x mod 2^dbits\n      // we really want the negative inverse, and -DV < y < DV\n      return (y>0)?this.DV-y:-y;\n    }\n\n    // Montgomery reduction\n    function Montgomery(m) {\n      this.m = m;\n      this.mp = m.invDigit();\n      this.mpl = this.mp&0x7fff;\n      this.mph = this.mp>>15;\n      this.um = (1<<(m.DB-15))-1;\n      this.mt2 = 2*m.t;\n    }\n\n    // xR mod m\n    function montConvert(x) {\n      var r = nbi();\n      x.abs().dlShiftTo(this.m.t,r);\n      r.divRemTo(this.m,null,r);\n      if(x.s < 0 && r.compareTo(BigInteger.ZERO) > 0) this.m.subTo(r,r);\n      return r;\n    }\n\n    // x/R mod m\n    function montRevert(x) {\n      var r = nbi();\n      x.copyTo(r);\n      this.reduce(r);\n      return r;\n    }\n\n    // x = x/R mod m (HAC 14.32)\n    function montReduce(x) {\n      while(x.t <= this.mt2) // pad x so am has enough room later\n        x[x.t++] = 0;\n      for(var i = 0; i < this.m.t; ++i) {\n        // faster way of calculating u0 = x[i]*mp mod DV\n        var j = x[i]&0x7fff;\n        var u0 = (j*this.mpl+(((j*this.mph+(x[i]>>15)*this.mpl)&this.um)<<15))&x.DM;\n        // use am to combine the multiply-shift-add into one call\n        j = i+this.m.t;\n        x[j] += this.m.am(0,u0,x,i,0,this.m.t);\n        // propagate carry\n        while(x[j] >= x.DV) { x[j] -= x.DV; x[++j]++; }\n      }\n      x.clamp();\n      x.drShiftTo(this.m.t,x);\n      if(x.compareTo(this.m) >= 0) x.subTo(this.m,x);\n    }\n\n    // r = \"x^2/R mod m\"; x != r\n    function montSqrTo(x,r) { x.squareTo(r); this.reduce(r); }\n\n    // r = \"xy/R mod m\"; x,y != r\n    function montMulTo(x,y,r) { x.multiplyTo(y,r); this.reduce(r); }\n\n    Montgomery.prototype.convert = montConvert;\n    Montgomery.prototype.revert = montRevert;\n    Montgomery.prototype.reduce = montReduce;\n    Montgomery.prototype.mulTo = montMulTo;\n    Montgomery.prototype.sqrTo = montSqrTo;\n\n    // (protected) true iff this is even\n    function bnpIsEven() { return ((this.t>0)?(this[0]&1):this.s) == 0; }\n\n    // (protected) this^e, e < 2^32, doing sqr and mul with \"r\" (HAC 14.79)\n    function bnpExp(e,z) {\n      if(e > 0xffffffff || e < 1) return BigInteger.ONE;\n      var r = nbi(), r2 = nbi(), g = z.convert(this), i = nbits(e)-1;\n      g.copyTo(r);\n      while(--i >= 0) {\n        z.sqrTo(r,r2);\n        if((e&(1<<i)) > 0) z.mulTo(r2,g,r);\n        else { var t = r; r = r2; r2 = t; }\n      }\n      return z.revert(r);\n    }\n\n    // (public) this^e % m, 0 <= e < 2^32\n    function bnModPowInt(e,m) {\n      var z;\n      if(e < 256 || m.isEven()) z = new Classic(m); else z = new Montgomery(m);\n      return this.exp(e,z);\n    }\n\n    // protected\n    BigInteger.prototype.copyTo = bnpCopyTo;\n    BigInteger.prototype.fromInt = bnpFromInt;\n    BigInteger.prototype.fromString = bnpFromString;\n    BigInteger.prototype.clamp = bnpClamp;\n    BigInteger.prototype.dlShiftTo = bnpDLShiftTo;\n    BigInteger.prototype.drShiftTo = bnpDRShiftTo;\n    BigInteger.prototype.lShiftTo = bnpLShiftTo;\n    BigInteger.prototype.rShiftTo = bnpRShiftTo;\n    BigInteger.prototype.subTo = bnpSubTo;\n    BigInteger.prototype.multiplyTo = bnpMultiplyTo;\n    BigInteger.prototype.squareTo = bnpSquareTo;\n    BigInteger.prototype.divRemTo = bnpDivRemTo;\n    BigInteger.prototype.invDigit = bnpInvDigit;\n    BigInteger.prototype.isEven = bnpIsEven;\n    BigInteger.prototype.exp = bnpExp;\n\n    // public\n    BigInteger.prototype.toString = bnToString;\n    BigInteger.prototype.negate = bnNegate;\n    BigInteger.prototype.abs = bnAbs;\n    BigInteger.prototype.compareTo = bnCompareTo;\n    BigInteger.prototype.bitLength = bnBitLength;\n    BigInteger.prototype.mod = bnMod;\n    BigInteger.prototype.modPowInt = bnModPowInt;\n\n    // \"constants\"\n    BigInteger.ZERO = nbv(0);\n    BigInteger.ONE = nbv(1);\n\n    // Copyright (c) 2005-2009  Tom Wu\n    // All Rights Reserved.\n    // See \"LICENSE\" for details.\n\n    // Extended JavaScript BN functions, required for RSA private ops.\n\n    // Version 1.1: new BigInteger(\"0\", 10) returns \"proper\" zero\n    // Version 1.2: square() API, isProbablePrime fix\n\n    // (public)\n    function bnClone() { var r = nbi(); this.copyTo(r); return r; }\n\n    // (public) return value as integer\n    function bnIntValue() {\n      if(this.s < 0) {\n        if(this.t == 1) return this[0]-this.DV;\n        else if(this.t == 0) return -1;\n      }\n      else if(this.t == 1) return this[0];\n      else if(this.t == 0) return 0;\n      // assumes 16 < DB < 32\n      return ((this[1]&((1<<(32-this.DB))-1))<<this.DB)|this[0];\n    }\n\n    // (public) return value as byte\n    function bnByteValue() { return (this.t==0)?this.s:(this[0]<<24)>>24; }\n\n    // (public) return value as short (assumes DB>=16)\n    function bnShortValue() { return (this.t==0)?this.s:(this[0]<<16)>>16; }\n\n    // (protected) return x s.t. r^x < DV\n    function bnpChunkSize(r) { return Math.floor(Math.LN2*this.DB/Math.log(r)); }\n\n    // (public) 0 if this == 0, 1 if this > 0\n    function bnSigNum() {\n      if(this.s < 0) return -1;\n      else if(this.t <= 0 || (this.t == 1 && this[0] <= 0)) return 0;\n      else return 1;\n    }\n\n    // (protected) convert to radix string\n    function bnpToRadix(b) {\n      if(b == null) b = 10;\n      if(this.signum() == 0 || b < 2 || b > 36) return \"0\";\n      var cs = this.chunkSize(b);\n      var a = Math.pow(b,cs);\n      var d = nbv(a), y = nbi(), z = nbi(), r = \"\";\n      this.divRemTo(d,y,z);\n      while(y.signum() > 0) {\n        r = (a+z.intValue()).toString(b).substr(1) + r;\n        y.divRemTo(d,y,z);\n      }\n      return z.intValue().toString(b) + r;\n    }\n\n    // (protected) convert from radix string\n    function bnpFromRadix(s,b) {\n      this.fromInt(0);\n      if(b == null) b = 10;\n      var cs = this.chunkSize(b);\n      var d = Math.pow(b,cs), mi = false, j = 0, w = 0;\n      for(var i = 0; i < s.length; ++i) {\n        var x = intAt(s,i);\n        if(x < 0) {\n          if(s.charAt(i) == \"-\" && this.signum() == 0) mi = true;\n          continue;\n        }\n        w = b*w+x;\n        if(++j >= cs) {\n          this.dMultiply(d);\n          this.dAddOffset(w,0);\n          j = 0;\n          w = 0;\n        }\n      }\n      if(j > 0) {\n        this.dMultiply(Math.pow(b,j));\n        this.dAddOffset(w,0);\n      }\n      if(mi) BigInteger.ZERO.subTo(this,this);\n    }\n\n    // (protected) alternate constructor\n    function bnpFromNumber(a,b,c) {\n      if(\"number\" == typeof b) {\n        // new BigInteger(int,int,RNG)\n        if(a < 2) this.fromInt(1);\n        else {\n          this.fromNumber(a,c);\n          if(!this.testBit(a-1))\t// force MSB set\n            this.bitwiseTo(BigInteger.ONE.shiftLeft(a-1),op_or,this);\n          if(this.isEven()) this.dAddOffset(1,0); // force odd\n          while(!this.isProbablePrime(b)) {\n            this.dAddOffset(2,0);\n            if(this.bitLength() > a) this.subTo(BigInteger.ONE.shiftLeft(a-1),this);\n          }\n        }\n      }\n      else {\n        // new BigInteger(int,RNG)\n        var x = new Array(), t = a&7;\n        x.length = (a>>3)+1;\n        b.nextBytes(x);\n        if(t > 0) x[0] &= ((1<<t)-1); else x[0] = 0;\n        this.fromString(x,256);\n      }\n    }\n\n    // (public) convert to bigendian byte array\n    function bnToByteArray() {\n      var i = this.t, r = new Array();\n      r[0] = this.s;\n      var p = this.DB-(i*this.DB)%8, d, k = 0;\n      if(i-- > 0) {\n        if(p < this.DB && (d = this[i]>>p) != (this.s&this.DM)>>p)\n          r[k++] = d|(this.s<<(this.DB-p));\n        while(i >= 0) {\n          if(p < 8) {\n            d = (this[i]&((1<<p)-1))<<(8-p);\n            d |= this[--i]>>(p+=this.DB-8);\n          }\n          else {\n            d = (this[i]>>(p-=8))&0xff;\n            if(p <= 0) { p += this.DB; --i; }\n          }\n          if((d&0x80) != 0) d |= -256;\n          if(k == 0 && (this.s&0x80) != (d&0x80)) ++k;\n          if(k > 0 || d != this.s) r[k++] = d;\n        }\n      }\n      return r;\n    }\n\n    function bnEquals(a) { return(this.compareTo(a)==0); }\n    function bnMin(a) { return(this.compareTo(a)<0)?this:a; }\n    function bnMax(a) { return(this.compareTo(a)>0)?this:a; }\n\n    // (protected) r = this op a (bitwise)\n    function bnpBitwiseTo(a,op,r) {\n      var i, f, m = Math.min(a.t,this.t);\n      for(i = 0; i < m; ++i) r[i] = op(this[i],a[i]);\n      if(a.t < this.t) {\n        f = a.s&this.DM;\n        for(i = m; i < this.t; ++i) r[i] = op(this[i],f);\n        r.t = this.t;\n      }\n      else {\n        f = this.s&this.DM;\n        for(i = m; i < a.t; ++i) r[i] = op(f,a[i]);\n        r.t = a.t;\n      }\n      r.s = op(this.s,a.s);\n      r.clamp();\n    }\n\n    // (public) this & a\n    function op_and(x,y) { return x&y; }\n    function bnAnd(a) { var r = nbi(); this.bitwiseTo(a,op_and,r); return r; }\n\n    // (public) this | a\n    function op_or(x,y) { return x|y; }\n    function bnOr(a) { var r = nbi(); this.bitwiseTo(a,op_or,r); return r; }\n\n    // (public) this ^ a\n    function op_xor(x,y) { return x^y; }\n    function bnXor(a) { var r = nbi(); this.bitwiseTo(a,op_xor,r); return r; }\n\n    // (public) this & ~a\n    function op_andnot(x,y) { return x&~y; }\n    function bnAndNot(a) { var r = nbi(); this.bitwiseTo(a,op_andnot,r); return r; }\n\n    // (public) ~this\n    function bnNot() {\n      var r = nbi();\n      for(var i = 0; i < this.t; ++i) r[i] = this.DM&~this[i];\n      r.t = this.t;\n      r.s = ~this.s;\n      return r;\n    }\n\n    // (public) this << n\n    function bnShiftLeft(n) {\n      var r = nbi();\n      if(n < 0) this.rShiftTo(-n,r); else this.lShiftTo(n,r);\n      return r;\n    }\n\n    // (public) this >> n\n    function bnShiftRight(n) {\n      var r = nbi();\n      if(n < 0) this.lShiftTo(-n,r); else this.rShiftTo(n,r);\n      return r;\n    }\n\n    // return index of lowest 1-bit in x, x < 2^31\n    function lbit(x) {\n      if(x == 0) return -1;\n      var r = 0;\n      if((x&0xffff) == 0) { x >>= 16; r += 16; }\n      if((x&0xff) == 0) { x >>= 8; r += 8; }\n      if((x&0xf) == 0) { x >>= 4; r += 4; }\n      if((x&3) == 0) { x >>= 2; r += 2; }\n      if((x&1) == 0) ++r;\n      return r;\n    }\n\n    // (public) returns index of lowest 1-bit (or -1 if none)\n    function bnGetLowestSetBit() {\n      for(var i = 0; i < this.t; ++i)\n        if(this[i] != 0) return i*this.DB+lbit(this[i]);\n      if(this.s < 0) return this.t*this.DB;\n      return -1;\n    }\n\n    // return number of 1 bits in x\n    function cbit(x) {\n      var r = 0;\n      while(x != 0) { x &= x-1; ++r; }\n      return r;\n    }\n\n    // (public) return number of set bits\n    function bnBitCount() {\n      var r = 0, x = this.s&this.DM;\n      for(var i = 0; i < this.t; ++i) r += cbit(this[i]^x);\n      return r;\n    }\n\n    // (public) true iff nth bit is set\n    function bnTestBit(n) {\n      var j = Math.floor(n/this.DB);\n      if(j >= this.t) return(this.s!=0);\n      return((this[j]&(1<<(n%this.DB)))!=0);\n    }\n\n    // (protected) this op (1<<n)\n    function bnpChangeBit(n,op) {\n      var r = BigInteger.ONE.shiftLeft(n);\n      this.bitwiseTo(r,op,r);\n      return r;\n    }\n\n    // (public) this | (1<<n)\n    function bnSetBit(n) { return this.changeBit(n,op_or); }\n\n    // (public) this & ~(1<<n)\n    function bnClearBit(n) { return this.changeBit(n,op_andnot); }\n\n    // (public) this ^ (1<<n)\n    function bnFlipBit(n) { return this.changeBit(n,op_xor); }\n\n    // (protected) r = this + a\n    function bnpAddTo(a,r) {\n      var i = 0, c = 0, m = Math.min(a.t,this.t);\n      while(i < m) {\n        c += this[i]+a[i];\n        r[i++] = c&this.DM;\n        c >>= this.DB;\n      }\n      if(a.t < this.t) {\n        c += a.s;\n        while(i < this.t) {\n          c += this[i];\n          r[i++] = c&this.DM;\n          c >>= this.DB;\n        }\n        c += this.s;\n      }\n      else {\n        c += this.s;\n        while(i < a.t) {\n          c += a[i];\n          r[i++] = c&this.DM;\n          c >>= this.DB;\n        }\n        c += a.s;\n      }\n      r.s = (c<0)?-1:0;\n      if(c > 0) r[i++] = c;\n      else if(c < -1) r[i++] = this.DV+c;\n      r.t = i;\n      r.clamp();\n    }\n\n    // (public) this + a\n    function bnAdd(a) { var r = nbi(); this.addTo(a,r); return r; }\n\n    // (public) this - a\n    function bnSubtract(a) { var r = nbi(); this.subTo(a,r); return r; }\n\n    // (public) this * a\n    function bnMultiply(a) { var r = nbi(); this.multiplyTo(a,r); return r; }\n\n    // (public) this^2\n    function bnSquare() { var r = nbi(); this.squareTo(r); return r; }\n\n    // (public) this / a\n    function bnDivide(a) { var r = nbi(); this.divRemTo(a,r,null); return r; }\n\n    // (public) this % a\n    function bnRemainder(a) { var r = nbi(); this.divRemTo(a,null,r); return r; }\n\n    // (public) [this/a,this%a]\n    function bnDivideAndRemainder(a) {\n      var q = nbi(), r = nbi();\n      this.divRemTo(a,q,r);\n      return new Array(q,r);\n    }\n\n    // (protected) this *= n, this >= 0, 1 < n < DV\n    function bnpDMultiply(n) {\n      this[this.t] = this.am(0,n-1,this,0,0,this.t);\n      ++this.t;\n      this.clamp();\n    }\n\n    // (protected) this += n << w words, this >= 0\n    function bnpDAddOffset(n,w) {\n      if(n == 0) return;\n      while(this.t <= w) this[this.t++] = 0;\n      this[w] += n;\n      while(this[w] >= this.DV) {\n        this[w] -= this.DV;\n        if(++w >= this.t) this[this.t++] = 0;\n        ++this[w];\n      }\n    }\n\n    // A \"null\" reducer\n    function NullExp() {}\n    function nNop(x) { return x; }\n    function nMulTo(x,y,r) { x.multiplyTo(y,r); }\n    function nSqrTo(x,r) { x.squareTo(r); }\n\n    NullExp.prototype.convert = nNop;\n    NullExp.prototype.revert = nNop;\n    NullExp.prototype.mulTo = nMulTo;\n    NullExp.prototype.sqrTo = nSqrTo;\n\n    // (public) this^e\n    function bnPow(e) { return this.exp(e,new NullExp()); }\n\n    // (protected) r = lower n words of \"this * a\", a.t <= n\n    // \"this\" should be the larger one if appropriate.\n    function bnpMultiplyLowerTo(a,n,r) {\n      var i = Math.min(this.t+a.t,n);\n      r.s = 0; // assumes a,this >= 0\n      r.t = i;\n      while(i > 0) r[--i] = 0;\n      var j;\n      for(j = r.t-this.t; i < j; ++i) r[i+this.t] = this.am(0,a[i],r,i,0,this.t);\n      for(j = Math.min(a.t,n); i < j; ++i) this.am(0,a[i],r,i,0,n-i);\n      r.clamp();\n    }\n\n    // (protected) r = \"this * a\" without lower n words, n > 0\n    // \"this\" should be the larger one if appropriate.\n    function bnpMultiplyUpperTo(a,n,r) {\n      --n;\n      var i = r.t = this.t+a.t-n;\n      r.s = 0; // assumes a,this >= 0\n      while(--i >= 0) r[i] = 0;\n      for(i = Math.max(n-this.t,0); i < a.t; ++i)\n        r[this.t+i-n] = this.am(n-i,a[i],r,0,0,this.t+i-n);\n      r.clamp();\n      r.drShiftTo(1,r);\n    }\n\n    // Barrett modular reduction\n    function Barrett(m) {\n      // setup Barrett\n      this.r2 = nbi();\n      this.q3 = nbi();\n      BigInteger.ONE.dlShiftTo(2*m.t,this.r2);\n      this.mu = this.r2.divide(m);\n      this.m = m;\n    }\n\n    function barrettConvert(x) {\n      if(x.s < 0 || x.t > 2*this.m.t) return x.mod(this.m);\n      else if(x.compareTo(this.m) < 0) return x;\n      else { var r = nbi(); x.copyTo(r); this.reduce(r); return r; }\n    }\n\n    function barrettRevert(x) { return x; }\n\n    // x = x mod m (HAC 14.42)\n    function barrettReduce(x) {\n      x.drShiftTo(this.m.t-1,this.r2);\n      if(x.t > this.m.t+1) { x.t = this.m.t+1; x.clamp(); }\n      this.mu.multiplyUpperTo(this.r2,this.m.t+1,this.q3);\n      this.m.multiplyLowerTo(this.q3,this.m.t+1,this.r2);\n      while(x.compareTo(this.r2) < 0) x.dAddOffset(1,this.m.t+1);\n      x.subTo(this.r2,x);\n      while(x.compareTo(this.m) >= 0) x.subTo(this.m,x);\n    }\n\n    // r = x^2 mod m; x != r\n    function barrettSqrTo(x,r) { x.squareTo(r); this.reduce(r); }\n\n    // r = x*y mod m; x,y != r\n    function barrettMulTo(x,y,r) { x.multiplyTo(y,r); this.reduce(r); }\n\n    Barrett.prototype.convert = barrettConvert;\n    Barrett.prototype.revert = barrettRevert;\n    Barrett.prototype.reduce = barrettReduce;\n    Barrett.prototype.mulTo = barrettMulTo;\n    Barrett.prototype.sqrTo = barrettSqrTo;\n\n    // (public) this^e % m (HAC 14.85)\n    function bnModPow(e,m) {\n      var i = e.bitLength(), k, r = nbv(1), z;\n      if(i <= 0) return r;\n      else if(i < 18) k = 1;\n      else if(i < 48) k = 3;\n      else if(i < 144) k = 4;\n      else if(i < 768) k = 5;\n      else k = 6;\n      if(i < 8)\n        z = new Classic(m);\n      else if(m.isEven())\n        z = new Barrett(m);\n      else\n        z = new Montgomery(m);\n\n      // precomputation\n      var g = new Array(), n = 3, k1 = k-1, km = (1<<k)-1;\n      g[1] = z.convert(this);\n      if(k > 1) {\n        var g2 = nbi();\n        z.sqrTo(g[1],g2);\n        while(n <= km) {\n          g[n] = nbi();\n          z.mulTo(g2,g[n-2],g[n]);\n          n += 2;\n        }\n      }\n\n      var j = e.t-1, w, is1 = true, r2 = nbi(), t;\n      i = nbits(e[j])-1;\n      while(j >= 0) {\n        if(i >= k1) w = (e[j]>>(i-k1))&km;\n        else {\n          w = (e[j]&((1<<(i+1))-1))<<(k1-i);\n          if(j > 0) w |= e[j-1]>>(this.DB+i-k1);\n        }\n\n        n = k;\n        while((w&1) == 0) { w >>= 1; --n; }\n        if((i -= n) < 0) { i += this.DB; --j; }\n        if(is1) {\t// ret == 1, don't bother squaring or multiplying it\n          g[w].copyTo(r);\n          is1 = false;\n        }\n        else {\n          while(n > 1) { z.sqrTo(r,r2); z.sqrTo(r2,r); n -= 2; }\n          if(n > 0) z.sqrTo(r,r2); else { t = r; r = r2; r2 = t; }\n          z.mulTo(r2,g[w],r);\n        }\n\n        while(j >= 0 && (e[j]&(1<<i)) == 0) {\n          z.sqrTo(r,r2); t = r; r = r2; r2 = t;\n          if(--i < 0) { i = this.DB-1; --j; }\n        }\n      }\n      return z.revert(r);\n    }\n\n    // (public) gcd(this,a) (HAC 14.54)\n    function bnGCD(a) {\n      var x = (this.s<0)?this.negate():this.clone();\n      var y = (a.s<0)?a.negate():a.clone();\n      if(x.compareTo(y) < 0) { var t = x; x = y; y = t; }\n      var i = x.getLowestSetBit(), g = y.getLowestSetBit();\n      if(g < 0) return x;\n      if(i < g) g = i;\n      if(g > 0) {\n        x.rShiftTo(g,x);\n        y.rShiftTo(g,y);\n      }\n      while(x.signum() > 0) {\n        if((i = x.getLowestSetBit()) > 0) x.rShiftTo(i,x);\n        if((i = y.getLowestSetBit()) > 0) y.rShiftTo(i,y);\n        if(x.compareTo(y) >= 0) {\n          x.subTo(y,x);\n          x.rShiftTo(1,x);\n        }\n        else {\n          y.subTo(x,y);\n          y.rShiftTo(1,y);\n        }\n      }\n      if(g > 0) y.lShiftTo(g,y);\n      return y;\n    }\n\n    // (protected) this % n, n < 2^26\n    function bnpModInt(n) {\n      if(n <= 0) return 0;\n      var d = this.DV%n, r = (this.s<0)?n-1:0;\n      if(this.t > 0)\n        if(d == 0) r = this[0]%n;\n        else for(var i = this.t-1; i >= 0; --i) r = (d*r+this[i])%n;\n      return r;\n    }\n\n    // (public) 1/this % m (HAC 14.61)\n    function bnModInverse(m) {\n      var ac = m.isEven();\n      if((this.isEven() && ac) || m.signum() == 0) return BigInteger.ZERO;\n      var u = m.clone(), v = this.clone();\n      var a = nbv(1), b = nbv(0), c = nbv(0), d = nbv(1);\n      while(u.signum() != 0) {\n        while(u.isEven()) {\n          u.rShiftTo(1,u);\n          if(ac) {\n            if(!a.isEven() || !b.isEven()) { a.addTo(this,a); b.subTo(m,b); }\n            a.rShiftTo(1,a);\n          }\n          else if(!b.isEven()) b.subTo(m,b);\n          b.rShiftTo(1,b);\n        }\n        while(v.isEven()) {\n          v.rShiftTo(1,v);\n          if(ac) {\n            if(!c.isEven() || !d.isEven()) { c.addTo(this,c); d.subTo(m,d); }\n            c.rShiftTo(1,c);\n          }\n          else if(!d.isEven()) d.subTo(m,d);\n          d.rShiftTo(1,d);\n        }\n        if(u.compareTo(v) >= 0) {\n          u.subTo(v,u);\n          if(ac) a.subTo(c,a);\n          b.subTo(d,b);\n        }\n        else {\n          v.subTo(u,v);\n          if(ac) c.subTo(a,c);\n          d.subTo(b,d);\n        }\n      }\n      if(v.compareTo(BigInteger.ONE) != 0) return BigInteger.ZERO;\n      if(d.compareTo(m) >= 0) return d.subtract(m);\n      if(d.signum() < 0) d.addTo(m,d); else return d;\n      if(d.signum() < 0) return d.add(m); else return d;\n    }\n\n    var lowprimes = [2,3,5,7,11,13,17,19,23,29,31,37,41,43,47,53,59,61,67,71,73,79,83,89,97,101,103,107,109,113,127,131,137,139,149,151,157,163,167,173,179,181,191,193,197,199,211,223,227,229,233,239,241,251,257,263,269,271,277,281,283,293,307,311,313,317,331,337,347,349,353,359,367,373,379,383,389,397,401,409,419,421,431,433,439,443,449,457,461,463,467,479,487,491,499,503,509,521,523,541,547,557,563,569,571,577,587,593,599,601,607,613,617,619,631,641,643,647,653,659,661,673,677,683,691,701,709,719,727,733,739,743,751,757,761,769,773,787,797,809,811,821,823,827,829,839,853,857,859,863,877,881,883,887,907,911,919,929,937,941,947,953,967,971,977,983,991,997];\n    var lplim = (1<<26)/lowprimes[lowprimes.length-1];\n\n    // (public) test primality with certainty >= 1-.5^t\n    function bnIsProbablePrime(t) {\n      var i, x = this.abs();\n      if(x.t == 1 && x[0] <= lowprimes[lowprimes.length-1]) {\n        for(i = 0; i < lowprimes.length; ++i)\n          if(x[0] == lowprimes[i]) return true;\n        return false;\n      }\n      if(x.isEven()) return false;\n      i = 1;\n      while(i < lowprimes.length) {\n        var m = lowprimes[i], j = i+1;\n        while(j < lowprimes.length && m < lplim) m *= lowprimes[j++];\n        m = x.modInt(m);\n        while(i < j) if(m%lowprimes[i++] == 0) return false;\n      }\n      return x.millerRabin(t);\n    }\n\n    // (protected) true if probably prime (HAC 4.24, Miller-Rabin)\n    function bnpMillerRabin(t) {\n      var n1 = this.subtract(BigInteger.ONE);\n      var k = n1.getLowestSetBit();\n      if(k <= 0) return false;\n      var r = n1.shiftRight(k);\n      t = (t+1)>>1;\n      if(t > lowprimes.length) t = lowprimes.length;\n      var a = nbi();\n      for(var i = 0; i < t; ++i) {\n        //Pick bases at random, instead of starting at 2\n        a.fromInt(lowprimes[Math.floor(Math.random()*lowprimes.length)]);\n        var y = a.modPow(r,this);\n        if(y.compareTo(BigInteger.ONE) != 0 && y.compareTo(n1) != 0) {\n          var j = 1;\n          while(j++ < k && y.compareTo(n1) != 0) {\n            y = y.modPowInt(2,this);\n            if(y.compareTo(BigInteger.ONE) == 0) return false;\n          }\n          if(y.compareTo(n1) != 0) return false;\n        }\n      }\n      return true;\n    }\n\n    // protected\n    BigInteger.prototype.chunkSize = bnpChunkSize;\n    BigInteger.prototype.toRadix = bnpToRadix;\n    BigInteger.prototype.fromRadix = bnpFromRadix;\n    BigInteger.prototype.fromNumber = bnpFromNumber;\n    BigInteger.prototype.bitwiseTo = bnpBitwiseTo;\n    BigInteger.prototype.changeBit = bnpChangeBit;\n    BigInteger.prototype.addTo = bnpAddTo;\n    BigInteger.prototype.dMultiply = bnpDMultiply;\n    BigInteger.prototype.dAddOffset = bnpDAddOffset;\n    BigInteger.prototype.multiplyLowerTo = bnpMultiplyLowerTo;\n    BigInteger.prototype.multiplyUpperTo = bnpMultiplyUpperTo;\n    BigInteger.prototype.modInt = bnpModInt;\n    BigInteger.prototype.millerRabin = bnpMillerRabin;\n\n    // public\n    BigInteger.prototype.clone = bnClone;\n    BigInteger.prototype.intValue = bnIntValue;\n    BigInteger.prototype.byteValue = bnByteValue;\n    BigInteger.prototype.shortValue = bnShortValue;\n    BigInteger.prototype.signum = bnSigNum;\n    BigInteger.prototype.toByteArray = bnToByteArray;\n    BigInteger.prototype.equals = bnEquals;\n    BigInteger.prototype.min = bnMin;\n    BigInteger.prototype.max = bnMax;\n    BigInteger.prototype.and = bnAnd;\n    BigInteger.prototype.or = bnOr;\n    BigInteger.prototype.xor = bnXor;\n    BigInteger.prototype.andNot = bnAndNot;\n    BigInteger.prototype.not = bnNot;\n    BigInteger.prototype.shiftLeft = bnShiftLeft;\n    BigInteger.prototype.shiftRight = bnShiftRight;\n    BigInteger.prototype.getLowestSetBit = bnGetLowestSetBit;\n    BigInteger.prototype.bitCount = bnBitCount;\n    BigInteger.prototype.testBit = bnTestBit;\n    BigInteger.prototype.setBit = bnSetBit;\n    BigInteger.prototype.clearBit = bnClearBit;\n    BigInteger.prototype.flipBit = bnFlipBit;\n    BigInteger.prototype.add = bnAdd;\n    BigInteger.prototype.subtract = bnSubtract;\n    BigInteger.prototype.multiply = bnMultiply;\n    BigInteger.prototype.divide = bnDivide;\n    BigInteger.prototype.remainder = bnRemainder;\n    BigInteger.prototype.divideAndRemainder = bnDivideAndRemainder;\n    BigInteger.prototype.modPow = bnModPow;\n    BigInteger.prototype.modInverse = bnModInverse;\n    BigInteger.prototype.pow = bnPow;\n    BigInteger.prototype.gcd = bnGCD;\n    BigInteger.prototype.isProbablePrime = bnIsProbablePrime;\n\n    // JSBN-specific extension\n    BigInteger.prototype.square = bnSquare;\n\n    // Expose the Barrett function\n    BigInteger.prototype.Barrett = Barrett\n\n    // BigInteger interfaces not implemented in jsbn:\n\n    // BigInteger(int signum, byte[] magnitude)\n    // double doubleValue()\n    // float floatValue()\n    // int hashCode()\n    // long longValue()\n    // static BigInteger valueOf(long val)\n\n\t// Random number generator - requires a PRNG backend, e.g. prng4.js\n\n\t// For best results, put code like\n\t// <body onClick='rng_seed_time();' onKeyPress='rng_seed_time();'>\n\t// in your main HTML document.\n\n\tvar rng_state;\n\tvar rng_pool;\n\tvar rng_pptr;\n\n\t// Mix in a 32-bit integer into the pool\n\tfunction rng_seed_int(x) {\n\t  rng_pool[rng_pptr++] ^= x & 255;\n\t  rng_pool[rng_pptr++] ^= (x >> 8) & 255;\n\t  rng_pool[rng_pptr++] ^= (x >> 16) & 255;\n\t  rng_pool[rng_pptr++] ^= (x >> 24) & 255;\n\t  if(rng_pptr >= rng_psize) rng_pptr -= rng_psize;\n\t}\n\n\t// Mix in the current time (w/milliseconds) into the pool\n\tfunction rng_seed_time() {\n\t  rng_seed_int(new Date().getTime());\n\t}\n\n\t// Initialize the pool with junk if needed.\n\tif(rng_pool == null) {\n\t  rng_pool = new Array();\n\t  rng_pptr = 0;\n\t  var t;\n\t  if(typeof window !== \"undefined\" && window.crypto) {\n\t\tif (window.crypto.getRandomValues) {\n\t\t  // Use webcrypto if available\n\t\t  var ua = new Uint8Array(32);\n\t\t  window.crypto.getRandomValues(ua);\n\t\t  for(t = 0; t < 32; ++t)\n\t\t\trng_pool[rng_pptr++] = ua[t];\n\t\t}\n\t\telse if(navigator.appName == \"Netscape\" && navigator.appVersion < \"5\") {\n\t\t  // Extract entropy (256 bits) from NS4 RNG if available\n\t\t  var z = window.crypto.random(32);\n\t\t  for(t = 0; t < z.length; ++t)\n\t\t\trng_pool[rng_pptr++] = z.charCodeAt(t) & 255;\n\t\t}\n\t  }\n\t  while(rng_pptr < rng_psize) {  // extract some randomness from Math.random()\n\t\tt = Math.floor(65536 * Math.random());\n\t\trng_pool[rng_pptr++] = t >>> 8;\n\t\trng_pool[rng_pptr++] = t & 255;\n\t  }\n\t  rng_pptr = 0;\n\t  rng_seed_time();\n\t  //rng_seed_int(window.screenX);\n\t  //rng_seed_int(window.screenY);\n\t}\n\n\tfunction rng_get_byte() {\n\t  if(rng_state == null) {\n\t\trng_seed_time();\n\t\trng_state = prng_newstate();\n\t\trng_state.init(rng_pool);\n\t\tfor(rng_pptr = 0; rng_pptr < rng_pool.length; ++rng_pptr)\n\t\t  rng_pool[rng_pptr] = 0;\n\t\trng_pptr = 0;\n\t\t//rng_pool = null;\n\t  }\n\t  // TODO: allow reseeding after first request\n\t  return rng_state.next();\n\t}\n\n\tfunction rng_get_bytes(ba) {\n\t  var i;\n\t  for(i = 0; i < ba.length; ++i) ba[i] = rng_get_byte();\n\t}\n\n\tfunction SecureRandom() {}\n\n\tSecureRandom.prototype.nextBytes = rng_get_bytes;\n\n\t// prng4.js - uses Arcfour as a PRNG\n\n\tfunction Arcfour() {\n\t  this.i = 0;\n\t  this.j = 0;\n\t  this.S = new Array();\n\t}\n\n\t// Initialize arcfour context from key, an array of ints, each from [0..255]\n\tfunction ARC4init(key) {\n\t  var i, j, t;\n\t  for(i = 0; i < 256; ++i)\n\t\tthis.S[i] = i;\n\t  j = 0;\n\t  for(i = 0; i < 256; ++i) {\n\t\tj = (j + this.S[i] + key[i % key.length]) & 255;\n\t\tt = this.S[i];\n\t\tthis.S[i] = this.S[j];\n\t\tthis.S[j] = t;\n\t  }\n\t  this.i = 0;\n\t  this.j = 0;\n\t}\n\n\tfunction ARC4next() {\n\t  var t;\n\t  this.i = (this.i + 1) & 255;\n\t  this.j = (this.j + this.S[this.i]) & 255;\n\t  t = this.S[this.i];\n\t  this.S[this.i] = this.S[this.j];\n\t  this.S[this.j] = t;\n\t  return this.S[(t + this.S[this.i]) & 255];\n\t}\n\n\tArcfour.prototype.init = ARC4init;\n\tArcfour.prototype.next = ARC4next;\n\n\t// Plug in your RNG constructor here\n\tfunction prng_newstate() {\n\t  return new Arcfour();\n\t}\n\n\t// Pool size must be a multiple of 4 and greater than 32.\n\t// An array of bytes the size of the pool will be passed to init()\n\tvar rng_psize = 256;\n\n  BigInteger.SecureRandom = SecureRandom;\n  BigInteger.BigInteger = BigInteger;\n  if (typeof exports !== 'undefined') {\n    exports = module.exports = BigInteger;\n  } else {\n    this.BigInteger = BigInteger;\n    this.SecureRandom = SecureRandom;\n  }\n\n}).call(this);\n"]}