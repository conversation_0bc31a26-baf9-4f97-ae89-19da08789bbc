/**
 * 设备代理云函数 - 处理内网设备连接和控制
 */
const http = require('http');
const https = require('https');

exports.main = async (event, context) => {
  console.log('设备代理云函数启动:', event);
  
  const { action, deviceIp, params = {} } = event;
  
  try {
    switch (action) {
      case 'restartDevice':
        return await restartDevice(deviceIp);
      
      case 'checkDevice':
        return await checkDeviceStatus(deviceIp);
      
      case 'controlDevice':
        return await controlDevice(deviceIp, params);
      
      case 'getDeviceInfo':
        return await getDeviceInfo(deviceIp);
      
      default:
        throw new Error(`未知操作: ${action}`);
    }
  } catch (error) {
    console.error('设备代理错误:', error);
    return {
      success: false,
      error: error.message,
      code: error.code || 'UNKNOWN_ERROR'
    };
  }
};

/**
 * 重启设备视频流
 */
async function restartDevice(deviceIp) {
  console.log(`重启设备视频流: ${deviceIp}`);
  
  return new Promise((resolve, reject) => {
    const restartUrl = `http://${deviceIp}/cgi-bin/set_camera_param.cgi?restart=true`;
    
    const req = http.get(restartUrl, {
      timeout: 5000,
      headers: {
        'User-Agent': 'TencentCloud-DeviceProxy/1.0',
        'Accept': '*/*',
        'Connection': 'close'
      }
    }, (res) => {
      console.log(`重启响应状态: ${res.statusCode}`);
      
      let data = '';
      res.on('data', chunk => data += chunk);
      res.on('end', () => {
        resolve({
          success: res.statusCode >= 200 && res.statusCode < 400,
          statusCode: res.statusCode,
          data: data,
          message: '设备重启指令已发送'
        });
      });
    });
    
    req.on('error', (error) => {
      console.error('重启设备失败:', error);
      resolve({
        success: false,
        error: error.message,
        code: 'RESTART_FAILED'
      });
    });
    
    req.on('timeout', () => {
      req.destroy();
      resolve({
        success: false,
        error: '重启请求超时',
        code: 'RESTART_TIMEOUT'
      });
    });
  });
}

/**
 * 检查设备状态
 */
async function checkDeviceStatus(deviceIp) {
  console.log(`检查设备状态: ${deviceIp}`);
  
  return new Promise((resolve, reject) => {
    const req = http.get(`http://${deviceIp}/`, {
      timeout: 3000,
      headers: {
        'User-Agent': 'TencentCloud-DeviceProxy/1.0'
      }
    }, (res) => {
      console.log(`设备状态检查响应: ${res.statusCode}`);
      
      resolve({
        success: true,
        online: res.statusCode < 500,
        statusCode: res.statusCode,
        message: res.statusCode < 500 ? '设备在线' : '设备可能离线'
      });
    });
    
    req.on('error', (error) => {
      console.error('设备状态检查失败:', error);
      resolve({
        success: false,
        online: false,
        error: error.message,
        code: 'DEVICE_OFFLINE'
      });
    });
    
    req.on('timeout', () => {
      req.destroy();
      resolve({
        success: false,
        online: false,
        error: '设备响应超时',
        code: 'DEVICE_TIMEOUT'
      });
    });
  });
}

/**
 * 控制设备
 */
async function controlDevice(deviceIp, params) {
  console.log(`控制设备: ${deviceIp}`, params);
  
  const { command, value } = params;
  let controlUrl = `http://${deviceIp}/cgi-bin/set_camera_param.cgi`;
  
  // 构建控制参数
  const queryParams = new URLSearchParams();
  if (command && value !== undefined) {
    queryParams.append(command, value);
  }
  
  if (queryParams.toString()) {
    controlUrl += '?' + queryParams.toString();
  }
  
  return new Promise((resolve, reject) => {
    const req = http.get(controlUrl, {
      timeout: 3000,
      headers: {
        'User-Agent': 'TencentCloud-DeviceProxy/1.0'
      }
    }, (res) => {
      console.log(`设备控制响应: ${res.statusCode}`);
      
      let data = '';
      res.on('data', chunk => data += chunk);
      res.on('end', () => {
        resolve({
          success: res.statusCode >= 200 && res.statusCode < 400,
          statusCode: res.statusCode,
          data: data,
          message: '设备控制指令已发送'
        });
      });
    });
    
    req.on('error', (error) => {
      console.error('设备控制失败:', error);
      resolve({
        success: false,
        error: error.message,
        code: 'CONTROL_FAILED'
      });
    });
    
    req.on('timeout', () => {
      req.destroy();
      resolve({
        success: false,
        error: '控制请求超时',
        code: 'CONTROL_TIMEOUT'
      });
    });
  });
}

/**
 * 获取设备信息
 */
async function getDeviceInfo(deviceIp) {
  console.log(`获取设备信息: ${deviceIp}`);
  
  return new Promise((resolve, reject) => {
    const req = http.get(`http://${deviceIp}/video`, {
      timeout: 3000,
      headers: {
        'User-Agent': 'TencentCloud-DeviceProxy/1.0'
      }
    }, (res) => {
      console.log(`设备信息响应: ${res.statusCode}`);
      
      let data = '';
      res.on('data', chunk => data += chunk);
      res.on('end', () => {
        // 解析HTML获取设备信息
        const deviceInfo = parseDeviceInfo(data);
        
        resolve({
          success: true,
          statusCode: res.statusCode,
          deviceInfo: deviceInfo,
          rawHtml: data
        });
      });
    });
    
    req.on('error', (error) => {
      console.error('获取设备信息失败:', error);
      resolve({
        success: false,
        error: error.message,
        code: 'INFO_FAILED'
      });
    });
    
    req.on('timeout', () => {
      req.destroy();
      resolve({
        success: false,
        error: '获取信息超时',
        code: 'INFO_TIMEOUT'
      });
    });
  });
}

/**
 * 解析设备信息
 */
function parseDeviceInfo(html) {
  const info = {
    hasVideoStream: false,
    streamPath: null,
    deviceType: 'unknown'
  };
  
  if (typeof html === 'string') {
    // 检查是否包含Camera Control
    if (html.includes('Camera Control')) {
      info.deviceType = 'camera';
    }
    
    // 检查是否有videoStream元素
    if (html.includes('videoStream')) {
      info.hasVideoStream = true;
    }
    
    // 尝试从注释中提取流路径
    const streamMatch = html.match(/<!--[^>]*src\s*=\s*["']([^"']*stream[^"']*)["']/i);
    if (streamMatch) {
      try {
        const url = new URL(streamMatch[1]);
        info.streamPath = url.pathname;
      } catch (e) {
        info.streamPath = '/stream'; // 默认路径
      }
    } else {
      info.streamPath = '/stream'; // 默认路径
    }
  }
  
  return info;
}
