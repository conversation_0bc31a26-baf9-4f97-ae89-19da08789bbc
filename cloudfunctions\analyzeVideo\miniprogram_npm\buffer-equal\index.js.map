{"version": 3, "sources": ["index.js"], "names": [], "mappings": ";;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.js", "sourcesContent": ["var Buffer = require('buffer').Buffer; // for use with browserify\n\nmodule.exports = function (a, b) {\n    if (!Buffer.isBuffer(a)) return undefined;\n    if (!Buffer.isBuffer(b)) return undefined;\n    if (typeof a.equals === 'function') return a.equals(b);\n    if (a.length !== b.length) return false;\n    \n    for (var i = 0; i < a.length; i++) {\n        if (a[i] !== b[i]) return false;\n    }\n    \n    return true;\n};\n"]}