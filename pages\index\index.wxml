<!-- 自定义导航栏 - Skyline渲染引擎，动态适配状态栏高度 -->
<view class="custom-navbar" style="height: {{totalNavHeight}}px; padding-top: {{safeAreaTop}}px;">
  <view class="navbar-title">设备管理系统</view>
</view>

<!-- 可滚动容器 - 修复滚动问题，动态适配内容区域top值 -->
<scroll-view class="scroll-container" style="top: {{totalNavHeight}}px; height: calc(100vh - {{totalNavHeight}}px);" scroll-y="{{true}}" enhanced="{{true}}" show-scrollbar="{{false}}" bindscroll="onScroll" bindtouchstart="onTouchStart" bindtouchmove="onTouchMove" bindtouchend="onTouchEnd">
  <view class="container {{recordingMode ? 'recording-mode' : ''}} {{isScrolling ? 'scrolling' : ''}}">
    <!-- 轮播图 -->
    <swiper class="carousel" autoplay interval="3000" duration="500">
      <swiper-item>
        <icon type="success" size="100" color="#4a90e2"></icon>
      </swiper-item>
      <swiper-item>
        <icon type="info" size="100" color="#ff9500"></icon>
      </swiper-item>
    </swiper>

    <!-- 录制准备状态 -->
    <view class="recording-preparing" wx:if="{{recordingPreparing && isRecording}}">
      <view class="recording-preparing-text">正在准备录制...</view>
      <view class="recording-preparing-spinner"></view>
    </view>

    <!-- 录制倒计时 -->
    <view class="recording-countdown" wx:if="{{isRecording && recordingCountdown > 0}}">
      {{recordingCountdown}}s
    </view>

    <!-- 全屏提示 -->
    <view class="fullscreen-tip" wx:if="{{showFullscreenTip && isLocalVideo && !recordingMode && !isRecording}}">
      双击视频进入全屏
    </view>

    <!-- 退出全屏提示（全屏模式下不显示任何UI提示，保持和录制模式一致） -->
    <!-- <view class="exit-fullscreen-tip" wx:if="{{false}}">
      双击视频退出全屏
    </view> -->

    <!-- 搜索栏 - 在参数模式时隐藏 -->
    <view class="{{searchBarClass || 'search-bar'}} {{isParameterMode ? 'search-bar-hidden' : ''}}" hidden="{{isParameterMode}}">
      <input class="search-input" type="text" placeholder="请输入关键词搜索" value="{{searchValue}}" bindinput="handleSearch" />
      <view class="search-buttons">
        <button class="search-button" hover-class="none" hover-stay-time="0" bindtap="onSearch">查询</button>
        <button class="clear-button" hover-class="none" hover-stay-time="0" bindtap="clearSearch">清除</button>
      </view>
    </view>

    <!-- 参数模式顶部按钮栏 - 替代搜索栏位置 -->
    <view class="{{parameterTopButtonsClass || 'parameter-top-buttons'}} {{isParameterMode ? 'parameter-top-buttons-visible' : ''}}" hidden="{{!isParameterMode}}">
      <button class="parameter-top-button return-button" hover-class="none" hover-stay-time="0" bindtap="toggleParameterMode">返回默认模式</button>
      <button class="parameter-top-button record-button {{isRecording ? 'recording' : ''}}" hover-class="none" hover-stay-time="0" bindtap="startRecordVideo">
        {{isRecording ? '停止录制' : '开始录制'}}
      </button>
      <button class="parameter-top-button analyze-button" hover-class="none" hover-stay-time="0" bindtap="onAnalyzeVideo">开始分析视频</button>
    </view>

    <!-- 视频播放器 -->
    <view class="{{videoContainerClass || 'video-container'}} {{recordingMode ? 'recording-fullscreen' : ''}}" id="videoContainer">
      <!-- 视频刷新指示器 -->
      <view class="video-refresh-indicator" wx:if="{{refreshingVideo}}">
        <view class="refresh-spinner"></view>
        <text class="refresh-text">刷新视频流...</text>
      </view>

      <!-- 本地视频和设备录制视频使用统一的video组件（带控制条） -->
      <video
      wx:if="{{isLocalVideo && videoUrl}}"
      id="myVideo"
      src="{{videoUrl}}"
      bindplay="onVideoPlay"
      bindpause="onVideoPause"
      bindtimeupdate="onVideoTimeUpdate"
      bindtap="onVideoTap"
      binderror="onVideoError"
      bindloadeddata="onVideoLoaded"
      show-center-play-btn="{{!isRecording && showVideoControls}}"
      controls="{{!isRecording && showVideoControls}}"
      show-fullscreen-btn="false"
      show-play-btn="{{!isRecording && showVideoControls}}"
      show-progress="{{!isRecording && showVideoControls}}"
      class="video-content {{refreshingVideo ? 'refreshing' : ''}} {{videoLoadError ? 'video-error' : ''}}"
      object-fit="contain"
      enable-progress-gesture="{{!isRecording && showVideoControls}}"
      preload="auto"
      poster=""></video>

    <!-- MJPEG流使用Canvas显示 -->
    <view wx:if="{{streamType === 'mjpeg' && !isLocalVideo}}" class="mjpeg-container">
      <canvas
        id="mjpegCanvas"
        canvas-id="mjpegCanvas"
        type="2d"
        class="video-content {{refreshingVideo ? 'refreshing' : ''}}"
        bindtouchstart="onCanvasTouchStart"
        bindtouchend="onCanvasTouchEnd">
      </canvas>

      <!-- 调试模式性能监控显示器 -->
      <view class="fps-monitor" wx:if="{{showFPSMonitor}}">
        <text class="fps-text">FPS: {{currentFPS}}</text>
        <text class="fps-target">目标: 10 (调试)</text>
        <view class="fps-bar">
          <view class="fps-progress" style="width: {{fpsPercentage}}%"></view>
        </view>
      </view>
    </view>

    <!-- 🔧 修复：检测区域只在有可预览视频时显示，不包括MJPEG流 -->
    <view class="detection-areas" wx:if="{{(isLocalVideo || hasVideo) && !isRecording}}">
      <view class="detection-box c-area {{isDragging && currentBox === 'cArea' ? 'dragging' : ''}} {{isResizing && currentBox === 'cArea' ? 'resizing' : ''}}"
        style="left: {{detectionAreas.cArea.x * 100}}%; top: {{detectionAreas.cArea.y * 100}}%; width: {{detectionAreas.cArea.width}}rpx; height: {{detectionAreas.cArea.height}}rpx;"
        catchtouchstart="handleBoxTouchStart"
        catchtouchmove="handleBoxTouchMove"
        catchtouchend="handleBoxTouchEnd"
        data-type="cArea">
        <text class="area-label">C区</text>
        <!-- 调整大小的手柄 -->
        <view class="resize-handle top-left" data-handle="top-left" data-type="cArea" catchtouchstart="handleResizeStart" catchtouchmove="handleResize" catchtouchend="handleResizeEnd"></view>
        <view class="resize-handle top-right" data-handle="top-right" data-type="cArea" catchtouchstart="handleResizeStart" catchtouchmove="handleResize" catchtouchend="handleResizeEnd"></view>
        <view class="resize-handle bottom-left" data-handle="bottom-left" data-type="cArea" catchtouchstart="handleResizeStart" catchtouchmove="handleResize" catchtouchend="handleResizeEnd"></view>
        <view class="resize-handle bottom-right" data-handle="bottom-right" data-type="cArea" catchtouchstart="handleResizeStart" catchtouchmove="handleResize" catchtouchend="handleResizeEnd"></view>
      </view>

      <view class="detection-box t-area {{isDragging && currentBox === 'tArea' ? 'dragging' : ''}} {{isResizing && currentBox === 'tArea' ? 'resizing' : ''}}"
        style="left: {{detectionAreas.tArea.x * 100}}%; top: {{detectionAreas.tArea.y * 100}}%; width: {{detectionAreas.tArea.width}}rpx; height: {{detectionAreas.tArea.height}}rpx;"
        catchtouchstart="handleBoxTouchStart"
        catchtouchmove="handleBoxTouchMove"
        catchtouchend="handleBoxTouchEnd"
        data-type="tArea">
        <text class="area-label">T区</text>
        <!-- 调整大小的手柄 -->
        <view class="resize-handle top-left" data-handle="top-left" data-type="tArea" catchtouchstart="handleResizeStart" catchtouchmove="handleResize" catchtouchend="handleResizeEnd"></view>
        <view class="resize-handle top-right" data-handle="top-right" data-type="tArea" catchtouchstart="handleResizeStart" catchtouchmove="handleResize" catchtouchend="handleResizeEnd"></view>
        <view class="resize-handle bottom-left" data-handle="bottom-left" data-type="tArea" catchtouchstart="handleResizeStart" catchtouchmove="handleResize" catchtouchend="handleResizeEnd"></view>
        <view class="resize-handle bottom-right" data-handle="bottom-right" data-type="tArea" catchtouchstart="handleResizeStart" catchtouchmove="handleResize" catchtouchend="handleResizeEnd"></view>
      </view>
    </view>

    <!-- 🔧 修复：参数处理Canvas只在有可预览视频时显示，不包括MJPEG流 -->
    <!-- 使用小程序旧版Canvas API，确保兼容性 -->
    <canvas
      wx:if="{{isParameterMode && (isLocalVideo || hasVideo)}}"
      canvas-id="parameterCanvas"
      class="parameter-canvas"
    />

    <!-- 任务状态显示 -->
    <view class="task-status-container" wx:if="{{taskStatus != 'idle' && taskStatus != 'completed'}}">
      <view class="task-status {{taskStatus}}">
        <text class="status-text">
          <block wx:if="{{taskStatus == 'creating'}}">正在创建分析任务...</block>
          <block wx:elif="{{taskStatus == 'processing'}}">视频分析处理中...</block>
          <block wx:elif="{{taskStatus == 'completed'}}">分析已完成</block>
          <block wx:elif="{{taskStatus == 'failed'}}">分析失败</block>
          <block wx:elif="{{taskStatus == 'timeout'}}">分析超时，请稍后查看</block>
        </text>
        <view class="status-indicator" wx:if="{{taskStatus == 'processing'}}"></view>
      </view>
    </view>
  </view>

  <!-- 控制按钮区域 - 在参数模式时隐藏 -->
  <view class="{{buttonGridClass || 'button-grid'}} {{isParameterMode ? 'button-grid-hidden' : ''}}" hidden="{{isParameterMode}}">
    <view class="button-row">
      <button class="grid-button {{isReceivingVideo ? 'disconnect-video' : ''}}" hover-class="{{isReceivingVideo ? 'grid-button-active' : 'none'}}" hover-stay-time="0" bindtap="toggleVideoStream">
        {{isReceivingVideo ? '断开接收视频' : '开始接收视频'}}
      </button>
      <button class="grid-button" hover-class="none" hover-stay-time="0" bindtap="onAnalyzeVideo">开始分析视频</button>
    </view>
    <view class="button-row">
      <button class="grid-button record-video-button {{isRecording ? 'recording' : ''}}" hover-class="{{isRecording ? 'grid-button-active' : 'none'}}" hover-stay-time="0" bindtap="startRecordVideo">
        {{isRecording ? '停止录制' : '开始录制'}}
      </button>
      <button class="grid-button" hover-class="none" hover-stay-time="0" bindtap="uploadLocalVideo">本地上传视频</button>
    </view>
    <view class="button-row">
      <button class="grid-button {{disableModeSwitching ? 'disabled-button' : ''}}" hover-class="{{disableModeSwitching ? '' : 'none'}}" hover-stay-time="0" bindtap="toggleParameterMode" disabled="{{disableModeSwitching}}">{{isParameterMode ? '返回默认模式' : '进入参数模式'}}</button>
      <button class="grid-button {{canReset ? '' : 'disabled-button'}}" hover-class="{{canReset ? 'none' : ''}}" hover-stay-time="0" bindtap="resetToLiveStream" disabled="{{!canReset}}">重置连接</button>
    </view>
    <!-- 🌈 彩虹按钮已注释 - 可通过移除注释重新启用 -->
    <!--
    <view class="button-row">
      <button class="grid-button disconnect-video" hover-class="grid-button-active" hover-stay-time="0" bindtap="testDeviceEndpoints">🧪 测试设备端点</button>
      <button class="grid-button disconnect-video" hover-class="grid-button-active" hover-stay-time="0" bindtap="diagnoseStreamConnection">🔍 诊断Stream连接</button>
    </view>
    <view class="button-row">
      <button class="grid-button disconnect-video" hover-class="grid-button-active" hover-stay-time="0" bindtap="testStreamFormats">📺 测试视频流格式</button>
      <button class="grid-button disconnect-video" hover-class="grid-button-active" hover-stay-time="0" bindtap="diagnoseNetwork">🔍 网络环境诊断</button>
    </view>
    -->
  </view>

  <!-- 视频上传进度面板 -->
  <view class="progress-panel upload-progress-panel" wx:if="{{uploadProgressVisible}}">
    <view class="progress-header">
      <text class="progress-title">{{uploadProgress.title}}</text>
      <text class="progress-percent">{{uploadProgress.percent}}%</text>
    </view>

    <view class="progress-bar-container">
      <view class="progress-bar upload-progress-style" style="width: {{uploadProgress.percent}}%;"></view>
    </view>

    <view class="progress-details">
      <view class="progress-item">
        <view class="item-label">当前阶段：</view>
        <view class="item-value">{{uploadProgress.stage}}</view>
      </view>



      <view class="progress-item" wx:if="{{uploadProgress.timeRemaining}}">
        <view class="item-label">预计剩余：</view>
        <view class="item-value">{{uploadProgress.timeRemaining}}</view>
      </view>

      <view class="progress-item" wx:if="{{uploadProgress.fileSize}}">
        <view class="item-label">文件大小：</view>
        <view class="item-value">{{uploadProgress.fileSize}}</view>
      </view>
    </view>

    <view class="progress-actions" wx:if="{{uploadProgress.showCancel}}">
      <button class="cancel-btn" bindtap="cancelUpload" hover-class="cancel-btn-hover">取消上传</button>
    </view>
  </view>

  <!-- 视频分析进度面板 -->
  <view class="progress-panel" wx:if="{{analysisProgressVisible}}">
    <view class="progress-header">
      <text class="progress-title">视频分析进度</text>
      <text class="progress-percent">{{analysisProgress.percent}}%</text>
    </view>
    
    <view class="progress-bar-container">
      <view class="progress-bar progress-style" style="width: {{analysisProgress.percent}}%;"></view>
    </view>
    
    <view class="progress-details">
      <view class="progress-item">
        <view class="item-label">当前阶段：</view>
        <view class="item-value">{{analysisProgress.stage}}</view>
      </view>
      
      <view class="progress-item">
        <view class="item-label">详细信息：</view>
        <view class="item-value">{{analysisProgress.details}}</view>
      </view>
      
      <view class="progress-item">
        <view class="item-label">剩余时间：</view>
        <view class="item-value">{{analysisProgress.timeRemaining}}</view>
      </view>
      
      <view class="progress-item" wx:if="{{analysisProgress.totalFrames > 0}}">
        <view class="item-label">已分析帧数：</view>
        <view class="item-value">{{analysisProgress.analyzedFrames}} / {{analysisProgress.totalFrames}}</view>
      </view>
    </view>
    
    <view class="progress-actions">
      <button class="cancel-btn" bindtap="cancelAnalysis" hover-class="cancel-btn-hover">取消分析</button>
    </view>
  </view>

  <!-- 内容区域 -->
  <view class="content-area">
    <!-- 默认模式 - 使用hidden属性而不是wx:if，避免元素的销毁和重建导致闪烁 -->
    <view class="{{defaultModeClass || 'default-mode'}}" hidden="{{isParameterMode || isSearching}}">
      <!-- 图片显示区 -->
      <view class="default-images">
        <view class="image-row">
          <!-- C区图片区域包裹 -->
          <view class="image-area-wrapper">
            <view class="image-container" 
                  hover-class="image-hover">
              <view class="image-content">
                <block wx:if="{{!cAreaImage}}">
                  <view class="empty-indicator">i</view>
                </block>
                <block wx:else>
                  <image src="{{cAreaImage}}" mode="aspectFit" class="analysis-image" binderror="handleImageError" data-type="cArea" bindtap="previewCAreaImage" />
                </block>
              </view>
            </view>
            <!-- 将标签容器移到图片容器外部 -->
            <view class="image-label-container">
              <view class="image-label">C 区</view>
            </view>
          </view>
          
          <!-- T区图片区域包裹 -->
          <view class="image-area-wrapper">
            <view class="image-container" 
                  hover-class="image-hover">
              <view class="image-content">
                <block wx:if="{{!tAreaImage}}">
                  <view class="empty-indicator">i</view>
                </block>
                <block wx:else>
                  <image src="{{tAreaImage}}" mode="aspectFit" class="analysis-image" binderror="handleImageError" data-type="tArea" bindtap="previewTAreaImage" />
                </block>
              </view>
            </view>
            <!-- 将标签容器移到图片容器外部 -->
            <view class="image-label-container">
              <view class="image-label">T 区</view>
            </view>
          </view>
        </view>
      </view>

      <!-- ECL值显示区域 -->
      <view class="ecl-section">
        <!-- 添加顶部光泽效果 -->
        <view class="ecl-top-gloss"></view>
        <view class="ecl-label">ECL值</view>
        <view class="ecl-values">
          <view class="value-item">
            <text class="value-label">C区：</text>
            <text class="value-text {{!cAreaValue ? 'loading-text' : ''}}">{{cAreaValue || '等待分析...'}}</text>
          </view>
          <view class="value-item">
            <text class="value-label">T区：</text>
            <text class="value-text {{!tAreaValue ? 'loading-text' : ''}}">{{tAreaValue || '等待分析...'}}</text>
          </view>
          <view class="value-item">
            <text class="value-label">T/C：</text>
            <text class="value-text {{!tcValue ? 'loading-text' : ''}}">{{tcValue || '等待分析...'}}</text>
          </view>
        </view>
        <!-- 添加底部平滑过渡元素 -->
        <view class="ecl-bottom-fade"></view>
      </view>
    </view>

    <!-- 参数模式 - 使用hidden属性替代wx:if，避免DOM重建导致闪烁 -->
    <view class="{{parameterModeClass || 'parameter-mode'}} {{isParameterMode ? 'parameter-mode-expanded' : ''}}" hidden="{{!isParameterMode || isSearching}}">
      <!-- 参数调节状态提示 -->
      <view class="parameter-status-tip" wx:if="{{!isReceivingVideo || !deviceIpAddress}}">
        <text class="status-text">⚠️ 请先点击"开始接收视频"按钮连接设备后，才能调节摄像头参数</text>
      </view>
      <view class="parameter-status-tip success" wx:if="{{isReceivingVideo && deviceIpAddress}}">
        <text class="status-text">✅ 设备已连接，可调节摄像头参数 (IP: {{deviceIpAddress}})</text>
      </view>

      <scroll-view
        scroll-y
        class="parameter-list {{parameterListAnimation}}"
        enhanced="{{true}}"
        show-scrollbar="{{true}}"
        fast-deceleration="{{true}}"
        bounce="{{false}}"
        enable-back-to-top="{{true}}"
        scroll-anchoring="{{true}}">
        <!-- 浓度输入 -->
        <view class="parameter-item">
          <view class="parameter-label">[LgE] 浓度设置</view>
          <view class="concentration-container">
            <!-- 快捷选择按钮 -->
            <view class="quick-select">
              <text class="quick-select-label">快捷选择:</text>
              <view class="quick-select-buttons">
                <text class="quick-btn" bindtap="setConcentration" data-value="100">100</text>
                <text class="quick-btn" bindtap="setConcentration" data-value="200">200</text>
                <text class="quick-btn" bindtap="setConcentration" data-value="500">500</text>
                <text class="quick-btn" bindtap="setConcentration" data-value="1000">1000</text>
              </view>
            </view>
            <!-- 手动输入区域 -->
            <view class="concentration-input-area">
              <input type="digit" 
                class="concentration-input" 
                value="{{concentration}}" 
                bindinput="onConcentrationInput" 
                placeholder="请输入浓度值(0-1000)"/>
              <text class="unit-text">ng mL⁻¹</text>
            </view>
          </view>
        </view>
        <!-- 亮度控制 -->
        <view class="parameter-item">
          <view class="parameter-label">亮度</view>
          <view class="parameter-control">
            <view class="minus-btn" bindtap="adjustParameter" data-type="brightness" data-action="minus">-</view>
            <slider class="parameter-slider" min="0" max="240" value="{{brightness}}" 
              block-size="22" 
              activeColor="#4a90e2" 
              backgroundColor="#e0e5ec"
              bindchange="onParameterChange" 
              data-type="brightness"
              show-value="{{false}}"
            />
            <view class="input-animation-wrapper" id="wrapper-brightness">
              <view class="current-value-container {{brightnessContainerAnimation ? 'animated' : ''}}" 
                    bindtap="showValueInput" 
                    data-type="brightness" 
                    hidden="{{editingParameter === 'brightness'}}"
                    animation="{{brightnessContainerAnimation}}">
                <text class="current-value">{{brightness}}</text>
              </view>
              <text class="edit-hint">点击编辑</text>
              <input class="current-value-input" 
                    type="number" 
                    value="{{brightness}}" 
                    focus="{{brightnessInputFocus}}"
                    bindinput="onValueInput" 
                    bindblur="hideValueInput" 
                    data-type="brightness"
                    hidden="{{editingParameter !== 'brightness'}}" />
            </view>
            <view class="plus-btn" bindtap="adjustParameter" data-type="brightness" data-action="plus">+</view>
          </view>
        </view>

        <!-- 对比度控制 -->
        <view class="parameter-item">
          <view class="parameter-label">对比度</view>
          <view class="parameter-control">
            <view class="minus-btn" bindtap="adjustParameter" data-type="contrast" data-action="minus">-</view>
            <slider class="parameter-slider" min="0" max="255" value="{{contrast}}" 
              block-size="22" 
              activeColor="#4a90e2" 
              backgroundColor="#e0e5ec"
              bindchange="onParameterChange" 
              data-type="contrast"
              show-value="{{false}}"
            />
            <view class="input-animation-wrapper" id="wrapper-contrast">
              <view class="current-value-container {{contrastContainerAnimation ? 'animated' : ''}}" 
                    bindtap="showValueInput" 
                    data-type="contrast" 
                    hidden="{{editingParameter === 'contrast'}}"
                    animation="{{contrastContainerAnimation}}">
                <text class="current-value">{{contrast}}</text>
              </view>
              <text class="edit-hint">点击编辑</text>
              <input class="current-value-input" 
                    type="number" 
                    value="{{contrast}}" 
                    focus="{{contrastInputFocus}}"
                    bindinput="onValueInput" 
                    bindblur="hideValueInput" 
                    data-type="contrast"
                    hidden="{{editingParameter !== 'contrast'}}" />
            </view>
            <view class="plus-btn" bindtap="adjustParameter" data-type="contrast" data-action="plus">+</view>
          </view>
        </view>

        <!-- 饱和度控制 -->
        <view class="parameter-item">
          <view class="parameter-label">饱和度</view>
          <view class="parameter-control">
            <view class="minus-btn" bindtap="adjustParameter" data-type="saturation" data-action="minus">-</view>
            <slider class="parameter-slider" min="0" max="255" value="{{saturation}}" 
              block-size="22" 
              activeColor="#4a90e2" 
              backgroundColor="#e0e5ec"
              bindchange="onParameterChange" 
              data-type="saturation"
              show-value="{{false}}"
            />
            <view class="input-animation-wrapper" id="wrapper-saturation">
              <view class="current-value-container {{saturationContainerAnimation ? 'animated' : ''}}" 
                    bindtap="showValueInput" 
                    data-type="saturation" 
                    hidden="{{editingParameter === 'saturation'}}"
                    animation="{{saturationContainerAnimation}}">
                <text class="current-value">{{saturation}}</text>
              </view>
              <text class="edit-hint">点击编辑</text>
              <input class="current-value-input" 
                    type="number" 
                    value="{{saturation}}" 
                    focus="{{saturationInputFocus}}"
                    bindinput="onValueInput" 
                    bindblur="hideValueInput" 
                    data-type="saturation"
                    hidden="{{editingParameter !== 'saturation'}}" />
            </view>
            <view class="plus-btn" bindtap="adjustParameter" data-type="saturation" data-action="plus">+</view>
          </view>
        </view>

        <!-- 自动白平衡控制 -->
        <view class="parameter-item">
          <view class="parameter-label">自动白平衡</view>
          <view class="parameter-control">
            <view class="minus-btn" bindtap="adjustParameter" data-type="white_balance_temperature_auto" data-action="minus">-</view>
            <slider class="parameter-slider" min="0" max="1" value="{{white_balance_temperature_auto}}" 
              block-size="22" 
              activeColor="#4a90e2" 
              backgroundColor="#e0e5ec"
              bindchange="onParameterChange" 
              data-type="white_balance_temperature_auto"
              show-value="{{false}}"
            />
            <view class="input-animation-wrapper" id="wrapper-white_balance_temperature_auto">
              <view class="current-value-container {{white_balance_temperature_autoContainerAnimation ? 'animated' : ''}}" 
                    bindtap="showValueInput" 
                    data-type="white_balance_temperature_auto" 
                    hidden="{{editingParameter === 'white_balance_temperature_auto'}}"
                    animation="{{white_balance_temperature_autoContainerAnimation}}">
                <text class="current-value">{{white_balance_temperature_auto}}</text>
              </view>
              <text class="edit-hint">点击编辑</text>
              <input class="current-value-input" 
                    type="number" 
                    value="{{white_balance_temperature_auto}}" 
                    focus="{{white_balance_temperature_autoInputFocus}}"
                    bindinput="onValueInput" 
                    bindblur="hideValueInput" 
                    data-type="white_balance_temperature_auto"
                    hidden="{{editingParameter !== 'white_balance_temperature_auto'}}" />
            </view>
            <view class="plus-btn" bindtap="adjustParameter" data-type="white_balance_temperature_auto" data-action="plus">+</view>
          </view>
        </view>

        <!-- 增益控制 -->
        <view class="parameter-item">
          <view class="parameter-label">增益</view>
          <view class="parameter-control">
            <view class="minus-btn" bindtap="adjustParameter" data-type="gain" data-action="minus">-</view>
            <slider class="parameter-slider" min="0" max="100" value="{{gain}}" 
              block-size="22" 
              activeColor="#4a90e2" 
              backgroundColor="#e0e5ec"
              bindchange="onParameterChange" 
              data-type="gain"
              show-value="{{false}}"
            />
            <view class="input-animation-wrapper" id="wrapper-gain">
              <view class="current-value-container {{gainContainerAnimation ? 'animated' : ''}}" 
                    bindtap="showValueInput" 
                    data-type="gain" 
                    hidden="{{editingParameter === 'gain'}}"
                    animation="{{gainContainerAnimation}}">
                <text class="current-value">{{gain}}</text>
              </view>
              <text class="edit-hint">点击编辑</text>
              <input class="current-value-input" 
                    type="number" 
                    value="{{gain}}" 
                    focus="{{gainInputFocus}}"
                    bindinput="onValueInput" 
                    bindblur="hideValueInput" 
                    data-type="gain"
                    hidden="{{editingParameter !== 'gain'}}" />
            </view>
            <view class="plus-btn" bindtap="adjustParameter" data-type="gain" data-action="plus">+</view>
          </view>
        </view>

        <!-- 电力线频率控制 -->
        <view class="parameter-item">
          <view class="parameter-label">电力线频率</view>
          <view class="parameter-control">
            <view class="minus-btn" bindtap="adjustParameter" data-type="power_line_frequency" data-action="minus">-</view>
            <slider class="parameter-slider" min="0" max="2" value="{{power_line_frequency}}" 
              block-size="22" 
              activeColor="#4a90e2" 
              backgroundColor="#e0e5ec"
              bindchange="onParameterChange" 
              data-type="power_line_frequency"
              show-value="{{false}}"
            />
            <view class="input-animation-wrapper" id="wrapper-power_line_frequency">
              <view class="current-value-container {{power_line_frequencyContainerAnimation ? 'animated' : ''}}" 
                    bindtap="showValueInput" 
                    data-type="power_line_frequency" 
                    hidden="{{editingParameter === 'power_line_frequency'}}"
                    animation="{{power_line_frequencyContainerAnimation}}">
                <text class="current-value">{{power_line_frequency}}</text>
              </view>
              <text class="edit-hint">点击编辑</text>
              <input class="current-value-input" 
                    type="number" 
                    value="{{power_line_frequency}}" 
                    focus="{{power_line_frequencyInputFocus}}"
                    bindinput="onValueInput" 
                    bindblur="hideValueInput" 
                    data-type="power_line_frequency"
                    hidden="{{editingParameter !== 'power_line_frequency'}}" />
            </view>
            <view class="plus-btn" bindtap="adjustParameter" data-type="power_line_frequency" data-action="plus">+</view>
          </view>
        </view>

        <!-- 白平衡温度控制 -->
        <view class="parameter-item">
          <view class="parameter-label">白平衡</view>
          <view class="parameter-control">
            <view class="minus-btn" bindtap="adjustParameter" data-type="white_balance_temperature" data-action="minus">-</view>
            <slider class="parameter-slider" min="2600" max="6500" value="{{white_balance_temperature}}" 
              block-size="22" 
              activeColor="#4a90e2" 
              backgroundColor="#e0e5ec"
              bindchange="onParameterChange" 
              data-type="white_balance_temperature"
              show-value="{{false}}"
            />
            <view class="input-animation-wrapper" id="wrapper-white_balance_temperature">
              <view class="current-value-container {{white_balance_temperatureContainerAnimation ? 'animated' : ''}}" 
                    bindtap="showValueInput" 
                    data-type="white_balance_temperature" 
                    hidden="{{editingParameter === 'white_balance_temperature'}}"
                    animation="{{white_balance_temperatureContainerAnimation}}">
                <text class="current-value">{{white_balance_temperature}}</text>
              </view>
              <text class="edit-hint">点击编辑</text>
              <input class="current-value-input" 
                    type="number" 
                    value="{{white_balance_temperature}}" 
                    focus="{{white_balance_temperatureInputFocus}}"
                    bindinput="onValueInput" 
                    bindblur="hideValueInput" 
                    data-type="white_balance_temperature"
                    hidden="{{editingParameter !== 'white_balance_temperature'}}" />
            </view>
            <view class="plus-btn" bindtap="adjustParameter" data-type="white_balance_temperature" data-action="plus">+</view>
          </view>
        </view>

        <!-- 清晰度控制 -->
        <view class="parameter-item">
          <view class="parameter-label">清晰度</view>
          <view class="parameter-control">
            <view class="minus-btn" bindtap="adjustParameter" data-type="sharpness" data-action="minus">-</view>
            <slider class="parameter-slider" min="0" max="255" value="{{sharpness}}" 
              block-size="22" 
              activeColor="#4a90e2" 
              backgroundColor="#e0e5ec"
              bindchange="onParameterChange" 
              data-type="sharpness"
              show-value="{{false}}"
            />
            <view class="input-animation-wrapper" id="wrapper-sharpness">
              <view class="current-value-container {{sharpnessContainerAnimation ? 'animated' : ''}}" 
                    bindtap="showValueInput" 
                    data-type="sharpness" 
                    hidden="{{editingParameter === 'sharpness'}}"
                    animation="{{sharpnessContainerAnimation}}">
                <text class="current-value">{{sharpness}}</text>
              </view>
              <text class="edit-hint">点击编辑</text>
              <input class="current-value-input" 
                    type="number" 
                    value="{{sharpness}}" 
                    focus="{{sharpnessInputFocus}}"
                    bindinput="onValueInput" 
                    bindblur="hideValueInput" 
                    data-type="sharpness"
                    hidden="{{editingParameter !== 'sharpness'}}" />
            </view>
            <view class="plus-btn" bindtap="adjustParameter" data-type="sharpness" data-action="plus">+</view>
          </view>
        </view>

        <!-- 自动曝光控制 -->
        <view class="parameter-item">
          <view class="parameter-label">自动曝光</view>
          <view class="parameter-control">
            <view class="minus-btn" bindtap="adjustParameter" data-type="exposure_auto" data-action="minus">-</view>
            <slider class="parameter-slider" min="0" max="3" value="{{exposure_auto}}" 
              block-size="22" 
              activeColor="#4a90e2" 
              backgroundColor="#e0e5ec"
              bindchange="onParameterChange" 
              data-type="exposure_auto"
              show-value="{{false}}"
            />
            <view class="input-animation-wrapper" id="wrapper-exposure_auto">
              <view class="current-value-container {{exposure_autoContainerAnimation ? 'animated' : ''}}" 
                    bindtap="showValueInput" 
                    data-type="exposure_auto" 
                    hidden="{{editingParameter === 'exposure_auto'}}"
                    animation="{{exposure_autoContainerAnimation}}">
                <text class="current-value">{{exposure_auto}}</text>
              </view>
              <text class="edit-hint">点击编辑</text>
              <input class="current-value-input" 
                    type="number" 
                    value="{{exposure_auto}}" 
                    focus="{{exposure_autoInputFocus}}"
                    bindinput="onValueInput" 
                    bindblur="hideValueInput" 
                    data-type="exposure_auto"
                    hidden="{{editingParameter !== 'exposure_auto'}}" />
            </view>
            <view class="plus-btn" bindtap="adjustParameter" data-type="exposure_auto" data-action="plus">+</view>
          </view>
        </view>

        <!-- 缩放控制 -->
        <view class="parameter-item">
          <view class="parameter-label">缩放</view>
          <view class="parameter-control">
            <view class="minus-btn" bindtap="adjustParameter" data-type="zoom_absolute" data-action="minus">-</view>
            <slider class="parameter-slider" min="100" max="190" value="{{zoom_absolute}}" 
              block-size="22" 
              activeColor="#4a90e2" 
              backgroundColor="#e0e5ec"
              bindchange="onParameterChange" 
              data-type="zoom_absolute"
              show-value="{{false}}"
            />
            <view class="input-animation-wrapper" id="wrapper-zoom_absolute">
              <view class="current-value-container {{zoom_absoluteContainerAnimation ? 'animated' : ''}}" 
                    bindtap="showValueInput" 
                    data-type="zoom_absolute" 
                    hidden="{{editingParameter === 'zoom_absolute'}}"
                    animation="{{zoom_absoluteContainerAnimation}}">
                <text class="current-value">{{zoom_absolute}}</text>
              </view>
              <text class="edit-hint">点击编辑</text>
              <input class="current-value-input" 
                    type="number" 
                    value="{{zoom_absolute}}" 
                    focus="{{zoom_absoluteInputFocus}}"
                    bindinput="onValueInput" 
                    bindblur="hideValueInput" 
                    data-type="zoom_absolute"
                    hidden="{{editingParameter !== 'zoom_absolute'}}" />
            </view>
            <view class="plus-btn" bindtap="adjustParameter" data-type="zoom_absolute" data-action="plus">+</view>
          </view>
        </view>

        <!-- 曝光值控制 -->
        <view class="parameter-item">
          <view class="parameter-label">曝光值</view>
          <view class="parameter-control">
            <view class="minus-btn" bindtap="adjustParameter" data-type="exposure_absolute" data-action="minus">-</view>
            <slider class="parameter-slider" min="5" max="2500" value="{{exposure_absolute}}" 
              block-size="22" 
              activeColor="#4a90e2" 
              backgroundColor="#e0e5ec"
              bindchange="onParameterChange" 
              data-type="exposure_absolute"
              show-value="{{false}}"
            />
            <view class="input-animation-wrapper" id="wrapper-exposure_absolute">
              <view class="current-value-container {{exposure_absoluteContainerAnimation ? 'animated' : ''}}" 
                    bindtap="showValueInput" 
                    data-type="exposure_absolute" 
                    hidden="{{editingParameter === 'exposure_absolute'}}"
                    animation="{{exposure_absoluteContainerAnimation}}">
                <text class="current-value">{{exposure_absolute}}</text>
              </view>
              <text class="edit-hint">点击编辑</text>
              <input class="current-value-input" 
                    type="number" 
                    value="{{exposure_absolute}}" 
                    focus="{{exposure_absoluteInputFocus}}"
                    bindinput="onValueInput" 
                    bindblur="hideValueInput" 
                    data-type="exposure_absolute"
                    hidden="{{editingParameter !== 'exposure_absolute'}}" />
            </view>
            <view class="plus-btn" bindtap="adjustParameter" data-type="exposure_absolute" data-action="plus">+</view>
          </view>
        </view>
        <!-- 摄像头水平转动控制 -->
        <view class="parameter-item">
          <view class="parameter-label">摄像头水平转动</view>
          <view class="parameter-control">
            <view class="minus-btn" bindtap="adjustParameter" data-type="pan_absolute" data-action="minus">-</view>
            <slider class="parameter-slider" min="-36000" max="36000" value="{{pan_absolute}}" 
              block-size="22" 
              activeColor="#4a90e2" 
              backgroundColor="#e0e5ec"
              bindchange="onParameterChange" 
              data-type="pan_absolute"
              show-value="{{false}}"
            />
            <view class="input-animation-wrapper" id="wrapper-pan_absolute">
              <view class="current-value-container {{pan_absoluteContainerAnimation ? 'animated' : ''}}" 
                    bindtap="showValueInput" 
                    data-type="pan_absolute" 
                    hidden="{{editingParameter === 'pan_absolute'}}"
                    animation="{{pan_absoluteContainerAnimation}}">
                <text class="current-value">{{pan_absolute}}</text>
              </view>
              <text class="edit-hint">点击编辑</text>
              <input class="current-value-input" 
                    type="number" 
                    value="{{pan_absolute}}" 
                    focus="{{pan_absoluteInputFocus}}"
                    bindinput="onValueInput" 
                    bindblur="hideValueInput" 
                    data-type="pan_absolute"
                    hidden="{{editingParameter !== 'pan_absolute'}}" />
            </view>
            <view class="plus-btn" bindtap="adjustParameter" data-type="pan_absolute" data-action="plus">+</view>
          </view>
        </view>

        <!-- 摄像头垂直转动控制 -->
        <view class="parameter-item">
          <view class="parameter-label">摄像头垂直转动</view>
          <view class="parameter-control">
            <view class="minus-btn" bindtap="adjustParameter" data-type="tilt_absolute" data-action="minus">-</view>
            <slider class="parameter-slider" min="-36000" max="36000" value="{{tilt_absolute}}" 
              block-size="22" 
              activeColor="#4a90e2" 
              backgroundColor="#e0e5ec"
              bindchange="onParameterChange" 
              data-type="tilt_absolute"
              show-value="{{false}}"
            />
            <view class="input-animation-wrapper" id="wrapper-tilt_absolute">
              <view class="current-value-container {{tilt_absoluteContainerAnimation ? 'animated' : ''}}" 
                    bindtap="showValueInput" 
                    data-type="tilt_absolute" 
                    hidden="{{editingParameter === 'tilt_absolute'}}"
                    animation="{{tilt_absoluteContainerAnimation}}">
                <text class="current-value">{{tilt_absolute}}</text>
              </view>
              <text class="edit-hint">点击编辑</text>
              <input class="current-value-input" 
                    type="number" 
                    value="{{tilt_absolute}}" 
                    focus="{{tilt_absoluteInputFocus}}"
                    bindinput="onValueInput" 
                    bindblur="hideValueInput" 
                    data-type="tilt_absolute"
                    hidden="{{editingParameter !== 'tilt_absolute'}}" />
            </view>
            <view class="plus-btn" bindtap="adjustParameter" data-type="tilt_absolute" data-action="plus">+</view>
          </view>
        </view>

        <!-- 摄像头焦点移动控制 -->
        <view class="parameter-item">
          <view class="parameter-label">摄像头焦点移动</view>
          <view class="parameter-control">
            <view class="minus-btn" bindtap="adjustParameter" data-type="focus_absolute" data-action="minus">-</view>
            <slider class="parameter-slider" min="0" max="255" value="{{focus_absolute}}" 
              block-size="22" 
              activeColor="#4a90e2" 
              backgroundColor="#e0e5ec"
              bindchange="onParameterChange" 
              data-type="focus_absolute"
              show-value="{{false}}"
            />
            <view class="input-animation-wrapper" id="wrapper-focus_absolute">
              <view class="current-value-container {{focus_absoluteContainerAnimation ? 'animated' : ''}}" 
                    bindtap="showValueInput" 
                    data-type="focus_absolute" 
                    hidden="{{editingParameter === 'focus_absolute'}}"
                    animation="{{focus_absoluteContainerAnimation}}">
                <text class="current-value">{{focus_absolute}}</text>
              </view>
              <text class="edit-hint">点击编辑</text>
              <input class="current-value-input" 
                    type="number" 
                    value="{{focus_absolute}}" 
                    focus="{{focus_absoluteInputFocus}}"
                    bindinput="onValueInput" 
                    bindblur="hideValueInput" 
                    data-type="focus_absolute"
                    hidden="{{editingParameter !== 'focus_absolute'}}" />
            </view>
            <view class="plus-btn" bindtap="adjustParameter" data-type="focus_absolute" data-action="plus">+</view>
          </view>
        </view>

        <!-- 摄像头移动速度控制 -->
        <view class="parameter-item">
          <view class="parameter-label">摄像头移动速度</view>
          <view class="parameter-control">
            <view class="minus-btn" bindtap="adjustParameter" data-type="camera_move_speed" data-action="minus">-</view>
            <slider class="parameter-slider" min="100" max="190" value="{{camera_move_speed}}" 
              block-size="22" 
              activeColor="#4a90e2" 
              backgroundColor="#e0e5ec"
              bindchange="onParameterChange" 
              data-type="camera_move_speed"
              show-value="{{false}}"
            />
            <view class="input-animation-wrapper" id="wrapper-camera_move_speed">
              <view class="current-value-container {{camera_move_speedContainerAnimation ? 'animated' : ''}}" 
                    bindtap="showValueInput" 
                    data-type="camera_move_speed" 
                    hidden="{{editingParameter === 'camera_move_speed'}}"
                    animation="{{camera_move_speedContainerAnimation}}">
                <text class="current-value">{{camera_move_speed}}</text>
              </view>
              <text class="edit-hint">点击编辑</text>
              <input class="current-value-input" 
                    type="number" 
                    value="{{camera_move_speed}}" 
                    focus="{{camera_move_speedInputFocus}}"
                    bindinput="onValueInput" 
                    bindblur="hideValueInput" 
                    data-type="camera_move_speed"
                    hidden="{{editingParameter !== 'camera_move_speed'}}" />
            </view>
            <view class="plus-btn" bindtap="adjustParameter" data-type="camera_move_speed" data-action="plus">+</view>
          </view>
        </view>

        <!-- Voltage控制 -->
        <view class="parameter-item">
          <view class="parameter-label">电压 (5~20 V)</view>
          <view class="parameter-control">
            <view class="minus-btn" bindtap="adjustParameter" data-type="setVoltage" data-action="minus">-</view>
            <slider class="parameter-slider" min="5000" max="20000" value="{{setVoltage}}" 
              block-size="22" 
              activeColor="#4a90e2" 
              backgroundColor="#e0e5ec"
              bindchange="onParameterChange" 
              data-type="setVoltage"
              show-value="{{false}}"
            />
            <view class="input-animation-wrapper" id="wrapper-setVoltage">
              <view class="current-value-container {{setVoltageContainerAnimation ? 'animated' : ''}}" 
                    bindtap="showValueInput" 
                    data-type="setVoltage" 
                    hidden="{{editingParameter === 'setVoltage'}}"
                    animation="{{setVoltageContainerAnimation}}">
                <text class="current-value">{{setVoltage}}</text>
              </view>
              <text class="edit-hint">点击编辑</text>
              <input class="current-value-input" 
                    type="number" 
                    value="{{setVoltage}}" 
                    focus="{{setVoltageInputFocus}}"
                    bindinput="onValueInput" 
                    bindblur="hideValueInput" 
                    data-type="setVoltage"
                    hidden="{{editingParameter !== 'setVoltage'}}" />
            </view>
            <view class="plus-btn" bindtap="adjustParameter" data-type="setVoltage" data-action="plus">+</view>
          </view>
        </view>
      </scroll-view>

      <!-- 底部按钮 -->
      <view class="parameter-buttons">
        <button class="reset-btn" bindtap="resetParameters">重置参数</button>
        <button class="save-btn" bindtap="saveParameterSettings">保存设置</button>
      </view>
    </view>

    <!-- 查询模式 -->
    <view class="search-mode" wx:if="{{isSearching}}">
      <scroll-view scroll-y class="device-list" enhanced show-scrollbar="true">
        <view class="device-item" wx:for="{{deviceList}}" wx:key="id" bindtap="navigateToDeviceDetail" data-id="{{item.id}}">
          <image src="{{item.image}}" mode="aspectFit"></image>
          <view class="device-info">
            <view class="info-line"><text class="info-label">设备名称：</text>{{item.name}}</view>
            <view class="info-line"><text class="info-label">型号：</text>{{item.model}}</view>
            <view class="info-line"><text class="info-label">MAC地址：</text>{{item.macAddress}}</view>
            <view class="info-line"><text class="info-label">序列号：</text>{{item.serialNumber}}</view>
            <view class="info-line"><text class="info-label">价格：</text>{{item.price}}</view>
            <view class="info-line"><text class="info-label">状态：</text>{{item.status}}</view>
          </view>
        </view>
      </scroll-view>
    </view>
  </view>
  </view>
</scroll-view>

  <!-- 重命名卡片 -->
  <view class="rename-mask {{showRenameCard ? 'show' : ''}}" bindtap="hideRenameCard" wx:if="{{!isParameterMode && showRenameCard}}">
    <view class="rename-card" catchtap="preventBubble">
      <view class="rename-title">重命名文件</view>
      <view class="rename-options">
        <view class="rename-option" bindtap="renameVideo">
          <image class="rename-icon" src="../../assets/img/video-icon.png" mode="aspectFit"></image>
          <text>视频</text>
        </view>
        <view class="rename-option" bindtap="renameImage">
          <image class="rename-icon" src="../../assets/img/image-icon.png" mode="aspectFit"></image>
          <text>图片</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 重命名输入框 -->
  <view class="rename-input-mask {{showRenameInput ? 'show' : ''}}" bindtap="hideRenameInput" wx:if="{{!isParameterMode && showRenameInput}}">
    <view class="rename-input-card" catchtap="preventBubble">
      <view class="rename-input-title">
        {{currentRenameType === 'analysis' ? '分析命名' : 
          currentRenameType === 'video' ? '视频重命名' : '图片重命名'}}
      </view>
      <input class="rename-input"
        type="text"
        value="{{newFileName}}"
        bindinput="onFileNameInput"
        placeholder="{{currentRenameType === 'analysis' ? '请输入分析名称（可选）' : '请输入文件名'}}"
        focus="{{showRenameInput}}"
      />
      <view class="rename-buttons">
        <button class="rename-cancel" bindtap="hideRenameInput">
          {{currentRenameType === 'analysis' ? '使用时间戳' : '取消'}}
        </button>
        <button class="rename-confirm" bindtap="confirmRename">确定</button>
      </view>
    </view>
  </view>

  <!-- 视频分析状态 -->
  <!-- <view class="video-analysis-status" wx:if="{{showVideoAnalysisStatus}}">
    <view class="status-header">
      <image class="status-icon" src="../../assets/img/video-processing-icon.gif" mode="aspectFit"></image>
      <text class="status-title">视频分析中...</text>
    </view>
    <view class="status-content">
      <view class="progress-bar-container">
        <view class="progress-bar" style="width: {{analysisProgress}}%;"></view>
      </view>
      <text class="progress-text">{{analysisProgress}}% 完成</text>
      <text class="status-message">{{analysisStatusMessage}}</text>
    </view>
    <view class="button-row">
      <button class="cancel-analysis-btn" bindtap="cancelVideoAnalysis">取消分析</button>
    </view>
  </view> -->

    <!-- 结果展示卡片 -->
    <view class="result-card" wx:if="{{showResultCard}}">
      <!-- 结果展示内容 -->
    </view>

    <!-- 自定义底部导航栏 -->
    <view class="custom-tabbar">
  <view class="tab-item {{ currentTab === 'index' ? 'active' : '' }}" bindtap="switchTab" data-page="index">
    <view class="tab-icon-home">
      <view class="tab-icon-home-roof"></view>
      <view class="tab-icon-home-body"></view>
      <view class="home-door"></view>
      <view class="home-window"></view>
      <view class="home-chimney"></view>
    </view>
    <text class="tab-text">主界面</text>
    <view class="tab-item-indicator"></view>
  </view>
  <view class="tab-item {{ currentTab === 'addDevice' ? 'active' : '' }}" bindtap="switchTab" data-page="addDevice">
    <view class="tab-icon-add">
      <view class="tab-icon-add-case"></view>
      <view class="tab-icon-add-screen"></view>
      <view class="add-connector"></view>
      <view class="add-plus-horizontal"></view>
      <view class="add-plus-vertical"></view>
    </view>
    <text class="tab-text">设备录入</text>
    <view class="tab-item-indicator"></view>
  </view>
  <view class="tab-item {{ currentTab === 'me' ? 'active' : '' }}" bindtap="switchTab" data-page="me">
    <view class="tab-icon-me">
      <view class="tab-icon-me-head"></view>
      <view class="tab-icon-me-highlight"></view>
      <view class="me-ear-left"></view>
      <view class="me-ear-right"></view>
      <view class="me-arm-left"></view>
      <view class="me-arm-right"></view>
    </view>
      <text class="tab-text">我的</text>
      <view class="tab-item-indicator"></view>
    </view>
    </view>

    <!-- 录制时间选择器弹窗 -->
    <view wx:if="{{showRecordTimeSelector}}" class="record-time-selector-overlay" bindtap="hideRecordTimeSelector">
      <view class="record-time-selector-content" catchtap="preventBubble">
        <view class="selector-header">
          <text class="selector-title">选择录制时间</text>
          <text class="selector-subtitle">数据传输时间 = 录制时间 - 2秒</text>
        </view>

        <view class="time-display">
          <text class="time-label">录制时间：</text>
          <text class="time-value">{{selectedRecordTime}}秒</text>
        </view>

        <view class="time-display">
          <text class="time-label">数据传输：</text>
          <text class="time-value">{{selectedRecordTime - 2}}秒</text>
    </view>

    <view class="picker-container">
      <picker mode="selector" range="{{recordTimeOptions}}" value="{{selectedRecordTime - 5}}" bindchange="onRecordTimeChange">
        <view class="picker-display">
          <text>{{selectedRecordTime}}秒</text>
          <text class="picker-arrow">▼</text>
        </view>
      </picker>
    </view>

    <view class="selector-buttons">
      <button class="selector-btn cancel-btn" bindtap="hideRecordTimeSelector">取消</button>
      <button class="selector-btn confirm-btn" bindtap="confirmRecordTime">确认录制</button>
    </view>
  </view>
</view>

<!-- 图片放大遮罩层 -->
<view class="image-mask {{isEnlargedC || isEnlargedT ? 'show' : ''}}" bindtap="handleMaskClick">
  <!-- C区放大图片 -->
  <view class="image-container enlarged {{cAreaImageShow ? 'show' : ''}}" wx:if="{{isEnlargedC}}" bindtap="handleMaskClick">
    <view class="image-content">
      <image src="{{cAreaImage}}" mode="aspectFit" class="analysis-image" />
    </view>
    <view class="close-btn">×</view>
  </view>

  <!-- T区放大图片 -->
  <view class="image-container enlarged {{tAreaImageShow ? 'show' : ''}}" wx:if="{{isEnlargedT}}" bindtap="handleMaskClick">
    <view class="image-content">
      <image src="{{tAreaImage}}" mode="aspectFit" class="analysis-image" />
    </view>
    <view class="close-btn">×</view>
  </view>
</view>

<!-- Restart成功提示卡片 -->
<view class="restart-success-mask {{showRestartSuccess ? 'show' : ''}}" wx:if="{{showRestartSuccess}}">
  <view class="restart-success-card {{restartSuccessCardShow ? 'show' : ''}}" catchtap="preventBubble">
    <!-- 卡片头部 -->
    <view class="restart-success-header">
      <view class="restart-success-icon">
        <view class="success-checkmark">✓</view>
      </view>
      <view class="restart-success-title">设备连接成功</view>
    </view>

    <!-- 卡片内容 -->
    <view class="restart-success-content">
      <view class="restart-success-message">设备已成功激活并建立连接</view>
      <view class="restart-success-details">正在自动进入参数调节模式...</view>
    </view>

    <!-- 进度指示器 -->
    <view class="restart-success-progress">
      <view class="progress-bar-container">
        <view class="progress-bar-fill"></view>
      </view>
    </view>
  </view>
</view>

<!-- MCU Start激活提示卡片 -->
<view class="mcu-start-mask {{showMcuStart ? 'show' : ''}}" wx:if="{{showMcuStart}}">
  <view class="mcu-start-card {{mcuStartCardShow ? 'show' : ''}}" catchtap="preventBubble">
    <!-- 卡片头部 -->
    <view class="mcu-start-header">
      <view class="mcu-start-icon {{mcuStartSuccess ? 'success' : (mcuStartFailed ? 'failed' : 'preparing')}}">
        <view class="mcu-icon-content">
          <text wx:if="{{!mcuStartSuccess && !mcuStartFailed}}">⚡</text>
          <text wx:if="{{mcuStartSuccess}}">✓</text>
          <text wx:if="{{mcuStartFailed}}">✗</text>
        </view>
      </view>
      <view class="mcu-start-title">
        <text wx:if="{{!mcuStartSuccess && !mcuStartFailed}}">MCU Start准备激活</text>
        <text wx:if="{{mcuStartSuccess}}">MCU Start成功</text>
        <text wx:if="{{mcuStartFailed}}">MCU Start失败</text>
      </view>
    </view>

    <!-- 卡片内容 -->
    <view class="mcu-start-content">
      <view class="mcu-start-message">
        <text wx:if="{{!mcuStartSuccess && !mcuStartFailed}}">正在为检测仪器施加电压...</text>
        <text wx:if="{{mcuStartSuccess}}">仪器已成功激活，开始录制</text>
        <text wx:if="{{mcuStartFailed}}">仪器激活失败，录制已停止</text>
      </view>
      <view class="mcu-start-details">
        <text wx:if="{{!mcuStartSuccess && !mcuStartFailed}}">即将开始 {{selectedRecordTime}} 秒录制</text>
        <text wx:if="{{mcuStartSuccess}}">录制进行中，请保持设备稳定</text>
        <text wx:if="{{mcuStartFailed}}">请检查设备连接后重试</text>
      </view>
    </view>

    <!-- 进度指示器 -->
    <view class="mcu-start-progress" wx:if="{{!mcuStartFailed}}">
      <view class="progress-bar-container">
        <view class="progress-bar-fill {{mcuStartSuccess ? 'complete' : ''}}"></view>
      </view>
    </view>
  </view>
</view>

<!-- 录制倒计时卡片 -->
<view class="recording-countdown-mask {{showRecordingCountdown ? 'show' : ''}}" wx:if="{{showRecordingCountdown}}">
  <view class="recording-countdown-card {{recordingCountdownCardShow ? 'show' : ''}}" catchtap="preventBubble">
    <view class="countdown-display">{{recordingCountdown}}</view>
    <view class="countdown-label">录制中</view>
    <view class="countdown-progress">
      <view class="countdown-progress-bar" style="width: {{(selectedRecordTime - recordingCountdown) / selectedRecordTime * 100}}%"></view>
    </view>
  </view>
</view>

<!-- 上传成功提示卡片 -->
<view class="upload-success-mask {{showUploadSuccess ? 'show' : ''}}" wx:if="{{showUploadSuccess}}">
  <view class="upload-success-card {{uploadSuccessCardShow ? 'show' : ''}}" catchtap="preventBubble">
    <view class="upload-success-header">
      <view class="upload-success-icon">
        <view class="success-checkmark">✓</view>
      </view>
      <view class="upload-success-title">{{uploadSuccessTitle || '上传成功'}}</view>
    </view>
    <view class="upload-success-content">
      <view class="upload-success-message">{{uploadSuccessMessage || '视频已成功上传到云端'}}</view>
      <view class="upload-success-details">{{uploadSuccessDetails || '可以开始分析视频了'}}</view>
    </view>
  </view>
</view>

<!-- 上传失败提示卡片 -->
<view class="upload-failed-mask {{showUploadFailed ? 'show' : ''}}" wx:if="{{showUploadFailed}}">
  <view class="upload-failed-card {{uploadFailedCardShow ? 'show' : ''}}" catchtap="preventBubble">
    <view class="upload-failed-header">
      <view class="upload-failed-icon">
        <view class="failed-cross">✗</view>
      </view>
      <view class="upload-failed-title">上传失败</view>
    </view>
    <view class="upload-failed-content">
      <view class="upload-failed-message">{{uploadFailedMessage || '视频上传失败'}}</view>
      <view class="upload-failed-details">请检查网络连接后重试</view>
    </view>
  </view>
</view>

<!-- 本地上传成功提示卡片 -->
<view class="local-upload-success-mask {{showLocalUploadSuccess ? 'show' : ''}}" wx:if="{{showLocalUploadSuccess}}">
  <view class="local-upload-success-card {{localUploadSuccessCardShow ? 'show' : ''}}" catchtap="preventBubble">
    <view class="local-upload-success-header">
      <view class="local-upload-success-icon">
        <view class="success-checkmark">✓</view>
      </view>
      <view class="local-upload-success-title">本地上传成功</view>
    </view>
    <view class="local-upload-success-content">
      <view class="local-upload-success-message">本地视频已成功上传</view>
      <view class="local-upload-success-details">可以开始分析视频了</view>
    </view>
  </view>
</view>

<!-- AVI文件选择成功提示卡片 -->
<view class="avi-select-success-mask {{showAviSelectSuccess ? 'show' : ''}}" wx:if="{{showAviSelectSuccess}}">
  <view class="avi-select-success-card {{aviSelectSuccessCardShow ? 'show' : ''}}" catchtap="preventBubble">
    <view class="avi-select-success-header">
      <view class="avi-select-success-icon">
        <view class="file-select-icon">📁</view>
      </view>
      <view class="avi-select-success-title">{{aviSelectSuccessTitle || 'AVI文件选择成功'}}</view>
    </view>
    <view class="avi-select-success-content">
      <view class="avi-select-success-message">{{aviSelectSuccessMessage || '已成功选择AVI格式视频'}}</view>
      <view class="avi-select-success-details">{{aviSelectSuccessDetails || '准备上传并转换为MP4格式'}}</view>
    </view>
  </view>
</view>

<!-- 上传处理中提示卡片 -->
<view class="upload-processing-mask {{showUploadProcessing ? 'show' : ''}}" wx:if="{{showUploadProcessing}}">
  <view class="upload-processing-card {{uploadProcessingCardShow ? 'show' : ''}} {{uploadProcessingTransition}}" catchtap="preventBubble">
    <view class="upload-processing-header">
      <view class="upload-processing-icon">
        <view class="processing-spinner">⟳</view>
      </view>
      <view class="upload-processing-title">{{uploadProcessingTitle || '上传处理中'}}</view>
    </view>
    <view class="upload-processing-content">
      <view class="upload-processing-message">{{uploadProcessingMessage || '正在上传视频到云端...'}}</view>
      <view class="upload-processing-details">请稍候，不要关闭页面</view>
    </view>
    <view class="upload-processing-progress">
      <view class="progress-bar-container">
        <view class="progress-bar-fill processing-progress"></view>
      </view>
    </view>
  </view>
</view>

<!-- 分析准备中提示卡片 -->
<view class="analysis-preparing-mask {{showAnalysisPreparing ? 'show' : ''}}" wx:if="{{showAnalysisPreparing}}">
  <view class="analysis-preparing-card {{analysisPreparingCardShow ? 'show' : ''}}" catchtap="preventBubble">
    <view class="analysis-preparing-header">
      <view class="analysis-preparing-icon">
        <view class="preparing-icon">⚙️</view>
      </view>
      <view class="analysis-preparing-title">分析准备中</view>
    </view>
    <view class="analysis-preparing-content">
      <view class="analysis-preparing-message">正在准备视频分析...</view>
      <view class="analysis-preparing-details">请稍候，即将开始分析</view>
    </view>
    <view class="analysis-preparing-progress">
      <view class="progress-bar-container">
        <view class="progress-bar-fill preparing-progress"></view>
      </view>
    </view>
  </view>
</view>

<!-- 🆕 设备连接中卡片 -->
<view class="connecting-mask {{showConnecting ? 'show' : ''}}" wx:if="{{showConnecting}}">
  <view class="connecting-card {{connectingCardShow ? 'show' : ''}}" catchtap="preventBubble">
    <!-- 卡片头部 -->
    <view class="connecting-header">
      <view class="connecting-icon">
        <view class="connecting-spinner">⟳</view>
      </view>
      <view class="connecting-title">正在连接设备</view>
    </view>

    <!-- 卡片内容 -->
    <view class="connecting-content">
      <view class="connecting-message">正在尝试连接到设备...</view>
      <view class="connecting-details">请确保设备已开机并连接到同一网络</view>
    </view>

    <!-- 进度指示器 -->
    <view class="connecting-progress">
      <view class="progress-bar-container">
        <view class="progress-bar-fill"></view>
      </view>
    </view>
  </view>
</view>

<!-- 🆕 设备断开连接成功卡片 -->
<view class="disconnected-mask {{showDisconnected ? 'show' : ''}}" wx:if="{{showDisconnected}}">
  <view class="disconnected-card {{disconnectedCardShow ? 'show' : ''}}" catchtap="preventBubble">
    <!-- 卡片头部 -->
    <view class="disconnected-header">
      <view class="disconnected-icon">
        <view class="success-checkmark">✓</view>
      </view>
      <view class="disconnected-title">设备连接已断开</view>
    </view>

    <!-- 卡片内容 -->
    <view class="disconnected-content">
      <view class="disconnected-message">设备连接已成功断开</view>
      <view class="disconnected-details">所有监控和录制已停止</view>
    </view>
  </view>
  </view>