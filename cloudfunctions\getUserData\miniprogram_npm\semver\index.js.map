{"version": 3, "sources": ["semver.js"], "names": [], "mappings": ";;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.js", "sourcesContent": ["exports = module.exports = SemVer\n\nvar debug\n/* istanbul ignore next */\nif (typeof process === 'object' &&\n    process.env &&\n    process.env.NODE_DEBUG &&\n    /\\bsemver\\b/i.test(process.env.NODE_DEBUG)) {\n  debug = function () {\n    var args = Array.prototype.slice.call(arguments, 0)\n    args.unshift('SEMVER')\n    console.log.apply(console, args)\n  }\n} else {\n  debug = function () {}\n}\n\n// Note: this is the semver.org version of the spec that it implements\n// Not necessarily the package version of this code.\nexports.SEMVER_SPEC_VERSION = '2.0.0'\n\nvar MAX_LENGTH = 256\nvar MAX_SAFE_INTEGER = Number.MAX_SAFE_INTEGER ||\n  /* istanbul ignore next */ 9007199254740991\n\n// Max safe segment length for coercion.\nvar MAX_SAFE_COMPONENT_LENGTH = 16\n\nvar MAX_SAFE_BUILD_LENGTH = MAX_LENGTH - 6\n\n// The actual regexps go on exports.re\nvar re = exports.re = []\nvar safeRe = exports.safeRe = []\nvar src = exports.src = []\nvar R = 0\n\nvar LETTERDASHNUMBER = '[a-zA-Z0-9-]'\n\n// Replace some greedy regex tokens to prevent regex dos issues. These regex are\n// used internally via the safeRe object since all inputs in this library get\n// normalized first to trim and collapse all extra whitespace. The original\n// regexes are exported for userland consumption and lower level usage. A\n// future breaking change could export the safer regex only with a note that\n// all input should have extra whitespace removed.\nvar safeRegexReplacements = [\n  ['\\\\s', 1],\n  ['\\\\d', MAX_LENGTH],\n  [LETTERDASHNUMBER, MAX_SAFE_BUILD_LENGTH],\n]\n\nfunction makeSafeRe (value) {\n  for (var i = 0; i < safeRegexReplacements.length; i++) {\n    var token = safeRegexReplacements[i][0]\n    var max = safeRegexReplacements[i][1]\n    value = value\n      .split(token + '*').join(token + '{0,' + max + '}')\n      .split(token + '+').join(token + '{1,' + max + '}')\n  }\n  return value\n}\n\n// The following Regular Expressions can be used for tokenizing,\n// validating, and parsing SemVer version strings.\n\n// ## Numeric Identifier\n// A single `0`, or a non-zero digit followed by zero or more digits.\n\nvar NUMERICIDENTIFIER = R++\nsrc[NUMERICIDENTIFIER] = '0|[1-9]\\\\d*'\nvar NUMERICIDENTIFIERLOOSE = R++\nsrc[NUMERICIDENTIFIERLOOSE] = '\\\\d+'\n\n// ## Non-numeric Identifier\n// Zero or more digits, followed by a letter or hyphen, and then zero or\n// more letters, digits, or hyphens.\n\nvar NONNUMERICIDENTIFIER = R++\nsrc[NONNUMERICIDENTIFIER] = '\\\\d*[a-zA-Z-]' + LETTERDASHNUMBER + '*'\n\n// ## Main Version\n// Three dot-separated numeric identifiers.\n\nvar MAINVERSION = R++\nsrc[MAINVERSION] = '(' + src[NUMERICIDENTIFIER] + ')\\\\.' +\n                   '(' + src[NUMERICIDENTIFIER] + ')\\\\.' +\n                   '(' + src[NUMERICIDENTIFIER] + ')'\n\nvar MAINVERSIONLOOSE = R++\nsrc[MAINVERSIONLOOSE] = '(' + src[NUMERICIDENTIFIERLOOSE] + ')\\\\.' +\n                        '(' + src[NUMERICIDENTIFIERLOOSE] + ')\\\\.' +\n                        '(' + src[NUMERICIDENTIFIERLOOSE] + ')'\n\n// ## Pre-release Version Identifier\n// A numeric identifier, or a non-numeric identifier.\n\nvar PRERELEASEIDENTIFIER = R++\nsrc[PRERELEASEIDENTIFIER] = '(?:' + src[NUMERICIDENTIFIER] +\n                            '|' + src[NONNUMERICIDENTIFIER] + ')'\n\nvar PRERELEASEIDENTIFIERLOOSE = R++\nsrc[PRERELEASEIDENTIFIERLOOSE] = '(?:' + src[NUMERICIDENTIFIERLOOSE] +\n                                 '|' + src[NONNUMERICIDENTIFIER] + ')'\n\n// ## Pre-release Version\n// Hyphen, followed by one or more dot-separated pre-release version\n// identifiers.\n\nvar PRERELEASE = R++\nsrc[PRERELEASE] = '(?:-(' + src[PRERELEASEIDENTIFIER] +\n                  '(?:\\\\.' + src[PRERELEASEIDENTIFIER] + ')*))'\n\nvar PRERELEASELOOSE = R++\nsrc[PRERELEASELOOSE] = '(?:-?(' + src[PRERELEASEIDENTIFIERLOOSE] +\n                       '(?:\\\\.' + src[PRERELEASEIDENTIFIERLOOSE] + ')*))'\n\n// ## Build Metadata Identifier\n// Any combination of digits, letters, or hyphens.\n\nvar BUILDIDENTIFIER = R++\nsrc[BUILDIDENTIFIER] = LETTERDASHNUMBER + '+'\n\n// ## Build Metadata\n// Plus sign, followed by one or more period-separated build metadata\n// identifiers.\n\nvar BUILD = R++\nsrc[BUILD] = '(?:\\\\+(' + src[BUILDIDENTIFIER] +\n             '(?:\\\\.' + src[BUILDIDENTIFIER] + ')*))'\n\n// ## Full Version String\n// A main version, followed optionally by a pre-release version and\n// build metadata.\n\n// Note that the only major, minor, patch, and pre-release sections of\n// the version string are capturing groups.  The build metadata is not a\n// capturing group, because it should not ever be used in version\n// comparison.\n\nvar FULL = R++\nvar FULLPLAIN = 'v?' + src[MAINVERSION] +\n                src[PRERELEASE] + '?' +\n                src[BUILD] + '?'\n\nsrc[FULL] = '^' + FULLPLAIN + '$'\n\n// like full, but allows v1.2.3 and =1.2.3, which people do sometimes.\n// also, 1.0.0alpha1 (prerelease without the hyphen) which is pretty\n// common in the npm registry.\nvar LOOSEPLAIN = '[v=\\\\s]*' + src[MAINVERSIONLOOSE] +\n                 src[PRERELEASELOOSE] + '?' +\n                 src[BUILD] + '?'\n\nvar LOOSE = R++\nsrc[LOOSE] = '^' + LOOSEPLAIN + '$'\n\nvar GTLT = R++\nsrc[GTLT] = '((?:<|>)?=?)'\n\n// Something like \"2.*\" or \"1.2.x\".\n// Note that \"x.x\" is a valid xRange identifer, meaning \"any version\"\n// Only the first item is strictly required.\nvar XRANGEIDENTIFIERLOOSE = R++\nsrc[XRANGEIDENTIFIERLOOSE] = src[NUMERICIDENTIFIERLOOSE] + '|x|X|\\\\*'\nvar XRANGEIDENTIFIER = R++\nsrc[XRANGEIDENTIFIER] = src[NUMERICIDENTIFIER] + '|x|X|\\\\*'\n\nvar XRANGEPLAIN = R++\nsrc[XRANGEPLAIN] = '[v=\\\\s]*(' + src[XRANGEIDENTIFIER] + ')' +\n                   '(?:\\\\.(' + src[XRANGEIDENTIFIER] + ')' +\n                   '(?:\\\\.(' + src[XRANGEIDENTIFIER] + ')' +\n                   '(?:' + src[PRERELEASE] + ')?' +\n                   src[BUILD] + '?' +\n                   ')?)?'\n\nvar XRANGEPLAINLOOSE = R++\nsrc[XRANGEPLAINLOOSE] = '[v=\\\\s]*(' + src[XRANGEIDENTIFIERLOOSE] + ')' +\n                        '(?:\\\\.(' + src[XRANGEIDENTIFIERLOOSE] + ')' +\n                        '(?:\\\\.(' + src[XRANGEIDENTIFIERLOOSE] + ')' +\n                        '(?:' + src[PRERELEASELOOSE] + ')?' +\n                        src[BUILD] + '?' +\n                        ')?)?'\n\nvar XRANGE = R++\nsrc[XRANGE] = '^' + src[GTLT] + '\\\\s*' + src[XRANGEPLAIN] + '$'\nvar XRANGELOOSE = R++\nsrc[XRANGELOOSE] = '^' + src[GTLT] + '\\\\s*' + src[XRANGEPLAINLOOSE] + '$'\n\n// Coercion.\n// Extract anything that could conceivably be a part of a valid semver\nvar COERCE = R++\nsrc[COERCE] = '(?:^|[^\\\\d])' +\n              '(\\\\d{1,' + MAX_SAFE_COMPONENT_LENGTH + '})' +\n              '(?:\\\\.(\\\\d{1,' + MAX_SAFE_COMPONENT_LENGTH + '}))?' +\n              '(?:\\\\.(\\\\d{1,' + MAX_SAFE_COMPONENT_LENGTH + '}))?' +\n              '(?:$|[^\\\\d])'\n\n// Tilde ranges.\n// Meaning is \"reasonably at or greater than\"\nvar LONETILDE = R++\nsrc[LONETILDE] = '(?:~>?)'\n\nvar TILDETRIM = R++\nsrc[TILDETRIM] = '(\\\\s*)' + src[LONETILDE] + '\\\\s+'\nre[TILDETRIM] = new RegExp(src[TILDETRIM], 'g')\nsafeRe[TILDETRIM] = new RegExp(makeSafeRe(src[TILDETRIM]), 'g')\nvar tildeTrimReplace = '$1~'\n\nvar TILDE = R++\nsrc[TILDE] = '^' + src[LONETILDE] + src[XRANGEPLAIN] + '$'\nvar TILDELOOSE = R++\nsrc[TILDELOOSE] = '^' + src[LONETILDE] + src[XRANGEPLAINLOOSE] + '$'\n\n// Caret ranges.\n// Meaning is \"at least and backwards compatible with\"\nvar LONECARET = R++\nsrc[LONECARET] = '(?:\\\\^)'\n\nvar CARETTRIM = R++\nsrc[CARETTRIM] = '(\\\\s*)' + src[LONECARET] + '\\\\s+'\nre[CARETTRIM] = new RegExp(src[CARETTRIM], 'g')\nsafeRe[CARETTRIM] = new RegExp(makeSafeRe(src[CARETTRIM]), 'g')\nvar caretTrimReplace = '$1^'\n\nvar CARET = R++\nsrc[CARET] = '^' + src[LONECARET] + src[XRANGEPLAIN] + '$'\nvar CARETLOOSE = R++\nsrc[CARETLOOSE] = '^' + src[LONECARET] + src[XRANGEPLAINLOOSE] + '$'\n\n// A simple gt/lt/eq thing, or just \"\" to indicate \"any version\"\nvar COMPARATORLOOSE = R++\nsrc[COMPARATORLOOSE] = '^' + src[GTLT] + '\\\\s*(' + LOOSEPLAIN + ')$|^$'\nvar COMPARATOR = R++\nsrc[COMPARATOR] = '^' + src[GTLT] + '\\\\s*(' + FULLPLAIN + ')$|^$'\n\n// An expression to strip any whitespace between the gtlt and the thing\n// it modifies, so that `> 1.2.3` ==> `>1.2.3`\nvar COMPARATORTRIM = R++\nsrc[COMPARATORTRIM] = '(\\\\s*)' + src[GTLT] +\n                      '\\\\s*(' + LOOSEPLAIN + '|' + src[XRANGEPLAIN] + ')'\n\n// this one has to use the /g flag\nre[COMPARATORTRIM] = new RegExp(src[COMPARATORTRIM], 'g')\nsafeRe[COMPARATORTRIM] = new RegExp(makeSafeRe(src[COMPARATORTRIM]), 'g')\nvar comparatorTrimReplace = '$1$2$3'\n\n// Something like `1.2.3 - 1.2.4`\n// Note that these all use the loose form, because they'll be\n// checked against either the strict or loose comparator form\n// later.\nvar HYPHENRANGE = R++\nsrc[HYPHENRANGE] = '^\\\\s*(' + src[XRANGEPLAIN] + ')' +\n                   '\\\\s+-\\\\s+' +\n                   '(' + src[XRANGEPLAIN] + ')' +\n                   '\\\\s*$'\n\nvar HYPHENRANGELOOSE = R++\nsrc[HYPHENRANGELOOSE] = '^\\\\s*(' + src[XRANGEPLAINLOOSE] + ')' +\n                        '\\\\s+-\\\\s+' +\n                        '(' + src[XRANGEPLAINLOOSE] + ')' +\n                        '\\\\s*$'\n\n// Star ranges basically just allow anything at all.\nvar STAR = R++\nsrc[STAR] = '(<|>)?=?\\\\s*\\\\*'\n\n// Compile to actual regexp objects.\n// All are flag-free, unless they were created above with a flag.\nfor (var i = 0; i < R; i++) {\n  debug(i, src[i])\n  if (!re[i]) {\n    re[i] = new RegExp(src[i])\n\n    // Replace all greedy whitespace to prevent regex dos issues. These regex are\n    // used internally via the safeRe object since all inputs in this library get\n    // normalized first to trim and collapse all extra whitespace. The original\n    // regexes are exported for userland consumption and lower level usage. A\n    // future breaking change could export the safer regex only with a note that\n    // all input should have extra whitespace removed.\n    safeRe[i] = new RegExp(makeSafeRe(src[i]))\n  }\n}\n\nexports.parse = parse\nfunction parse (version, options) {\n  if (!options || typeof options !== 'object') {\n    options = {\n      loose: !!options,\n      includePrerelease: false\n    }\n  }\n\n  if (version instanceof SemVer) {\n    return version\n  }\n\n  if (typeof version !== 'string') {\n    return null\n  }\n\n  if (version.length > MAX_LENGTH) {\n    return null\n  }\n\n  var r = options.loose ? safeRe[LOOSE] : safeRe[FULL]\n  if (!r.test(version)) {\n    return null\n  }\n\n  try {\n    return new SemVer(version, options)\n  } catch (er) {\n    return null\n  }\n}\n\nexports.valid = valid\nfunction valid (version, options) {\n  var v = parse(version, options)\n  return v ? v.version : null\n}\n\nexports.clean = clean\nfunction clean (version, options) {\n  var s = parse(version.trim().replace(/^[=v]+/, ''), options)\n  return s ? s.version : null\n}\n\nexports.SemVer = SemVer\n\nfunction SemVer (version, options) {\n  if (!options || typeof options !== 'object') {\n    options = {\n      loose: !!options,\n      includePrerelease: false\n    }\n  }\n  if (version instanceof SemVer) {\n    if (version.loose === options.loose) {\n      return version\n    } else {\n      version = version.version\n    }\n  } else if (typeof version !== 'string') {\n    throw new TypeError('Invalid Version: ' + version)\n  }\n\n  if (version.length > MAX_LENGTH) {\n    throw new TypeError('version is longer than ' + MAX_LENGTH + ' characters')\n  }\n\n  if (!(this instanceof SemVer)) {\n    return new SemVer(version, options)\n  }\n\n  debug('SemVer', version, options)\n  this.options = options\n  this.loose = !!options.loose\n\n  var m = version.trim().match(options.loose ? safeRe[LOOSE] : safeRe[FULL])\n\n  if (!m) {\n    throw new TypeError('Invalid Version: ' + version)\n  }\n\n  this.raw = version\n\n  // these are actually numbers\n  this.major = +m[1]\n  this.minor = +m[2]\n  this.patch = +m[3]\n\n  if (this.major > MAX_SAFE_INTEGER || this.major < 0) {\n    throw new TypeError('Invalid major version')\n  }\n\n  if (this.minor > MAX_SAFE_INTEGER || this.minor < 0) {\n    throw new TypeError('Invalid minor version')\n  }\n\n  if (this.patch > MAX_SAFE_INTEGER || this.patch < 0) {\n    throw new TypeError('Invalid patch version')\n  }\n\n  // numberify any prerelease numeric ids\n  if (!m[4]) {\n    this.prerelease = []\n  } else {\n    this.prerelease = m[4].split('.').map(function (id) {\n      if (/^[0-9]+$/.test(id)) {\n        var num = +id\n        if (num >= 0 && num < MAX_SAFE_INTEGER) {\n          return num\n        }\n      }\n      return id\n    })\n  }\n\n  this.build = m[5] ? m[5].split('.') : []\n  this.format()\n}\n\nSemVer.prototype.format = function () {\n  this.version = this.major + '.' + this.minor + '.' + this.patch\n  if (this.prerelease.length) {\n    this.version += '-' + this.prerelease.join('.')\n  }\n  return this.version\n}\n\nSemVer.prototype.toString = function () {\n  return this.version\n}\n\nSemVer.prototype.compare = function (other) {\n  debug('SemVer.compare', this.version, this.options, other)\n  if (!(other instanceof SemVer)) {\n    other = new SemVer(other, this.options)\n  }\n\n  return this.compareMain(other) || this.comparePre(other)\n}\n\nSemVer.prototype.compareMain = function (other) {\n  if (!(other instanceof SemVer)) {\n    other = new SemVer(other, this.options)\n  }\n\n  return compareIdentifiers(this.major, other.major) ||\n         compareIdentifiers(this.minor, other.minor) ||\n         compareIdentifiers(this.patch, other.patch)\n}\n\nSemVer.prototype.comparePre = function (other) {\n  if (!(other instanceof SemVer)) {\n    other = new SemVer(other, this.options)\n  }\n\n  // NOT having a prerelease is > having one\n  if (this.prerelease.length && !other.prerelease.length) {\n    return -1\n  } else if (!this.prerelease.length && other.prerelease.length) {\n    return 1\n  } else if (!this.prerelease.length && !other.prerelease.length) {\n    return 0\n  }\n\n  var i = 0\n  do {\n    var a = this.prerelease[i]\n    var b = other.prerelease[i]\n    debug('prerelease compare', i, a, b)\n    if (a === undefined && b === undefined) {\n      return 0\n    } else if (b === undefined) {\n      return 1\n    } else if (a === undefined) {\n      return -1\n    } else if (a === b) {\n      continue\n    } else {\n      return compareIdentifiers(a, b)\n    }\n  } while (++i)\n}\n\n// preminor will bump the version up to the next minor release, and immediately\n// down to pre-release. premajor and prepatch work the same way.\nSemVer.prototype.inc = function (release, identifier) {\n  switch (release) {\n    case 'premajor':\n      this.prerelease.length = 0\n      this.patch = 0\n      this.minor = 0\n      this.major++\n      this.inc('pre', identifier)\n      break\n    case 'preminor':\n      this.prerelease.length = 0\n      this.patch = 0\n      this.minor++\n      this.inc('pre', identifier)\n      break\n    case 'prepatch':\n      // If this is already a prerelease, it will bump to the next version\n      // drop any prereleases that might already exist, since they are not\n      // relevant at this point.\n      this.prerelease.length = 0\n      this.inc('patch', identifier)\n      this.inc('pre', identifier)\n      break\n    // If the input is a non-prerelease version, this acts the same as\n    // prepatch.\n    case 'prerelease':\n      if (this.prerelease.length === 0) {\n        this.inc('patch', identifier)\n      }\n      this.inc('pre', identifier)\n      break\n\n    case 'major':\n      // If this is a pre-major version, bump up to the same major version.\n      // Otherwise increment major.\n      // 1.0.0-5 bumps to 1.0.0\n      // 1.1.0 bumps to 2.0.0\n      if (this.minor !== 0 ||\n          this.patch !== 0 ||\n          this.prerelease.length === 0) {\n        this.major++\n      }\n      this.minor = 0\n      this.patch = 0\n      this.prerelease = []\n      break\n    case 'minor':\n      // If this is a pre-minor version, bump up to the same minor version.\n      // Otherwise increment minor.\n      // 1.2.0-5 bumps to 1.2.0\n      // 1.2.1 bumps to 1.3.0\n      if (this.patch !== 0 || this.prerelease.length === 0) {\n        this.minor++\n      }\n      this.patch = 0\n      this.prerelease = []\n      break\n    case 'patch':\n      // If this is not a pre-release version, it will increment the patch.\n      // If it is a pre-release it will bump up to the same patch version.\n      // 1.2.0-5 patches to 1.2.0\n      // 1.2.0 patches to 1.2.1\n      if (this.prerelease.length === 0) {\n        this.patch++\n      }\n      this.prerelease = []\n      break\n    // This probably shouldn't be used publicly.\n    // 1.0.0 \"pre\" would become 1.0.0-0 which is the wrong direction.\n    case 'pre':\n      if (this.prerelease.length === 0) {\n        this.prerelease = [0]\n      } else {\n        var i = this.prerelease.length\n        while (--i >= 0) {\n          if (typeof this.prerelease[i] === 'number') {\n            this.prerelease[i]++\n            i = -2\n          }\n        }\n        if (i === -1) {\n          // didn't increment anything\n          this.prerelease.push(0)\n        }\n      }\n      if (identifier) {\n        // 1.2.0-beta.1 bumps to 1.2.0-beta.2,\n        // 1.2.0-beta.fooblz or 1.2.0-beta bumps to 1.2.0-beta.0\n        if (this.prerelease[0] === identifier) {\n          if (isNaN(this.prerelease[1])) {\n            this.prerelease = [identifier, 0]\n          }\n        } else {\n          this.prerelease = [identifier, 0]\n        }\n      }\n      break\n\n    default:\n      throw new Error('invalid increment argument: ' + release)\n  }\n  this.format()\n  this.raw = this.version\n  return this\n}\n\nexports.inc = inc\nfunction inc (version, release, loose, identifier) {\n  if (typeof (loose) === 'string') {\n    identifier = loose\n    loose = undefined\n  }\n\n  try {\n    return new SemVer(version, loose).inc(release, identifier).version\n  } catch (er) {\n    return null\n  }\n}\n\nexports.diff = diff\nfunction diff (version1, version2) {\n  if (eq(version1, version2)) {\n    return null\n  } else {\n    var v1 = parse(version1)\n    var v2 = parse(version2)\n    var prefix = ''\n    if (v1.prerelease.length || v2.prerelease.length) {\n      prefix = 'pre'\n      var defaultResult = 'prerelease'\n    }\n    for (var key in v1) {\n      if (key === 'major' || key === 'minor' || key === 'patch') {\n        if (v1[key] !== v2[key]) {\n          return prefix + key\n        }\n      }\n    }\n    return defaultResult // may be undefined\n  }\n}\n\nexports.compareIdentifiers = compareIdentifiers\n\nvar numeric = /^[0-9]+$/\nfunction compareIdentifiers (a, b) {\n  var anum = numeric.test(a)\n  var bnum = numeric.test(b)\n\n  if (anum && bnum) {\n    a = +a\n    b = +b\n  }\n\n  return a === b ? 0\n    : (anum && !bnum) ? -1\n    : (bnum && !anum) ? 1\n    : a < b ? -1\n    : 1\n}\n\nexports.rcompareIdentifiers = rcompareIdentifiers\nfunction rcompareIdentifiers (a, b) {\n  return compareIdentifiers(b, a)\n}\n\nexports.major = major\nfunction major (a, loose) {\n  return new SemVer(a, loose).major\n}\n\nexports.minor = minor\nfunction minor (a, loose) {\n  return new SemVer(a, loose).minor\n}\n\nexports.patch = patch\nfunction patch (a, loose) {\n  return new SemVer(a, loose).patch\n}\n\nexports.compare = compare\nfunction compare (a, b, loose) {\n  return new SemVer(a, loose).compare(new SemVer(b, loose))\n}\n\nexports.compareLoose = compareLoose\nfunction compareLoose (a, b) {\n  return compare(a, b, true)\n}\n\nexports.rcompare = rcompare\nfunction rcompare (a, b, loose) {\n  return compare(b, a, loose)\n}\n\nexports.sort = sort\nfunction sort (list, loose) {\n  return list.sort(function (a, b) {\n    return exports.compare(a, b, loose)\n  })\n}\n\nexports.rsort = rsort\nfunction rsort (list, loose) {\n  return list.sort(function (a, b) {\n    return exports.rcompare(a, b, loose)\n  })\n}\n\nexports.gt = gt\nfunction gt (a, b, loose) {\n  return compare(a, b, loose) > 0\n}\n\nexports.lt = lt\nfunction lt (a, b, loose) {\n  return compare(a, b, loose) < 0\n}\n\nexports.eq = eq\nfunction eq (a, b, loose) {\n  return compare(a, b, loose) === 0\n}\n\nexports.neq = neq\nfunction neq (a, b, loose) {\n  return compare(a, b, loose) !== 0\n}\n\nexports.gte = gte\nfunction gte (a, b, loose) {\n  return compare(a, b, loose) >= 0\n}\n\nexports.lte = lte\nfunction lte (a, b, loose) {\n  return compare(a, b, loose) <= 0\n}\n\nexports.cmp = cmp\nfunction cmp (a, op, b, loose) {\n  switch (op) {\n    case '===':\n      if (typeof a === 'object')\n        a = a.version\n      if (typeof b === 'object')\n        b = b.version\n      return a === b\n\n    case '!==':\n      if (typeof a === 'object')\n        a = a.version\n      if (typeof b === 'object')\n        b = b.version\n      return a !== b\n\n    case '':\n    case '=':\n    case '==':\n      return eq(a, b, loose)\n\n    case '!=':\n      return neq(a, b, loose)\n\n    case '>':\n      return gt(a, b, loose)\n\n    case '>=':\n      return gte(a, b, loose)\n\n    case '<':\n      return lt(a, b, loose)\n\n    case '<=':\n      return lte(a, b, loose)\n\n    default:\n      throw new TypeError('Invalid operator: ' + op)\n  }\n}\n\nexports.Comparator = Comparator\nfunction Comparator (comp, options) {\n  if (!options || typeof options !== 'object') {\n    options = {\n      loose: !!options,\n      includePrerelease: false\n    }\n  }\n\n  if (comp instanceof Comparator) {\n    if (comp.loose === !!options.loose) {\n      return comp\n    } else {\n      comp = comp.value\n    }\n  }\n\n  if (!(this instanceof Comparator)) {\n    return new Comparator(comp, options)\n  }\n\n  comp = comp.trim().split(/\\s+/).join(' ')\n  debug('comparator', comp, options)\n  this.options = options\n  this.loose = !!options.loose\n  this.parse(comp)\n\n  if (this.semver === ANY) {\n    this.value = ''\n  } else {\n    this.value = this.operator + this.semver.version\n  }\n\n  debug('comp', this)\n}\n\nvar ANY = {}\nComparator.prototype.parse = function (comp) {\n  var r = this.options.loose ? safeRe[COMPARATORLOOSE] : safeRe[COMPARATOR]\n  var m = comp.match(r)\n\n  if (!m) {\n    throw new TypeError('Invalid comparator: ' + comp)\n  }\n\n  this.operator = m[1]\n  if (this.operator === '=') {\n    this.operator = ''\n  }\n\n  // if it literally is just '>' or '' then allow anything.\n  if (!m[2]) {\n    this.semver = ANY\n  } else {\n    this.semver = new SemVer(m[2], this.options.loose)\n  }\n}\n\nComparator.prototype.toString = function () {\n  return this.value\n}\n\nComparator.prototype.test = function (version) {\n  debug('Comparator.test', version, this.options.loose)\n\n  if (this.semver === ANY) {\n    return true\n  }\n\n  if (typeof version === 'string') {\n    version = new SemVer(version, this.options)\n  }\n\n  return cmp(version, this.operator, this.semver, this.options)\n}\n\nComparator.prototype.intersects = function (comp, options) {\n  if (!(comp instanceof Comparator)) {\n    throw new TypeError('a Comparator is required')\n  }\n\n  if (!options || typeof options !== 'object') {\n    options = {\n      loose: !!options,\n      includePrerelease: false\n    }\n  }\n\n  var rangeTmp\n\n  if (this.operator === '') {\n    rangeTmp = new Range(comp.value, options)\n    return satisfies(this.value, rangeTmp, options)\n  } else if (comp.operator === '') {\n    rangeTmp = new Range(this.value, options)\n    return satisfies(comp.semver, rangeTmp, options)\n  }\n\n  var sameDirectionIncreasing =\n    (this.operator === '>=' || this.operator === '>') &&\n    (comp.operator === '>=' || comp.operator === '>')\n  var sameDirectionDecreasing =\n    (this.operator === '<=' || this.operator === '<') &&\n    (comp.operator === '<=' || comp.operator === '<')\n  var sameSemVer = this.semver.version === comp.semver.version\n  var differentDirectionsInclusive =\n    (this.operator === '>=' || this.operator === '<=') &&\n    (comp.operator === '>=' || comp.operator === '<=')\n  var oppositeDirectionsLessThan =\n    cmp(this.semver, '<', comp.semver, options) &&\n    ((this.operator === '>=' || this.operator === '>') &&\n    (comp.operator === '<=' || comp.operator === '<'))\n  var oppositeDirectionsGreaterThan =\n    cmp(this.semver, '>', comp.semver, options) &&\n    ((this.operator === '<=' || this.operator === '<') &&\n    (comp.operator === '>=' || comp.operator === '>'))\n\n  return sameDirectionIncreasing || sameDirectionDecreasing ||\n    (sameSemVer && differentDirectionsInclusive) ||\n    oppositeDirectionsLessThan || oppositeDirectionsGreaterThan\n}\n\nexports.Range = Range\nfunction Range (range, options) {\n  if (!options || typeof options !== 'object') {\n    options = {\n      loose: !!options,\n      includePrerelease: false\n    }\n  }\n\n  if (range instanceof Range) {\n    if (range.loose === !!options.loose &&\n        range.includePrerelease === !!options.includePrerelease) {\n      return range\n    } else {\n      return new Range(range.raw, options)\n    }\n  }\n\n  if (range instanceof Comparator) {\n    return new Range(range.value, options)\n  }\n\n  if (!(this instanceof Range)) {\n    return new Range(range, options)\n  }\n\n  this.options = options\n  this.loose = !!options.loose\n  this.includePrerelease = !!options.includePrerelease\n\n  // First reduce all whitespace as much as possible so we do not have to rely\n  // on potentially slow regexes like \\s*. This is then stored and used for\n  // future error messages as well.\n  this.raw = range\n    .trim()\n    .split(/\\s+/)\n    .join(' ')\n\n  // First, split based on boolean or ||\n  this.set = this.raw.split('||').map(function (range) {\n    return this.parseRange(range.trim())\n  }, this).filter(function (c) {\n    // throw out any that are not relevant for whatever reason\n    return c.length\n  })\n\n  if (!this.set.length) {\n    throw new TypeError('Invalid SemVer Range: ' + this.raw)\n  }\n\n  this.format()\n}\n\nRange.prototype.format = function () {\n  this.range = this.set.map(function (comps) {\n    return comps.join(' ').trim()\n  }).join('||').trim()\n  return this.range\n}\n\nRange.prototype.toString = function () {\n  return this.range\n}\n\nRange.prototype.parseRange = function (range) {\n  var loose = this.options.loose\n  // `1.2.3 - 1.2.4` => `>=1.2.3 <=1.2.4`\n  var hr = loose ? safeRe[HYPHENRANGELOOSE] : safeRe[HYPHENRANGE]\n  range = range.replace(hr, hyphenReplace)\n  debug('hyphen replace', range)\n  // `> 1.2.3 < 1.2.5` => `>1.2.3 <1.2.5`\n  range = range.replace(safeRe[COMPARATORTRIM], comparatorTrimReplace)\n  debug('comparator trim', range, safeRe[COMPARATORTRIM])\n\n  // `~ 1.2.3` => `~1.2.3`\n  range = range.replace(safeRe[TILDETRIM], tildeTrimReplace)\n\n  // `^ 1.2.3` => `^1.2.3`\n  range = range.replace(safeRe[CARETTRIM], caretTrimReplace)\n\n  // At this point, the range is completely trimmed and\n  // ready to be split into comparators.\n  var compRe = loose ? safeRe[COMPARATORLOOSE] : safeRe[COMPARATOR]\n  var set = range.split(' ').map(function (comp) {\n    return parseComparator(comp, this.options)\n  }, this).join(' ').split(/\\s+/)\n  if (this.options.loose) {\n    // in loose mode, throw out any that are not valid comparators\n    set = set.filter(function (comp) {\n      return !!comp.match(compRe)\n    })\n  }\n  set = set.map(function (comp) {\n    return new Comparator(comp, this.options)\n  }, this)\n\n  return set\n}\n\nRange.prototype.intersects = function (range, options) {\n  if (!(range instanceof Range)) {\n    throw new TypeError('a Range is required')\n  }\n\n  return this.set.some(function (thisComparators) {\n    return thisComparators.every(function (thisComparator) {\n      return range.set.some(function (rangeComparators) {\n        return rangeComparators.every(function (rangeComparator) {\n          return thisComparator.intersects(rangeComparator, options)\n        })\n      })\n    })\n  })\n}\n\n// Mostly just for testing and legacy API reasons\nexports.toComparators = toComparators\nfunction toComparators (range, options) {\n  return new Range(range, options).set.map(function (comp) {\n    return comp.map(function (c) {\n      return c.value\n    }).join(' ').trim().split(' ')\n  })\n}\n\n// comprised of xranges, tildes, stars, and gtlt's at this point.\n// already replaced the hyphen ranges\n// turn into a set of JUST comparators.\nfunction parseComparator (comp, options) {\n  debug('comp', comp, options)\n  comp = replaceCarets(comp, options)\n  debug('caret', comp)\n  comp = replaceTildes(comp, options)\n  debug('tildes', comp)\n  comp = replaceXRanges(comp, options)\n  debug('xrange', comp)\n  comp = replaceStars(comp, options)\n  debug('stars', comp)\n  return comp\n}\n\nfunction isX (id) {\n  return !id || id.toLowerCase() === 'x' || id === '*'\n}\n\n// ~, ~> --> * (any, kinda silly)\n// ~2, ~2.x, ~2.x.x, ~>2, ~>2.x ~>2.x.x --> >=2.0.0 <3.0.0\n// ~2.0, ~2.0.x, ~>2.0, ~>2.0.x --> >=2.0.0 <2.1.0\n// ~1.2, ~1.2.x, ~>1.2, ~>1.2.x --> >=1.2.0 <1.3.0\n// ~1.2.3, ~>1.2.3 --> >=1.2.3 <1.3.0\n// ~1.2.0, ~>1.2.0 --> >=1.2.0 <1.3.0\nfunction replaceTildes (comp, options) {\n  return comp.trim().split(/\\s+/).map(function (comp) {\n    return replaceTilde(comp, options)\n  }).join(' ')\n}\n\nfunction replaceTilde (comp, options) {\n  var r = options.loose ? safeRe[TILDELOOSE] : safeRe[TILDE]\n  return comp.replace(r, function (_, M, m, p, pr) {\n    debug('tilde', comp, _, M, m, p, pr)\n    var ret\n\n    if (isX(M)) {\n      ret = ''\n    } else if (isX(m)) {\n      ret = '>=' + M + '.0.0 <' + (+M + 1) + '.0.0'\n    } else if (isX(p)) {\n      // ~1.2 == >=1.2.0 <1.3.0\n      ret = '>=' + M + '.' + m + '.0 <' + M + '.' + (+m + 1) + '.0'\n    } else if (pr) {\n      debug('replaceTilde pr', pr)\n      ret = '>=' + M + '.' + m + '.' + p + '-' + pr +\n            ' <' + M + '.' + (+m + 1) + '.0'\n    } else {\n      // ~1.2.3 == >=1.2.3 <1.3.0\n      ret = '>=' + M + '.' + m + '.' + p +\n            ' <' + M + '.' + (+m + 1) + '.0'\n    }\n\n    debug('tilde return', ret)\n    return ret\n  })\n}\n\n// ^ --> * (any, kinda silly)\n// ^2, ^2.x, ^2.x.x --> >=2.0.0 <3.0.0\n// ^2.0, ^2.0.x --> >=2.0.0 <3.0.0\n// ^1.2, ^1.2.x --> >=1.2.0 <2.0.0\n// ^1.2.3 --> >=1.2.3 <2.0.0\n// ^1.2.0 --> >=1.2.0 <2.0.0\nfunction replaceCarets (comp, options) {\n  return comp.trim().split(/\\s+/).map(function (comp) {\n    return replaceCaret(comp, options)\n  }).join(' ')\n}\n\nfunction replaceCaret (comp, options) {\n  debug('caret', comp, options)\n  var r = options.loose ? safeRe[CARETLOOSE] : safeRe[CARET]\n  return comp.replace(r, function (_, M, m, p, pr) {\n    debug('caret', comp, _, M, m, p, pr)\n    var ret\n\n    if (isX(M)) {\n      ret = ''\n    } else if (isX(m)) {\n      ret = '>=' + M + '.0.0 <' + (+M + 1) + '.0.0'\n    } else if (isX(p)) {\n      if (M === '0') {\n        ret = '>=' + M + '.' + m + '.0 <' + M + '.' + (+m + 1) + '.0'\n      } else {\n        ret = '>=' + M + '.' + m + '.0 <' + (+M + 1) + '.0.0'\n      }\n    } else if (pr) {\n      debug('replaceCaret pr', pr)\n      if (M === '0') {\n        if (m === '0') {\n          ret = '>=' + M + '.' + m + '.' + p + '-' + pr +\n                ' <' + M + '.' + m + '.' + (+p + 1)\n        } else {\n          ret = '>=' + M + '.' + m + '.' + p + '-' + pr +\n                ' <' + M + '.' + (+m + 1) + '.0'\n        }\n      } else {\n        ret = '>=' + M + '.' + m + '.' + p + '-' + pr +\n              ' <' + (+M + 1) + '.0.0'\n      }\n    } else {\n      debug('no pr')\n      if (M === '0') {\n        if (m === '0') {\n          ret = '>=' + M + '.' + m + '.' + p +\n                ' <' + M + '.' + m + '.' + (+p + 1)\n        } else {\n          ret = '>=' + M + '.' + m + '.' + p +\n                ' <' + M + '.' + (+m + 1) + '.0'\n        }\n      } else {\n        ret = '>=' + M + '.' + m + '.' + p +\n              ' <' + (+M + 1) + '.0.0'\n      }\n    }\n\n    debug('caret return', ret)\n    return ret\n  })\n}\n\nfunction replaceXRanges (comp, options) {\n  debug('replaceXRanges', comp, options)\n  return comp.split(/\\s+/).map(function (comp) {\n    return replaceXRange(comp, options)\n  }).join(' ')\n}\n\nfunction replaceXRange (comp, options) {\n  comp = comp.trim()\n  var r = options.loose ? safeRe[XRANGELOOSE] : safeRe[XRANGE]\n  return comp.replace(r, function (ret, gtlt, M, m, p, pr) {\n    debug('xRange', comp, ret, gtlt, M, m, p, pr)\n    var xM = isX(M)\n    var xm = xM || isX(m)\n    var xp = xm || isX(p)\n    var anyX = xp\n\n    if (gtlt === '=' && anyX) {\n      gtlt = ''\n    }\n\n    if (xM) {\n      if (gtlt === '>' || gtlt === '<') {\n        // nothing is allowed\n        ret = '<0.0.0'\n      } else {\n        // nothing is forbidden\n        ret = '*'\n      }\n    } else if (gtlt && anyX) {\n      // we know patch is an x, because we have any x at all.\n      // replace X with 0\n      if (xm) {\n        m = 0\n      }\n      p = 0\n\n      if (gtlt === '>') {\n        // >1 => >=2.0.0\n        // >1.2 => >=1.3.0\n        // >1.2.3 => >= 1.2.4\n        gtlt = '>='\n        if (xm) {\n          M = +M + 1\n          m = 0\n          p = 0\n        } else {\n          m = +m + 1\n          p = 0\n        }\n      } else if (gtlt === '<=') {\n        // <=0.7.x is actually <0.8.0, since any 0.7.x should\n        // pass.  Similarly, <=7.x is actually <8.0.0, etc.\n        gtlt = '<'\n        if (xm) {\n          M = +M + 1\n        } else {\n          m = +m + 1\n        }\n      }\n\n      ret = gtlt + M + '.' + m + '.' + p\n    } else if (xm) {\n      ret = '>=' + M + '.0.0 <' + (+M + 1) + '.0.0'\n    } else if (xp) {\n      ret = '>=' + M + '.' + m + '.0 <' + M + '.' + (+m + 1) + '.0'\n    }\n\n    debug('xRange return', ret)\n\n    return ret\n  })\n}\n\n// Because * is AND-ed with everything else in the comparator,\n// and '' means \"any version\", just remove the *s entirely.\nfunction replaceStars (comp, options) {\n  debug('replaceStars', comp, options)\n  // Looseness is ignored here.  star is always as loose as it gets!\n  return comp.trim().replace(safeRe[STAR], '')\n}\n\n// This function is passed to string.replace(safeRe[HYPHENRANGE])\n// M, m, patch, prerelease, build\n// 1.2 - 3.4.5 => >=1.2.0 <=3.4.5\n// 1.2.3 - 3.4 => >=1.2.0 <3.5.0 Any 3.4.x will do\n// 1.2 - 3.4 => >=1.2.0 <3.5.0\nfunction hyphenReplace ($0,\n  from, fM, fm, fp, fpr, fb,\n  to, tM, tm, tp, tpr, tb) {\n  if (isX(fM)) {\n    from = ''\n  } else if (isX(fm)) {\n    from = '>=' + fM + '.0.0'\n  } else if (isX(fp)) {\n    from = '>=' + fM + '.' + fm + '.0'\n  } else {\n    from = '>=' + from\n  }\n\n  if (isX(tM)) {\n    to = ''\n  } else if (isX(tm)) {\n    to = '<' + (+tM + 1) + '.0.0'\n  } else if (isX(tp)) {\n    to = '<' + tM + '.' + (+tm + 1) + '.0'\n  } else if (tpr) {\n    to = '<=' + tM + '.' + tm + '.' + tp + '-' + tpr\n  } else {\n    to = '<=' + to\n  }\n\n  return (from + ' ' + to).trim()\n}\n\n// if ANY of the sets match ALL of its comparators, then pass\nRange.prototype.test = function (version) {\n  if (!version) {\n    return false\n  }\n\n  if (typeof version === 'string') {\n    version = new SemVer(version, this.options)\n  }\n\n  for (var i = 0; i < this.set.length; i++) {\n    if (testSet(this.set[i], version, this.options)) {\n      return true\n    }\n  }\n  return false\n}\n\nfunction testSet (set, version, options) {\n  for (var i = 0; i < set.length; i++) {\n    if (!set[i].test(version)) {\n      return false\n    }\n  }\n\n  if (version.prerelease.length && !options.includePrerelease) {\n    // Find the set of versions that are allowed to have prereleases\n    // For example, ^1.2.3-pr.1 desugars to >=1.2.3-pr.1 <2.0.0\n    // That should allow `1.2.3-pr.2` to pass.\n    // However, `1.2.4-alpha.notready` should NOT be allowed,\n    // even though it's within the range set by the comparators.\n    for (i = 0; i < set.length; i++) {\n      debug(set[i].semver)\n      if (set[i].semver === ANY) {\n        continue\n      }\n\n      if (set[i].semver.prerelease.length > 0) {\n        var allowed = set[i].semver\n        if (allowed.major === version.major &&\n            allowed.minor === version.minor &&\n            allowed.patch === version.patch) {\n          return true\n        }\n      }\n    }\n\n    // Version has a -pre, but it's not one of the ones we like.\n    return false\n  }\n\n  return true\n}\n\nexports.satisfies = satisfies\nfunction satisfies (version, range, options) {\n  try {\n    range = new Range(range, options)\n  } catch (er) {\n    return false\n  }\n  return range.test(version)\n}\n\nexports.maxSatisfying = maxSatisfying\nfunction maxSatisfying (versions, range, options) {\n  var max = null\n  var maxSV = null\n  try {\n    var rangeObj = new Range(range, options)\n  } catch (er) {\n    return null\n  }\n  versions.forEach(function (v) {\n    if (rangeObj.test(v)) {\n      // satisfies(v, range, options)\n      if (!max || maxSV.compare(v) === -1) {\n        // compare(max, v, true)\n        max = v\n        maxSV = new SemVer(max, options)\n      }\n    }\n  })\n  return max\n}\n\nexports.minSatisfying = minSatisfying\nfunction minSatisfying (versions, range, options) {\n  var min = null\n  var minSV = null\n  try {\n    var rangeObj = new Range(range, options)\n  } catch (er) {\n    return null\n  }\n  versions.forEach(function (v) {\n    if (rangeObj.test(v)) {\n      // satisfies(v, range, options)\n      if (!min || minSV.compare(v) === 1) {\n        // compare(min, v, true)\n        min = v\n        minSV = new SemVer(min, options)\n      }\n    }\n  })\n  return min\n}\n\nexports.minVersion = minVersion\nfunction minVersion (range, loose) {\n  range = new Range(range, loose)\n\n  var minver = new SemVer('0.0.0')\n  if (range.test(minver)) {\n    return minver\n  }\n\n  minver = new SemVer('0.0.0-0')\n  if (range.test(minver)) {\n    return minver\n  }\n\n  minver = null\n  for (var i = 0; i < range.set.length; ++i) {\n    var comparators = range.set[i]\n\n    comparators.forEach(function (comparator) {\n      // Clone to avoid manipulating the comparator's semver object.\n      var compver = new SemVer(comparator.semver.version)\n      switch (comparator.operator) {\n        case '>':\n          if (compver.prerelease.length === 0) {\n            compver.patch++\n          } else {\n            compver.prerelease.push(0)\n          }\n          compver.raw = compver.format()\n          /* fallthrough */\n        case '':\n        case '>=':\n          if (!minver || gt(minver, compver)) {\n            minver = compver\n          }\n          break\n        case '<':\n        case '<=':\n          /* Ignore maximum versions */\n          break\n        /* istanbul ignore next */\n        default:\n          throw new Error('Unexpected operation: ' + comparator.operator)\n      }\n    })\n  }\n\n  if (minver && range.test(minver)) {\n    return minver\n  }\n\n  return null\n}\n\nexports.validRange = validRange\nfunction validRange (range, options) {\n  try {\n    // Return '*' instead of '' so that truthiness works.\n    // This will throw if it's invalid anyway\n    return new Range(range, options).range || '*'\n  } catch (er) {\n    return null\n  }\n}\n\n// Determine if version is less than all the versions possible in the range\nexports.ltr = ltr\nfunction ltr (version, range, options) {\n  return outside(version, range, '<', options)\n}\n\n// Determine if version is greater than all the versions possible in the range.\nexports.gtr = gtr\nfunction gtr (version, range, options) {\n  return outside(version, range, '>', options)\n}\n\nexports.outside = outside\nfunction outside (version, range, hilo, options) {\n  version = new SemVer(version, options)\n  range = new Range(range, options)\n\n  var gtfn, ltefn, ltfn, comp, ecomp\n  switch (hilo) {\n    case '>':\n      gtfn = gt\n      ltefn = lte\n      ltfn = lt\n      comp = '>'\n      ecomp = '>='\n      break\n    case '<':\n      gtfn = lt\n      ltefn = gte\n      ltfn = gt\n      comp = '<'\n      ecomp = '<='\n      break\n    default:\n      throw new TypeError('Must provide a hilo val of \"<\" or \">\"')\n  }\n\n  // If it satisifes the range it is not outside\n  if (satisfies(version, range, options)) {\n    return false\n  }\n\n  // From now on, variable terms are as if we're in \"gtr\" mode.\n  // but note that everything is flipped for the \"ltr\" function.\n\n  for (var i = 0; i < range.set.length; ++i) {\n    var comparators = range.set[i]\n\n    var high = null\n    var low = null\n\n    comparators.forEach(function (comparator) {\n      if (comparator.semver === ANY) {\n        comparator = new Comparator('>=0.0.0')\n      }\n      high = high || comparator\n      low = low || comparator\n      if (gtfn(comparator.semver, high.semver, options)) {\n        high = comparator\n      } else if (ltfn(comparator.semver, low.semver, options)) {\n        low = comparator\n      }\n    })\n\n    // If the edge version comparator has a operator then our version\n    // isn't outside it\n    if (high.operator === comp || high.operator === ecomp) {\n      return false\n    }\n\n    // If the lowest version comparator has an operator and our version\n    // is less than it then it isn't higher than the range\n    if ((!low.operator || low.operator === comp) &&\n        ltefn(version, low.semver)) {\n      return false\n    } else if (low.operator === ecomp && ltfn(version, low.semver)) {\n      return false\n    }\n  }\n  return true\n}\n\nexports.prerelease = prerelease\nfunction prerelease (version, options) {\n  var parsed = parse(version, options)\n  return (parsed && parsed.prerelease.length) ? parsed.prerelease : null\n}\n\nexports.intersects = intersects\nfunction intersects (r1, r2, options) {\n  r1 = new Range(r1, options)\n  r2 = new Range(r2, options)\n  return r1.intersects(r2)\n}\n\nexports.coerce = coerce\nfunction coerce (version) {\n  if (version instanceof SemVer) {\n    return version\n  }\n\n  if (typeof version !== 'string') {\n    return null\n  }\n\n  var match = version.match(safeRe[COERCE])\n\n  if (match == null) {\n    return null\n  }\n\n  return parse(match[1] +\n    '.' + (match[2] || '0') +\n    '.' + (match[3] || '0'))\n}\n"]}