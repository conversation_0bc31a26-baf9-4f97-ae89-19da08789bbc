// 🎨 视频参数处理器类
class VideoParameterProcessor {
  constructor(videoElement, canvasElement, ctx, sizeInfo = null) {
    this.video = videoElement; // 在小程序中可能为null
    this.canvas = canvasElement;
    this.ctx = ctx;
    this.isProcessing = false;
    this.animationFrame = null;
    this.videoContext = null; // 用于存储VideoContext

    // 🎯 存储尺寸信息，用于正确的坐标映射
    this.sizeInfo = sizeInfo || {
      displayWidth: canvasElement ? canvasElement.width : 369,
      displayHeight: canvasElement ? canvasElement.height : 270,
      dpr: 1
    };

    // 保存Canvas尺寸作为备份（防止访问时为undefined）
    this.canvasWidth = canvasElement ? canvasElement.width : this.sizeInfo.displayWidth;
    this.canvasHeight = canvasElement ? canvasElement.height : this.sizeInfo.displayHeight;

    // 如果没有video元素，尝试获取VideoContext
    if (!this.video) {
      try {
        this.videoContext = wx.createVideoContext('myVideo');
        console.log('✅ 创建VideoContext成功');
      } catch (e) {
        console.warn('⚠️ 创建VideoContext失败:', e);
      }
    }

    // 🎯 检测是否为旧版Canvas API
    this.isLegacyCanvas = sizeInfo && sizeInfo.isLegacyCanvas;

    // 🎯 维护累积的参数状态
    this.accumulatedParams = {
      brightness: 115,
      contrast: 115,
      saturation: 106,
      white_balance_temperature: 4650,
      exposure_absolute: 1250,
      sharpness: 10,
      gain: 0
    };

    console.log('🎯 VideoParameterProcessor 创建成功（旧版Canvas API）', {
      canvasWidth: this.canvasWidth,
      canvasHeight: this.canvasHeight,
      sizeInfo: this.sizeInfo,
      isLegacyCanvas: this.isLegacyCanvas,
      canvasElement: canvasElement,
      accumulatedParams: this.accumulatedParams
    });
  }

  // 应用所有参数并实时预览
  applyParameters(params, detectionAreas = null) {
    // 🎯 旧版Canvas API只需要检查ctx，不需要canvas元素
    if (this.isProcessing || !this.ctx || (!this.isLegacyCanvas && !this.canvas)) {
      console.warn('🎨 参数处理器未准备好或正在处理中');
      return;
    }

    // 保存检测框坐标
    this.detectionAreas = detectionAreas;

    // 在小程序中，如果没有video元素，使用VideoContext截取当前帧
    if (!this.video) {
      this.applyParametersWithVideoContext(params);
      return;
    }

    // 检查视频源是否准备好
    if (this.video.tagName === 'VIDEO' && this.video.readyState < 2) {
      console.warn('🎨 视频还未准备好，跳过参数应用');
      return;
    }

    // 如果是Canvas元素，检查是否有内容
    if (this.video.tagName === 'CANVAS') {
      const tempCtx = this.video.getContext('2d');
      const tempData = tempCtx.getImageData(0, 0, 1, 1);
      if (!tempData || tempData.data.every(val => val === 0)) {
        console.warn('🎨 Canvas视频源还没有内容，跳过参数应用');
        return;
      }
    }

    this.isProcessing = true;

    try {
      // 获取视频源实际尺寸
      let videoWidth, videoHeight;

      if (this.video.tagName === 'VIDEO') {
        // 视频元素
        videoWidth = this.video.videoWidth || this.video.width || 1920;
        videoHeight = this.video.videoHeight || this.video.height || 1080;
      } else if (this.video.tagName === 'CANVAS') {
        // Canvas元素
        videoWidth = this.video.width || 1920;
        videoHeight = this.video.height || 1080;
      } else {
        // 默认尺寸
        videoWidth = 1920;
        videoHeight = 1080;
      }

      // 🎯 旧版Canvas API使用固定尺寸
      const canvasDisplayWidth = this.isLegacyCanvas ? this.canvasWidth : (this.canvas.offsetWidth || this.canvas.width);
      const canvasDisplayHeight = this.isLegacyCanvas ? this.canvasHeight : (this.canvas.offsetHeight || this.canvas.height);

      const canvasWidth = this.isLegacyCanvas ? this.canvasWidth : this.canvas.width;
      const canvasHeight = this.isLegacyCanvas ? this.canvasHeight : this.canvas.height;

      // 清除之前的内容
      this.ctx.clearRect(0, 0, canvasWidth, canvasHeight);

      // 🎯 旧版Canvas API不支持drawImage，跳过视频帧绘制
      if (!this.isLegacyCanvas) {
        // 绘制当前视频帧到Canvas
        this.ctx.drawImage(this.video, 0, 0, canvasWidth, canvasHeight);

        // 获取像素数据
        const imageData = this.ctx.getImageData(0, 0, canvasWidth, canvasHeight);
      } else {
        // 旧版Canvas API直接创建模拟效果
        this.createSimulatedVideoFrame(params);
        return;
      }
      const data = imageData.data;

      // 检查是否有有效的像素数据
      if (data.length === 0) {
        console.warn('🎨 无法获取有效的像素数据');
        return;
      }

      // 依次应用各种参数算法（注意顺序：先自动算法，再手动调节）

      // 第一步：自动算法（分析整体图像特征）
      this.applyAutoWhiteBalance(data, params.white_balance_temperature_auto || 0);
      this.applyAutoExposure(data, params.exposure_auto || 3);
      this.applyPowerLineFilter(data, params.power_line_frequency || 2);

      // 第二步：基础色彩调节
      this.applyBrightness(data, params.brightness || 115);
      this.applyContrast(data, params.contrast || 115);
      this.applyGain(data, params.gain || 0);
      this.applyExposure(data, params.exposure_absolute || 1250);

      // 第三步：色彩和白平衡（手动色温会覆盖自动白平衡）
      this.applyColorTemperature(data, params.white_balance_temperature || 4650);
      this.applySaturation(data, params.saturation || 106);

      // 第四步：锐度处理（最后应用，避免影响其他算法）
      this.applySharpness(imageData, params.sharpness || 10);

      // 更新Canvas显示
      this.ctx.putImageData(imageData, 0, 0);

      console.log('🎨 参数应用完成:', {
        videoSize: `${videoWidth}x${videoHeight}`,
        canvasSize: `${canvasWidth}x${canvasHeight}`,
        params: params
      });

    } catch (error) {
      console.error('❌ 应用参数失败:', error);
      // 尝试清除Canvas内容
      try {
        const clearWidth = this.isLegacyCanvas ? this.canvasWidth : this.canvas.width;
        const clearHeight = this.isLegacyCanvas ? this.canvasHeight : this.canvas.height;
        this.ctx.clearRect(0, 0, clearWidth, clearHeight);
      } catch (clearError) {
        console.error('❌ 清除Canvas失败:', clearError);
      }
    } finally {
      this.isProcessing = false;
    }
  }

  // 小程序模式：使用VideoContext截取帧并应用参数
  applyParametersWithVideoContext(params) {
    if (this.isProcessing) return;

    this.isProcessing = true;

    try {
      // 直接创建模拟的视频帧效果
      // 注意：小程序的video组件无法直接截取帧，所以我们创建模拟效果
      this.createSimulatedVideoFrame(params);

    } catch (error) {
      console.error('❌ 小程序参数应用失败:', error);
      this.createSimulatedVideoFrame(params);
    } finally {
      this.isProcessing = false;
    }
  }

  // 创建模拟的参数调节效果（累积参数版本）
  createSimulatedVideoFrame(params) {
    try {
      // 🎯 更新累积参数状态
      this.updateAccumulatedParams(params);

      // 🎯 使用累积的参数创建效果
      if (this.hasParameterChanges(this.accumulatedParams)) {
        this.createParameterEffectOverlay(this.accumulatedParams);
        console.log('🎨 参数调节效果更新（累积版）:', {
          newParams: params,
          accumulatedParams: this.accumulatedParams
        });
      } else {
        // 🎯 没有参数变化，清除Canvas
        const width = this.canvasWidth || 369;
        const height = this.canvasHeight || 270;
        this.ctx.clearRect(0, 0, width, height);
        if (this.isLegacyCanvas) {
          this.ctx.draw(true);
        }
        console.log('🎨 默认参数，Canvas透明');
      }

    } catch (error) {
      console.error('❌ 创建参数效果失败:', error);
      // 即使出错也显示一个基础的指示器
      this.createBasicIndicator();
    }
  }

  // 🎯 更新累积参数状态
  updateAccumulatedParams(newParams) {
    // 将新参数合并到累积参数中
    for (const [key, value] of Object.entries(newParams)) {
      if (value !== undefined && value !== null) {
        this.accumulatedParams[key] = value;
        console.log(`🎯 累积参数更新: ${key} = ${value}`);
      }
    }

    console.log('🎯 当前累积参数状态:', this.accumulatedParams);
  }

  // 🎯 重置累积参数到默认值
  resetAccumulatedParams() {
    this.accumulatedParams = {
      brightness: 115,
      contrast: 115,
      saturation: 106,
      white_balance_temperature: 4650,
      exposure_absolute: 1250,
      sharpness: 10,
      gain: 0
    };
    console.log('🎯 累积参数已重置到默认值');
  }

  // 🎯 重置所有参数并清除Canvas（供重置按钮调用）
  resetAllParameters() {
    // 重置累积参数状态
    this.resetAccumulatedParams();

    // 清除Canvas内容
    const width = this.sizeInfo.displayWidth || this.canvasWidth || 369;
    const height = this.sizeInfo.displayHeight || this.canvasHeight || 270;
    this.ctx.clearRect(0, 0, width, height);

    // 如果是旧版Canvas，需要调用draw()
    if (this.isLegacyCanvas) {
      this.ctx.draw(true);
    }

    console.log('🎯 所有参数已重置，Canvas已清除');

    // 返回默认参数对象，供前端更新UI
    return {
      brightness: 115,
      contrast: 115,
      saturation: 106,
      white_balance_temperature: 4650,
      exposure_absolute: 1250,
      sharpness: 10,
      gain: 0
    };
  }

  // 创建基础指示器（当出错时显示）
  createBasicIndicator() {
    // 🎯 旧版Canvas API没有Canvas元素，使用固定尺寸
    const width = this.isLegacyCanvas ? this.canvasWidth : (this.canvas ? this.canvas.width : this.canvasWidth);
    const height = this.isLegacyCanvas ? this.canvasHeight : (this.canvas ? this.canvas.height : this.canvasHeight);

    console.log('🎯 开始基础指示器测试:', {
      canvasWidth: width,
      canvasHeight: height,
      isLegacyCanvas: this.isLegacyCanvas,
      canvasElement: this.canvas
    });

    // 🎯 旧版Canvas API使用不同的方法
    if (this.isLegacyCanvas) {
      // 旧版API清除方法
      this.ctx.clearRect(0, 0, width, height);

      // 🎯 首先测试最简单的绘制（旧版API）
      console.log('🎯 开始最简单的旧版Canvas测试');
      this.ctx.setFillStyle('#FF0000'); // 旧版API使用setFillStyle
      this.ctx.fillRect(10, 10, 50, 50);
      console.log('🎯 绘制了红色方块 (10,10,50,50) - 旧版API');
    } else {
      // 新版API清除方法
      this.ctx.clearRect(0, 0, width, height);

      // 🎯 首先测试最简单的绘制（新版API）
      console.log('🎯 开始最简单的Canvas测试');
      this.ctx.fillStyle = '#FF0000'; // 新版API使用fillStyle
      this.ctx.fillRect(10, 10, 50, 50);
      console.log('🎯 绘制了红色方块 (10,10,50,50) - 新版API');
    }

    // 🎯 绘制超明显的测试图案，确认Canvas位置
    if (this.isLegacyCanvas) {
      // 旧版API绘制方法
      // 绘制四个角的大标记（更大更明显）
      this.ctx.setFillStyle('rgba(255, 255, 0, 1)'); // 完全不透明的黄色
      this.ctx.fillRect(0, 0, 40, 40);           // 左上角 - 更大
      this.ctx.fillRect(width-40, 0, 40, 40);    // 右上角 - 更大
      this.ctx.fillRect(0, height-40, 40, 40);   // 左下角 - 更大
      this.ctx.fillRect(width-40, height-40, 40, 40); // 右下角 - 更大

      // 绘制中心大十字
      this.ctx.fillRect(width/2-20, height/2-5, 40, 10);  // 横线 - 更大
      this.ctx.fillRect(width/2-5, height/2-20, 10, 40);  // 竖线 - 更大

      // 绘制粗边框
      this.ctx.setStrokeStyle('rgba(255, 255, 0, 1)');
      this.ctx.setLineWidth(5); // 更粗的线
      this.ctx.strokeRect(5, 5, width-10, height-10);

      // 🎯 添加文字标记
      this.ctx.setFillStyle('rgba(255, 0, 0, 1)'); // 红色文字
      this.ctx.setFontSize(20);
      this.ctx.setTextAlign('center');
      this.ctx.setTextBaseline('middle');
      this.ctx.fillText('CANVAS TEST', width/2, height/2 + 40);
    } else {
      // 新版API绘制方法
      // 绘制四个角的大标记（更大更明显）
      this.ctx.fillStyle = 'rgba(255, 255, 0, 1)'; // 完全不透明的黄色
      this.ctx.fillRect(0, 0, 40, 40);           // 左上角 - 更大
      this.ctx.fillRect(width-40, 0, 40, 40);    // 右上角 - 更大
      this.ctx.fillRect(0, height-40, 40, 40);   // 左下角 - 更大
      this.ctx.fillRect(width-40, height-40, 40, 40); // 右下角 - 更大

      // 绘制中心大十字
      this.ctx.fillRect(width/2-20, height/2-5, 40, 10);  // 横线 - 更大
      this.ctx.fillRect(width/2-5, height/2-20, 10, 40);  // 竖线 - 更大

      // 绘制粗边框
      this.ctx.strokeStyle = 'rgba(255, 255, 0, 1)';
      this.ctx.lineWidth = 5; // 更粗的线
      this.ctx.strokeRect(5, 5, width-10, height-10);

      // 🎯 添加文字标记
      this.ctx.fillStyle = 'rgba(255, 0, 0, 1)'; // 红色文字
      this.ctx.font = 'bold 20px Arial';
      this.ctx.textAlign = 'center';
      this.ctx.textBaseline = 'middle';
      this.ctx.fillText('CANVAS TEST', width/2, height/2 + 40);
    }

    // 🎯 根据Canvas类型触发渲染
    try {
      if (this.isLegacyCanvas) {
        // 旧版Canvas API必须调用draw()才能显示
        this.ctx.draw(true);
        console.log('🎯 旧版Canvas draw()调用完成');
      } else {
        // 新版Canvas 2D可能需要其他方式触发渲染
        if (this.ctx.draw) {
          this.ctx.draw(true);
        }
        if (this.canvas && this.canvas.requestAnimationFrame) {
          this.canvas.requestAnimationFrame(() => {
            console.log('🎯 Canvas动画帧渲染完成');
          });
        }
      }
    } catch (error) {
      console.warn('⚠️ Canvas渲染触发失败:', error);
    }

    console.log('🎯 基础指示器绘制完成，应该看到黄色标记');
  }

  // 创建参数效果覆盖层（恢复正常工作版本）
  createParameterEffectOverlay(params) {
    // 🎯 使用显示尺寸
    const width = this.sizeInfo.displayWidth || this.canvasWidth || 369;
    const height = this.sizeInfo.displayHeight || this.canvasHeight || 270;

    // 🎯 每次清除Canvas，重新绘制所有参数效果
    this.ctx.clearRect(0, 0, width, height);

    // 创建参数预览效果
    this.createParameterPreviewAreas(params);

    // 添加参数信息显示
    this.drawParameterOverlay(params);
  }

  // 创建模拟的视频帧并应用参数算法（累积模式）
  createSimulatedFrame(params) {
    // 🎯 使用显示尺寸创建模拟视频帧
    const width = this.sizeInfo.displayWidth || this.canvasWidth || 369;
    const height = this.sizeInfo.displayHeight || this.canvasHeight || 270;

    // 🎯 不清除Canvas，实现参数累积效果
    // this.ctx.clearRect(0, 0, width, height); // 注释掉，保持累积效果

    // 创建参数效果预览区域（只在部分区域显示效果，不覆盖整个视频）
    this.createParameterPreviewAreas(params);

    // 显示参数调节提示
    this.drawParameterOverlay(params);
  }

  // 在Canvas内部创建参数预览效果（像蓝色背景一样）
  createParameterPreviewAreas(params) {
    // 🎯 使用显示尺寸而不是内部分辨率，确保绘制在正确的坐标系统中
    const width = this.sizeInfo.displayWidth || this.canvasWidth || 369;
    const height = this.sizeInfo.displayHeight || this.canvasHeight || 270;

    console.log('🎯 Canvas尺寸检查（修复版）:', {
      canvasInternalWidth: this.isLegacyCanvas ? this.canvasWidth : (this.canvas ? this.canvas.width : 'N/A'),
      canvasInternalHeight: this.isLegacyCanvas ? this.canvasHeight : (this.canvas ? this.canvas.height : 'N/A'),
      displayWidth: this.sizeInfo.displayWidth,
      displayHeight: this.sizeInfo.displayHeight,
      usedWidth: width,
      usedHeight: height,
      dpr: this.sizeInfo.dpr,
      isLegacyCanvas: this.isLegacyCanvas,
      canvasElement: this.canvas
    });

    // 检查是否有参数变化
    if (!this.hasParameterChanges(params)) {
      console.log('🎨 没有参数变化，清除Canvas内容');
      // 🎯 清除Canvas，使其完全透明
      this.ctx.clearRect(0, 0, width, height);
      if (this.isLegacyCanvas) {
        this.ctx.draw(true); // 旧版API需要调用draw()
      }
      return; // 没有参数变化，不显示预览
    }

    // 🎯 不需要重置检查，直接绘制参数效果

    // 像之前的蓝色背景一样，在整个Canvas区域显示参数效果
    this.createCanvasWideParameterEffect(params, width, height);
  }

  // 🎯 检查是否应该重置Canvas（当参数回到接近默认值时）
  shouldResetCanvas(params) {
    const defaultParams = {
      brightness: 115,
      contrast: 115,
      saturation: 106,
      white_balance_temperature: 4650,
      exposure_absolute: 1250,
      sharpness: 10,
      gain: 0
    };

    // 检查所有参数是否都接近默认值
    const tolerance = 2; // 允许的误差范围
    for (const [key, defaultValue] of Object.entries(defaultParams)) {
      const currentValue = params[key] || defaultValue;
      if (Math.abs(currentValue - defaultValue) > tolerance) {
        return false; // 还有参数偏离默认值
      }
    }

    // 如果需要重置，也重置累积参数
    this.resetAccumulatedParams();
    return true; // 所有参数都接近默认值，应该重置
  }

  // 🎯 重置累积参数到默认值
  resetAccumulatedParams() {
    this.accumulatedParams = {
      brightness: 115,
      contrast: 115,
      saturation: 106,
      white_balance_temperature: 4650,
      exposure_absolute: 1250,
      sharpness: 10,
      gain: 0
    };
    console.log('🎯 累积参数已重置到默认值');
  }

  // 在Canvas上创建参数效果的算法模拟绘制
  createCanvasWideParameterEffect(params, width, height) {
    console.log('🎯 绘制参数效果算法模拟:', {
      canvasSize: `${width}x${height}`,
      params: params
    });

    // 🎯 不清除Canvas，让参数效果能够叠加
    // 只在最开始清除一次（在createParameterEffectOverlay中已经清除）

    // 🎯 在透明背景上绘制参数效果的视觉指示器
    this.drawParameterVisualIndicators(params, width, height);

    // 🎯 最后调用draw()（旧版API需要）
    if (this.isLegacyCanvas) {
      this.ctx.draw(true);
      console.log('🎯 累积参数算法模拟绘制完成');
    }
  }

  // 🎯 新方法：绘制参数效果的视觉指示器（累积算法模拟）
  drawParameterVisualIndicators(params, width, height) {
    // 🎯 创建基础图像数据进行累积处理
    this.drawCumulativeParameterEffects(params, width, height);

    // 🎯 绘制参数信息文字（在最后，避免被覆盖）
    this.drawParameterInfo(params, width, height);
  }

  // 🎯 绘制简单的参数效果指示（实用方案）
  drawCumulativeParameterEffects(params, width, height) {
    console.log('🎯 绘制参数效果指示:', params);

    // 🎯 计算所有参数的综合效果
    const effects = this.calculateParameterEffects(params);

    // 🎯 绘制综合效果
    this.drawSimpleParameterIndicator(effects, width, height);

    // 🎯 绘制自动功能指示器
    this.drawAutoFunctionIndicators(params, width, height);
  }

  // 🎯 计算参数效果（完全符合后端算法）
  calculateParameterEffects(params) {
    const effects = {
      brightness: 0,
      contrast: 1,
      saturation: 1,
      colorShift: { r: 0, g: 0, b: 0 },
      gainFactor: 1,
      exposureFactor: 1,
      sharpnessFactor: 0,
      autoWhiteBalance: false,
      powerLineFrequency: 2,
      hasChanges: false
    };

    // 🎯 亮度变化 - 完全符合后端算法
    if ((params.brightness || 115) !== 115) {
      // 正确算法：将0-230范围映射到-1.0到1.0，然后转换为0-255范围
      // 115是中点(0)，0对应-1.0，230对应1.0
      const brightnessFFmpeg = (params.brightness - 115) / 115; // FFmpeg值 -1.0到1.0
      effects.brightness = brightnessFFmpeg * 255; // 转换为0-255范围的变化量
      effects.hasChanges = true;
      console.log(`🎯 亮度: ${params.brightness} → FFmpeg: ${brightnessFFmpeg.toFixed(3)} → 前端: ${effects.brightness.toFixed(1)}`);
    }

    // 🎯 对比度变化 - 完全符合后端算法
    if ((params.contrast || 115) !== 115) {
      // 正确算法：将0-230范围映射到0.0到2.0
      // 115是中点(1.0)，0对应0.0，230对应2.0
      effects.contrast = (params.contrast || 115) / 115; // FFmpeg值 0.0到2.0
      effects.hasChanges = true;
      console.log(`🎯 对比度: ${params.contrast} → FFmpeg: ${effects.contrast.toFixed(3)}`);
    }

    // 🎯 饱和度变化 - 完全符合后端算法
    if ((params.saturation || 106) !== 106) {
      // 正确映射：106→1.0, 0→0.0, 212→2.0 (范围限制到2.0)
      effects.saturation = Math.max(0.0, Math.min(2.0, (params.saturation || 106) / 106));
      effects.hasChanges = true;
      console.log(`🎯 饱和度: ${params.saturation} → ${effects.saturation.toFixed(3)}`);
    }

    // 🎯 色温变化 - 完全符合后端算法
    if ((params.white_balance_temperature || 4650) !== 4650) {
      const temperature = params.white_balance_temperature || 4650;
      const tempFactor = (temperature - 4650) / 1950; // 与后端完全一致

      // 使用与后端相同的colorbalance算法
      if (tempFactor < 0) {
        // 暖色调：增加红色，减少蓝色
        const redShift = Math.max(-1.0, Math.min(1.0, Math.abs(tempFactor) * 0.5));
        const blueShift = Math.max(-1.0, Math.min(1.0, -Math.abs(tempFactor) * 0.3));
        effects.colorShift.r = redShift * 128;
        effects.colorShift.b = blueShift * 128;
      } else {
        // 冷色调：增加蓝色，减少红色
        const blueShift = Math.max(-1.0, Math.min(1.0, tempFactor * 0.5));
        const redShift = Math.max(-1.0, Math.min(1.0, -tempFactor * 0.3));
        effects.colorShift.r = redShift * 128;
        effects.colorShift.b = blueShift * 128;
      }
      effects.hasChanges = true;
      console.log(`🎯 色温: ${temperature}K → r=${effects.colorShift.r.toFixed(1)}, b=${effects.colorShift.b.toFixed(1)}`);
    }

    // 🎯 增益变化 - 完全符合后端算法（独立效果 + 合并到亮度中）
    if ((params.gain || 0) !== 0) {
      // 后端算法：gainValue = (gain / 100 * 0.5)，然后加到亮度中
      const gainValue = (params.gain / 100 * 0.5);
      effects.gainFactor = 1 + gainValue; // 独立的增益因子用于透明覆盖层
      effects.brightness += gainValue * 255; // 转换为0-255范围并加到亮度中
      effects.hasChanges = true;
      console.log(`🎯 增益: ${params.gain} → 增益值: ${gainValue.toFixed(3)} → 因子: ${effects.gainFactor.toFixed(3)} → 加到亮度: ${(gainValue * 255).toFixed(1)}`);
    }

    // 🎯 曝光变化 - 完全符合后端算法
    if ((params.exposure_absolute || 1250) !== 1250) {
      const exposure = params.exposure_absolute || 1250;
      const gamma = Math.log(exposure / 1250) / Math.log(2) + 1; // 与后端完全一致
      effects.exposureFactor = Math.max(0.1, Math.min(10.0, gamma)); // 与后端范围一致：0.1到10.0
      effects.hasChanges = true;
      console.log(`🎯 曝光: ${exposure} → gamma: ${effects.exposureFactor.toFixed(3)}`);
    }

    // 🎯 锐度变化 - 完全符合后端算法
    if ((params.sharpness || 10) !== 10) {
      // 正确映射：10→0(无变化), 0→-1.0(模糊), 20→1.0(锐化)
      const sharpness = params.sharpness || 10;
      const factor = (sharpness - 10) / 10; // 真实算法公式
      effects.sharpnessFactor = Math.max(-1.0, Math.min(1.0, factor));
      effects.hasChanges = true;
      console.log(`🎯 锐度: ${sharpness} → ${effects.sharpnessFactor.toFixed(3)}`);
    }

    // 🎯 自动白平衡 - 完全符合后端算法
    if ((params.white_balance_temperature_auto || 0) !== 0) {
      // 自动白平衡：应用固定的色彩平衡效果（与后端一致）
      effects.autoWhiteBalance = true; // 标记自动白平衡开启
      effects.colorShift.r += 0.1 * 128; // rs=0.1 转换为0-255范围
      effects.colorShift.g += 0.0 * 128; // gs=0.0
      effects.colorShift.b += -0.1 * 128; // bs=-0.1 转换为0-255范围
      effects.hasChanges = true;
      console.log(`🎯 自动白平衡: 开启 → r=${(0.1 * 128).toFixed(1)}, b=${(-0.1 * 128).toFixed(1)}`);
    }

    // 🎯 自动曝光 - 在参数预览中不显示自动曝光效果
    if ((params.exposure_auto || 3) === 3) {
      // 自动曝光：模拟histeq直方图均衡化效果
      // 在参数预览中，我们不显示自动曝光的效果，避免干扰用户调整的参数
      // effects.exposureFactor = 0.9; // 注释掉，不在预览中显示
      // effects.hasChanges = true;
      console.log(`🎯 自动曝光: 开启 → 在参数预览中不显示，避免干扰用户参数`);
    }

    // 🎯 电力线频率 - 完全符合后端算法
    if ((params.power_line_frequency || 2) !== 2) {
      // 电力线频率主要影响闪烁，对颜色影响较小，但仍然标记为有变化
      effects.powerLineFrequency = params.power_line_frequency || 2;
      effects.hasChanges = true;
      console.log(`🎯 电力线频率: ${params.power_line_frequency} (默认2)`);
    }

    console.log('🎯 计算的参数效果（符合后端算法）:', effects);
    return effects;
  }

  // 🎯 绘制基于FFmpeg算法的透明覆盖层效果
  drawSimpleParameterIndicator(effects, width, height) {
    if (!effects.hasChanges) {
      console.log('🎯 没有参数变化，保持透明');
      return; // 没有变化，保持透明
    }

    console.log('🎯 开始绘制基于FFmpeg算法的透明覆盖层:', effects);

    // 🎯 使用FFmpeg算法计算参数效果，然后转换为透明覆盖层
    this.drawFFmpegBasedTransparentOverlay(effects, width, height);

    // 🎯 旧版Canvas API需要调用draw()才能显示
    if (this.isLegacyCanvas) {
      this.ctx.draw(true);
    }

    console.log('🎯 基于FFmpeg算法的透明覆盖层绘制完成');
  }

  // 🎯 绘制基于FFmpeg算法的透明覆盖层
  drawFFmpegBasedTransparentOverlay(effects, width, height) {
    console.log('🎯 开始绘制基于FFmpeg算法的透明覆盖层:', effects);

    // 🎯 按照FFmpeg处理顺序定义参数层（固定顺序）
    const parameterLayers = [
      { name: '亮度', condition: effects.brightness !== 0, drawFunc: () => this.drawFFmpegBrightnessOverlay(effects.brightness, width, height) },
      { name: '增益', condition: effects.gainFactor !== 1, drawFunc: () => this.drawFFmpegGainOverlay(effects.gainFactor, width, height) },
      { name: '对比度', condition: effects.contrast !== 1, drawFunc: () => this.drawFFmpegContrastOverlay(effects.contrast, width, height) },
      { name: '饱和度', condition: effects.saturation !== 1, drawFunc: () => this.drawFFmpegSaturationOverlay(effects.saturation, width, height) },
      { name: '色温', condition: effects.colorShift.r !== 0 || effects.colorShift.b !== 0, drawFunc: () => this.drawFFmpegColorTemperatureOverlay(effects.colorShift, width, height) },
      { name: '自动白平衡', condition: effects.autoWhiteBalance, drawFunc: () => this.drawFFmpegAutoWhiteBalanceOverlay(width, height) },
      { name: '曝光', condition: effects.exposureFactor !== 1, drawFunc: () => this.drawFFmpegExposureOverlay(effects.exposureFactor, width, height) },
      { name: '锐度', condition: effects.sharpnessFactor !== 0, drawFunc: () => this.drawFFmpegSharpnessOverlay(effects.sharpnessFactor, width, height) },
      { name: '电力线频率', condition: effects.powerLineFrequency !== 2, drawFunc: () => this.drawFFmpegPowerLineFrequencyOverlay(effects.powerLineFrequency, width, height) }
    ];

    // 🎯 过滤出需要绘制的参数（保持FFmpeg顺序）
    const activeLayers = parameterLayers.filter(layer => layer.condition);

    if (activeLayers.length === 0) {
      console.log('🎯 没有活跃的参数层');
      return;
    }

    // 🎯 双层融合绘制透明覆盖层
    this.drawTwoLayerFusionOverlay(activeLayers, width, height);

    console.log(`🎯 基于FFmpeg算法的透明覆盖层绘制完成，处理了${activeLayers.length}个参数`);
  }

  // 🎯 正确的双层融合绘制（绘制所有参数）
  drawTwoLayerFusionOverlay(activeLayers, width, height) {
    if (activeLayers.length === 0) {
      console.log('🎯 没有活跃的参数层');
      return;
    }

    if (activeLayers.length === 1) {
      // 只有一个参数，直接绘制
      console.log(`🎯 绘制单个参数层：${activeLayers[0].name}`);
      activeLayers[0].drawFunc();
    } else {
      // 多个参数，按双层融合逻辑绘制
      console.log(`🎯 初始化第一层：${activeLayers[0].name}`);
      activeLayers[0].drawFunc();

      // 后续参数逐个作为第二层，与第一层融合
      for (let i = 1; i < activeLayers.length; i++) {
        const currentLayer = activeLayers[i];
        console.log(`🎯 添加第二层：${currentLayer.name}，准备与第一层融合`);

        currentLayer.drawFunc();

        console.log(`🎯 融合完成，${currentLayer.name}已成为新的第一层`);
      }
    }
  }

  // 🎯 基于FFmpeg亮度算法的透明覆盖层（真实算法模拟）
  drawFFmpegBrightnessOverlay(brightnessChange, width, height) {
    // 使用FFmpeg亮度算法：newPixel = originalPixel + brightnessChange
    // 透明覆盖层直接使用亮度变化值作为RGB值

    if (brightnessChange > 0) {
      // 增亮：直接用亮度变化值作为白色的强度
      const clampedBrightness = Math.min(255, Math.abs(brightnessChange));
      const alpha = Math.min(1.0, clampedBrightness / 255);
      this.fillLayer(`rgba(${clampedBrightness}, ${clampedBrightness}, ${clampedBrightness}, ${alpha})`, width, height);
      console.log(`🎯 FFmpeg亮度覆盖层(增亮): RGB(${clampedBrightness}), alpha=${alpha.toFixed(3)}`);
    } else if (brightnessChange < 0) {
      // 变暗：使用黑色，透明度基于变化量
      const darknessFactor = Math.abs(brightnessChange) / 255;
      const alpha = Math.min(0.8, darknessFactor);
      this.fillLayer(`rgba(0, 0, 0, ${alpha})`, width, height);
      console.log(`🎯 FFmpeg亮度覆盖层(变暗): 变化=${brightnessChange.toFixed(1)}, alpha=${alpha.toFixed(3)}`);
    }
  }

  // 🎯 基于FFmpeg对比度算法的透明覆盖层（真实算法模拟）
  drawFFmpegContrastOverlay(contrastFactor, width, height) {
    // FFmpeg对比度算法：newPixel = (originalPixel - 128) * factor + 128
    // 模拟这个效果：对比度增强让明暗差异更大，对比度降低让图像更平淡

    const intensity = Math.abs(contrastFactor - 1);

    if (contrastFactor > 1) {
      // 对比度增强：创建渐变效果模拟明暗对比增强
      // 1. 基础明亮层：模拟整体对比度提升
      const contrastBoost = (contrastFactor - 1) * 50; // 转换为RGB值
      const baseAlpha = Math.min(0.3, intensity * 0.8);
      this.fillLayer(`rgba(${255 - contrastBoost}, ${255 - contrastBoost}, ${255 - contrastBoost}, ${baseAlpha})`, width, height);

      // 2. 网格层：模拟边缘和结构增强
      const gridAlpha = Math.min(0.15, intensity * 0.6);
      this.drawContrastGrid(gridAlpha, width, height);

      console.log(`🎯 FFmpeg对比度覆盖层(增强): 因子=${contrastFactor.toFixed(3)}, 对比度提升=${contrastBoost.toFixed(1)}, 基础alpha=${baseAlpha.toFixed(3)}`);
    } else if (contrastFactor < 1) {
      // 对比度降低：用灰色层模拟图像变平淡
      // 灰度值基于对比度因子计算
      const grayLevel = 128 + (1 - contrastFactor) * 30; // 对比度越低，灰度越高
      const alpha = Math.min(0.4, intensity * 1.2);
      this.fillLayer(`rgba(${grayLevel}, ${grayLevel}, ${grayLevel}, ${alpha})`, width, height);

      console.log(`🎯 FFmpeg对比度覆盖层(降低): 因子=${contrastFactor.toFixed(3)}, 灰度=${grayLevel.toFixed(1)}, alpha=${alpha.toFixed(3)}`);
    }
  }

  // 🎯 基于FFmpeg饱和度算法的透明覆盖层（真实算法模拟）
  drawFFmpegSaturationOverlay(saturationFactor, width, height) {
    // FFmpeg饱和度算法：gray + saturationFactor * (original - gray)
    // 模拟这个效果：饱和度增强让颜色更鲜艳，饱和度降低让颜色更灰

    const intensity = Math.abs(saturationFactor - 1);

    if (saturationFactor > 1) {
      // 饱和度增强：用鲜艳的颜色覆盖层模拟色彩增强
      const saturationBoost = (saturationFactor - 1) * 100; // 转换为颜色强度
      const red = Math.min(255, 200 + saturationBoost);
      const green = Math.min(255, 100 + saturationBoost * 0.5);
      const blue = Math.min(255, 50 + saturationBoost * 0.3);
      const alpha = Math.min(0.3, intensity * 0.8);

      this.fillLayer(`rgba(${red}, ${green}, ${blue}, ${alpha})`, width, height);
      console.log(`🎯 FFmpeg饱和度覆盖层(增强): 因子=${saturationFactor.toFixed(3)}, RGB(${red.toFixed(0)},${green.toFixed(0)},${blue.toFixed(0)}), alpha=${alpha.toFixed(3)}`);
    } else if (saturationFactor < 1) {
      // 饱和度降低：用灰色层模拟去色效果
      // 灰度值基于饱和度因子计算，越低越灰
      const desaturationLevel = (1 - saturationFactor) * 60;
      const grayValue = 128 + desaturationLevel;
      const alpha = Math.min(0.4, intensity * 1.2);

      this.fillLayer(`rgba(${grayValue}, ${grayValue}, ${grayValue}, ${alpha})`, width, height);
      console.log(`🎯 FFmpeg饱和度覆盖层(降低): 因子=${saturationFactor.toFixed(3)}, 灰度=${grayValue.toFixed(1)}, alpha=${alpha.toFixed(3)}`);
    }
  }

  // 🎯 基于FFmpeg色温算法的透明覆盖层（真实算法模拟）
  drawFFmpegColorTemperatureOverlay(colorShift, width, height) {
    // FFmpeg colorbalance算法：直接调整RGB通道
    // 透明覆盖层直接使用colorShift的RGB值

    const maxShift = Math.max(Math.abs(colorShift.r), Math.abs(colorShift.g), Math.abs(colorShift.b));
    const intensity = maxShift / 128;
    const alpha = Math.min(0.4, intensity * 1.5);

    // 基于真实colorShift值计算覆盖层颜色
    const baseR = 128, baseG = 128, baseB = 128; // 中性基础色
    const adjustedR = Math.max(0, Math.min(255, baseR + colorShift.r));
    const adjustedG = Math.max(0, Math.min(255, baseG + colorShift.g));
    const adjustedB = Math.max(0, Math.min(255, baseB + colorShift.b));

    this.fillLayer(`rgba(${adjustedR}, ${adjustedG}, ${adjustedB}, ${alpha})`, width, height);

    const colorType = colorShift.r > 0 ? '暖色' : (colorShift.b > 0 ? '冷色' : '中性');
    console.log(`🎯 FFmpeg色温覆盖层(${colorType}): RGB(${adjustedR.toFixed(0)},${adjustedG.toFixed(0)},${adjustedB.toFixed(0)}), alpha=${alpha.toFixed(3)}`);
  }

  // 🎯 基于FFmpeg曝光算法的透明覆盖层（真实算法模拟）
  drawFFmpegExposureOverlay(exposureFactor, width, height) {
    // FFmpeg曝光算法：gamma校正 output = 255 * (input/255)^(1/gamma)
    // 模拟gamma校正对中性灰的影响

    const intensity = Math.abs(exposureFactor - 1);
    const alpha = Math.min(0.4, intensity * 1.5);

    // 使用gamma校正算法计算覆盖层颜色
    const basePixel = 128; // 中性灰
    const gammaCorrection = 1 / exposureFactor;
    const adjustedPixel = 255 * Math.pow(basePixel / 255, gammaCorrection);

    // 计算需要叠加的颜色来达到这个效果
    const overlayValue = Math.max(0, Math.min(255, adjustedPixel));

    this.fillLayer(`rgba(${overlayValue}, ${overlayValue}, ${overlayValue}, ${alpha})`, width, height);

    const exposureType = exposureFactor > 1 ? '增加' : '降低';
    console.log(`🎯 FFmpeg曝光覆盖层(${exposureType}): 因子=${exposureFactor.toFixed(3)}, gamma校正值=${overlayValue.toFixed(1)}, alpha=${alpha.toFixed(3)}`);
  }

  // 🎯 基于FFmpeg锐度算法的透明覆盖层（真实算法模拟）
  drawFFmpegSharpnessOverlay(sharpnessFactor, width, height) {
    // FFmpeg锐度算法：边缘增强卷积核处理
    // 模拟锐化对图像边缘和细节的影响

    const intensity = Math.abs(sharpnessFactor);
    const alpha = Math.min(0.3, intensity * 1.5);

    if (sharpnessFactor > 0) {
      // 锐化：模拟边缘增强效果
      // 锐化会增强边缘对比，用亮色模拟
      const sharpnessBoost = sharpnessFactor * 20; // 转换为亮度增强
      const overlayValue = Math.min(255, 128 + sharpnessBoost);

      // 1. 基础增强层
      this.fillLayer(`rgba(${overlayValue}, ${overlayValue}, ${overlayValue}, ${alpha})`, width, height);
      // 2. 边缘线条层
      this.drawSharpnessLines(alpha * 1.5, width, height);

      console.log(`🎯 FFmpeg锐度覆盖层(锐化): 因子=${sharpnessFactor.toFixed(3)}, 增强值=${overlayValue.toFixed(1)}, alpha=${alpha.toFixed(3)}`);
    } else if (sharpnessFactor < 0) {
      // 模糊：模拟细节丢失效果
      // 模糊会减少细节，用灰色模拟
      const blurReduction = Math.abs(sharpnessFactor) * 15;
      const overlayValue = Math.max(0, 128 - blurReduction);

      this.fillLayer(`rgba(${overlayValue}, ${overlayValue}, ${overlayValue}, ${alpha})`, width, height);
      console.log(`🎯 FFmpeg锐度覆盖层(模糊): 因子=${sharpnessFactor.toFixed(3)}, 模糊值=${overlayValue.toFixed(1)}, alpha=${alpha.toFixed(3)}`);
    }
  }

  // 🎯 基于FFmpeg增益算法的透明覆盖层（真实算法模拟）
  drawFFmpegGainOverlay(gainFactor, width, height) {
    // FFmpeg增益算法：增益会影响整体亮度
    // 增益因子 = 1 + (gain / 100 * 0.5)

    const intensity = Math.abs(gainFactor - 1);
    const alpha = Math.min(0.3, intensity * 2);

    if (gainFactor > 1) {
      // 增益增加：模拟信号放大效果
      const gainBoost = (gainFactor - 1) * 100; // 转换为亮度增强
      const overlayValue = Math.min(255, 128 + gainBoost);

      this.fillLayer(`rgba(${overlayValue}, ${overlayValue}, ${overlayValue}, ${alpha})`, width, height);
      console.log(`🎯 FFmpeg增益覆盖层(增加): 因子=${gainFactor.toFixed(3)}, 增强值=${overlayValue.toFixed(1)}, alpha=${alpha.toFixed(3)}`);
    } else if (gainFactor < 1) {
      // 增益降低：模拟信号衰减效果
      const gainReduction = (1 - gainFactor) * 80;
      const overlayValue = Math.max(0, 128 - gainReduction);

      this.fillLayer(`rgba(${overlayValue}, ${overlayValue}, ${overlayValue}, ${alpha})`, width, height);
      console.log(`🎯 FFmpeg增益覆盖层(降低): 因子=${gainFactor.toFixed(3)}, 衰减值=${overlayValue.toFixed(1)}, alpha=${alpha.toFixed(3)}`);
    }
  }

  // 🎯 基于FFmpeg自动白平衡算法的透明覆盖层（真实算法模拟）
  drawFFmpegAutoWhiteBalanceOverlay(width, height) {
    // FFmpeg自动白平衡：固定的colorbalance调整 rs=0.1, gs=0.0, bs=-0.1
    // 模拟轻微的暖色调整

    const alpha = 0.2; // 固定透明度，因为是固定的调整值
    const warmR = 128 + 0.1 * 128; // 增加红色
    const neutralG = 128; // 绿色不变
    const coolB = 128 - 0.1 * 128; // 减少蓝色

    this.fillLayer(`rgba(${warmR}, ${neutralG}, ${coolB}, ${alpha})`, width, height);
    console.log(`🎯 FFmpeg自动白平衡覆盖层: RGB(${warmR.toFixed(1)},${neutralG},${coolB.toFixed(1)}), alpha=${alpha.toFixed(3)}`);
  }

  // 🎯 基于FFmpeg电力线频率算法的透明覆盖层（真实算法模拟）
  drawFFmpegPowerLineFrequencyOverlay(frequency, width, height) {
    // FFmpeg电力线频率：主要影响防闪烁，对图像颜色影响很小
    // 用微妙的闪烁效果模拟

    const alpha = 0.1; // 很低的透明度，因为影响很小
    let overlayColor;

    switch (frequency) {
      case 0: // 禁用
        overlayColor = `rgba(128, 128, 128, ${alpha})`;
        break;
      case 1: // 50Hz
        overlayColor = `rgba(120, 128, 135, ${alpha})`;
        break;
      case 2: // 60Hz (默认)
      default:
        return; // 默认值，不绘制覆盖层
    }

    this.fillLayer(overlayColor, width, height);

    const freqText = frequency === 0 ? '禁用' : (frequency === 1 ? '50Hz' : '60Hz');
    console.log(`🎯 FFmpeg电力线频率覆盖层(${freqText}): 频率=${frequency}, alpha=${alpha.toFixed(3)}`);
  }

  // 🎯 通用填充层方法
  fillLayer(color, width, height) {
    if (this.isLegacyCanvas) {
      this.ctx.setFillStyle(color);
    } else {
      this.ctx.fillStyle = color;
    }
    this.ctx.fillRect(0, 0, width, height);
  }

  // 🎯 对比度网格效果
  drawContrastGrid(alpha, width, height) {
    if (this.isLegacyCanvas) {
      this.ctx.setStrokeStyle(`rgba(255, 255, 255, ${alpha})`);
      this.ctx.setLineWidth(1);
    } else {
      this.ctx.strokeStyle = `rgba(255, 255, 255, ${alpha})`;
      this.ctx.lineWidth = 1;
    }

    for (let i = 0; i < width; i += 80) {
      this.ctx.beginPath();
      this.ctx.moveTo(i, 0);
      this.ctx.lineTo(i, height);
      this.ctx.stroke();
    }
    for (let i = 0; i < height; i += 80) {
      this.ctx.beginPath();
      this.ctx.moveTo(0, i);
      this.ctx.lineTo(width, i);
      this.ctx.stroke();
    }
  }

  // 🎯 锐度线条效果
  drawSharpnessLines(alpha, width, height) {
    if (this.isLegacyCanvas) {
      this.ctx.setStrokeStyle(`rgba(255, 255, 255, ${alpha})`);
      this.ctx.setLineWidth(1);
    } else {
      this.ctx.strokeStyle = `rgba(255, 255, 255, ${alpha})`;
      this.ctx.lineWidth = 1;
    }

    this.ctx.beginPath();
    this.ctx.moveTo(0, 0);
    this.ctx.lineTo(width, height);
    this.ctx.moveTo(width, 0);
    this.ctx.lineTo(0, height);
    this.ctx.stroke();
  }

  // 🎯 已删除测试图像相关代码，只使用透明覆盖层

  // 🎯 绘制自动功能指示器（UI元素，不影响图像处理）
  drawAutoFunctionIndicators(params, width, height) {
    // 自动白平衡指示器
    if ((params.white_balance_temperature_auto || 0) !== 0) {
      this.drawAutoWhiteBalanceIndicator(width, height);
    }

    // 自动曝光指示器
    if ((params.exposure_auto || 3) !== 3) {
      this.drawAutoExposureIndicator(params.exposure_auto || 3, width, height);
    }

    // 电力线频率指示器（特殊UI）
    if ((params.power_line_frequency || 2) !== 2) {
      this.drawPowerLineFrequencyIndicator(params.power_line_frequency || 2, width, height);
    }
  }

  // 🎯 绘制亮度指示器（基于真实算法）
  drawBrightnessIndicator(brightnessChange, width, height) {
    // 🎯 使用真实的亮度算法：factor = (brightness - 115) * 2
    // brightnessChange是相对于115的变化量
    const brightness = 115 + brightnessChange; // 当前亮度值
    const factor = (brightness - 115) * 2; // 真实算法公式

    if (factor === 0) return; // 没有变化

    // 模拟算法效果：对基础颜色应用亮度调整
    const baseR = 128, baseG = 128, baseB = 128; // 中性灰作为基础
    const adjustedR = Math.max(0, Math.min(255, baseR + factor));
    const adjustedG = Math.max(0, Math.min(255, baseG + factor));
    const adjustedB = Math.max(0, Math.min(255, baseB + factor));

    const alpha = Math.min(0.4, Math.abs(factor) / 130); // 基于真实变化量
    const color = `rgba(${Math.round(adjustedR)}, ${Math.round(adjustedG)}, ${Math.round(adjustedB)}, ${alpha})`;

    this.drawGradientOverlay(color, width, height, 'brightness');
  }

  // 🎯 绘制对比度指示器（基于真实算法）
  drawContrastIndicator(contrastChange, width, height) {
    // 🎯 使用真实的对比度算法：factor = (contrast / 115) * 2
    const contrast = 115 + contrastChange; // 当前对比度值
    const factor = (contrast / 115) * 2; // 真实算法公式

    if (factor === 2) return; // 没有变化（默认factor=2）

    // 模拟算法效果：对基础颜色应用对比度调整
    const baseR = 128, baseG = 128, baseB = 128; // 中性灰作为基础
    const adjustedR = Math.max(0, Math.min(255, (baseR - 128) * factor + 128));
    const adjustedG = Math.max(0, Math.min(255, (baseG - 128) * factor + 128));
    const adjustedB = Math.max(0, Math.min(255, (baseB - 128) * factor + 128));

    const alpha = Math.min(0.4, Math.abs(factor - 2) / 2); // 基于真实变化量
    const color = `rgba(${Math.round(adjustedR)}, ${Math.round(adjustedG)}, ${Math.round(adjustedB)}, ${alpha})`;

    this.drawGradientOverlay(color, width, height, 'contrast');
  }

  // 🎯 绘制饱和度指示器（基于真实算法）
  drawSaturationIndicator(saturationChange, width, height) {
    // 🎯 使用真实的饱和度算法：factor = saturation / 106
    const saturation = 106 + saturationChange; // 当前饱和度值
    const factor = saturation / 106; // 真实算法公式

    if (factor === 1) return; // 没有变化

    // 模拟算法效果：对彩色基础应用饱和度调整
    const baseR = 200, baseG = 100, baseB = 50; // 有色彩的基础色
    const gray = 0.299 * baseR + 0.587 * baseG + 0.114 * baseB; // 灰度值

    const adjustedR = Math.max(0, Math.min(255, gray + factor * (baseR - gray)));
    const adjustedG = Math.max(0, Math.min(255, gray + factor * (baseG - gray)));
    const adjustedB = Math.max(0, Math.min(255, gray + factor * (baseB - gray)));

    const alpha = Math.min(0.4, Math.abs(factor - 1) * 0.8); // 基于真实变化量
    const color = `rgba(${Math.round(adjustedR)}, ${Math.round(adjustedG)}, ${Math.round(adjustedB)}, ${alpha})`;

    this.drawGradientOverlay(color, width, height, 'saturation');
  }

  // 🎯 绘制渐变覆盖层
  drawGradientOverlay(color, width, height, type) {
    if (this.isLegacyCanvas) {
      // 旧版API：简单填充
      this.ctx.setFillStyle(color);
      this.ctx.fillRect(0, 0, width, height);
    } else {
      // 新版API：可以使用渐变
      this.ctx.fillStyle = color;
      this.ctx.fillRect(0, 0, width, height);
    }
  }

  // 🎯 绘制对比度增强效果
  drawContrastEnhancement(width, height, intensity) {
    const alpha = Math.min(0.3, intensity * 0.5);

    // 绘制边缘线条模拟对比度增强
    if (this.isLegacyCanvas) {
      this.ctx.setStrokeStyle(`rgba(255, 255, 255, ${alpha})`);
      this.ctx.setLineWidth(2);
    } else {
      this.ctx.strokeStyle = `rgba(255, 255, 255, ${alpha})`;
      this.ctx.lineWidth = 2;
    }

    // 绘制网格线模拟对比度
    for (let i = 0; i < width; i += 40) {
      this.ctx.beginPath();
      this.ctx.moveTo(i, 0);
      this.ctx.lineTo(i, height);
      this.ctx.stroke();
    }

    for (let i = 0; i < height; i += 40) {
      this.ctx.beginPath();
      this.ctx.moveTo(0, i);
      this.ctx.lineTo(width, i);
      this.ctx.stroke();
    }
  }

  // 🎯 绘制对比度减少效果
  drawContrastReduction(width, height, intensity) {
    const alpha = Math.min(0.4, intensity * 0.6);
    const color = `rgba(128, 128, 128, ${alpha})`;
    this.drawGradientOverlay(color, width, height, 'contrast-reduce');
  }

  // 🎯 绘制彩色渐变（饱和度增加）
  drawColorfulGradient(width, height, alpha) {
    // 绘制彩色条纹模拟饱和度增加
    const colors = [
      `rgba(255, 0, 0, ${alpha})`,   // 红
      `rgba(255, 165, 0, ${alpha})`, // 橙
      `rgba(255, 255, 0, ${alpha})`, // 黄
      `rgba(0, 255, 0, ${alpha})`,   // 绿
      `rgba(0, 0, 255, ${alpha})`,   // 蓝
      `rgba(128, 0, 128, ${alpha})`  // 紫
    ];

    const stripeWidth = width / colors.length;

    colors.forEach((color, index) => {
      if (this.isLegacyCanvas) {
        this.ctx.setFillStyle(color);
      } else {
        this.ctx.fillStyle = color;
      }
      this.ctx.fillRect(index * stripeWidth, 0, stripeWidth, height);
    });
  }

  // 🎯 绘制参数信息文字
  drawParameterInfo(params, width, height) {
    const changedParams = [];

    // 收集所有变化的参数（使用真正的默认值）
    if ((params.brightness || 115) !== 115) changedParams.push(`亮度: ${params.brightness || 115}`);
    if ((params.contrast || 115) !== 115) changedParams.push(`对比度: ${params.contrast || 115}`);
    if ((params.saturation || 106) !== 106) changedParams.push(`饱和度: ${params.saturation || 106}`);
    if ((params.white_balance_temperature || 4650) !== 4650) changedParams.push(`色温: ${params.white_balance_temperature || 4650}K`);
    if ((params.gain || 0) !== 0) changedParams.push(`增益: ${params.gain || 0}`);
    if ((params.exposure_absolute || 1250) !== 1250) changedParams.push(`曝光: ${params.exposure_absolute || 1250}`);
    if ((params.sharpness || 10) !== 10) changedParams.push(`锐度: ${params.sharpness || 10}`);
    if ((params.white_balance_temperature_auto || 0) !== 0) changedParams.push(`自动白平衡: 开启`);
    if ((params.exposure_auto || 3) !== 3) changedParams.push(`自动曝光: ${params.exposure_auto === 1 ? '手动' : '自动'}`);
    if ((params.power_line_frequency || 2) !== 2) {
      const freqText = params.power_line_frequency === 0 ? '禁用' : (params.power_line_frequency === 1 ? '50Hz' : '60Hz');
      changedParams.push(`电力线频率: ${freqText}`);
    }

    if (changedParams.length > 0) {
      if (this.isLegacyCanvas) {
        this.ctx.setFillStyle('rgba(255, 255, 255, 0.9)');
        this.ctx.setFontSize(10);
        this.ctx.setTextAlign('center');
        this.ctx.setTextBaseline('top');
      } else {
        this.ctx.fillStyle = 'rgba(255, 255, 255, 0.9)';
        this.ctx.font = '10px Arial';
        this.ctx.textAlign = 'center';
        this.ctx.textBaseline = 'top';
      }

      // 在Canvas底部显示参数信息（显示更多参数，字体更小）
      const textY = height - 80;
      const maxParams = Math.min(5, changedParams.length); // 最多显示5个参数

      for (let i = 0; i < maxParams; i++) {
        this.ctx.fillText(changedParams[i], width/2, textY + i * 14);
      }

      // 如果参数太多，显示省略号
      if (changedParams.length > 5) {
        this.ctx.fillText(`...还有${changedParams.length - 5}个参数`, width/2, textY + 5 * 14);
      }
    }
  }

  // 🎯 绘制白平衡指示器（基于真实算法）
  drawWhiteBalanceIndicator(whiteBalanceChange, width, height) {
    if (whiteBalanceChange === 0) return; // 默认值不处理

    // 🎯 使用真实的色温算法
    const temperature = 4650 + whiteBalanceChange; // 真实色温值
    const tempFactor = (temperature - 4650) / 1950; // 真实算法公式
    const redFactor = tempFactor < 0 ? 1 + Math.abs(tempFactor) * 0.3 : 1;
    const blueFactor = tempFactor > 0 ? 1 + tempFactor * 0.3 : 1;

    // 模拟算法效果：对基础颜色应用白平衡调整
    const baseR = 128, baseG = 128, baseB = 128;
    const adjustedR = Math.max(0, Math.min(255, baseR * redFactor));
    const adjustedB = Math.max(0, Math.min(255, baseB * blueFactor));

    const alpha = Math.min(0.4, Math.abs(tempFactor) * 1.2);
    const color = `rgba(${Math.round(adjustedR)}, ${baseG}, ${Math.round(adjustedB)}, ${alpha})`;

    this.drawGradientOverlay(color, width, height, 'white-balance');
  }

  // 🎯 绘制增益指示器（基于真实算法）
  drawGainIndicator(gainChange, width, height) {
    if (gainChange === 0) return; // 没有变化

    // 🎯 使用真实的增益算法：factor = 1 + (gain / 100) * 2
    const factor = 1 + (gainChange / 100) * 2; // 真实算法公式

    // 模拟算法效果：对基础颜色应用增益调整
    const baseR = 128, baseG = 128, baseB = 128;
    const adjustedR = Math.max(0, Math.min(255, baseR * factor));
    const adjustedG = Math.max(0, Math.min(255, baseG * factor));
    const adjustedB = Math.max(0, Math.min(255, baseB * factor));

    const alpha = Math.min(0.4, Math.abs(factor - 1) * 0.5);
    const color = `rgba(${Math.round(adjustedR)}, ${Math.round(adjustedG)}, ${Math.round(adjustedB)}, ${alpha})`;

    this.drawGradientOverlay(color, width, height, 'gain');
  }

  // 🎯 绘制曝光指示器（基于真实算法）
  drawExposureIndicator(exposureChange, width, height) {
    if (exposureChange === 0) return; // 没有变化

    // 🎯 使用真实的曝光算法：伽马校正
    const exposure = 1250 + exposureChange; // 真实曝光值
    const gamma = Math.log(exposure / 1250) / Math.log(2) + 1; // 真实算法公式
    const gammaCorrection = 1 / Math.max(0.1, Math.min(3.0, gamma));

    // 模拟算法效果：对基础颜色应用伽马校正
    const baseR = 128, baseG = 128, baseB = 128;
    const adjustedR = Math.max(0, Math.min(255, 255 * Math.pow(baseR / 255, gammaCorrection)));
    const adjustedG = Math.max(0, Math.min(255, 255 * Math.pow(baseG / 255, gammaCorrection)));
    const adjustedB = Math.max(0, Math.min(255, 255 * Math.pow(baseB / 255, gammaCorrection)));

    const alpha = Math.min(0.4, Math.abs(gamma - 1) * 0.6);
    const color = `rgba(${Math.round(adjustedR)}, ${Math.round(adjustedG)}, ${Math.round(adjustedB)}, ${alpha})`;

    this.drawGradientOverlay(color, width, height, 'exposure');
  }

  // 🎯 绘制锐度指示器（基于真实算法）
  drawSharpnessIndicator(sharpnessChange, width, height) {
    if (sharpnessChange === 0) return; // 没有变化

    // 🎯 使用真实的锐度算法：factor = (sharpness - 10) / 255 * 2
    const sharpness = 10 + sharpnessChange; // 真实锐度值
    const factor = (sharpness - 10) / 255 * 2; // 真实算法公式

    if (factor === 0) return;

    // 模拟锐化卷积核效果
    const alpha = Math.min(0.3, Math.abs(factor) * 0.8);

    if (factor > 0) {
      // 锐化增强：绘制边缘增强效果
      this.drawSharpnessEnhancement(width, height, alpha);
    } else {
      // 锐化减少：绘制模糊效果
      const color = `rgba(128, 128, 128, ${alpha})`;
      this.drawGradientOverlay(color, width, height, 'blur');
    }
  }

  // 🎯 绘制自动白平衡指示器
  drawAutoWhiteBalanceIndicator(width, height) {
    // 绘制自动调节动画效果
    this.drawAutoAdjustmentAnimation(width, height, 'auto-wb');
  }

  // 🎯 绘制自动曝光指示器
  drawAutoExposureIndicator(autoExposure, width, height) {
    if (autoExposure === 1) {
      // 手动模式：不显示
      return;
    }
    // 绘制自动调节动画效果
    this.drawAutoAdjustmentAnimation(width, height, 'auto-exp');
  }

  // 🎯 绘制闪烁效果（增益）
  drawSparkleEffect(width, height, intensity) {
    const sparkleCount = Math.floor(intensity * 10);
    const alpha = Math.min(0.8, intensity);

    if (this.isLegacyCanvas) {
      this.ctx.setFillStyle(`rgba(255, 255, 255, ${alpha})`);
    } else {
      this.ctx.fillStyle = `rgba(255, 255, 255, ${alpha})`;
    }

    // 绘制随机闪烁点
    for (let i = 0; i < sparkleCount; i++) {
      const x = Math.random() * width;
      const y = Math.random() * height;
      this.ctx.fillRect(x, y, 3, 3);
    }
  }

  // 🎯 绘制径向渐变（曝光）
  drawRadialGradient(width, height, color, type) {
    // 旧版Canvas API不支持径向渐变，使用简单填充
    this.drawGradientOverlay(color, width, height, type);
  }

  // 🎯 绘制锐化增强效果
  drawSharpnessEnhancement(width, height, intensity) {
    const alpha = Math.min(0.3, intensity * 0.5);

    if (this.isLegacyCanvas) {
      this.ctx.setStrokeStyle(`rgba(255, 255, 255, ${alpha})`);
      this.ctx.setLineWidth(1);
    } else {
      this.ctx.strokeStyle = `rgba(255, 255, 255, ${alpha})`;
      this.ctx.lineWidth = 1;
    }

    // 绘制细密网格模拟锐化
    for (let i = 0; i < width; i += 20) {
      this.ctx.beginPath();
      this.ctx.moveTo(i, 0);
      this.ctx.lineTo(i, height);
      this.ctx.stroke();
    }

    for (let i = 0; i < height; i += 20) {
      this.ctx.beginPath();
      this.ctx.moveTo(0, i);
      this.ctx.lineTo(width, i);
      this.ctx.stroke();
    }
  }

  // 🎯 绘制自动调节动画
  drawAutoAdjustmentAnimation(width, height, type) {
    const alpha = 0.3;

    if (this.isLegacyCanvas) {
      this.ctx.setFillStyle(`rgba(0, 255, 0, ${alpha})`);
      this.ctx.setStrokeStyle(`rgba(0, 255, 0, 0.6)`);
      this.ctx.setLineWidth(2);
    } else {
      this.ctx.fillStyle = `rgba(0, 255, 0, ${alpha})`;
      this.ctx.strokeStyle = `rgba(0, 255, 0, 0.6)`;
      this.ctx.lineWidth = 2;
    }

    // 绘制"AUTO"标识
    const centerX = width / 2;
    const centerY = height / 2;

    // 绘制圆形背景
    this.ctx.beginPath();
    this.ctx.arc(centerX, centerY, 30, 0, 2 * Math.PI);
    this.ctx.fill();
    this.ctx.stroke();

    // 绘制AUTO文字
    if (this.isLegacyCanvas) {
      this.ctx.setFillStyle('rgba(255, 255, 255, 0.9)');
      this.ctx.setFontSize(12);
      this.ctx.setTextAlign('center');
      this.ctx.setTextBaseline('middle');
    } else {
      this.ctx.fillStyle = 'rgba(255, 255, 255, 0.9)';
      this.ctx.font = 'bold 12px Arial';
      this.ctx.textAlign = 'center';
      this.ctx.textBaseline = 'middle';
    }

    this.ctx.fillText('AUTO', centerX, centerY);
  }

  // 🎯 绘制电力线频率指示器（基于真实算法）
  drawPowerLineFrequencyIndicator(powerLineFreq, width, height) {
    if (powerLineFreq === 2) return; // 默认值不处理

    // 🎯 使用真实的电力线频率算法：去闪烁处理
    const frequency = powerLineFreq === 1 ? 50 : (powerLineFreq === 0 ? 0 : 60); // 0=禁用, 1=50Hz, 2=60Hz

    if (frequency === 0) {
      // 禁用状态：显示"OFF"标识
      this.drawFrequencyOffIndicator(width, height);
    } else {
      // 启用状态：显示频率值和去闪烁效果
      this.drawFrequencyActiveIndicator(frequency, width, height);
    }
  }

  // 🎯 绘制频率禁用指示器
  drawFrequencyOffIndicator(width, height) {
    const alpha = 0.4;

    if (this.isLegacyCanvas) {
      this.ctx.setFillStyle(`rgba(255, 0, 0, ${alpha})`);
      this.ctx.setStrokeStyle(`rgba(255, 0, 0, 0.6)`);
      this.ctx.setLineWidth(2);
    } else {
      this.ctx.fillStyle = `rgba(255, 0, 0, ${alpha})`;
      this.ctx.strokeStyle = `rgba(255, 0, 0, 0.6)`;
      this.ctx.lineWidth = 2;
    }

    // 绘制"OFF"标识
    const centerX = width / 2;
    const centerY = height / 2;

    // 绘制圆形背景
    this.ctx.beginPath();
    this.ctx.arc(centerX, centerY, 25, 0, 2 * Math.PI);
    this.ctx.fill();
    this.ctx.stroke();

    // 绘制OFF文字
    if (this.isLegacyCanvas) {
      this.ctx.setFillStyle('rgba(255, 255, 255, 0.9)');
      this.ctx.setFontSize(10);
      this.ctx.setTextAlign('center');
      this.ctx.setTextBaseline('middle');
    } else {
      this.ctx.fillStyle = 'rgba(255, 255, 255, 0.9)';
      this.ctx.font = 'bold 10px Arial';
      this.ctx.textAlign = 'center';
      this.ctx.textBaseline = 'middle';
    }

    this.ctx.fillText('OFF', centerX, centerY);
  }

  // 🎯 绘制频率激活指示器
  drawFrequencyActiveIndicator(frequency, width, height) {
    const alpha = 0.3;

    // 绘制去闪烁效果：波纹状图案
    if (this.isLegacyCanvas) {
      this.ctx.setStrokeStyle(`rgba(100, 200, 255, ${alpha})`);
      this.ctx.setLineWidth(1);
    } else {
      this.ctx.strokeStyle = `rgba(100, 200, 255, ${alpha})`;
      this.ctx.lineWidth = 1;
    }

    // 绘制波纹线条模拟去闪烁
    const waveCount = frequency === 50 ? 5 : 6; // 50Hz绘制5条波纹，60Hz绘制6条
    for (let i = 0; i < waveCount; i++) {
      const y = (height / (waveCount + 1)) * (i + 1);
      this.ctx.beginPath();
      this.ctx.moveTo(0, y);

      // 绘制正弦波
      for (let x = 0; x <= width; x += 5) {
        const waveY = y + Math.sin((x / width) * Math.PI * 4) * 10;
        this.ctx.lineTo(x, waveY);
      }
      this.ctx.stroke();
    }

    // 显示频率值
    if (this.isLegacyCanvas) {
      this.ctx.setFillStyle('rgba(100, 200, 255, 0.8)');
      this.ctx.setFontSize(12);
      this.ctx.setTextAlign('center');
      this.ctx.setTextBaseline('middle');
    } else {
      this.ctx.fillStyle = 'rgba(100, 200, 255, 0.8)';
      this.ctx.font = 'bold 12px Arial';
      this.ctx.textAlign = 'center';
      this.ctx.textBaseline = 'middle';
    }

    this.ctx.fillText(`${frequency}Hz`, width / 2, height / 2);
  }

  // 这个方法已不再使用，参数效果现在在整个Canvas区域显示

  // 检查是否有参数变化
  hasParameterChanges(params) {
    // 🎯 使用真正的默认值（与resetParameters一致）
    const defaultParams = {
      brightness: 115,
      contrast: 115,
      saturation: 106,
      white_balance_temperature: 4650,
      white_balance_temperature_auto: 0,
      gain: 0,
      exposure_absolute: 1250,
      exposure_auto: 3,
      sharpness: 10,
      power_line_frequency: 2
    };

    // 🎯 添加调试信息
    console.log('🎯 检查参数变化:', {
      currentParams: params,
      defaultParams: defaultParams
    });

    let hasChanges = false;
    for (const [key, defaultValue] of Object.entries(defaultParams)) {
      if (params[key] !== undefined && params[key] !== defaultValue) {
        console.log(`🎯 参数变化检测到: ${key} = ${params[key]} (默认: ${defaultValue})`);
        hasChanges = true;
      }
    }

    if (hasChanges) {
      return true;
    }

    // 🎯 如果没有参数变化，重置累积参数
    this.resetAccumulatedParams();
    console.log('🎯 没有参数变化，Canvas应该透明，累积参数已重置');
    return false;
  }

  // 绘制参数调节的视觉提示（不再需要，效果在Canvas区域显示）
  drawParameterOverlay(params) {
    // 不再显示额外的提示，参数效果直接在Canvas区域显示
    return;
  }



  // 亮度调节算法（恢复真实算法）
  applyBrightness(data, brightness) {
    const factor = (brightness - 115) * 2; // 115为默认值，恢复真实算法
    console.log('🎯 亮度算法 factor:', factor);

    for (let i = 0; i < data.length; i += 4) {
      data[i] = Math.max(0, Math.min(255, data[i] + factor));         // R
      data[i + 1] = Math.max(0, Math.min(255, data[i + 1] + factor)); // G
      data[i + 2] = Math.max(0, Math.min(255, data[i + 2] + factor)); // B
    }
  }

  // 对比度调节算法（恢复真实算法）
  applyContrast(data, contrast) {
    const factor = (contrast / 115) * 2; // 115为默认值，恢复真实算法
    console.log('🎯 对比度算法 factor:', factor);

    for (let i = 0; i < data.length; i += 4) {
      data[i] = Math.max(0, Math.min(255, (data[i] - 128) * factor + 128));
      data[i + 1] = Math.max(0, Math.min(255, (data[i + 1] - 128) * factor + 128));
      data[i + 2] = Math.max(0, Math.min(255, (data[i + 2] - 128) * factor + 128));
    }
  }

  // 饱和度调节算法（恢复真实算法）
  applySaturation(data, saturation) {
    const factor = saturation / 106; // 106为默认值，恢复真实算法
    console.log('🎯 饱和度算法 factor:', factor);

    for (let i = 0; i < data.length; i += 4) {
      const r = data[i];
      const g = data[i + 1];
      const b = data[i + 2];

      // 计算灰度值
      const gray = 0.299 * r + 0.587 * g + 0.114 * b;

      // 应用饱和度
      data[i] = Math.max(0, Math.min(255, gray + factor * (r - gray)));
      data[i + 1] = Math.max(0, Math.min(255, gray + factor * (g - gray)));
      data[i + 2] = Math.max(0, Math.min(255, gray + factor * (b - gray)));
    }
  }

  // 色温调节算法（手动白平衡）
  applyColorTemperature(data, temperature) {
    // 只在非默认值时应用，避免与自动白平衡冲突
    if (temperature === 4650) return; // 默认值不处理

    // 2600K(暖) 到 6500K(冷)，4650为默认值
    const tempFactor = (temperature - 4650) / 1950;
    const redFactor = tempFactor < 0 ? 1 + Math.abs(tempFactor) * 0.3 : 1;
    const blueFactor = tempFactor > 0 ? 1 + tempFactor * 0.3 : 1;

    for (let i = 0; i < data.length; i += 4) {
      data[i] = Math.max(0, Math.min(255, data[i] * redFactor));         // R
      data[i + 2] = Math.max(0, Math.min(255, data[i + 2] * blueFactor)); // B
    }
  }

  // 增益调节算法
  applyGain(data, gain) {
    const factor = 1 + (gain / 100) * 2; // gain范围0-100，转换为1-3的倍数
    for (let i = 0; i < data.length; i += 4) {
      data[i] = Math.max(0, Math.min(255, data[i] * factor));
      data[i + 1] = Math.max(0, Math.min(255, data[i + 1] * factor));
      data[i + 2] = Math.max(0, Math.min(255, data[i + 2] * factor));
    }
  }

  // 曝光调节算法（伽马校正）
  applyExposure(data, exposure) {
    // exposure_absolute范围5-2500，1250为默认值
    const gamma = Math.log(exposure / 1250) / Math.log(2) + 1; // 转换为伽马值
    const gammaCorrection = 1 / Math.max(0.1, Math.min(3.0, gamma));

    for (let i = 0; i < data.length; i += 4) {
      data[i] = Math.max(0, Math.min(255, 255 * Math.pow(data[i] / 255, gammaCorrection)));
      data[i + 1] = Math.max(0, Math.min(255, 255 * Math.pow(data[i + 1] / 255, gammaCorrection)));
      data[i + 2] = Math.max(0, Math.min(255, 255 * Math.pow(data[i + 2] / 255, gammaCorrection)));
    }
  }

  // 自动白平衡算法
  applyAutoWhiteBalance(data, autoMode) {
    // autoMode: 0=关闭, 1=开启
    if (autoMode === 0) return;

    // 灰度世界算法：假设图像的平均颜色应该是灰色
    let rSum = 0, gSum = 0, bSum = 0;
    let pixelCount = 0;

    // 计算RGB平均值
    for (let i = 0; i < data.length; i += 4) {
      rSum += data[i];
      gSum += data[i + 1];
      bSum += data[i + 2];
      pixelCount++;
    }

    const rAvg = rSum / pixelCount;
    const gAvg = gSum / pixelCount;
    const bAvg = bSum / pixelCount;

    // 计算灰度平均值
    const grayAvg = (rAvg + gAvg + bAvg) / 3;

    // 计算校正因子
    const rFactor = grayAvg / Math.max(rAvg, 1);
    const gFactor = grayAvg / Math.max(gAvg, 1);
    const bFactor = grayAvg / Math.max(bAvg, 1);

    // 应用白平衡校正
    for (let i = 0; i < data.length; i += 4) {
      data[i] = Math.max(0, Math.min(255, data[i] * rFactor));
      data[i + 1] = Math.max(0, Math.min(255, data[i + 1] * gFactor));
      data[i + 2] = Math.max(0, Math.min(255, data[i + 2] * bFactor));
    }
  }

  // 自动曝光算法
  applyAutoExposure(data, autoMode) {
    // autoMode: 1=手动, 3=自动
    if (autoMode === 1) return;

    // 计算图像亮度直方图
    let brightness = 0;
    let pixelCount = 0;

    for (let i = 0; i < data.length; i += 4) {
      const r = data[i];
      const g = data[i + 1];
      const b = data[i + 2];
      brightness += (0.299 * r + 0.587 * g + 0.114 * b);
      pixelCount++;
    }

    const avgBrightness = brightness / pixelCount;
    const targetBrightness = 128; // 目标亮度
    const adjustmentFactor = targetBrightness / Math.max(avgBrightness, 1);

    // 限制调整范围，避免过度曝光或欠曝
    const limitedFactor = Math.max(0.5, Math.min(2.0, adjustmentFactor));

    // 应用曝光调整
    for (let i = 0; i < data.length; i += 4) {
      data[i] = Math.max(0, Math.min(255, data[i] * limitedFactor));
      data[i + 1] = Math.max(0, Math.min(255, data[i + 1] * limitedFactor));
      data[i + 2] = Math.max(0, Math.min(255, data[i + 2] * limitedFactor));
    }
  }

  // 锐度调节算法
  applySharpness(imageData, sharpness) {
    // sharpness范围0-255，默认10
    if (sharpness === 10) return; // 默认值不处理

    const data = imageData.data;
    const width = imageData.width;
    const height = imageData.height;
    const factor = (sharpness - 10) / 255 * 2; // 转换为-2到2的范围

    // 锐化卷积核
    const kernel = [
      0, -factor, 0,
      -factor, 1 + 4 * factor, -factor,
      0, -factor, 0
    ];

    // 创建输出数据副本
    const output = new Uint8ClampedArray(data);

    // 应用卷积
    for (let y = 1; y < height - 1; y++) {
      for (let x = 1; x < width - 1; x++) {
        for (let c = 0; c < 3; c++) { // RGB通道
          let sum = 0;
          for (let ky = -1; ky <= 1; ky++) {
            for (let kx = -1; kx <= 1; kx++) {
              const idx = ((y + ky) * width + (x + kx)) * 4 + c;
              const kernelIdx = (ky + 1) * 3 + (kx + 1);
              sum += data[idx] * kernel[kernelIdx];
            }
          }
          const outputIdx = (y * width + x) * 4 + c;
          output[outputIdx] = Math.max(0, Math.min(255, sum));
        }
      }
    }

    // 复制结果回原数据
    for (let i = 0; i < data.length; i++) {
      data[i] = output[i];
    }
  }

  // 电力线频率滤波算法
  applyPowerLineFilter(data, frequency) {
    // frequency: 0=禁用, 1=50Hz, 2=60Hz
    if (frequency === 0) return;

    // 简单的去闪烁算法：减少亮度波动
    const targetFreq = frequency === 1 ? 50 : 60;
    const dampingFactor = 0.95; // 减少闪烁的阻尼因子

    // 计算平均亮度
    let totalBrightness = 0;
    let pixelCount = 0;

    for (let i = 0; i < data.length; i += 4) {
      const brightness = 0.299 * data[i] + 0.587 * data[i + 1] + 0.114 * data[i + 2];
      totalBrightness += brightness;
      pixelCount++;
    }

    const avgBrightness = totalBrightness / pixelCount;

    // 对每个像素应用去闪烁处理
    for (let i = 0; i < data.length; i += 4) {
      const currentBrightness = 0.299 * data[i] + 0.587 * data[i + 1] + 0.114 * data[i + 2];
      const deviation = currentBrightness - avgBrightness;
      const adjustment = deviation * (1 - dampingFactor);

      // 应用调整
      data[i] = Math.max(0, Math.min(255, data[i] - adjustment));
      data[i + 1] = Math.max(0, Math.min(255, data[i + 1] - adjustment));
      data[i + 2] = Math.max(0, Math.min(255, data[i + 2] - adjustment));
    }
  }

  // 清理资源
  destroy() {
    if (this.animationFrame) {
      cancelAnimationFrame(this.animationFrame);
      this.animationFrame = null;
    }
    this.isProcessing = false;
    console.log('🎨 VideoParameterProcessor 已销毁');
  }
}

// 导出类
module.exports = VideoParameterProcessor;
