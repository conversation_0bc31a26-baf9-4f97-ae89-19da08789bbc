{"version": 3, "sources": ["index.js"], "names": [], "mappings": ";;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.js", "sourcesContent": ["\n\nmodule.exports = pixelmatch;\n\nfunction pixelmatch(img1, img2, output, width, height, options) {\n\n    if (!options) options = {};\n\n    var threshold = options.threshold === undefined ? 0.1 : options.threshold;\n\n    // maximum acceptable square distance between two colors;\n    // 35215 is the maximum possible value for the YIQ difference metric\n    var maxDelta = 35215 * threshold * threshold,\n        diff = 0;\n\n    // compare each pixel of one image against the other one\n    for (var y = 0; y < height; y++) {\n        for (var x = 0; x < width; x++) {\n\n            var pos = (y * width + x) * 4;\n\n            // squared YUV distance between colors at this pixel position\n            var delta = colorDelta(img1, img2, pos, pos);\n\n            // the color difference is above the threshold\n            if (delta > maxDelta) {\n                // check it's a real rendering difference or just anti-aliasing\n                if (!options.includeAA && (antialiased(img1, x, y, width, height, img2) ||\n                                   antialiased(img2, x, y, width, height, img1))) {\n                    // one of the pixels is anti-aliasing; draw as yellow and do not count as difference\n                    if (output) drawPixel(output, pos, 255, 255, 0);\n\n                } else {\n                    // found substantial difference not caused by anti-aliasing; draw it as red\n                    if (output) drawPixel(output, pos, 255, 0, 0);\n                    diff++;\n                }\n\n            } else if (output) {\n                // pixels are similar; draw background as grayscale image blended with white\n                var val = blend(grayPixel(img1, pos), 0.1);\n                drawPixel(output, pos, val, val, val);\n            }\n        }\n    }\n\n    // return the number of different pixels\n    return diff;\n}\n\n// check if a pixel is likely a part of anti-aliasing;\n// based on \"Anti-aliased Pixel and Intensity Slope Detector\" paper by V. Vysniauskas, 2009\n\nfunction antialiased(img, x1, y1, width, height, img2) {\n    var x0 = Math.max(x1 - 1, 0),\n        y0 = Math.max(y1 - 1, 0),\n        x2 = Math.min(x1 + 1, width - 1),\n        y2 = Math.min(y1 + 1, height - 1),\n        pos = (y1 * width + x1) * 4,\n        zeroes = 0,\n        positives = 0,\n        negatives = 0,\n        min = 0,\n        max = 0,\n        minX, minY, maxX, maxY;\n\n    // go through 8 adjacent pixels\n    for (var x = x0; x <= x2; x++) {\n        for (var y = y0; y <= y2; y++) {\n            if (x === x1 && y === y1) continue;\n\n            // brightness delta between the center pixel and adjacent one\n            var delta = colorDelta(img, img, pos, (y * width + x) * 4, true);\n\n            // count the number of equal, darker and brighter adjacent pixels\n            if (delta === 0) zeroes++;\n            else if (delta < 0) negatives++;\n            else if (delta > 0) positives++;\n\n            // if found more than 2 equal siblings, it's definitely not anti-aliasing\n            if (zeroes > 2) return false;\n\n            if (!img2) continue;\n\n            // remember the darkest pixel\n            if (delta < min) {\n                min = delta;\n                minX = x;\n                minY = y;\n            }\n            // remember the brightest pixel\n            if (delta > max) {\n                max = delta;\n                maxX = x;\n                maxY = y;\n            }\n        }\n    }\n\n    if (!img2) return true;\n\n    // if there are no both darker and brighter pixels among siblings, it's not anti-aliasing\n    if (negatives === 0 || positives === 0) return false;\n\n    // if either the darkest or the brightest pixel has more than 2 equal siblings in both images\n    // (definitely not anti-aliased), this pixel is anti-aliased\n    return (!antialiased(img, minX, minY, width, height) && !antialiased(img2, minX, minY, width, height)) ||\n           (!antialiased(img, maxX, maxY, width, height) && !antialiased(img2, maxX, maxY, width, height));\n}\n\n// calculate color difference according to the paper \"Measuring perceived color difference\n// using YIQ NTSC transmission color space in mobile applications\" by Y. Kotsarenko and F. Ramos\n\nfunction colorDelta(img1, img2, k, m, yOnly) {\n    var a1 = img1[k + 3] / 255,\n        a2 = img2[m + 3] / 255,\n\n        r1 = blend(img1[k + 0], a1),\n        g1 = blend(img1[k + 1], a1),\n        b1 = blend(img1[k + 2], a1),\n\n        r2 = blend(img2[m + 0], a2),\n        g2 = blend(img2[m + 1], a2),\n        b2 = blend(img2[m + 2], a2),\n\n        y = rgb2y(r1, g1, b1) - rgb2y(r2, g2, b2);\n\n    if (yOnly) return y; // brightness difference only\n\n    var i = rgb2i(r1, g1, b1) - rgb2i(r2, g2, b2),\n        q = rgb2q(r1, g1, b1) - rgb2q(r2, g2, b2);\n\n    return 0.5053 * y * y + 0.299 * i * i + 0.1957 * q * q;\n}\n\nfunction rgb2y(r, g, b) { return r * 0.29889531 + g * 0.58662247 + b * 0.11448223; }\nfunction rgb2i(r, g, b) { return r * 0.59597799 - g * 0.27417610 - b * 0.32180189; }\nfunction rgb2q(r, g, b) { return r * 0.21147017 - g * 0.52261711 + b * 0.31114694; }\n\n// blend semi-transparent color with white\nfunction blend(c, a) {\n    return 255 + (c - 255) * a;\n}\n\nfunction drawPixel(output, pos, r, g, b) {\n    output[pos + 0] = r;\n    output[pos + 1] = g;\n    output[pos + 2] = b;\n    output[pos + 3] = 255;\n}\n\nfunction grayPixel(img, i) {\n    var a = img[i + 3] / 255,\n        r = blend(img[i + 0], a),\n        g = blend(img[i + 1], a),\n        b = blend(img[i + 2], a);\n    return rgb2y(r, g, b);\n}\n"]}