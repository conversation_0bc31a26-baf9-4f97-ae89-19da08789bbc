#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
改进的PDF转Word转换器
使用PyMuPDF进行更精确的布局保持
"""

import os
import sys
import tempfile
import shutil
from pathlib import Path
from flask import Flask, request, jsonify, send_file, render_template_string
from werkzeug.utils import secure_filename
import logging

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

app = Flask(__name__)
app.config['MAX_CONTENT_LENGTH'] = 50 * 1024 * 1024  # 50MB max file size

# 导入必要的库
try:
    import fitz  # PyMuPDF
    from docx import Document
    from docx.shared import Inches, Pt
    from docx.enum.text import WD_ALIGN_PARAGRAPH
    from docx.oxml.ns import qn
    logger.info("PyMuPDF和python-docx库已加载")
except ImportError as e:
    logger.error(f"缺少必要库: {e}")
    logger.error("请运行: pip install pymupdf python-docx")
    sys.exit(1)

try:
    from pdf2docx import parse
    logger.info("pdf2docx库已加载")
    HAS_PDF2DOCX = True
except ImportError:
    logger.warning("pdf2docx库未安装，将使用PyMuPDF方法")
    HAS_PDF2DOCX = False

def extract_text_with_layout(pdf_path):
    """使用PyMuPDF提取文本并保持布局"""
    doc = fitz.open(pdf_path)
    docx_doc = Document()
    
    for page_num in range(len(doc)):
        page = doc[page_num]
        
        # 获取页面尺寸
        page_rect = page.rect
        logger.info(f"处理第{page_num + 1}页，尺寸: {page_rect.width} x {page_rect.height}")
        
        # 检测文本块
        blocks = page.get_text("dict")
        
        # 按Y坐标排序文本块
        text_blocks = []
        for block in blocks["blocks"]:
            if "lines" in block:
                for line in block["lines"]:
                    for span in line["spans"]:
                        text_blocks.append({
                            'text': span['text'],
                            'bbox': span['bbox'],
                            'font': span['font'],
                            'size': span['size']
                        })
        
        # 按Y坐标排序
        text_blocks.sort(key=lambda x: (x['bbox'][1], x['bbox'][0]))
        
        # 检测双栏布局
        if text_blocks:
            page_width = page_rect.width
            mid_x = page_width / 2
            
            left_column = [b for b in text_blocks if b['bbox'][0] < mid_x]
            right_column = [b for b in text_blocks if b['bbox'][0] >= mid_x]
            
            if len(left_column) > 0 and len(right_column) > 0:
                logger.info(f"检测到双栏布局: 左栏{len(left_column)}块，右栏{len(right_column)}块")
                
                # 处理双栏布局
                process_dual_column(docx_doc, left_column, right_column)
            else:
                # 单栏布局
                logger.info("检测到单栏布局")
                process_single_column(docx_doc, text_blocks)
        
        # 添加分页符（除了最后一页）
        if page_num < len(doc) - 1:
            docx_doc.add_page_break()
    
    doc.close()
    return docx_doc

def process_single_column(docx_doc, text_blocks):
    """处理单栏布局"""
    current_paragraph = None
    
    for block in text_blocks:
        text = block['text'].strip()
        if not text:
            continue
            
        if current_paragraph is None:
            current_paragraph = docx_doc.add_paragraph()
        
        run = current_paragraph.add_run(text + " ")
        
        # 设置字体大小
        font_size = max(8, min(block['size'], 20))  # 限制字体大小范围
        run.font.size = Pt(font_size)

def process_dual_column(docx_doc, left_column, right_column):
    """处理双栏布局 - 先左栏后右栏"""
    # 处理左栏
    if left_column:
        left_column.sort(key=lambda x: x['bbox'][1])  # 按Y坐标排序
        para = docx_doc.add_paragraph()
        para.add_run("【左栏内容】").bold = True
        docx_doc.add_paragraph()
        
        for block in left_column:
            text = block['text'].strip()
            if text:
                p = docx_doc.add_paragraph()
                run = p.add_run(text)
                font_size = max(8, min(block['size'], 16))
                run.font.size = Pt(font_size)
    
    # 处理右栏
    if right_column:
        right_column.sort(key=lambda x: x['bbox'][1])  # 按Y坐标排序
        para = docx_doc.add_paragraph()
        para.add_run("【右栏内容】").bold = True
        docx_doc.add_paragraph()
        
        for block in right_column:
            text = block['text'].strip()
            if text:
                p = docx_doc.add_paragraph()
                run = p.add_run(text)
                font_size = max(8, min(block['size'], 16))
                run.font.size = Pt(font_size)

# HTML模板
HTML_TEMPLATE = '''
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>改进的PDF转Word转换器</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh; padding: 20px;
        }
        .container {
            max-width: 800px; margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px; padding: 30px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        }
        h1 { text-align: center; color: #333; margin-bottom: 30px; }
        .upload-area {
            border: 3px dashed #ddd; border-radius: 10px;
            padding: 40px; text-align: center; margin: 20px 0;
            transition: all 0.3s ease; cursor: pointer;
        }
        .upload-area:hover { border-color: #667eea; background: #f8f9ff; }
        .upload-area.dragover { border-color: #667eea; background: #f0f4ff; }
        .btn {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white; border: none; padding: 12px 30px;
            border-radius: 25px; cursor: pointer; margin: 10px;
            font-size: 16px; transition: all 0.3s ease;
        }
        .btn:hover { transform: translateY(-2px); box-shadow: 0 5px 15px rgba(0,0,0,0.2); }
        .btn:disabled { background: #ccc; cursor: not-allowed; transform: none; }
        .progress { display: none; margin: 20px 0; }
        .progress-bar {
            width: 100%; height: 20px; background: #f0f0f0;
            border-radius: 10px; overflow: hidden;
        }
        .progress-fill {
            height: 100%; background: linear-gradient(45deg, #667eea, #764ba2);
            width: 0%; transition: width 0.3s ease;
        }
        .message { padding: 15px; border-radius: 5px; margin: 15px 0; }
        .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        .method-info {
            background: #fff3cd; padding: 15px; border-radius: 5px;
            margin: 15px 0; border-left: 4px solid #ffc107;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔄 改进的PDF转Word转换器</h1>
        <p style="text-align: center; color: #666; margin-bottom: 20px;">
            基于PyMuPDF的智能布局识别转换
        </p>
        
        <div class="method-info">
            <h4>💡 转换方法说明</h4>
            <p><strong>智能布局转换</strong>：自动识别单栏/双栏布局，保持阅读顺序</p>
            <p><strong>传统转换</strong>：使用pdf2docx库进行转换（如果可用）</p>
        </div>
        
        <div class="upload-area" onclick="document.getElementById('fileInput').click()">
            <p>📄 点击选择PDF文件或拖拽到此处</p>
            <p style="color: #999; margin-top: 10px;">支持最大50MB的PDF文件</p>
            <input type="file" id="fileInput" accept=".pdf" style="display: none;">
        </div>
        
        <div style="text-align: center; margin: 20px 0;">
            <button class="btn" id="smartBtn" onclick="convertSmart()" disabled>智能布局转换</button>
            <button class="btn" id="traditionalBtn" onclick="convertTraditional()" disabled>传统转换</button>
            <button class="btn" onclick="clearAll()">清空</button>
        </div>
        
        <div class="progress" id="progress">
            <div class="progress-bar">
                <div class="progress-fill" id="progressFill"></div>
            </div>
            <p id="progressText" style="text-align: center; margin-top: 10px;">准备中...</p>
        </div>
        
        <div id="messages"></div>
    </div>

    <script>
        let selectedFile = null;
        
        // 文件选择处理
        document.getElementById('fileInput').addEventListener('change', function(e) {
            const file = e.target.files[0];
            if (file && file.type === 'application/pdf') {
                selectedFile = file;
                document.getElementById('smartBtn').disabled = false;
                document.getElementById('traditionalBtn').disabled = false;
                showMessage(`已选择文件: ${file.name} (${(file.size/1024/1024).toFixed(2)}MB)`, 'info');
            } else {
                showMessage('请选择有效的PDF文件', 'error');
                selectedFile = null;
                document.getElementById('smartBtn').disabled = true;
                document.getElementById('traditionalBtn').disabled = true;
            }
        });
        
        // 拖拽处理
        const uploadArea = document.querySelector('.upload-area');
        uploadArea.addEventListener('dragover', (e) => {
            e.preventDefault();
            uploadArea.classList.add('dragover');
        });
        uploadArea.addEventListener('dragleave', () => {
            uploadArea.classList.remove('dragover');
        });
        uploadArea.addEventListener('drop', (e) => {
            e.preventDefault();
            uploadArea.classList.remove('dragover');
            const files = e.dataTransfer.files;
            if (files.length > 0) {
                document.getElementById('fileInput').files = files;
                document.getElementById('fileInput').dispatchEvent(new Event('change'));
            }
        });
        
        async function convertSmart() {
            await doConvert('/convert_smart', '智能布局转换');
        }
        
        async function convertTraditional() {
            await doConvert('/convert_traditional', '传统转换');
        }
        
        async function doConvert(endpoint, mode) {
            if (!selectedFile) {
                showMessage('请先选择PDF文件', 'error');
                return;
            }
            
            const formData = new FormData();
            formData.append('file', selectedFile);
            
            showProgress(true);
            updateProgress(10, `开始${mode}...`);
            
            try {
                const response = await fetch(endpoint, {
                    method: 'POST',
                    body: formData
                });
                
                if (response.ok) {
                    updateProgress(90, '转换完成，准备下载...');
                    
                    const blob = await response.blob();
                    const url = window.URL.createObjectURL(blob);
                    const a = document.createElement('a');
                    a.href = url;
                    a.download = selectedFile.name.replace('.pdf', `_${mode}.docx`);
                    document.body.appendChild(a);
                    a.click();
                    document.body.removeChild(a);
                    window.URL.revokeObjectURL(url);
                    
                    updateProgress(100, '转换完成！');
                    showMessage(`${mode}成功！文件已下载。`, 'success');
                } else {
                    const error = await response.text();
                    throw new Error(error);
                }
            } catch (error) {
                showMessage(`${mode}失败: ` + error.message, 'error');
            } finally {
                setTimeout(() => showProgress(false), 2000);
            }
        }
        
        function showProgress(show) {
            document.getElementById('progress').style.display = show ? 'block' : 'none';
            if (!show) {
                updateProgress(0, '');
            }
        }
        
        function updateProgress(percent, text) {
            document.getElementById('progressFill').style.width = percent + '%';
            document.getElementById('progressText').textContent = text;
        }
        
        function showMessage(message, type) {
            const messagesDiv = document.getElementById('messages');
            messagesDiv.innerHTML = `<div class="message ${type}">${message}</div>`;
        }
        
        function clearAll() {
            selectedFile = null;
            document.getElementById('fileInput').value = '';
            document.getElementById('smartBtn').disabled = true;
            document.getElementById('traditionalBtn').disabled = true;
            document.getElementById('messages').innerHTML = '';
            showProgress(false);
        }
    </script>
</body>
</html>
'''

@app.route('/')
def index():
    """主页"""
    return render_template_string(HTML_TEMPLATE)

@app.route('/convert_smart', methods=['POST'])
def convert_smart():
    """智能布局转换"""
    return convert_pdf_internal(method='smart')

@app.route('/convert_traditional', methods=['POST'])
def convert_traditional():
    """传统转换"""
    return convert_pdf_internal(method='traditional')

def convert_pdf_internal(method='smart'):
    """内部转换函数"""
    try:
        # 检查文件
        if 'file' not in request.files:
            return jsonify({'error': '没有上传文件'}), 400

        file = request.files['file']
        if file.filename == '':
            return jsonify({'error': '没有选择文件'}), 400

        if not file.filename.lower().endswith('.pdf'):
            return jsonify({'error': '只支持PDF文件'}), 400

        # 创建临时目录
        with tempfile.TemporaryDirectory() as temp_dir:
            # 保存上传的PDF文件
            pdf_filename = secure_filename(file.filename)
            pdf_path = os.path.join(temp_dir, pdf_filename)
            file.save(pdf_path)

            # 生成输出文件名
            docx_filename = pdf_filename.replace('.pdf', f'_{method}.docx')
            docx_path = os.path.join(temp_dir, docx_filename)

            logger.info(f"开始{method}转换: {pdf_path} -> {docx_path}")

            # 根据方法选择转换方式
            if method == 'smart':
                # 使用智能布局转换
                docx_doc = extract_text_with_layout(pdf_path)
                docx_doc.save(docx_path)
            elif method == 'traditional' and HAS_PDF2DOCX:
                # 使用传统pdf2docx转换
                parse(pdf_path, docx_path)
            else:
                return jsonify({'error': 'pdf2docx库未安装，无法使用传统转换'}), 500

            logger.info("转换完成")

            # 返回转换后的文件
            return send_file(
                docx_path,
                as_attachment=True,
                download_name=docx_filename,
                mimetype='application/vnd.openxmlformats-officedocument.wordprocessingml.document'
            )

    except Exception as e:
        logger.error(f"转换失败: {str(e)}")
        return jsonify({'error': f'转换失败: {str(e)}'}), 500

if __name__ == '__main__':
    print("🚀 启动改进的PDF转Word服务器...")
    print("📋 基于PyMuPDF的智能布局识别")
    print("🌐 服务器地址: http://localhost:5002")
    print("📄 支持智能双栏布局识别")

    app.run(host='0.0.0.0', port=5002, debug=True)
