{"version": 3, "sources": ["index.js", "parse-attribs.js"], "names": [], "mappings": ";;;;;;;AAAA;AACA;AACA;AACA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.js", "sourcesContent": ["var xml2js = require('xml2js')\nvar parseAttributes = require('./parse-attribs')\n\nmodule.exports = function parseBMFontXML(data) {\n  data = data.toString().trim()\n\n  var output = {\n    pages: [],\n    chars: [],\n    kernings: []\n  }\n\n  xml2js.parseString(data, function(err, result) {\n    if (err)\n      throw err\n    if (!result.font)\n      throw \"XML bitmap font doesn't have <font> root\"\n    result = result.font\n\n    output.common = parseAttributes(result.common[0].$)\n    output.info = parseAttributes(result.info[0].$)\n\n    for (var i = 0; i < result.pages.length; i++) {\n      var p = result.pages[i].page[0].$\n\n      if (typeof p.id === \"undefined\")\n        throw new Error(\"malformed file -- needs page id=N\")\n      if (typeof p.file !== \"string\")\n        throw new Error(\"malformed file -- needs page file=\\\"path\\\"\")\n\n      output.pages[parseInt(p.id, 10)] = p.file\n    }\n\n    if (result.chars) {\n      var chrArray = result.chars[0]['char'] || []\n      for (var i = 0; i < chrArray.length; i++) {\n        output.chars.push(parseAttributes(chrArray[i].$))\n      }\n    }\n\n    if (result.kernings) {\n      var kernArray = result.kernings[0]['kerning'] || []\n      for (var i = 0; i < kernArray.length; i++) {\n        output.kernings.push(parseAttributes(kernArray[i].$))\n      }\n    }\n  })\n  return output\n}\n", "//Some versions of GlyphDesigner have a typo\n//that causes some bugs with parsing. \n//Need to confirm with recent version of the software\n//to see whether this is still an issue or not.\nvar GLYPH_DESIGNER_ERROR = 'chasrset'\n\nmodule.exports = function parseAttributes(obj) {\n  obj = Object.assign({}, obj)\n  if (GLYPH_DESIGNER_ERROR in obj) {\n    obj['charset'] = obj[GLYPH_DESIGNER_ERROR]\n    delete obj[GLYPH_DESIGNER_ERROR]\n  }\n\n  for (var k in obj) {\n    if (k === 'face' || k === 'charset') \n      continue\n    else if (k === 'padding' || k === 'spacing')\n      obj[k] = parseIntList(obj[k])\n    else\n      obj[k] = parseInt(obj[k], 10) \n  }\n  return obj\n}\n\nfunction parseIntList(data) {\n  return data.split(',').map(function(val) {\n    return parseInt(val, 10)\n  })\n}"]}