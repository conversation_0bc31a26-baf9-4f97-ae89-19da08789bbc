{"version": 3, "sources": ["index.js", "package.json"], "names": [], "mappings": ";;;;;;;AAAA;AACA;AACA;AACA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.js", "sourcesContent": ["module.exports =\n/******/ (function(modules) { // webpackBootstrap\n/******/ \t// The module cache\n/******/ \tvar installedModules = {};\n/******/\n/******/ \t// The require function\n/******/ \tfunction __webpack_require__(moduleId) {\n/******/\n/******/ \t\t// Check if module is in cache\n/******/ \t\tif(installedModules[moduleId]) {\n/******/ \t\t\treturn installedModules[moduleId].exports;\n/******/ \t\t}\n/******/ \t\t// Create a new module (and put it into the cache)\n/******/ \t\tvar module = installedModules[moduleId] = {\n/******/ \t\t\ti: moduleId,\n/******/ \t\t\tl: false,\n/******/ \t\t\texports: {}\n/******/ \t\t};\n/******/\n/******/ \t\t// Execute the module function\n/******/ \t\tmodules[moduleId].call(module.exports, module, module.exports, __webpack_require__);\n/******/\n/******/ \t\t// Flag the module as loaded\n/******/ \t\tmodule.l = true;\n/******/\n/******/ \t\t// Return the exports of the module\n/******/ \t\treturn module.exports;\n/******/ \t}\n/******/\n/******/\n/******/ \t// expose the modules object (__webpack_modules__)\n/******/ \t__webpack_require__.m = modules;\n/******/\n/******/ \t// expose the module cache\n/******/ \t__webpack_require__.c = installedModules;\n/******/\n/******/ \t// define getter function for harmony exports\n/******/ \t__webpack_require__.d = function(exports, name, getter) {\n/******/ \t\tif(!__webpack_require__.o(exports, name)) {\n/******/ \t\t\tObject.defineProperty(exports, name, { enumerable: true, get: getter });\n/******/ \t\t}\n/******/ \t};\n/******/\n/******/ \t// define __esModule on exports\n/******/ \t__webpack_require__.r = function(exports) {\n/******/ \t\tif(typeof Symbol !== 'undefined' && Symbol.toStringTag) {\n/******/ \t\t\tObject.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });\n/******/ \t\t}\n/******/ \t\tObject.defineProperty(exports, '__esModule', { value: true });\n/******/ \t};\n/******/\n/******/ \t// create a fake namespace object\n/******/ \t// mode & 1: value is a module id, require it\n/******/ \t// mode & 2: merge all properties of value into the ns\n/******/ \t// mode & 4: return value when already ns object\n/******/ \t// mode & 8|1: behave like require\n/******/ \t__webpack_require__.t = function(value, mode) {\n/******/ \t\tif(mode & 1) value = __webpack_require__(value);\n/******/ \t\tif(mode & 8) return value;\n/******/ \t\tif((mode & 4) && typeof value === 'object' && value && value.__esModule) return value;\n/******/ \t\tvar ns = Object.create(null);\n/******/ \t\t__webpack_require__.r(ns);\n/******/ \t\tObject.defineProperty(ns, 'default', { enumerable: true, value: value });\n/******/ \t\tif(mode & 2 && typeof value != 'string') for(var key in value) __webpack_require__.d(ns, key, function(key) { return value[key]; }.bind(null, key));\n/******/ \t\treturn ns;\n/******/ \t};\n/******/\n/******/ \t// getDefaultExport function for compatibility with non-harmony modules\n/******/ \t__webpack_require__.n = function(module) {\n/******/ \t\tvar getter = module && module.__esModule ?\n/******/ \t\t\tfunction getDefault() { return module['default']; } :\n/******/ \t\t\tfunction getModuleExports() { return module; };\n/******/ \t\t__webpack_require__.d(getter, 'a', getter);\n/******/ \t\treturn getter;\n/******/ \t};\n/******/\n/******/ \t// Object.prototype.hasOwnProperty.call\n/******/ \t__webpack_require__.o = function(object, property) { return Object.prototype.hasOwnProperty.call(object, property); };\n/******/\n/******/ \t// __webpack_public_path__\n/******/ \t__webpack_require__.p = \"\";\n/******/\n/******/\n/******/ \t// Load entry module and return exports\n/******/ \treturn __webpack_require__(__webpack_require__.s = \"./src/index.ts\");\n/******/ })\n/************************************************************************/\n/******/ ({\n\n/***/ \"./src/api/cloud/index.ts\":\n/*!********************************!*\\\n  !*** ./src/api/cloud/index.ts ***!\n  \\********************************/\n/*! no static exports found */\n/***/ (function(module, exports, __webpack_require__) {\n\n\n\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.Cloud = void 0;\nconst provider_1 = __webpack_require__(/*! ./provider */ \"./src/api/cloud/provider/index.ts\");\nconst error_1 = __webpack_require__(/*! utils/error */ \"./src/utils/error.ts\");\nconst type_1 = __webpack_require__(/*! utils/type */ \"./src/utils/type.ts\");\nconst index_1 = __webpack_require__(/*! ../index */ \"./src/api/index.ts\");\nconst cross_account_token_1 = __webpack_require__(/*! utils/cross-account-token */ \"./src/utils/cross-account-token.ts\");\n// @ts-ignore\nconst { version } = require('./package.json');\nclass Cloud {\n    // internal\n    constructor(options) {\n        this.inited = false;\n        this.services = {};\n        this.debug = false;\n        this.instanceForEnv = new Map;\n        this.wrapCommonAPICheck = (func) => {\n            return (...args) => {\n                if (!this.inited) {\n                    throw new error_1.CloudSDKError({\n                        errMsg: 'Cloud API isn\\'t enabled, please call init first\\n' +\n                            '请先调用 init 完成初始化后再调用其他云 API。init 方法可传入一个对象用于设置默认配置，详见文档。'\n                    });\n                }\n                return func.apply(this, args);\n            };\n        };\n        if (options) {\n            this.isCloudInstance = true;\n            this.cloudInstanceOptions = JSON.parse(JSON.stringify(options));\n            if (!options.resourceEnv) {\n                throw new Error(`missing resourceEnv`);\n            }\n        }\n        else {\n            this.isCloudInstance = false;\n        }\n        const that = this;\n        this.exportAPI = {\n            version,\n            get DYNAMIC_CURRENT_ENV() {\n                const env = process.env.TCB_ENV || process.env.SCF_NAMESPACE;\n                if (env === 'local' && process.env.TENCENTCLOUD_RUNENV === 'WX_LOCAL_SCF') {\n                    console.group('环境提示');\n                    console.log('在本地调试中使用 DYNAMIC_CURRENT_ENV 相当于使用默认环境（第一个创建的环境）');\n                    console.groupEnd();\n                    return undefined;\n                }\n                return that.provider.DYNAMIC_CURRENT_ENV;\n            },\n            init: this.isCloudInstance ? this.instanceInit.bind(this) : this.defaultInit.bind(this),\n            // @ts-ignore\n            updateConfig: this.updateConfig.bind(this),\n            registerService: (serviceProvider) => {\n                this.registerService(serviceProvider.createService(this));\n            },\n            // @ts-ignore\n            createNewInstance: (config) => {\n                const instance = new Cloud();\n                instance.defaultInit(config);\n                return instance.exportAPI;\n            },\n            // @ts-ignore\n            Cloud: function (options) {\n                const instance = new Cloud(options);\n                return instance.exportAPI;\n            },\n        };\n        index_1.registerServices(this);\n        this.meta = {\n            session_id: (+new Date).toString()\n        };\n        this.config = {\n            env: {},\n        };\n        this.provider = provider_1.default();\n    }\n    getMetaData() {\n        return this.meta;\n    }\n    getAPIs() {\n        return Object.assign({}, this.exportAPI);\n    }\n    defaultInit(config = {}) {\n        if (this.inited)\n            return;\n        this.inited = true;\n        this.instance = this.provider.init(this.getProviderInitOptions(config));\n        const _config = Object.assign(Object.assign({}, config), { env: type_1.isObject(config.env) ? config.env : { default: config.env } });\n        this.config = _config;\n        this.exportAPI.logger = this.instance.logger.bind(this.instance);\n        // if (process.env.TENCENTCLOUD_RUNENV === 'WX_LOCAL_SCF') {\n        //   this.autoRefreshProviderInstanceConfig()\n        // }\n    }\n    async instanceInit() {\n        if (this.inited)\n            return;\n        const providerInitOptions = this.getProviderInitOptions();\n        if (this.cloudInstanceOptions.resourceAppid) {\n            providerInitOptions.env = providerInitOptions.env || process.env.TCB_ENV;\n            // tmp instance for calling wx openapi\n            this.instance = this.provider.init(JSON.parse(JSON.stringify(providerInitOptions)));\n            this.crossAccountToken = await cross_account_token_1.getBoundGetCrossAccountToken(this)({\n                resourceAppid: this.cloudInstanceOptions.resourceAppid,\n                resourceEnv: this.cloudInstanceOptions.resourceEnv,\n            });\n            providerInitOptions.env = this.cloudInstanceOptions.resourceEnv;\n            providerInitOptions.getCrossAccountInfo = () => this.crossAccountToken;\n        }\n        this.instance = this.provider.init(providerInitOptions);\n        this.exportAPI.logger = this.instance.logger.bind(this.instance);\n        // if (process.env.TENCENTCLOUD_RUNENV === 'WX_LOCAL_SCF') {\n        //   this.autoRefreshProviderInstanceConfig()\n        // }\n        this.inited = true;\n    }\n    getProviderInitOptions(config = {}) {\n        const wxLocalSCFConfig = process.env.TENCENTCLOUD_RUNENV === 'WX_LOCAL_SCF' ? this.getWXLocalSCFConfig() : {};\n        return this.isCloudInstance ? Object.assign(Object.assign(Object.assign({ throwOnCode: false }, this.cloudInstanceOptions), { version: `wx-server-sdk/${version}` }), wxLocalSCFConfig) : Object.assign(Object.assign(Object.assign({ throwOnCode: false }, config), { version: `wx-server-sdk/${version}` }), wxLocalSCFConfig);\n    }\n    getWXLocalSCFConfig() {\n        return {\n            isHttp: true,\n        };\n    }\n    // autoRefreshProviderInstanceConfig() {\n    //   setTimeout(() => {\n    //     const newPartialConfig: any = this.getWXLocalSCFConfig()\n    //     for (const key in newPartialConfig) {\n    //       this.instance.config[key] = newPartialConfig[key]\n    //     }\n    //     this.autoRefreshProviderInstanceConfig()\n    //   }, 3 * 60 * 1000)\n    // }\n    getInstanceForEnv(env) {\n        if (this.isCloudInstance) {\n            return this.instance;\n        }\n        else {\n            // aggressive mode\n            return this.provider.init(this.getProviderInitOptions(Object.assign(Object.assign({}, this.config), { env })));\n        }\n    }\n    updateConfig(config = {}) {\n        this.provider.init(Object.assign(Object.assign({ throwOnCode: false }, config), { version: `wx-server-sdk/${version}` }));\n        const _config = Object.assign(Object.assign({}, config), { env: type_1.isObject(config.env) ? config.env : { default: config.env } });\n        this.config = _config;\n    }\n    registerService(service) {\n        this.services[service.name] = service;\n        if (service.getAPIs) {\n            const functions = service.getAPIs();\n            for (const name in functions) {\n                this.registerFunction(name, functions[name], service.initRequired);\n            }\n        }\n        else if (service.getNamespace) {\n            const { namespace, apis } = service.getNamespace();\n            this.exportAPI[namespace] = apis;\n        }\n    }\n    registerFunction(name, func, initRequired) {\n        this.exportAPI[name] = initRequired === false ? func : this.wrapCommonAPICheck(func);\n    }\n}\nexports.Cloud = Cloud;\nconst cloud = new Cloud();\nexports.default = cloud;\n\n\n/***/ }),\n\n/***/ \"./src/api/cloud/provider/index.ts\":\n/*!*****************************************!*\\\n  !*** ./src/api/cloud/provider/index.ts ***!\n  \\*****************************************/\n/*! no static exports found */\n/***/ (function(module, exports, __webpack_require__) {\n\n\n\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.PROVIDER = void 0;\nconst tcb_1 = __webpack_require__(/*! ./tcb */ \"./src/api/cloud/provider/tcb/index.ts\");\nvar PROVIDER;\n(function (PROVIDER) {\n    PROVIDER[PROVIDER[\"TCB\"] = 0] = \"TCB\";\n})(PROVIDER = exports.PROVIDER || (exports.PROVIDER = {}));\nfunction getProvider(provider = PROVIDER.TCB) {\n    switch (provider) {\n        default: {\n            return tcb_1.default;\n        }\n    }\n}\nexports.default = getProvider;\n\n\n/***/ }),\n\n/***/ \"./src/api/cloud/provider/tcb/api/callFunction.ts\":\n/*!********************************************************!*\\\n  !*** ./src/api/cloud/provider/tcb/api/callFunction.ts ***!\n  \\********************************************************/\n/*! no static exports found */\n/***/ (function(module, exports, __webpack_require__) {\n\n\n\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.callFunction = void 0;\nconst error_config_1 = __webpack_require__(/*! ../config/error.config */ \"./src/api/cloud/provider/tcb/config/error.config.ts\");\nasync function callFunction(options, config) {\n    const tcbInstance = config.instance;\n    const res = await tcbInstance.callFunction(options);\n    if (res.code && error_config_1.TCB_ERR_CODE[res.code] !== 0) {\n        throw {\n            errCode: error_config_1.TCB_ERR_CODE[res.code] || error_config_1.TCB_ERR_CODE.SYS_ERR,\n            errMsg: `requestID ${res.requestId}, ${res.message}`,\n            requestId: res.requestId,\n        };\n    }\n    else {\n        return {\n            result: res.result,\n            requestId: res.requestId,\n        };\n    }\n}\nexports.callFunction = callFunction;\n\n\n/***/ }),\n\n/***/ \"./src/api/cloud/provider/tcb/api/callOpenAPI.ts\":\n/*!*******************************************************!*\\\n  !*** ./src/api/cloud/provider/tcb/api/callOpenAPI.ts ***!\n  \\*******************************************************/\n/*! no static exports found */\n/***/ (function(module, exports, __webpack_require__) {\n\n\n\n// DEPRECATED\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.callOpenAPI = void 0;\nconst error_config_1 = __webpack_require__(/*! ../config/error.config */ \"./src/api/cloud/provider/tcb/config/error.config.ts\");\nconst sleep = (ms = 0) => new Promise(resolve => setTimeout(resolve, ms));\nasync function callOpenAPI(options, config) {\n    await sleep();\n    // console.log('wx-server-sdk tcb.callWxOpenApi options: ', options)\n    const tcbInstance = config.instance;\n    const res = await tcbInstance.callWxOpenApi({\n        apiName: options.api,\n        requestData: options.data,\n        event: options.event,\n    });\n    // console.log('wx-server-sdk tcb.callWxOpenApi res: ', res)\n    if (res.code && error_config_1.TCB_ERR_CODE[res.code] !== 0) {\n        throw {\n            errCode: error_config_1.TCB_ERR_CODE[res.code] || error_config_1.TCB_ERR_CODE.SYS_ERR,\n            errMsg: res.message,\n        };\n    }\n    else {\n        return {\n            result: res.result,\n        };\n    }\n}\nexports.callOpenAPI = callOpenAPI;\n\n\n/***/ }),\n\n/***/ \"./src/api/cloud/provider/tcb/api/callWXOpenAPI.ts\":\n/*!*********************************************************!*\\\n  !*** ./src/api/cloud/provider/tcb/api/callWXOpenAPI.ts ***!\n  \\*********************************************************/\n/*! no static exports found */\n/***/ (function(module, exports, __webpack_require__) {\n\n\n\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.callWXPayAPI = exports.callWXOpenAPI = exports.callGeneralOpenAPI = void 0;\nconst error_config_1 = __webpack_require__(/*! ../config/error.config */ \"./src/api/cloud/provider/tcb/config/error.config.ts\");\nconst error_config_2 = __webpack_require__(/*! ../../../../../config/error.config */ \"./src/config/error.config.ts\");\nconst error_1 = __webpack_require__(/*! ../../../../../utils/error */ \"./src/utils/error.ts\");\nconst msg_1 = __webpack_require__(/*! ../../../../../utils/msg */ \"./src/utils/msg.ts\");\nconst openapi_1 = __webpack_require__(/*! ../../../../../protobuf/openapi */ \"./src/protobuf/openapi.js\");\nconst sleep = (ms = 0) => new Promise(resolve => setTimeout(resolve, ms));\nasync function callGeneralOpenAPI(method, options, config) {\n    await sleep();\n    let res;\n    try {\n        // console.log('wx-server-sdk tcb.callWxOpenApi options: ', options)\n        const tcbInstance = config.instance;\n        if (method === 'callWxPayApi') {\n            res = await tcbInstance.callWxPayApi({\n                apiName: options.api,\n                requestData: options.data,\n                apiOptions: options.apiOptions,\n            });\n        }\n        else {\n            res = await tcbInstance.callCompatibleWxOpenApi({\n                apiName: options.api,\n                requestData: options.data,\n                // @ts-ignore\n                cgiName: config.version === 'v2' ? 'commrpcv2' : undefined,\n            });\n        }\n    }\n    catch (err) {\n        throw {\n            errCode: (err && err.code && error_config_1.TCB_ERR_CODE[err.code]) || error_config_1.TCB_ERR_CODE.SYS_ERR,\n            errMsg: (err && err.message) || err || 'empty error message',\n        };\n    }\n    if (!Buffer.isBuffer(res)) {\n        // is object\n        // must be error\n        // tcb must not return object\n        if (res.code && res.hasOwnProperty('message')) {\n            // tcb error\n            throw new error_1.CloudSDKError({\n                errCode: error_config_1.TCB_ERR_CODE[res.code] || error_config_1.TCB_ERR_CODE.SYS_ERR,\n                errMsg: msg_1.apiFailMsg(options.api, res.message)\n            });\n        }\n        if (res.errcode) {\n            // wx error\n            throw new error_1.CloudSDKError({\n                errCode: res.errcode,\n                errMsg: msg_1.apiFailMsg(options.api, res.errmsg),\n            });\n        }\n        if (res.byteLength === 0) {\n            throw new error_1.CloudSDKError({\n                errCode: error_config_2.ERR_CODE.WX_SYSTEM_ERROR,\n                errMsg: msg_1.apiFailMsg(options.api, `empty response`)\n            });\n        }\n        throw new error_1.CloudSDKError({\n            errCode: error_config_1.TCB_ERR_CODE.SYS_ERR,\n            errMsg: msg_1.apiFailMsg(options.api, `unknown response ${res}`)\n        });\n    }\n    else {\n        // is buffer\n        if (method === 'callCompatibleWxOpenApi') {\n            let wxResp;\n            try {\n                wxResp = openapi_1.CommOpenApiResp.decode(res);\n            }\n            catch (decodeError) {\n                // fail to parse pb, may well be tcb error\n                let jsonParseResult;\n                try {\n                    jsonParseResult = JSON.parse(res.toString());\n                }\n                catch (parseTCBRespError) {\n                    // unknown error\n                    throw new error_1.CloudSDKError({\n                        errCode: error_config_2.ERR_CODE.WX_SYSTEM_ERROR,\n                        errMsg: msg_1.apiFailMsg(options.api, `unknown wx response received: ${decodeError}`)\n                    });\n                }\n                if (jsonParseResult.code && jsonParseResult.hasOwnProperty('message')) {\n                    // tcb error\n                    throw new error_1.CloudSDKError({\n                        errCode: error_config_1.TCB_ERR_CODE[jsonParseResult.code] || error_config_1.TCB_ERR_CODE.SYS_ERR,\n                        errMsg: msg_1.apiFailMsg(options.api, jsonParseResult.message)\n                    });\n                }\n                else {\n                    // unknown tcb error\n                    throw new error_1.CloudSDKError({\n                        errCode: error_config_1.TCB_ERR_CODE.SYS_ERR,\n                        errMsg: msg_1.apiFailMsg(options.api, JSON.stringify(jsonParseResult)),\n                    });\n                }\n            }\n            if (wxResp) {\n                if (wxResp.errorCode) {\n                    // wx system error, for example: no permission\n                    throw new error_1.CloudSDKError({\n                        errCode: error_config_2.ERR_CODE[error_config_2.ERR_CODE[wxResp.errorCode]] || wxResp.errorCode,\n                        errMsg: `${error_config_2.ERR_CODE[error_config_2.ERR_CODE.WX_SYSTEM_ERROR]}: error code: ${wxResp.errorCode}`\n                    });\n                }\n            }\n            else {\n                throw new error_1.CloudSDKError({\n                    errCode: error_config_2.ERR_CODE.WX_SYSTEM_ERROR,\n                    errMsg: msg_1.apiFailMsg(options.api, `empty wx response buffer`)\n                });\n            }\n            return wxResp;\n        }\n        else {\n            // callWxPayApi\n            let jsonParseResult;\n            try {\n                jsonParseResult = JSON.parse(res.toString());\n            }\n            catch (parseTCBRespError) {\n                return res;\n                // // unknown error\n                // throw new CloudSDKError({\n                //   errCode: ERR_CODE.WX_SYSTEM_ERROR as number,\n                //   errMsg: apiFailMsg(options.api, `unknown wx response received: ${res.toString()}`)\n                // })\n            }\n            if (jsonParseResult.code && jsonParseResult.hasOwnProperty('message')) {\n                // tcb error\n                throw new error_1.CloudSDKError({\n                    errCode: error_config_1.TCB_ERR_CODE[jsonParseResult.code] || error_config_1.TCB_ERR_CODE.SYS_ERR,\n                    errMsg: msg_1.apiFailMsg(options.api, jsonParseResult.message)\n                });\n            }\n            return jsonParseResult;\n        }\n    }\n}\nexports.callGeneralOpenAPI = callGeneralOpenAPI;\nasync function callWXOpenAPI(options, config) {\n    return callGeneralOpenAPI('callCompatibleWxOpenApi', options, config);\n}\nexports.callWXOpenAPI = callWXOpenAPI;\nasync function callWXPayAPI(options, config) {\n    return callGeneralOpenAPI('callWxPayApi', options, config);\n}\nexports.callWXPayAPI = callWXPayAPI;\n\n\n/***/ }),\n\n/***/ \"./src/api/cloud/provider/tcb/api/deleteFile.ts\":\n/*!******************************************************!*\\\n  !*** ./src/api/cloud/provider/tcb/api/deleteFile.ts ***!\n  \\******************************************************/\n/*! no static exports found */\n/***/ (function(module, exports, __webpack_require__) {\n\n\n\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.deleteFile = void 0;\nconst error_config_1 = __webpack_require__(/*! ../config/error.config */ \"./src/api/cloud/provider/tcb/config/error.config.ts\");\nconst error_config_2 = __webpack_require__(/*! ../../../../../config/error.config */ \"./src/config/error.config.ts\");\nconst sleep = (ms = 0) => new Promise(resolve => setTimeout(resolve, ms));\nasync function deleteFile(options, config) {\n    await sleep();\n    const tcbInstance = config.instance;\n    const res = await tcbInstance.deleteFile({\n        fileList: options.fileList\n    });\n    if (res.code && error_config_1.TCB_ERR_CODE[res.code] !== 0) {\n        throw {\n            errCode: error_config_1.TCB_ERR_CODE[res.code] || error_config_1.TCB_ERR_CODE.SYS_ERR,\n            errMsg: res.message,\n        };\n    }\n    else {\n        const fileList = (res.fileList || []).filter((s) => Boolean(s)).map((f) => {\n            if (f.code && error_config_1.TCB_ERR_CODE[f.code] !== 0) {\n                const code = error_config_1.TCB_ERR_CODE[f.code] || error_config_1.TCB_ERR_CODE.SYS_ERR;\n                return {\n                    fileID: f.fileID,\n                    status: error_config_1.TCB_ERR_CODE[f.code] || error_config_1.TCB_ERR_CODE.SYS_ERR,\n                    errMsg: error_config_2.ERR_CODE[code]\n                };\n            }\n            return {\n                fileID: f.fileID,\n                status: 0,\n                errMsg: 'ok',\n            };\n        });\n        //@ts-ignore\n        return {\n            fileList: fileList,\n            requestId: res.requestId,\n        };\n    }\n}\nexports.deleteFile = deleteFile;\n\n\n/***/ }),\n\n/***/ \"./src/api/cloud/provider/tcb/api/downloadFile.ts\":\n/*!********************************************************!*\\\n  !*** ./src/api/cloud/provider/tcb/api/downloadFile.ts ***!\n  \\********************************************************/\n/*! no static exports found */\n/***/ (function(module, exports, __webpack_require__) {\n\n\n\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.downloadFile = void 0;\nconst error_config_1 = __webpack_require__(/*! ../config/error.config */ \"./src/api/cloud/provider/tcb/config/error.config.ts\");\nconst sleep = (ms = 0) => new Promise(resolve => setTimeout(resolve, ms));\nasync function downloadFile(options, config) {\n    await sleep();\n    const statusCode = 200;\n    const tcbInstance = config.instance;\n    const res = await tcbInstance.downloadFile({\n        fileID: options.fileID\n    });\n    if (res.code && error_config_1.TCB_ERR_CODE[res.code] !== 0) {\n        throw {\n            errCode: error_config_1.TCB_ERR_CODE[res.code] || error_config_1.TCB_ERR_CODE.SYS_ERR,\n            errMsg: res.message,\n        };\n    }\n    return {\n        statusCode: statusCode,\n        fileContent: res.fileContent,\n    };\n}\nexports.downloadFile = downloadFile;\n\n\n/***/ }),\n\n/***/ \"./src/api/cloud/provider/tcb/api/getTempFileURL.ts\":\n/*!**********************************************************!*\\\n  !*** ./src/api/cloud/provider/tcb/api/getTempFileURL.ts ***!\n  \\**********************************************************/\n/*! no static exports found */\n/***/ (function(module, exports, __webpack_require__) {\n\n\n\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.getTempFileURL = void 0;\nconst error_config_1 = __webpack_require__(/*! ../config/error.config */ \"./src/api/cloud/provider/tcb/config/error.config.ts\");\nconst error_config_2 = __webpack_require__(/*! ../../../../../config/error.config */ \"./src/config/error.config.ts\");\nconst sleep = (ms = 0) => new Promise(resolve => setTimeout(resolve, ms));\nasync function getTempFileURL(options, config) {\n    await sleep();\n    const tcbInstance = config.instance;\n    const res = await tcbInstance.getTempFileURL({\n        fileList: options.fileList,\n    });\n    if (res.code && error_config_1.TCB_ERR_CODE[res.code] !== 0) {\n        throw {\n            errCode: error_config_1.TCB_ERR_CODE[res.code] || error_config_1.TCB_ERR_CODE.SYS_ERR,\n            errMsg: res.message,\n        };\n    }\n    else {\n        const fileList = (res.fileList || []).filter((s) => Boolean(s)).map((f, i) => {\n            if (f.code && error_config_1.TCB_ERR_CODE[f.code] !== 0) {\n                const code = error_config_1.TCB_ERR_CODE[f.code] || error_config_1.TCB_ERR_CODE.SYS_ERR;\n                return {\n                    fileID: f.fileID,\n                    status: error_config_1.TCB_ERR_CODE[f.code] || error_config_1.TCB_ERR_CODE.SYS_ERR,\n                    errMsg: error_config_2.ERR_CODE[code],\n                    maxAge: f.maxAge,\n                    tempFileURL: '',\n                };\n            }\n            return {\n                fileID: f.fileID,\n                status: 0,\n                errMsg: 'ok',\n                maxAge: f.maxAge,\n                tempFileURL: f.tempFileURL,\n            };\n        });\n        return {\n            fileList: fileList,\n            requestId: res.requestId,\n        };\n    }\n}\nexports.getTempFileURL = getTempFileURL;\n\n\n/***/ }),\n\n/***/ \"./src/api/cloud/provider/tcb/api/uploadFile.ts\":\n/*!******************************************************!*\\\n  !*** ./src/api/cloud/provider/tcb/api/uploadFile.ts ***!\n  \\******************************************************/\n/*! no static exports found */\n/***/ (function(module, exports, __webpack_require__) {\n\n\n\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.uploadFile = void 0;\nconst error_config_1 = __webpack_require__(/*! ../config/error.config */ \"./src/api/cloud/provider/tcb/config/error.config.ts\");\nconst sleep = (ms = 0) => new Promise(resolve => setTimeout(resolve, ms));\nasync function uploadFile(options, config) {\n    await sleep();\n    let statusCode = -1;\n    const tcbInstance = config.instance;\n    const res = await tcbInstance.uploadFile({\n        cloudPath: options.cloudPath,\n        fileContent: options.fileContent,\n    }, {\n        onResponseReceived(resp) {\n            statusCode = resp ? resp.statusCode : statusCode;\n        }\n    });\n    if (res.code && error_config_1.TCB_ERR_CODE[res.code] !== 0) {\n        throw {\n            errCode: error_config_1.TCB_ERR_CODE[res.code] || error_config_1.TCB_ERR_CODE.SYS_ERR,\n            errMsg: res.message,\n        };\n    }\n    else {\n        //@ts-ignore\n        return {\n            fileID: res.fileID,\n            requestId: res.requestId,\n            statusCode,\n        };\n    }\n}\nexports.uploadFile = uploadFile;\n\n\n/***/ }),\n\n/***/ \"./src/api/cloud/provider/tcb/config/error.config.ts\":\n/*!***********************************************************!*\\\n  !*** ./src/api/cloud/provider/tcb/config/error.config.ts ***!\n  \\***********************************************************/\n/*! no static exports found */\n/***/ (function(module, exports, __webpack_require__) {\n\n\n\n// provider should also register the error codes in src/config/error.config.ts\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.TCB_ERR_CODE = void 0;\nexports.TCB_ERR_CODE = {\n    // 通用\n    SUCCESS: 0,\n    SYS_ERR: -501001,\n    SERVER_TIMEOUT: -501002,\n    EXCEED_REQUEST_LIMIT: -501003,\n    EXCEED_CONCURRENT_REQUEST_LIMIT: -501004,\n    INVALIID_ENV: -501005,\n    INVALID_COMMON_PARAM: -501006,\n    INVALID_PARAM: -501007,\n    INVALID_REQUEST_SOURCE: -501008,\n    RESOURCE_NOT_INITIAL: -501009,\n    // 数据库\n    DATABASE_REQUEST_FAILED: -502001,\n    DATABASE_INVALID_OPERRATOR: -502002,\n    DATABASE_PERMISSION_DENIED: -502003,\n    DATABASE_COLLECTION_EXCEED_LIMIT: -502004,\n    DATABASE_COLLECTION_NOT_EXIST: -502005,\n    // 文件\n    STORAGE_REQUEST_FAIL: -503001,\n    STORAGE_EXCEED_AUTHORITY: -503002,\n    STORAGE_FILE_NONEXIST: -503003,\n    STORAGE_SIGN_PARAM_INVALID: -503004,\n    // 云函数\n    FUNCTIONS_REQUEST_FAIL: -504001,\n    FUNCTIONS_EXECUTE_FAIL: -504002,\n};\n\n\n/***/ }),\n\n/***/ \"./src/api/cloud/provider/tcb/index.ts\":\n/*!*********************************************!*\\\n  !*** ./src/api/cloud/provider/tcb/index.ts ***!\n  \\*********************************************/\n/*! no static exports found */\n/***/ (function(module, exports, __webpack_require__) {\n\n\n\nObject.defineProperty(exports, \"__esModule\", { value: true });\n// import tcb = require('tcb-admin-node')\nconst tcb = __webpack_require__(/*! @cloudbase/node-sdk */ \"@cloudbase/node-sdk\");\n// import { addDocument, queryDocument, updateDocument, removeDocument, countDocument } from './api/database'\nconst callFunction_1 = __webpack_require__(/*! ./api/callFunction */ \"./src/api/cloud/provider/tcb/api/callFunction.ts\");\nconst downloadFile_1 = __webpack_require__(/*! ./api/downloadFile */ \"./src/api/cloud/provider/tcb/api/downloadFile.ts\");\nconst uploadFile_1 = __webpack_require__(/*! ./api/uploadFile */ \"./src/api/cloud/provider/tcb/api/uploadFile.ts\");\nconst deleteFile_1 = __webpack_require__(/*! ./api/deleteFile */ \"./src/api/cloud/provider/tcb/api/deleteFile.ts\");\nconst getTempFileURL_1 = __webpack_require__(/*! ./api/getTempFileURL */ \"./src/api/cloud/provider/tcb/api/getTempFileURL.ts\");\nconst callOpenAPI_1 = __webpack_require__(/*! ./api/callOpenAPI */ \"./src/api/cloud/provider/tcb/api/callOpenAPI.ts\");\nconst callWXOpenAPI_1 = __webpack_require__(/*! ./api/callWXOpenAPI */ \"./src/api/cloud/provider/tcb/api/callWXOpenAPI.ts\");\nconst provider = {\n    // init\n    init: (config) => {\n        return tcb.init(Object.assign(Object.assign({}, config), { isHttp: process.env.TENCENTCLOUD_RUNENV === 'WX_LOCAL_SCF' }));\n    },\n    get DYNAMIC_CURRENT_ENV() {\n        return tcb.SYMBOL_CURRENT_ENV;\n    },\n    // api\n    api: {\n        callFunction: callFunction_1.callFunction,\n        downloadFile: downloadFile_1.downloadFile,\n        uploadFile: uploadFile_1.uploadFile,\n        deleteFile: deleteFile_1.deleteFile,\n        getTempFileURL: getTempFileURL_1.getTempFileURL,\n        callOpenAPI: callOpenAPI_1.callOpenAPI,\n        callWXOpenAPI: callWXOpenAPI_1.callWXOpenAPI,\n        callWXPayAPI: callWXOpenAPI_1.callWXPayAPI,\n    }\n};\nexports.default = provider;\n\n\n/***/ }),\n\n/***/ \"./src/api/cloudpay/index.ts\":\n/*!***********************************!*\\\n  !*** ./src/api/cloudpay/index.ts ***!\n  \\***********************************/\n/*! no static exports found */\n/***/ (function(module, exports, __webpack_require__) {\n\n\n\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.registerService = exports.CLOUDPAY_SERVICE_NAMESPACE_NAME = exports.CLOUDPAY_SERVICE_NAME = void 0;\nconst namespace_1 = __webpack_require__(/*! ./namespace */ \"./src/api/cloudpay/namespace/index.ts\");\nexports.CLOUDPAY_SERVICE_NAME = 'cloudPay';\nexports.CLOUDPAY_SERVICE_NAMESPACE_NAME = 'cloudPay';\nfunction createStorageService(cloud) {\n    return {\n        name: exports.CLOUDPAY_SERVICE_NAME,\n        getNamespace: () => {\n            return {\n                namespace: exports.CLOUDPAY_SERVICE_NAMESPACE_NAME,\n                apis: namespace_1.getAPIs(cloud),\n            };\n        },\n    };\n}\nfunction registerService(cloud) {\n    cloud.registerService(createStorageService(cloud));\n}\nexports.registerService = registerService;\n\n\n/***/ }),\n\n/***/ \"./src/api/cloudpay/namespace/generic.ts\":\n/*!***********************************************!*\\\n  !*** ./src/api/cloudpay/namespace/generic.ts ***!\n  \\***********************************************/\n/*! no static exports found */\n/***/ (function(module, exports, __webpack_require__) {\n\n\n\nObject.defineProperty(exports, \"__esModule\", { value: true });\nconst openapi_1 = __webpack_require__(/*! ../../../protobuf/openapi */ \"./src/protobuf/openapi.js\");\nconst error_1 = __webpack_require__(/*! utils/error */ \"./src/utils/error.ts\");\nconst msg_1 = __webpack_require__(/*! utils/msg */ \"./src/utils/msg.ts\");\nconst utils_1 = __webpack_require__(/*! utils/utils */ \"./src/utils/utils.ts\");\nconst generic_fn_1 = __webpack_require__(/*! utils/generic-fn */ \"./src/utils/generic-fn.ts\");\nconst type_1 = __webpack_require__(/*! utils/type */ \"./src/utils/type.ts\");\nconst error_config_1 = __webpack_require__(/*! config/error.config */ \"./src/config/error.config.ts\");\nfunction getCloudPayAPIs(cloud) {\n    const callable = (innerContext, data) => {\n        if (cloud.debug) {\n            console.log(`cloudPay.${innerContext.paths.join('.')} called with data:`, data);\n        }\n        const api = innerContext.paths.join('.');\n        // console.log(`call cloudpay args`, api, data, innerContext.appid)\n        return callCloudPayAPI({\n            api,\n            data,\n            appid: innerContext.appid,\n        });\n    };\n    return generic_fn_1.getProxyObject({\n        callable: cloud.wrapCommonAPICheck(callable),\n        paths: [],\n    });\n    async function callCloudPayAPI(options) {\n        const displayAPIName = `cloudPay.${options.api}`;\n        if (!options) {\n            throw error_1.returnAsFinalCloudSDKError({\n                errMsg: `Params for ${displayAPIName} must be an object instead of ` + typeof options,\n            }, displayAPIName);\n        }\n        // convert camel case to snake case\n        const data = utils_1.convertCase(options.data, {\n            from: 'camelcase',\n            to: 'snakecase',\n            recursive: true,\n        });\n        try {\n            // console.log(`callwxpayapi args`, options.api, options.appid)\n            const wxResp = await cloud.provider.api.callWXPayAPI({\n                api: options.api,\n                data: Buffer.from(JSON.stringify(data)),\n                apiOptions: Buffer.from(openapi_1.ApiOptions.encode({\n                    appid: options.appid,\n                }).finish()),\n            }, {\n                instance: cloud.instance,\n            });\n            let result;\n            if (type_1.isBuffer(wxResp)) {\n                if (options.api === 'downloadBill') {\n                    if (data.tar_type === 'GZIP') {\n                        result = {\n                            data: wxResp,\n                        };\n                    }\n                    else {\n                        result = {\n                            data: wxResp.toString(),\n                        };\n                    }\n                }\n                else {\n                    throw new error_1.CloudSDKError({\n                        errCode: error_config_1.ERR_CODE.WX_SYSTEM_ERROR,\n                        errMsg: msg_1.apiFailMsg(options.api, `unknown wx response received: ${wxResp.toString()}`)\n                    });\n                }\n            }\n            else {\n                // convert snake case to camel case\n                result = utils_1.convertCase(wxResp, {\n                    from: 'snakecase',\n                    to: 'camelcase',\n                    recursive: true,\n                });\n            }\n            return Object.assign(Object.assign({}, result), { errCode: 0, errMsg: `${displayAPIName}:ok` });\n        }\n        catch (e) {\n            throw error_1.returnAsFinalCloudSDKError(e, displayAPIName);\n        }\n    }\n}\nexports.default = getCloudPayAPIs;\n\n\n/***/ }),\n\n/***/ \"./src/api/cloudpay/namespace/index.ts\":\n/*!*********************************************!*\\\n  !*** ./src/api/cloudpay/namespace/index.ts ***!\n  \\*********************************************/\n/*! no static exports found */\n/***/ (function(module, exports, __webpack_require__) {\n\n\n\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.getAPIs = void 0;\nconst generic_1 = __webpack_require__(/*! ./generic */ \"./src/api/cloudpay/namespace/generic.ts\");\nfunction getAPIs(cloud) {\n    return generic_1.default(cloud);\n}\nexports.getAPIs = getAPIs;\n\n\n/***/ }),\n\n/***/ \"./src/api/database/api/api.ts\":\n/*!*************************************!*\\\n  !*** ./src/api/database/api/api.ts ***!\n  \\*************************************/\n/*! no static exports found */\n/***/ (function(module, exports, __webpack_require__) {\n\n\n\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.getAPIs = void 0;\nconst database_1 = __webpack_require__(/*! ./database */ \"./src/api/database/api/database.ts\");\nfunction getAPIs(cloud) {\n    return {\n        database: database_1.default.bind(null, cloud),\n    };\n}\nexports.getAPIs = getAPIs;\n\n\n/***/ }),\n\n/***/ \"./src/api/database/api/database.ts\":\n/*!******************************************!*\\\n  !*** ./src/api/database/api/database.ts ***!\n  \\******************************************/\n/*! no static exports found */\n/***/ (function(module, exports, __webpack_require__) {\n\n\n\nObject.defineProperty(exports, \"__esModule\", { value: true });\nconst collection_1 = __webpack_require__(/*! ./database/collection */ \"./src/api/database/api/database/collection.ts\");\nconst transaction_1 = __webpack_require__(/*! ./database/transaction/transaction */ \"./src/api/database/api/database/transaction/transaction.ts\");\nconst geo_1 = __webpack_require__(/*! ./database/geo/geo */ \"./src/api/database/api/database/geo/geo.ts\");\nconst msg_1 = __webpack_require__(/*! utils/msg */ \"./src/utils/msg.ts\");\nconst error_1 = __webpack_require__(/*! utils/error */ \"./src/utils/error.ts\");\nconst error_checker_1 = __webpack_require__(/*! ./database/helper/error-checker */ \"./src/api/database/api/database/helper/error-checker.ts\");\nfunction getDatabase(cloud, config) {\n    const Database = getDatabaseClass(cloud);\n    return new Database(config);\n}\nexports.default = getDatabase;\nconst getDatabaseClass = (cloud) => {\n    return class Database {\n        constructor(config = {}) {\n            this.runTransaction = (run, retryTimes) => {\n                return transaction_1.runTransaction.call(this, run, retryTimes);\n            };\n            this.cloud = cloud;\n            this.config = config;\n            if (cloud.isCloudInstance) {\n                this._db = cloud.instance.database();\n            }\n            if (config.env) {\n                this._db = cloud.getInstanceForEnv(config.env).database();\n            }\n            else {\n                this._db = cloud.instance.database();\n            }\n            this.command = this._db.command;\n            this.Geo = geo_1.initGeo(this._db.Geo);\n            this.serverDate = this._db.serverDate;\n            this.RegExp = this._db.RegExp;\n        }\n        collection(collectionName) {\n            return new collection_1.CollectionReference(this._db.collection(collectionName), collectionName, this);\n        }\n        startTransaction() {\n            return transaction_1.startTransaction.call(this);\n        }\n        async createCollection(collectionName) {\n            const apiName = 'createCollection';\n            try {\n                const result = await this._db.createCollection(collectionName);\n                error_checker_1.checkError(result, apiName);\n                return {\n                    errMsg: msg_1.apiSuccessMsg(apiName),\n                    requestId: result.requestId,\n                };\n            }\n            catch (e) {\n                throw error_1.returnAsFinalCloudSDKError(e, apiName);\n            }\n        }\n    };\n};\n\n\n/***/ }),\n\n/***/ \"./src/api/database/api/database/aggregate.ts\":\n/*!****************************************************!*\\\n  !*** ./src/api/database/api/database/aggregate.ts ***!\n  \\****************************************************/\n/*! no static exports found */\n/***/ (function(module, exports, __webpack_require__) {\n\n\n\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.Aggregate = exports.ORDER_DIRECTION = void 0;\nconst msg_1 = __webpack_require__(/*! utils/msg */ \"./src/utils/msg.ts\");\nconst error_1 = __webpack_require__(/*! utils/error */ \"./src/utils/error.ts\");\nconst error_checker_1 = __webpack_require__(/*! ./helper/error-checker */ \"./src/api/database/api/database/helper/error-checker.ts\");\nvar ORDER_DIRECTION;\n(function (ORDER_DIRECTION) {\n    ORDER_DIRECTION[\"ASC\"] = \"asc\";\n    ORDER_DIRECTION[\"DESC\"] = \"desc\";\n})(ORDER_DIRECTION = exports.ORDER_DIRECTION || (exports.ORDER_DIRECTION = {}));\nclass Aggregate {\n    constructor(_aggregate, collectionName, database) {\n        this._aggregate = _aggregate;\n        this.collectionName = collectionName;\n        this.database = database;\n    }\n    pushStage(stage, val) {\n        return new Aggregate(this._aggregate[stage](val), this.collectionName, this.database);\n    }\n    addFields(val) {\n        return this.pushStage('addFields', val);\n    }\n    bucket(val) {\n        return this.pushStage('bucket', val);\n    }\n    bucketAuto(val) {\n        return this.pushStage('bucketAuto', val);\n    }\n    collStats(val) {\n        return this.pushStage('collStats', val);\n    }\n    count(val) {\n        return this.pushStage('count', val);\n    }\n    facet(val) {\n        return this.pushStage('facet', val);\n    }\n    geoNear(val) {\n        return this.pushStage('geoNear', val);\n    }\n    graphLookup(val) {\n        return this.pushStage('graphLookup', val);\n    }\n    group(val) {\n        return this.pushStage('group', val);\n    }\n    indexStats() {\n        return this.pushStage('indexStats', {});\n    }\n    limit(val) {\n        return this.pushStage('limit', val);\n    }\n    lookup(val) {\n        return this.pushStage('lookup', val);\n    }\n    match(val) {\n        return this.pushStage('match', val);\n    }\n    out(val) {\n        return this.pushStage('out', val);\n    }\n    project(val) {\n        return this.pushStage('project', val);\n    }\n    redact(val) {\n        return this.pushStage('redact', val);\n    }\n    replaceRoot(val) {\n        return this.pushStage('replaceRoot', val);\n    }\n    sample(val) {\n        return this.pushStage('sample', val);\n    }\n    skip(val) {\n        return this.pushStage('skip', val);\n    }\n    sort(val) {\n        return this.pushStage('sort', val);\n    }\n    sortByCount(val) {\n        return this.pushStage('sortByCount', val);\n    }\n    unwind(val) {\n        return this.pushStage('unwind', val);\n    }\n    end() {\n        const apiName = 'collection.aggregate';\n        return new Promise(async (resolve, reject) => {\n            try {\n                const result = await this._aggregate.end();\n                error_checker_1.checkError(result, apiName);\n                resolve({\n                    list: result.data,\n                    errMsg: msg_1.apiSuccessMsg(apiName),\n                });\n            }\n            catch (err) {\n                reject(error_1.returnAsFinalCloudSDKError(err, apiName));\n            }\n        });\n    }\n}\nexports.Aggregate = Aggregate;\n\n\n/***/ }),\n\n/***/ \"./src/api/database/api/database/collection.ts\":\n/*!*****************************************************!*\\\n  !*** ./src/api/database/api/database/collection.ts ***!\n  \\*****************************************************/\n/*! no static exports found */\n/***/ (function(module, exports, __webpack_require__) {\n\n\n\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.CollectionReference = void 0;\nconst query_1 = __webpack_require__(/*! ./query */ \"./src/api/database/api/database/query.ts\");\nconst document_1 = __webpack_require__(/*! ./document */ \"./src/api/database/api/database/document.ts\");\nconst msg_1 = __webpack_require__(/*! utils/msg */ \"./src/utils/msg.ts\");\nconst error_1 = __webpack_require__(/*! utils/error */ \"./src/utils/error.ts\");\nconst error_checker_1 = __webpack_require__(/*! ./helper/error-checker */ \"./src/api/database/api/database/helper/error-checker.ts\");\nconst aggregate_1 = __webpack_require__(/*! ./aggregate */ \"./src/api/database/api/database/aggregate.ts\");\nclass CollectionReference extends query_1.Query {\n    constructor(_collection, collectionName, database) {\n        super(_collection, collectionName, database);\n        this._collection = _collection;\n        this.collectionName = collectionName;\n        this.database = database;\n    }\n    doc(docId) {\n        return new document_1.DocumentReference(this._collection.doc(docId), this, docId, this.database);\n    }\n    add(options) {\n        const apiName = 'collection.add';\n        return new Promise(async (resolve, reject) => {\n            try {\n                const addResult = await this._collection.add(options.data);\n                error_checker_1.checkError(addResult, apiName);\n                if (addResult.ids) {\n                    resolve({\n                        _ids: addResult.ids,\n                        errMsg: msg_1.apiSuccessMsg(apiName),\n                    });\n                }\n                else {\n                    resolve({\n                        _id: addResult.id,\n                        errMsg: msg_1.apiSuccessMsg(apiName),\n                    });\n                }\n            }\n            catch (err) {\n                reject(error_1.returnAsFinalCloudSDKError(error_checker_1.maybeTransformError(err), apiName));\n            }\n        });\n    }\n    aggregate() {\n        return new aggregate_1.Aggregate(this._collection.aggregate(), this.collectionName, this.database);\n    }\n}\nexports.CollectionReference = CollectionReference;\n\n\n/***/ }),\n\n/***/ \"./src/api/database/api/database/document.ts\":\n/*!***************************************************!*\\\n  !*** ./src/api/database/api/database/document.ts ***!\n  \\***************************************************/\n/*! no static exports found */\n/***/ (function(module, exports, __webpack_require__) {\n\n\n\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.DocumentReference = void 0;\nconst msg_1 = __webpack_require__(/*! utils/msg */ \"./src/utils/msg.ts\");\nconst error_1 = __webpack_require__(/*! utils/error */ \"./src/utils/error.ts\");\nconst assert_1 = __webpack_require__(/*! utils/assert */ \"./src/utils/assert.ts\");\nconst error_checker_1 = __webpack_require__(/*! ./helper/error-checker */ \"./src/api/database/api/database/helper/error-checker.ts\");\nconst GET_API_NAME = 'document.get';\nconst UPDATE_API_NAME = 'document.update';\nconst SET_API_NAME = 'document.set';\nconst REMOVE_API_NAME = 'document.remove';\nclass DocumentReference {\n    constructor(_document, collection, docId, database) {\n        this._document = _document;\n        this.collection = collection;\n        this.database = database;\n        this._id = docId;\n    }\n    field(object) {\n        assert_1.assertRequiredParam(object, 'object', 'field');\n        assert_1.assertType(object, 'object', 'field');\n        return new DocumentReference(this._document.field(object), this.collection, this._id, this.database);\n    }\n    get() {\n        return new Promise(async (resolve, reject) => {\n            try {\n                const throwOnNotFound = this.database.config.hasOwnProperty('throwOnNotFound') ? Boolean(this.database.config.throwOnNotFound) : true;\n                const _id = this._id;\n                const queryResult = await this._document.get();\n                error_checker_1.checkError(queryResult, GET_API_NAME);\n                // in normal doc.get, queryResult.data is array, in transaction, it is object instead\n                if (!queryResult.data || (Array.isArray(queryResult.data) && !queryResult.data.length)) {\n                    if (throwOnNotFound) {\n                        throw `document with _id ${_id} does not exist`;\n                    }\n                    else {\n                        resolve({\n                            data: null,\n                            errMsg: msg_1.apiSuccessMsg(GET_API_NAME),\n                        });\n                    }\n                }\n                else {\n                    resolve({\n                        data: Array.isArray(queryResult.data) ? queryResult.data[0] : queryResult.data,\n                        errMsg: msg_1.apiSuccessMsg(GET_API_NAME),\n                    });\n                }\n            }\n            catch (err) {\n                reject(error_1.returnAsFinalCloudSDKError(error_checker_1.maybeTransformError(err), GET_API_NAME));\n            }\n        });\n    }\n    set(options) {\n        return new Promise(async (resolve, reject) => {\n            try {\n                assert_1.assertType(options, {\n                    data: 'object',\n                });\n                assert_1.assertObjectNotEmpty({\n                    name: 'options.data',\n                    target: options.data,\n                });\n                const _id = this._id;\n                const setResult = await this._document.set(options.data);\n                error_checker_1.checkError(setResult, SET_API_NAME);\n                resolve({\n                    _id,\n                    errMsg: msg_1.apiSuccessMsg(SET_API_NAME),\n                    stats: {\n                        updated: setResult.updated,\n                        created: setResult.upsertedId ? 1 : 0,\n                    }\n                });\n            }\n            catch (err) {\n                reject(error_1.returnAsFinalCloudSDKError(error_checker_1.maybeTransformError(err), SET_API_NAME));\n            }\n        });\n    }\n    update(options) {\n        return new Promise(async (resolve, reject) => {\n            try {\n                assert_1.assertType(options, {\n                    data: 'object',\n                });\n                assert_1.assertObjectNotEmpty({\n                    name: 'options.data',\n                    target: options.data,\n                });\n                const updateResult = await this._document.update(options.data);\n                error_checker_1.checkError(updateResult, UPDATE_API_NAME);\n                resolve({\n                    stats: {\n                        updated: updateResult.updated,\n                    },\n                    errMsg: msg_1.apiSuccessMsg(UPDATE_API_NAME),\n                });\n            }\n            catch (err) {\n                reject(error_1.returnAsFinalCloudSDKError(error_checker_1.maybeTransformError(err), UPDATE_API_NAME));\n            }\n        });\n    }\n    remove(options) {\n        return new Promise(async (resolve, reject) => {\n            try {\n                // normal doc.remove -> _doc.remove\n                // transaction doc.remove -> _doc.delete\n                const removeResult = await (this._document.remove ? this._document.remove() : this._document.delete());\n                error_checker_1.checkError(removeResult, REMOVE_API_NAME);\n                resolve({\n                    stats: {\n                        removed: removeResult.deleted || 0,\n                    },\n                    errMsg: msg_1.apiSuccessMsg(REMOVE_API_NAME),\n                });\n            }\n            catch (err) {\n                reject(error_1.returnAsFinalCloudSDKError(error_checker_1.maybeTransformError(err), REMOVE_API_NAME));\n            }\n        });\n    }\n}\nexports.DocumentReference = DocumentReference;\n\n\n/***/ }),\n\n/***/ \"./src/api/database/api/database/geo/geo.ts\":\n/*!**************************************************!*\\\n  !*** ./src/api/database/api/database/geo/geo.ts ***!\n  \\**************************************************/\n/*! no static exports found */\n/***/ (function(module, exports, __webpack_require__) {\n\n\n\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.initGeo = void 0;\nexports.initGeo = (_geo) => {\n    const Geo = {};\n    for (const key in _geo) {\n        if (_geo.hasOwnProperty(key)) {\n            if (typeof _geo[key] === 'function') {\n                Geo[key] = function () {\n                    return new _geo[key](...arguments);\n                };\n            }\n            else {\n                Geo[key] = _geo[key];\n            }\n        }\n    }\n    return Geo;\n};\n\n\n/***/ }),\n\n/***/ \"./src/api/database/api/database/helper/error-checker.ts\":\n/*!***************************************************************!*\\\n  !*** ./src/api/database/api/database/helper/error-checker.ts ***!\n  \\***************************************************************/\n/*! no static exports found */\n/***/ (function(module, exports, __webpack_require__) {\n\n\n\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.maybeTransformError = exports.checkError = void 0;\nconst error_1 = __webpack_require__(/*! utils/error */ \"./src/utils/error.ts\");\nconst error_config_1 = __webpack_require__(/*! api/cloud/provider/tcb/config/error.config */ \"./src/api/cloud/provider/tcb/config/error.config.ts\");\nfunction checkError(tcbResult, apiName) {\n    if (tcbResult && tcbResult.code) {\n        throw error_1.toSDKError({\n            errCode: error_config_1.TCB_ERR_CODE[tcbResult.code] || error_config_1.TCB_ERR_CODE.SYS_ERR,\n            errMsg: tcbResult.message,\n        }, apiName);\n    }\n}\nexports.checkError = checkError;\nfunction maybeTransformError(maybeTcbError) {\n    if (maybeTcbError && maybeTcbError.code) {\n        return {\n            errCode: error_config_1.TCB_ERR_CODE[maybeTcbError.code] || error_config_1.TCB_ERR_CODE.SYS_ERR,\n            errMsg: maybeTcbError.message,\n        };\n    }\n    return maybeTcbError;\n}\nexports.maybeTransformError = maybeTransformError;\n\n\n/***/ }),\n\n/***/ \"./src/api/database/api/database/query.ts\":\n/*!************************************************!*\\\n  !*** ./src/api/database/api/database/query.ts ***!\n  \\************************************************/\n/*! no static exports found */\n/***/ (function(module, exports, __webpack_require__) {\n\n\n\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.Query = exports.ORDER_DIRECTION = void 0;\nconst msg_1 = __webpack_require__(/*! utils/msg */ \"./src/utils/msg.ts\");\nconst assert_1 = __webpack_require__(/*! utils/assert */ \"./src/utils/assert.ts\");\nconst error_1 = __webpack_require__(/*! utils/error */ \"./src/utils/error.ts\");\nconst error_checker_1 = __webpack_require__(/*! ./helper/error-checker */ \"./src/api/database/api/database/helper/error-checker.ts\");\nvar ORDER_DIRECTION;\n(function (ORDER_DIRECTION) {\n    ORDER_DIRECTION[\"ASC\"] = \"asc\";\n    ORDER_DIRECTION[\"DESC\"] = \"desc\";\n})(ORDER_DIRECTION = exports.ORDER_DIRECTION || (exports.ORDER_DIRECTION = {}));\nclass Query {\n    constructor(_query, collectionName, database) {\n        this._query = _query;\n        this.collectionName = collectionName;\n        this.database = database;\n    }\n    field(object) {\n        return new Query(this._query.field(object), this.collectionName, this.database);\n    }\n    where(condition) {\n        return new Query(this._query.where(condition), this.collectionName, this.database);\n    }\n    orderBy(fieldPath, order) {\n        return new Query(this._query.orderBy(fieldPath, order), this.collectionName, this.database);\n    }\n    limit(max) {\n        return new Query(this._query.limit(max), this.collectionName, this.database);\n    }\n    skip(offset) {\n        return new Query(this._query.skip(offset), this.collectionName, this.database);\n    }\n    get(options) {\n        const apiName = 'collection.get';\n        return new Promise(async (resolve, reject) => {\n            try {\n                const queryResult = await this._query.get();\n                error_checker_1.checkError(queryResult, apiName);\n                resolve({\n                    data: queryResult.data,\n                    errMsg: msg_1.apiSuccessMsg(apiName),\n                });\n            }\n            catch (err) {\n                reject(error_1.returnAsFinalCloudSDKError(error_checker_1.maybeTransformError(err), apiName));\n            }\n        });\n    }\n    update(options) {\n        const apiName = 'collection.update';\n        return new Promise(async (resolve, reject) => {\n            try {\n                assert_1.assertType(options.data, 'object');\n                assert_1.assertObjectNotEmpty({\n                    name: 'options.data',\n                    target: options.data,\n                });\n                let query = this._query;\n                if (options.multi === false) {\n                    query = this._query.options({\n                        multiple: false,\n                    });\n                }\n                const updateResult = await query.update(options.data);\n                error_checker_1.checkError(updateResult, apiName);\n                resolve({\n                    stats: {\n                        updated: updateResult.updated || 0,\n                    },\n                    errMsg: msg_1.apiSuccessMsg(apiName),\n                });\n            }\n            catch (err) {\n                reject(error_1.returnAsFinalCloudSDKError(error_checker_1.maybeTransformError(err), apiName));\n            }\n        });\n    }\n    remove(options = {}) {\n        const apiName = 'collection.remove';\n        return new Promise(async (resolve, reject) => {\n            try {\n                let query = this._query;\n                if (options.multi === false) {\n                    query = this._query.options({\n                        multiple: false,\n                    });\n                }\n                const removeResult = await query.remove();\n                error_checker_1.checkError(removeResult, apiName);\n                resolve({\n                    stats: {\n                        removed: removeResult.deleted,\n                    },\n                    errMsg: msg_1.apiSuccessMsg(apiName),\n                });\n            }\n            catch (err) {\n                reject(error_1.returnAsFinalCloudSDKError(error_checker_1.maybeTransformError(err), apiName));\n            }\n        });\n    }\n    count(options) {\n        const apiName = 'collection.count';\n        return new Promise(async (resolve, reject) => {\n            try {\n                const queryResult = await this._query.count();\n                error_checker_1.checkError(queryResult, apiName);\n                resolve({\n                    total: queryResult.total,\n                    errMsg: msg_1.apiSuccessMsg(apiName),\n                });\n            }\n            catch (err) {\n                reject(error_1.returnAsFinalCloudSDKError(error_checker_1.maybeTransformError(err), apiName));\n            }\n        });\n    }\n}\nexports.Query = Query;\n\n\n/***/ }),\n\n/***/ \"./src/api/database/api/database/transaction/transaction.ts\":\n/*!******************************************************************!*\\\n  !*** ./src/api/database/api/database/transaction/transaction.ts ***!\n  \\******************************************************************/\n/*! no static exports found */\n/***/ (function(module, exports, __webpack_require__) {\n\n\n\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.startTransaction = exports.runTransaction = exports.Transaction = void 0;\nconst collection_1 = __webpack_require__(/*! ../collection */ \"./src/api/database/api/database/collection.ts\");\nconst error_1 = __webpack_require__(/*! utils/error */ \"./src/utils/error.ts\");\nclass Transaction {\n    constructor(transaction, db) {\n        this._transaction = transaction;\n        this._db = db;\n    }\n    async init() {\n        return this._transaction.init();\n    }\n    collection(collName) {\n        if (!collName) {\n            throw new Error('Collection name is required');\n        }\n        return new collection_1.CollectionReference(this._transaction.collection(collName), collName, this._db);\n    }\n    async commit() {\n        try {\n            return this._transaction.commit();\n        }\n        catch (e) {\n            throw error_1.returnAsFinalCloudSDKError(e, `transaction.commit`);\n        }\n    }\n    async rollback(reason) {\n        try {\n            return this._transaction.rollback(reason);\n        }\n        catch (e) {\n            throw error_1.returnAsFinalCloudSDKError(e, `transaction.rollback`);\n        }\n    }\n}\nexports.Transaction = Transaction;\nasync function runTransaction(callback, times = 3) {\n    return this._db.runTransaction((_transaction) => {\n        const transaction = new Transaction(_transaction, this);\n        return callback(transaction);\n    }, times);\n}\nexports.runTransaction = runTransaction;\nasync function startTransaction() {\n    const _transaction = await this._db.startTransaction();\n    return new Transaction(_transaction, this);\n}\nexports.startTransaction = startTransaction;\n\n\n/***/ }),\n\n/***/ \"./src/api/database/index.ts\":\n/*!***********************************!*\\\n  !*** ./src/api/database/index.ts ***!\n  \\***********************************/\n/*! no static exports found */\n/***/ (function(module, exports, __webpack_require__) {\n\n\n\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.registerService = exports.DATABASE_SERVICE_NAME = void 0;\nconst api_1 = __webpack_require__(/*! ./api/api */ \"./src/api/database/api/api.ts\");\nexports.DATABASE_SERVICE_NAME = 'database';\nfunction createDatabaseService(cloud) {\n    return {\n        name: exports.DATABASE_SERVICE_NAME,\n        getAPIs: api_1.getAPIs.bind(null, cloud),\n    };\n}\nfunction registerService(cloud) {\n    cloud.registerService(createDatabaseService(cloud));\n}\nexports.registerService = registerService;\n\n\n/***/ }),\n\n/***/ \"./src/api/functions/api/api.ts\":\n/*!**************************************!*\\\n  !*** ./src/api/functions/api/api.ts ***!\n  \\**************************************/\n/*! no static exports found */\n/***/ (function(module, exports, __webpack_require__) {\n\n\n\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.getAPIs = void 0;\nconst callFunction_1 = __webpack_require__(/*! ./callFunction */ \"./src/api/functions/api/callFunction.ts\");\nfunction getAPIs(cloud) {\n    return {\n        callFunction: callFunction_1.default(cloud),\n    };\n}\nexports.getAPIs = getAPIs;\n\n\n/***/ }),\n\n/***/ \"./src/api/functions/api/callFunction.ts\":\n/*!***********************************************!*\\\n  !*** ./src/api/functions/api/callFunction.ts ***!\n  \\***********************************************/\n/*! no static exports found */\n/***/ (function(module, exports, __webpack_require__) {\n\n\n\nObject.defineProperty(exports, \"__esModule\", { value: true });\nconst assert_1 = __webpack_require__(/*! utils/assert */ \"./src/utils/assert.ts\");\nconst error_1 = __webpack_require__(/*! utils/error */ \"./src/utils/error.ts\");\nconst msg_1 = __webpack_require__(/*! utils/msg */ \"./src/utils/msg.ts\");\nconst utils_1 = __webpack_require__(/*! utils/utils */ \"./src/utils/utils.ts\");\nconst error_config_1 = __webpack_require__(/*! @/config/error.config */ \"./src/config/error.config.ts\");\nfunction getCallFunction(cloud) {\n    return function callFunction(options) {\n        const apiName = 'callFunction';\n        return new Promise(async (resolve, reject) => {\n            if (!options) {\n                return reject(error_1.returnAsFinalCloudSDKError({\n                    errMsg: 'Params for callFunction must be an object instead of ' + typeof options,\n                }, apiName));\n            }\n            try {\n                assert_1.assertType(options, {\n                    name: 'string'\n                });\n                if (options.hasOwnProperty('data')) {\n                    assert_1.assertType(options, {\n                        data: 'object'\n                    });\n                }\n            }\n            catch (e) {\n                return reject(error_1.returnAsFinalCloudSDKError(e, apiName));\n            }\n            try {\n                const tcbInstance = cloud.isCloudInstance ? cloud.instance : cloud.getInstanceForEnv(utils_1.getEnvFromAPIConfig(options.config, cloud.config, 'functions'));\n                const res = await tcbInstance.callFunction({\n                    name: options.name,\n                    data: options.data || {},\n                }, {\n                    timeout: options.timeout,\n                    retryOptions: options.retryOptions,\n                });\n                if (res.code && error_config_1.TCB_ERR_CODE[res.code] !== 0) {\n                    throw {\n                        errCode: error_config_1.TCB_ERR_CODE[res.code] || error_config_1.TCB_ERR_CODE.SYS_ERR,\n                        errMsg: `requestID ${res.requestId}, ${res.message}`,\n                        requestId: res.requestId,\n                    };\n                }\n                else {\n                    let parsedResult = res.result;\n                    try {\n                        if (typeof parsedResult === 'string') {\n                            parsedResult = JSON.parse(res.result);\n                        }\n                    }\n                    catch (_) {\n                        // no nothing\n                    }\n                    return resolve({\n                        result: parsedResult,\n                        errMsg: msg_1.apiSuccessMsg(apiName),\n                        requestID: res.requestId,\n                    });\n                }\n            }\n            catch (e) {\n                const error = error_1.returnAsFinalCloudSDKError(e, apiName);\n                // @ts-ignore\n                error.requestID = e.requestID;\n                return reject(error);\n            }\n        });\n    };\n}\nexports.default = getCallFunction;\n\n\n/***/ }),\n\n/***/ \"./src/api/functions/index.ts\":\n/*!************************************!*\\\n  !*** ./src/api/functions/index.ts ***!\n  \\************************************/\n/*! no static exports found */\n/***/ (function(module, exports, __webpack_require__) {\n\n\n\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.registerService = exports.FUNCTIONS_SERVICE_NAME = void 0;\nconst api_1 = __webpack_require__(/*! ./api/api */ \"./src/api/functions/api/api.ts\");\nexports.FUNCTIONS_SERVICE_NAME = 'functions';\nfunction createFunctionsService(cloud) {\n    return {\n        name: exports.FUNCTIONS_SERVICE_NAME,\n        getAPIs: api_1.getAPIs.bind(null, cloud),\n    };\n}\nfunction registerService(cloud) {\n    cloud.registerService(createFunctionsService(cloud));\n}\nexports.registerService = registerService;\n\n\n/***/ }),\n\n/***/ \"./src/api/index.ts\":\n/*!**************************!*\\\n  !*** ./src/api/index.ts ***!\n  \\**************************/\n/*! no static exports found */\n/***/ (function(module, exports, __webpack_require__) {\n\n\n\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.registerServices = void 0;\nconst database_1 = __webpack_require__(/*! ./database */ \"./src/api/database/index.ts\");\nconst storage_1 = __webpack_require__(/*! ./storage */ \"./src/api/storage/index.ts\");\nconst functions_1 = __webpack_require__(/*! ./functions */ \"./src/api/functions/index.ts\");\nconst open_1 = __webpack_require__(/*! ./open */ \"./src/api/open/index.ts\");\nconst utils_1 = __webpack_require__(/*! ./utils */ \"./src/api/utils/index.ts\");\nconst openapi_1 = __webpack_require__(/*! ./openapi */ \"./src/api/openapi/index.ts\");\nconst cloudpay_1 = __webpack_require__(/*! ./cloudpay */ \"./src/api/cloudpay/index.ts\");\nfunction registerServices(cloud) {\n    database_1.registerService(cloud);\n    storage_1.registerService(cloud);\n    functions_1.registerService(cloud);\n    open_1.registerService(cloud);\n    utils_1.registerService(cloud);\n    openapi_1.registerService(cloud);\n    cloudpay_1.registerService(cloud);\n}\nexports.registerServices = registerServices;\n\n\n/***/ }),\n\n/***/ \"./src/api/open/api/api.ts\":\n/*!*********************************!*\\\n  !*** ./src/api/open/api/api.ts ***!\n  \\*********************************/\n/*! no static exports found */\n/***/ (function(module, exports, __webpack_require__) {\n\n\n\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.getAPIs = void 0;\nconst callOpenAPI_1 = __webpack_require__(/*! ./callOpenAPI */ \"./src/api/open/api/callOpenAPI.ts\");\nconst getOpenData_1 = __webpack_require__(/*! ./getOpenData */ \"./src/api/open/api/getOpenData.ts\");\nconst getVoIPSign_1 = __webpack_require__(/*! ./getVoIPSign */ \"./src/api/open/api/getVoIPSign.ts\");\nconst getCloudCallSign_1 = __webpack_require__(/*! ./getCloudCallSign */ \"./src/api/open/api/getCloudCallSign.ts\");\nfunction getAPIs(cloud) {\n    return {\n        callOpenAPI: callOpenAPI_1.default(cloud),\n        getOpenData: getOpenData_1.default(cloud),\n        getVoIPSign: getVoIPSign_1.default(cloud),\n        getCloudCallSign: getCloudCallSign_1.default(cloud),\n    };\n}\nexports.getAPIs = getAPIs;\n\n\n/***/ }),\n\n/***/ \"./src/api/open/api/callOpenAPI.ts\":\n/*!*****************************************!*\\\n  !*** ./src/api/open/api/callOpenAPI.ts ***!\n  \\*****************************************/\n/*! no static exports found */\n/***/ (function(module, exports, __webpack_require__) {\n\n\n\nObject.defineProperty(exports, \"__esModule\", { value: true });\nconst assert_1 = __webpack_require__(/*! utils/assert */ \"./src/utils/assert.ts\");\nconst error_1 = __webpack_require__(/*! utils/error */ \"./src/utils/error.ts\");\nconst msg_1 = __webpack_require__(/*! utils/msg */ \"./src/utils/msg.ts\");\nconst utils_1 = __webpack_require__(/*! utils/utils */ \"./src/utils/utils.ts\");\nconst error_config_1 = __webpack_require__(/*! @/config/error.config */ \"./src/config/error.config.ts\");\nfunction getCallOpenAPI(cloud) {\n    return function callOpenAPI(options) {\n        const apiName = 'callOpenAPI';\n        return new Promise(async (resolve, reject) => {\n            if (!options) {\n                return reject(error_1.returnAsFinalCloudSDKError({\n                    errMsg: 'Params for callOpenAPI must be an object instead of ' + typeof options,\n                }, apiName));\n            }\n            try {\n                assert_1.assertType(options, {\n                    api: 'string'\n                });\n                if (options.data) {\n                    assert_1.assertType(options, {\n                        data: 'object'\n                    });\n                }\n            }\n            catch (e) {\n                return reject(error_1.returnAsFinalCloudSDKError(e, apiName));\n            }\n            try {\n                const tcbInstance = cloud.isCloudInstance ? cloud.instance : cloud.getInstanceForEnv(utils_1.getEnvFromAPIConfig(options.config, cloud.config, 'functions'));\n                const res = await tcbInstance.callWxOpenApi({\n                    apiName: options.api,\n                    requestData: options.data,\n                    cgiName: options.version === 'v1' ? undefined : 'commrpcv2',\n                }, options /** timeout, retryOptions */);\n                if (res.code && error_config_1.TCB_ERR_CODE[res.code] !== 0) {\n                    throw {\n                        errCode: error_config_1.TCB_ERR_CODE[res.code] || error_config_1.TCB_ERR_CODE.SYS_ERR,\n                        errMsg: res.message,\n                    };\n                }\n                const result = res.result;\n                let parsedResult = result;\n                try {\n                    if (typeof parsedResult === 'string') {\n                        parsedResult = JSON.parse(result);\n                    }\n                }\n                catch (_) {\n                    // no nothing\n                }\n                return resolve({\n                    result: parsedResult,\n                    errMsg: msg_1.apiSuccessMsg(apiName),\n                });\n            }\n            catch (e) {\n                const error = error_1.returnAsFinalCloudSDKError(e, apiName);\n                return reject(error);\n            }\n        });\n    };\n}\nexports.default = getCallOpenAPI;\n\n\n/***/ }),\n\n/***/ \"./src/api/open/api/getCloudCallSign.ts\":\n/*!**********************************************!*\\\n  !*** ./src/api/open/api/getCloudCallSign.ts ***!\n  \\**********************************************/\n/*! no static exports found */\n/***/ (function(module, exports, __webpack_require__) {\n\n\n\nObject.defineProperty(exports, \"__esModule\", { value: true });\nconst error_1 = __webpack_require__(/*! utils/error */ \"./src/utils/error.ts\");\nconst msg_1 = __webpack_require__(/*! utils/msg */ \"./src/utils/msg.ts\");\nconst openapi_1 = __webpack_require__(/*! ../../../protobuf/openapi */ \"./src/protobuf/openapi.js\");\nconst error_config_1 = __webpack_require__(/*! config/error.config */ \"./src/config/error.config.ts\");\nconst API_NAME = 'getCloudCallSign';\nfunction getGetCloudCallSign(cloud) {\n    return function getCloudCallSign(options) {\n        return new Promise(async (resolve, reject) => {\n            if (!options) {\n                return reject(error_1.returnAsFinalCloudSDKError({\n                    errMsg: 'Params for getCloudCallSign must be an object instead of ' + typeof options,\n                }, API_NAME));\n            }\n            try {\n                const getCloudCallSignReqBuffer = openapi_1.GetCloudCallSignReq.encode({\n                    parameterList: options.parameterList,\n                }).finish();\n                const svrkitData = {\n                    apiName: 'GetCloudCallSign',\n                    reqData: getCloudCallSignReqBuffer,\n                };\n                const pbMessage = openapi_1.CommApiData.encode({\n                    apiType: openapi_1.CommApiData.ApiType.SVRKIT_API,\n                    svrkitData,\n                }).finish();\n                const wxResp = await cloud.provider.api.callWXOpenAPI({\n                    api: 'GetCloudCallSign',\n                    data: Buffer.from(pbMessage),\n                }, {\n                    instance: cloud.instance,\n                });\n                if (wxResp.svrkitErrorCode !== 0) {\n                    throw {\n                        errCode: error_config_1.ERR_CODE.WX_SYSTEM_ERROR,\n                        errMsg: `internal svrkit error, code ${wxResp.svrkitErrorCode}`,\n                    };\n                }\n                if (!wxResp.respData) {\n                    throw {\n                        errCode: error_config_1.ERR_CODE.WX_SYSTEM_ERROR,\n                        errMsg: `internal svrkit error, empty respData`,\n                    };\n                }\n                const pbRespMsg = openapi_1.GetCloudCallSignResp.decode(wxResp.respData);\n                resolve({\n                    signature: pbRespMsg.signature,\n                    errMsg: msg_1.apiSuccessMsg(API_NAME),\n                    errCode: 0,\n                });\n            }\n            catch (e) {\n                const error = error_1.returnAsFinalCloudSDKError(e, API_NAME);\n                return reject(error);\n            }\n        });\n    };\n}\nexports.default = getGetCloudCallSign;\n\n\n/***/ }),\n\n/***/ \"./src/api/open/api/getOpenData.ts\":\n/*!*****************************************!*\\\n  !*** ./src/api/open/api/getOpenData.ts ***!\n  \\*****************************************/\n/*! no static exports found */\n/***/ (function(module, exports, __webpack_require__) {\n\n\n\nObject.defineProperty(exports, \"__esModule\", { value: true });\nconst error_1 = __webpack_require__(/*! utils/error */ \"./src/utils/error.ts\");\nconst msg_1 = __webpack_require__(/*! utils/msg */ \"./src/utils/msg.ts\");\nconst openapi_1 = __webpack_require__(/*! ../../../protobuf/openapi */ \"./src/protobuf/openapi.js\");\nconst error_config_1 = __webpack_require__(/*! config/error.config */ \"./src/config/error.config.ts\");\nconst API_NAME = 'getOpenData';\nfunction getGetOpenData(cloud) {\n    return function getOpenData(options) {\n        return new Promise(async (resolve, reject) => {\n            if (!options) {\n                return reject(error_1.returnAsFinalCloudSDKError({\n                    errMsg: 'Params for getOpenData must be an object instead of ' + typeof options,\n                }, API_NAME));\n            }\n            try {\n                if (!options.list) {\n                    throw new Error('list must be provided');\n                }\n            }\n            catch (e) {\n                return reject(error_1.returnAsFinalCloudSDKError(e, API_NAME));\n            }\n            try {\n                const getOpenDataReqBuffer = openapi_1.ApiGetOpenDataByCloudIdReq.encode({\n                    cloudidList: options.list,\n                }).finish();\n                const svrkitData = {\n                    apiName: 'ApiGetOpenDataByCloudId',\n                    reqData: getOpenDataReqBuffer,\n                };\n                const pbMessage = openapi_1.CommApiData.encode({\n                    apiType: openapi_1.CommApiData.ApiType.SVRKIT_API,\n                    svrkitData,\n                    appid: options.appid,\n                }).finish();\n                const wxResp = await cloud.provider.api.callWXOpenAPI({\n                    api: 'ApiGetOpenDataByCloudId',\n                    data: Buffer.from(pbMessage),\n                }, {\n                    instance: cloud.instance,\n                });\n                if (wxResp.svrkitErrorCode !== 0) {\n                    throw {\n                        errCode: error_config_1.ERR_CODE.WX_SYSTEM_ERROR,\n                        errMsg: `internal svrkit error, code ${wxResp.svrkitErrorCode}`,\n                    };\n                }\n                if (!wxResp.respData) {\n                    throw {\n                        errCode: error_config_1.ERR_CODE.WX_SYSTEM_ERROR,\n                        errMsg: `internal svrkit error, empty respData`,\n                    };\n                }\n                const pbRespMsg = openapi_1.ApiGetOpenDataByCloudIdResp.decode(wxResp.respData);\n                // const svrkitResponse = GetOpenDataRespMessage.toObject(pbRespMsg, {\n                //   longs: String,\n                // })\n                const openDataList = pbRespMsg.dataList.map(item => {\n                    if (!item.json) {\n                        throw {\n                            errCode: error_config_1.ERR_CODE.WX_SYSTEM_ERROR,\n                            errMsg: `internal svrkit error, empty openData json field for ${item.cloudId}`,\n                        };\n                    }\n                    return JSON.parse(item.json);\n                });\n                resolve({\n                    list: openDataList,\n                    errMsg: msg_1.apiSuccessMsg(API_NAME),\n                    errCode: 0,\n                });\n            }\n            catch (e) {\n                const error = error_1.returnAsFinalCloudSDKError(e, API_NAME);\n                return reject(error);\n            }\n        });\n    };\n}\nexports.default = getGetOpenData;\n\n\n/***/ }),\n\n/***/ \"./src/api/open/api/getVoIPSign.ts\":\n/*!*****************************************!*\\\n  !*** ./src/api/open/api/getVoIPSign.ts ***!\n  \\*****************************************/\n/*! no static exports found */\n/***/ (function(module, exports, __webpack_require__) {\n\n\n\nObject.defineProperty(exports, \"__esModule\", { value: true });\nconst assert_1 = __webpack_require__(/*! utils/assert */ \"./src/utils/assert.ts\");\nconst error_1 = __webpack_require__(/*! utils/error */ \"./src/utils/error.ts\");\nconst msg_1 = __webpack_require__(/*! utils/msg */ \"./src/utils/msg.ts\");\nconst openapi_1 = __webpack_require__(/*! ../../../protobuf/openapi */ \"./src/protobuf/openapi.js\");\nconst error_config_1 = __webpack_require__(/*! config/error.config */ \"./src/config/error.config.ts\");\nconst API_NAME = 'getVoIPSign';\nfunction getGetVoIPSign(cloud) {\n    return function getVoIPSign(options) {\n        return new Promise(async (resolve, reject) => {\n            if (!options) {\n                return reject(error_1.returnAsFinalCloudSDKError({\n                    errMsg: 'Params for getVoIPSign must be an object instead of ' + typeof options,\n                }, API_NAME));\n            }\n            try {\n                assert_1.assertType(options, {\n                    groupId: 'string',\n                    timestamp: 'number',\n                    nonce: 'string',\n                });\n            }\n            catch (e) {\n                return reject(error_1.returnAsFinalCloudSDKError(e, API_NAME));\n            }\n            try {\n                const getVoIPSignReqBuffer = openapi_1.ApiVoipSignReq.encode({\n                    groupId: options.groupId,\n                    timestamp: options.timestamp,\n                    nonce: options.nonce,\n                }).finish();\n                const svrkitData = {\n                    apiName: 'ApiVoipSign',\n                    reqData: getVoIPSignReqBuffer,\n                };\n                const pbMessage = openapi_1.CommApiData.encode({\n                    apiType: openapi_1.CommApiData.ApiType.SVRKIT_API,\n                    svrkitData,\n                }).finish();\n                const wxResp = await cloud.provider.api.callWXOpenAPI({\n                    api: 'ApiVoipSign',\n                    data: Buffer.from(pbMessage),\n                }, {\n                    instance: cloud.instance,\n                });\n                if (wxResp.svrkitErrorCode !== 0) {\n                    throw {\n                        errCode: error_config_1.ERR_CODE.WX_SYSTEM_ERROR,\n                        errMsg: `internal svrkit error, code ${wxResp.svrkitErrorCode}`,\n                    };\n                }\n                if (!wxResp.respData) {\n                    throw {\n                        errCode: error_config_1.ERR_CODE.WX_SYSTEM_ERROR,\n                        errMsg: `internal svrkit error, empty respData`,\n                    };\n                }\n                const pbRespMsg = openapi_1.ApiVoipSignResp.decode(wxResp.respData);\n                resolve({\n                    signature: pbRespMsg.signature,\n                    errMsg: msg_1.apiSuccessMsg(API_NAME),\n                    errCode: 0,\n                });\n            }\n            catch (e) {\n                const error = error_1.returnAsFinalCloudSDKError(e, API_NAME);\n                return reject(error);\n            }\n        });\n    };\n}\nexports.default = getGetVoIPSign;\n\n\n/***/ }),\n\n/***/ \"./src/api/open/index.ts\":\n/*!*******************************!*\\\n  !*** ./src/api/open/index.ts ***!\n  \\*******************************/\n/*! no static exports found */\n/***/ (function(module, exports, __webpack_require__) {\n\n\n\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.registerService = void 0;\nconst api_1 = __webpack_require__(/*! ./api/api */ \"./src/api/open/api/api.ts\");\nconst OPEN_SERVICE_NAME = 'open';\nfunction createOpenService(cloud) {\n    return {\n        name: OPEN_SERVICE_NAME,\n        getAPIs: api_1.getAPIs.bind(null, cloud),\n    };\n}\nfunction registerService(cloud) {\n    cloud.registerService(createOpenService(cloud));\n}\nexports.registerService = registerService;\n\n\n/***/ }),\n\n/***/ \"./src/api/openapi/index.ts\":\n/*!**********************************!*\\\n  !*** ./src/api/openapi/index.ts ***!\n  \\**********************************/\n/*! no static exports found */\n/***/ (function(module, exports, __webpack_require__) {\n\n\n\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.registerService = exports.OPENAPI_SERVICE_NAMESPACE_NAME = exports.OPENAPI_SERVICE_NAME = void 0;\nconst namespace_1 = __webpack_require__(/*! ./namespace */ \"./src/api/openapi/namespace/index.ts\");\nexports.OPENAPI_SERVICE_NAME = 'openapi';\nexports.OPENAPI_SERVICE_NAMESPACE_NAME = 'openapi';\nfunction createStorageService(cloud) {\n    return {\n        name: exports.OPENAPI_SERVICE_NAME,\n        getNamespace: () => {\n            return {\n                namespace: exports.OPENAPI_SERVICE_NAMESPACE_NAME,\n                apis: namespace_1.getAPIs(cloud),\n            };\n        },\n    };\n}\nfunction registerService(cloud) {\n    cloud.registerService(createStorageService(cloud));\n}\nexports.registerService = registerService;\n\n\n/***/ }),\n\n/***/ \"./src/api/openapi/namespace/generic.ts\":\n/*!**********************************************!*\\\n  !*** ./src/api/openapi/namespace/generic.ts ***!\n  \\**********************************************/\n/*! no static exports found */\n/***/ (function(module, exports, __webpack_require__) {\n\n\n\nObject.defineProperty(exports, \"__esModule\", { value: true });\nconst JSONBigIntDefault = __webpack_require__(/*! json-bigint */ \"json-bigint\");\nconst openapi_1 = __webpack_require__(/*! ../../../protobuf/openapi */ \"./src/protobuf/openapi.js\");\nconst type_1 = __webpack_require__(/*! ../../../utils/type */ \"./src/utils/type.ts\");\nconst error_1 = __webpack_require__(/*! ../../../utils/error */ \"./src/utils/error.ts\");\nconst msg_1 = __webpack_require__(/*! ../../../utils/msg */ \"./src/utils/msg.ts\");\nconst utils_1 = __webpack_require__(/*! ../../../utils/utils */ \"./src/utils/utils.ts\");\nconst error_config_1 = __webpack_require__(/*! ../../../config/error.config */ \"./src/config/error.config.ts\");\nconst param_converter_1 = __webpack_require__(/*! ./param-converter */ \"./src/api/openapi/namespace/param-converter.ts\");\nconst JSONBigInt = JSONBigIntDefault({\n    useNativeBigInt: true,\n});\nconst functionIntrinsicProperties = new Set(Object.getOwnPropertyNames(Function.prototype));\nconst getCallableObject = (options) => {\n    const f = function () { };\n    return new Proxy(f, {\n        get(target, prop) {\n            if (prop === 'toJSON') {\n                return {};\n            }\n            if (functionIntrinsicProperties.has(prop)) {\n                // @ts-ignore\n                return options.callable[prop];\n            }\n            else {\n                return getCallableObject(Object.assign(Object.assign({}, options), { paths: [...options.paths, prop] }));\n            }\n        },\n        apply(target, thisArg, args) {\n            return options.callable.call(thisArg, options, ...args);\n        },\n    });\n};\nconst getProxyObject = (options) => {\n    const f = () => { };\n    const proxy = new Proxy(f, {\n        get(target, prop) {\n            if (prop === 'toJSON') {\n                return {};\n            }\n            return getCallableObject(Object.assign(Object.assign({}, options), { paths: [prop] }));\n        },\n        apply(target, thisArg, args) {\n            // set options\n            if (!args[0] || !type_1.isObject(args[0])) {\n                throw new Error('an options object is expected');\n            }\n            return getProxyObject(Object.assign(Object.assign({}, args[0]), { \n                // some options are not writable\n                callable: options.callable, paths: options.paths }));\n        }\n    });\n    return proxy;\n};\nfunction getGenericOpenAPI(cloud) {\n    const callable = (innerContext, data) => {\n        if (cloud.debug) {\n            console.log(`openapi.${innerContext.paths.join('.')} called with data:`, data);\n        }\n        const api = innerContext.paths.join('.');\n        return callWXOpenAPI({\n            api,\n            data,\n            appid: innerContext.appid,\n            convertCase: innerContext.convertCase,\n            version: innerContext.version,\n        });\n    };\n    return getProxyObject({\n        callable: cloud.wrapCommonAPICheck(callable),\n        paths: [],\n    });\n    async function callWXOpenAPI(options) {\n        const displayAPIName = `openapi.${options.api}`;\n        const shouldConvertCase = options.convertCase !== false;\n        try {\n            // convert camel case to snake case\n            const data = shouldConvertCase ? utils_1.convertCase(options.data, {\n                from: 'camelcase',\n                to: 'snakecase',\n                recursive: true,\n            }) : options.data;\n            // convert data to pb buffer\n            const pairs = [];\n            for (const key in data) {\n                const val = data[key];\n                const convertResult = param_converter_1.convert(key, val, displayAPIName, data);\n                if (convertResult) {\n                    if (convertResult === param_converter_1.SYMBOL_DISCARD) {\n                        continue;\n                    }\n                    else {\n                        pairs.push(convertResult);\n                    }\n                }\n                else {\n                    const valStr = JSONBigInt.stringify(val);\n                    if (valStr !== undefined) {\n                        pairs.push({\n                            key,\n                            value: Buffer.from(valStr),\n                        });\n                    }\n                }\n            }\n            const pbMessage = openapi_1.CommApiData.encode({\n                apiType: openapi_1.CommApiData.ApiType.OPEN_API,\n                openapiData: {\n                    pairs,\n                },\n                appid: options.appid,\n            }).finish();\n            const wxResp = await cloud.provider.api.callWXOpenAPI({\n                api: options.api,\n                data: Buffer.from(pbMessage),\n            }, {\n                instance: cloud.instance,\n                version: options.version,\n            });\n            let result;\n            if (wxResp) {\n                if (/(application\\/json)|(text\\/plain)/.test(wxResp.contentType)) {\n                    // json response\n                    // NOTICE: sometimes the wx sever, for no reason, returns a json with content-type text/plain, and we have to deal with it...\n                    try {\n                        result = JSONBigInt.parse(wxResp.respData.toString());\n                    }\n                    catch (parseWXRespJSONError) {\n                        // wx server says it's a json but instead it is not a valid json\n                        // if the content-type is text/plain and is not a valid json, we can safely return the string back\n                        if (/text\\/plain/.test(wxResp.contentType)) {\n                            result = {\n                                result: wxResp.respData.toString()\n                            };\n                        }\n                        else {\n                            // internal error\n                            throw new error_1.CloudSDKError({\n                                errCode: error_config_1.ERR_CODE.WX_SYSTEM_ERROR,\n                                errMsg: msg_1.apiFailMsg(displayAPIName, `wechat server internal error, response body is invalid json: ${wxResp.respData.toString()}`)\n                            });\n                        }\n                    }\n                    if (result.errcode) {\n                        // wx error\n                        throw new error_1.CloudSDKError({\n                            errCode: result.errcode,\n                            errMsg: msg_1.apiFailMsg(displayAPIName, result.errmsg),\n                        });\n                    }\n                    else {\n                        delete result.errcode;\n                        delete result.errmsg;\n                        if (shouldConvertCase) {\n                            // convert snake case to camel case\n                            result = utils_1.convertCase(result, {\n                                from: 'snakecase',\n                                to: 'camelcase',\n                                recursive: true,\n                            });\n                        }\n                    }\n                }\n                else {\n                    // buffer response\n                    result = {\n                        contentType: wxResp.contentType.trim(),\n                        buffer: wxResp.respData,\n                    };\n                }\n            }\n            else {\n                throw {\n                    errCode: error_config_1.ERR_CODE.WX_SYSTEM_ERROR,\n                    errMsg: `internal server error, empty resp buffer`,\n                };\n            }\n            return Object.assign(Object.assign({}, result), { errMsg: msg_1.apiSuccessMsg(displayAPIName), errCode: 0 });\n        }\n        catch (e) {\n            throw error_1.returnAsFinalCloudSDKError(e, displayAPIName);\n        }\n    }\n}\nexports.default = getGenericOpenAPI;\n\n\n/***/ }),\n\n/***/ \"./src/api/openapi/namespace/index.ts\":\n/*!********************************************!*\\\n  !*** ./src/api/openapi/namespace/index.ts ***!\n  \\********************************************/\n/*! no static exports found */\n/***/ (function(module, exports, __webpack_require__) {\n\n\n\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.getAPIs = void 0;\nconst generic_1 = __webpack_require__(/*! ./generic */ \"./src/api/openapi/namespace/generic.ts\");\nfunction getAPIs(cloud) {\n    return generic_1.default(cloud);\n}\nexports.getAPIs = getAPIs;\n\n\n/***/ }),\n\n/***/ \"./src/api/openapi/namespace/param-converter.ts\":\n/*!******************************************************!*\\\n  !*** ./src/api/openapi/namespace/param-converter.ts ***!\n  \\******************************************************/\n/*! no static exports found */\n/***/ (function(module, exports, __webpack_require__) {\n\n\n\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.SYMBOL_DISCARD = exports.convert = void 0;\nconst type_1 = __webpack_require__(/*! utils/type */ \"./src/utils/type.ts\");\nconst mimetype_1 = __webpack_require__(/*! utils/mimetype */ \"./src/utils/mimetype.ts\");\nconst signature_1 = __webpack_require__(/*! api/utils/api/signature */ \"./src/api/utils/api/signature.ts\");\nconst converters = [];\nfunction convert(key, value, api, params) {\n    for (const converter of converters) {\n        if (converter.shouldConvert(key, value, api, params)) {\n            return converter.convert(key, value, api, params);\n        }\n    }\n    return;\n}\nexports.convert = convert;\nexports.SYMBOL_DISCARD = Symbol('discard');\nconst mediaConverter = {\n    shouldConvert: (key, val, api, params) => {\n        return type_1.isObject(val) && (val.contentType || val.content_type) && val.value && type_1.isBuffer(val.value);\n    },\n    convert: (key, val, api, params) => {\n        const contentType = (val.contentType || val.content_type).toString();\n        const fileExtension = mimetype_1.mimeTypeToFileExtension(contentType);\n        if (!fileExtension) {\n            console.warn(`[${api}] the input param ${key}.contentType is not a valid mime type`);\n        }\n        let filename = val.fileName;\n        if (!filename) {\n            filename = `${key}.${fileExtension || 'png'}`;\n        }\n        return {\n            key,\n            value: val.value,\n            contentType,\n            filename,\n        };\n    },\n};\nconst midasAPIConfig = {\n    'openapi.midas.getBalance': {\n        cgiPath: '/cgi-bin/midas/getbalance',\n        method: 'POST',\n    },\n    'openapi.midas.getBalanceSandbox': {\n        cgiPath: '/cgi-bin/midas/sandbox/getbalance',\n        method: 'POST',\n    },\n    'openapi.midas.pay': {\n        cgiPath: '/cgi-bin/midas/pay',\n        method: 'POST',\n    },\n    'openapi.midas.paySandbox': {\n        cgiPath: '/cgi-bin/midas/sandbox/pay',\n        method: 'POST',\n    },\n    'openapi.midas.cancelPay': {\n        cgiPath: '/cgi-bin/midas/cancelpay',\n        method: 'POST',\n    },\n    'openapi.midas.cancelPaySandbox': {\n        cgiPath: '/cgi-bin/midas/sandbox/cancelpay',\n        method: 'POST',\n    },\n    'openapi.midas.present': {\n        cgiPath: '/cgi-bin/midas/present',\n        method: 'POST',\n    },\n    'openapi.midas.presentSandbox': {\n        cgiPath: '/cgi-bin/midas/sandbox/present',\n        method: 'POST',\n    },\n};\nconst midasSignatureConverter = {\n    shouldConvert: (key, val, api, params) => {\n        return (val instanceof signature_1.MidasSignature) && Boolean(midasAPIConfig[api]);\n    },\n    convert: (key, val, api, params) => {\n        const { cgiPath, method } = midasAPIConfig[api];\n        return {\n            key,\n            value: Buffer.from(JSON.stringify(val.compute(cgiPath, method, val.secret, params))),\n        };\n    },\n};\nconverters.push(mediaConverter);\nconverters.push(midasSignatureConverter);\n\n\n/***/ }),\n\n/***/ \"./src/api/storage/api/api.ts\":\n/*!************************************!*\\\n  !*** ./src/api/storage/api/api.ts ***!\n  \\************************************/\n/*! no static exports found */\n/***/ (function(module, exports, __webpack_require__) {\n\n\n\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.getAPIs = void 0;\nconst uploadFile_1 = __webpack_require__(/*! ./uploadFile */ \"./src/api/storage/api/uploadFile.ts\");\nconst downloadFile_1 = __webpack_require__(/*! ./downloadFile */ \"./src/api/storage/api/downloadFile.ts\");\nconst getTempFileURL_1 = __webpack_require__(/*! ./getTempFileURL */ \"./src/api/storage/api/getTempFileURL.ts\");\nconst deleteFile_1 = __webpack_require__(/*! ./deleteFile */ \"./src/api/storage/api/deleteFile.ts\");\nfunction getAPIs(cloud) {\n    return {\n        uploadFile: uploadFile_1.default(cloud),\n        downloadFile: downloadFile_1.default(cloud),\n        getTempFileURL: getTempFileURL_1.default(cloud),\n        deleteFile: deleteFile_1.default(cloud),\n    };\n}\nexports.getAPIs = getAPIs;\n\n\n/***/ }),\n\n/***/ \"./src/api/storage/api/deleteFile.ts\":\n/*!*******************************************!*\\\n  !*** ./src/api/storage/api/deleteFile.ts ***!\n  \\*******************************************/\n/*! no static exports found */\n/***/ (function(module, exports, __webpack_require__) {\n\n\n\nObject.defineProperty(exports, \"__esModule\", { value: true });\nconst assert_1 = __webpack_require__(/*! utils/assert */ \"./src/utils/assert.ts\");\nconst error_1 = __webpack_require__(/*! utils/error */ \"./src/utils/error.ts\");\nconst msg_1 = __webpack_require__(/*! utils/msg */ \"./src/utils/msg.ts\");\nconst utils_1 = __webpack_require__(/*! utils/utils */ \"./src/utils/utils.ts\");\nfunction getDeleteFile(cloud) {\n    return function deleteFile(options) {\n        const apiName = 'deleteFile';\n        return new Promise(async (resolve, reject) => {\n            if (!options) {\n                return reject(error_1.returnAsFinalCloudSDKError({\n                    errMsg: 'Params for deleteFile must be an object instead of ' + typeof options,\n                }, apiName));\n            }\n            try {\n                assert_1.assertType(options, {\n                    fileList: 'array'\n                });\n                options.fileList.forEach((f, i) => {\n                    if (typeof f !== 'string') {\n                        throw new Error(`Type of fileList[${i}] must be string instead of ${typeof f}`);\n                    }\n                });\n            }\n            catch (e) {\n                return reject(error_1.returnAsFinalCloudSDKError(e, apiName));\n            }\n            try {\n                const result = await cloud.provider.api.deleteFile({\n                    fileList: options.fileList\n                }, {\n                    instance: cloud.isCloudInstance ? cloud.instance : cloud.getInstanceForEnv(utils_1.getEnvFromAPIConfig(options.config, cloud.config, 'storage')),\n                });\n                return resolve({\n                    fileList: result.fileList,\n                    errMsg: msg_1.apiSuccessMsg(apiName),\n                });\n            }\n            catch (e) {\n                return reject(error_1.returnAsFinalCloudSDKError(e, apiName));\n            }\n        });\n    };\n}\nexports.default = getDeleteFile;\n\n\n/***/ }),\n\n/***/ \"./src/api/storage/api/downloadFile.ts\":\n/*!*********************************************!*\\\n  !*** ./src/api/storage/api/downloadFile.ts ***!\n  \\*********************************************/\n/*! no static exports found */\n/***/ (function(module, exports, __webpack_require__) {\n\n\n\nObject.defineProperty(exports, \"__esModule\", { value: true });\nconst assert_1 = __webpack_require__(/*! utils/assert */ \"./src/utils/assert.ts\");\nconst error_1 = __webpack_require__(/*! utils/error */ \"./src/utils/error.ts\");\nconst msg_1 = __webpack_require__(/*! utils/msg */ \"./src/utils/msg.ts\");\nconst utils_1 = __webpack_require__(/*! utils/utils */ \"./src/utils/utils.ts\");\nfunction getDownloadFile(cloud) {\n    return function downloadFile(options) {\n        const apiName = 'downloadFile';\n        return new Promise(async (resolve, reject) => {\n            if (!options) {\n                return reject(error_1.returnAsFinalCloudSDKError({\n                    errMsg: 'Params for downloadFile must be an object instead of ' + typeof options,\n                }, apiName));\n            }\n            try {\n                assert_1.assertType(options, {\n                    fileID: 'string',\n                });\n            }\n            catch (e) {\n                return reject(error_1.returnAsFinalCloudSDKError(e, apiName));\n            }\n            try {\n                const result = await cloud.provider.api.downloadFile({\n                    fileID: options.fileID,\n                }, {\n                    instance: cloud.isCloudInstance ? cloud.instance : cloud.getInstanceForEnv(utils_1.getEnvFromAPIConfig(options.config, cloud.config, 'storage')),\n                });\n                return resolve({\n                    fileContent: result.fileContent,\n                    statusCode: result.statusCode,\n                    errMsg: msg_1.apiSuccessMsg(apiName),\n                });\n            }\n            catch (e) {\n                return reject(error_1.returnAsFinalCloudSDKError(e, apiName));\n            }\n        });\n    };\n}\nexports.default = getDownloadFile;\n\n\n/***/ }),\n\n/***/ \"./src/api/storage/api/getTempFileURL.ts\":\n/*!***********************************************!*\\\n  !*** ./src/api/storage/api/getTempFileURL.ts ***!\n  \\***********************************************/\n/*! no static exports found */\n/***/ (function(module, exports, __webpack_require__) {\n\n\n\nObject.defineProperty(exports, \"__esModule\", { value: true });\nconst assert_1 = __webpack_require__(/*! utils/assert */ \"./src/utils/assert.ts\");\nconst error_1 = __webpack_require__(/*! utils/error */ \"./src/utils/error.ts\");\nconst msg_1 = __webpack_require__(/*! utils/msg */ \"./src/utils/msg.ts\");\nconst utils_1 = __webpack_require__(/*! utils/utils */ \"./src/utils/utils.ts\");\nfunction getGetTempFileURL(cloud) {\n    return function getTempFileURL(options) {\n        const apiName = 'getTempFileURL';\n        return new Promise(async (resolve, reject) => {\n            if (!options) {\n                return reject(error_1.returnAsFinalCloudSDKError({\n                    errMsg: 'Params for getTempFileURL must be an object instead of ' + typeof options,\n                }, apiName));\n            }\n            try {\n                assert_1.assertType(options, {\n                    fileList: 'array',\n                });\n            }\n            catch (e) {\n                return reject(error_1.returnAsFinalCloudSDKError(e, apiName));\n            }\n            try {\n                const result = await cloud.provider.api.getTempFileURL({\n                    fileList: options.fileList,\n                }, {\n                    instance: cloud.isCloudInstance ? cloud.instance : cloud.getInstanceForEnv(utils_1.getEnvFromAPIConfig(options.config, cloud.config, 'storage')),\n                });\n                return resolve({\n                    fileList: result.fileList,\n                    errMsg: msg_1.apiSuccessMsg(apiName),\n                });\n            }\n            catch (e) {\n                return reject(error_1.returnAsFinalCloudSDKError(e, apiName));\n            }\n        });\n    };\n}\nexports.default = getGetTempFileURL;\n\n\n/***/ }),\n\n/***/ \"./src/api/storage/api/uploadFile.ts\":\n/*!*******************************************!*\\\n  !*** ./src/api/storage/api/uploadFile.ts ***!\n  \\*******************************************/\n/*! no static exports found */\n/***/ (function(module, exports, __webpack_require__) {\n\n\n\nObject.defineProperty(exports, \"__esModule\", { value: true });\nconst assert_1 = __webpack_require__(/*! utils/assert */ \"./src/utils/assert.ts\");\nconst error_1 = __webpack_require__(/*! utils/error */ \"./src/utils/error.ts\");\nconst msg_1 = __webpack_require__(/*! utils/msg */ \"./src/utils/msg.ts\");\nconst utils_1 = __webpack_require__(/*! utils/utils */ \"./src/utils/utils.ts\");\nfunction getUploadFile(cloud) {\n    return function uploadFile(options) {\n        const apiName = 'uploadFile';\n        return new Promise(async (resolve, reject) => {\n            if (!options) {\n                return reject(error_1.returnAsFinalCloudSDKError({\n                    errMsg: 'Params for uploadFile must be an object instead of ' + typeof options,\n                }, apiName));\n            }\n            try {\n                assert_1.assertType(options, {\n                    cloudPath: 'string',\n                });\n                if (!options.fileContent) {\n                    return reject(error_1.returnAsFinalCloudSDKError(new Error('Type of fileContent must be fs.ReadStream instead of ' + typeof options.fileContent), apiName));\n                }\n            }\n            catch (e) {\n                return reject(error_1.returnAsFinalCloudSDKError(e, apiName));\n            }\n            const header = options.header || {};\n            try {\n                const result = await cloud.provider.api.uploadFile({\n                    fileContent: options.fileContent,\n                    cloudPath: options.cloudPath,\n                    header,\n                }, {\n                    instance: cloud.isCloudInstance ? cloud.instance : cloud.getInstanceForEnv(utils_1.getEnvFromAPIConfig(options.config, cloud.config, 'storage')),\n                });\n                return resolve({\n                    fileID: result.fileID,\n                    statusCode: result.statusCode,\n                    errMsg: msg_1.apiSuccessMsg(apiName),\n                });\n            }\n            catch (e) {\n                return reject(error_1.returnAsFinalCloudSDKError(e, apiName));\n            }\n        });\n    };\n}\nexports.default = getUploadFile;\n\n\n/***/ }),\n\n/***/ \"./src/api/storage/index.ts\":\n/*!**********************************!*\\\n  !*** ./src/api/storage/index.ts ***!\n  \\**********************************/\n/*! no static exports found */\n/***/ (function(module, exports, __webpack_require__) {\n\n\n\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.registerService = exports.STORAGE_SERVICE_NAME = void 0;\nconst api_1 = __webpack_require__(/*! ./api/api */ \"./src/api/storage/api/api.ts\");\nexports.STORAGE_SERVICE_NAME = 'storage';\nfunction createStorageService(cloud) {\n    return {\n        name: exports.STORAGE_SERVICE_NAME,\n        getAPIs: api_1.getAPIs.bind(null, cloud),\n    };\n}\nfunction registerService(cloud) {\n    cloud.registerService(createStorageService(cloud));\n}\nexports.registerService = registerService;\n\n\n/***/ }),\n\n/***/ \"./src/api/utils/api/api.ts\":\n/*!**********************************!*\\\n  !*** ./src/api/utils/api/api.ts ***!\n  \\**********************************/\n/*! no static exports found */\n/***/ (function(module, exports, __webpack_require__) {\n\n\n\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.getAPIs = void 0;\nconst getWXContext_1 = __webpack_require__(/*! ./getWXContext */ \"./src/api/utils/api/getWXContext.ts\");\nconst signature_1 = __webpack_require__(/*! ./signature */ \"./src/api/utils/api/signature.ts\");\nfunction getAPIs() {\n    return {\n        getWXContext: getWXContext_1.default,\n        signature: signature_1.default,\n    };\n}\nexports.getAPIs = getAPIs;\n\n\n/***/ }),\n\n/***/ \"./src/api/utils/api/getWXContext.ts\":\n/*!*******************************************!*\\\n  !*** ./src/api/utils/api/getWXContext.ts ***!\n  \\*******************************************/\n/*! no static exports found */\n/***/ (function(module, exports, __webpack_require__) {\n\n\n\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.isNumber = exports.isContextKeyInBlacklist = void 0;\nconst error_1 = __webpack_require__(/*! utils/error */ \"./src/utils/error.ts\");\nconst WX_PREFIX = 'WX_';\nconst CONTEXT_KEYS_BLACKLIST = [\n    'API_TOKEN',\n    'TRIGGER_API_TOKEN_V0',\n];\nfunction isContextKeyInBlacklist(key) {\n    return CONTEXT_KEYS_BLACKLIST.some(v => v === key || (WX_PREFIX + v) === key);\n}\nexports.isContextKeyInBlacklist = isContextKeyInBlacklist;\nfunction isNumber(val) {\n    return /^[-]?\\d+$/.test(val);\n}\nexports.isNumber = isNumber;\nfunction getWXContext() {\n    const apiName = 'getWXContext';\n    const wxContext = {};\n    if (!process.env.WX_CONTEXT_KEYS)\n        return wxContext;\n    try {\n        const contextKeys = process.env.WX_CONTEXT_KEYS.split(',');\n        for (const key of contextKeys) {\n            if (!key)\n                continue;\n            if (isContextKeyInBlacklist(key))\n                continue;\n            let val = process.env[key];\n            if (val === undefined)\n                continue;\n            if (isNumber(val)) {\n                val = parseInt(val);\n            }\n            if (key.startsWith(WX_PREFIX) && key.length > 3) {\n                wxContext[key.slice(3)] = val;\n            }\n            else {\n                wxContext[key] = val;\n            }\n        }\n        wxContext.ENV = process.env.TCB_ENV || process.env.SCF_NAMESPACE;\n        if (process.env.TCB_SOURCE) {\n            wxContext.SOURCE = process.env.TCB_SOURCE;\n        }\n        return wxContext;\n    }\n    catch (e) {\n        const error = error_1.returnAsFinalCloudSDKError(e, apiName);\n        throw error;\n    }\n}\nexports.default = getWXContext;\n\n\n/***/ }),\n\n/***/ \"./src/api/utils/api/signature.ts\":\n/*!****************************************!*\\\n  !*** ./src/api/utils/api/signature.ts ***!\n  \\****************************************/\n/*! no static exports found */\n/***/ (function(module, exports, __webpack_require__) {\n\n\n\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.MidasSignature = exports.signature = void 0;\nconst crypto = __webpack_require__(/*! crypto */ \"crypto\");\nconst utils_1 = __webpack_require__(/*! utils/utils */ \"./src/utils/utils.ts\");\nfunction signature(options) {\n    switch (options.type) {\n        case 'midas': {\n            return new MidasSignature(options);\n        }\n    }\n}\nexports.signature = signature;\nclass MidasSignature {\n    constructor(options) {\n        this.type = 'midas';\n        if (!options.params || !Array.isArray(options.params)) {\n            throw new Error('options.params must be a string array');\n        }\n        if (!options.secret) {\n            throw new Error('options.secret must be provided');\n        }\n        this.params = options.params;\n        this.secret = options.secret;\n    }\n    compute(cgiPath, method, secret, paramValues) {\n        // sort params by ascii\n        const paramNames = [...this.params].sort().map(name => utils_1.convertCase(name, {\n            from: 'camelcase',\n            to: 'snakecase',\n        }));\n        // get params string\n        const paramStr = paramNames.map(paramName => {\n            if (!paramValues.hasOwnProperty(paramName)) {\n                throw new Error(`Cannot compute signature: lack of param '${paramName}'`);\n            }\n            return `${paramName}=${paramValues[paramName]}`;\n        }).join('&');\n        // concatenate params string, cgi path, and midas secret\n        const signSource = paramStr + `&org_loc=${cgiPath}&method=${method}&secret=${secret}`;\n        // sign\n        const signature = crypto.createHmac('sha256', secret).update(signSource).digest('hex');\n        return signature;\n    }\n}\nexports.MidasSignature = MidasSignature;\nexports.default = signature;\n\n\n/***/ }),\n\n/***/ \"./src/api/utils/index.ts\":\n/*!********************************!*\\\n  !*** ./src/api/utils/index.ts ***!\n  \\********************************/\n/*! no static exports found */\n/***/ (function(module, exports, __webpack_require__) {\n\n\n\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.registerService = void 0;\nconst api_1 = __webpack_require__(/*! ./api/api */ \"./src/api/utils/api/api.ts\");\nconst UTILS_SERVICE_NAME = 'utils';\nfunction createUtilsService() {\n    return {\n        name: UTILS_SERVICE_NAME,\n        getAPIs: api_1.getAPIs,\n        initRequired: false,\n    };\n}\nfunction registerService(cloud) {\n    cloud.registerService(createUtilsService());\n}\nexports.registerService = registerService;\n\n\n/***/ }),\n\n/***/ \"./src/config/error.config.ts\":\n/*!************************************!*\\\n  !*** ./src/config/error.config.ts ***!\n  \\************************************/\n/*! no static exports found */\n/***/ (function(module, exports, __webpack_require__) {\n\n\n\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.TCB_ERR_CODE = exports.ERR_CODE = void 0;\nexports.ERR_CODE = {\n    '-1': 'unknown error',\n    UNKNOWN_ERROR: -1,\n    // 以 6 开始的是由微信服务器侧产生的错误码\n    // 以 5 开始的是由腾讯云侧产生的错误码\n    // 以 4 开始的是本地 SDK 产生的错误\n    // 接下来两位表示具体业务类型：01通用，02数据库，03文件，04云函数\n    // 最后三位表示具体的错误\n    // 小程序 SDK 云函数\n    '-404001': 'empty call result',\n    SDK_FUNCTIONS_EMPTY_CALL_RESULT: -404001,\n    '-404002': 'empty event id',\n    SDK_FUNCTIONS_EMPTY_EVENT_ID: -404002,\n    '-404003': 'empty poll url',\n    SDK_FUNCTIONS_EMPTY_POLL_URL: -404003,\n    '-404004': 'empty poll result json',\n    SDK_FUNCTIONS_EMPTY_POLL_RESULT_JSON: -404004,\n    '-404005': 'exceed max poll retry',\n    SDK_FUNCTIONS_EXCEED_MAX_POLL_RETRY: -404005,\n    '-404006': 'empty poll result base resp',\n    SDK_FUNCTIONS_EMPTY_POLL_RESULT_BASE_RESP: -404006,\n    '-404007': 'error while polling for the result, poll result base resp ret %s',\n    SDK_FUNCTIONS_POLL_RESULT_BASE_RESP_RET_ABNORMAL: -404007,\n    '-404008': 'error while polling for the result, polling server return a status code of %s',\n    SDK_FUNCTIONS_POLL_RESULT_STATUS_CODE_ERROR: -404008,\n    '-404009': 'error while polling for the result: %s',\n    SDK_FUNCTIONS_POLL_ERROR: -404009,\n    // 微信服务器\n    '-601001': 'system error',\n    WX_SYSTEM_ERROR: -601001,\n    '-601002': 'system args error',\n    WX_SYSTEM_ARGS_ERROR: -601002,\n    '-601003': 'system network error',\n    WX_SYSTEM_NETWORK_ERROR: -601003,\n    '-601004': 'api permission denied',\n    WX_API_PERMISSION_DENIED: -601004,\n    '-601005': 'invalid cloudID',\n    WX_INVALID_CLOUDID: -601005,\n    '-601006': 'cloudID expired',\n    WX_CLOUDID_EXPIRED: -601006,\n    '-601007': 'cloudID and calling user does not match',\n    WX_CLOUDID_USER_NOT_MATCH: -601007,\n    '-601008': 'server-side request timedout',\n    WX_SERVER_REQUEST_TIMEOUT: -601008,\n    '-601009': 'missing mobile phone',\n    WX_MISSING_MOBILE_PHONE: -601009,\n    '-601010': 'no write permission',\n    WX_NO_WRITE_PERMISSION: -601010,\n    '-601011': 'no privilege permission',\n    WX_NO_PRIVILEGE_PERMISSION: -601011,\n    '-601012': 'unauthorized env',\n    WX_UNAUTHORIZED_ENV: -601012,\n    '-601013': 'no multiend permission',\n    WX_NO_MULTIEND_PERMISSION: -601013,\n    '-601015': 'access denied (cloudfunction cloudbase_auth returns empty errCode)',\n    WX_CLOUDBASE_AUTH_RETURN_EMPTY_ERRCODE: -601015,\n    '-601016': 'missing env auth info',\n    WX_MISSING_ENV_AUTH_INFO: -601016,\n    '-601017': 'access denied (cloudbase_auth returns non-zero errCode)',\n    WX_CLOUDBASE_AUTH_RETURN_NON_ZERO_ERRCODE: -601017,\n    '-602018': 'unauthorized API',\n    WX_UNAUTHORIZED_API: -601018,\n    '-602001': 'database query result size exceed limit (1MB)',\n    WX_DATABASE_QUERY_SIZE_EXCEED_LIMIT: -602001,\n    '-604001': 'cloudfunction result size exceed limit (1MB)',\n    WX_CLOUDFUNCTION_RESULT_SIZE_EXCEED_LIMIT: -604001,\n    '-604100': 'API not found',\n    WX_FUNCTIONS_SERVER_OPENAPI_NOT_FOUND: -604100,\n    '-604101': 'function has no permission to call this API',\n    WX_FUNCTIONS_SERVER_OPENAPI_NO_PERMISSION: -604101,\n    '-604102': 'call open API timeout',\n    WX_FUNCTIONS_SERVER_OPENAPI_TIMEOUT: -604102,\n    '-604103': 'call open API system error',\n    WX_FUNCTIONS_SERVER_OPENAPI_SYSTEM_ERROR: -604103,\n    '-604104': 'illegal source of invocation',\n    WX_FUNCTIONS_SERVER_OPENAPI_ILLEGAL_INVOCATION_SOURCE: -604104,\n    // 腾讯云通用\n    '-501001': 'resource system error',\n    TCB_RESOURCE_SYSTEM_ERROR: -501001,\n    '-501002': 'resource server timeout',\n    TCB_RESOURCE_SERVER_TIMEOUT: -501002,\n    '-501003': 'exceed request limit',\n    TCB_EXCEED_REQUEST_LIMIT: -501003,\n    '-501004': 'exceed concurrent request limit',\n    TCB_EXCEED_CONCURRENT_REQUEST_LIMIT: -501004,\n    '-501005': 'invalid env',\n    TCB_INVALID_ENV: -501005,\n    '-501006': 'invalid common parameters',\n    TCB_INVALID_COMMON_PARAM: -501006,\n    '-501007': 'invalid parameters',\n    TCB_INVALID_PARAM: -501007,\n    '-501008': 'invalid request source',\n    TCB_INVALID_REQUEST_SOURCE: -501008,\n    '-501009': 'resource not initialized',\n    TCB_RESOURCE_NOT_INITIALIZED: -501009,\n    // 腾讯云数据库\n    '-502001': 'database request fail',\n    TCB_DB_REQUEST_FAIL: -502001,\n    '-502002': 'database invalid command',\n    TCB_DB_INVALID_COMMAND: -502002,\n    '-502003': 'database permission denied',\n    TCB_DB_PERMISSION_DENIED: -502003,\n    '-502004': 'database exceed collection limit',\n    TCB_DB_EXCEED_COLLECTION_LIMIT: -502004,\n    '-502005': 'database collection not exists',\n    TCB_DB_COLLECTION_NOT_EXISTS: -502005,\n    // 腾讯云文件管理\n    '-503001': 'storage request fail',\n    TCB_STORAGE_REQUEST_FAIL: -503001,\n    '-503002': 'storage permission denied',\n    TCB_STORAGE_PERMISSION_DENIED: -503002,\n    '-503003': 'storage file not exists',\n    TCB_STORAGE_FILE_NOT_EXISTS: -503003,\n    '-503004': 'storage invalid sign parameter',\n    TCB_STORAGE_INVALID_SIGN_PARAM: -503004,\n    // 腾讯云云函数\n    '-504001': 'functions request fail',\n    TCB_FUNCTIONS_REQUEST_FAIL: -504001,\n    '-504002': 'functions execute fail',\n    TCB_FUNCTIONS_EXEC_FAIL: -504002,\n};\nexports.TCB_ERR_CODE = {\n    // 通用\n    SUCCESS: 0,\n    SYS_ERR: -501001,\n    SERVER_TIMEOUT: -501002,\n    EXCEED_REQUEST_LIMIT: -501003,\n    EXCEED_CONCURRENT_REQUEST_LIMIT: -501004,\n    INVALIID_ENV: -501005,\n    INVALID_COMMON_PARAM: -501006,\n    INVALID_PARAM: -501007,\n    INVALID_REQUEST_SOURCE: -501008,\n    RESOURCE_NOT_INITIAL: -501009,\n    // 数据库\n    DATABASE_REQUEST_FAILED: -502001,\n    DATABASE_INVALID_OPERRATOR: -502002,\n    DATABASE_PERMISSION_DENIED: -502003,\n    DATABASE_COLLECTION_EXCEED_LIMIT: -502004,\n    DATABASE_COLLECTION_NOT_EXIST: -502005,\n    // 文件\n    STORAGE_REQUEST_FAIL: -503001,\n    STORAGE_EXCEED_AUTHORITY: -503002,\n    STORAGE_FILE_NONEXIST: -503003,\n    STORAGE_SIGN_PARAM_INVALID: -503004,\n    // 云函数\n    FUNCTIONS_REQUEST_FAIL: -504001,\n    FUNCTIONS_EXECUTE_FAIL: -504002,\n};\n\n\n/***/ }),\n\n/***/ \"./src/index.ts\":\n/*!**********************!*\\\n  !*** ./src/index.ts ***!\n  \\**********************/\n/*! no static exports found */\n/***/ (function(module, exports, __webpack_require__) {\n\n\n\nconst cloud_1 = __webpack_require__(/*! ./api/cloud */ \"./src/api/cloud/index.ts\");\nmodule.exports = cloud_1.default.exportAPI;\n\n\n/***/ }),\n\n/***/ \"./src/protobuf/openapi.js\":\n/*!*********************************!*\\\n  !*** ./src/protobuf/openapi.js ***!\n  \\*********************************/\n/*! no static exports found */\n/***/ (function(module, exports, __webpack_require__) {\n\n\n/*eslint-disable block-scoped-var, id-length, no-control-regex, no-magic-numbers, no-prototype-builtins, no-redeclare, no-shadow, no-var, sort-vars*/\n\n\nvar $protobuf = __webpack_require__(/*! protobufjs/minimal */ \"protobufjs/minimal\");\n\n// Common aliases\nvar $Reader = $protobuf.Reader, $Writer = $protobuf.Writer, $util = $protobuf.util;\n\n// Exported root namespace\nvar $root = $protobuf.roots[\"default\"] || ($protobuf.roots[\"default\"] = {});\n\n$root.KeyValuePair = (function() {\n\n    /**\n     * Properties of a KeyValuePair.\n     * @exports IKeyValuePair\n     * @interface IKeyValuePair\n     * @property {string|null} [key] KeyValuePair key\n     * @property {Uint8Array|null} [value] KeyValuePair value\n     * @property {string|null} [contenttype] KeyValuePair contenttype\n     * @property {string|null} [filename] KeyValuePair filename\n     */\n\n    /**\n     * Constructs a new KeyValuePair.\n     * @exports KeyValuePair\n     * @classdesc Represents a KeyValuePair.\n     * @implements IKeyValuePair\n     * @constructor\n     * @param {IKeyValuePair=} [properties] Properties to set\n     */\n    function KeyValuePair(properties) {\n        if (properties)\n            for (var keys = Object.keys(properties), i = 0; i < keys.length; ++i)\n                if (properties[keys[i]] != null)\n                    this[keys[i]] = properties[keys[i]];\n    }\n\n    /**\n     * KeyValuePair key.\n     * @member {string} key\n     * @memberof KeyValuePair\n     * @instance\n     */\n    KeyValuePair.prototype.key = \"\";\n\n    /**\n     * KeyValuePair value.\n     * @member {Uint8Array} value\n     * @memberof KeyValuePair\n     * @instance\n     */\n    KeyValuePair.prototype.value = $util.newBuffer([]);\n\n    /**\n     * KeyValuePair contenttype.\n     * @member {string} contenttype\n     * @memberof KeyValuePair\n     * @instance\n     */\n    KeyValuePair.prototype.contenttype = \"\";\n\n    /**\n     * KeyValuePair filename.\n     * @member {string} filename\n     * @memberof KeyValuePair\n     * @instance\n     */\n    KeyValuePair.prototype.filename = \"\";\n\n    /**\n     * Creates a new KeyValuePair instance using the specified properties.\n     * @function create\n     * @memberof KeyValuePair\n     * @static\n     * @param {IKeyValuePair=} [properties] Properties to set\n     * @returns {KeyValuePair} KeyValuePair instance\n     */\n    KeyValuePair.create = function create(properties) {\n        return new KeyValuePair(properties);\n    };\n\n    /**\n     * Encodes the specified KeyValuePair message. Does not implicitly {@link KeyValuePair.verify|verify} messages.\n     * @function encode\n     * @memberof KeyValuePair\n     * @static\n     * @param {IKeyValuePair} message KeyValuePair message or plain object to encode\n     * @param {$protobuf.Writer} [writer] Writer to encode to\n     * @returns {$protobuf.Writer} Writer\n     */\n    KeyValuePair.encode = function encode(message, writer) {\n        if (!writer)\n            writer = $Writer.create();\n        if (message.key != null && message.hasOwnProperty(\"key\"))\n            writer.uint32(/* id 1, wireType 2 =*/10).string(message.key);\n        if (message.value != null && message.hasOwnProperty(\"value\"))\n            writer.uint32(/* id 2, wireType 2 =*/18).bytes(message.value);\n        if (message.contenttype != null && message.hasOwnProperty(\"contenttype\"))\n            writer.uint32(/* id 3, wireType 2 =*/26).string(message.contenttype);\n        if (message.filename != null && message.hasOwnProperty(\"filename\"))\n            writer.uint32(/* id 4, wireType 2 =*/34).string(message.filename);\n        return writer;\n    };\n\n    /**\n     * Encodes the specified KeyValuePair message, length delimited. Does not implicitly {@link KeyValuePair.verify|verify} messages.\n     * @function encodeDelimited\n     * @memberof KeyValuePair\n     * @static\n     * @param {IKeyValuePair} message KeyValuePair message or plain object to encode\n     * @param {$protobuf.Writer} [writer] Writer to encode to\n     * @returns {$protobuf.Writer} Writer\n     */\n    KeyValuePair.encodeDelimited = function encodeDelimited(message, writer) {\n        return this.encode(message, writer).ldelim();\n    };\n\n    /**\n     * Decodes a KeyValuePair message from the specified reader or buffer.\n     * @function decode\n     * @memberof KeyValuePair\n     * @static\n     * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from\n     * @param {number} [length] Message length if known beforehand\n     * @returns {KeyValuePair} KeyValuePair\n     * @throws {Error} If the payload is not a reader or valid buffer\n     * @throws {$protobuf.util.ProtocolError} If required fields are missing\n     */\n    KeyValuePair.decode = function decode(reader, length) {\n        if (!(reader instanceof $Reader))\n            reader = $Reader.create(reader);\n        var end = length === undefined ? reader.len : reader.pos + length, message = new $root.KeyValuePair();\n        while (reader.pos < end) {\n            var tag = reader.uint32();\n            switch (tag >>> 3) {\n            case 1:\n                message.key = reader.string();\n                break;\n            case 2:\n                message.value = reader.bytes();\n                break;\n            case 3:\n                message.contenttype = reader.string();\n                break;\n            case 4:\n                message.filename = reader.string();\n                break;\n            default:\n                reader.skipType(tag & 7);\n                break;\n            }\n        }\n        return message;\n    };\n\n    /**\n     * Decodes a KeyValuePair message from the specified reader or buffer, length delimited.\n     * @function decodeDelimited\n     * @memberof KeyValuePair\n     * @static\n     * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from\n     * @returns {KeyValuePair} KeyValuePair\n     * @throws {Error} If the payload is not a reader or valid buffer\n     * @throws {$protobuf.util.ProtocolError} If required fields are missing\n     */\n    KeyValuePair.decodeDelimited = function decodeDelimited(reader) {\n        if (!(reader instanceof $Reader))\n            reader = new $Reader(reader);\n        return this.decode(reader, reader.uint32());\n    };\n\n    /**\n     * Verifies a KeyValuePair message.\n     * @function verify\n     * @memberof KeyValuePair\n     * @static\n     * @param {Object.<string,*>} message Plain object to verify\n     * @returns {string|null} `null` if valid, otherwise the reason why it is not\n     */\n    KeyValuePair.verify = function verify(message) {\n        if (typeof message !== \"object\" || message === null)\n            return \"object expected\";\n        if (message.key != null && message.hasOwnProperty(\"key\"))\n            if (!$util.isString(message.key))\n                return \"key: string expected\";\n        if (message.value != null && message.hasOwnProperty(\"value\"))\n            if (!(message.value && typeof message.value.length === \"number\" || $util.isString(message.value)))\n                return \"value: buffer expected\";\n        if (message.contenttype != null && message.hasOwnProperty(\"contenttype\"))\n            if (!$util.isString(message.contenttype))\n                return \"contenttype: string expected\";\n        if (message.filename != null && message.hasOwnProperty(\"filename\"))\n            if (!$util.isString(message.filename))\n                return \"filename: string expected\";\n        return null;\n    };\n\n    /**\n     * Creates a KeyValuePair message from a plain object. Also converts values to their respective internal types.\n     * @function fromObject\n     * @memberof KeyValuePair\n     * @static\n     * @param {Object.<string,*>} object Plain object\n     * @returns {KeyValuePair} KeyValuePair\n     */\n    KeyValuePair.fromObject = function fromObject(object) {\n        if (object instanceof $root.KeyValuePair)\n            return object;\n        var message = new $root.KeyValuePair();\n        if (object.key != null)\n            message.key = String(object.key);\n        if (object.value != null)\n            if (typeof object.value === \"string\")\n                $util.base64.decode(object.value, message.value = $util.newBuffer($util.base64.length(object.value)), 0);\n            else if (object.value.length)\n                message.value = object.value;\n        if (object.contenttype != null)\n            message.contenttype = String(object.contenttype);\n        if (object.filename != null)\n            message.filename = String(object.filename);\n        return message;\n    };\n\n    /**\n     * Creates a plain object from a KeyValuePair message. Also converts values to other types if specified.\n     * @function toObject\n     * @memberof KeyValuePair\n     * @static\n     * @param {KeyValuePair} message KeyValuePair\n     * @param {$protobuf.IConversionOptions} [options] Conversion options\n     * @returns {Object.<string,*>} Plain object\n     */\n    KeyValuePair.toObject = function toObject(message, options) {\n        if (!options)\n            options = {};\n        var object = {};\n        if (options.defaults) {\n            object.key = \"\";\n            if (options.bytes === String)\n                object.value = \"\";\n            else {\n                object.value = [];\n                if (options.bytes !== Array)\n                    object.value = $util.newBuffer(object.value);\n            }\n            object.contenttype = \"\";\n            object.filename = \"\";\n        }\n        if (message.key != null && message.hasOwnProperty(\"key\"))\n            object.key = message.key;\n        if (message.value != null && message.hasOwnProperty(\"value\"))\n            object.value = options.bytes === String ? $util.base64.encode(message.value, 0, message.value.length) : options.bytes === Array ? Array.prototype.slice.call(message.value) : message.value;\n        if (message.contenttype != null && message.hasOwnProperty(\"contenttype\"))\n            object.contenttype = message.contenttype;\n        if (message.filename != null && message.hasOwnProperty(\"filename\"))\n            object.filename = message.filename;\n        return object;\n    };\n\n    /**\n     * Converts this KeyValuePair to JSON.\n     * @function toJSON\n     * @memberof KeyValuePair\n     * @instance\n     * @returns {Object.<string,*>} JSON object\n     */\n    KeyValuePair.prototype.toJSON = function toJSON() {\n        return this.constructor.toObject(this, $protobuf.util.toJSONOptions);\n    };\n\n    return KeyValuePair;\n})();\n\n$root.OpenApiData = (function() {\n\n    /**\n     * Properties of an OpenApiData.\n     * @exports IOpenApiData\n     * @interface IOpenApiData\n     * @property {Array.<IKeyValuePair>|null} [pairs] OpenApiData pairs\n     */\n\n    /**\n     * Constructs a new OpenApiData.\n     * @exports OpenApiData\n     * @classdesc Represents an OpenApiData.\n     * @implements IOpenApiData\n     * @constructor\n     * @param {IOpenApiData=} [properties] Properties to set\n     */\n    function OpenApiData(properties) {\n        this.pairs = [];\n        if (properties)\n            for (var keys = Object.keys(properties), i = 0; i < keys.length; ++i)\n                if (properties[keys[i]] != null)\n                    this[keys[i]] = properties[keys[i]];\n    }\n\n    /**\n     * OpenApiData pairs.\n     * @member {Array.<IKeyValuePair>} pairs\n     * @memberof OpenApiData\n     * @instance\n     */\n    OpenApiData.prototype.pairs = $util.emptyArray;\n\n    /**\n     * Creates a new OpenApiData instance using the specified properties.\n     * @function create\n     * @memberof OpenApiData\n     * @static\n     * @param {IOpenApiData=} [properties] Properties to set\n     * @returns {OpenApiData} OpenApiData instance\n     */\n    OpenApiData.create = function create(properties) {\n        return new OpenApiData(properties);\n    };\n\n    /**\n     * Encodes the specified OpenApiData message. Does not implicitly {@link OpenApiData.verify|verify} messages.\n     * @function encode\n     * @memberof OpenApiData\n     * @static\n     * @param {IOpenApiData} message OpenApiData message or plain object to encode\n     * @param {$protobuf.Writer} [writer] Writer to encode to\n     * @returns {$protobuf.Writer} Writer\n     */\n    OpenApiData.encode = function encode(message, writer) {\n        if (!writer)\n            writer = $Writer.create();\n        if (message.pairs != null && message.pairs.length)\n            for (var i = 0; i < message.pairs.length; ++i)\n                $root.KeyValuePair.encode(message.pairs[i], writer.uint32(/* id 1, wireType 2 =*/10).fork()).ldelim();\n        return writer;\n    };\n\n    /**\n     * Encodes the specified OpenApiData message, length delimited. Does not implicitly {@link OpenApiData.verify|verify} messages.\n     * @function encodeDelimited\n     * @memberof OpenApiData\n     * @static\n     * @param {IOpenApiData} message OpenApiData message or plain object to encode\n     * @param {$protobuf.Writer} [writer] Writer to encode to\n     * @returns {$protobuf.Writer} Writer\n     */\n    OpenApiData.encodeDelimited = function encodeDelimited(message, writer) {\n        return this.encode(message, writer).ldelim();\n    };\n\n    /**\n     * Decodes an OpenApiData message from the specified reader or buffer.\n     * @function decode\n     * @memberof OpenApiData\n     * @static\n     * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from\n     * @param {number} [length] Message length if known beforehand\n     * @returns {OpenApiData} OpenApiData\n     * @throws {Error} If the payload is not a reader or valid buffer\n     * @throws {$protobuf.util.ProtocolError} If required fields are missing\n     */\n    OpenApiData.decode = function decode(reader, length) {\n        if (!(reader instanceof $Reader))\n            reader = $Reader.create(reader);\n        var end = length === undefined ? reader.len : reader.pos + length, message = new $root.OpenApiData();\n        while (reader.pos < end) {\n            var tag = reader.uint32();\n            switch (tag >>> 3) {\n            case 1:\n                if (!(message.pairs && message.pairs.length))\n                    message.pairs = [];\n                message.pairs.push($root.KeyValuePair.decode(reader, reader.uint32()));\n                break;\n            default:\n                reader.skipType(tag & 7);\n                break;\n            }\n        }\n        return message;\n    };\n\n    /**\n     * Decodes an OpenApiData message from the specified reader or buffer, length delimited.\n     * @function decodeDelimited\n     * @memberof OpenApiData\n     * @static\n     * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from\n     * @returns {OpenApiData} OpenApiData\n     * @throws {Error} If the payload is not a reader or valid buffer\n     * @throws {$protobuf.util.ProtocolError} If required fields are missing\n     */\n    OpenApiData.decodeDelimited = function decodeDelimited(reader) {\n        if (!(reader instanceof $Reader))\n            reader = new $Reader(reader);\n        return this.decode(reader, reader.uint32());\n    };\n\n    /**\n     * Verifies an OpenApiData message.\n     * @function verify\n     * @memberof OpenApiData\n     * @static\n     * @param {Object.<string,*>} message Plain object to verify\n     * @returns {string|null} `null` if valid, otherwise the reason why it is not\n     */\n    OpenApiData.verify = function verify(message) {\n        if (typeof message !== \"object\" || message === null)\n            return \"object expected\";\n        if (message.pairs != null && message.hasOwnProperty(\"pairs\")) {\n            if (!Array.isArray(message.pairs))\n                return \"pairs: array expected\";\n            for (var i = 0; i < message.pairs.length; ++i) {\n                var error = $root.KeyValuePair.verify(message.pairs[i]);\n                if (error)\n                    return \"pairs.\" + error;\n            }\n        }\n        return null;\n    };\n\n    /**\n     * Creates an OpenApiData message from a plain object. Also converts values to their respective internal types.\n     * @function fromObject\n     * @memberof OpenApiData\n     * @static\n     * @param {Object.<string,*>} object Plain object\n     * @returns {OpenApiData} OpenApiData\n     */\n    OpenApiData.fromObject = function fromObject(object) {\n        if (object instanceof $root.OpenApiData)\n            return object;\n        var message = new $root.OpenApiData();\n        if (object.pairs) {\n            if (!Array.isArray(object.pairs))\n                throw TypeError(\".OpenApiData.pairs: array expected\");\n            message.pairs = [];\n            for (var i = 0; i < object.pairs.length; ++i) {\n                if (typeof object.pairs[i] !== \"object\")\n                    throw TypeError(\".OpenApiData.pairs: object expected\");\n                message.pairs[i] = $root.KeyValuePair.fromObject(object.pairs[i]);\n            }\n        }\n        return message;\n    };\n\n    /**\n     * Creates a plain object from an OpenApiData message. Also converts values to other types if specified.\n     * @function toObject\n     * @memberof OpenApiData\n     * @static\n     * @param {OpenApiData} message OpenApiData\n     * @param {$protobuf.IConversionOptions} [options] Conversion options\n     * @returns {Object.<string,*>} Plain object\n     */\n    OpenApiData.toObject = function toObject(message, options) {\n        if (!options)\n            options = {};\n        var object = {};\n        if (options.arrays || options.defaults)\n            object.pairs = [];\n        if (message.pairs && message.pairs.length) {\n            object.pairs = [];\n            for (var j = 0; j < message.pairs.length; ++j)\n                object.pairs[j] = $root.KeyValuePair.toObject(message.pairs[j], options);\n        }\n        return object;\n    };\n\n    /**\n     * Converts this OpenApiData to JSON.\n     * @function toJSON\n     * @memberof OpenApiData\n     * @instance\n     * @returns {Object.<string,*>} JSON object\n     */\n    OpenApiData.prototype.toJSON = function toJSON() {\n        return this.constructor.toObject(this, $protobuf.util.toJSONOptions);\n    };\n\n    return OpenApiData;\n})();\n\n$root.TokenApiData = (function() {\n\n    /**\n     * Properties of a TokenApiData.\n     * @exports ITokenApiData\n     * @interface ITokenApiData\n     * @property {string|null} [resourceAppid] TokenApiData resourceAppid\n     * @property {string|null} [resourceEnv] TokenApiData resourceEnv\n     */\n\n    /**\n     * Constructs a new TokenApiData.\n     * @exports TokenApiData\n     * @classdesc Represents a TokenApiData.\n     * @implements ITokenApiData\n     * @constructor\n     * @param {ITokenApiData=} [properties] Properties to set\n     */\n    function TokenApiData(properties) {\n        if (properties)\n            for (var keys = Object.keys(properties), i = 0; i < keys.length; ++i)\n                if (properties[keys[i]] != null)\n                    this[keys[i]] = properties[keys[i]];\n    }\n\n    /**\n     * TokenApiData resourceAppid.\n     * @member {string} resourceAppid\n     * @memberof TokenApiData\n     * @instance\n     */\n    TokenApiData.prototype.resourceAppid = \"\";\n\n    /**\n     * TokenApiData resourceEnv.\n     * @member {string} resourceEnv\n     * @memberof TokenApiData\n     * @instance\n     */\n    TokenApiData.prototype.resourceEnv = \"\";\n\n    /**\n     * Creates a new TokenApiData instance using the specified properties.\n     * @function create\n     * @memberof TokenApiData\n     * @static\n     * @param {ITokenApiData=} [properties] Properties to set\n     * @returns {TokenApiData} TokenApiData instance\n     */\n    TokenApiData.create = function create(properties) {\n        return new TokenApiData(properties);\n    };\n\n    /**\n     * Encodes the specified TokenApiData message. Does not implicitly {@link TokenApiData.verify|verify} messages.\n     * @function encode\n     * @memberof TokenApiData\n     * @static\n     * @param {ITokenApiData} message TokenApiData message or plain object to encode\n     * @param {$protobuf.Writer} [writer] Writer to encode to\n     * @returns {$protobuf.Writer} Writer\n     */\n    TokenApiData.encode = function encode(message, writer) {\n        if (!writer)\n            writer = $Writer.create();\n        if (message.resourceAppid != null && message.hasOwnProperty(\"resourceAppid\"))\n            writer.uint32(/* id 1, wireType 2 =*/10).string(message.resourceAppid);\n        if (message.resourceEnv != null && message.hasOwnProperty(\"resourceEnv\"))\n            writer.uint32(/* id 2, wireType 2 =*/18).string(message.resourceEnv);\n        return writer;\n    };\n\n    /**\n     * Encodes the specified TokenApiData message, length delimited. Does not implicitly {@link TokenApiData.verify|verify} messages.\n     * @function encodeDelimited\n     * @memberof TokenApiData\n     * @static\n     * @param {ITokenApiData} message TokenApiData message or plain object to encode\n     * @param {$protobuf.Writer} [writer] Writer to encode to\n     * @returns {$protobuf.Writer} Writer\n     */\n    TokenApiData.encodeDelimited = function encodeDelimited(message, writer) {\n        return this.encode(message, writer).ldelim();\n    };\n\n    /**\n     * Decodes a TokenApiData message from the specified reader or buffer.\n     * @function decode\n     * @memberof TokenApiData\n     * @static\n     * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from\n     * @param {number} [length] Message length if known beforehand\n     * @returns {TokenApiData} TokenApiData\n     * @throws {Error} If the payload is not a reader or valid buffer\n     * @throws {$protobuf.util.ProtocolError} If required fields are missing\n     */\n    TokenApiData.decode = function decode(reader, length) {\n        if (!(reader instanceof $Reader))\n            reader = $Reader.create(reader);\n        var end = length === undefined ? reader.len : reader.pos + length, message = new $root.TokenApiData();\n        while (reader.pos < end) {\n            var tag = reader.uint32();\n            switch (tag >>> 3) {\n            case 1:\n                message.resourceAppid = reader.string();\n                break;\n            case 2:\n                message.resourceEnv = reader.string();\n                break;\n            default:\n                reader.skipType(tag & 7);\n                break;\n            }\n        }\n        return message;\n    };\n\n    /**\n     * Decodes a TokenApiData message from the specified reader or buffer, length delimited.\n     * @function decodeDelimited\n     * @memberof TokenApiData\n     * @static\n     * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from\n     * @returns {TokenApiData} TokenApiData\n     * @throws {Error} If the payload is not a reader or valid buffer\n     * @throws {$protobuf.util.ProtocolError} If required fields are missing\n     */\n    TokenApiData.decodeDelimited = function decodeDelimited(reader) {\n        if (!(reader instanceof $Reader))\n            reader = new $Reader(reader);\n        return this.decode(reader, reader.uint32());\n    };\n\n    /**\n     * Verifies a TokenApiData message.\n     * @function verify\n     * @memberof TokenApiData\n     * @static\n     * @param {Object.<string,*>} message Plain object to verify\n     * @returns {string|null} `null` if valid, otherwise the reason why it is not\n     */\n    TokenApiData.verify = function verify(message) {\n        if (typeof message !== \"object\" || message === null)\n            return \"object expected\";\n        if (message.resourceAppid != null && message.hasOwnProperty(\"resourceAppid\"))\n            if (!$util.isString(message.resourceAppid))\n                return \"resourceAppid: string expected\";\n        if (message.resourceEnv != null && message.hasOwnProperty(\"resourceEnv\"))\n            if (!$util.isString(message.resourceEnv))\n                return \"resourceEnv: string expected\";\n        return null;\n    };\n\n    /**\n     * Creates a TokenApiData message from a plain object. Also converts values to their respective internal types.\n     * @function fromObject\n     * @memberof TokenApiData\n     * @static\n     * @param {Object.<string,*>} object Plain object\n     * @returns {TokenApiData} TokenApiData\n     */\n    TokenApiData.fromObject = function fromObject(object) {\n        if (object instanceof $root.TokenApiData)\n            return object;\n        var message = new $root.TokenApiData();\n        if (object.resourceAppid != null)\n            message.resourceAppid = String(object.resourceAppid);\n        if (object.resourceEnv != null)\n            message.resourceEnv = String(object.resourceEnv);\n        return message;\n    };\n\n    /**\n     * Creates a plain object from a TokenApiData message. Also converts values to other types if specified.\n     * @function toObject\n     * @memberof TokenApiData\n     * @static\n     * @param {TokenApiData} message TokenApiData\n     * @param {$protobuf.IConversionOptions} [options] Conversion options\n     * @returns {Object.<string,*>} Plain object\n     */\n    TokenApiData.toObject = function toObject(message, options) {\n        if (!options)\n            options = {};\n        var object = {};\n        if (options.defaults) {\n            object.resourceAppid = \"\";\n            object.resourceEnv = \"\";\n        }\n        if (message.resourceAppid != null && message.hasOwnProperty(\"resourceAppid\"))\n            object.resourceAppid = message.resourceAppid;\n        if (message.resourceEnv != null && message.hasOwnProperty(\"resourceEnv\"))\n            object.resourceEnv = message.resourceEnv;\n        return object;\n    };\n\n    /**\n     * Converts this TokenApiData to JSON.\n     * @function toJSON\n     * @memberof TokenApiData\n     * @instance\n     * @returns {Object.<string,*>} JSON object\n     */\n    TokenApiData.prototype.toJSON = function toJSON() {\n        return this.constructor.toObject(this, $protobuf.util.toJSONOptions);\n    };\n\n    return TokenApiData;\n})();\n\n$root.CommApiData = (function() {\n\n    /**\n     * Properties of a CommApiData.\n     * @exports ICommApiData\n     * @interface ICommApiData\n     * @property {CommApiData.ApiType|null} [apiType] CommApiData apiType\n     * @property {IOpenApiData|null} [openapiData] CommApiData openapiData\n     * @property {IInnerApiData|null} [innerData] CommApiData innerData\n     * @property {ISvrkitApiData|null} [svrkitData] CommApiData svrkitData\n     * @property {ITokenApiData|null} [tokenData] CommApiData tokenData\n     * @property {string|null} [appid] CommApiData appid\n     */\n\n    /**\n     * Constructs a new CommApiData.\n     * @exports CommApiData\n     * @classdesc Represents a CommApiData.\n     * @implements ICommApiData\n     * @constructor\n     * @param {ICommApiData=} [properties] Properties to set\n     */\n    function CommApiData(properties) {\n        if (properties)\n            for (var keys = Object.keys(properties), i = 0; i < keys.length; ++i)\n                if (properties[keys[i]] != null)\n                    this[keys[i]] = properties[keys[i]];\n    }\n\n    /**\n     * CommApiData apiType.\n     * @member {CommApiData.ApiType} apiType\n     * @memberof CommApiData\n     * @instance\n     */\n    CommApiData.prototype.apiType = 0;\n\n    /**\n     * CommApiData openapiData.\n     * @member {IOpenApiData|null|undefined} openapiData\n     * @memberof CommApiData\n     * @instance\n     */\n    CommApiData.prototype.openapiData = null;\n\n    /**\n     * CommApiData innerData.\n     * @member {IInnerApiData|null|undefined} innerData\n     * @memberof CommApiData\n     * @instance\n     */\n    CommApiData.prototype.innerData = null;\n\n    /**\n     * CommApiData svrkitData.\n     * @member {ISvrkitApiData|null|undefined} svrkitData\n     * @memberof CommApiData\n     * @instance\n     */\n    CommApiData.prototype.svrkitData = null;\n\n    /**\n     * CommApiData tokenData.\n     * @member {ITokenApiData|null|undefined} tokenData\n     * @memberof CommApiData\n     * @instance\n     */\n    CommApiData.prototype.tokenData = null;\n\n    /**\n     * CommApiData appid.\n     * @member {string} appid\n     * @memberof CommApiData\n     * @instance\n     */\n    CommApiData.prototype.appid = \"\";\n\n    /**\n     * Creates a new CommApiData instance using the specified properties.\n     * @function create\n     * @memberof CommApiData\n     * @static\n     * @param {ICommApiData=} [properties] Properties to set\n     * @returns {CommApiData} CommApiData instance\n     */\n    CommApiData.create = function create(properties) {\n        return new CommApiData(properties);\n    };\n\n    /**\n     * Encodes the specified CommApiData message. Does not implicitly {@link CommApiData.verify|verify} messages.\n     * @function encode\n     * @memberof CommApiData\n     * @static\n     * @param {ICommApiData} message CommApiData message or plain object to encode\n     * @param {$protobuf.Writer} [writer] Writer to encode to\n     * @returns {$protobuf.Writer} Writer\n     */\n    CommApiData.encode = function encode(message, writer) {\n        if (!writer)\n            writer = $Writer.create();\n        if (message.apiType != null && message.hasOwnProperty(\"apiType\"))\n            writer.uint32(/* id 1, wireType 0 =*/8).int32(message.apiType);\n        if (message.openapiData != null && message.hasOwnProperty(\"openapiData\"))\n            $root.OpenApiData.encode(message.openapiData, writer.uint32(/* id 2, wireType 2 =*/18).fork()).ldelim();\n        if (message.innerData != null && message.hasOwnProperty(\"innerData\"))\n            $root.InnerApiData.encode(message.innerData, writer.uint32(/* id 3, wireType 2 =*/26).fork()).ldelim();\n        if (message.svrkitData != null && message.hasOwnProperty(\"svrkitData\"))\n            $root.SvrkitApiData.encode(message.svrkitData, writer.uint32(/* id 4, wireType 2 =*/34).fork()).ldelim();\n        if (message.tokenData != null && message.hasOwnProperty(\"tokenData\"))\n            $root.TokenApiData.encode(message.tokenData, writer.uint32(/* id 5, wireType 2 =*/42).fork()).ldelim();\n        if (message.appid != null && message.hasOwnProperty(\"appid\"))\n            writer.uint32(/* id 6, wireType 2 =*/50).string(message.appid);\n        return writer;\n    };\n\n    /**\n     * Encodes the specified CommApiData message, length delimited. Does not implicitly {@link CommApiData.verify|verify} messages.\n     * @function encodeDelimited\n     * @memberof CommApiData\n     * @static\n     * @param {ICommApiData} message CommApiData message or plain object to encode\n     * @param {$protobuf.Writer} [writer] Writer to encode to\n     * @returns {$protobuf.Writer} Writer\n     */\n    CommApiData.encodeDelimited = function encodeDelimited(message, writer) {\n        return this.encode(message, writer).ldelim();\n    };\n\n    /**\n     * Decodes a CommApiData message from the specified reader or buffer.\n     * @function decode\n     * @memberof CommApiData\n     * @static\n     * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from\n     * @param {number} [length] Message length if known beforehand\n     * @returns {CommApiData} CommApiData\n     * @throws {Error} If the payload is not a reader or valid buffer\n     * @throws {$protobuf.util.ProtocolError} If required fields are missing\n     */\n    CommApiData.decode = function decode(reader, length) {\n        if (!(reader instanceof $Reader))\n            reader = $Reader.create(reader);\n        var end = length === undefined ? reader.len : reader.pos + length, message = new $root.CommApiData();\n        while (reader.pos < end) {\n            var tag = reader.uint32();\n            switch (tag >>> 3) {\n            case 1:\n                message.apiType = reader.int32();\n                break;\n            case 2:\n                message.openapiData = $root.OpenApiData.decode(reader, reader.uint32());\n                break;\n            case 3:\n                message.innerData = $root.InnerApiData.decode(reader, reader.uint32());\n                break;\n            case 4:\n                message.svrkitData = $root.SvrkitApiData.decode(reader, reader.uint32());\n                break;\n            case 5:\n                message.tokenData = $root.TokenApiData.decode(reader, reader.uint32());\n                break;\n            case 6:\n                message.appid = reader.string();\n                break;\n            default:\n                reader.skipType(tag & 7);\n                break;\n            }\n        }\n        return message;\n    };\n\n    /**\n     * Decodes a CommApiData message from the specified reader or buffer, length delimited.\n     * @function decodeDelimited\n     * @memberof CommApiData\n     * @static\n     * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from\n     * @returns {CommApiData} CommApiData\n     * @throws {Error} If the payload is not a reader or valid buffer\n     * @throws {$protobuf.util.ProtocolError} If required fields are missing\n     */\n    CommApiData.decodeDelimited = function decodeDelimited(reader) {\n        if (!(reader instanceof $Reader))\n            reader = new $Reader(reader);\n        return this.decode(reader, reader.uint32());\n    };\n\n    /**\n     * Verifies a CommApiData message.\n     * @function verify\n     * @memberof CommApiData\n     * @static\n     * @param {Object.<string,*>} message Plain object to verify\n     * @returns {string|null} `null` if valid, otherwise the reason why it is not\n     */\n    CommApiData.verify = function verify(message) {\n        if (typeof message !== \"object\" || message === null)\n            return \"object expected\";\n        if (message.apiType != null && message.hasOwnProperty(\"apiType\"))\n            switch (message.apiType) {\n            default:\n                return \"apiType: enum value expected\";\n            case 0:\n            case 1:\n            case 2:\n            case 3:\n                break;\n            }\n        if (message.openapiData != null && message.hasOwnProperty(\"openapiData\")) {\n            var error = $root.OpenApiData.verify(message.openapiData);\n            if (error)\n                return \"openapiData.\" + error;\n        }\n        if (message.innerData != null && message.hasOwnProperty(\"innerData\")) {\n            var error = $root.InnerApiData.verify(message.innerData);\n            if (error)\n                return \"innerData.\" + error;\n        }\n        if (message.svrkitData != null && message.hasOwnProperty(\"svrkitData\")) {\n            var error = $root.SvrkitApiData.verify(message.svrkitData);\n            if (error)\n                return \"svrkitData.\" + error;\n        }\n        if (message.tokenData != null && message.hasOwnProperty(\"tokenData\")) {\n            var error = $root.TokenApiData.verify(message.tokenData);\n            if (error)\n                return \"tokenData.\" + error;\n        }\n        if (message.appid != null && message.hasOwnProperty(\"appid\"))\n            if (!$util.isString(message.appid))\n                return \"appid: string expected\";\n        return null;\n    };\n\n    /**\n     * Creates a CommApiData message from a plain object. Also converts values to their respective internal types.\n     * @function fromObject\n     * @memberof CommApiData\n     * @static\n     * @param {Object.<string,*>} object Plain object\n     * @returns {CommApiData} CommApiData\n     */\n    CommApiData.fromObject = function fromObject(object) {\n        if (object instanceof $root.CommApiData)\n            return object;\n        var message = new $root.CommApiData();\n        switch (object.apiType) {\n        case \"OPEN_API\":\n        case 0:\n            message.apiType = 0;\n            break;\n        case \"INNER_API\":\n        case 1:\n            message.apiType = 1;\n            break;\n        case \"SVRKIT_API\":\n        case 2:\n            message.apiType = 2;\n            break;\n        case \"TOKEN_API\":\n        case 3:\n            message.apiType = 3;\n            break;\n        }\n        if (object.openapiData != null) {\n            if (typeof object.openapiData !== \"object\")\n                throw TypeError(\".CommApiData.openapiData: object expected\");\n            message.openapiData = $root.OpenApiData.fromObject(object.openapiData);\n        }\n        if (object.innerData != null) {\n            if (typeof object.innerData !== \"object\")\n                throw TypeError(\".CommApiData.innerData: object expected\");\n            message.innerData = $root.InnerApiData.fromObject(object.innerData);\n        }\n        if (object.svrkitData != null) {\n            if (typeof object.svrkitData !== \"object\")\n                throw TypeError(\".CommApiData.svrkitData: object expected\");\n            message.svrkitData = $root.SvrkitApiData.fromObject(object.svrkitData);\n        }\n        if (object.tokenData != null) {\n            if (typeof object.tokenData !== \"object\")\n                throw TypeError(\".CommApiData.tokenData: object expected\");\n            message.tokenData = $root.TokenApiData.fromObject(object.tokenData);\n        }\n        if (object.appid != null)\n            message.appid = String(object.appid);\n        return message;\n    };\n\n    /**\n     * Creates a plain object from a CommApiData message. Also converts values to other types if specified.\n     * @function toObject\n     * @memberof CommApiData\n     * @static\n     * @param {CommApiData} message CommApiData\n     * @param {$protobuf.IConversionOptions} [options] Conversion options\n     * @returns {Object.<string,*>} Plain object\n     */\n    CommApiData.toObject = function toObject(message, options) {\n        if (!options)\n            options = {};\n        var object = {};\n        if (options.defaults) {\n            object.apiType = options.enums === String ? \"OPEN_API\" : 0;\n            object.openapiData = null;\n            object.innerData = null;\n            object.svrkitData = null;\n            object.tokenData = null;\n            object.appid = \"\";\n        }\n        if (message.apiType != null && message.hasOwnProperty(\"apiType\"))\n            object.apiType = options.enums === String ? $root.CommApiData.ApiType[message.apiType] : message.apiType;\n        if (message.openapiData != null && message.hasOwnProperty(\"openapiData\"))\n            object.openapiData = $root.OpenApiData.toObject(message.openapiData, options);\n        if (message.innerData != null && message.hasOwnProperty(\"innerData\"))\n            object.innerData = $root.InnerApiData.toObject(message.innerData, options);\n        if (message.svrkitData != null && message.hasOwnProperty(\"svrkitData\"))\n            object.svrkitData = $root.SvrkitApiData.toObject(message.svrkitData, options);\n        if (message.tokenData != null && message.hasOwnProperty(\"tokenData\"))\n            object.tokenData = $root.TokenApiData.toObject(message.tokenData, options);\n        if (message.appid != null && message.hasOwnProperty(\"appid\"))\n            object.appid = message.appid;\n        return object;\n    };\n\n    /**\n     * Converts this CommApiData to JSON.\n     * @function toJSON\n     * @memberof CommApiData\n     * @instance\n     * @returns {Object.<string,*>} JSON object\n     */\n    CommApiData.prototype.toJSON = function toJSON() {\n        return this.constructor.toObject(this, $protobuf.util.toJSONOptions);\n    };\n\n    /**\n     * ApiType enum.\n     * @name CommApiData.ApiType\n     * @enum {string}\n     * @property {number} OPEN_API=0 OPEN_API value\n     * @property {number} INNER_API=1 INNER_API value\n     * @property {number} SVRKIT_API=2 SVRKIT_API value\n     * @property {number} TOKEN_API=3 TOKEN_API value\n     */\n    CommApiData.ApiType = (function() {\n        var valuesById = {}, values = Object.create(valuesById);\n        values[valuesById[0] = \"OPEN_API\"] = 0;\n        values[valuesById[1] = \"INNER_API\"] = 1;\n        values[valuesById[2] = \"SVRKIT_API\"] = 2;\n        values[valuesById[3] = \"TOKEN_API\"] = 3;\n        return values;\n    })();\n\n    return CommApiData;\n})();\n\n$root.CommOpenApiResp = (function() {\n\n    /**\n     * Properties of a CommOpenApiResp.\n     * @exports ICommOpenApiResp\n     * @interface ICommOpenApiResp\n     * @property {Uint8Array|null} [respData] CommOpenApiResp respData\n     * @property {string|null} [contentType] CommOpenApiResp contentType\n     * @property {number|null} [errorCode] CommOpenApiResp errorCode\n     * @property {number|null} [httpCode] CommOpenApiResp httpCode\n     * @property {Array.<IHttpHeader>|null} [headers] CommOpenApiResp headers\n     * @property {number|null} [svrkitErrorCode] CommOpenApiResp svrkitErrorCode\n     */\n\n    /**\n     * Constructs a new CommOpenApiResp.\n     * @exports CommOpenApiResp\n     * @classdesc Represents a CommOpenApiResp.\n     * @implements ICommOpenApiResp\n     * @constructor\n     * @param {ICommOpenApiResp=} [properties] Properties to set\n     */\n    function CommOpenApiResp(properties) {\n        this.headers = [];\n        if (properties)\n            for (var keys = Object.keys(properties), i = 0; i < keys.length; ++i)\n                if (properties[keys[i]] != null)\n                    this[keys[i]] = properties[keys[i]];\n    }\n\n    /**\n     * CommOpenApiResp respData.\n     * @member {Uint8Array} respData\n     * @memberof CommOpenApiResp\n     * @instance\n     */\n    CommOpenApiResp.prototype.respData = $util.newBuffer([]);\n\n    /**\n     * CommOpenApiResp contentType.\n     * @member {string} contentType\n     * @memberof CommOpenApiResp\n     * @instance\n     */\n    CommOpenApiResp.prototype.contentType = \"\";\n\n    /**\n     * CommOpenApiResp errorCode.\n     * @member {number} errorCode\n     * @memberof CommOpenApiResp\n     * @instance\n     */\n    CommOpenApiResp.prototype.errorCode = 0;\n\n    /**\n     * CommOpenApiResp httpCode.\n     * @member {number} httpCode\n     * @memberof CommOpenApiResp\n     * @instance\n     */\n    CommOpenApiResp.prototype.httpCode = 0;\n\n    /**\n     * CommOpenApiResp headers.\n     * @member {Array.<IHttpHeader>} headers\n     * @memberof CommOpenApiResp\n     * @instance\n     */\n    CommOpenApiResp.prototype.headers = $util.emptyArray;\n\n    /**\n     * CommOpenApiResp svrkitErrorCode.\n     * @member {number} svrkitErrorCode\n     * @memberof CommOpenApiResp\n     * @instance\n     */\n    CommOpenApiResp.prototype.svrkitErrorCode = 0;\n\n    /**\n     * Creates a new CommOpenApiResp instance using the specified properties.\n     * @function create\n     * @memberof CommOpenApiResp\n     * @static\n     * @param {ICommOpenApiResp=} [properties] Properties to set\n     * @returns {CommOpenApiResp} CommOpenApiResp instance\n     */\n    CommOpenApiResp.create = function create(properties) {\n        return new CommOpenApiResp(properties);\n    };\n\n    /**\n     * Encodes the specified CommOpenApiResp message. Does not implicitly {@link CommOpenApiResp.verify|verify} messages.\n     * @function encode\n     * @memberof CommOpenApiResp\n     * @static\n     * @param {ICommOpenApiResp} message CommOpenApiResp message or plain object to encode\n     * @param {$protobuf.Writer} [writer] Writer to encode to\n     * @returns {$protobuf.Writer} Writer\n     */\n    CommOpenApiResp.encode = function encode(message, writer) {\n        if (!writer)\n            writer = $Writer.create();\n        if (message.respData != null && message.hasOwnProperty(\"respData\"))\n            writer.uint32(/* id 1, wireType 2 =*/10).bytes(message.respData);\n        if (message.contentType != null && message.hasOwnProperty(\"contentType\"))\n            writer.uint32(/* id 2, wireType 2 =*/18).string(message.contentType);\n        if (message.errorCode != null && message.hasOwnProperty(\"errorCode\"))\n            writer.uint32(/* id 3, wireType 0 =*/24).int32(message.errorCode);\n        if (message.httpCode != null && message.hasOwnProperty(\"httpCode\"))\n            writer.uint32(/* id 4, wireType 0 =*/32).uint32(message.httpCode);\n        if (message.headers != null && message.headers.length)\n            for (var i = 0; i < message.headers.length; ++i)\n                $root.HttpHeader.encode(message.headers[i], writer.uint32(/* id 5, wireType 2 =*/42).fork()).ldelim();\n        if (message.svrkitErrorCode != null && message.hasOwnProperty(\"svrkitErrorCode\"))\n            writer.uint32(/* id 6, wireType 0 =*/48).int32(message.svrkitErrorCode);\n        return writer;\n    };\n\n    /**\n     * Encodes the specified CommOpenApiResp message, length delimited. Does not implicitly {@link CommOpenApiResp.verify|verify} messages.\n     * @function encodeDelimited\n     * @memberof CommOpenApiResp\n     * @static\n     * @param {ICommOpenApiResp} message CommOpenApiResp message or plain object to encode\n     * @param {$protobuf.Writer} [writer] Writer to encode to\n     * @returns {$protobuf.Writer} Writer\n     */\n    CommOpenApiResp.encodeDelimited = function encodeDelimited(message, writer) {\n        return this.encode(message, writer).ldelim();\n    };\n\n    /**\n     * Decodes a CommOpenApiResp message from the specified reader or buffer.\n     * @function decode\n     * @memberof CommOpenApiResp\n     * @static\n     * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from\n     * @param {number} [length] Message length if known beforehand\n     * @returns {CommOpenApiResp} CommOpenApiResp\n     * @throws {Error} If the payload is not a reader or valid buffer\n     * @throws {$protobuf.util.ProtocolError} If required fields are missing\n     */\n    CommOpenApiResp.decode = function decode(reader, length) {\n        if (!(reader instanceof $Reader))\n            reader = $Reader.create(reader);\n        var end = length === undefined ? reader.len : reader.pos + length, message = new $root.CommOpenApiResp();\n        while (reader.pos < end) {\n            var tag = reader.uint32();\n            switch (tag >>> 3) {\n            case 1:\n                message.respData = reader.bytes();\n                break;\n            case 2:\n                message.contentType = reader.string();\n                break;\n            case 3:\n                message.errorCode = reader.int32();\n                break;\n            case 4:\n                message.httpCode = reader.uint32();\n                break;\n            case 5:\n                if (!(message.headers && message.headers.length))\n                    message.headers = [];\n                message.headers.push($root.HttpHeader.decode(reader, reader.uint32()));\n                break;\n            case 6:\n                message.svrkitErrorCode = reader.int32();\n                break;\n            default:\n                reader.skipType(tag & 7);\n                break;\n            }\n        }\n        return message;\n    };\n\n    /**\n     * Decodes a CommOpenApiResp message from the specified reader or buffer, length delimited.\n     * @function decodeDelimited\n     * @memberof CommOpenApiResp\n     * @static\n     * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from\n     * @returns {CommOpenApiResp} CommOpenApiResp\n     * @throws {Error} If the payload is not a reader or valid buffer\n     * @throws {$protobuf.util.ProtocolError} If required fields are missing\n     */\n    CommOpenApiResp.decodeDelimited = function decodeDelimited(reader) {\n        if (!(reader instanceof $Reader))\n            reader = new $Reader(reader);\n        return this.decode(reader, reader.uint32());\n    };\n\n    /**\n     * Verifies a CommOpenApiResp message.\n     * @function verify\n     * @memberof CommOpenApiResp\n     * @static\n     * @param {Object.<string,*>} message Plain object to verify\n     * @returns {string|null} `null` if valid, otherwise the reason why it is not\n     */\n    CommOpenApiResp.verify = function verify(message) {\n        if (typeof message !== \"object\" || message === null)\n            return \"object expected\";\n        if (message.respData != null && message.hasOwnProperty(\"respData\"))\n            if (!(message.respData && typeof message.respData.length === \"number\" || $util.isString(message.respData)))\n                return \"respData: buffer expected\";\n        if (message.contentType != null && message.hasOwnProperty(\"contentType\"))\n            if (!$util.isString(message.contentType))\n                return \"contentType: string expected\";\n        if (message.errorCode != null && message.hasOwnProperty(\"errorCode\"))\n            if (!$util.isInteger(message.errorCode))\n                return \"errorCode: integer expected\";\n        if (message.httpCode != null && message.hasOwnProperty(\"httpCode\"))\n            if (!$util.isInteger(message.httpCode))\n                return \"httpCode: integer expected\";\n        if (message.headers != null && message.hasOwnProperty(\"headers\")) {\n            if (!Array.isArray(message.headers))\n                return \"headers: array expected\";\n            for (var i = 0; i < message.headers.length; ++i) {\n                var error = $root.HttpHeader.verify(message.headers[i]);\n                if (error)\n                    return \"headers.\" + error;\n            }\n        }\n        if (message.svrkitErrorCode != null && message.hasOwnProperty(\"svrkitErrorCode\"))\n            if (!$util.isInteger(message.svrkitErrorCode))\n                return \"svrkitErrorCode: integer expected\";\n        return null;\n    };\n\n    /**\n     * Creates a CommOpenApiResp message from a plain object. Also converts values to their respective internal types.\n     * @function fromObject\n     * @memberof CommOpenApiResp\n     * @static\n     * @param {Object.<string,*>} object Plain object\n     * @returns {CommOpenApiResp} CommOpenApiResp\n     */\n    CommOpenApiResp.fromObject = function fromObject(object) {\n        if (object instanceof $root.CommOpenApiResp)\n            return object;\n        var message = new $root.CommOpenApiResp();\n        if (object.respData != null)\n            if (typeof object.respData === \"string\")\n                $util.base64.decode(object.respData, message.respData = $util.newBuffer($util.base64.length(object.respData)), 0);\n            else if (object.respData.length)\n                message.respData = object.respData;\n        if (object.contentType != null)\n            message.contentType = String(object.contentType);\n        if (object.errorCode != null)\n            message.errorCode = object.errorCode | 0;\n        if (object.httpCode != null)\n            message.httpCode = object.httpCode >>> 0;\n        if (object.headers) {\n            if (!Array.isArray(object.headers))\n                throw TypeError(\".CommOpenApiResp.headers: array expected\");\n            message.headers = [];\n            for (var i = 0; i < object.headers.length; ++i) {\n                if (typeof object.headers[i] !== \"object\")\n                    throw TypeError(\".CommOpenApiResp.headers: object expected\");\n                message.headers[i] = $root.HttpHeader.fromObject(object.headers[i]);\n            }\n        }\n        if (object.svrkitErrorCode != null)\n            message.svrkitErrorCode = object.svrkitErrorCode | 0;\n        return message;\n    };\n\n    /**\n     * Creates a plain object from a CommOpenApiResp message. Also converts values to other types if specified.\n     * @function toObject\n     * @memberof CommOpenApiResp\n     * @static\n     * @param {CommOpenApiResp} message CommOpenApiResp\n     * @param {$protobuf.IConversionOptions} [options] Conversion options\n     * @returns {Object.<string,*>} Plain object\n     */\n    CommOpenApiResp.toObject = function toObject(message, options) {\n        if (!options)\n            options = {};\n        var object = {};\n        if (options.arrays || options.defaults)\n            object.headers = [];\n        if (options.defaults) {\n            if (options.bytes === String)\n                object.respData = \"\";\n            else {\n                object.respData = [];\n                if (options.bytes !== Array)\n                    object.respData = $util.newBuffer(object.respData);\n            }\n            object.contentType = \"\";\n            object.errorCode = 0;\n            object.httpCode = 0;\n            object.svrkitErrorCode = 0;\n        }\n        if (message.respData != null && message.hasOwnProperty(\"respData\"))\n            object.respData = options.bytes === String ? $util.base64.encode(message.respData, 0, message.respData.length) : options.bytes === Array ? Array.prototype.slice.call(message.respData) : message.respData;\n        if (message.contentType != null && message.hasOwnProperty(\"contentType\"))\n            object.contentType = message.contentType;\n        if (message.errorCode != null && message.hasOwnProperty(\"errorCode\"))\n            object.errorCode = message.errorCode;\n        if (message.httpCode != null && message.hasOwnProperty(\"httpCode\"))\n            object.httpCode = message.httpCode;\n        if (message.headers && message.headers.length) {\n            object.headers = [];\n            for (var j = 0; j < message.headers.length; ++j)\n                object.headers[j] = $root.HttpHeader.toObject(message.headers[j], options);\n        }\n        if (message.svrkitErrorCode != null && message.hasOwnProperty(\"svrkitErrorCode\"))\n            object.svrkitErrorCode = message.svrkitErrorCode;\n        return object;\n    };\n\n    /**\n     * Converts this CommOpenApiResp to JSON.\n     * @function toJSON\n     * @memberof CommOpenApiResp\n     * @instance\n     * @returns {Object.<string,*>} JSON object\n     */\n    CommOpenApiResp.prototype.toJSON = function toJSON() {\n        return this.constructor.toObject(this, $protobuf.util.toJSONOptions);\n    };\n\n    return CommOpenApiResp;\n})();\n\n$root.InnerApiData = (function() {\n\n    /**\n     * Properties of an InnerApiData.\n     * @exports IInnerApiData\n     * @interface IInnerApiData\n     * @property {number|null} [modid] InnerApiData modid\n     * @property {number|null} [cmdid] InnerApiData cmdid\n     * @property {string|null} [url] InnerApiData url\n     * @property {boolean|null} [useHttps] InnerApiData useHttps\n     * @property {HTTP_METHODS|null} [method] InnerApiData method\n     * @property {Array.<string>|null} [headers] InnerApiData headers\n     * @property {Uint8Array|null} [body] InnerApiData body\n     */\n\n    /**\n     * Constructs a new InnerApiData.\n     * @exports InnerApiData\n     * @classdesc Represents an InnerApiData.\n     * @implements IInnerApiData\n     * @constructor\n     * @param {IInnerApiData=} [properties] Properties to set\n     */\n    function InnerApiData(properties) {\n        this.headers = [];\n        if (properties)\n            for (var keys = Object.keys(properties), i = 0; i < keys.length; ++i)\n                if (properties[keys[i]] != null)\n                    this[keys[i]] = properties[keys[i]];\n    }\n\n    /**\n     * InnerApiData modid.\n     * @member {number} modid\n     * @memberof InnerApiData\n     * @instance\n     */\n    InnerApiData.prototype.modid = 0;\n\n    /**\n     * InnerApiData cmdid.\n     * @member {number} cmdid\n     * @memberof InnerApiData\n     * @instance\n     */\n    InnerApiData.prototype.cmdid = 0;\n\n    /**\n     * InnerApiData url.\n     * @member {string} url\n     * @memberof InnerApiData\n     * @instance\n     */\n    InnerApiData.prototype.url = \"\";\n\n    /**\n     * InnerApiData useHttps.\n     * @member {boolean} useHttps\n     * @memberof InnerApiData\n     * @instance\n     */\n    InnerApiData.prototype.useHttps = false;\n\n    /**\n     * InnerApiData method.\n     * @member {HTTP_METHODS} method\n     * @memberof InnerApiData\n     * @instance\n     */\n    InnerApiData.prototype.method = 1;\n\n    /**\n     * InnerApiData headers.\n     * @member {Array.<string>} headers\n     * @memberof InnerApiData\n     * @instance\n     */\n    InnerApiData.prototype.headers = $util.emptyArray;\n\n    /**\n     * InnerApiData body.\n     * @member {Uint8Array} body\n     * @memberof InnerApiData\n     * @instance\n     */\n    InnerApiData.prototype.body = $util.newBuffer([]);\n\n    /**\n     * Creates a new InnerApiData instance using the specified properties.\n     * @function create\n     * @memberof InnerApiData\n     * @static\n     * @param {IInnerApiData=} [properties] Properties to set\n     * @returns {InnerApiData} InnerApiData instance\n     */\n    InnerApiData.create = function create(properties) {\n        return new InnerApiData(properties);\n    };\n\n    /**\n     * Encodes the specified InnerApiData message. Does not implicitly {@link InnerApiData.verify|verify} messages.\n     * @function encode\n     * @memberof InnerApiData\n     * @static\n     * @param {IInnerApiData} message InnerApiData message or plain object to encode\n     * @param {$protobuf.Writer} [writer] Writer to encode to\n     * @returns {$protobuf.Writer} Writer\n     */\n    InnerApiData.encode = function encode(message, writer) {\n        if (!writer)\n            writer = $Writer.create();\n        if (message.modid != null && message.hasOwnProperty(\"modid\"))\n            writer.uint32(/* id 1, wireType 0 =*/8).uint32(message.modid);\n        if (message.cmdid != null && message.hasOwnProperty(\"cmdid\"))\n            writer.uint32(/* id 2, wireType 0 =*/16).uint32(message.cmdid);\n        if (message.url != null && message.hasOwnProperty(\"url\"))\n            writer.uint32(/* id 3, wireType 2 =*/26).string(message.url);\n        if (message.useHttps != null && message.hasOwnProperty(\"useHttps\"))\n            writer.uint32(/* id 4, wireType 0 =*/32).bool(message.useHttps);\n        if (message.method != null && message.hasOwnProperty(\"method\"))\n            writer.uint32(/* id 5, wireType 0 =*/40).int32(message.method);\n        if (message.headers != null && message.headers.length)\n            for (var i = 0; i < message.headers.length; ++i)\n                writer.uint32(/* id 6, wireType 2 =*/50).string(message.headers[i]);\n        if (message.body != null && message.hasOwnProperty(\"body\"))\n            writer.uint32(/* id 7, wireType 2 =*/58).bytes(message.body);\n        return writer;\n    };\n\n    /**\n     * Encodes the specified InnerApiData message, length delimited. Does not implicitly {@link InnerApiData.verify|verify} messages.\n     * @function encodeDelimited\n     * @memberof InnerApiData\n     * @static\n     * @param {IInnerApiData} message InnerApiData message or plain object to encode\n     * @param {$protobuf.Writer} [writer] Writer to encode to\n     * @returns {$protobuf.Writer} Writer\n     */\n    InnerApiData.encodeDelimited = function encodeDelimited(message, writer) {\n        return this.encode(message, writer).ldelim();\n    };\n\n    /**\n     * Decodes an InnerApiData message from the specified reader or buffer.\n     * @function decode\n     * @memberof InnerApiData\n     * @static\n     * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from\n     * @param {number} [length] Message length if known beforehand\n     * @returns {InnerApiData} InnerApiData\n     * @throws {Error} If the payload is not a reader or valid buffer\n     * @throws {$protobuf.util.ProtocolError} If required fields are missing\n     */\n    InnerApiData.decode = function decode(reader, length) {\n        if (!(reader instanceof $Reader))\n            reader = $Reader.create(reader);\n        var end = length === undefined ? reader.len : reader.pos + length, message = new $root.InnerApiData();\n        while (reader.pos < end) {\n            var tag = reader.uint32();\n            switch (tag >>> 3) {\n            case 1:\n                message.modid = reader.uint32();\n                break;\n            case 2:\n                message.cmdid = reader.uint32();\n                break;\n            case 3:\n                message.url = reader.string();\n                break;\n            case 4:\n                message.useHttps = reader.bool();\n                break;\n            case 5:\n                message.method = reader.int32();\n                break;\n            case 6:\n                if (!(message.headers && message.headers.length))\n                    message.headers = [];\n                message.headers.push(reader.string());\n                break;\n            case 7:\n                message.body = reader.bytes();\n                break;\n            default:\n                reader.skipType(tag & 7);\n                break;\n            }\n        }\n        return message;\n    };\n\n    /**\n     * Decodes an InnerApiData message from the specified reader or buffer, length delimited.\n     * @function decodeDelimited\n     * @memberof InnerApiData\n     * @static\n     * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from\n     * @returns {InnerApiData} InnerApiData\n     * @throws {Error} If the payload is not a reader or valid buffer\n     * @throws {$protobuf.util.ProtocolError} If required fields are missing\n     */\n    InnerApiData.decodeDelimited = function decodeDelimited(reader) {\n        if (!(reader instanceof $Reader))\n            reader = new $Reader(reader);\n        return this.decode(reader, reader.uint32());\n    };\n\n    /**\n     * Verifies an InnerApiData message.\n     * @function verify\n     * @memberof InnerApiData\n     * @static\n     * @param {Object.<string,*>} message Plain object to verify\n     * @returns {string|null} `null` if valid, otherwise the reason why it is not\n     */\n    InnerApiData.verify = function verify(message) {\n        if (typeof message !== \"object\" || message === null)\n            return \"object expected\";\n        if (message.modid != null && message.hasOwnProperty(\"modid\"))\n            if (!$util.isInteger(message.modid))\n                return \"modid: integer expected\";\n        if (message.cmdid != null && message.hasOwnProperty(\"cmdid\"))\n            if (!$util.isInteger(message.cmdid))\n                return \"cmdid: integer expected\";\n        if (message.url != null && message.hasOwnProperty(\"url\"))\n            if (!$util.isString(message.url))\n                return \"url: string expected\";\n        if (message.useHttps != null && message.hasOwnProperty(\"useHttps\"))\n            if (typeof message.useHttps !== \"boolean\")\n                return \"useHttps: boolean expected\";\n        if (message.method != null && message.hasOwnProperty(\"method\"))\n            switch (message.method) {\n            default:\n                return \"method: enum value expected\";\n            case 1:\n            case 2:\n            case 3:\n            case 4:\n            case 5:\n            case 6:\n                break;\n            }\n        if (message.headers != null && message.hasOwnProperty(\"headers\")) {\n            if (!Array.isArray(message.headers))\n                return \"headers: array expected\";\n            for (var i = 0; i < message.headers.length; ++i)\n                if (!$util.isString(message.headers[i]))\n                    return \"headers: string[] expected\";\n        }\n        if (message.body != null && message.hasOwnProperty(\"body\"))\n            if (!(message.body && typeof message.body.length === \"number\" || $util.isString(message.body)))\n                return \"body: buffer expected\";\n        return null;\n    };\n\n    /**\n     * Creates an InnerApiData message from a plain object. Also converts values to their respective internal types.\n     * @function fromObject\n     * @memberof InnerApiData\n     * @static\n     * @param {Object.<string,*>} object Plain object\n     * @returns {InnerApiData} InnerApiData\n     */\n    InnerApiData.fromObject = function fromObject(object) {\n        if (object instanceof $root.InnerApiData)\n            return object;\n        var message = new $root.InnerApiData();\n        if (object.modid != null)\n            message.modid = object.modid >>> 0;\n        if (object.cmdid != null)\n            message.cmdid = object.cmdid >>> 0;\n        if (object.url != null)\n            message.url = String(object.url);\n        if (object.useHttps != null)\n            message.useHttps = Boolean(object.useHttps);\n        switch (object.method) {\n        case \"HTTP_GET\":\n        case 1:\n            message.method = 1;\n            break;\n        case \"HTTP_POST\":\n        case 2:\n            message.method = 2;\n            break;\n        case \"HTTP_PUT\":\n        case 3:\n            message.method = 3;\n            break;\n        case \"HTTP_DELETE\":\n        case 4:\n            message.method = 4;\n            break;\n        case \"HTTP_HEAD\":\n        case 5:\n            message.method = 5;\n            break;\n        case \"HTTP_PATCH\":\n        case 6:\n            message.method = 6;\n            break;\n        }\n        if (object.headers) {\n            if (!Array.isArray(object.headers))\n                throw TypeError(\".InnerApiData.headers: array expected\");\n            message.headers = [];\n            for (var i = 0; i < object.headers.length; ++i)\n                message.headers[i] = String(object.headers[i]);\n        }\n        if (object.body != null)\n            if (typeof object.body === \"string\")\n                $util.base64.decode(object.body, message.body = $util.newBuffer($util.base64.length(object.body)), 0);\n            else if (object.body.length)\n                message.body = object.body;\n        return message;\n    };\n\n    /**\n     * Creates a plain object from an InnerApiData message. Also converts values to other types if specified.\n     * @function toObject\n     * @memberof InnerApiData\n     * @static\n     * @param {InnerApiData} message InnerApiData\n     * @param {$protobuf.IConversionOptions} [options] Conversion options\n     * @returns {Object.<string,*>} Plain object\n     */\n    InnerApiData.toObject = function toObject(message, options) {\n        if (!options)\n            options = {};\n        var object = {};\n        if (options.arrays || options.defaults)\n            object.headers = [];\n        if (options.defaults) {\n            object.modid = 0;\n            object.cmdid = 0;\n            object.url = \"\";\n            object.useHttps = false;\n            object.method = options.enums === String ? \"HTTP_GET\" : 1;\n            if (options.bytes === String)\n                object.body = \"\";\n            else {\n                object.body = [];\n                if (options.bytes !== Array)\n                    object.body = $util.newBuffer(object.body);\n            }\n        }\n        if (message.modid != null && message.hasOwnProperty(\"modid\"))\n            object.modid = message.modid;\n        if (message.cmdid != null && message.hasOwnProperty(\"cmdid\"))\n            object.cmdid = message.cmdid;\n        if (message.url != null && message.hasOwnProperty(\"url\"))\n            object.url = message.url;\n        if (message.useHttps != null && message.hasOwnProperty(\"useHttps\"))\n            object.useHttps = message.useHttps;\n        if (message.method != null && message.hasOwnProperty(\"method\"))\n            object.method = options.enums === String ? $root.HTTP_METHODS[message.method] : message.method;\n        if (message.headers && message.headers.length) {\n            object.headers = [];\n            for (var j = 0; j < message.headers.length; ++j)\n                object.headers[j] = message.headers[j];\n        }\n        if (message.body != null && message.hasOwnProperty(\"body\"))\n            object.body = options.bytes === String ? $util.base64.encode(message.body, 0, message.body.length) : options.bytes === Array ? Array.prototype.slice.call(message.body) : message.body;\n        return object;\n    };\n\n    /**\n     * Converts this InnerApiData to JSON.\n     * @function toJSON\n     * @memberof InnerApiData\n     * @instance\n     * @returns {Object.<string,*>} JSON object\n     */\n    InnerApiData.prototype.toJSON = function toJSON() {\n        return this.constructor.toObject(this, $protobuf.util.toJSONOptions);\n    };\n\n    return InnerApiData;\n})();\n\n$root.SvrkitApiData = (function() {\n\n    /**\n     * Properties of a SvrkitApiData.\n     * @exports ISvrkitApiData\n     * @interface ISvrkitApiData\n     * @property {string|null} [apiName] SvrkitApiData apiName\n     * @property {Uint8Array|null} [reqData] SvrkitApiData reqData\n     */\n\n    /**\n     * Constructs a new SvrkitApiData.\n     * @exports SvrkitApiData\n     * @classdesc Represents a SvrkitApiData.\n     * @implements ISvrkitApiData\n     * @constructor\n     * @param {ISvrkitApiData=} [properties] Properties to set\n     */\n    function SvrkitApiData(properties) {\n        if (properties)\n            for (var keys = Object.keys(properties), i = 0; i < keys.length; ++i)\n                if (properties[keys[i]] != null)\n                    this[keys[i]] = properties[keys[i]];\n    }\n\n    /**\n     * SvrkitApiData apiName.\n     * @member {string} apiName\n     * @memberof SvrkitApiData\n     * @instance\n     */\n    SvrkitApiData.prototype.apiName = \"\";\n\n    /**\n     * SvrkitApiData reqData.\n     * @member {Uint8Array} reqData\n     * @memberof SvrkitApiData\n     * @instance\n     */\n    SvrkitApiData.prototype.reqData = $util.newBuffer([]);\n\n    /**\n     * Creates a new SvrkitApiData instance using the specified properties.\n     * @function create\n     * @memberof SvrkitApiData\n     * @static\n     * @param {ISvrkitApiData=} [properties] Properties to set\n     * @returns {SvrkitApiData} SvrkitApiData instance\n     */\n    SvrkitApiData.create = function create(properties) {\n        return new SvrkitApiData(properties);\n    };\n\n    /**\n     * Encodes the specified SvrkitApiData message. Does not implicitly {@link SvrkitApiData.verify|verify} messages.\n     * @function encode\n     * @memberof SvrkitApiData\n     * @static\n     * @param {ISvrkitApiData} message SvrkitApiData message or plain object to encode\n     * @param {$protobuf.Writer} [writer] Writer to encode to\n     * @returns {$protobuf.Writer} Writer\n     */\n    SvrkitApiData.encode = function encode(message, writer) {\n        if (!writer)\n            writer = $Writer.create();\n        if (message.apiName != null && message.hasOwnProperty(\"apiName\"))\n            writer.uint32(/* id 1, wireType 2 =*/10).string(message.apiName);\n        if (message.reqData != null && message.hasOwnProperty(\"reqData\"))\n            writer.uint32(/* id 2, wireType 2 =*/18).bytes(message.reqData);\n        return writer;\n    };\n\n    /**\n     * Encodes the specified SvrkitApiData message, length delimited. Does not implicitly {@link SvrkitApiData.verify|verify} messages.\n     * @function encodeDelimited\n     * @memberof SvrkitApiData\n     * @static\n     * @param {ISvrkitApiData} message SvrkitApiData message or plain object to encode\n     * @param {$protobuf.Writer} [writer] Writer to encode to\n     * @returns {$protobuf.Writer} Writer\n     */\n    SvrkitApiData.encodeDelimited = function encodeDelimited(message, writer) {\n        return this.encode(message, writer).ldelim();\n    };\n\n    /**\n     * Decodes a SvrkitApiData message from the specified reader or buffer.\n     * @function decode\n     * @memberof SvrkitApiData\n     * @static\n     * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from\n     * @param {number} [length] Message length if known beforehand\n     * @returns {SvrkitApiData} SvrkitApiData\n     * @throws {Error} If the payload is not a reader or valid buffer\n     * @throws {$protobuf.util.ProtocolError} If required fields are missing\n     */\n    SvrkitApiData.decode = function decode(reader, length) {\n        if (!(reader instanceof $Reader))\n            reader = $Reader.create(reader);\n        var end = length === undefined ? reader.len : reader.pos + length, message = new $root.SvrkitApiData();\n        while (reader.pos < end) {\n            var tag = reader.uint32();\n            switch (tag >>> 3) {\n            case 1:\n                message.apiName = reader.string();\n                break;\n            case 2:\n                message.reqData = reader.bytes();\n                break;\n            default:\n                reader.skipType(tag & 7);\n                break;\n            }\n        }\n        return message;\n    };\n\n    /**\n     * Decodes a SvrkitApiData message from the specified reader or buffer, length delimited.\n     * @function decodeDelimited\n     * @memberof SvrkitApiData\n     * @static\n     * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from\n     * @returns {SvrkitApiData} SvrkitApiData\n     * @throws {Error} If the payload is not a reader or valid buffer\n     * @throws {$protobuf.util.ProtocolError} If required fields are missing\n     */\n    SvrkitApiData.decodeDelimited = function decodeDelimited(reader) {\n        if (!(reader instanceof $Reader))\n            reader = new $Reader(reader);\n        return this.decode(reader, reader.uint32());\n    };\n\n    /**\n     * Verifies a SvrkitApiData message.\n     * @function verify\n     * @memberof SvrkitApiData\n     * @static\n     * @param {Object.<string,*>} message Plain object to verify\n     * @returns {string|null} `null` if valid, otherwise the reason why it is not\n     */\n    SvrkitApiData.verify = function verify(message) {\n        if (typeof message !== \"object\" || message === null)\n            return \"object expected\";\n        if (message.apiName != null && message.hasOwnProperty(\"apiName\"))\n            if (!$util.isString(message.apiName))\n                return \"apiName: string expected\";\n        if (message.reqData != null && message.hasOwnProperty(\"reqData\"))\n            if (!(message.reqData && typeof message.reqData.length === \"number\" || $util.isString(message.reqData)))\n                return \"reqData: buffer expected\";\n        return null;\n    };\n\n    /**\n     * Creates a SvrkitApiData message from a plain object. Also converts values to their respective internal types.\n     * @function fromObject\n     * @memberof SvrkitApiData\n     * @static\n     * @param {Object.<string,*>} object Plain object\n     * @returns {SvrkitApiData} SvrkitApiData\n     */\n    SvrkitApiData.fromObject = function fromObject(object) {\n        if (object instanceof $root.SvrkitApiData)\n            return object;\n        var message = new $root.SvrkitApiData();\n        if (object.apiName != null)\n            message.apiName = String(object.apiName);\n        if (object.reqData != null)\n            if (typeof object.reqData === \"string\")\n                $util.base64.decode(object.reqData, message.reqData = $util.newBuffer($util.base64.length(object.reqData)), 0);\n            else if (object.reqData.length)\n                message.reqData = object.reqData;\n        return message;\n    };\n\n    /**\n     * Creates a plain object from a SvrkitApiData message. Also converts values to other types if specified.\n     * @function toObject\n     * @memberof SvrkitApiData\n     * @static\n     * @param {SvrkitApiData} message SvrkitApiData\n     * @param {$protobuf.IConversionOptions} [options] Conversion options\n     * @returns {Object.<string,*>} Plain object\n     */\n    SvrkitApiData.toObject = function toObject(message, options) {\n        if (!options)\n            options = {};\n        var object = {};\n        if (options.defaults) {\n            object.apiName = \"\";\n            if (options.bytes === String)\n                object.reqData = \"\";\n            else {\n                object.reqData = [];\n                if (options.bytes !== Array)\n                    object.reqData = $util.newBuffer(object.reqData);\n            }\n        }\n        if (message.apiName != null && message.hasOwnProperty(\"apiName\"))\n            object.apiName = message.apiName;\n        if (message.reqData != null && message.hasOwnProperty(\"reqData\"))\n            object.reqData = options.bytes === String ? $util.base64.encode(message.reqData, 0, message.reqData.length) : options.bytes === Array ? Array.prototype.slice.call(message.reqData) : message.reqData;\n        return object;\n    };\n\n    /**\n     * Converts this SvrkitApiData to JSON.\n     * @function toJSON\n     * @memberof SvrkitApiData\n     * @instance\n     * @returns {Object.<string,*>} JSON object\n     */\n    SvrkitApiData.prototype.toJSON = function toJSON() {\n        return this.constructor.toObject(this, $protobuf.util.toJSONOptions);\n    };\n\n    return SvrkitApiData;\n})();\n\n/**\n * HTTP_METHODS enum.\n * @exports HTTP_METHODS\n * @enum {string}\n * @property {number} HTTP_GET=1 HTTP_GET value\n * @property {number} HTTP_POST=2 HTTP_POST value\n * @property {number} HTTP_PUT=3 HTTP_PUT value\n * @property {number} HTTP_DELETE=4 HTTP_DELETE value\n * @property {number} HTTP_HEAD=5 HTTP_HEAD value\n * @property {number} HTTP_PATCH=6 HTTP_PATCH value\n */\n$root.HTTP_METHODS = (function() {\n    var valuesById = {}, values = Object.create(valuesById);\n    values[valuesById[1] = \"HTTP_GET\"] = 1;\n    values[valuesById[2] = \"HTTP_POST\"] = 2;\n    values[valuesById[3] = \"HTTP_PUT\"] = 3;\n    values[valuesById[4] = \"HTTP_DELETE\"] = 4;\n    values[valuesById[5] = \"HTTP_HEAD\"] = 5;\n    values[valuesById[6] = \"HTTP_PATCH\"] = 6;\n    return values;\n})();\n\n$root.HttpHeader = (function() {\n\n    /**\n     * Properties of a HttpHeader.\n     * @exports IHttpHeader\n     * @interface IHttpHeader\n     * @property {string|null} [key] HttpHeader key\n     * @property {string|null} [value] HttpHeader value\n     */\n\n    /**\n     * Constructs a new HttpHeader.\n     * @exports HttpHeader\n     * @classdesc Represents a HttpHeader.\n     * @implements IHttpHeader\n     * @constructor\n     * @param {IHttpHeader=} [properties] Properties to set\n     */\n    function HttpHeader(properties) {\n        if (properties)\n            for (var keys = Object.keys(properties), i = 0; i < keys.length; ++i)\n                if (properties[keys[i]] != null)\n                    this[keys[i]] = properties[keys[i]];\n    }\n\n    /**\n     * HttpHeader key.\n     * @member {string} key\n     * @memberof HttpHeader\n     * @instance\n     */\n    HttpHeader.prototype.key = \"\";\n\n    /**\n     * HttpHeader value.\n     * @member {string} value\n     * @memberof HttpHeader\n     * @instance\n     */\n    HttpHeader.prototype.value = \"\";\n\n    /**\n     * Creates a new HttpHeader instance using the specified properties.\n     * @function create\n     * @memberof HttpHeader\n     * @static\n     * @param {IHttpHeader=} [properties] Properties to set\n     * @returns {HttpHeader} HttpHeader instance\n     */\n    HttpHeader.create = function create(properties) {\n        return new HttpHeader(properties);\n    };\n\n    /**\n     * Encodes the specified HttpHeader message. Does not implicitly {@link HttpHeader.verify|verify} messages.\n     * @function encode\n     * @memberof HttpHeader\n     * @static\n     * @param {IHttpHeader} message HttpHeader message or plain object to encode\n     * @param {$protobuf.Writer} [writer] Writer to encode to\n     * @returns {$protobuf.Writer} Writer\n     */\n    HttpHeader.encode = function encode(message, writer) {\n        if (!writer)\n            writer = $Writer.create();\n        if (message.key != null && message.hasOwnProperty(\"key\"))\n            writer.uint32(/* id 1, wireType 2 =*/10).string(message.key);\n        if (message.value != null && message.hasOwnProperty(\"value\"))\n            writer.uint32(/* id 2, wireType 2 =*/18).string(message.value);\n        return writer;\n    };\n\n    /**\n     * Encodes the specified HttpHeader message, length delimited. Does not implicitly {@link HttpHeader.verify|verify} messages.\n     * @function encodeDelimited\n     * @memberof HttpHeader\n     * @static\n     * @param {IHttpHeader} message HttpHeader message or plain object to encode\n     * @param {$protobuf.Writer} [writer] Writer to encode to\n     * @returns {$protobuf.Writer} Writer\n     */\n    HttpHeader.encodeDelimited = function encodeDelimited(message, writer) {\n        return this.encode(message, writer).ldelim();\n    };\n\n    /**\n     * Decodes a HttpHeader message from the specified reader or buffer.\n     * @function decode\n     * @memberof HttpHeader\n     * @static\n     * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from\n     * @param {number} [length] Message length if known beforehand\n     * @returns {HttpHeader} HttpHeader\n     * @throws {Error} If the payload is not a reader or valid buffer\n     * @throws {$protobuf.util.ProtocolError} If required fields are missing\n     */\n    HttpHeader.decode = function decode(reader, length) {\n        if (!(reader instanceof $Reader))\n            reader = $Reader.create(reader);\n        var end = length === undefined ? reader.len : reader.pos + length, message = new $root.HttpHeader();\n        while (reader.pos < end) {\n            var tag = reader.uint32();\n            switch (tag >>> 3) {\n            case 1:\n                message.key = reader.string();\n                break;\n            case 2:\n                message.value = reader.string();\n                break;\n            default:\n                reader.skipType(tag & 7);\n                break;\n            }\n        }\n        return message;\n    };\n\n    /**\n     * Decodes a HttpHeader message from the specified reader or buffer, length delimited.\n     * @function decodeDelimited\n     * @memberof HttpHeader\n     * @static\n     * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from\n     * @returns {HttpHeader} HttpHeader\n     * @throws {Error} If the payload is not a reader or valid buffer\n     * @throws {$protobuf.util.ProtocolError} If required fields are missing\n     */\n    HttpHeader.decodeDelimited = function decodeDelimited(reader) {\n        if (!(reader instanceof $Reader))\n            reader = new $Reader(reader);\n        return this.decode(reader, reader.uint32());\n    };\n\n    /**\n     * Verifies a HttpHeader message.\n     * @function verify\n     * @memberof HttpHeader\n     * @static\n     * @param {Object.<string,*>} message Plain object to verify\n     * @returns {string|null} `null` if valid, otherwise the reason why it is not\n     */\n    HttpHeader.verify = function verify(message) {\n        if (typeof message !== \"object\" || message === null)\n            return \"object expected\";\n        if (message.key != null && message.hasOwnProperty(\"key\"))\n            if (!$util.isString(message.key))\n                return \"key: string expected\";\n        if (message.value != null && message.hasOwnProperty(\"value\"))\n            if (!$util.isString(message.value))\n                return \"value: string expected\";\n        return null;\n    };\n\n    /**\n     * Creates a HttpHeader message from a plain object. Also converts values to their respective internal types.\n     * @function fromObject\n     * @memberof HttpHeader\n     * @static\n     * @param {Object.<string,*>} object Plain object\n     * @returns {HttpHeader} HttpHeader\n     */\n    HttpHeader.fromObject = function fromObject(object) {\n        if (object instanceof $root.HttpHeader)\n            return object;\n        var message = new $root.HttpHeader();\n        if (object.key != null)\n            message.key = String(object.key);\n        if (object.value != null)\n            message.value = String(object.value);\n        return message;\n    };\n\n    /**\n     * Creates a plain object from a HttpHeader message. Also converts values to other types if specified.\n     * @function toObject\n     * @memberof HttpHeader\n     * @static\n     * @param {HttpHeader} message HttpHeader\n     * @param {$protobuf.IConversionOptions} [options] Conversion options\n     * @returns {Object.<string,*>} Plain object\n     */\n    HttpHeader.toObject = function toObject(message, options) {\n        if (!options)\n            options = {};\n        var object = {};\n        if (options.defaults) {\n            object.key = \"\";\n            object.value = \"\";\n        }\n        if (message.key != null && message.hasOwnProperty(\"key\"))\n            object.key = message.key;\n        if (message.value != null && message.hasOwnProperty(\"value\"))\n            object.value = message.value;\n        return object;\n    };\n\n    /**\n     * Converts this HttpHeader to JSON.\n     * @function toJSON\n     * @memberof HttpHeader\n     * @instance\n     * @returns {Object.<string,*>} JSON object\n     */\n    HttpHeader.prototype.toJSON = function toJSON() {\n        return this.constructor.toObject(this, $protobuf.util.toJSONOptions);\n    };\n\n    return HttpHeader;\n})();\n\n$root.ApiGetOpenDataByCloudIdReq = (function() {\n\n    /**\n     * Properties of an ApiGetOpenDataByCloudIdReq.\n     * @exports IApiGetOpenDataByCloudIdReq\n     * @interface IApiGetOpenDataByCloudIdReq\n     * @property {Array.<string>|null} [cloudidList] ApiGetOpenDataByCloudIdReq cloudidList\n     * @property {string|null} [appid] ApiGetOpenDataByCloudIdReq appid\n     */\n\n    /**\n     * Constructs a new ApiGetOpenDataByCloudIdReq.\n     * @exports ApiGetOpenDataByCloudIdReq\n     * @classdesc Represents an ApiGetOpenDataByCloudIdReq.\n     * @implements IApiGetOpenDataByCloudIdReq\n     * @constructor\n     * @param {IApiGetOpenDataByCloudIdReq=} [properties] Properties to set\n     */\n    function ApiGetOpenDataByCloudIdReq(properties) {\n        this.cloudidList = [];\n        if (properties)\n            for (var keys = Object.keys(properties), i = 0; i < keys.length; ++i)\n                if (properties[keys[i]] != null)\n                    this[keys[i]] = properties[keys[i]];\n    }\n\n    /**\n     * ApiGetOpenDataByCloudIdReq cloudidList.\n     * @member {Array.<string>} cloudidList\n     * @memberof ApiGetOpenDataByCloudIdReq\n     * @instance\n     */\n    ApiGetOpenDataByCloudIdReq.prototype.cloudidList = $util.emptyArray;\n\n    /**\n     * ApiGetOpenDataByCloudIdReq appid.\n     * @member {string} appid\n     * @memberof ApiGetOpenDataByCloudIdReq\n     * @instance\n     */\n    ApiGetOpenDataByCloudIdReq.prototype.appid = \"\";\n\n    /**\n     * Creates a new ApiGetOpenDataByCloudIdReq instance using the specified properties.\n     * @function create\n     * @memberof ApiGetOpenDataByCloudIdReq\n     * @static\n     * @param {IApiGetOpenDataByCloudIdReq=} [properties] Properties to set\n     * @returns {ApiGetOpenDataByCloudIdReq} ApiGetOpenDataByCloudIdReq instance\n     */\n    ApiGetOpenDataByCloudIdReq.create = function create(properties) {\n        return new ApiGetOpenDataByCloudIdReq(properties);\n    };\n\n    /**\n     * Encodes the specified ApiGetOpenDataByCloudIdReq message. Does not implicitly {@link ApiGetOpenDataByCloudIdReq.verify|verify} messages.\n     * @function encode\n     * @memberof ApiGetOpenDataByCloudIdReq\n     * @static\n     * @param {IApiGetOpenDataByCloudIdReq} message ApiGetOpenDataByCloudIdReq message or plain object to encode\n     * @param {$protobuf.Writer} [writer] Writer to encode to\n     * @returns {$protobuf.Writer} Writer\n     */\n    ApiGetOpenDataByCloudIdReq.encode = function encode(message, writer) {\n        if (!writer)\n            writer = $Writer.create();\n        if (message.cloudidList != null && message.cloudidList.length)\n            for (var i = 0; i < message.cloudidList.length; ++i)\n                writer.uint32(/* id 2, wireType 2 =*/18).string(message.cloudidList[i]);\n        if (message.appid != null && message.hasOwnProperty(\"appid\"))\n            writer.uint32(/* id 3, wireType 2 =*/26).string(message.appid);\n        return writer;\n    };\n\n    /**\n     * Encodes the specified ApiGetOpenDataByCloudIdReq message, length delimited. Does not implicitly {@link ApiGetOpenDataByCloudIdReq.verify|verify} messages.\n     * @function encodeDelimited\n     * @memberof ApiGetOpenDataByCloudIdReq\n     * @static\n     * @param {IApiGetOpenDataByCloudIdReq} message ApiGetOpenDataByCloudIdReq message or plain object to encode\n     * @param {$protobuf.Writer} [writer] Writer to encode to\n     * @returns {$protobuf.Writer} Writer\n     */\n    ApiGetOpenDataByCloudIdReq.encodeDelimited = function encodeDelimited(message, writer) {\n        return this.encode(message, writer).ldelim();\n    };\n\n    /**\n     * Decodes an ApiGetOpenDataByCloudIdReq message from the specified reader or buffer.\n     * @function decode\n     * @memberof ApiGetOpenDataByCloudIdReq\n     * @static\n     * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from\n     * @param {number} [length] Message length if known beforehand\n     * @returns {ApiGetOpenDataByCloudIdReq} ApiGetOpenDataByCloudIdReq\n     * @throws {Error} If the payload is not a reader or valid buffer\n     * @throws {$protobuf.util.ProtocolError} If required fields are missing\n     */\n    ApiGetOpenDataByCloudIdReq.decode = function decode(reader, length) {\n        if (!(reader instanceof $Reader))\n            reader = $Reader.create(reader);\n        var end = length === undefined ? reader.len : reader.pos + length, message = new $root.ApiGetOpenDataByCloudIdReq();\n        while (reader.pos < end) {\n            var tag = reader.uint32();\n            switch (tag >>> 3) {\n            case 2:\n                if (!(message.cloudidList && message.cloudidList.length))\n                    message.cloudidList = [];\n                message.cloudidList.push(reader.string());\n                break;\n            case 3:\n                message.appid = reader.string();\n                break;\n            default:\n                reader.skipType(tag & 7);\n                break;\n            }\n        }\n        return message;\n    };\n\n    /**\n     * Decodes an ApiGetOpenDataByCloudIdReq message from the specified reader or buffer, length delimited.\n     * @function decodeDelimited\n     * @memberof ApiGetOpenDataByCloudIdReq\n     * @static\n     * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from\n     * @returns {ApiGetOpenDataByCloudIdReq} ApiGetOpenDataByCloudIdReq\n     * @throws {Error} If the payload is not a reader or valid buffer\n     * @throws {$protobuf.util.ProtocolError} If required fields are missing\n     */\n    ApiGetOpenDataByCloudIdReq.decodeDelimited = function decodeDelimited(reader) {\n        if (!(reader instanceof $Reader))\n            reader = new $Reader(reader);\n        return this.decode(reader, reader.uint32());\n    };\n\n    /**\n     * Verifies an ApiGetOpenDataByCloudIdReq message.\n     * @function verify\n     * @memberof ApiGetOpenDataByCloudIdReq\n     * @static\n     * @param {Object.<string,*>} message Plain object to verify\n     * @returns {string|null} `null` if valid, otherwise the reason why it is not\n     */\n    ApiGetOpenDataByCloudIdReq.verify = function verify(message) {\n        if (typeof message !== \"object\" || message === null)\n            return \"object expected\";\n        if (message.cloudidList != null && message.hasOwnProperty(\"cloudidList\")) {\n            if (!Array.isArray(message.cloudidList))\n                return \"cloudidList: array expected\";\n            for (var i = 0; i < message.cloudidList.length; ++i)\n                if (!$util.isString(message.cloudidList[i]))\n                    return \"cloudidList: string[] expected\";\n        }\n        if (message.appid != null && message.hasOwnProperty(\"appid\"))\n            if (!$util.isString(message.appid))\n                return \"appid: string expected\";\n        return null;\n    };\n\n    /**\n     * Creates an ApiGetOpenDataByCloudIdReq message from a plain object. Also converts values to their respective internal types.\n     * @function fromObject\n     * @memberof ApiGetOpenDataByCloudIdReq\n     * @static\n     * @param {Object.<string,*>} object Plain object\n     * @returns {ApiGetOpenDataByCloudIdReq} ApiGetOpenDataByCloudIdReq\n     */\n    ApiGetOpenDataByCloudIdReq.fromObject = function fromObject(object) {\n        if (object instanceof $root.ApiGetOpenDataByCloudIdReq)\n            return object;\n        var message = new $root.ApiGetOpenDataByCloudIdReq();\n        if (object.cloudidList) {\n            if (!Array.isArray(object.cloudidList))\n                throw TypeError(\".ApiGetOpenDataByCloudIdReq.cloudidList: array expected\");\n            message.cloudidList = [];\n            for (var i = 0; i < object.cloudidList.length; ++i)\n                message.cloudidList[i] = String(object.cloudidList[i]);\n        }\n        if (object.appid != null)\n            message.appid = String(object.appid);\n        return message;\n    };\n\n    /**\n     * Creates a plain object from an ApiGetOpenDataByCloudIdReq message. Also converts values to other types if specified.\n     * @function toObject\n     * @memberof ApiGetOpenDataByCloudIdReq\n     * @static\n     * @param {ApiGetOpenDataByCloudIdReq} message ApiGetOpenDataByCloudIdReq\n     * @param {$protobuf.IConversionOptions} [options] Conversion options\n     * @returns {Object.<string,*>} Plain object\n     */\n    ApiGetOpenDataByCloudIdReq.toObject = function toObject(message, options) {\n        if (!options)\n            options = {};\n        var object = {};\n        if (options.arrays || options.defaults)\n            object.cloudidList = [];\n        if (options.defaults)\n            object.appid = \"\";\n        if (message.cloudidList && message.cloudidList.length) {\n            object.cloudidList = [];\n            for (var j = 0; j < message.cloudidList.length; ++j)\n                object.cloudidList[j] = message.cloudidList[j];\n        }\n        if (message.appid != null && message.hasOwnProperty(\"appid\"))\n            object.appid = message.appid;\n        return object;\n    };\n\n    /**\n     * Converts this ApiGetOpenDataByCloudIdReq to JSON.\n     * @function toJSON\n     * @memberof ApiGetOpenDataByCloudIdReq\n     * @instance\n     * @returns {Object.<string,*>} JSON object\n     */\n    ApiGetOpenDataByCloudIdReq.prototype.toJSON = function toJSON() {\n        return this.constructor.toObject(this, $protobuf.util.toJSONOptions);\n    };\n\n    return ApiGetOpenDataByCloudIdReq;\n})();\n\n$root.ApiGetOpenDataByCloudIdResp = (function() {\n\n    /**\n     * Properties of an ApiGetOpenDataByCloudIdResp.\n     * @exports IApiGetOpenDataByCloudIdResp\n     * @interface IApiGetOpenDataByCloudIdResp\n     * @property {Array.<ApiGetOpenDataByCloudIdResp.IOpDataItem>|null} [dataList] ApiGetOpenDataByCloudIdResp dataList\n     */\n\n    /**\n     * Constructs a new ApiGetOpenDataByCloudIdResp.\n     * @exports ApiGetOpenDataByCloudIdResp\n     * @classdesc Represents an ApiGetOpenDataByCloudIdResp.\n     * @implements IApiGetOpenDataByCloudIdResp\n     * @constructor\n     * @param {IApiGetOpenDataByCloudIdResp=} [properties] Properties to set\n     */\n    function ApiGetOpenDataByCloudIdResp(properties) {\n        this.dataList = [];\n        if (properties)\n            for (var keys = Object.keys(properties), i = 0; i < keys.length; ++i)\n                if (properties[keys[i]] != null)\n                    this[keys[i]] = properties[keys[i]];\n    }\n\n    /**\n     * ApiGetOpenDataByCloudIdResp dataList.\n     * @member {Array.<ApiGetOpenDataByCloudIdResp.IOpDataItem>} dataList\n     * @memberof ApiGetOpenDataByCloudIdResp\n     * @instance\n     */\n    ApiGetOpenDataByCloudIdResp.prototype.dataList = $util.emptyArray;\n\n    /**\n     * Creates a new ApiGetOpenDataByCloudIdResp instance using the specified properties.\n     * @function create\n     * @memberof ApiGetOpenDataByCloudIdResp\n     * @static\n     * @param {IApiGetOpenDataByCloudIdResp=} [properties] Properties to set\n     * @returns {ApiGetOpenDataByCloudIdResp} ApiGetOpenDataByCloudIdResp instance\n     */\n    ApiGetOpenDataByCloudIdResp.create = function create(properties) {\n        return new ApiGetOpenDataByCloudIdResp(properties);\n    };\n\n    /**\n     * Encodes the specified ApiGetOpenDataByCloudIdResp message. Does not implicitly {@link ApiGetOpenDataByCloudIdResp.verify|verify} messages.\n     * @function encode\n     * @memberof ApiGetOpenDataByCloudIdResp\n     * @static\n     * @param {IApiGetOpenDataByCloudIdResp} message ApiGetOpenDataByCloudIdResp message or plain object to encode\n     * @param {$protobuf.Writer} [writer] Writer to encode to\n     * @returns {$protobuf.Writer} Writer\n     */\n    ApiGetOpenDataByCloudIdResp.encode = function encode(message, writer) {\n        if (!writer)\n            writer = $Writer.create();\n        if (message.dataList != null && message.dataList.length)\n            for (var i = 0; i < message.dataList.length; ++i)\n                $root.ApiGetOpenDataByCloudIdResp.OpDataItem.encode(message.dataList[i], writer.uint32(/* id 1, wireType 2 =*/10).fork()).ldelim();\n        return writer;\n    };\n\n    /**\n     * Encodes the specified ApiGetOpenDataByCloudIdResp message, length delimited. Does not implicitly {@link ApiGetOpenDataByCloudIdResp.verify|verify} messages.\n     * @function encodeDelimited\n     * @memberof ApiGetOpenDataByCloudIdResp\n     * @static\n     * @param {IApiGetOpenDataByCloudIdResp} message ApiGetOpenDataByCloudIdResp message or plain object to encode\n     * @param {$protobuf.Writer} [writer] Writer to encode to\n     * @returns {$protobuf.Writer} Writer\n     */\n    ApiGetOpenDataByCloudIdResp.encodeDelimited = function encodeDelimited(message, writer) {\n        return this.encode(message, writer).ldelim();\n    };\n\n    /**\n     * Decodes an ApiGetOpenDataByCloudIdResp message from the specified reader or buffer.\n     * @function decode\n     * @memberof ApiGetOpenDataByCloudIdResp\n     * @static\n     * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from\n     * @param {number} [length] Message length if known beforehand\n     * @returns {ApiGetOpenDataByCloudIdResp} ApiGetOpenDataByCloudIdResp\n     * @throws {Error} If the payload is not a reader or valid buffer\n     * @throws {$protobuf.util.ProtocolError} If required fields are missing\n     */\n    ApiGetOpenDataByCloudIdResp.decode = function decode(reader, length) {\n        if (!(reader instanceof $Reader))\n            reader = $Reader.create(reader);\n        var end = length === undefined ? reader.len : reader.pos + length, message = new $root.ApiGetOpenDataByCloudIdResp();\n        while (reader.pos < end) {\n            var tag = reader.uint32();\n            switch (tag >>> 3) {\n            case 1:\n                if (!(message.dataList && message.dataList.length))\n                    message.dataList = [];\n                message.dataList.push($root.ApiGetOpenDataByCloudIdResp.OpDataItem.decode(reader, reader.uint32()));\n                break;\n            default:\n                reader.skipType(tag & 7);\n                break;\n            }\n        }\n        return message;\n    };\n\n    /**\n     * Decodes an ApiGetOpenDataByCloudIdResp message from the specified reader or buffer, length delimited.\n     * @function decodeDelimited\n     * @memberof ApiGetOpenDataByCloudIdResp\n     * @static\n     * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from\n     * @returns {ApiGetOpenDataByCloudIdResp} ApiGetOpenDataByCloudIdResp\n     * @throws {Error} If the payload is not a reader or valid buffer\n     * @throws {$protobuf.util.ProtocolError} If required fields are missing\n     */\n    ApiGetOpenDataByCloudIdResp.decodeDelimited = function decodeDelimited(reader) {\n        if (!(reader instanceof $Reader))\n            reader = new $Reader(reader);\n        return this.decode(reader, reader.uint32());\n    };\n\n    /**\n     * Verifies an ApiGetOpenDataByCloudIdResp message.\n     * @function verify\n     * @memberof ApiGetOpenDataByCloudIdResp\n     * @static\n     * @param {Object.<string,*>} message Plain object to verify\n     * @returns {string|null} `null` if valid, otherwise the reason why it is not\n     */\n    ApiGetOpenDataByCloudIdResp.verify = function verify(message) {\n        if (typeof message !== \"object\" || message === null)\n            return \"object expected\";\n        if (message.dataList != null && message.hasOwnProperty(\"dataList\")) {\n            if (!Array.isArray(message.dataList))\n                return \"dataList: array expected\";\n            for (var i = 0; i < message.dataList.length; ++i) {\n                var error = $root.ApiGetOpenDataByCloudIdResp.OpDataItem.verify(message.dataList[i]);\n                if (error)\n                    return \"dataList.\" + error;\n            }\n        }\n        return null;\n    };\n\n    /**\n     * Creates an ApiGetOpenDataByCloudIdResp message from a plain object. Also converts values to their respective internal types.\n     * @function fromObject\n     * @memberof ApiGetOpenDataByCloudIdResp\n     * @static\n     * @param {Object.<string,*>} object Plain object\n     * @returns {ApiGetOpenDataByCloudIdResp} ApiGetOpenDataByCloudIdResp\n     */\n    ApiGetOpenDataByCloudIdResp.fromObject = function fromObject(object) {\n        if (object instanceof $root.ApiGetOpenDataByCloudIdResp)\n            return object;\n        var message = new $root.ApiGetOpenDataByCloudIdResp();\n        if (object.dataList) {\n            if (!Array.isArray(object.dataList))\n                throw TypeError(\".ApiGetOpenDataByCloudIdResp.dataList: array expected\");\n            message.dataList = [];\n            for (var i = 0; i < object.dataList.length; ++i) {\n                if (typeof object.dataList[i] !== \"object\")\n                    throw TypeError(\".ApiGetOpenDataByCloudIdResp.dataList: object expected\");\n                message.dataList[i] = $root.ApiGetOpenDataByCloudIdResp.OpDataItem.fromObject(object.dataList[i]);\n            }\n        }\n        return message;\n    };\n\n    /**\n     * Creates a plain object from an ApiGetOpenDataByCloudIdResp message. Also converts values to other types if specified.\n     * @function toObject\n     * @memberof ApiGetOpenDataByCloudIdResp\n     * @static\n     * @param {ApiGetOpenDataByCloudIdResp} message ApiGetOpenDataByCloudIdResp\n     * @param {$protobuf.IConversionOptions} [options] Conversion options\n     * @returns {Object.<string,*>} Plain object\n     */\n    ApiGetOpenDataByCloudIdResp.toObject = function toObject(message, options) {\n        if (!options)\n            options = {};\n        var object = {};\n        if (options.arrays || options.defaults)\n            object.dataList = [];\n        if (message.dataList && message.dataList.length) {\n            object.dataList = [];\n            for (var j = 0; j < message.dataList.length; ++j)\n                object.dataList[j] = $root.ApiGetOpenDataByCloudIdResp.OpDataItem.toObject(message.dataList[j], options);\n        }\n        return object;\n    };\n\n    /**\n     * Converts this ApiGetOpenDataByCloudIdResp to JSON.\n     * @function toJSON\n     * @memberof ApiGetOpenDataByCloudIdResp\n     * @instance\n     * @returns {Object.<string,*>} JSON object\n     */\n    ApiGetOpenDataByCloudIdResp.prototype.toJSON = function toJSON() {\n        return this.constructor.toObject(this, $protobuf.util.toJSONOptions);\n    };\n\n    ApiGetOpenDataByCloudIdResp.OpDataItem = (function() {\n\n        /**\n         * Properties of an OpDataItem.\n         * @memberof ApiGetOpenDataByCloudIdResp\n         * @interface IOpDataItem\n         * @property {string|null} [cloudId] OpDataItem cloudId\n         * @property {string|null} [json] OpDataItem json\n         */\n\n        /**\n         * Constructs a new OpDataItem.\n         * @memberof ApiGetOpenDataByCloudIdResp\n         * @classdesc Represents an OpDataItem.\n         * @implements IOpDataItem\n         * @constructor\n         * @param {ApiGetOpenDataByCloudIdResp.IOpDataItem=} [properties] Properties to set\n         */\n        function OpDataItem(properties) {\n            if (properties)\n                for (var keys = Object.keys(properties), i = 0; i < keys.length; ++i)\n                    if (properties[keys[i]] != null)\n                        this[keys[i]] = properties[keys[i]];\n        }\n\n        /**\n         * OpDataItem cloudId.\n         * @member {string} cloudId\n         * @memberof ApiGetOpenDataByCloudIdResp.OpDataItem\n         * @instance\n         */\n        OpDataItem.prototype.cloudId = \"\";\n\n        /**\n         * OpDataItem json.\n         * @member {string} json\n         * @memberof ApiGetOpenDataByCloudIdResp.OpDataItem\n         * @instance\n         */\n        OpDataItem.prototype.json = \"\";\n\n        /**\n         * Creates a new OpDataItem instance using the specified properties.\n         * @function create\n         * @memberof ApiGetOpenDataByCloudIdResp.OpDataItem\n         * @static\n         * @param {ApiGetOpenDataByCloudIdResp.IOpDataItem=} [properties] Properties to set\n         * @returns {ApiGetOpenDataByCloudIdResp.OpDataItem} OpDataItem instance\n         */\n        OpDataItem.create = function create(properties) {\n            return new OpDataItem(properties);\n        };\n\n        /**\n         * Encodes the specified OpDataItem message. Does not implicitly {@link ApiGetOpenDataByCloudIdResp.OpDataItem.verify|verify} messages.\n         * @function encode\n         * @memberof ApiGetOpenDataByCloudIdResp.OpDataItem\n         * @static\n         * @param {ApiGetOpenDataByCloudIdResp.IOpDataItem} message OpDataItem message or plain object to encode\n         * @param {$protobuf.Writer} [writer] Writer to encode to\n         * @returns {$protobuf.Writer} Writer\n         */\n        OpDataItem.encode = function encode(message, writer) {\n            if (!writer)\n                writer = $Writer.create();\n            if (message.cloudId != null && message.hasOwnProperty(\"cloudId\"))\n                writer.uint32(/* id 1, wireType 2 =*/10).string(message.cloudId);\n            if (message.json != null && message.hasOwnProperty(\"json\"))\n                writer.uint32(/* id 2, wireType 2 =*/18).string(message.json);\n            return writer;\n        };\n\n        /**\n         * Encodes the specified OpDataItem message, length delimited. Does not implicitly {@link ApiGetOpenDataByCloudIdResp.OpDataItem.verify|verify} messages.\n         * @function encodeDelimited\n         * @memberof ApiGetOpenDataByCloudIdResp.OpDataItem\n         * @static\n         * @param {ApiGetOpenDataByCloudIdResp.IOpDataItem} message OpDataItem message or plain object to encode\n         * @param {$protobuf.Writer} [writer] Writer to encode to\n         * @returns {$protobuf.Writer} Writer\n         */\n        OpDataItem.encodeDelimited = function encodeDelimited(message, writer) {\n            return this.encode(message, writer).ldelim();\n        };\n\n        /**\n         * Decodes an OpDataItem message from the specified reader or buffer.\n         * @function decode\n         * @memberof ApiGetOpenDataByCloudIdResp.OpDataItem\n         * @static\n         * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from\n         * @param {number} [length] Message length if known beforehand\n         * @returns {ApiGetOpenDataByCloudIdResp.OpDataItem} OpDataItem\n         * @throws {Error} If the payload is not a reader or valid buffer\n         * @throws {$protobuf.util.ProtocolError} If required fields are missing\n         */\n        OpDataItem.decode = function decode(reader, length) {\n            if (!(reader instanceof $Reader))\n                reader = $Reader.create(reader);\n            var end = length === undefined ? reader.len : reader.pos + length, message = new $root.ApiGetOpenDataByCloudIdResp.OpDataItem();\n            while (reader.pos < end) {\n                var tag = reader.uint32();\n                switch (tag >>> 3) {\n                case 1:\n                    message.cloudId = reader.string();\n                    break;\n                case 2:\n                    message.json = reader.string();\n                    break;\n                default:\n                    reader.skipType(tag & 7);\n                    break;\n                }\n            }\n            return message;\n        };\n\n        /**\n         * Decodes an OpDataItem message from the specified reader or buffer, length delimited.\n         * @function decodeDelimited\n         * @memberof ApiGetOpenDataByCloudIdResp.OpDataItem\n         * @static\n         * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from\n         * @returns {ApiGetOpenDataByCloudIdResp.OpDataItem} OpDataItem\n         * @throws {Error} If the payload is not a reader or valid buffer\n         * @throws {$protobuf.util.ProtocolError} If required fields are missing\n         */\n        OpDataItem.decodeDelimited = function decodeDelimited(reader) {\n            if (!(reader instanceof $Reader))\n                reader = new $Reader(reader);\n            return this.decode(reader, reader.uint32());\n        };\n\n        /**\n         * Verifies an OpDataItem message.\n         * @function verify\n         * @memberof ApiGetOpenDataByCloudIdResp.OpDataItem\n         * @static\n         * @param {Object.<string,*>} message Plain object to verify\n         * @returns {string|null} `null` if valid, otherwise the reason why it is not\n         */\n        OpDataItem.verify = function verify(message) {\n            if (typeof message !== \"object\" || message === null)\n                return \"object expected\";\n            if (message.cloudId != null && message.hasOwnProperty(\"cloudId\"))\n                if (!$util.isString(message.cloudId))\n                    return \"cloudId: string expected\";\n            if (message.json != null && message.hasOwnProperty(\"json\"))\n                if (!$util.isString(message.json))\n                    return \"json: string expected\";\n            return null;\n        };\n\n        /**\n         * Creates an OpDataItem message from a plain object. Also converts values to their respective internal types.\n         * @function fromObject\n         * @memberof ApiGetOpenDataByCloudIdResp.OpDataItem\n         * @static\n         * @param {Object.<string,*>} object Plain object\n         * @returns {ApiGetOpenDataByCloudIdResp.OpDataItem} OpDataItem\n         */\n        OpDataItem.fromObject = function fromObject(object) {\n            if (object instanceof $root.ApiGetOpenDataByCloudIdResp.OpDataItem)\n                return object;\n            var message = new $root.ApiGetOpenDataByCloudIdResp.OpDataItem();\n            if (object.cloudId != null)\n                message.cloudId = String(object.cloudId);\n            if (object.json != null)\n                message.json = String(object.json);\n            return message;\n        };\n\n        /**\n         * Creates a plain object from an OpDataItem message. Also converts values to other types if specified.\n         * @function toObject\n         * @memberof ApiGetOpenDataByCloudIdResp.OpDataItem\n         * @static\n         * @param {ApiGetOpenDataByCloudIdResp.OpDataItem} message OpDataItem\n         * @param {$protobuf.IConversionOptions} [options] Conversion options\n         * @returns {Object.<string,*>} Plain object\n         */\n        OpDataItem.toObject = function toObject(message, options) {\n            if (!options)\n                options = {};\n            var object = {};\n            if (options.defaults) {\n                object.cloudId = \"\";\n                object.json = \"\";\n            }\n            if (message.cloudId != null && message.hasOwnProperty(\"cloudId\"))\n                object.cloudId = message.cloudId;\n            if (message.json != null && message.hasOwnProperty(\"json\"))\n                object.json = message.json;\n            return object;\n        };\n\n        /**\n         * Converts this OpDataItem to JSON.\n         * @function toJSON\n         * @memberof ApiGetOpenDataByCloudIdResp.OpDataItem\n         * @instance\n         * @returns {Object.<string,*>} JSON object\n         */\n        OpDataItem.prototype.toJSON = function toJSON() {\n            return this.constructor.toObject(this, $protobuf.util.toJSONOptions);\n        };\n\n        return OpDataItem;\n    })();\n\n    return ApiGetOpenDataByCloudIdResp;\n})();\n\n$root.ApiVoipSignReq = (function() {\n\n    /**\n     * Properties of an ApiVoipSignReq.\n     * @exports IApiVoipSignReq\n     * @interface IApiVoipSignReq\n     * @property {string|null} [groupId] ApiVoipSignReq groupId\n     * @property {number|null} [timestamp] ApiVoipSignReq timestamp\n     * @property {string|null} [nonce] ApiVoipSignReq nonce\n     */\n\n    /**\n     * Constructs a new ApiVoipSignReq.\n     * @exports ApiVoipSignReq\n     * @classdesc Represents an ApiVoipSignReq.\n     * @implements IApiVoipSignReq\n     * @constructor\n     * @param {IApiVoipSignReq=} [properties] Properties to set\n     */\n    function ApiVoipSignReq(properties) {\n        if (properties)\n            for (var keys = Object.keys(properties), i = 0; i < keys.length; ++i)\n                if (properties[keys[i]] != null)\n                    this[keys[i]] = properties[keys[i]];\n    }\n\n    /**\n     * ApiVoipSignReq groupId.\n     * @member {string} groupId\n     * @memberof ApiVoipSignReq\n     * @instance\n     */\n    ApiVoipSignReq.prototype.groupId = \"\";\n\n    /**\n     * ApiVoipSignReq timestamp.\n     * @member {number} timestamp\n     * @memberof ApiVoipSignReq\n     * @instance\n     */\n    ApiVoipSignReq.prototype.timestamp = 0;\n\n    /**\n     * ApiVoipSignReq nonce.\n     * @member {string} nonce\n     * @memberof ApiVoipSignReq\n     * @instance\n     */\n    ApiVoipSignReq.prototype.nonce = \"\";\n\n    /**\n     * Creates a new ApiVoipSignReq instance using the specified properties.\n     * @function create\n     * @memberof ApiVoipSignReq\n     * @static\n     * @param {IApiVoipSignReq=} [properties] Properties to set\n     * @returns {ApiVoipSignReq} ApiVoipSignReq instance\n     */\n    ApiVoipSignReq.create = function create(properties) {\n        return new ApiVoipSignReq(properties);\n    };\n\n    /**\n     * Encodes the specified ApiVoipSignReq message. Does not implicitly {@link ApiVoipSignReq.verify|verify} messages.\n     * @function encode\n     * @memberof ApiVoipSignReq\n     * @static\n     * @param {IApiVoipSignReq} message ApiVoipSignReq message or plain object to encode\n     * @param {$protobuf.Writer} [writer] Writer to encode to\n     * @returns {$protobuf.Writer} Writer\n     */\n    ApiVoipSignReq.encode = function encode(message, writer) {\n        if (!writer)\n            writer = $Writer.create();\n        if (message.groupId != null && message.hasOwnProperty(\"groupId\"))\n            writer.uint32(/* id 2, wireType 2 =*/18).string(message.groupId);\n        if (message.timestamp != null && message.hasOwnProperty(\"timestamp\"))\n            writer.uint32(/* id 3, wireType 0 =*/24).uint32(message.timestamp);\n        if (message.nonce != null && message.hasOwnProperty(\"nonce\"))\n            writer.uint32(/* id 4, wireType 2 =*/34).string(message.nonce);\n        return writer;\n    };\n\n    /**\n     * Encodes the specified ApiVoipSignReq message, length delimited. Does not implicitly {@link ApiVoipSignReq.verify|verify} messages.\n     * @function encodeDelimited\n     * @memberof ApiVoipSignReq\n     * @static\n     * @param {IApiVoipSignReq} message ApiVoipSignReq message or plain object to encode\n     * @param {$protobuf.Writer} [writer] Writer to encode to\n     * @returns {$protobuf.Writer} Writer\n     */\n    ApiVoipSignReq.encodeDelimited = function encodeDelimited(message, writer) {\n        return this.encode(message, writer).ldelim();\n    };\n\n    /**\n     * Decodes an ApiVoipSignReq message from the specified reader or buffer.\n     * @function decode\n     * @memberof ApiVoipSignReq\n     * @static\n     * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from\n     * @param {number} [length] Message length if known beforehand\n     * @returns {ApiVoipSignReq} ApiVoipSignReq\n     * @throws {Error} If the payload is not a reader or valid buffer\n     * @throws {$protobuf.util.ProtocolError} If required fields are missing\n     */\n    ApiVoipSignReq.decode = function decode(reader, length) {\n        if (!(reader instanceof $Reader))\n            reader = $Reader.create(reader);\n        var end = length === undefined ? reader.len : reader.pos + length, message = new $root.ApiVoipSignReq();\n        while (reader.pos < end) {\n            var tag = reader.uint32();\n            switch (tag >>> 3) {\n            case 2:\n                message.groupId = reader.string();\n                break;\n            case 3:\n                message.timestamp = reader.uint32();\n                break;\n            case 4:\n                message.nonce = reader.string();\n                break;\n            default:\n                reader.skipType(tag & 7);\n                break;\n            }\n        }\n        return message;\n    };\n\n    /**\n     * Decodes an ApiVoipSignReq message from the specified reader or buffer, length delimited.\n     * @function decodeDelimited\n     * @memberof ApiVoipSignReq\n     * @static\n     * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from\n     * @returns {ApiVoipSignReq} ApiVoipSignReq\n     * @throws {Error} If the payload is not a reader or valid buffer\n     * @throws {$protobuf.util.ProtocolError} If required fields are missing\n     */\n    ApiVoipSignReq.decodeDelimited = function decodeDelimited(reader) {\n        if (!(reader instanceof $Reader))\n            reader = new $Reader(reader);\n        return this.decode(reader, reader.uint32());\n    };\n\n    /**\n     * Verifies an ApiVoipSignReq message.\n     * @function verify\n     * @memberof ApiVoipSignReq\n     * @static\n     * @param {Object.<string,*>} message Plain object to verify\n     * @returns {string|null} `null` if valid, otherwise the reason why it is not\n     */\n    ApiVoipSignReq.verify = function verify(message) {\n        if (typeof message !== \"object\" || message === null)\n            return \"object expected\";\n        if (message.groupId != null && message.hasOwnProperty(\"groupId\"))\n            if (!$util.isString(message.groupId))\n                return \"groupId: string expected\";\n        if (message.timestamp != null && message.hasOwnProperty(\"timestamp\"))\n            if (!$util.isInteger(message.timestamp))\n                return \"timestamp: integer expected\";\n        if (message.nonce != null && message.hasOwnProperty(\"nonce\"))\n            if (!$util.isString(message.nonce))\n                return \"nonce: string expected\";\n        return null;\n    };\n\n    /**\n     * Creates an ApiVoipSignReq message from a plain object. Also converts values to their respective internal types.\n     * @function fromObject\n     * @memberof ApiVoipSignReq\n     * @static\n     * @param {Object.<string,*>} object Plain object\n     * @returns {ApiVoipSignReq} ApiVoipSignReq\n     */\n    ApiVoipSignReq.fromObject = function fromObject(object) {\n        if (object instanceof $root.ApiVoipSignReq)\n            return object;\n        var message = new $root.ApiVoipSignReq();\n        if (object.groupId != null)\n            message.groupId = String(object.groupId);\n        if (object.timestamp != null)\n            message.timestamp = object.timestamp >>> 0;\n        if (object.nonce != null)\n            message.nonce = String(object.nonce);\n        return message;\n    };\n\n    /**\n     * Creates a plain object from an ApiVoipSignReq message. Also converts values to other types if specified.\n     * @function toObject\n     * @memberof ApiVoipSignReq\n     * @static\n     * @param {ApiVoipSignReq} message ApiVoipSignReq\n     * @param {$protobuf.IConversionOptions} [options] Conversion options\n     * @returns {Object.<string,*>} Plain object\n     */\n    ApiVoipSignReq.toObject = function toObject(message, options) {\n        if (!options)\n            options = {};\n        var object = {};\n        if (options.defaults) {\n            object.groupId = \"\";\n            object.timestamp = 0;\n            object.nonce = \"\";\n        }\n        if (message.groupId != null && message.hasOwnProperty(\"groupId\"))\n            object.groupId = message.groupId;\n        if (message.timestamp != null && message.hasOwnProperty(\"timestamp\"))\n            object.timestamp = message.timestamp;\n        if (message.nonce != null && message.hasOwnProperty(\"nonce\"))\n            object.nonce = message.nonce;\n        return object;\n    };\n\n    /**\n     * Converts this ApiVoipSignReq to JSON.\n     * @function toJSON\n     * @memberof ApiVoipSignReq\n     * @instance\n     * @returns {Object.<string,*>} JSON object\n     */\n    ApiVoipSignReq.prototype.toJSON = function toJSON() {\n        return this.constructor.toObject(this, $protobuf.util.toJSONOptions);\n    };\n\n    return ApiVoipSignReq;\n})();\n\n$root.ApiVoipSignResp = (function() {\n\n    /**\n     * Properties of an ApiVoipSignResp.\n     * @exports IApiVoipSignResp\n     * @interface IApiVoipSignResp\n     * @property {string|null} [signature] ApiVoipSignResp signature\n     */\n\n    /**\n     * Constructs a new ApiVoipSignResp.\n     * @exports ApiVoipSignResp\n     * @classdesc Represents an ApiVoipSignResp.\n     * @implements IApiVoipSignResp\n     * @constructor\n     * @param {IApiVoipSignResp=} [properties] Properties to set\n     */\n    function ApiVoipSignResp(properties) {\n        if (properties)\n            for (var keys = Object.keys(properties), i = 0; i < keys.length; ++i)\n                if (properties[keys[i]] != null)\n                    this[keys[i]] = properties[keys[i]];\n    }\n\n    /**\n     * ApiVoipSignResp signature.\n     * @member {string} signature\n     * @memberof ApiVoipSignResp\n     * @instance\n     */\n    ApiVoipSignResp.prototype.signature = \"\";\n\n    /**\n     * Creates a new ApiVoipSignResp instance using the specified properties.\n     * @function create\n     * @memberof ApiVoipSignResp\n     * @static\n     * @param {IApiVoipSignResp=} [properties] Properties to set\n     * @returns {ApiVoipSignResp} ApiVoipSignResp instance\n     */\n    ApiVoipSignResp.create = function create(properties) {\n        return new ApiVoipSignResp(properties);\n    };\n\n    /**\n     * Encodes the specified ApiVoipSignResp message. Does not implicitly {@link ApiVoipSignResp.verify|verify} messages.\n     * @function encode\n     * @memberof ApiVoipSignResp\n     * @static\n     * @param {IApiVoipSignResp} message ApiVoipSignResp message or plain object to encode\n     * @param {$protobuf.Writer} [writer] Writer to encode to\n     * @returns {$protobuf.Writer} Writer\n     */\n    ApiVoipSignResp.encode = function encode(message, writer) {\n        if (!writer)\n            writer = $Writer.create();\n        if (message.signature != null && message.hasOwnProperty(\"signature\"))\n            writer.uint32(/* id 1, wireType 2 =*/10).string(message.signature);\n        return writer;\n    };\n\n    /**\n     * Encodes the specified ApiVoipSignResp message, length delimited. Does not implicitly {@link ApiVoipSignResp.verify|verify} messages.\n     * @function encodeDelimited\n     * @memberof ApiVoipSignResp\n     * @static\n     * @param {IApiVoipSignResp} message ApiVoipSignResp message or plain object to encode\n     * @param {$protobuf.Writer} [writer] Writer to encode to\n     * @returns {$protobuf.Writer} Writer\n     */\n    ApiVoipSignResp.encodeDelimited = function encodeDelimited(message, writer) {\n        return this.encode(message, writer).ldelim();\n    };\n\n    /**\n     * Decodes an ApiVoipSignResp message from the specified reader or buffer.\n     * @function decode\n     * @memberof ApiVoipSignResp\n     * @static\n     * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from\n     * @param {number} [length] Message length if known beforehand\n     * @returns {ApiVoipSignResp} ApiVoipSignResp\n     * @throws {Error} If the payload is not a reader or valid buffer\n     * @throws {$protobuf.util.ProtocolError} If required fields are missing\n     */\n    ApiVoipSignResp.decode = function decode(reader, length) {\n        if (!(reader instanceof $Reader))\n            reader = $Reader.create(reader);\n        var end = length === undefined ? reader.len : reader.pos + length, message = new $root.ApiVoipSignResp();\n        while (reader.pos < end) {\n            var tag = reader.uint32();\n            switch (tag >>> 3) {\n            case 1:\n                message.signature = reader.string();\n                break;\n            default:\n                reader.skipType(tag & 7);\n                break;\n            }\n        }\n        return message;\n    };\n\n    /**\n     * Decodes an ApiVoipSignResp message from the specified reader or buffer, length delimited.\n     * @function decodeDelimited\n     * @memberof ApiVoipSignResp\n     * @static\n     * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from\n     * @returns {ApiVoipSignResp} ApiVoipSignResp\n     * @throws {Error} If the payload is not a reader or valid buffer\n     * @throws {$protobuf.util.ProtocolError} If required fields are missing\n     */\n    ApiVoipSignResp.decodeDelimited = function decodeDelimited(reader) {\n        if (!(reader instanceof $Reader))\n            reader = new $Reader(reader);\n        return this.decode(reader, reader.uint32());\n    };\n\n    /**\n     * Verifies an ApiVoipSignResp message.\n     * @function verify\n     * @memberof ApiVoipSignResp\n     * @static\n     * @param {Object.<string,*>} message Plain object to verify\n     * @returns {string|null} `null` if valid, otherwise the reason why it is not\n     */\n    ApiVoipSignResp.verify = function verify(message) {\n        if (typeof message !== \"object\" || message === null)\n            return \"object expected\";\n        if (message.signature != null && message.hasOwnProperty(\"signature\"))\n            if (!$util.isString(message.signature))\n                return \"signature: string expected\";\n        return null;\n    };\n\n    /**\n     * Creates an ApiVoipSignResp message from a plain object. Also converts values to their respective internal types.\n     * @function fromObject\n     * @memberof ApiVoipSignResp\n     * @static\n     * @param {Object.<string,*>} object Plain object\n     * @returns {ApiVoipSignResp} ApiVoipSignResp\n     */\n    ApiVoipSignResp.fromObject = function fromObject(object) {\n        if (object instanceof $root.ApiVoipSignResp)\n            return object;\n        var message = new $root.ApiVoipSignResp();\n        if (object.signature != null)\n            message.signature = String(object.signature);\n        return message;\n    };\n\n    /**\n     * Creates a plain object from an ApiVoipSignResp message. Also converts values to other types if specified.\n     * @function toObject\n     * @memberof ApiVoipSignResp\n     * @static\n     * @param {ApiVoipSignResp} message ApiVoipSignResp\n     * @param {$protobuf.IConversionOptions} [options] Conversion options\n     * @returns {Object.<string,*>} Plain object\n     */\n    ApiVoipSignResp.toObject = function toObject(message, options) {\n        if (!options)\n            options = {};\n        var object = {};\n        if (options.defaults)\n            object.signature = \"\";\n        if (message.signature != null && message.hasOwnProperty(\"signature\"))\n            object.signature = message.signature;\n        return object;\n    };\n\n    /**\n     * Converts this ApiVoipSignResp to JSON.\n     * @function toJSON\n     * @memberof ApiVoipSignResp\n     * @instance\n     * @returns {Object.<string,*>} JSON object\n     */\n    ApiVoipSignResp.prototype.toJSON = function toJSON() {\n        return this.constructor.toObject(this, $protobuf.util.toJSONOptions);\n    };\n\n    return ApiVoipSignResp;\n})();\n\n$root.GetCloudCallSignReq = (function() {\n\n    /**\n     * Properties of a GetCloudCallSignReq.\n     * @exports IGetCloudCallSignReq\n     * @interface IGetCloudCallSignReq\n     * @property {Array.<string>|null} [parameterList] GetCloudCallSignReq parameterList\n     */\n\n    /**\n     * Constructs a new GetCloudCallSignReq.\n     * @exports GetCloudCallSignReq\n     * @classdesc Represents a GetCloudCallSignReq.\n     * @implements IGetCloudCallSignReq\n     * @constructor\n     * @param {IGetCloudCallSignReq=} [properties] Properties to set\n     */\n    function GetCloudCallSignReq(properties) {\n        this.parameterList = [];\n        if (properties)\n            for (var keys = Object.keys(properties), i = 0; i < keys.length; ++i)\n                if (properties[keys[i]] != null)\n                    this[keys[i]] = properties[keys[i]];\n    }\n\n    /**\n     * GetCloudCallSignReq parameterList.\n     * @member {Array.<string>} parameterList\n     * @memberof GetCloudCallSignReq\n     * @instance\n     */\n    GetCloudCallSignReq.prototype.parameterList = $util.emptyArray;\n\n    /**\n     * Creates a new GetCloudCallSignReq instance using the specified properties.\n     * @function create\n     * @memberof GetCloudCallSignReq\n     * @static\n     * @param {IGetCloudCallSignReq=} [properties] Properties to set\n     * @returns {GetCloudCallSignReq} GetCloudCallSignReq instance\n     */\n    GetCloudCallSignReq.create = function create(properties) {\n        return new GetCloudCallSignReq(properties);\n    };\n\n    /**\n     * Encodes the specified GetCloudCallSignReq message. Does not implicitly {@link GetCloudCallSignReq.verify|verify} messages.\n     * @function encode\n     * @memberof GetCloudCallSignReq\n     * @static\n     * @param {IGetCloudCallSignReq} message GetCloudCallSignReq message or plain object to encode\n     * @param {$protobuf.Writer} [writer] Writer to encode to\n     * @returns {$protobuf.Writer} Writer\n     */\n    GetCloudCallSignReq.encode = function encode(message, writer) {\n        if (!writer)\n            writer = $Writer.create();\n        if (message.parameterList != null && message.parameterList.length)\n            for (var i = 0; i < message.parameterList.length; ++i)\n                writer.uint32(/* id 2, wireType 2 =*/18).string(message.parameterList[i]);\n        return writer;\n    };\n\n    /**\n     * Encodes the specified GetCloudCallSignReq message, length delimited. Does not implicitly {@link GetCloudCallSignReq.verify|verify} messages.\n     * @function encodeDelimited\n     * @memberof GetCloudCallSignReq\n     * @static\n     * @param {IGetCloudCallSignReq} message GetCloudCallSignReq message or plain object to encode\n     * @param {$protobuf.Writer} [writer] Writer to encode to\n     * @returns {$protobuf.Writer} Writer\n     */\n    GetCloudCallSignReq.encodeDelimited = function encodeDelimited(message, writer) {\n        return this.encode(message, writer).ldelim();\n    };\n\n    /**\n     * Decodes a GetCloudCallSignReq message from the specified reader or buffer.\n     * @function decode\n     * @memberof GetCloudCallSignReq\n     * @static\n     * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from\n     * @param {number} [length] Message length if known beforehand\n     * @returns {GetCloudCallSignReq} GetCloudCallSignReq\n     * @throws {Error} If the payload is not a reader or valid buffer\n     * @throws {$protobuf.util.ProtocolError} If required fields are missing\n     */\n    GetCloudCallSignReq.decode = function decode(reader, length) {\n        if (!(reader instanceof $Reader))\n            reader = $Reader.create(reader);\n        var end = length === undefined ? reader.len : reader.pos + length, message = new $root.GetCloudCallSignReq();\n        while (reader.pos < end) {\n            var tag = reader.uint32();\n            switch (tag >>> 3) {\n            case 2:\n                if (!(message.parameterList && message.parameterList.length))\n                    message.parameterList = [];\n                message.parameterList.push(reader.string());\n                break;\n            default:\n                reader.skipType(tag & 7);\n                break;\n            }\n        }\n        return message;\n    };\n\n    /**\n     * Decodes a GetCloudCallSignReq message from the specified reader or buffer, length delimited.\n     * @function decodeDelimited\n     * @memberof GetCloudCallSignReq\n     * @static\n     * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from\n     * @returns {GetCloudCallSignReq} GetCloudCallSignReq\n     * @throws {Error} If the payload is not a reader or valid buffer\n     * @throws {$protobuf.util.ProtocolError} If required fields are missing\n     */\n    GetCloudCallSignReq.decodeDelimited = function decodeDelimited(reader) {\n        if (!(reader instanceof $Reader))\n            reader = new $Reader(reader);\n        return this.decode(reader, reader.uint32());\n    };\n\n    /**\n     * Verifies a GetCloudCallSignReq message.\n     * @function verify\n     * @memberof GetCloudCallSignReq\n     * @static\n     * @param {Object.<string,*>} message Plain object to verify\n     * @returns {string|null} `null` if valid, otherwise the reason why it is not\n     */\n    GetCloudCallSignReq.verify = function verify(message) {\n        if (typeof message !== \"object\" || message === null)\n            return \"object expected\";\n        if (message.parameterList != null && message.hasOwnProperty(\"parameterList\")) {\n            if (!Array.isArray(message.parameterList))\n                return \"parameterList: array expected\";\n            for (var i = 0; i < message.parameterList.length; ++i)\n                if (!$util.isString(message.parameterList[i]))\n                    return \"parameterList: string[] expected\";\n        }\n        return null;\n    };\n\n    /**\n     * Creates a GetCloudCallSignReq message from a plain object. Also converts values to their respective internal types.\n     * @function fromObject\n     * @memberof GetCloudCallSignReq\n     * @static\n     * @param {Object.<string,*>} object Plain object\n     * @returns {GetCloudCallSignReq} GetCloudCallSignReq\n     */\n    GetCloudCallSignReq.fromObject = function fromObject(object) {\n        if (object instanceof $root.GetCloudCallSignReq)\n            return object;\n        var message = new $root.GetCloudCallSignReq();\n        if (object.parameterList) {\n            if (!Array.isArray(object.parameterList))\n                throw TypeError(\".GetCloudCallSignReq.parameterList: array expected\");\n            message.parameterList = [];\n            for (var i = 0; i < object.parameterList.length; ++i)\n                message.parameterList[i] = String(object.parameterList[i]);\n        }\n        return message;\n    };\n\n    /**\n     * Creates a plain object from a GetCloudCallSignReq message. Also converts values to other types if specified.\n     * @function toObject\n     * @memberof GetCloudCallSignReq\n     * @static\n     * @param {GetCloudCallSignReq} message GetCloudCallSignReq\n     * @param {$protobuf.IConversionOptions} [options] Conversion options\n     * @returns {Object.<string,*>} Plain object\n     */\n    GetCloudCallSignReq.toObject = function toObject(message, options) {\n        if (!options)\n            options = {};\n        var object = {};\n        if (options.arrays || options.defaults)\n            object.parameterList = [];\n        if (message.parameterList && message.parameterList.length) {\n            object.parameterList = [];\n            for (var j = 0; j < message.parameterList.length; ++j)\n                object.parameterList[j] = message.parameterList[j];\n        }\n        return object;\n    };\n\n    /**\n     * Converts this GetCloudCallSignReq to JSON.\n     * @function toJSON\n     * @memberof GetCloudCallSignReq\n     * @instance\n     * @returns {Object.<string,*>} JSON object\n     */\n    GetCloudCallSignReq.prototype.toJSON = function toJSON() {\n        return this.constructor.toObject(this, $protobuf.util.toJSONOptions);\n    };\n\n    return GetCloudCallSignReq;\n})();\n\n$root.GetCloudCallSignResp = (function() {\n\n    /**\n     * Properties of a GetCloudCallSignResp.\n     * @exports IGetCloudCallSignResp\n     * @interface IGetCloudCallSignResp\n     * @property {string|null} [signature] GetCloudCallSignResp signature\n     */\n\n    /**\n     * Constructs a new GetCloudCallSignResp.\n     * @exports GetCloudCallSignResp\n     * @classdesc Represents a GetCloudCallSignResp.\n     * @implements IGetCloudCallSignResp\n     * @constructor\n     * @param {IGetCloudCallSignResp=} [properties] Properties to set\n     */\n    function GetCloudCallSignResp(properties) {\n        if (properties)\n            for (var keys = Object.keys(properties), i = 0; i < keys.length; ++i)\n                if (properties[keys[i]] != null)\n                    this[keys[i]] = properties[keys[i]];\n    }\n\n    /**\n     * GetCloudCallSignResp signature.\n     * @member {string} signature\n     * @memberof GetCloudCallSignResp\n     * @instance\n     */\n    GetCloudCallSignResp.prototype.signature = \"\";\n\n    /**\n     * Creates a new GetCloudCallSignResp instance using the specified properties.\n     * @function create\n     * @memberof GetCloudCallSignResp\n     * @static\n     * @param {IGetCloudCallSignResp=} [properties] Properties to set\n     * @returns {GetCloudCallSignResp} GetCloudCallSignResp instance\n     */\n    GetCloudCallSignResp.create = function create(properties) {\n        return new GetCloudCallSignResp(properties);\n    };\n\n    /**\n     * Encodes the specified GetCloudCallSignResp message. Does not implicitly {@link GetCloudCallSignResp.verify|verify} messages.\n     * @function encode\n     * @memberof GetCloudCallSignResp\n     * @static\n     * @param {IGetCloudCallSignResp} message GetCloudCallSignResp message or plain object to encode\n     * @param {$protobuf.Writer} [writer] Writer to encode to\n     * @returns {$protobuf.Writer} Writer\n     */\n    GetCloudCallSignResp.encode = function encode(message, writer) {\n        if (!writer)\n            writer = $Writer.create();\n        if (message.signature != null && message.hasOwnProperty(\"signature\"))\n            writer.uint32(/* id 1, wireType 2 =*/10).string(message.signature);\n        return writer;\n    };\n\n    /**\n     * Encodes the specified GetCloudCallSignResp message, length delimited. Does not implicitly {@link GetCloudCallSignResp.verify|verify} messages.\n     * @function encodeDelimited\n     * @memberof GetCloudCallSignResp\n     * @static\n     * @param {IGetCloudCallSignResp} message GetCloudCallSignResp message or plain object to encode\n     * @param {$protobuf.Writer} [writer] Writer to encode to\n     * @returns {$protobuf.Writer} Writer\n     */\n    GetCloudCallSignResp.encodeDelimited = function encodeDelimited(message, writer) {\n        return this.encode(message, writer).ldelim();\n    };\n\n    /**\n     * Decodes a GetCloudCallSignResp message from the specified reader or buffer.\n     * @function decode\n     * @memberof GetCloudCallSignResp\n     * @static\n     * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from\n     * @param {number} [length] Message length if known beforehand\n     * @returns {GetCloudCallSignResp} GetCloudCallSignResp\n     * @throws {Error} If the payload is not a reader or valid buffer\n     * @throws {$protobuf.util.ProtocolError} If required fields are missing\n     */\n    GetCloudCallSignResp.decode = function decode(reader, length) {\n        if (!(reader instanceof $Reader))\n            reader = $Reader.create(reader);\n        var end = length === undefined ? reader.len : reader.pos + length, message = new $root.GetCloudCallSignResp();\n        while (reader.pos < end) {\n            var tag = reader.uint32();\n            switch (tag >>> 3) {\n            case 1:\n                message.signature = reader.string();\n                break;\n            default:\n                reader.skipType(tag & 7);\n                break;\n            }\n        }\n        return message;\n    };\n\n    /**\n     * Decodes a GetCloudCallSignResp message from the specified reader or buffer, length delimited.\n     * @function decodeDelimited\n     * @memberof GetCloudCallSignResp\n     * @static\n     * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from\n     * @returns {GetCloudCallSignResp} GetCloudCallSignResp\n     * @throws {Error} If the payload is not a reader or valid buffer\n     * @throws {$protobuf.util.ProtocolError} If required fields are missing\n     */\n    GetCloudCallSignResp.decodeDelimited = function decodeDelimited(reader) {\n        if (!(reader instanceof $Reader))\n            reader = new $Reader(reader);\n        return this.decode(reader, reader.uint32());\n    };\n\n    /**\n     * Verifies a GetCloudCallSignResp message.\n     * @function verify\n     * @memberof GetCloudCallSignResp\n     * @static\n     * @param {Object.<string,*>} message Plain object to verify\n     * @returns {string|null} `null` if valid, otherwise the reason why it is not\n     */\n    GetCloudCallSignResp.verify = function verify(message) {\n        if (typeof message !== \"object\" || message === null)\n            return \"object expected\";\n        if (message.signature != null && message.hasOwnProperty(\"signature\"))\n            if (!$util.isString(message.signature))\n                return \"signature: string expected\";\n        return null;\n    };\n\n    /**\n     * Creates a GetCloudCallSignResp message from a plain object. Also converts values to their respective internal types.\n     * @function fromObject\n     * @memberof GetCloudCallSignResp\n     * @static\n     * @param {Object.<string,*>} object Plain object\n     * @returns {GetCloudCallSignResp} GetCloudCallSignResp\n     */\n    GetCloudCallSignResp.fromObject = function fromObject(object) {\n        if (object instanceof $root.GetCloudCallSignResp)\n            return object;\n        var message = new $root.GetCloudCallSignResp();\n        if (object.signature != null)\n            message.signature = String(object.signature);\n        return message;\n    };\n\n    /**\n     * Creates a plain object from a GetCloudCallSignResp message. Also converts values to other types if specified.\n     * @function toObject\n     * @memberof GetCloudCallSignResp\n     * @static\n     * @param {GetCloudCallSignResp} message GetCloudCallSignResp\n     * @param {$protobuf.IConversionOptions} [options] Conversion options\n     * @returns {Object.<string,*>} Plain object\n     */\n    GetCloudCallSignResp.toObject = function toObject(message, options) {\n        if (!options)\n            options = {};\n        var object = {};\n        if (options.defaults)\n            object.signature = \"\";\n        if (message.signature != null && message.hasOwnProperty(\"signature\"))\n            object.signature = message.signature;\n        return object;\n    };\n\n    /**\n     * Converts this GetCloudCallSignResp to JSON.\n     * @function toJSON\n     * @memberof GetCloudCallSignResp\n     * @instance\n     * @returns {Object.<string,*>} JSON object\n     */\n    GetCloudCallSignResp.prototype.toJSON = function toJSON() {\n        return this.constructor.toObject(this, $protobuf.util.toJSONOptions);\n    };\n\n    return GetCloudCallSignResp;\n})();\n\n$root.AuthorizationInfo = (function() {\n\n    /**\n     * Properties of an AuthorizationInfo.\n     * @exports IAuthorizationInfo\n     * @interface IAuthorizationInfo\n     * @property {AuthorizationInfo.ITcbCredentials|null} [tcbCredentials] AuthorizationInfo tcbCredentials\n     * @property {Uint8Array|null} [wxParam] AuthorizationInfo wxParam\n     */\n\n    /**\n     * Constructs a new AuthorizationInfo.\n     * @exports AuthorizationInfo\n     * @classdesc Represents an AuthorizationInfo.\n     * @implements IAuthorizationInfo\n     * @constructor\n     * @param {IAuthorizationInfo=} [properties] Properties to set\n     */\n    function AuthorizationInfo(properties) {\n        if (properties)\n            for (var keys = Object.keys(properties), i = 0; i < keys.length; ++i)\n                if (properties[keys[i]] != null)\n                    this[keys[i]] = properties[keys[i]];\n    }\n\n    /**\n     * AuthorizationInfo tcbCredentials.\n     * @member {AuthorizationInfo.ITcbCredentials|null|undefined} tcbCredentials\n     * @memberof AuthorizationInfo\n     * @instance\n     */\n    AuthorizationInfo.prototype.tcbCredentials = null;\n\n    /**\n     * AuthorizationInfo wxParam.\n     * @member {Uint8Array} wxParam\n     * @memberof AuthorizationInfo\n     * @instance\n     */\n    AuthorizationInfo.prototype.wxParam = $util.newBuffer([]);\n\n    /**\n     * Creates a new AuthorizationInfo instance using the specified properties.\n     * @function create\n     * @memberof AuthorizationInfo\n     * @static\n     * @param {IAuthorizationInfo=} [properties] Properties to set\n     * @returns {AuthorizationInfo} AuthorizationInfo instance\n     */\n    AuthorizationInfo.create = function create(properties) {\n        return new AuthorizationInfo(properties);\n    };\n\n    /**\n     * Encodes the specified AuthorizationInfo message. Does not implicitly {@link AuthorizationInfo.verify|verify} messages.\n     * @function encode\n     * @memberof AuthorizationInfo\n     * @static\n     * @param {IAuthorizationInfo} message AuthorizationInfo message or plain object to encode\n     * @param {$protobuf.Writer} [writer] Writer to encode to\n     * @returns {$protobuf.Writer} Writer\n     */\n    AuthorizationInfo.encode = function encode(message, writer) {\n        if (!writer)\n            writer = $Writer.create();\n        if (message.tcbCredentials != null && message.hasOwnProperty(\"tcbCredentials\"))\n            $root.AuthorizationInfo.TcbCredentials.encode(message.tcbCredentials, writer.uint32(/* id 1, wireType 2 =*/10).fork()).ldelim();\n        if (message.wxParam != null && message.hasOwnProperty(\"wxParam\"))\n            writer.uint32(/* id 2, wireType 2 =*/18).bytes(message.wxParam);\n        return writer;\n    };\n\n    /**\n     * Encodes the specified AuthorizationInfo message, length delimited. Does not implicitly {@link AuthorizationInfo.verify|verify} messages.\n     * @function encodeDelimited\n     * @memberof AuthorizationInfo\n     * @static\n     * @param {IAuthorizationInfo} message AuthorizationInfo message or plain object to encode\n     * @param {$protobuf.Writer} [writer] Writer to encode to\n     * @returns {$protobuf.Writer} Writer\n     */\n    AuthorizationInfo.encodeDelimited = function encodeDelimited(message, writer) {\n        return this.encode(message, writer).ldelim();\n    };\n\n    /**\n     * Decodes an AuthorizationInfo message from the specified reader or buffer.\n     * @function decode\n     * @memberof AuthorizationInfo\n     * @static\n     * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from\n     * @param {number} [length] Message length if known beforehand\n     * @returns {AuthorizationInfo} AuthorizationInfo\n     * @throws {Error} If the payload is not a reader or valid buffer\n     * @throws {$protobuf.util.ProtocolError} If required fields are missing\n     */\n    AuthorizationInfo.decode = function decode(reader, length) {\n        if (!(reader instanceof $Reader))\n            reader = $Reader.create(reader);\n        var end = length === undefined ? reader.len : reader.pos + length, message = new $root.AuthorizationInfo();\n        while (reader.pos < end) {\n            var tag = reader.uint32();\n            switch (tag >>> 3) {\n            case 1:\n                message.tcbCredentials = $root.AuthorizationInfo.TcbCredentials.decode(reader, reader.uint32());\n                break;\n            case 2:\n                message.wxParam = reader.bytes();\n                break;\n            default:\n                reader.skipType(tag & 7);\n                break;\n            }\n        }\n        return message;\n    };\n\n    /**\n     * Decodes an AuthorizationInfo message from the specified reader or buffer, length delimited.\n     * @function decodeDelimited\n     * @memberof AuthorizationInfo\n     * @static\n     * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from\n     * @returns {AuthorizationInfo} AuthorizationInfo\n     * @throws {Error} If the payload is not a reader or valid buffer\n     * @throws {$protobuf.util.ProtocolError} If required fields are missing\n     */\n    AuthorizationInfo.decodeDelimited = function decodeDelimited(reader) {\n        if (!(reader instanceof $Reader))\n            reader = new $Reader(reader);\n        return this.decode(reader, reader.uint32());\n    };\n\n    /**\n     * Verifies an AuthorizationInfo message.\n     * @function verify\n     * @memberof AuthorizationInfo\n     * @static\n     * @param {Object.<string,*>} message Plain object to verify\n     * @returns {string|null} `null` if valid, otherwise the reason why it is not\n     */\n    AuthorizationInfo.verify = function verify(message) {\n        if (typeof message !== \"object\" || message === null)\n            return \"object expected\";\n        if (message.tcbCredentials != null && message.hasOwnProperty(\"tcbCredentials\")) {\n            var error = $root.AuthorizationInfo.TcbCredentials.verify(message.tcbCredentials);\n            if (error)\n                return \"tcbCredentials.\" + error;\n        }\n        if (message.wxParam != null && message.hasOwnProperty(\"wxParam\"))\n            if (!(message.wxParam && typeof message.wxParam.length === \"number\" || $util.isString(message.wxParam)))\n                return \"wxParam: buffer expected\";\n        return null;\n    };\n\n    /**\n     * Creates an AuthorizationInfo message from a plain object. Also converts values to their respective internal types.\n     * @function fromObject\n     * @memberof AuthorizationInfo\n     * @static\n     * @param {Object.<string,*>} object Plain object\n     * @returns {AuthorizationInfo} AuthorizationInfo\n     */\n    AuthorizationInfo.fromObject = function fromObject(object) {\n        if (object instanceof $root.AuthorizationInfo)\n            return object;\n        var message = new $root.AuthorizationInfo();\n        if (object.tcbCredentials != null) {\n            if (typeof object.tcbCredentials !== \"object\")\n                throw TypeError(\".AuthorizationInfo.tcbCredentials: object expected\");\n            message.tcbCredentials = $root.AuthorizationInfo.TcbCredentials.fromObject(object.tcbCredentials);\n        }\n        if (object.wxParam != null)\n            if (typeof object.wxParam === \"string\")\n                $util.base64.decode(object.wxParam, message.wxParam = $util.newBuffer($util.base64.length(object.wxParam)), 0);\n            else if (object.wxParam.length)\n                message.wxParam = object.wxParam;\n        return message;\n    };\n\n    /**\n     * Creates a plain object from an AuthorizationInfo message. Also converts values to other types if specified.\n     * @function toObject\n     * @memberof AuthorizationInfo\n     * @static\n     * @param {AuthorizationInfo} message AuthorizationInfo\n     * @param {$protobuf.IConversionOptions} [options] Conversion options\n     * @returns {Object.<string,*>} Plain object\n     */\n    AuthorizationInfo.toObject = function toObject(message, options) {\n        if (!options)\n            options = {};\n        var object = {};\n        if (options.defaults) {\n            object.tcbCredentials = null;\n            if (options.bytes === String)\n                object.wxParam = \"\";\n            else {\n                object.wxParam = [];\n                if (options.bytes !== Array)\n                    object.wxParam = $util.newBuffer(object.wxParam);\n            }\n        }\n        if (message.tcbCredentials != null && message.hasOwnProperty(\"tcbCredentials\"))\n            object.tcbCredentials = $root.AuthorizationInfo.TcbCredentials.toObject(message.tcbCredentials, options);\n        if (message.wxParam != null && message.hasOwnProperty(\"wxParam\"))\n            object.wxParam = options.bytes === String ? $util.base64.encode(message.wxParam, 0, message.wxParam.length) : options.bytes === Array ? Array.prototype.slice.call(message.wxParam) : message.wxParam;\n        return object;\n    };\n\n    /**\n     * Converts this AuthorizationInfo to JSON.\n     * @function toJSON\n     * @memberof AuthorizationInfo\n     * @instance\n     * @returns {Object.<string,*>} JSON object\n     */\n    AuthorizationInfo.prototype.toJSON = function toJSON() {\n        return this.constructor.toObject(this, $protobuf.util.toJSONOptions);\n    };\n\n    AuthorizationInfo.TcbCredentials = (function() {\n\n        /**\n         * Properties of a TcbCredentials.\n         * @memberof AuthorizationInfo\n         * @interface ITcbCredentials\n         * @property {string|null} [secretId] TcbCredentials secretId\n         * @property {string|null} [secretKey] TcbCredentials secretKey\n         * @property {string|null} [token] TcbCredentials token\n         */\n\n        /**\n         * Constructs a new TcbCredentials.\n         * @memberof AuthorizationInfo\n         * @classdesc Represents a TcbCredentials.\n         * @implements ITcbCredentials\n         * @constructor\n         * @param {AuthorizationInfo.ITcbCredentials=} [properties] Properties to set\n         */\n        function TcbCredentials(properties) {\n            if (properties)\n                for (var keys = Object.keys(properties), i = 0; i < keys.length; ++i)\n                    if (properties[keys[i]] != null)\n                        this[keys[i]] = properties[keys[i]];\n        }\n\n        /**\n         * TcbCredentials secretId.\n         * @member {string} secretId\n         * @memberof AuthorizationInfo.TcbCredentials\n         * @instance\n         */\n        TcbCredentials.prototype.secretId = \"\";\n\n        /**\n         * TcbCredentials secretKey.\n         * @member {string} secretKey\n         * @memberof AuthorizationInfo.TcbCredentials\n         * @instance\n         */\n        TcbCredentials.prototype.secretKey = \"\";\n\n        /**\n         * TcbCredentials token.\n         * @member {string} token\n         * @memberof AuthorizationInfo.TcbCredentials\n         * @instance\n         */\n        TcbCredentials.prototype.token = \"\";\n\n        /**\n         * Creates a new TcbCredentials instance using the specified properties.\n         * @function create\n         * @memberof AuthorizationInfo.TcbCredentials\n         * @static\n         * @param {AuthorizationInfo.ITcbCredentials=} [properties] Properties to set\n         * @returns {AuthorizationInfo.TcbCredentials} TcbCredentials instance\n         */\n        TcbCredentials.create = function create(properties) {\n            return new TcbCredentials(properties);\n        };\n\n        /**\n         * Encodes the specified TcbCredentials message. Does not implicitly {@link AuthorizationInfo.TcbCredentials.verify|verify} messages.\n         * @function encode\n         * @memberof AuthorizationInfo.TcbCredentials\n         * @static\n         * @param {AuthorizationInfo.ITcbCredentials} message TcbCredentials message or plain object to encode\n         * @param {$protobuf.Writer} [writer] Writer to encode to\n         * @returns {$protobuf.Writer} Writer\n         */\n        TcbCredentials.encode = function encode(message, writer) {\n            if (!writer)\n                writer = $Writer.create();\n            if (message.secretId != null && message.hasOwnProperty(\"secretId\"))\n                writer.uint32(/* id 1, wireType 2 =*/10).string(message.secretId);\n            if (message.secretKey != null && message.hasOwnProperty(\"secretKey\"))\n                writer.uint32(/* id 2, wireType 2 =*/18).string(message.secretKey);\n            if (message.token != null && message.hasOwnProperty(\"token\"))\n                writer.uint32(/* id 3, wireType 2 =*/26).string(message.token);\n            return writer;\n        };\n\n        /**\n         * Encodes the specified TcbCredentials message, length delimited. Does not implicitly {@link AuthorizationInfo.TcbCredentials.verify|verify} messages.\n         * @function encodeDelimited\n         * @memberof AuthorizationInfo.TcbCredentials\n         * @static\n         * @param {AuthorizationInfo.ITcbCredentials} message TcbCredentials message or plain object to encode\n         * @param {$protobuf.Writer} [writer] Writer to encode to\n         * @returns {$protobuf.Writer} Writer\n         */\n        TcbCredentials.encodeDelimited = function encodeDelimited(message, writer) {\n            return this.encode(message, writer).ldelim();\n        };\n\n        /**\n         * Decodes a TcbCredentials message from the specified reader or buffer.\n         * @function decode\n         * @memberof AuthorizationInfo.TcbCredentials\n         * @static\n         * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from\n         * @param {number} [length] Message length if known beforehand\n         * @returns {AuthorizationInfo.TcbCredentials} TcbCredentials\n         * @throws {Error} If the payload is not a reader or valid buffer\n         * @throws {$protobuf.util.ProtocolError} If required fields are missing\n         */\n        TcbCredentials.decode = function decode(reader, length) {\n            if (!(reader instanceof $Reader))\n                reader = $Reader.create(reader);\n            var end = length === undefined ? reader.len : reader.pos + length, message = new $root.AuthorizationInfo.TcbCredentials();\n            while (reader.pos < end) {\n                var tag = reader.uint32();\n                switch (tag >>> 3) {\n                case 1:\n                    message.secretId = reader.string();\n                    break;\n                case 2:\n                    message.secretKey = reader.string();\n                    break;\n                case 3:\n                    message.token = reader.string();\n                    break;\n                default:\n                    reader.skipType(tag & 7);\n                    break;\n                }\n            }\n            return message;\n        };\n\n        /**\n         * Decodes a TcbCredentials message from the specified reader or buffer, length delimited.\n         * @function decodeDelimited\n         * @memberof AuthorizationInfo.TcbCredentials\n         * @static\n         * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from\n         * @returns {AuthorizationInfo.TcbCredentials} TcbCredentials\n         * @throws {Error} If the payload is not a reader or valid buffer\n         * @throws {$protobuf.util.ProtocolError} If required fields are missing\n         */\n        TcbCredentials.decodeDelimited = function decodeDelimited(reader) {\n            if (!(reader instanceof $Reader))\n                reader = new $Reader(reader);\n            return this.decode(reader, reader.uint32());\n        };\n\n        /**\n         * Verifies a TcbCredentials message.\n         * @function verify\n         * @memberof AuthorizationInfo.TcbCredentials\n         * @static\n         * @param {Object.<string,*>} message Plain object to verify\n         * @returns {string|null} `null` if valid, otherwise the reason why it is not\n         */\n        TcbCredentials.verify = function verify(message) {\n            if (typeof message !== \"object\" || message === null)\n                return \"object expected\";\n            if (message.secretId != null && message.hasOwnProperty(\"secretId\"))\n                if (!$util.isString(message.secretId))\n                    return \"secretId: string expected\";\n            if (message.secretKey != null && message.hasOwnProperty(\"secretKey\"))\n                if (!$util.isString(message.secretKey))\n                    return \"secretKey: string expected\";\n            if (message.token != null && message.hasOwnProperty(\"token\"))\n                if (!$util.isString(message.token))\n                    return \"token: string expected\";\n            return null;\n        };\n\n        /**\n         * Creates a TcbCredentials message from a plain object. Also converts values to their respective internal types.\n         * @function fromObject\n         * @memberof AuthorizationInfo.TcbCredentials\n         * @static\n         * @param {Object.<string,*>} object Plain object\n         * @returns {AuthorizationInfo.TcbCredentials} TcbCredentials\n         */\n        TcbCredentials.fromObject = function fromObject(object) {\n            if (object instanceof $root.AuthorizationInfo.TcbCredentials)\n                return object;\n            var message = new $root.AuthorizationInfo.TcbCredentials();\n            if (object.secretId != null)\n                message.secretId = String(object.secretId);\n            if (object.secretKey != null)\n                message.secretKey = String(object.secretKey);\n            if (object.token != null)\n                message.token = String(object.token);\n            return message;\n        };\n\n        /**\n         * Creates a plain object from a TcbCredentials message. Also converts values to other types if specified.\n         * @function toObject\n         * @memberof AuthorizationInfo.TcbCredentials\n         * @static\n         * @param {AuthorizationInfo.TcbCredentials} message TcbCredentials\n         * @param {$protobuf.IConversionOptions} [options] Conversion options\n         * @returns {Object.<string,*>} Plain object\n         */\n        TcbCredentials.toObject = function toObject(message, options) {\n            if (!options)\n                options = {};\n            var object = {};\n            if (options.defaults) {\n                object.secretId = \"\";\n                object.secretKey = \"\";\n                object.token = \"\";\n            }\n            if (message.secretId != null && message.hasOwnProperty(\"secretId\"))\n                object.secretId = message.secretId;\n            if (message.secretKey != null && message.hasOwnProperty(\"secretKey\"))\n                object.secretKey = message.secretKey;\n            if (message.token != null && message.hasOwnProperty(\"token\"))\n                object.token = message.token;\n            return object;\n        };\n\n        /**\n         * Converts this TcbCredentials to JSON.\n         * @function toJSON\n         * @memberof AuthorizationInfo.TcbCredentials\n         * @instance\n         * @returns {Object.<string,*>} JSON object\n         */\n        TcbCredentials.prototype.toJSON = function toJSON() {\n            return this.constructor.toObject(this, $protobuf.util.toJSONOptions);\n        };\n\n        return TcbCredentials;\n    })();\n\n    AuthorizationInfo.WxParam = (function() {\n\n        /**\n         * Properties of a WxParam.\n         * @memberof AuthorizationInfo\n         * @interface IWxParam\n         * @property {Uint8Array|null} [qbaseTicket] WxParam qbaseTicket\n         * @property {string|null} [authUin] WxParam authUin\n         * @property {string|null} [extJson] WxParam extJson\n         */\n\n        /**\n         * Constructs a new WxParam.\n         * @memberof AuthorizationInfo\n         * @classdesc Represents a WxParam.\n         * @implements IWxParam\n         * @constructor\n         * @param {AuthorizationInfo.IWxParam=} [properties] Properties to set\n         */\n        function WxParam(properties) {\n            if (properties)\n                for (var keys = Object.keys(properties), i = 0; i < keys.length; ++i)\n                    if (properties[keys[i]] != null)\n                        this[keys[i]] = properties[keys[i]];\n        }\n\n        /**\n         * WxParam qbaseTicket.\n         * @member {Uint8Array} qbaseTicket\n         * @memberof AuthorizationInfo.WxParam\n         * @instance\n         */\n        WxParam.prototype.qbaseTicket = $util.newBuffer([]);\n\n        /**\n         * WxParam authUin.\n         * @member {string} authUin\n         * @memberof AuthorizationInfo.WxParam\n         * @instance\n         */\n        WxParam.prototype.authUin = \"\";\n\n        /**\n         * WxParam extJson.\n         * @member {string} extJson\n         * @memberof AuthorizationInfo.WxParam\n         * @instance\n         */\n        WxParam.prototype.extJson = \"\";\n\n        /**\n         * Creates a new WxParam instance using the specified properties.\n         * @function create\n         * @memberof AuthorizationInfo.WxParam\n         * @static\n         * @param {AuthorizationInfo.IWxParam=} [properties] Properties to set\n         * @returns {AuthorizationInfo.WxParam} WxParam instance\n         */\n        WxParam.create = function create(properties) {\n            return new WxParam(properties);\n        };\n\n        /**\n         * Encodes the specified WxParam message. Does not implicitly {@link AuthorizationInfo.WxParam.verify|verify} messages.\n         * @function encode\n         * @memberof AuthorizationInfo.WxParam\n         * @static\n         * @param {AuthorizationInfo.IWxParam} message WxParam message or plain object to encode\n         * @param {$protobuf.Writer} [writer] Writer to encode to\n         * @returns {$protobuf.Writer} Writer\n         */\n        WxParam.encode = function encode(message, writer) {\n            if (!writer)\n                writer = $Writer.create();\n            if (message.qbaseTicket != null && message.hasOwnProperty(\"qbaseTicket\"))\n                writer.uint32(/* id 1, wireType 2 =*/10).bytes(message.qbaseTicket);\n            if (message.authUin != null && message.hasOwnProperty(\"authUin\"))\n                writer.uint32(/* id 2, wireType 2 =*/18).string(message.authUin);\n            if (message.extJson != null && message.hasOwnProperty(\"extJson\"))\n                writer.uint32(/* id 3, wireType 2 =*/26).string(message.extJson);\n            return writer;\n        };\n\n        /**\n         * Encodes the specified WxParam message, length delimited. Does not implicitly {@link AuthorizationInfo.WxParam.verify|verify} messages.\n         * @function encodeDelimited\n         * @memberof AuthorizationInfo.WxParam\n         * @static\n         * @param {AuthorizationInfo.IWxParam} message WxParam message or plain object to encode\n         * @param {$protobuf.Writer} [writer] Writer to encode to\n         * @returns {$protobuf.Writer} Writer\n         */\n        WxParam.encodeDelimited = function encodeDelimited(message, writer) {\n            return this.encode(message, writer).ldelim();\n        };\n\n        /**\n         * Decodes a WxParam message from the specified reader or buffer.\n         * @function decode\n         * @memberof AuthorizationInfo.WxParam\n         * @static\n         * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from\n         * @param {number} [length] Message length if known beforehand\n         * @returns {AuthorizationInfo.WxParam} WxParam\n         * @throws {Error} If the payload is not a reader or valid buffer\n         * @throws {$protobuf.util.ProtocolError} If required fields are missing\n         */\n        WxParam.decode = function decode(reader, length) {\n            if (!(reader instanceof $Reader))\n                reader = $Reader.create(reader);\n            var end = length === undefined ? reader.len : reader.pos + length, message = new $root.AuthorizationInfo.WxParam();\n            while (reader.pos < end) {\n                var tag = reader.uint32();\n                switch (tag >>> 3) {\n                case 1:\n                    message.qbaseTicket = reader.bytes();\n                    break;\n                case 2:\n                    message.authUin = reader.string();\n                    break;\n                case 3:\n                    message.extJson = reader.string();\n                    break;\n                default:\n                    reader.skipType(tag & 7);\n                    break;\n                }\n            }\n            return message;\n        };\n\n        /**\n         * Decodes a WxParam message from the specified reader or buffer, length delimited.\n         * @function decodeDelimited\n         * @memberof AuthorizationInfo.WxParam\n         * @static\n         * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from\n         * @returns {AuthorizationInfo.WxParam} WxParam\n         * @throws {Error} If the payload is not a reader or valid buffer\n         * @throws {$protobuf.util.ProtocolError} If required fields are missing\n         */\n        WxParam.decodeDelimited = function decodeDelimited(reader) {\n            if (!(reader instanceof $Reader))\n                reader = new $Reader(reader);\n            return this.decode(reader, reader.uint32());\n        };\n\n        /**\n         * Verifies a WxParam message.\n         * @function verify\n         * @memberof AuthorizationInfo.WxParam\n         * @static\n         * @param {Object.<string,*>} message Plain object to verify\n         * @returns {string|null} `null` if valid, otherwise the reason why it is not\n         */\n        WxParam.verify = function verify(message) {\n            if (typeof message !== \"object\" || message === null)\n                return \"object expected\";\n            if (message.qbaseTicket != null && message.hasOwnProperty(\"qbaseTicket\"))\n                if (!(message.qbaseTicket && typeof message.qbaseTicket.length === \"number\" || $util.isString(message.qbaseTicket)))\n                    return \"qbaseTicket: buffer expected\";\n            if (message.authUin != null && message.hasOwnProperty(\"authUin\"))\n                if (!$util.isString(message.authUin))\n                    return \"authUin: string expected\";\n            if (message.extJson != null && message.hasOwnProperty(\"extJson\"))\n                if (!$util.isString(message.extJson))\n                    return \"extJson: string expected\";\n            return null;\n        };\n\n        /**\n         * Creates a WxParam message from a plain object. Also converts values to their respective internal types.\n         * @function fromObject\n         * @memberof AuthorizationInfo.WxParam\n         * @static\n         * @param {Object.<string,*>} object Plain object\n         * @returns {AuthorizationInfo.WxParam} WxParam\n         */\n        WxParam.fromObject = function fromObject(object) {\n            if (object instanceof $root.AuthorizationInfo.WxParam)\n                return object;\n            var message = new $root.AuthorizationInfo.WxParam();\n            if (object.qbaseTicket != null)\n                if (typeof object.qbaseTicket === \"string\")\n                    $util.base64.decode(object.qbaseTicket, message.qbaseTicket = $util.newBuffer($util.base64.length(object.qbaseTicket)), 0);\n                else if (object.qbaseTicket.length)\n                    message.qbaseTicket = object.qbaseTicket;\n            if (object.authUin != null)\n                message.authUin = String(object.authUin);\n            if (object.extJson != null)\n                message.extJson = String(object.extJson);\n            return message;\n        };\n\n        /**\n         * Creates a plain object from a WxParam message. Also converts values to other types if specified.\n         * @function toObject\n         * @memberof AuthorizationInfo.WxParam\n         * @static\n         * @param {AuthorizationInfo.WxParam} message WxParam\n         * @param {$protobuf.IConversionOptions} [options] Conversion options\n         * @returns {Object.<string,*>} Plain object\n         */\n        WxParam.toObject = function toObject(message, options) {\n            if (!options)\n                options = {};\n            var object = {};\n            if (options.defaults) {\n                if (options.bytes === String)\n                    object.qbaseTicket = \"\";\n                else {\n                    object.qbaseTicket = [];\n                    if (options.bytes !== Array)\n                        object.qbaseTicket = $util.newBuffer(object.qbaseTicket);\n                }\n                object.authUin = \"\";\n                object.extJson = \"\";\n            }\n            if (message.qbaseTicket != null && message.hasOwnProperty(\"qbaseTicket\"))\n                object.qbaseTicket = options.bytes === String ? $util.base64.encode(message.qbaseTicket, 0, message.qbaseTicket.length) : options.bytes === Array ? Array.prototype.slice.call(message.qbaseTicket) : message.qbaseTicket;\n            if (message.authUin != null && message.hasOwnProperty(\"authUin\"))\n                object.authUin = message.authUin;\n            if (message.extJson != null && message.hasOwnProperty(\"extJson\"))\n                object.extJson = message.extJson;\n            return object;\n        };\n\n        /**\n         * Converts this WxParam to JSON.\n         * @function toJSON\n         * @memberof AuthorizationInfo.WxParam\n         * @instance\n         * @returns {Object.<string,*>} JSON object\n         */\n        WxParam.prototype.toJSON = function toJSON() {\n            return this.constructor.toObject(this, $protobuf.util.toJSONOptions);\n        };\n\n        return WxParam;\n    })();\n\n    return AuthorizationInfo;\n})();\n\n$root.ApiOptions = (function() {\n\n    /**\n     * Properties of an ApiOptions.\n     * @exports IApiOptions\n     * @interface IApiOptions\n     * @property {string|null} [appid] ApiOptions appid\n     */\n\n    /**\n     * Constructs a new ApiOptions.\n     * @exports ApiOptions\n     * @classdesc Represents an ApiOptions.\n     * @implements IApiOptions\n     * @constructor\n     * @param {IApiOptions=} [properties] Properties to set\n     */\n    function ApiOptions(properties) {\n        if (properties)\n            for (var keys = Object.keys(properties), i = 0; i < keys.length; ++i)\n                if (properties[keys[i]] != null)\n                    this[keys[i]] = properties[keys[i]];\n    }\n\n    /**\n     * ApiOptions appid.\n     * @member {string} appid\n     * @memberof ApiOptions\n     * @instance\n     */\n    ApiOptions.prototype.appid = \"\";\n\n    /**\n     * Creates a new ApiOptions instance using the specified properties.\n     * @function create\n     * @memberof ApiOptions\n     * @static\n     * @param {IApiOptions=} [properties] Properties to set\n     * @returns {ApiOptions} ApiOptions instance\n     */\n    ApiOptions.create = function create(properties) {\n        return new ApiOptions(properties);\n    };\n\n    /**\n     * Encodes the specified ApiOptions message. Does not implicitly {@link ApiOptions.verify|verify} messages.\n     * @function encode\n     * @memberof ApiOptions\n     * @static\n     * @param {IApiOptions} message ApiOptions message or plain object to encode\n     * @param {$protobuf.Writer} [writer] Writer to encode to\n     * @returns {$protobuf.Writer} Writer\n     */\n    ApiOptions.encode = function encode(message, writer) {\n        if (!writer)\n            writer = $Writer.create();\n        if (message.appid != null && message.hasOwnProperty(\"appid\"))\n            writer.uint32(/* id 1, wireType 2 =*/10).string(message.appid);\n        return writer;\n    };\n\n    /**\n     * Encodes the specified ApiOptions message, length delimited. Does not implicitly {@link ApiOptions.verify|verify} messages.\n     * @function encodeDelimited\n     * @memberof ApiOptions\n     * @static\n     * @param {IApiOptions} message ApiOptions message or plain object to encode\n     * @param {$protobuf.Writer} [writer] Writer to encode to\n     * @returns {$protobuf.Writer} Writer\n     */\n    ApiOptions.encodeDelimited = function encodeDelimited(message, writer) {\n        return this.encode(message, writer).ldelim();\n    };\n\n    /**\n     * Decodes an ApiOptions message from the specified reader or buffer.\n     * @function decode\n     * @memberof ApiOptions\n     * @static\n     * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from\n     * @param {number} [length] Message length if known beforehand\n     * @returns {ApiOptions} ApiOptions\n     * @throws {Error} If the payload is not a reader or valid buffer\n     * @throws {$protobuf.util.ProtocolError} If required fields are missing\n     */\n    ApiOptions.decode = function decode(reader, length) {\n        if (!(reader instanceof $Reader))\n            reader = $Reader.create(reader);\n        var end = length === undefined ? reader.len : reader.pos + length, message = new $root.ApiOptions();\n        while (reader.pos < end) {\n            var tag = reader.uint32();\n            switch (tag >>> 3) {\n            case 1:\n                message.appid = reader.string();\n                break;\n            default:\n                reader.skipType(tag & 7);\n                break;\n            }\n        }\n        return message;\n    };\n\n    /**\n     * Decodes an ApiOptions message from the specified reader or buffer, length delimited.\n     * @function decodeDelimited\n     * @memberof ApiOptions\n     * @static\n     * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from\n     * @returns {ApiOptions} ApiOptions\n     * @throws {Error} If the payload is not a reader or valid buffer\n     * @throws {$protobuf.util.ProtocolError} If required fields are missing\n     */\n    ApiOptions.decodeDelimited = function decodeDelimited(reader) {\n        if (!(reader instanceof $Reader))\n            reader = new $Reader(reader);\n        return this.decode(reader, reader.uint32());\n    };\n\n    /**\n     * Verifies an ApiOptions message.\n     * @function verify\n     * @memberof ApiOptions\n     * @static\n     * @param {Object.<string,*>} message Plain object to verify\n     * @returns {string|null} `null` if valid, otherwise the reason why it is not\n     */\n    ApiOptions.verify = function verify(message) {\n        if (typeof message !== \"object\" || message === null)\n            return \"object expected\";\n        if (message.appid != null && message.hasOwnProperty(\"appid\"))\n            if (!$util.isString(message.appid))\n                return \"appid: string expected\";\n        return null;\n    };\n\n    /**\n     * Creates an ApiOptions message from a plain object. Also converts values to their respective internal types.\n     * @function fromObject\n     * @memberof ApiOptions\n     * @static\n     * @param {Object.<string,*>} object Plain object\n     * @returns {ApiOptions} ApiOptions\n     */\n    ApiOptions.fromObject = function fromObject(object) {\n        if (object instanceof $root.ApiOptions)\n            return object;\n        var message = new $root.ApiOptions();\n        if (object.appid != null)\n            message.appid = String(object.appid);\n        return message;\n    };\n\n    /**\n     * Creates a plain object from an ApiOptions message. Also converts values to other types if specified.\n     * @function toObject\n     * @memberof ApiOptions\n     * @static\n     * @param {ApiOptions} message ApiOptions\n     * @param {$protobuf.IConversionOptions} [options] Conversion options\n     * @returns {Object.<string,*>} Plain object\n     */\n    ApiOptions.toObject = function toObject(message, options) {\n        if (!options)\n            options = {};\n        var object = {};\n        if (options.defaults)\n            object.appid = \"\";\n        if (message.appid != null && message.hasOwnProperty(\"appid\"))\n            object.appid = message.appid;\n        return object;\n    };\n\n    /**\n     * Converts this ApiOptions to JSON.\n     * @function toJSON\n     * @memberof ApiOptions\n     * @instance\n     * @returns {Object.<string,*>} JSON object\n     */\n    ApiOptions.prototype.toJSON = function toJSON() {\n        return this.constructor.toObject(this, $protobuf.util.toJSONOptions);\n    };\n\n    return ApiOptions;\n})();\n\nmodule.exports = $root;\n\n\n/***/ }),\n\n/***/ \"./src/utils/assert.ts\":\n/*!*****************************!*\\\n  !*** ./src/utils/assert.ts ***!\n  \\*****************************/\n/*! no static exports found */\n/***/ (function(module, exports, __webpack_require__) {\n\n\n\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.assertObjectNotEmpty = exports.assertRequiredParam = exports.assertObjectOptionalType = exports.assertType = exports.validObjectOptionalType = exports.validType = exports.sameType = void 0;\nconst type_1 = __webpack_require__(/*! ./type */ \"./src/utils/type.ts\");\nconst error_1 = __webpack_require__(/*! ./error */ \"./src/utils/error.ts\");\nconst error_config_1 = __webpack_require__(/*! config/error.config */ \"./src/config/error.config.ts\");\nfunction sameType(input, ref, name) {\n    function sameTypeImpl(input, ref, name) {\n        const inputType = type_1.getType(input);\n        const refType = type_1.getType(ref);\n        if (inputType !== refType) {\n            return `${name} should be ${refType} instead of ${inputType}; `;\n        }\n        let errors = '';\n        switch (inputType) {\n            case 'object': {\n                for (const key in ref) {\n                    errors += sameTypeImpl(input[key], ref[key], `${name}.${key}`);\n                }\n                break;\n            }\n            case 'array': {\n                for (let i = 0; i < ref.length; i++) {\n                    errors += sameTypeImpl(input[i], ref[i], `${name}[${i}]`);\n                }\n                break;\n            }\n            default: {\n                break;\n            }\n        }\n        return errors;\n    }\n    const error = sameTypeImpl(input, ref, name);\n    return {\n        passed: !error,\n        reason: error,\n    };\n}\nexports.sameType = sameType;\nfunction validType(input, ref, name = 'parameter') {\n    function validTypeImpl(input, ref, name) {\n        const inputType = type_1.getType(input);\n        const refType = type_1.getType(ref);\n        if (refType === 'string') {\n            if (inputType !== ref) {\n                return `${name} should be ${ref} instead of ${inputType};`;\n            }\n            return '';\n        }\n        else {\n            if (inputType !== refType) {\n                return `${name} should be ${refType} instead of ${inputType}; `;\n            }\n            let errors = '';\n            switch (inputType) {\n                case 'object': {\n                    for (const key in ref) {\n                        errors += validTypeImpl(input[key], ref[key], `${name}.${key}`);\n                    }\n                    break;\n                }\n                case 'array': {\n                    for (let i = 0; i < ref.length; i++) {\n                        errors += validTypeImpl(input[i], ref[i], `${name}[${i}]`);\n                    }\n                    break;\n                }\n                default: {\n                    break;\n                }\n            }\n            return errors;\n        }\n    }\n    const error = validTypeImpl(input, ref, name);\n    return {\n        passed: !error,\n        reason: error,\n    };\n}\nexports.validType = validType;\nfunction validObjectOptionalType(input, ref, name = 'parameter') {\n    function validImpl(input, ref, name) {\n        const inputType = type_1.getType(input);\n        const refType = type_1.getType(ref);\n        if (refType !== 'object')\n            return '';\n        if (inputType === 'object') {\n            for (const key in input) {\n                const val = input[key];\n                if (val === undefined || key === null) {\n                    continue;\n                }\n                const assertResult = validType(val, ref[key], `${name}.${key}`);\n                return assertResult.passed ? '' : assertResult.reason;\n            }\n        }\n        else {\n            return `${name} should be object instead of ${inputType}`;\n        }\n        return '';\n    }\n    const error = validImpl(input, ref, name);\n    return {\n        passed: !error,\n        reason: error,\n    };\n}\nexports.validObjectOptionalType = validObjectOptionalType;\nfunction assertType(param, ref, name = 'parameter', ErrorClass = error_1.CloudSDKError) {\n    // check param validity\n    let paramCheckResult = validType(param, ref, name);\n    if (!paramCheckResult.passed) {\n        throw new ErrorClass({\n            errMsg: paramCheckResult.reason,\n        });\n    }\n}\nexports.assertType = assertType;\nfunction assertObjectOptionalType(param, ref, name = 'parameter', ErrorClass = error_1.CloudSDKError) {\n    // check param validity\n    let paramCheckResult = validObjectOptionalType(param, ref, name);\n    if (!paramCheckResult.passed) {\n        throw new ErrorClass({\n            errMsg: paramCheckResult.reason,\n        });\n    }\n}\nexports.assertObjectOptionalType = assertObjectOptionalType;\nfunction assertRequiredParam(param, name, funcName, ErrorClass = error_1.CloudSDKError) {\n    if (param === undefined || param === null) {\n        throw new ErrorClass({\n            errMsg: `parameter ${name} of function ${funcName} must be provided`,\n        });\n    }\n}\nexports.assertRequiredParam = assertRequiredParam;\nfunction assertObjectNotEmpty({ target, name, ErrorClass = error_1.CloudSDKError }) {\n    if (Object.keys(target).length === 0) {\n        throw new ErrorClass({\n            errCode: error_config_1.ERR_CODE.SDK_API_PARAMETER_ERROR,\n            errMsg: `${name} must not be empty`\n        });\n    }\n}\nexports.assertObjectNotEmpty = assertObjectNotEmpty;\n/*\nexport function constructTypeRef(typeDef: any): any {\n\n  const type = getType(typeDef)\n\n  switch(type) {\n    case 'string': {\n      return ''\n    }\n    case 'number': {\n\n    }\n  }\n\n}\n*/ \n\n\n/***/ }),\n\n/***/ \"./src/utils/cross-account-token.ts\":\n/*!******************************************!*\\\n  !*** ./src/utils/cross-account-token.ts ***!\n  \\******************************************/\n/*! no static exports found */\n/***/ (function(module, exports, __webpack_require__) {\n\n\n\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.getBoundGetCrossAccountToken = void 0;\nconst openapi_1 = __webpack_require__(/*! ../protobuf/openapi */ \"./src/protobuf/openapi.js\");\nconst error_config_1 = __webpack_require__(/*! config/error.config */ \"./src/config/error.config.ts\");\nfunction getBoundGetCrossAccountToken(cloud) {\n    return async function getCrossAccountToken(options) {\n        try {\n            const pbMessage = openapi_1.CommApiData.encode({\n                apiType: openapi_1.CommApiData.ApiType.TOKEN_API,\n                tokenData: {\n                    resourceAppid: options.resourceAppid,\n                    resourceEnv: options.resourceEnv,\n                },\n            }).finish();\n            const wxResp = await cloud.provider.api.callWXOpenAPI({\n                api: 'getCrossAccountToken',\n                data: Buffer.from(pbMessage),\n            }, {\n                instance: cloud.instance,\n            });\n            if (!wxResp.respData) {\n                throw {\n                    errCode: error_config_1.ERR_CODE.WX_SYSTEM_ERROR,\n                    errMsg: `internal svrkit error, empty respData`,\n                };\n            }\n            const pbRespMsg = openapi_1.AuthorizationInfo.decode(wxResp.respData);\n            return {\n                credential: pbRespMsg.tcbCredentials,\n                authorization: {\n                    mpToken: Buffer.from(pbRespMsg.wxParam).toString('base64'),\n                },\n            };\n        }\n        catch (e) {\n            throw e;\n        }\n    };\n}\nexports.getBoundGetCrossAccountToken = getBoundGetCrossAccountToken;\n\n\n/***/ }),\n\n/***/ \"./src/utils/error.ts\":\n/*!****************************!*\\\n  !*** ./src/utils/error.ts ***!\n  \\****************************/\n/*! no static exports found */\n/***/ (function(module, exports, __webpack_require__) {\n\n\n\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.toSDKError = exports.returnAsFinalCloudSDKError = exports.returnAsCloudSDKError = exports.isSDKError = exports.createError = exports.CloudSDKError = void 0;\nconst type_1 = __webpack_require__(/*! ./type */ \"./src/utils/type.ts\");\nconst error_config_1 = __webpack_require__(/*! config/error.config */ \"./src/config/error.config.ts\");\n/**\n * @deprecated\n */\nclass CloudSDKError extends Error {\n    constructor(options) {\n        super(options.errMsg);\n        this.errCode = -1;\n        Object.defineProperties(this, {\n            message: {\n                get() {\n                    return `errCode: ${this.errCode} ${error_config_1.ERR_CODE[this.errCode] || ''} | errMsg: ` + this.errMsg;\n                },\n                set(msg) {\n                    this.errMsg = msg;\n                }\n            }\n        });\n        this.errCode = options.errCode || -1;\n        this.errMsg = options.errMsg;\n    }\n    get message() {\n        return `errCode: ${this.errCode} | errMsg: ` + this.errMsg;\n    }\n    set message(msg) {\n        this.errMsg = msg;\n    }\n}\nexports.CloudSDKError = CloudSDKError;\n/**\n * @deprecated\n */\nfunction createError({ errCode = 1, errMsg = '', errClass = CloudSDKError, } = {}) {\n    return new errClass({\n        errCode,\n        errMsg,\n    });\n}\nexports.createError = createError;\nfunction isSDKError(error) {\n    return error && (error instanceof Error) && type_1.isString(error.errMsg);\n}\nexports.isSDKError = isSDKError;\n/**\n * @deprecated\n */\nfunction returnAsCloudSDKError(err, appendMsg = '') {\n    if (err) {\n        if (isSDKError(err)) {\n            if (appendMsg) {\n                err.errMsg += '; ' + appendMsg;\n            }\n            return err;\n        }\n        const errCode = err ? err.errCode : undefined;\n        const errMsg = (err && err.errMsg || err.toString() || 'unknown error') + '; ' + appendMsg;\n        return new CloudSDKError({\n            errCode,\n            errMsg,\n        });\n    }\n    return new CloudSDKError({\n        errMsg: appendMsg\n    });\n}\nexports.returnAsCloudSDKError = returnAsCloudSDKError;\n/**\n * @deprecated\n */\nfunction returnAsFinalCloudSDKError(err, apiName) {\n    return toSDKError(err, apiName);\n    // if (err && isSDKError(err)) {\n    //   return err\n    // }\n    // const e = returnAsCloudSDKError(err, `at ${apiName} api; `)\n    // e.errMsg = apiFailMsg(apiName, e.errMsg)\n    // return e\n}\nexports.returnAsFinalCloudSDKError = returnAsFinalCloudSDKError;\nfunction toSDKError(e, apiName) {\n    if (e) {\n        if (isSDKError(e)) {\n            return e;\n        }\n        const prefix = `${apiName}:fail `;\n        let err;\n        if (e instanceof Error) {\n            e.message = `${prefix}${e.message}`;\n            e.stack = e.stack.slice(0, 7) + prefix + e.stack.slice(7);\n            err = e;\n            err.errCode = -1;\n        }\n        else if (typeof e === 'string') {\n            err = new Error(`${prefix}${e}`);\n            err.errCode = -1;\n        }\n        else {\n            // errCode + errMsg\n            const errMsg = e.errMsg || '';\n            err = new Error(`${apiName}:fail ${e.errCode} ${error_config_1.ERR_CODE[e.errCode] || ''}. ${errMsg}`);\n            err.errCode = e.errCode || -1;\n        }\n        err.errMsg = err.message + '';\n        return err;\n    }\n    const err = new Error(`${apiName}:fail`);\n    err.errCode = -1;\n    err.errMsg = err.message + '';\n    return err;\n}\nexports.toSDKError = toSDKError;\n\n\n/***/ }),\n\n/***/ \"./src/utils/generic-fn.ts\":\n/*!*********************************!*\\\n  !*** ./src/utils/generic-fn.ts ***!\n  \\*********************************/\n/*! no static exports found */\n/***/ (function(module, exports, __webpack_require__) {\n\n\n\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.getProxyObject = exports.getCallableObject = exports.functionIntrinsicProperties = void 0;\nconst type_1 = __webpack_require__(/*! ./type */ \"./src/utils/type.ts\");\nexports.functionIntrinsicProperties = new Set(Object.getOwnPropertyNames(Function.prototype));\nexports.getCallableObject = (options) => {\n    const f = function () { };\n    return new Proxy(f, {\n        get(target, prop) {\n            if (prop === 'toJSON') {\n                return {};\n            }\n            if (exports.functionIntrinsicProperties.has(prop)) {\n                // @ts-ignore\n                return options.callable[prop];\n            }\n            else {\n                return exports.getCallableObject(Object.assign(Object.assign({}, options), { paths: [...options.paths, prop] }));\n            }\n        },\n        apply(target, thisArg, args) {\n            return options.callable.call(thisArg, options, ...args);\n        },\n    });\n};\nexports.getProxyObject = (options) => {\n    const f = () => { };\n    const proxy = new Proxy(f, {\n        get(target, prop) {\n            if (prop === 'toJSON') {\n                return {};\n            }\n            return exports.getCallableObject(Object.assign(Object.assign({}, options), { paths: [prop] }));\n        },\n        apply(target, thisArg, args) {\n            // set options\n            if (!args[0] || !type_1.isObject(args[0])) {\n                throw new Error('an options object is expected');\n            }\n            return exports.getProxyObject(Object.assign(Object.assign({}, args[0]), { \n                // some options are not writable\n                callable: options.callable, paths: options.paths }));\n        }\n    });\n    return proxy;\n    const o = {};\n    return new Proxy(o, {\n        get(target, prop) {\n            if (prop === 'toJSON') {\n                return {};\n            }\n            return exports.getCallableObject(Object.assign(Object.assign({}, options), { paths: [prop] }));\n        }\n    });\n};\n\n\n/***/ }),\n\n/***/ \"./src/utils/mimetype.ts\":\n/*!*******************************!*\\\n  !*** ./src/utils/mimetype.ts ***!\n  \\*******************************/\n/*! no static exports found */\n/***/ (function(module, exports, __webpack_require__) {\n\n\n\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.mimeTypeToFileExtension = void 0;\nconst mimeDB = __webpack_require__(/*! mime-db */ \"mime-db\");\nexports.mimeTypeToFileExtension = (mimeType, defaultExtension) => {\n    const mime = mimeDB[mimeType];\n    if (mime && mime.extensions && mime.extensions.length) {\n        return mime.extensions[0];\n    }\n    else {\n        return defaultExtension;\n    }\n};\n\n\n/***/ }),\n\n/***/ \"./src/utils/msg.ts\":\n/*!**************************!*\\\n  !*** ./src/utils/msg.ts ***!\n  \\**************************/\n/*! no static exports found */\n/***/ (function(module, exports, __webpack_require__) {\n\n\n\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.apiFailMsg = exports.apiCancelMsg = exports.apiSuccessMsg = void 0;\nfunction apiSuccessMsg(apiName) {\n    return `${apiName}:ok`;\n}\nexports.apiSuccessMsg = apiSuccessMsg;\nfunction apiCancelMsg(apiName, msg) {\n    return `${apiName}:cancel ${msg}`;\n}\nexports.apiCancelMsg = apiCancelMsg;\nfunction apiFailMsg(apiName, msg) {\n    return `${apiName}:fail ${msg}`;\n}\nexports.apiFailMsg = apiFailMsg;\n\n\n/***/ }),\n\n/***/ \"./src/utils/symbol.ts\":\n/*!*****************************!*\\\n  !*** ./src/utils/symbol.ts ***!\n  \\*****************************/\n/*! no static exports found */\n/***/ (function(module, exports, __webpack_require__) {\n\n\n\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.InternalSymbol = void 0;\nconst _symbols = [];\nconst __internalMark__ = {};\nclass HiddenSymbol {\n    constructor(target) {\n        Object.defineProperties(this, {\n            target: {\n                enumerable: false,\n                writable: false,\n                configurable: false,\n                value: target,\n            },\n        });\n    }\n}\nclass InternalSymbol extends HiddenSymbol {\n    constructor(target, __mark__) {\n        if (__mark__ !== __internalMark__) {\n            throw new TypeError('InternalSymbol cannot be constructed with new operator');\n        }\n        super(target);\n    }\n    static for(target) {\n        for (let i = 0, len = _symbols.length; i < len; i++) {\n            if (_symbols[i].target === target) {\n                return _symbols[i].instance;\n            }\n        }\n        const symbol = new InternalSymbol(target, __internalMark__);\n        _symbols.push({\n            target,\n            instance: symbol,\n        });\n        return symbol;\n    }\n}\nexports.InternalSymbol = InternalSymbol;\nexports.default = InternalSymbol;\n\n\n/***/ }),\n\n/***/ \"./src/utils/type.ts\":\n/*!***************************!*\\\n  !*** ./src/utils/type.ts ***!\n  \\***************************/\n/*! no static exports found */\n/***/ (function(module, exports, __webpack_require__) {\n\n\n\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.isPlainObject = exports.isInternalObject = exports.isBuffer = exports.isDate = exports.isArray = exports.isFunction = exports.isPromise = exports.isNumber = exports.isString = exports.isObject = exports.getType = void 0;\nconst symbol_1 = __webpack_require__(/*! ./symbol */ \"./src/utils/symbol.ts\");\nexports.getType = (x) => Object.prototype.toString.call(x).slice(8, -1).toLowerCase();\nexports.isObject = (x) => exports.getType(x) === 'object';\nexports.isString = (x) => exports.getType(x) === 'string';\nexports.isNumber = (x) => exports.getType(x) === 'number';\nexports.isPromise = (x) => exports.getType(x) === 'promise';\nexports.isFunction = (x) => typeof x === 'function';\nexports.isArray = (x) => Array.isArray(x);\nexports.isDate = (x) => exports.getType(x) === 'date';\nexports.isBuffer = (x) => Buffer.isBuffer(x);\nexports.isInternalObject = (x) => x && (x._internalType instanceof symbol_1.InternalSymbol);\nexports.isPlainObject = (obj) => {\n    if (typeof obj !== 'object' || obj === null)\n        return false;\n    let proto = obj;\n    while (Object.getPrototypeOf(proto) !== null) {\n        proto = Object.getPrototypeOf(proto);\n    }\n    return Object.getPrototypeOf(obj) === proto;\n};\n\n\n/***/ }),\n\n/***/ \"./src/utils/utils.ts\":\n/*!****************************!*\\\n  !*** ./src/utils/utils.ts ***!\n  \\****************************/\n/*! no static exports found */\n/***/ (function(module, exports, __webpack_require__) {\n\n\n\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.getMergedAPIConfig = exports.getServiceConfigFromDefaultConfig = exports.getEnvFromAPIConfig = exports.getEnvFromCloudConfig = exports.isSCFEnvReady = exports.convertCase = void 0;\nconst type_1 = __webpack_require__(/*! ./type */ \"./src/utils/type.ts\");\nconst signature_1 = __webpack_require__(/*! api/utils/api/signature */ \"./src/api/utils/api/signature.ts\");\nconst ignoreInConvert = (input) => input instanceof signature_1.MidasSignature;\nexports.convertCase = (input, options) => {\n    const { from, to, recursive } = options;\n    if (type_1.isString(input)) {\n        if (from === 'camelcase' && to === 'snakecase') {\n            return input.replace(/[A-Z]/g, (match, ind) => `${ind ? '_' : ''}${match.toLowerCase()}`);\n        }\n        else if (from === 'snakecase' && to === 'camelcase') {\n            return input.replace(/_[a-z]/g, (match, ind) => `${match[1].toUpperCase()}`);\n        }\n    }\n    else if (type_1.isObject(input)) {\n        return ignoreInConvert(input) ? input : convertObject(input);\n    }\n    else if (type_1.isArray(input)) {\n        const array = [];\n        for (const item of input) {\n            if (type_1.isObject(item)) {\n                array.push(convertObject(item));\n            }\n            else if (type_1.isArray(item)) {\n                if (options.recursive) {\n                    array.push(exports.convertCase(item, options));\n                }\n                else {\n                    array.push(item);\n                }\n            }\n            else {\n                array.push(item);\n            }\n        }\n        return array;\n    }\n    else\n        return input;\n    function convertObject(input) {\n        const data = Object.assign({}, input);\n        for (const key in data) {\n            const val = recursive && (type_1.isObject(data[key]) || type_1.isArray(data[key])) ? exports.convertCase(data[key], options) : data[key];\n            const convertedKey = exports.convertCase(key, options);\n            data[convertedKey] = val;\n            if (convertedKey !== key) {\n                delete data[key];\n            }\n        }\n        return data;\n    }\n};\nexports.isSCFEnvReady = () => Boolean(process.env.TCB_ENV);\nexports.getEnvFromCloudConfig = (config, serviceName = 'default') => {\n    const env = config.env[serviceName] || config.env.default;\n    return env;\n};\nexports.getEnvFromAPIConfig = (apiConfig, cloudConfig, serviceName = 'default') => {\n    if (apiConfig && apiConfig.env) {\n        return apiConfig.env;\n    }\n    return exports.getEnvFromCloudConfig(cloudConfig, serviceName);\n};\nexports.getServiceConfigFromDefaultConfig = (defaultConfig, serviceName = 'default') => {\n    return Object.assign(Object.assign({}, defaultConfig), { env: exports.getEnvFromCloudConfig(defaultConfig, serviceName) });\n};\nexports.getMergedAPIConfig = (defaultConfig, newConfig, serviceName = 'default') => {\n    const merged = Object.assign(Object.assign({}, defaultConfig), newConfig);\n    if (newConfig && newConfig.env) {\n        merged.env = newConfig.env;\n    }\n    else {\n        merged.env = exports.getEnvFromCloudConfig(defaultConfig, serviceName);\n    }\n    return merged;\n};\n\n\n/***/ }),\n\n/***/ \"@cloudbase/node-sdk\":\n/*!**************************************!*\\\n  !*** external \"@cloudbase/node-sdk\" ***!\n  \\**************************************/\n/*! no static exports found */\n/***/ (function(module, exports) {\n\nmodule.exports = require(\"@cloudbase/node-sdk\");\n\n/***/ }),\n\n/***/ \"crypto\":\n/*!*************************!*\\\n  !*** external \"crypto\" ***!\n  \\*************************/\n/*! no static exports found */\n/***/ (function(module, exports) {\n\nmodule.exports = require(\"crypto\");\n\n/***/ }),\n\n/***/ \"json-bigint\":\n/*!******************************!*\\\n  !*** external \"json-bigint\" ***!\n  \\******************************/\n/*! no static exports found */\n/***/ (function(module, exports) {\n\nmodule.exports = require(\"json-bigint\");\n\n/***/ }),\n\n/***/ \"mime-db\":\n/*!**************************!*\\\n  !*** external \"mime-db\" ***!\n  \\**************************/\n/*! no static exports found */\n/***/ (function(module, exports) {\n\nmodule.exports = require(\"mime-db\");\n\n/***/ }),\n\n/***/ \"protobufjs/minimal\":\n/*!*************************************!*\\\n  !*** external \"protobufjs/minimal\" ***!\n  \\*************************************/\n/*! no static exports found */\n/***/ (function(module, exports) {\n\nmodule.exports = require(\"protobufjs/minimal\");\n\n/***/ })\n\n/******/ });", "module.exports = {\n  \"name\": \"wx-server-sdk\",\n  \"version\": \"2.6.3\",\n  \"description\": \"mini program cloud server sdk\",\n  \"homepage\": \"https://developers.weixin.qq.com/miniprogram/dev/wxcloud/basis/getting-started.html\",\n  \"main\": \"index.js\",\n  \"author\": \"wechat mini program team\",\n  \"license\": \"MIT\",\n  \"types\": \"index.d.ts\",\n  \"dependencies\": {\n    \"@cloudbase/node-sdk\": \"2.9.1\",\n    \"json-bigint\": \"^1.0.0\",\n    \"protobufjs\": \"^6.8.8\",\n    \"tslib\": \"^1.9.3\",\n    \"tcb-admin-node\": \"latest\"\n  }\n}"]}