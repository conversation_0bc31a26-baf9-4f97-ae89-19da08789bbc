{"version": 3, "sources": ["index.js"], "names": [], "mappings": ";;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.js", "sourcesContent": ["/*\n * Copyright 2016, Joyent, Inc. All rights reserved.\n * Author: <PERSON> <<EMAIL>>\n *\n * Permission is hereby granted, free of charge, to any person obtaining a copy\n * of this software and associated documentation files (the \"Software\"), to\n * deal in the Software without restriction, including without limitation the\n * rights to use, copy, modify, merge, publish, distribute, sublicense, and/or\n * sell copies of the Software, and to permit persons to whom the Software is\n * furnished to do so, subject to the following conditions:\n *\n * The above copyright notice and this permission notice shall be included in\n * all copies or substantial portions of the Software.\n *\n * THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\n * AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n * LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING\n * FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS\n * IN THE SOFTWARE.\n*/\n\nmodule.exports = {\n\tgetPass: getPass\n};\n\nconst mod_tty = require('tty');\nconst mod_fs = require('fs');\nconst mod_assert = require('assert-plus');\n\nvar BACKSPACE = String.fromCharCode(127);\nvar CTRLC = '\\u0003';\nvar CTRLD = '\\u0004';\n\nfunction getPass(opts, cb) {\n\tif (typeof (opts) === 'function' && cb === undefined) {\n\t\tcb = opts;\n\t\topts = {};\n\t}\n\tmod_assert.object(opts, 'options');\n\tmod_assert.func(cb, 'callback');\n\n\tmod_assert.optionalString(opts.prompt, 'options.prompt');\n\tif (opts.prompt === undefined)\n\t\topts.prompt = 'Password';\n\n\topenTTY(function (err, rfd, wfd, rtty, wtty) {\n\t\tif (err) {\n\t\t\tcb(err);\n\t\t\treturn;\n\t\t}\n\n\t\twtty.write(opts.prompt + ':');\n\t\trtty.resume();\n\t\trtty.setRawMode(true);\n\t\trtty.resume();\n\t\trtty.setEncoding('utf8');\n\n\t\tvar pw = '';\n\t\trtty.on('data', onData);\n\n\t\tfunction onData(data) {\n\t\t\tvar str = data.toString('utf8');\n\t\t\tfor (var i = 0; i < str.length; ++i) {\n\t\t\t\tvar ch = str[i];\n\t\t\t\tswitch (ch) {\n\t\t\t\tcase '\\r':\n\t\t\t\tcase '\\n':\n\t\t\t\tcase CTRLD:\n\t\t\t\t\tcleanup();\n\t\t\t\t\tcb(null, pw);\n\t\t\t\t\treturn;\n\t\t\t\tcase CTRLC:\n\t\t\t\t\tcleanup();\n\t\t\t\t\tcb(new Error('Aborted'));\n\t\t\t\t\treturn;\n\t\t\t\tcase BACKSPACE:\n\t\t\t\t\tpw = pw.slice(0, pw.length - 1);\n\t\t\t\t\tbreak;\n\t\t\t\tdefault:\n\t\t\t\t\tpw += ch;\n\t\t\t\t\tbreak;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\tfunction cleanup() {\n\t\t\twtty.write('\\r\\n');\n\t\t\trtty.setRawMode(false);\n\t\t\trtty.pause();\n\t\t\trtty.removeListener('data', onData);\n\t\t\tif (wfd !== undefined && wfd !== rfd) {\n\t\t\t\twtty.end();\n\t\t\t\tmod_fs.closeSync(wfd);\n\t\t\t}\n\t\t\tif (rfd !== undefined) {\n\t\t\t\trtty.end();\n\t\t\t\tmod_fs.closeSync(rfd);\n\t\t\t}\n\t\t}\n\t});\n}\n\nfunction openTTY(cb) {\n\tmod_fs.open('/dev/tty', 'r+', function (err, rttyfd) {\n\t\tif ((err && (err.code === 'ENOENT' || err.code === 'EACCES')) ||\n\t\t    (process.version.match(/^v0[.][0-8][.]/))) {\n\t\t\tcb(null, undefined, undefined, process.stdin,\n\t\t\t    process.stdout);\n\t\t\treturn;\n\t\t}\n\t\tvar rtty = new mod_tty.ReadStream(rttyfd);\n\t\tmod_fs.open('/dev/tty', 'w+', function (err3, wttyfd) {\n\t\t\tvar wtty = new mod_tty.WriteStream(wttyfd);\n\t\t\tif (err3) {\n\t\t\t\tcb(err3);\n\t\t\t\treturn;\n\t\t\t}\n\t\t\tcb(null, rttyfd, wttyfd, rtty, wtty);\n\t\t});\n\t});\n}\n"]}