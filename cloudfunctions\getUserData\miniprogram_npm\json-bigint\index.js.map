{"version": 3, "sources": ["index.js", "lib/stringify.js", "lib/parse.js"], "names": [], "mappings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file": "index.js", "sourcesContent": ["var json_stringify = require('./lib/stringify.js').stringify;\nvar json_parse     = require('./lib/parse.js');\n\nmodule.exports = function(options) {\n    return  {\n        parse: json_parse(options),\n        stringify: json_stringify\n    }\n};\n//create the default method members with no options applied for backwards compatibility\nmodule.exports.parse = json_parse();\nmodule.exports.stringify = json_stringify;\n", "var BigNumber = require('bignumber.js');\n\n/*\n    json2.js\n    2013-05-26\n\n    Public Domain.\n\n    NO WARRANTY EXPRESSED OR IMPLIED. USE AT YOUR OWN RISK.\n\n    See http://www.JSON.org/js.html\n\n\n    This code should be minified before deployment.\n    See http://javascript.crockford.com/jsmin.html\n\n    USE YOUR OWN COPY. IT IS EXTREMELY UNWISE TO LOAD CODE FROM SERVERS YOU DO\n    NOT CONTROL.\n\n\n    This file creates a global JSON object containing two methods: stringify\n    and parse.\n\n        JSON.stringify(value, replacer, space)\n            value       any JavaScript value, usually an object or array.\n\n            replacer    an optional parameter that determines how object\n                        values are stringified for objects. It can be a\n                        function or an array of strings.\n\n            space       an optional parameter that specifies the indentation\n                        of nested structures. If it is omitted, the text will\n                        be packed without extra whitespace. If it is a number,\n                        it will specify the number of spaces to indent at each\n                        level. If it is a string (such as '\\t' or '&nbsp;'),\n                        it contains the characters used to indent at each level.\n\n            This method produces a JSON text from a JavaScript value.\n\n            When an object value is found, if the object contains a toJSON\n            method, its toJSON method will be called and the result will be\n            stringified. A toJSON method does not serialize: it returns the\n            value represented by the name/value pair that should be serialized,\n            or undefined if nothing should be serialized. The toJSON method\n            will be passed the key associated with the value, and this will be\n            bound to the value\n\n            For example, this would serialize Dates as ISO strings.\n\n                Date.prototype.toJSON = function (key) {\n                    function f(n) {\n                        // Format integers to have at least two digits.\n                        return n < 10 ? '0' + n : n;\n                    }\n\n                    return this.getUTCFullYear()   + '-' +\n                         f(this.getUTCMonth() + 1) + '-' +\n                         f(this.getUTCDate())      + 'T' +\n                         f(this.getUTCHours())     + ':' +\n                         f(this.getUTCMinutes())   + ':' +\n                         f(this.getUTCSeconds())   + 'Z';\n                };\n\n            You can provide an optional replacer method. It will be passed the\n            key and value of each member, with this bound to the containing\n            object. The value that is returned from your method will be\n            serialized. If your method returns undefined, then the member will\n            be excluded from the serialization.\n\n            If the replacer parameter is an array of strings, then it will be\n            used to select the members to be serialized. It filters the results\n            such that only members with keys listed in the replacer array are\n            stringified.\n\n            Values that do not have JSON representations, such as undefined or\n            functions, will not be serialized. Such values in objects will be\n            dropped; in arrays they will be replaced with null. You can use\n            a replacer function to replace those with JSON values.\n            JSON.stringify(undefined) returns undefined.\n\n            The optional space parameter produces a stringification of the\n            value that is filled with line breaks and indentation to make it\n            easier to read.\n\n            If the space parameter is a non-empty string, then that string will\n            be used for indentation. If the space parameter is a number, then\n            the indentation will be that many spaces.\n\n            Example:\n\n            text = JSON.stringify(['e', {pluribus: 'unum'}]);\n            // text is '[\"e\",{\"pluribus\":\"unum\"}]'\n\n\n            text = JSON.stringify(['e', {pluribus: 'unum'}], null, '\\t');\n            // text is '[\\n\\t\"e\",\\n\\t{\\n\\t\\t\"pluribus\": \"unum\"\\n\\t}\\n]'\n\n            text = JSON.stringify([new Date()], function (key, value) {\n                return this[key] instanceof Date ?\n                    'Date(' + this[key] + ')' : value;\n            });\n            // text is '[\"Date(---current time---)\"]'\n\n\n        JSON.parse(text, reviver)\n            This method parses a JSON text to produce an object or array.\n            It can throw a SyntaxError exception.\n\n            The optional reviver parameter is a function that can filter and\n            transform the results. It receives each of the keys and values,\n            and its return value is used instead of the original value.\n            If it returns what it received, then the structure is not modified.\n            If it returns undefined then the member is deleted.\n\n            Example:\n\n            // Parse the text. Values that look like ISO date strings will\n            // be converted to Date objects.\n\n            myData = JSON.parse(text, function (key, value) {\n                var a;\n                if (typeof value === 'string') {\n                    a =\n/^(\\d{4})-(\\d{2})-(\\d{2})T(\\d{2}):(\\d{2}):(\\d{2}(?:\\.\\d*)?)Z$/.exec(value);\n                    if (a) {\n                        return new Date(Date.UTC(+a[1], +a[2] - 1, +a[3], +a[4],\n                            +a[5], +a[6]));\n                    }\n                }\n                return value;\n            });\n\n            myData = JSON.parse('[\"Date(09/09/2001)\"]', function (key, value) {\n                var d;\n                if (typeof value === 'string' &&\n                        value.slice(0, 5) === 'Date(' &&\n                        value.slice(-1) === ')') {\n                    d = new Date(value.slice(5, -1));\n                    if (d) {\n                        return d;\n                    }\n                }\n                return value;\n            });\n\n\n    This is a reference implementation. You are free to copy, modify, or\n    redistribute.\n*/\n\n/*jslint evil: true, regexp: true */\n\n/*members \"\", \"\\b\", \"\\t\", \"\\n\", \"\\f\", \"\\r\", \"\\\"\", JSON, \"\\\\\", apply,\n    call, charCodeAt, getUTCDate, getUTCFullYear, getUTCHours,\n    getUTCMinutes, getUTCMonth, getUTCSeconds, hasOwnProperty, join,\n    lastIndex, length, parse, prototype, push, replace, slice, stringify,\n    test, toJSON, toString, valueOf\n*/\n\n\n// Create a JSON object only if one does not already exist. We create the\n// methods in a closure to avoid creating global variables.\n\nvar JSON = module.exports;\n\n(function () {\n    \n\n    function f(n) {\n        // Format integers to have at least two digits.\n        return n < 10 ? '0' + n : n;\n    }\n\n    var cx = /[\\u0000\\u00ad\\u0600-\\u0604\\u070f\\u17b4\\u17b5\\u200c-\\u200f\\u2028-\\u202f\\u2060-\\u206f\\ufeff\\ufff0-\\uffff]/g,\n        escapable = /[\\\\\\\"\\x00-\\x1f\\x7f-\\x9f\\u00ad\\u0600-\\u0604\\u070f\\u17b4\\u17b5\\u200c-\\u200f\\u2028-\\u202f\\u2060-\\u206f\\ufeff\\ufff0-\\uffff]/g,\n        gap,\n        indent,\n        meta = {    // table of character substitutions\n            '\\b': '\\\\b',\n            '\\t': '\\\\t',\n            '\\n': '\\\\n',\n            '\\f': '\\\\f',\n            '\\r': '\\\\r',\n            '\"' : '\\\\\"',\n            '\\\\': '\\\\\\\\'\n        },\n        rep;\n\n\n    function quote(string) {\n\n// If the string contains no control characters, no quote characters, and no\n// backslash characters, then we can safely slap some quotes around it.\n// Otherwise we must also replace the offending characters with safe escape\n// sequences.\n\n        escapable.lastIndex = 0;\n        return escapable.test(string) ? '\"' + string.replace(escapable, function (a) {\n            var c = meta[a];\n            return typeof c === 'string'\n                ? c\n                : '\\\\u' + ('0000' + a.charCodeAt(0).toString(16)).slice(-4);\n        }) + '\"' : '\"' + string + '\"';\n    }\n\n\n    function str(key, holder) {\n\n// Produce a string from holder[key].\n\n        var i,          // The loop counter.\n            k,          // The member key.\n            v,          // The member value.\n            length,\n            mind = gap,\n            partial,\n            value = holder[key],\n            isBigNumber = value != null && (value instanceof BigNumber || BigNumber.isBigNumber(value));\n\n// If the value has a toJSON method, call it to obtain a replacement value.\n\n        if (value && typeof value === 'object' &&\n                typeof value.toJSON === 'function') {\n            value = value.toJSON(key);\n        }\n\n// If we were called with a replacer function, then call the replacer to\n// obtain a replacement value.\n\n        if (typeof rep === 'function') {\n            value = rep.call(holder, key, value);\n        }\n\n// What happens next depends on the value's type.\n\n        switch (typeof value) {\n        case 'string':\n            if (isBigNumber) {\n                return value;\n            } else {\n                return quote(value);\n            }\n\n        case 'number':\n\n// JSON numbers must be finite. Encode non-finite numbers as null.\n\n            return isFinite(value) ? String(value) : 'null';\n\n        case 'boolean':\n        case 'null':\n        case 'bigint':\n\n// If the value is a boolean or null, convert it to a string. Note:\n// typeof null does not produce 'null'. The case is included here in\n// the remote chance that this gets fixed someday.\n\n            return String(value);\n\n// If the type is 'object', we might be dealing with an object or an array or\n// null.\n\n        case 'object':\n\n// Due to a specification blunder in ECMAScript, typeof null is 'object',\n// so watch out for that case.\n\n            if (!value) {\n                return 'null';\n            }\n\n// Make an array to hold the partial results of stringifying this object value.\n\n            gap += indent;\n            partial = [];\n\n// Is the value an array?\n\n            if (Object.prototype.toString.apply(value) === '[object Array]') {\n\n// The value is an array. Stringify every element. Use null as a placeholder\n// for non-JSON values.\n\n                length = value.length;\n                for (i = 0; i < length; i += 1) {\n                    partial[i] = str(i, value) || 'null';\n                }\n\n// Join all of the elements together, separated with commas, and wrap them in\n// brackets.\n\n                v = partial.length === 0\n                    ? '[]'\n                    : gap\n                    ? '[\\n' + gap + partial.join(',\\n' + gap) + '\\n' + mind + ']'\n                    : '[' + partial.join(',') + ']';\n                gap = mind;\n                return v;\n            }\n\n// If the replacer is an array, use it to select the members to be stringified.\n\n            if (rep && typeof rep === 'object') {\n                length = rep.length;\n                for (i = 0; i < length; i += 1) {\n                    if (typeof rep[i] === 'string') {\n                        k = rep[i];\n                        v = str(k, value);\n                        if (v) {\n                            partial.push(quote(k) + (gap ? ': ' : ':') + v);\n                        }\n                    }\n                }\n            } else {\n\n// Otherwise, iterate through all of the keys in the object.\n\n                Object.keys(value).forEach(function(k) {\n                    var v = str(k, value);\n                    if (v) {\n                        partial.push(quote(k) + (gap ? ': ' : ':') + v);\n                    }\n                });\n            }\n\n// Join all of the member texts together, separated with commas,\n// and wrap them in braces.\n\n            v = partial.length === 0\n                ? '{}'\n                : gap\n                ? '{\\n' + gap + partial.join(',\\n' + gap) + '\\n' + mind + '}'\n                : '{' + partial.join(',') + '}';\n            gap = mind;\n            return v;\n        }\n    }\n\n// If the JSON object does not yet have a stringify method, give it one.\n\n    if (typeof JSON.stringify !== 'function') {\n        JSON.stringify = function (value, replacer, space) {\n\n// The stringify method takes a value and an optional replacer, and an optional\n// space parameter, and returns a JSON text. The replacer can be a function\n// that can replace values, or an array of strings that will select the keys.\n// A default replacer method can be provided. Use of the space parameter can\n// produce text that is more easily readable.\n\n            var i;\n            gap = '';\n            indent = '';\n\n// If the space parameter is a number, make an indent string containing that\n// many spaces.\n\n            if (typeof space === 'number') {\n                for (i = 0; i < space; i += 1) {\n                    indent += ' ';\n                }\n\n// If the space parameter is a string, it will be used as the indent string.\n\n            } else if (typeof space === 'string') {\n                indent = space;\n            }\n\n// If there is a replacer, it must be a function or an array.\n// Otherwise, throw an error.\n\n            rep = replacer;\n            if (replacer && typeof replacer !== 'function' &&\n                    (typeof replacer !== 'object' ||\n                    typeof replacer.length !== 'number')) {\n                throw new Error('JSON.stringify');\n            }\n\n// Make a fake root object containing our value under the key of ''.\n// Return the result of stringifying the value.\n\n            return str('', {'': value});\n        };\n    }\n}());\n", "var BigNumber = null;\n\n// regexpxs extracted from\n// (c) BSD-3-Clause\n// https://github.com/fastify/secure-json-parse/graphs/contributors and https://github.com/hapijs/bourne/graphs/contributors\n\nconst suspectProtoRx = /(?:_|\\\\u005[Ff])(?:_|\\\\u005[Ff])(?:p|\\\\u0070)(?:r|\\\\u0072)(?:o|\\\\u006[Ff])(?:t|\\\\u0074)(?:o|\\\\u006[Ff])(?:_|\\\\u005[Ff])(?:_|\\\\u005[Ff])/;\nconst suspectConstructorRx = /(?:c|\\\\u0063)(?:o|\\\\u006[Ff])(?:n|\\\\u006[Ee])(?:s|\\\\u0073)(?:t|\\\\u0074)(?:r|\\\\u0072)(?:u|\\\\u0075)(?:c|\\\\u0063)(?:t|\\\\u0074)(?:o|\\\\u006[Ff])(?:r|\\\\u0072)/;\n\n/*\n    json_parse.js\n    2012-06-20\n\n    Public Domain.\n\n    NO WARRANTY EXPRESSED OR IMPLIED. USE AT YOUR OWN RISK.\n\n    This file creates a json_parse function.\n    During create you can (optionally) specify some behavioural switches\n\n        require('json-bigint')(options)\n\n            The optional options parameter holds switches that drive certain\n            aspects of the parsing process:\n            * options.strict = true will warn about duplicate-key usage in the json.\n              The default (strict = false) will silently ignore those and overwrite\n              values for keys that are in duplicate use.\n\n    The resulting function follows this signature:\n        json_parse(text, reviver)\n            This method parses a JSON text to produce an object or array.\n            It can throw a SyntaxError exception.\n\n            The optional reviver parameter is a function that can filter and\n            transform the results. It receives each of the keys and values,\n            and its return value is used instead of the original value.\n            If it returns what it received, then the structure is not modified.\n            If it returns undefined then the member is deleted.\n\n            Example:\n\n            // Parse the text. Values that look like ISO date strings will\n            // be converted to Date objects.\n\n            myData = json_parse(text, function (key, value) {\n                var a;\n                if (typeof value === 'string') {\n                    a =\n/^(\\d{4})-(\\d{2})-(\\d{2})T(\\d{2}):(\\d{2}):(\\d{2}(?:\\.\\d*)?)Z$/.exec(value);\n                    if (a) {\n                        return new Date(Date.UTC(+a[1], +a[2] - 1, +a[3], +a[4],\n                            +a[5], +a[6]));\n                    }\n                }\n                return value;\n            });\n\n    This is a reference implementation. You are free to copy, modify, or\n    redistribute.\n\n    This code should be minified before deployment.\n    See http://javascript.crockford.com/jsmin.html\n\n    USE YOUR OWN COPY. IT IS EXTREMELY UNWISE TO LOAD CODE FROM SERVERS YOU DO\n    NOT CONTROL.\n*/\n\n/*members \"\", \"\\\"\", \"\\/\", \"\\\\\", at, b, call, charAt, f, fromCharCode,\n    hasOwnProperty, message, n, name, prototype, push, r, t, text\n*/\n\nvar json_parse = function (options) {\n  \n\n  // This is a function that can parse a JSON text, producing a JavaScript\n  // data structure. It is a simple, recursive descent parser. It does not use\n  // eval or regular expressions, so it can be used as a model for implementing\n  // a JSON parser in other languages.\n\n  // We are defining the function inside of another function to avoid creating\n  // global variables.\n\n  // Default options one can override by passing options to the parse()\n  var _options = {\n    strict: false, // not being strict means do not generate syntax errors for \"duplicate key\"\n    storeAsString: false, // toggles whether the values should be stored as BigNumber (default) or a string\n    alwaysParseAsBig: false, // toggles whether all numbers should be Big\n    useNativeBigInt: false, // toggles whether to use native BigInt instead of bignumber.js\n    protoAction: 'error',\n    constructorAction: 'error',\n  };\n\n  // If there are options, then use them to override the default _options\n  if (options !== undefined && options !== null) {\n    if (options.strict === true) {\n      _options.strict = true;\n    }\n    if (options.storeAsString === true) {\n      _options.storeAsString = true;\n    }\n    _options.alwaysParseAsBig =\n      options.alwaysParseAsBig === true ? options.alwaysParseAsBig : false;\n    _options.useNativeBigInt =\n      options.useNativeBigInt === true ? options.useNativeBigInt : false;\n\n    if (typeof options.constructorAction !== 'undefined') {\n      if (\n        options.constructorAction === 'error' ||\n        options.constructorAction === 'ignore' ||\n        options.constructorAction === 'preserve'\n      ) {\n        _options.constructorAction = options.constructorAction;\n      } else {\n        throw new Error(\n          `Incorrect value for constructorAction option, must be \"error\", \"ignore\" or undefined but passed ${options.constructorAction}`\n        );\n      }\n    }\n\n    if (typeof options.protoAction !== 'undefined') {\n      if (\n        options.protoAction === 'error' ||\n        options.protoAction === 'ignore' ||\n        options.protoAction === 'preserve'\n      ) {\n        _options.protoAction = options.protoAction;\n      } else {\n        throw new Error(\n          `Incorrect value for protoAction option, must be \"error\", \"ignore\" or undefined but passed ${options.protoAction}`\n        );\n      }\n    }\n  }\n\n  var at, // The index of the current character\n    ch, // The current character\n    escapee = {\n      '\"': '\"',\n      '\\\\': '\\\\',\n      '/': '/',\n      b: '\\b',\n      f: '\\f',\n      n: '\\n',\n      r: '\\r',\n      t: '\\t',\n    },\n    text,\n    error = function (m) {\n      // Call error when something is wrong.\n\n      throw {\n        name: 'SyntaxError',\n        message: m,\n        at: at,\n        text: text,\n      };\n    },\n    next = function (c) {\n      // If a c parameter is provided, verify that it matches the current character.\n\n      if (c && c !== ch) {\n        error(\"Expected '\" + c + \"' instead of '\" + ch + \"'\");\n      }\n\n      // Get the next character. When there are no more characters,\n      // return the empty string.\n\n      ch = text.charAt(at);\n      at += 1;\n      return ch;\n    },\n    number = function () {\n      // Parse a number value.\n\n      var number,\n        string = '';\n\n      if (ch === '-') {\n        string = '-';\n        next('-');\n      }\n      while (ch >= '0' && ch <= '9') {\n        string += ch;\n        next();\n      }\n      if (ch === '.') {\n        string += '.';\n        while (next() && ch >= '0' && ch <= '9') {\n          string += ch;\n        }\n      }\n      if (ch === 'e' || ch === 'E') {\n        string += ch;\n        next();\n        if (ch === '-' || ch === '+') {\n          string += ch;\n          next();\n        }\n        while (ch >= '0' && ch <= '9') {\n          string += ch;\n          next();\n        }\n      }\n      number = +string;\n      if (!isFinite(number)) {\n        error('Bad number');\n      } else {\n        if (BigNumber == null) BigNumber = require('bignumber.js');\n        //if (number > 9007199254740992 || number < -9007199254740992)\n        // Bignumber has stricter check: everything with length > 15 digits disallowed\n        if (string.length > 15)\n          return _options.storeAsString\n            ? string\n            : _options.useNativeBigInt\n            ? BigInt(string)\n            : new BigNumber(string);\n        else\n          return !_options.alwaysParseAsBig\n            ? number\n            : _options.useNativeBigInt\n            ? BigInt(number)\n            : new BigNumber(number);\n      }\n    },\n    string = function () {\n      // Parse a string value.\n\n      var hex,\n        i,\n        string = '',\n        uffff;\n\n      // When parsing for string values, we must look for \" and \\ characters.\n\n      if (ch === '\"') {\n        var startAt = at;\n        while (next()) {\n          if (ch === '\"') {\n            if (at - 1 > startAt) string += text.substring(startAt, at - 1);\n            next();\n            return string;\n          }\n          if (ch === '\\\\') {\n            if (at - 1 > startAt) string += text.substring(startAt, at - 1);\n            next();\n            if (ch === 'u') {\n              uffff = 0;\n              for (i = 0; i < 4; i += 1) {\n                hex = parseInt(next(), 16);\n                if (!isFinite(hex)) {\n                  break;\n                }\n                uffff = uffff * 16 + hex;\n              }\n              string += String.fromCharCode(uffff);\n            } else if (typeof escapee[ch] === 'string') {\n              string += escapee[ch];\n            } else {\n              break;\n            }\n            startAt = at;\n          }\n        }\n      }\n      error('Bad string');\n    },\n    white = function () {\n      // Skip whitespace.\n\n      while (ch && ch <= ' ') {\n        next();\n      }\n    },\n    word = function () {\n      // true, false, or null.\n\n      switch (ch) {\n        case 't':\n          next('t');\n          next('r');\n          next('u');\n          next('e');\n          return true;\n        case 'f':\n          next('f');\n          next('a');\n          next('l');\n          next('s');\n          next('e');\n          return false;\n        case 'n':\n          next('n');\n          next('u');\n          next('l');\n          next('l');\n          return null;\n      }\n      error(\"Unexpected '\" + ch + \"'\");\n    },\n    value, // Place holder for the value function.\n    array = function () {\n      // Parse an array value.\n\n      var array = [];\n\n      if (ch === '[') {\n        next('[');\n        white();\n        if (ch === ']') {\n          next(']');\n          return array; // empty array\n        }\n        while (ch) {\n          array.push(value());\n          white();\n          if (ch === ']') {\n            next(']');\n            return array;\n          }\n          next(',');\n          white();\n        }\n      }\n      error('Bad array');\n    },\n    object = function () {\n      // Parse an object value.\n\n      var key,\n        object = Object.create(null);\n\n      if (ch === '{') {\n        next('{');\n        white();\n        if (ch === '}') {\n          next('}');\n          return object; // empty object\n        }\n        while (ch) {\n          key = string();\n          white();\n          next(':');\n          if (\n            _options.strict === true &&\n            Object.hasOwnProperty.call(object, key)\n          ) {\n            error('Duplicate key \"' + key + '\"');\n          }\n\n          if (suspectProtoRx.test(key) === true) {\n            if (_options.protoAction === 'error') {\n              error('Object contains forbidden prototype property');\n            } else if (_options.protoAction === 'ignore') {\n              value();\n            } else {\n              object[key] = value();\n            }\n          } else if (suspectConstructorRx.test(key) === true) {\n            if (_options.constructorAction === 'error') {\n              error('Object contains forbidden constructor property');\n            } else if (_options.constructorAction === 'ignore') {\n              value();\n            } else {\n              object[key] = value();\n            }\n          } else {\n            object[key] = value();\n          }\n\n          white();\n          if (ch === '}') {\n            next('}');\n            return object;\n          }\n          next(',');\n          white();\n        }\n      }\n      error('Bad object');\n    };\n\n  value = function () {\n    // Parse a JSON value. It could be an object, an array, a string, a number,\n    // or a word.\n\n    white();\n    switch (ch) {\n      case '{':\n        return object();\n      case '[':\n        return array();\n      case '\"':\n        return string();\n      case '-':\n        return number();\n      default:\n        return ch >= '0' && ch <= '9' ? number() : word();\n    }\n  };\n\n  // Return the json_parse function. It will have access to all of the above\n  // functions and variables.\n\n  return function (source, reviver) {\n    var result;\n\n    text = source + '';\n    at = 0;\n    ch = ' ';\n    result = value();\n    white();\n    if (ch) {\n      error('Syntax error');\n    }\n\n    // If there is a reviver function, we recursively walk the new structure,\n    // passing each name/value pair to the reviver function for possible\n    // transformation, starting with a temporary root object that holds the result\n    // in an empty key. If there is not a reviver function, we simply return the\n    // result.\n\n    return typeof reviver === 'function'\n      ? (function walk(holder, key) {\n          var k,\n            v,\n            value = holder[key];\n          if (value && typeof value === 'object') {\n            Object.keys(value).forEach(function (k) {\n              v = walk(value, k);\n              if (v !== undefined) {\n                value[k] = v;\n              } else {\n                delete value[k];\n              }\n            });\n          }\n          return reviver.call(holder, key, value);\n        })({ '': result }, '')\n      : result;\n  };\n};\n\nmodule.exports = json_parse;\n"]}