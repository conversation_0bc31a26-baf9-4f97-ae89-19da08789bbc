{"version": 3, "sources": ["index.js"], "names": [], "mappings": ";;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.js", "sourcesContent": ["\n/*!\n *  Copyright 2010 LearnBoost <<EMAIL>>\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\n/**\n * Module dependencies.\n */\n\nvar crypto = require('crypto')\n  , parse = require('url').parse\n  ;\n\n/**\n * Valid keys.\n */\n\nvar keys = \n  [ 'acl'\n  , 'location'\n  , 'logging'\n  , 'notification'\n  , 'partNumber'\n  , 'policy'\n  , 'requestPayment'\n  , 'torrent'\n  , 'uploadId'\n  , 'uploads'\n  , 'versionId'\n  , 'versioning'\n  , 'versions'\n  , 'website'\n  ]\n\n/**\n * Return an \"Authorization\" header value with the given `options`\n * in the form of \"AWS <key>:<signature>\"\n *\n * @param {Object} options\n * @return {String}\n * @api private\n */\n\nfunction authorization (options) {\n  return 'AWS ' + options.key + ':' + sign(options)\n}\n\nmodule.exports = authorization\nmodule.exports.authorization = authorization\n\n/**\n * Simple HMAC-SHA1 Wrapper\n *\n * @param {Object} options\n * @return {String}\n * @api private\n */ \n\nfunction hmacSha1 (options) {\n  return crypto.createHmac('sha1', options.secret).update(options.message).digest('base64')\n}\n\nmodule.exports.hmacSha1 = hmacSha1\n\n/**\n * Create a base64 sha1 HMAC for `options`. \n * \n * @param {Object} options\n * @return {String}\n * @api private\n */\n\nfunction sign (options) {\n  options.message = stringToSign(options)\n  return hmacSha1(options)\n}\nmodule.exports.sign = sign\n\n/**\n * Create a base64 sha1 HMAC for `options`. \n *\n * Specifically to be used with S3 presigned URLs\n * \n * @param {Object} options\n * @return {String}\n * @api private\n */\n\nfunction signQuery (options) {\n  options.message = queryStringToSign(options)\n  return hmacSha1(options)\n}\nmodule.exports.signQuery= signQuery\n\n/**\n * Return a string for sign() with the given `options`.\n *\n * Spec:\n * \n *    <verb>\\n\n *    <md5>\\n\n *    <content-type>\\n\n *    <date>\\n\n *    [headers\\n]\n *    <resource>\n *\n * @param {Object} options\n * @return {String}\n * @api private\n */\n\nfunction stringToSign (options) {\n  var headers = options.amazonHeaders || ''\n  if (headers) headers += '\\n'\n  var r = \n    [ options.verb\n    , options.md5\n    , options.contentType\n    , options.date ? options.date.toUTCString() : ''\n    , headers + options.resource\n    ]\n  return r.join('\\n')\n}\nmodule.exports.stringToSign = stringToSign\n\n/**\n * Return a string for sign() with the given `options`, but is meant exclusively\n * for S3 presigned URLs\n *\n * Spec:\n * \n *    <date>\\n\n *    <resource>\n *\n * @param {Object} options\n * @return {String}\n * @api private\n */\n\nfunction queryStringToSign (options){\n  return 'GET\\n\\n\\n' + options.date + '\\n' + options.resource\n}\nmodule.exports.queryStringToSign = queryStringToSign\n\n/**\n * Perform the following:\n *\n *  - ignore non-amazon headers\n *  - lowercase fields\n *  - sort lexicographically\n *  - trim whitespace between \":\"\n *  - join with newline\n *\n * @param {Object} headers\n * @return {String}\n * @api private\n */\n\nfunction canonicalizeHeaders (headers) {\n  var buf = []\n    , fields = Object.keys(headers)\n    ;\n  for (var i = 0, len = fields.length; i < len; ++i) {\n    var field = fields[i]\n      , val = headers[field]\n      , field = field.toLowerCase()\n      ;\n    if (0 !== field.indexOf('x-amz')) continue\n    buf.push(field + ':' + val)\n  }\n  return buf.sort().join('\\n')\n}\nmodule.exports.canonicalizeHeaders = canonicalizeHeaders\n\n/**\n * Perform the following:\n *\n *  - ignore non sub-resources\n *  - sort lexicographically\n *\n * @param {String} resource\n * @return {String}\n * @api private\n */\n\nfunction canonicalizeResource (resource) {\n  var url = parse(resource, true)\n    , path = url.pathname\n    , buf = []\n    ;\n\n  Object.keys(url.query).forEach(function(key){\n    if (!~keys.indexOf(key)) return\n    var val = '' == url.query[key] ? '' : '=' + encodeURIComponent(url.query[key])\n    buf.push(key + val)\n  })\n\n  return path + (buf.length ? '?' + buf.sort().join('&') : '')\n}\nmodule.exports.canonicalizeResource = canonicalizeResource\n"]}