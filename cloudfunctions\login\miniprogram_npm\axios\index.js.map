{"version": 3, "sources": ["index.js", "lib/axios.js", "lib/utils.js", "lib/helpers/bind.js", "lib/core/Axios.js", "lib/helpers/buildURL.js", "lib/core/InterceptorManager.js", "lib/core/dispatchRequest.js", "lib/core/transformData.js", "lib/defaults.js", "lib/helpers/normalizeHeaderName.js", "lib/core/enhanceError.js", "lib/adapters/xhr.js", "lib/core/settle.js", "lib/core/createError.js", "lib/helpers/cookies.js", "lib/core/buildFullPath.js", "lib/helpers/isAbsoluteURL.js", "lib/helpers/combineURLs.js", "lib/helpers/parseHeaders.js", "lib/helpers/isURLSameOrigin.js", "lib/adapters/http.js", "package.json", "lib/cancel/isCancel.js", "lib/core/mergeConfig.js", "lib/helpers/validator.js", "lib/cancel/Cancel.js", "lib/cancel/CancelToken.js", "lib/helpers/spread.js", "lib/helpers/isAxiosError.js"], "names": [], "mappings": ";;;;;;;AAAA;;;ACAA;AACA;AACA;AACA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,AENA,ADGA;ADIA,AENA,ADGA;ADIA,AENA,ADGA;ADIA,AGTA,ADGA,ADGA;ADIA,AGTA,ADGA,ADGA;ADIA,AGTA,ADGA,ADGA;ADIA,AGTA,ADGA,AENA,AHSA;ADIA,AGTA,ADGA,AENA,AHSA;ADIA,AGTA,ADGA,AENA,AHSA;ADIA,AGTA,AENA,AHSA,AENA,AHSA;ADIA,AGTA,AENA,AHSA,AENA,AHSA;ADIA,AGTA,AENA,AHSA,AENA,AHSA;ADIA,AGTA,AENA,ACHA,AFMA,AHSA;ADIA,AGTA,AENA,ACHA,AFMA,AHSA;ADIA,AGTA,AENA,ACHA,AFMA,AHSA;ADIA,AGTA,AENA,ACHA,ACHA,AHSA,AHSA;ADIA,AGTA,AENA,ACHA,ACHA,AHSA,AHSA;ADIA,AGTA,AENA,ACHA,ACHA,AHSA,AHSA;ADIA,AGTA,AENA,ACHA,ACHA,ACHA,AJYA,AHSA;ADIA,AGTA,AENA,ACHA,ACHA,ACHA,AJYA,AHSA;ADIA,AGTA,AENA,ACHA,ACHA,ACHA,AJYA,AHSA;ADIA,AGTA,AENA,ACHA,ACHA,ACHA,AJYA,AKfA,ARwBA;ADIA,AGTA,AENA,ACHA,ACHA,ACHA,AJYA,AKfA,ARwBA;ADIA,AGTA,AENA,ACHA,ACHA,ACHA,AJYA,AKfA,ARwBA;ADIA,AGTA,AENA,ACHA,AIZA,AHSA,ACHA,AJYA,AKfA,ARwBA;ADIA,AGTA,AENA,ACHA,AIZA,AHSA,ACHA,AJYA,AKfA,ARwBA;ADIA,AGTA,AENA,ACHA,AIZA,AHSA,ACHA,AJYA,AKfA,ARwBA;AU7BA,AXiCA,AGTA,AENA,ACHA,AIZA,AHSA,ACHA,AJYA,AKfA,ARwBA;AU7BA,AXiCA,AGTA,AENA,ACHA,AIZA,AHSA,ACHA,AJYA,AKfA,ARwBA;AU7BA,AXiCA,AGTA,AENA,ACHA,AIZA,AHSA,ACHA,AJYA,AKfA,ARwBA;AU7BA,AXiCA,AGTA,AENA,ACHA,AIZA,AENA,ALeA,ACHA,AJYA,AKfA,ARwBA;AU7BA,AXiCA,AGTA,AENA,ACHA,AIZA,AENA,ALeA,ACHA,AJYA,AKfA,ARwBA;AU7BA,AXiCA,AGTA,AENA,ACHA,AIZA,AENA,ALeA,ACHA,AJYA,AKfA,ARwBA;AU7BA,AXiCA,AGTA,AENA,AQxBA,APqBA,AIZA,AENA,ALeA,ACHA,AJYA,AKfA,ARwBA;AU7BA,AXiCA,AGTA,AENA,AQxBA,APqBA,AIZA,AENA,ALeA,ACHA,AJYA,AHSA;AU7BA,AXiCA,AGTA,AENA,AQxBA,APqBA,AIZA,AENA,ALeA,ACHA,AJYA,AHSA;AU7BA,AXiCA,AGTA,AENA,AQxBA,APqBA,AIZA,AENA,ALeA,ACHA,AJYA,AU9BA,AbuCA;AU7BA,AXiCA,AGTA,AENA,AQxBA,APqBA,AIZA,AENA,ALeA,ACHA,AJYA,AU9BA,AbuCA;AU7BA,AXiCA,AGTA,AENA,AQxBA,APqBA,AIZA,AENA,AJYA,AJYA,AU9BA,AbuCA;AU7BA,AXiCA,AGTA,AENA,AU9BA,AFMA,APqBA,AIZA,AENA,AJYA,AJYA,AU9BA,AbuCA;AU7BA,AXiCA,AGTA,AENA,AU9BA,AFMA,APqBA,AIZA,AENA,AJYA,AJYA,AU9BA,AbuCA;AU7BA,AXiCA,AGTA,AENA,AU9BA,AFMA,APqBA,AIZA,AENA,AJYA,AJYA,AU9BA,AbuCA;AU7BA,AXiCA,AGTA,AENA,AU9BA,AFMA,APqBA,AIZA,AENA,AJYA,AJYA,AU9BA,AENA,Af6CA;AU7BA,AXiCA,AGTA,AENA,AU9BA,AFMA,APqBA,AIZA,AENA,AJYA,AJYA,AU9BA,AENA,Af6CA;AU7BA,AXiCA,AGTA,AENA,AU9BA,AFMA,APqBA,AIZA,AENA,AJYA,AJYA,AU9BA,AENA,Af6CA;AU7BA,AXiCA,AGTA,AENA,AU9BA,AFMA,APqBA,AIZA,AENA,AJYA,AJYA,AavCA,AHSA,AENA,Af6CA;AU7BA,AXiCA,AGTA,AENA,AU9BA,AFMA,APqBA,AIZA,AENA,AJYA,AJYA,AavCA,AHSA,AENA,Af6CA;AU7BA,AXiCA,AGTA,AENA,AU9BA,AFMA,APqBA,AIZA,AENA,AJYA,AJYA,AavCA,AHSA,AENA,Af6CA;AU7BA,AXiCA,AGTA,AENA,AU9BA,AFMA,APqBA,AIZA,AENA,AJYA,AJYA,AavCA,AHSA,AENA,AENA,AjBmDA;AU7BA,AXiCA,AGTA,AENA,AU9BA,AFMA,APqBA,AIZA,AENA,AJYA,AJYA,AavCA,AHSA,AENA,AENA,AjBmDA;AU7BA,AXiCA,AGTA,AENA,AU9BA,AFMA,APqBA,AIZA,AENA,AJYA,AJYA,AavCA,AHSA,AENA,AENA,AjBmDA;AU7BA,ARwBA,AENA,AU9BA,AFMA,APqBA,AIZA,AENA,AJYA,AJYA,AavCA,AHSA,AENA,AGTA,ADGA,AjBmDA;AU7BA,ARwBA,AENA,AU9BA,AT2BA,AIZA,AENA,AJYA,AJYA,AavCA,AHSA,AENA,AGTA,ADGA,AjBmDA;AU7BA,ARwBA,AENA,AU9BA,AT2BA,AIZA,AENA,AJYA,AJYA,AavCA,AHSA,AENA,AGTA,ADGA,AjBmDA;AmBxDA,AT2BA,ARwBA,AENA,AU9BA,AT2BA,AIZA,AENA,AJYA,AJYA,AavCA,AHSA,AENA,AGTA,ADGA,AjBmDA;AmBxDA,AT2BA,ARwBA,AENA,AU9BA,AT2BA,AIZA,AENA,AJYA,AJYA,AavCA,AHSA,AENA,AGTA,ADGA,AjBmDA;AmBxDA,AT2BA,ARwBA,AENA,AU9BA,AT2BA,AIZA,AFMA,AJYA,AavCA,AHSA,AENA,AGTA,ADGA,AjBmDA;AmBxDA,AT2BA,ARwBA,AENA,AU9BA,AT2BA,AIZA,AFMA,AJYA,AavCA,AHSA,AKfA,ADGA,AjBmDA,AoB5DA;ADIA,AT2BA,ARwBA,AENA,AU9BA,AT2BA,AIZA,AFMA,AJYA,AavCA,AHSA,AKfA,ADGA,AjBmDA,AoB5DA;ADIA,AT2BA,ARwBA,AENA,AU9BA,AT2BA,AIZA,AFMA,AJYA,AavCA,AHSA,AKfA,ADGA,AjBmDA,AoB5DA;ADIA,AT2BA,AWjCA,AnByDA,AENA,ACHA,AIZA,AFMA,AJYA,AU9BA,AKfA,ADGA,AjBmDA,AoB5DA;ADIA,AT2BA,AWjCA,AnByDA,AENA,ACHA,AIZA,AFMA,AJYA,AU9BA,AKfA,ADGA,AjBmDA,AoB5DA;ADIA,AT2BA,AWjCA,AnByDA,AENA,ACHA,AIZA,AFMA,AJYA,AU9BA,AKfA,ADGA,AjBmDA,AoB5DA;ADIA,AT2BA,AWjCA,AnByDA,AENA,ACHA,AIZA,AavCA,Af6CA,AJYA,AU9BA,AKfA,ADGA,AjBmDA,AoB5DA;ADIA,AT2BA,AWjCA,AnByDA,AGTA,AIZA,AavCA,Af6CA,AJYA,AU9BA,AKfA,ADGA,AjBmDA,AoB5DA;ADIA,AT2BA,AWjCA,AnByDA,AGTA,AIZA,AavCA,Af6CA,AJYA,AU9BA,AKfA,ADGA,AjBmDA,AoB5DA;ADIA,AT2BA,ARwBA,AGTA,AIZA,AavCA,Af6CA,AJYA,AU9BA,AKfA,ADGA,AMlBA,AvBqEA,AoB5DA;ADIA,AT2BA,ARwBA,AGTA,AiBnDA,Af6CA,AJYA,AU9BA,AKfA,ADGA,AMlBA,AvBqEA,AoB5DA;ADIA,AT2BA,ARwBA,AGTA,AiBnDA,Af6CA,AJYA,AU9BA,AKfA,ADGA,AMlBA,AvBqEA,AoB5DA;ADIA,AT2BA,Ac1CA,AtBkEA,AGTA,AiBnDA,Af6CA,AJYA,AU9BA,AKfA,ADGA,AMlBA,AvBqEA,AoB5DA;ADIA,AT2BA,Ac1CA,AtBkEA,AGTA,AiBnDA,Af6CA,AJYA,AU9BA,AKfA,ADGA,AMlBA,AvBqEA,AoB5DA;ADIA,AT2BA,Ac1CA,AtBkEA,AGTA,AiBnDA,Af6CA,AJYA,AU9BA,AKfA,ADGA,AMlBA,AvBqEA,AoB5DA;ADIA,AT2BA,Ac1CA,ACHA,AvBqEA,AGTA,AiBnDA,Af6CA,AJYA,AU9BA,AKfA,ADGA,AMlBA,AvBqEA,AoB5DA;ADIA,AT2BA,Ac1CA,ACHA,AvBqEA,AGTA,AiBnDA,Af6CA,AJYA,AU9BA,AKfA,ADGA,AMlBA,AvBqEA,AoB5DA;ADIA,AT2BA,Ac1CA,ACHA,AvBqEA,AGTA,AiBnDA,Af6CA,AJYA,AU9BA,AKfA,ADGA,AMlBA,AvBqEA,AoB5DA;ADIA,AT2BA,Ac1CA,ACHA,AvBqEA,AGTA,AiBnDA,Af6CA,AJYA,AU9BA,AKfA,ADGA,AS3BA,AHSA,AvBqEA,AoB5DA;ADIA,AT2BA,Ac1CA,ACHA,AvBqEA,AGTA,AiBnDA,Af6CA,AJYA,AU9BA,AKfA,ADGA,AS3BA,AHSA,AvBqEA,AoB5DA;ADIA,AT2BA,Ac1CA,ACHA,AvBqEA,AGTA,AiBnDA,Af6CA,AMlBA,AKfA,ADGA,AS3BA,AHSA,AvBqEA,AoB5DA;ADIA,AT2BA,Ac1CA,ACHA,AvBqEA,AGTA,AiBnDA,Af6CA,AMlBA,Ac1CA,AT2BA,ADGA,AS3BA,AHSA,AvBqEA,AoB5DA;ADIA,AT2BA,Ac1CA,ACHA,AvBqEA,AGTA,AiBnDA,Af6CA,AMlBA,Ac1CA,AT2BA,ADGA,AS3BA,AHSA,AvBqEA,AoB5DA;ADIA,AT2BA,Ac1CA,ACHA,AvBqEA,AGTA,AiBnDA,Af6CA,AMlBA,Ac1CA,AT2BA,ADGA,AS3BA,AHSA,AvBqEA,AoB5DA;ADIA,AT2BA,Ac1CA,ACHA,AvBqEA,AGTA,AiBnDA,Af6CA,AMlBA,Ac1CA,AT2BA,ADGA,AS3BA,AHSA,AvBqEA,AoB5DA;ADIA,AT2BA,Ac1CA,ACHA,AvBqEA,AGTA,AiBnDA,Af6CA,AMlBA,Ac1CA,AT2BA,ADGA,AS3BA,AHSA,AvBqEA,AoB5DA;ADIA,AT2BA,Ac1CA,ACHA,AvBqEA,AGTA,AiBnDA,Af6CA,AMlBA,Ac1CA,AT2BA,ADGA,AS3BA,AHSA,AvBqEA,AoB5DA;ADIA,AT2BA,Ac1CA,ACHA,AvBqEA,AGTA,AiBnDA,Af6CA,AMlBA,Ac1CA,AT2BA,ADGA,AS3BA,AHSA,AvBqEA,AoB5DA;ADIA,AT2BA,Ac1CA,ACHA,AvBqEA,AGTA,AiBnDA,Af6CA,AMlBA,Ac1CA,AT2BA,ADGA,AS3BA,AHSA,AvBqEA,AoB5DA;ADIA,AT2BA,Ac1CA,ACHA,AvBqEA,AGTA,AiBnDA,Af6CA,AMlBA,Ac1CA,AT2BA,ADGA,AS3BA,AHSA,AvBqEA,AoB5DA;ADIA,AT2BA,Ac1CA,ACHA,AvBqEA,AGTA,AiBnDA,Af6CA,AMlBA,Ac1CA,AT2BA,ADGA,AS3BA,AHSA,AvBqEA,AoB5DA;ADIA,AT2BA,Ac1CA,ACHA,AvBqEA,AGTA,AiBnDA,Af6CA,AMlBA,Ac1CA,AT2BA,ADGA,AS3BA,AHSA,AvBqEA,AoB5DA;ADIA,AT2BA,Ae7CA,AvBqEA,AGTA,AiBnDA,Af6CA,AMlBA,Ac1CA,AT2BA,ADGA,AS3BA,AHSA,AvBqEA,AoB5DA;ADIA,AT2BA,Ae7CA,AvBqEA,AGTA,AiBnDA,Af6CA,AWjCA,ADGA,AS3BA,AHSA,AvBqEA,AoB5DA;ADIA,AT2BA,Ae7CA,AvBqEA,AGTA,AiBnDA,Af6CA,AWjCA,ADGA,AS3BA,AHSA,AvBqEA,AoB5DA;ADIA,AT2BA,Ae7CA,AvBqEA,AGTA,AiBnDA,Af6CA,AWjCA,ADGA,AS3BA,AHSA,AvBqEA,AoB5DA;ADIA,AT2BA,Ae7CA,AvBqEA,AGTA,AiBnDA,Af6CA,AWjCA,ADGA,AS3BA,AHSA,AvBqEA,AoB5DA;ADIA,AT2BA,Ae7CA,AvBqEA,AGTA,AiBnDA,Af6CA,AWjCA,ADGA,AS3BA,AHSA,AvBqEA,AoB5DA;ADIA,AT2BA,Ae7CA,AvBqEA,AoB5DA,Af6CA,AWjCA,ADGA,AS3BA,AHSA,AvBqEA,AoB5DA;ADIA,AT2BA,Ae7CA,AvBqEA,AoB5DA,Af6CA,AWjCA,ADGA,AS3BA,AHSA,AvBqEA,AoB5DA;ADIA,AT2BA,Ae7CA,AvBqEA,AoB5DA,Af6CA,AWjCA,ADGA,AS3BA,AHSA,AvBqEA,AoB5DA;ADIA,AT2BA,Ae7CA,AvBqEA,AoB5DA,Af6CA,AWjCA,ADGA,AS3BA,AHSA,AvBqEA,AoB5DA;ADIA,AT2BA,Ae7CA,AvBqEA,AoB5DA,Af6CA,AWjCA,ADGA,AS3BA,AHSA,AvBqEA,AoB5DA;ADIA,AT2BA,Ae7CA,AvBqEA,AoB5DA,Af6CA,AWjCA,ADGA,AS3BA,AHSA,AvBqEA,AoB5DA;ADIA,AT2BA,Ae7CA,AvBqEA,AoB5DA,Af6CA,AWjCA,ADGA,AS3BA,AHSA,AvBqEA,AoB5DA;ADIA,AT2BA,Ae7CA,AvBqEA,AoB5DA,Af6CA,AWjCA,AQxBA,AHSA,AvBqEA,AoB5DA;ADIA,AT2BA,Ae7CA,AvBqEA,AoB5DA,Af6CA,AWjCA,AKfA,AvBqEA,AoB5DA;ADIA,AT2BA,Ae7CA,AvBqEA,AoB5DA,Af6CA,AWjCA,AKfA,AvBqEA,AoB5DA;ADIA,AT2BA,Ae7CA,AvBqEA,AoB5DA,Af6CA,AWjCA,AKfA,AvBqEA,AoB5DA;ADIA,AT2BA,Ae7CA,AvBqEA,AoB5DA,Af6CA,AWjCA,AKfA,AvBqEA,AoB5DA;ADIA,AT2BA,Ae7CA,AvBqEA,AoB5DA,Af6CA,AWjCA,AKfA,AvBqEA,AoB5DA;ADIA,AT2BA,Ae7CA,AvBqEA,AoB5DA,Af6CA,AWjCA,AKfA,AvBqEA,AoB5DA;ADIA,AT2BA,Ae7CA,AvBqEA,AoB5DA,Af6CA,AWjCA,AKfA,AvBqEA,AoB5DA;ADIA,AT2BA,Ae7CA,AvBqEA,AoB5DA,Af6CA,AWjCA,AKfA,AvBqEA,AoB5DA;ADIA,AT2BA,Ae7CA,AvBqEA,AoB5DA,Af6CA,AWjCA,AKfA,AvBqEA,AoB5DA;ADIA,AT2BA,Ae7CA,AvBqEA,AoB5DA,Af6CA,AWjCA,AKfA,AvBqEA,AoB5DA;ADIA,AT2BA,Ae7CA,AvBqEA,AoB5DA,Af6CA,AWjCA,AKfA,AvBqEA,AoB5DA;ADIA,AT2BA,Ae7CA,AvBqEA,AoB5DA,Af6CA,AWjCA,AKfA,AvBqEA,AoB5DA;ADIA,AT2BA,Ae7CA,AvBqEA,AoB5DA,Af6CA,AWjCA,AKfA,AvBqEA,AoB5DA;ADIA,AT2BA,Ae7CA,AvBqEA,AoB5DA,Af6CA,AWjCA,AKfA,AvBqEA,AoB5DA;ADIA,AT2BA,Ae7CA,AvBqEA,AoB5DA,Af6CA,AWjCA,AKfA,AvBqEA,AoB5DA;ADIA,AT2BA,Ae7CA,AvBqEA,AoB5DA,Af6CA,AWjCA,AKfA,AvBqEA,AoB5DA;ADIA,AT2BA,Ae7CA,AvBqEA,AoB5DA,Af6CA,AWjCA,AKfA,AvBqEA,AoB5DA;ADIA,AT2BA,Ae7CA,AvBqEA,AoB5DA,Af6CA,AgBhDA,AvBqEA,AoB5DA;ADIA,AT2BA,Ae7CA,AvBqEA,AoB5DA,Af6CA,AgBhDA,AvBqEA,AoB5DA;ADIA,AT2BA,Ae7CA,AvBqEA,AoB5DA,Af6CA,AgBhDA,AvBqEA,AoB5DA;ADIA,AT2BA,Ae7CA,AvBqEA,AoB5DA,Af6CA,AgBhDA,AvBqEA,AoB5DA;ADIA,AT2BA,Ae7CA,AvBqEA,AoB5DA,Af6CA,AgBhDA,AvBqEA,AoB5DA;ADIA,AT2BA,Ae7CA,AvBqEA,AoB5DA,Af6CA,AgBhDA,AvBqEA,AoB5DA;ADIA,AT2BA,Ae7CA,AvBqEA,AoB5DA,Af6CA,AgBhDA,AvBqEA,AoB5DA;ADIA,AT2BA,Ae7CA,AvBqEA,AoB5DA,Af6CA,AgBhDA,AvBqEA,AoB5DA;ADIA,AT2BA,Ae7CA,AvBqEA,AoB5DA,Af6CA,AgBhDA,AvBqEA,AoB5DA;ADIA,AT2BA,Ae7CA,AvBqEA,AoB5DA,Af6CA,AgBhDA,AvBqEA,AoB5DA;ADIA,AT2BA,ARwBA,AoB5DA,Af6CA,AgBhDA,AvBqEA,AoB5DA;ADIA,AT2BA,ARwBA,AoB5DA,Af6CA,AgBhDA,AvBqEA,AoB5DA;ADIA,AT2BA,ARwBA,AoB5DA,Af6CA,AgBhDA,AvBqEA,AoB5DA;ADIA,AT2BA,ARwBA,AoB5DA,Af6CA,AgBhDA,AvBqEA,AoB5DA;ADIA,AT2BA,ARwBA,AoB5DA,Af6CA,AgBhDA,AvBqEA,AoB5DA;ADIA,AT2BA,ARwBA,AoB5DA,Af6CA,AgBhDA,AvBqEA,AoB5DA;ADIA,AT2BA,ARwBA,AoB5DA,Af6CA,AgBhDA,AvBqEA,AoB5DA;ADIA,AT2BA,ARwBA,AoB5DA,Af6CA,AgBhDA,AvBqEA,AoB5DA;ADIA,AT2BA,ARwBA,AoB5DA,Af6CA,AgBhDA,AvBqEA,AoB5DA;ADIA,AT2BA,ARwBA,AoB5DA,Af6CA,AgBhDA,AvBqEA,AoB5DA;ADIA,AT2BA,ARwBA,AoB5DA,Af6CA,AgBhDA,AvBqEA,AoB5DA;ADIA,AT2BA,ARwBA,AoB5DA,Af6CA,AgBhDA,AvBqEA,AoB5DA;ADIA,AT2BA,ARwBA,AoB5DA,Af6CA,AgBhDA,AvBqEA;AmBxDA,AT2BA,ARwBA,AoB5DA,Af6CA,AgBhDA,AvBqEA;AmBxDA,AT2BA,ARwBA,AoB5DA,Af6CA,AgBhDA,AvBqEA;AmBxDA,AT2BA,ARwBA,AoB5DA,Af6CA,AgBhDA,AvBqEA;AmBxDA,AT2BA,ARwBA,AoB5DA,Af6CA,AgBhDA,AvBqEA;AmBxDA,AT2BA,ARwBA,AoB5DA,Af6CA,AgBhDA,AvBqEA;AmBxDA,AT2BA,ARwBA,AoB5DA,Af6CA,AgBhDA,AvBqEA;AmBxDA,AT2BA,ARwBA,AoB5DA,Af6CA,AgBhDA,AvBqEA;AmBxDA,AT2BA,ARwBA,AoB5DA,Af6CA,AgBhDA,AvBqEA;AmBxDA,AT2BA,ARwBA,AKfA,AgBhDA,AvBqEA;AmBxDA,AT2BA,AHSA,AgBhDA,AvBqEA;AmBxDA,AT2BA,AavCA,AvBqEA;AmBxDA,AT2BA,AavCA,AvBqEA;AmBxDA,AT2BA,AavCA,AvBqEA;AmBxDA,AT2BA,AavCA,AvBqEA;AmBxDA,AT2BA,AavCA,AvBqEA;AmBxDA,AT2BA,AavCA,AvBqEA;AmBxDA,AT2BA,AavCA,AvBqEA;AmBxDA,AT2BA,AavCA,AvBqEA;AmBxDA,AT2BA,AavCA,AvBqEA;AmBxDA,AT2BA,AavCA,AvBqEA;AmBxDA,AT2BA,AavCA,AvBqEA;AmBxDA,AT2BA,AavCA,AvBqEA;AmBxDA,AT2BA,AavCA,AvBqEA;AmBxDA,AT2BA,AavCA,AvBqEA;AmBxDA,AT2BA,AavCA,AvBqEA;AmBxDA,AT2BA,AavCA,AvBqEA;AmBxDA,AT2BA,AavCA,AvBqEA;AmBxDA,AT2BA,AavCA,AvBqEA;AmBxDA,AT2BA,AavCA,AvBqEA;AmBxDA,AT2BA,AV8BA;AmBxDA,AT2BA,AV8BA;AmBxDA,AT2BA,AV8BA;AmBxDA,AT2BA,AV8BA;AmBxDA,AT2BA,AV8BA;AmBxDA,AT2BA,AV8BA;AmBxDA,AT2BA,AV8BA;AmBxDA,AT2BA,AV8BA;AmBxDA,AT2BA,AV8BA;AmBxDA,AT2BA,AV8BA;AmBxDA,AT2BA,AV8BA;AmBxDA,AT2BA,AV8BA;AmBxDA,AT2BA,AV8BA;AmBxDA,AT2BA,AV8BA;AmBxDA,AT2BA,AV8BA;AmBxDA,AT2BA,AV8BA;AmBxDA,AT2BA,AV8BA;AmBxDA,AT2BA,AV8BA;AmBxDA,AT2BA,AV8BA;AmBxDA,AT2BA,AV8BA;AmBxDA,AT2BA,AV8BA;AmBxDA,AT2BA,AV8BA;AmBxDA,AT2BA,AV8BA;AmBxDA,AT2BA,AV8BA;AmBxDA,AT2BA,AV8BA;AmBxDA,AT2BA,AV8BA;AmBxDA,AT2BA,AV8BA;AmBxDA,AT2BA,AV8BA;AmBxDA,AT2BA,AV8BA;AmBxDA,AT2BA,AV8BA;AmBxDA,AT2BA,AV8BA;AmBxDA,AT2BA,AV8BA;AmBxDA,AT2BA,AV8BA;AmBxDA,AT2BA,AV8BA;AmBxDA,AT2BA,AV8BA;AmBxDA,AT2BA,AV8BA;AmBxDA,AT2BA,AV8BA;AmBxDA,AT2BA,AV8BA;AmBxDA,AT2BA,AV8BA;AmBxDA,AT2BA,AV8BA;AmBxDA,AT2BA,AV8BA;AmBxDA,AT2BA,AV8BA;AmBxDA,AT2BA,AV8BA;AmBxDA,AT2BA,AV8BA;AmBxDA,AT2BA,AV8BA;AmBxDA,AnByDA;AmBxDA,AnByDA;AmBxDA,AnByDA;AmBxDA,AnByDA;AmBxDA,AnByDA;AmBxDA,AnByDA;AmBxDA,AnByDA;AmBxDA,AnByDA;AmBxDA,AnByDA;AmBxDA,AnByDA;AmBxDA,AnByDA;AmBxDA,AnByDA;AmBxDA,AnByDA;AmBxDA,AnByDA;AmBxDA,AnByDA;AmBxDA,AnByDA;AmBxDA,AnByDA;AmBxDA,AnByDA;AmBxDA,AnByDA;AmBxDA,AnByDA;AmBxDA,AnByDA;AmBxDA,AnByDA;AmBxDA,AnByDA;AmBxDA,AnByDA;AmBxDA,AnByDA;AmBxDA,AnByDA;AmBxDA,AnByDA;AmBxDA,AnByDA;AmBxDA,AnByDA;AmBxDA,AnByDA;AmBxDA,AnByDA;AmBxDA,AnByDA;AmBxDA,AnByDA;AmBxDA,AnByDA;AmBxDA,AnByDA;AmBxDA,AnByDA;AmBxDA,AnByDA;AmBxDA,AnByDA;AmBxDA,AnByDA;AmBxDA,AnByDA;AmBxDA,AnByDA;AmBxDA,AnByDA;AmBxDA,AnByDA;AmBxDA,AnByDA;AmBxDA,AnByDA;AmBxDA,AnByDA;AmBxDA,AnByDA;AmBxDA,AnByDA;AmBxDA,AnByDA;AmBxDA,AnByDA;AmBxDA,AnByDA;AmBxDA,AnByDA;AmBxDA,AnByDA;AmBxDA,AnByDA;AmBxDA,AnByDA;AmBxDA,AnByDA;AmBxDA,AnByDA;AmBxDA,AnByDA;AmBxDA,AnByDA;AmBxDA,AnByDA;AmBxDA,AnByDA;AmBxDA,AnByDA;AmBxDA,AnByDA;AmBxDA,AnByDA;AmBxDA,AnByDA;AmBxDA,AnByDA;AmBxDA,AnByDA;AmBxDA,AnByDA;AmBxDA,AnByDA;AmBxDA,AnByDA;AmBxDA,AnByDA;AmBxDA,AnByDA;AmBxDA,AnByDA;AmBxDA,AnByDA;AmBxDA,AnByDA;AmBxDA,AnByDA;AmBxDA,AnByDA;AmBxDA,AnByDA;AmBxDA,AnByDA;AmBxDA,AnByDA;AmBxDA,AnByDA;AmBxDA,AnByDA;AmBxDA,AnByDA;AmBxDA,AnByDA;AmBxDA,AnByDA;AmBxDA,AnByDA;AmBxDA,AnByDA;AmBxDA,AnByDA;AmBxDA,AnByDA;AmBxDA,AnByDA;AmBxDA,AnByDA;AmBxDA,AnByDA;AmBxDA,AnByDA;AmBxDA,AnByDA;AmBxDA,AnByDA;AmBxDA,AnByDA;AmBxDA,AnByDA;AmBxDA,AnByDA;AmBxDA,AnByDA;AmBxDA,AnByDA;AmBxDA,AnByDA;AmBxDA,AnByDA;AmBxDA,AnByDA;AmBxDA,AnByDA;AmBxDA,AnByDA;AmBxDA,AnByDA;AmBxDA,AnByDA;AmBxDA,AnByDA;AmBxDA,AnByDA;AmBxDA,AnByDA;AmBxDA,AnByDA;AmBxDA,AnByDA;AmBxDA,AnByDA;AmBxDA,AnByDA;AmBxDA,AnByDA;AmBxDA,AnByDA;AmBxDA,AnByDA;AmBxDA,AnByDA;AmBxDA,AnByDA;AmBxDA,AnByDA;AmBxDA,AnByDA;AmBxDA,AnByDA;AmBxDA,AnByDA;AmBxDA,AnByDA;AmBxDA,AnByDA;AmBxDA,AnByDA;AmBxDA,AnByDA;AmBxDA,AnByDA;AmBxDA,AnByDA;AmBxDA,AnByDA;AmBxDA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.js", "sourcesContent": ["module.exports = require('./lib/axios');", "\n\nvar utils = require('./utils');\nvar bind = require('./helpers/bind');\nvar Axios = require('./core/Axios');\nvar mergeConfig = require('./core/mergeConfig');\nvar defaults = require('./defaults');\n\n/**\n * Create an instance of Axios\n *\n * @param {Object} defaultConfig The default config for the instance\n * @return {Axios} A new instance of Axios\n */\nfunction createInstance(defaultConfig) {\n  var context = new Axios(defaultConfig);\n  var instance = bind(Axios.prototype.request, context);\n\n  // Copy axios.prototype to instance\n  utils.extend(instance, Axios.prototype, context);\n\n  // Copy context to instance\n  utils.extend(instance, context);\n\n  return instance;\n}\n\n// Create the default instance to be exported\nvar axios = createInstance(defaults);\n\n// Expose Axios class to allow class inheritance\naxios.Axios = Axios;\n\n// Factory for creating new instances\naxios.create = function create(instanceConfig) {\n  return createInstance(mergeConfig(axios.defaults, instanceConfig));\n};\n\n// Expose Cancel & CancelToken\naxios.Cancel = require('./cancel/Cancel');\naxios.CancelToken = require('./cancel/CancelToken');\naxios.isCancel = require('./cancel/isCancel');\n\n// Expose all/spread\naxios.all = function all(promises) {\n  return Promise.all(promises);\n};\naxios.spread = require('./helpers/spread');\n\n// Expose isAxiosError\naxios.isAxiosError = require('./helpers/isAxiosError');\n\nmodule.exports = axios;\n\n// Allow use of default import syntax in TypeScript\nmodule.exports.default = axios;\n", "\n\nvar bind = require('./helpers/bind');\n\n// utils is a library of generic helper functions non-specific to axios\n\nvar toString = Object.prototype.toString;\n\n/**\n * Determine if a value is an Array\n *\n * @param {Object} val The value to test\n * @returns {boolean} True if value is an Array, otherwise false\n */\nfunction isArray(val) {\n  return toString.call(val) === '[object Array]';\n}\n\n/**\n * Determine if a value is undefined\n *\n * @param {Object} val The value to test\n * @returns {boolean} True if the value is undefined, otherwise false\n */\nfunction isUndefined(val) {\n  return typeof val === 'undefined';\n}\n\n/**\n * Determine if a value is a Buffer\n *\n * @param {Object} val The value to test\n * @returns {boolean} True if value is a Buffer, otherwise false\n */\nfunction isBuffer(val) {\n  return val !== null && !isUndefined(val) && val.constructor !== null && !isUndefined(val.constructor)\n    && typeof val.constructor.isBuffer === 'function' && val.constructor.isBuffer(val);\n}\n\n/**\n * Determine if a value is an ArrayBuffer\n *\n * @param {Object} val The value to test\n * @returns {boolean} True if value is an ArrayBuffer, otherwise false\n */\nfunction isArrayBuffer(val) {\n  return toString.call(val) === '[object ArrayBuffer]';\n}\n\n/**\n * Determine if a value is a FormData\n *\n * @param {Object} val The value to test\n * @returns {boolean} True if value is an FormData, otherwise false\n */\nfunction isFormData(val) {\n  return (typeof FormData !== 'undefined') && (val instanceof FormData);\n}\n\n/**\n * Determine if a value is a view on an ArrayBuffer\n *\n * @param {Object} val The value to test\n * @returns {boolean} True if value is a view on an ArrayBuffer, otherwise false\n */\nfunction isArrayBufferView(val) {\n  var result;\n  if ((typeof ArrayBuffer !== 'undefined') && (ArrayBuffer.isView)) {\n    result = ArrayBuffer.isView(val);\n  } else {\n    result = (val) && (val.buffer) && (val.buffer instanceof ArrayBuffer);\n  }\n  return result;\n}\n\n/**\n * Determine if a value is a String\n *\n * @param {Object} val The value to test\n * @returns {boolean} True if value is a String, otherwise false\n */\nfunction isString(val) {\n  return typeof val === 'string';\n}\n\n/**\n * Determine if a value is a Number\n *\n * @param {Object} val The value to test\n * @returns {boolean} True if value is a Number, otherwise false\n */\nfunction isNumber(val) {\n  return typeof val === 'number';\n}\n\n/**\n * Determine if a value is an Object\n *\n * @param {Object} val The value to test\n * @returns {boolean} True if value is an Object, otherwise false\n */\nfunction isObject(val) {\n  return val !== null && typeof val === 'object';\n}\n\n/**\n * Determine if a value is a plain Object\n *\n * @param {Object} val The value to test\n * @return {boolean} True if value is a plain Object, otherwise false\n */\nfunction isPlainObject(val) {\n  if (toString.call(val) !== '[object Object]') {\n    return false;\n  }\n\n  var prototype = Object.getPrototypeOf(val);\n  return prototype === null || prototype === Object.prototype;\n}\n\n/**\n * Determine if a value is a Date\n *\n * @param {Object} val The value to test\n * @returns {boolean} True if value is a Date, otherwise false\n */\nfunction isDate(val) {\n  return toString.call(val) === '[object Date]';\n}\n\n/**\n * Determine if a value is a File\n *\n * @param {Object} val The value to test\n * @returns {boolean} True if value is a File, otherwise false\n */\nfunction isFile(val) {\n  return toString.call(val) === '[object File]';\n}\n\n/**\n * Determine if a value is a Blob\n *\n * @param {Object} val The value to test\n * @returns {boolean} True if value is a Blob, otherwise false\n */\nfunction isBlob(val) {\n  return toString.call(val) === '[object Blob]';\n}\n\n/**\n * Determine if a value is a Function\n *\n * @param {Object} val The value to test\n * @returns {boolean} True if value is a Function, otherwise false\n */\nfunction isFunction(val) {\n  return toString.call(val) === '[object Function]';\n}\n\n/**\n * Determine if a value is a Stream\n *\n * @param {Object} val The value to test\n * @returns {boolean} True if value is a Stream, otherwise false\n */\nfunction isStream(val) {\n  return isObject(val) && isFunction(val.pipe);\n}\n\n/**\n * Determine if a value is a URLSearchParams object\n *\n * @param {Object} val The value to test\n * @returns {boolean} True if value is a URLSearchParams object, otherwise false\n */\nfunction isURLSearchParams(val) {\n  return typeof URLSearchParams !== 'undefined' && val instanceof URLSearchParams;\n}\n\n/**\n * Trim excess whitespace off the beginning and end of a string\n *\n * @param {String} str The String to trim\n * @returns {String} The String freed of excess whitespace\n */\nfunction trim(str) {\n  return str.trim ? str.trim() : str.replace(/^\\s+|\\s+$/g, '');\n}\n\n/**\n * Determine if we're running in a standard browser environment\n *\n * This allows axios to run in a web worker, and react-native.\n * Both environments support XMLHttpRequest, but not fully standard globals.\n *\n * web workers:\n *  typeof window -> undefined\n *  typeof document -> undefined\n *\n * react-native:\n *  navigator.product -> 'ReactNative'\n * nativescript\n *  navigator.product -> 'NativeScript' or 'NS'\n */\nfunction isStandardBrowserEnv() {\n  if (typeof navigator !== 'undefined' && (navigator.product === 'ReactNative' ||\n                                           navigator.product === 'NativeScript' ||\n                                           navigator.product === 'NS')) {\n    return false;\n  }\n  return (\n    typeof window !== 'undefined' &&\n    typeof document !== 'undefined'\n  );\n}\n\n/**\n * Iterate over an Array or an Object invoking a function for each item.\n *\n * If `obj` is an Array callback will be called passing\n * the value, index, and complete array for each item.\n *\n * If 'obj' is an Object callback will be called passing\n * the value, key, and complete object for each property.\n *\n * @param {Object|Array} obj The object to iterate\n * @param {Function} fn The callback to invoke for each item\n */\nfunction forEach(obj, fn) {\n  // Don't bother if no value provided\n  if (obj === null || typeof obj === 'undefined') {\n    return;\n  }\n\n  // Force an array if not already something iterable\n  if (typeof obj !== 'object') {\n    /*eslint no-param-reassign:0*/\n    obj = [obj];\n  }\n\n  if (isArray(obj)) {\n    // Iterate over array values\n    for (var i = 0, l = obj.length; i < l; i++) {\n      fn.call(null, obj[i], i, obj);\n    }\n  } else {\n    // Iterate over object keys\n    for (var key in obj) {\n      if (Object.prototype.hasOwnProperty.call(obj, key)) {\n        fn.call(null, obj[key], key, obj);\n      }\n    }\n  }\n}\n\n/**\n * Accepts varargs expecting each argument to be an object, then\n * immutably merges the properties of each object and returns result.\n *\n * When multiple objects contain the same key the later object in\n * the arguments list will take precedence.\n *\n * Example:\n *\n * ```js\n * var result = merge({foo: 123}, {foo: 456});\n * console.log(result.foo); // outputs 456\n * ```\n *\n * @param {Object} obj1 Object to merge\n * @returns {Object} Result of all merge properties\n */\nfunction merge(/* obj1, obj2, obj3, ... */) {\n  var result = {};\n  function assignValue(val, key) {\n    if (isPlainObject(result[key]) && isPlainObject(val)) {\n      result[key] = merge(result[key], val);\n    } else if (isPlainObject(val)) {\n      result[key] = merge({}, val);\n    } else if (isArray(val)) {\n      result[key] = val.slice();\n    } else {\n      result[key] = val;\n    }\n  }\n\n  for (var i = 0, l = arguments.length; i < l; i++) {\n    forEach(arguments[i], assignValue);\n  }\n  return result;\n}\n\n/**\n * Extends object a by mutably adding to it the properties of object b.\n *\n * @param {Object} a The object to be extended\n * @param {Object} b The object to copy properties from\n * @param {Object} thisArg The object to bind function to\n * @return {Object} The resulting value of object a\n */\nfunction extend(a, b, thisArg) {\n  forEach(b, function assignValue(val, key) {\n    if (thisArg && typeof val === 'function') {\n      a[key] = bind(val, thisArg);\n    } else {\n      a[key] = val;\n    }\n  });\n  return a;\n}\n\n/**\n * Remove byte order marker. This catches EF BB BF (the UTF-8 BOM)\n *\n * @param {string} content with BOM\n * @return {string} content value without BOM\n */\nfunction stripBOM(content) {\n  if (content.charCodeAt(0) === 0xFEFF) {\n    content = content.slice(1);\n  }\n  return content;\n}\n\nmodule.exports = {\n  isArray: isArray,\n  isArrayBuffer: isArrayBuffer,\n  isBuffer: isBuffer,\n  isFormData: isFormData,\n  isArrayBufferView: isArrayBufferView,\n  isString: isString,\n  isNumber: isNumber,\n  isObject: isObject,\n  isPlainObject: isPlainObject,\n  isUndefined: isUndefined,\n  isDate: isDate,\n  isFile: isFile,\n  isBlob: isBlob,\n  isFunction: isFunction,\n  isStream: isStream,\n  isURLSearchParams: isURLSearchParams,\n  isStandardBrowserEnv: isStandardBrowserEnv,\n  forEach: forEach,\n  merge: merge,\n  extend: extend,\n  trim: trim,\n  stripBOM: stripBOM\n};\n", "\n\nmodule.exports = function bind(fn, thisArg) {\n  return function wrap() {\n    var args = new Array(arguments.length);\n    for (var i = 0; i < args.length; i++) {\n      args[i] = arguments[i];\n    }\n    return fn.apply(thisArg, args);\n  };\n};\n", "\n\nvar utils = require('./../utils');\nvar buildURL = require('../helpers/buildURL');\nvar InterceptorManager = require('./InterceptorManager');\nvar dispatchRequest = require('./dispatchRequest');\nvar mergeConfig = require('./mergeConfig');\nvar validator = require('../helpers/validator');\n\nvar validators = validator.validators;\n/**\n * Create a new instance of Axios\n *\n * @param {Object} instanceConfig The default config for the instance\n */\nfunction Axios(instanceConfig) {\n  this.defaults = instanceConfig;\n  this.interceptors = {\n    request: new InterceptorManager(),\n    response: new InterceptorManager()\n  };\n}\n\n/**\n * Dispatch a request\n *\n * @param {Object} config The config specific for this request (merged with this.defaults)\n */\nAxios.prototype.request = function request(config) {\n  /*eslint no-param-reassign:0*/\n  // Allow for axios('example/url'[, config]) a la fetch API\n  if (typeof config === 'string') {\n    config = arguments[1] || {};\n    config.url = arguments[0];\n  } else {\n    config = config || {};\n  }\n\n  config = mergeConfig(this.defaults, config);\n\n  // Set config.method\n  if (config.method) {\n    config.method = config.method.toLowerCase();\n  } else if (this.defaults.method) {\n    config.method = this.defaults.method.toLowerCase();\n  } else {\n    config.method = 'get';\n  }\n\n  var transitional = config.transitional;\n\n  if (transitional !== undefined) {\n    validator.assertOptions(transitional, {\n      silentJSONParsing: validators.transitional(validators.boolean, '1.0.0'),\n      forcedJSONParsing: validators.transitional(validators.boolean, '1.0.0'),\n      clarifyTimeoutError: validators.transitional(validators.boolean, '1.0.0')\n    }, false);\n  }\n\n  // filter out skipped interceptors\n  var requestInterceptorChain = [];\n  var synchronousRequestInterceptors = true;\n  this.interceptors.request.forEach(function unshiftRequestInterceptors(interceptor) {\n    if (typeof interceptor.runWhen === 'function' && interceptor.runWhen(config) === false) {\n      return;\n    }\n\n    synchronousRequestInterceptors = synchronousRequestInterceptors && interceptor.synchronous;\n\n    requestInterceptorChain.unshift(interceptor.fulfilled, interceptor.rejected);\n  });\n\n  var responseInterceptorChain = [];\n  this.interceptors.response.forEach(function pushResponseInterceptors(interceptor) {\n    responseInterceptorChain.push(interceptor.fulfilled, interceptor.rejected);\n  });\n\n  var promise;\n\n  if (!synchronousRequestInterceptors) {\n    var chain = [dispatchRequest, undefined];\n\n    Array.prototype.unshift.apply(chain, requestInterceptorChain);\n    chain = chain.concat(responseInterceptorChain);\n\n    promise = Promise.resolve(config);\n    while (chain.length) {\n      promise = promise.then(chain.shift(), chain.shift());\n    }\n\n    return promise;\n  }\n\n\n  var newConfig = config;\n  while (requestInterceptorChain.length) {\n    var onFulfilled = requestInterceptorChain.shift();\n    var onRejected = requestInterceptorChain.shift();\n    try {\n      newConfig = onFulfilled(newConfig);\n    } catch (error) {\n      onRejected(error);\n      break;\n    }\n  }\n\n  try {\n    promise = dispatchRequest(newConfig);\n  } catch (error) {\n    return Promise.reject(error);\n  }\n\n  while (responseInterceptorChain.length) {\n    promise = promise.then(responseInterceptorChain.shift(), responseInterceptorChain.shift());\n  }\n\n  return promise;\n};\n\nAxios.prototype.getUri = function getUri(config) {\n  config = mergeConfig(this.defaults, config);\n  return buildURL(config.url, config.params, config.paramsSerializer).replace(/^\\?/, '');\n};\n\n// Provide aliases for supported request methods\nutils.forEach(['delete', 'get', 'head', 'options'], function forEachMethodNoData(method) {\n  /*eslint func-names:0*/\n  Axios.prototype[method] = function(url, config) {\n    return this.request(mergeConfig(config || {}, {\n      method: method,\n      url: url,\n      data: (config || {}).data\n    }));\n  };\n});\n\nutils.forEach(['post', 'put', 'patch'], function forEachMethodWithData(method) {\n  /*eslint func-names:0*/\n  Axios.prototype[method] = function(url, data, config) {\n    return this.request(mergeConfig(config || {}, {\n      method: method,\n      url: url,\n      data: data\n    }));\n  };\n});\n\nmodule.exports = Axios;\n", "\n\nvar utils = require('./../utils');\n\nfunction encode(val) {\n  return encodeURIComponent(val).\n    replace(/%3A/gi, ':').\n    replace(/%24/g, '$').\n    replace(/%2C/gi, ',').\n    replace(/%20/g, '+').\n    replace(/%5B/gi, '[').\n    replace(/%5D/gi, ']');\n}\n\n/**\n * Build a URL by appending params to the end\n *\n * @param {string} url The base of the url (e.g., http://www.google.com)\n * @param {object} [params] The params to be appended\n * @returns {string} The formatted url\n */\nmodule.exports = function buildURL(url, params, paramsSerializer) {\n  /*eslint no-param-reassign:0*/\n  if (!params) {\n    return url;\n  }\n\n  var serializedParams;\n  if (paramsSerializer) {\n    serializedParams = paramsSerializer(params);\n  } else if (utils.isURLSearchParams(params)) {\n    serializedParams = params.toString();\n  } else {\n    var parts = [];\n\n    utils.forEach(params, function serialize(val, key) {\n      if (val === null || typeof val === 'undefined') {\n        return;\n      }\n\n      if (utils.isArray(val)) {\n        key = key + '[]';\n      } else {\n        val = [val];\n      }\n\n      utils.forEach(val, function parseValue(v) {\n        if (utils.isDate(v)) {\n          v = v.toISOString();\n        } else if (utils.isObject(v)) {\n          v = JSON.stringify(v);\n        }\n        parts.push(encode(key) + '=' + encode(v));\n      });\n    });\n\n    serializedParams = parts.join('&');\n  }\n\n  if (serializedParams) {\n    var hashmarkIndex = url.indexOf('#');\n    if (hashmarkIndex !== -1) {\n      url = url.slice(0, hashmarkIndex);\n    }\n\n    url += (url.indexOf('?') === -1 ? '?' : '&') + serializedParams;\n  }\n\n  return url;\n};\n", "\n\nvar utils = require('./../utils');\n\nfunction InterceptorManager() {\n  this.handlers = [];\n}\n\n/**\n * Add a new interceptor to the stack\n *\n * @param {Function} fulfilled The function to handle `then` for a `Promise`\n * @param {Function} rejected The function to handle `reject` for a `Promise`\n *\n * @return {Number} An ID used to remove interceptor later\n */\nInterceptorManager.prototype.use = function use(fulfilled, rejected, options) {\n  this.handlers.push({\n    fulfilled: fulfilled,\n    rejected: rejected,\n    synchronous: options ? options.synchronous : false,\n    runWhen: options ? options.runWhen : null\n  });\n  return this.handlers.length - 1;\n};\n\n/**\n * Remove an interceptor from the stack\n *\n * @param {Number} id The ID that was returned by `use`\n */\nInterceptorManager.prototype.eject = function eject(id) {\n  if (this.handlers[id]) {\n    this.handlers[id] = null;\n  }\n};\n\n/**\n * Iterate over all the registered interceptors\n *\n * This method is particularly useful for skipping over any\n * interceptors that may have become `null` calling `eject`.\n *\n * @param {Function} fn The function to call for each interceptor\n */\nInterceptorManager.prototype.forEach = function forEach(fn) {\n  utils.forEach(this.handlers, function forEachHandler(h) {\n    if (h !== null) {\n      fn(h);\n    }\n  });\n};\n\nmodule.exports = InterceptorManager;\n", "\n\nvar utils = require('./../utils');\nvar transformData = require('./transformData');\nvar isCancel = require('../cancel/isCancel');\nvar defaults = require('../defaults');\n\n/**\n * Throws a `Cancel` if cancellation has been requested.\n */\nfunction throwIfCancellationRequested(config) {\n  if (config.cancelToken) {\n    config.cancelToken.throwIfRequested();\n  }\n}\n\n/**\n * Dispatch a request to the server using the configured adapter.\n *\n * @param {object} config The config that is to be used for the request\n * @returns {Promise} The Promise to be fulfilled\n */\nmodule.exports = function dispatchRequest(config) {\n  throwIfCancellationRequested(config);\n\n  // Ensure headers exist\n  config.headers = config.headers || {};\n\n  // Transform request data\n  config.data = transformData.call(\n    config,\n    config.data,\n    config.headers,\n    config.transformRequest\n  );\n\n  // Flatten headers\n  config.headers = utils.merge(\n    config.headers.common || {},\n    config.headers[config.method] || {},\n    config.headers\n  );\n\n  utils.forEach(\n    ['delete', 'get', 'head', 'post', 'put', 'patch', 'common'],\n    function cleanHeaderConfig(method) {\n      delete config.headers[method];\n    }\n  );\n\n  var adapter = config.adapter || defaults.adapter;\n\n  return adapter(config).then(function onAdapterResolution(response) {\n    throwIfCancellationRequested(config);\n\n    // Transform response data\n    response.data = transformData.call(\n      config,\n      response.data,\n      response.headers,\n      config.transformResponse\n    );\n\n    return response;\n  }, function onAdapterRejection(reason) {\n    if (!isCancel(reason)) {\n      throwIfCancellationRequested(config);\n\n      // Transform response data\n      if (reason && reason.response) {\n        reason.response.data = transformData.call(\n          config,\n          reason.response.data,\n          reason.response.headers,\n          config.transformResponse\n        );\n      }\n    }\n\n    return Promise.reject(reason);\n  });\n};\n", "\n\nvar utils = require('./../utils');\nvar defaults = require('./../defaults');\n\n/**\n * Transform the data for a request or a response\n *\n * @param {Object|String} data The data to be transformed\n * @param {Array} headers The headers for the request or response\n * @param {Array|Function} fns A single function or Array of functions\n * @returns {*} The resulting transformed data\n */\nmodule.exports = function transformData(data, headers, fns) {\n  var context = this || defaults;\n  /*eslint no-param-reassign:0*/\n  utils.forEach(fns, function transform(fn) {\n    data = fn.call(context, data, headers);\n  });\n\n  return data;\n};\n", "\n\nvar utils = require('./utils');\nvar normalizeHeaderName = require('./helpers/normalizeHeaderName');\nvar enhanceError = require('./core/enhanceError');\n\nvar DEFAULT_CONTENT_TYPE = {\n  'Content-Type': 'application/x-www-form-urlencoded'\n};\n\nfunction setContentTypeIfUnset(headers, value) {\n  if (!utils.isUndefined(headers) && utils.isUndefined(headers['Content-Type'])) {\n    headers['Content-Type'] = value;\n  }\n}\n\nfunction getDefaultAdapter() {\n  var adapter;\n  if (typeof XMLHttpRequest !== 'undefined') {\n    // For browsers use XHR adapter\n    adapter = require('./adapters/xhr');\n  } else if (typeof process !== 'undefined' && Object.prototype.toString.call(process) === '[object process]') {\n    // For node use HTTP adapter\n    adapter = require('./adapters/http');\n  }\n  return adapter;\n}\n\nfunction stringifySafely(rawValue, parser, encoder) {\n  if (utils.isString(rawValue)) {\n    try {\n      (parser || JSON.parse)(rawValue);\n      return utils.trim(rawValue);\n    } catch (e) {\n      if (e.name !== 'SyntaxError') {\n        throw e;\n      }\n    }\n  }\n\n  return (encoder || JSON.stringify)(rawValue);\n}\n\nvar defaults = {\n\n  transitional: {\n    silentJSONParsing: true,\n    forcedJSONParsing: true,\n    clarifyTimeoutError: false\n  },\n\n  adapter: getDefaultAdapter(),\n\n  transformRequest: [function transformRequest(data, headers) {\n    normalizeHeaderName(headers, 'Accept');\n    normalizeHeaderName(headers, 'Content-Type');\n\n    if (utils.isFormData(data) ||\n      utils.isArrayBuffer(data) ||\n      utils.isBuffer(data) ||\n      utils.isStream(data) ||\n      utils.isFile(data) ||\n      utils.isBlob(data)\n    ) {\n      return data;\n    }\n    if (utils.isArrayBufferView(data)) {\n      return data.buffer;\n    }\n    if (utils.isURLSearchParams(data)) {\n      setContentTypeIfUnset(headers, 'application/x-www-form-urlencoded;charset=utf-8');\n      return data.toString();\n    }\n    if (utils.isObject(data) || (headers && headers['Content-Type'] === 'application/json')) {\n      setContentTypeIfUnset(headers, 'application/json');\n      return stringifySafely(data);\n    }\n    return data;\n  }],\n\n  transformResponse: [function transformResponse(data) {\n    var transitional = this.transitional;\n    var silentJSONParsing = transitional && transitional.silentJSONParsing;\n    var forcedJSONParsing = transitional && transitional.forcedJSONParsing;\n    var strictJSONParsing = !silentJSONParsing && this.responseType === 'json';\n\n    if (strictJSONParsing || (forcedJSONParsing && utils.isString(data) && data.length)) {\n      try {\n        return JSON.parse(data);\n      } catch (e) {\n        if (strictJSONParsing) {\n          if (e.name === 'SyntaxError') {\n            throw enhanceError(e, this, 'E_JSON_PARSE');\n          }\n          throw e;\n        }\n      }\n    }\n\n    return data;\n  }],\n\n  /**\n   * A timeout in milliseconds to abort a request. If set to 0 (default) a\n   * timeout is not created.\n   */\n  timeout: 0,\n\n  xsrfCookieName: 'XSRF-TOKEN',\n  xsrfHeaderName: 'X-XSRF-TOKEN',\n\n  maxContentLength: -1,\n  maxBodyLength: -1,\n\n  validateStatus: function validateStatus(status) {\n    return status >= 200 && status < 300;\n  }\n};\n\ndefaults.headers = {\n  common: {\n    'Accept': 'application/json, text/plain, */*'\n  }\n};\n\nutils.forEach(['delete', 'get', 'head'], function forEachMethodNoData(method) {\n  defaults.headers[method] = {};\n});\n\nutils.forEach(['post', 'put', 'patch'], function forEachMethodWithData(method) {\n  defaults.headers[method] = utils.merge(DEFAULT_CONTENT_TYPE);\n});\n\nmodule.exports = defaults;\n", "\n\nvar utils = require('../utils');\n\nmodule.exports = function normalizeHeaderName(headers, normalizedName) {\n  utils.forEach(headers, function processHeader(value, name) {\n    if (name !== normalizedName && name.toUpperCase() === normalizedName.toUpperCase()) {\n      headers[normalizedName] = value;\n      delete headers[name];\n    }\n  });\n};\n", "\n\n/**\n * Update an Error with the specified config, error code, and response.\n *\n * @param {Error} error The error to update.\n * @param {Object} config The config.\n * @param {string} [code] The error code (for example, 'ECONNABORTED').\n * @param {Object} [request] The request.\n * @param {Object} [response] The response.\n * @returns {Error} The error.\n */\nmodule.exports = function enhanceError(error, config, code, request, response) {\n  error.config = config;\n  if (code) {\n    error.code = code;\n  }\n\n  error.request = request;\n  error.response = response;\n  error.isAxiosError = true;\n\n  error.toJSON = function toJSON() {\n    return {\n      // Standard\n      message: this.message,\n      name: this.name,\n      // Microsoft\n      description: this.description,\n      number: this.number,\n      // Mozilla\n      fileName: this.fileName,\n      lineNumber: this.lineNumber,\n      columnNumber: this.columnNumber,\n      stack: this.stack,\n      // Axios\n      config: this.config,\n      code: this.code\n    };\n  };\n  return error;\n};\n", "\n\nvar utils = require('./../utils');\nvar settle = require('./../core/settle');\nvar cookies = require('./../helpers/cookies');\nvar buildURL = require('./../helpers/buildURL');\nvar buildFullPath = require('../core/buildFullPath');\nvar parseHeaders = require('./../helpers/parseHeaders');\nvar isURLSameOrigin = require('./../helpers/isURLSameOrigin');\nvar createError = require('../core/createError');\n\nmodule.exports = function xhrAdapter(config) {\n  return new Promise(function dispatchXhrRequest(resolve, reject) {\n    var requestData = config.data;\n    var requestHeaders = config.headers;\n    var responseType = config.responseType;\n\n    if (utils.isFormData(requestData)) {\n      delete requestHeaders['Content-Type']; // Let the browser set it\n    }\n\n    var request = new XMLHttpRequest();\n\n    // HTTP basic authentication\n    if (config.auth) {\n      var username = config.auth.username || '';\n      var password = config.auth.password ? unescape(encodeURIComponent(config.auth.password)) : '';\n      requestHeaders.Authorization = 'Basic ' + btoa(username + ':' + password);\n    }\n\n    var fullPath = buildFullPath(config.baseURL, config.url);\n    request.open(config.method.toUpperCase(), buildURL(fullPath, config.params, config.paramsSerializer), true);\n\n    // Set the request timeout in MS\n    request.timeout = config.timeout;\n\n    function onloadend() {\n      if (!request) {\n        return;\n      }\n      // Prepare the response\n      var responseHeaders = 'getAllResponseHeaders' in request ? parseHeaders(request.getAllResponseHeaders()) : null;\n      var responseData = !responseType || responseType === 'text' ||  responseType === 'json' ?\n        request.responseText : request.response;\n      var response = {\n        data: responseData,\n        status: request.status,\n        statusText: request.statusText,\n        headers: responseHeaders,\n        config: config,\n        request: request\n      };\n\n      settle(resolve, reject, response);\n\n      // Clean up request\n      request = null;\n    }\n\n    if ('onloadend' in request) {\n      // Use onloadend if available\n      request.onloadend = onloadend;\n    } else {\n      // Listen for ready state to emulate onloadend\n      request.onreadystatechange = function handleLoad() {\n        if (!request || request.readyState !== 4) {\n          return;\n        }\n\n        // The request errored out and we didn't get a response, this will be\n        // handled by onerror instead\n        // With one exception: request that using file: protocol, most browsers\n        // will return status as 0 even though it's a successful request\n        if (request.status === 0 && !(request.responseURL && request.responseURL.indexOf('file:') === 0)) {\n          return;\n        }\n        // readystate handler is calling before onerror or ontimeout handlers,\n        // so we should call onloadend on the next 'tick'\n        setTimeout(onloadend);\n      };\n    }\n\n    // Handle browser request cancellation (as opposed to a manual cancellation)\n    request.onabort = function handleAbort() {\n      if (!request) {\n        return;\n      }\n\n      reject(createError('Request aborted', config, 'ECONNABORTED', request));\n\n      // Clean up request\n      request = null;\n    };\n\n    // Handle low level network errors\n    request.onerror = function handleError() {\n      // Real errors are hidden from us by the browser\n      // onerror should only fire if it's a network error\n      reject(createError('Network Error', config, null, request));\n\n      // Clean up request\n      request = null;\n    };\n\n    // Handle timeout\n    request.ontimeout = function handleTimeout() {\n      var timeoutErrorMessage = 'timeout of ' + config.timeout + 'ms exceeded';\n      if (config.timeoutErrorMessage) {\n        timeoutErrorMessage = config.timeoutErrorMessage;\n      }\n      reject(createError(\n        timeoutErrorMessage,\n        config,\n        config.transitional && config.transitional.clarifyTimeoutError ? 'ETIMEDOUT' : 'ECONNABORTED',\n        request));\n\n      // Clean up request\n      request = null;\n    };\n\n    // Add xsrf header\n    // This is only done if running in a standard browser environment.\n    // Specifically not if we're in a web worker, or react-native.\n    if (utils.isStandardBrowserEnv()) {\n      // Add xsrf header\n      var xsrfValue = (config.withCredentials || isURLSameOrigin(fullPath)) && config.xsrfCookieName ?\n        cookies.read(config.xsrfCookieName) :\n        undefined;\n\n      if (xsrfValue) {\n        requestHeaders[config.xsrfHeaderName] = xsrfValue;\n      }\n    }\n\n    // Add headers to the request\n    if ('setRequestHeader' in request) {\n      utils.forEach(requestHeaders, function setRequestHeader(val, key) {\n        if (typeof requestData === 'undefined' && key.toLowerCase() === 'content-type') {\n          // Remove Content-Type if data is undefined\n          delete requestHeaders[key];\n        } else {\n          // Otherwise add header to the request\n          request.setRequestHeader(key, val);\n        }\n      });\n    }\n\n    // Add withCredentials to request if needed\n    if (!utils.isUndefined(config.withCredentials)) {\n      request.withCredentials = !!config.withCredentials;\n    }\n\n    // Add responseType to request if needed\n    if (responseType && responseType !== 'json') {\n      request.responseType = config.responseType;\n    }\n\n    // Handle progress if needed\n    if (typeof config.onDownloadProgress === 'function') {\n      request.addEventListener('progress', config.onDownloadProgress);\n    }\n\n    // Not all browsers support upload events\n    if (typeof config.onUploadProgress === 'function' && request.upload) {\n      request.upload.addEventListener('progress', config.onUploadProgress);\n    }\n\n    if (config.cancelToken) {\n      // Handle cancellation\n      config.cancelToken.promise.then(function onCanceled(cancel) {\n        if (!request) {\n          return;\n        }\n\n        request.abort();\n        reject(cancel);\n        // Clean up request\n        request = null;\n      });\n    }\n\n    if (!requestData) {\n      requestData = null;\n    }\n\n    // Send the request\n    request.send(requestData);\n  });\n};\n", "\n\nvar createError = require('./createError');\n\n/**\n * Resolve or reject a Promise based on response status.\n *\n * @param {Function} resolve A function that resolves the promise.\n * @param {Function} reject A function that rejects the promise.\n * @param {object} response The response.\n */\nmodule.exports = function settle(resolve, reject, response) {\n  var validateStatus = response.config.validateStatus;\n  if (!response.status || !validateStatus || validateStatus(response.status)) {\n    resolve(response);\n  } else {\n    reject(createError(\n      'Request failed with status code ' + response.status,\n      response.config,\n      null,\n      response.request,\n      response\n    ));\n  }\n};\n", "\n\nvar enhanceError = require('./enhanceError');\n\n/**\n * Create an Error with the specified message, config, error code, request and response.\n *\n * @param {string} message The error message.\n * @param {Object} config The config.\n * @param {string} [code] The error code (for example, 'ECONNABORTED').\n * @param {Object} [request] The request.\n * @param {Object} [response] The response.\n * @returns {Error} The created error.\n */\nmodule.exports = function createError(message, config, code, request, response) {\n  var error = new Error(message);\n  return enhanceError(error, config, code, request, response);\n};\n", "\n\nvar utils = require('./../utils');\n\nmodule.exports = (\n  utils.isStandardBrowserEnv() ?\n\n  // Standard browser envs support document.cookie\n    (function standardBrowserEnv() {\n      return {\n        write: function write(name, value, expires, path, domain, secure) {\n          var cookie = [];\n          cookie.push(name + '=' + encodeURIComponent(value));\n\n          if (utils.isNumber(expires)) {\n            cookie.push('expires=' + new Date(expires).toGMTString());\n          }\n\n          if (utils.isString(path)) {\n            cookie.push('path=' + path);\n          }\n\n          if (utils.isString(domain)) {\n            cookie.push('domain=' + domain);\n          }\n\n          if (secure === true) {\n            cookie.push('secure');\n          }\n\n          document.cookie = cookie.join('; ');\n        },\n\n        read: function read(name) {\n          var match = document.cookie.match(new RegExp('(^|;\\\\s*)(' + name + ')=([^;]*)'));\n          return (match ? decodeURIComponent(match[3]) : null);\n        },\n\n        remove: function remove(name) {\n          this.write(name, '', Date.now() - 86400000);\n        }\n      };\n    })() :\n\n  // Non standard browser env (web workers, react-native) lack needed support.\n    (function nonStandardBrowserEnv() {\n      return {\n        write: function write() {},\n        read: function read() { return null; },\n        remove: function remove() {}\n      };\n    })()\n);\n", "\n\nvar isAbsoluteURL = require('../helpers/isAbsoluteURL');\nvar combineURLs = require('../helpers/combineURLs');\n\n/**\n * Creates a new URL by combining the baseURL with the requestedURL,\n * only when the requestedURL is not already an absolute URL.\n * If the requestURL is absolute, this function returns the requestedURL untouched.\n *\n * @param {string} baseURL The base URL\n * @param {string} requestedURL Absolute or relative URL to combine\n * @returns {string} The combined full path\n */\nmodule.exports = function buildFullPath(baseURL, requestedURL) {\n  if (baseURL && !isAbsoluteURL(requestedURL)) {\n    return combineURLs(baseURL, requestedURL);\n  }\n  return requestedURL;\n};\n", "\n\n/**\n * Determines whether the specified URL is absolute\n *\n * @param {string} url The URL to test\n * @returns {boolean} True if the specified URL is absolute, otherwise false\n */\nmodule.exports = function isAbsoluteURL(url) {\n  // A URL is considered absolute if it begins with \"<scheme>://\" or \"//\" (protocol-relative URL).\n  // RFC 3986 defines scheme name as a sequence of characters beginning with a letter and followed\n  // by any combination of letters, digits, plus, period, or hyphen.\n  return /^([a-z][a-z\\d\\+\\-\\.]*:)?\\/\\//i.test(url);\n};\n", "\n\n/**\n * Creates a new URL by combining the specified URLs\n *\n * @param {string} baseURL The base URL\n * @param {string} relativeURL The relative URL\n * @returns {string} The combined URL\n */\nmodule.exports = function combineURLs(baseURL, relativeURL) {\n  return relativeURL\n    ? baseURL.replace(/\\/+$/, '') + '/' + relativeURL.replace(/^\\/+/, '')\n    : baseURL;\n};\n", "\n\nvar utils = require('./../utils');\n\n// Headers whose duplicates are ignored by node\n// c.f. https://nodejs.org/api/http.html#http_message_headers\nvar ignoreDuplicateOf = [\n  'age', 'authorization', 'content-length', 'content-type', 'etag',\n  'expires', 'from', 'host', 'if-modified-since', 'if-unmodified-since',\n  'last-modified', 'location', 'max-forwards', 'proxy-authorization',\n  'referer', 'retry-after', 'user-agent'\n];\n\n/**\n * Parse headers into an object\n *\n * ```\n * Date: Wed, 27 Aug 2014 08:58:49 GMT\n * Content-Type: application/json\n * Connection: keep-alive\n * Transfer-Encoding: chunked\n * ```\n *\n * @param {String} headers Headers needing to be parsed\n * @returns {Object} Headers parsed into an object\n */\nmodule.exports = function parseHeaders(headers) {\n  var parsed = {};\n  var key;\n  var val;\n  var i;\n\n  if (!headers) { return parsed; }\n\n  utils.forEach(headers.split('\\n'), function parser(line) {\n    i = line.indexOf(':');\n    key = utils.trim(line.substr(0, i)).toLowerCase();\n    val = utils.trim(line.substr(i + 1));\n\n    if (key) {\n      if (parsed[key] && ignoreDuplicateOf.indexOf(key) >= 0) {\n        return;\n      }\n      if (key === 'set-cookie') {\n        parsed[key] = (parsed[key] ? parsed[key] : []).concat([val]);\n      } else {\n        parsed[key] = parsed[key] ? parsed[key] + ', ' + val : val;\n      }\n    }\n  });\n\n  return parsed;\n};\n", "\n\nvar utils = require('./../utils');\n\nmodule.exports = (\n  utils.isStandardBrowserEnv() ?\n\n  // Standard browser envs have full support of the APIs needed to test\n  // whether the request URL is of the same origin as current location.\n    (function standardBrowserEnv() {\n      var msie = /(msie|trident)/i.test(navigator.userAgent);\n      var urlParsingNode = document.createElement('a');\n      var originURL;\n\n      /**\n    * Parse a URL to discover it's components\n    *\n    * @param {String} url The URL to be parsed\n    * @returns {Object}\n    */\n      function resolveURL(url) {\n        var href = url;\n\n        if (msie) {\n        // IE needs attribute set twice to normalize properties\n          urlParsingNode.setAttribute('href', href);\n          href = urlParsingNode.href;\n        }\n\n        urlParsingNode.setAttribute('href', href);\n\n        // urlParsingNode provides the UrlUtils interface - http://url.spec.whatwg.org/#urlutils\n        return {\n          href: urlParsingNode.href,\n          protocol: urlParsingNode.protocol ? urlParsingNode.protocol.replace(/:$/, '') : '',\n          host: urlParsingNode.host,\n          search: urlParsingNode.search ? urlParsingNode.search.replace(/^\\?/, '') : '',\n          hash: urlParsingNode.hash ? urlParsingNode.hash.replace(/^#/, '') : '',\n          hostname: urlParsingNode.hostname,\n          port: urlParsingNode.port,\n          pathname: (urlParsingNode.pathname.charAt(0) === '/') ?\n            urlParsingNode.pathname :\n            '/' + urlParsingNode.pathname\n        };\n      }\n\n      originURL = resolveURL(window.location.href);\n\n      /**\n    * Determine if a URL shares the same origin as the current location\n    *\n    * @param {String} requestURL The URL to test\n    * @returns {boolean} True if URL shares the same origin, otherwise false\n    */\n      return function isURLSameOrigin(requestURL) {\n        var parsed = (utils.isString(requestURL)) ? resolveURL(requestURL) : requestURL;\n        return (parsed.protocol === originURL.protocol &&\n            parsed.host === originURL.host);\n      };\n    })() :\n\n  // Non standard browser envs (web workers, react-native) lack needed support.\n    (function nonStandardBrowserEnv() {\n      return function isURLSameOrigin() {\n        return true;\n      };\n    })()\n);\n", "\n\nvar utils = require('./../utils');\nvar settle = require('./../core/settle');\nvar buildFullPath = require('../core/buildFullPath');\nvar buildURL = require('./../helpers/buildURL');\nvar http = require('http');\nvar https = require('https');\nvar httpFollow = require('follow-redirects').http;\nvar httpsFollow = require('follow-redirects').https;\nvar url = require('url');\nvar zlib = require('zlib');\nvar pkg = require('./../../package.json');\nvar createError = require('../core/createError');\nvar enhanceError = require('../core/enhanceError');\n\nvar isHttps = /https:?/;\n\n/**\n *\n * @param {http.ClientRequestArgs} options\n * @param {AxiosProxyConfig} proxy\n * @param {string} location\n */\nfunction setProxy(options, proxy, location) {\n  options.hostname = proxy.host;\n  options.host = proxy.host;\n  options.port = proxy.port;\n  options.path = location;\n\n  // Basic proxy authorization\n  if (proxy.auth) {\n    var base64 = Buffer.from(proxy.auth.username + ':' + proxy.auth.password, 'utf8').toString('base64');\n    options.headers['Proxy-Authorization'] = 'Basic ' + base64;\n  }\n\n  // If a proxy is used, any redirects must also pass through the proxy\n  options.beforeRedirect = function beforeRedirect(redirection) {\n    redirection.headers.host = redirection.host;\n    setProxy(redirection, proxy, redirection.href);\n  };\n}\n\n/*eslint consistent-return:0*/\nmodule.exports = function httpAdapter(config) {\n  return new Promise(function dispatchHttpRequest(resolvePromise, rejectPromise) {\n    var resolve = function resolve(value) {\n      resolvePromise(value);\n    };\n    var reject = function reject(value) {\n      rejectPromise(value);\n    };\n    var data = config.data;\n    var headers = config.headers;\n\n    // Set User-Agent (required by some servers)\n    // See https://github.com/axios/axios/issues/69\n    if ('User-Agent' in headers || 'user-agent' in headers) {\n      // User-Agent is specified; handle case where no UA header is desired\n      if (!headers['User-Agent'] && !headers['user-agent']) {\n        delete headers['User-Agent'];\n        delete headers['user-agent'];\n      }\n      // Otherwise, use specified value\n    } else {\n      // Only set header if it hasn't been set in config\n      headers['User-Agent'] = 'axios/' + pkg.version;\n    }\n\n    if (data && !utils.isStream(data)) {\n      if (Buffer.isBuffer(data)) {\n        // Nothing to do...\n      } else if (utils.isArrayBuffer(data)) {\n        data = Buffer.from(new Uint8Array(data));\n      } else if (utils.isString(data)) {\n        data = Buffer.from(data, 'utf-8');\n      } else {\n        return reject(createError(\n          'Data after transformation must be a string, an ArrayBuffer, a Buffer, or a Stream',\n          config\n        ));\n      }\n\n      // Add Content-Length header if data exists\n      headers['Content-Length'] = data.length;\n    }\n\n    // HTTP basic authentication\n    var auth = undefined;\n    if (config.auth) {\n      var username = config.auth.username || '';\n      var password = config.auth.password || '';\n      auth = username + ':' + password;\n    }\n\n    // Parse url\n    var fullPath = buildFullPath(config.baseURL, config.url);\n    var parsed = url.parse(fullPath);\n    var protocol = parsed.protocol || 'http:';\n\n    if (!auth && parsed.auth) {\n      var urlAuth = parsed.auth.split(':');\n      var urlUsername = urlAuth[0] || '';\n      var urlPassword = urlAuth[1] || '';\n      auth = urlUsername + ':' + urlPassword;\n    }\n\n    if (auth) {\n      delete headers.Authorization;\n    }\n\n    var isHttpsRequest = isHttps.test(protocol);\n    var agent = isHttpsRequest ? config.httpsAgent : config.httpAgent;\n\n    var options = {\n      path: buildURL(parsed.path, config.params, config.paramsSerializer).replace(/^\\?/, ''),\n      method: config.method.toUpperCase(),\n      headers: headers,\n      agent: agent,\n      agents: { http: config.httpAgent, https: config.httpsAgent },\n      auth: auth\n    };\n\n    if (config.socketPath) {\n      options.socketPath = config.socketPath;\n    } else {\n      options.hostname = parsed.hostname;\n      options.port = parsed.port;\n    }\n\n    var proxy = config.proxy;\n    if (!proxy && proxy !== false) {\n      var proxyEnv = protocol.slice(0, -1) + '_proxy';\n      var proxyUrl = process.env[proxyEnv] || process.env[proxyEnv.toUpperCase()];\n      if (proxyUrl) {\n        var parsedProxyUrl = url.parse(proxyUrl);\n        var noProxyEnv = process.env.no_proxy || process.env.NO_PROXY;\n        var shouldProxy = true;\n\n        if (noProxyEnv) {\n          var noProxy = noProxyEnv.split(',').map(function trim(s) {\n            return s.trim();\n          });\n\n          shouldProxy = !noProxy.some(function proxyMatch(proxyElement) {\n            if (!proxyElement) {\n              return false;\n            }\n            if (proxyElement === '*') {\n              return true;\n            }\n            if (proxyElement[0] === '.' &&\n                parsed.hostname.substr(parsed.hostname.length - proxyElement.length) === proxyElement) {\n              return true;\n            }\n\n            return parsed.hostname === proxyElement;\n          });\n        }\n\n        if (shouldProxy) {\n          proxy = {\n            host: parsedProxyUrl.hostname,\n            port: parsedProxyUrl.port,\n            protocol: parsedProxyUrl.protocol\n          };\n\n          if (parsedProxyUrl.auth) {\n            var proxyUrlAuth = parsedProxyUrl.auth.split(':');\n            proxy.auth = {\n              username: proxyUrlAuth[0],\n              password: proxyUrlAuth[1]\n            };\n          }\n        }\n      }\n    }\n\n    if (proxy) {\n      options.headers.host = parsed.hostname + (parsed.port ? ':' + parsed.port : '');\n      setProxy(options, proxy, protocol + '//' + parsed.hostname + (parsed.port ? ':' + parsed.port : '') + options.path);\n    }\n\n    var transport;\n    var isHttpsProxy = isHttpsRequest && (proxy ? isHttps.test(proxy.protocol) : true);\n    if (config.transport) {\n      transport = config.transport;\n    } else if (config.maxRedirects === 0) {\n      transport = isHttpsProxy ? https : http;\n    } else {\n      if (config.maxRedirects) {\n        options.maxRedirects = config.maxRedirects;\n      }\n      transport = isHttpsProxy ? httpsFollow : httpFollow;\n    }\n\n    if (config.maxBodyLength > -1) {\n      options.maxBodyLength = config.maxBodyLength;\n    }\n\n    // Create the request\n    var req = transport.request(options, function handleResponse(res) {\n      if (req.aborted) return;\n\n      // uncompress the response body transparently if required\n      var stream = res;\n\n      // return the last request in case of redirects\n      var lastRequest = res.req || req;\n\n\n      // if no content, is HEAD request or decompress disabled we should not decompress\n      if (res.statusCode !== 204 && lastRequest.method !== 'HEAD' && config.decompress !== false) {\n        switch (res.headers['content-encoding']) {\n        /*eslint default-case:0*/\n        case 'gzip':\n        case 'compress':\n        case 'deflate':\n        // add the unzipper to the body stream processing pipeline\n          stream = stream.pipe(zlib.createUnzip());\n\n          // remove the content-encoding in order to not confuse downstream operations\n          delete res.headers['content-encoding'];\n          break;\n        }\n      }\n\n      var response = {\n        status: res.statusCode,\n        statusText: res.statusMessage,\n        headers: res.headers,\n        config: config,\n        request: lastRequest\n      };\n\n      if (config.responseType === 'stream') {\n        response.data = stream;\n        settle(resolve, reject, response);\n      } else {\n        var responseBuffer = [];\n        var totalResponseBytes = 0;\n        stream.on('data', function handleStreamData(chunk) {\n          responseBuffer.push(chunk);\n          totalResponseBytes += chunk.length;\n\n          // make sure the content length is not over the maxContentLength if specified\n          if (config.maxContentLength > -1 && totalResponseBytes > config.maxContentLength) {\n            stream.destroy();\n            reject(createError('maxContentLength size of ' + config.maxContentLength + ' exceeded',\n              config, null, lastRequest));\n          }\n        });\n\n        stream.on('error', function handleStreamError(err) {\n          if (req.aborted) return;\n          reject(enhanceError(err, config, null, lastRequest));\n        });\n\n        stream.on('end', function handleStreamEnd() {\n          var responseData = Buffer.concat(responseBuffer);\n          if (config.responseType !== 'arraybuffer') {\n            responseData = responseData.toString(config.responseEncoding);\n            if (!config.responseEncoding || config.responseEncoding === 'utf8') {\n              responseData = utils.stripBOM(responseData);\n            }\n          }\n\n          response.data = responseData;\n          settle(resolve, reject, response);\n        });\n      }\n    });\n\n    // Handle errors\n    req.on('error', function handleRequestError(err) {\n      if (req.aborted && err.code !== 'ERR_FR_TOO_MANY_REDIRECTS') return;\n      reject(enhanceError(err, config, null, req));\n    });\n\n    // Handle request timeout\n    if (config.timeout) {\n      // This is forcing a int timeout to avoid problems if the `req` interface doesn't handle other types.\n      var timeout = parseInt(config.timeout, 10);\n\n      if (isNaN(timeout)) {\n        reject(createError(\n          'error trying to parse `config.timeout` to int',\n          config,\n          'ERR_PARSE_TIMEOUT',\n          req\n        ));\n\n        return;\n      }\n\n      // Sometime, the response will be very slow, and does not respond, the connect event will be block by event loop system.\n      // And timer callback will be fired, and abort() will be invoked before connection, then get \"socket hang up\" and code ECONNRESET.\n      // At this time, if we have a large number of request, nodejs will hang up some socket on background. and the number will up and up.\n      // And then these socket which be hang up will devoring CPU little by little.\n      // ClientRequest.setTimeout will be fired on the specify milliseconds, and can make sure that abort() will be fired after connect.\n      req.setTimeout(timeout, function handleRequestTimeout() {\n        req.abort();\n        reject(createError(\n          'timeout of ' + timeout + 'ms exceeded',\n          config,\n          config.transitional && config.transitional.clarifyTimeoutError ? 'ETIMEDOUT' : 'ECONNABORTED',\n          req\n        ));\n      });\n    }\n\n    if (config.cancelToken) {\n      // Handle cancellation\n      config.cancelToken.promise.then(function onCanceled(cancel) {\n        if (req.aborted) return;\n\n        req.abort();\n        reject(cancel);\n      });\n    }\n\n    // Send the request\n    if (utils.isStream(data)) {\n      data.on('error', function handleStreamError(err) {\n        reject(enhanceError(err, config, null, req));\n      }).pipe(req);\n    } else {\n      req.end(data);\n    }\n  });\n};\n", "module.exports = {\n  \"name\": \"axios\",\n  \"version\": \"0.21.4\",\n  \"description\": \"Promise based HTTP client for the browser and node.js\",\n  \"main\": \"index.js\",\n  \"scripts\": {\n    \"test\": \"grunt test\",\n    \"start\": \"node ./sandbox/server.js\",\n    \"build\": \"NODE_ENV=production grunt build\",\n    \"preversion\": \"npm test\",\n    \"version\": \"npm run build && grunt version && git add -A dist && git add CHANGELOG.md bower.json package.json\",\n    \"postversion\": \"git push && git push --tags\",\n    \"examples\": \"node ./examples/server.js\",\n    \"coveralls\": \"cat coverage/lcov.info | ./node_modules/coveralls/bin/coveralls.js\",\n    \"fix\": \"eslint --fix lib/**/*.js\"\n  },\n  \"repository\": {\n    \"type\": \"git\",\n    \"url\": \"https://github.com/axios/axios.git\"\n  },\n  \"keywords\": [\n    \"xhr\",\n    \"http\",\n    \"ajax\",\n    \"promise\",\n    \"node\"\n  ],\n  \"author\": \"<PERSON>\",\n  \"license\": \"MIT\",\n  \"bugs\": {\n    \"url\": \"https://github.com/axios/axios/issues\"\n  },\n  \"homepage\": \"https://axios-http.com\",\n  \"devDependencies\": {\n    \"coveralls\": \"^3.0.0\",\n    \"es6-promise\": \"^4.2.4\",\n    \"grunt\": \"^1.3.0\",\n    \"grunt-banner\": \"^0.6.0\",\n    \"grunt-cli\": \"^1.2.0\",\n    \"grunt-contrib-clean\": \"^1.1.0\",\n    \"grunt-contrib-watch\": \"^1.0.0\",\n    \"grunt-eslint\": \"^23.0.0\",\n    \"grunt-karma\": \"^4.0.0\",\n    \"grunt-mocha-test\": \"^0.13.3\",\n    \"grunt-ts\": \"^6.0.0-beta.19\",\n    \"grunt-webpack\": \"^4.0.2\",\n    \"istanbul-instrumenter-loader\": \"^1.0.0\",\n    \"jasmine-core\": \"^2.4.1\",\n    \"karma\": \"^6.3.2\",\n    \"karma-chrome-launcher\": \"^3.1.0\",\n    \"karma-firefox-launcher\": \"^2.1.0\",\n    \"karma-jasmine\": \"^1.1.1\",\n    \"karma-jasmine-ajax\": \"^0.1.13\",\n    \"karma-safari-launcher\": \"^1.0.0\",\n    \"karma-sauce-launcher\": \"^4.3.6\",\n    \"karma-sinon\": \"^1.0.5\",\n    \"karma-sourcemap-loader\": \"^0.3.8\",\n    \"karma-webpack\": \"^4.0.2\",\n    \"load-grunt-tasks\": \"^3.5.2\",\n    \"minimist\": \"^1.2.0\",\n    \"mocha\": \"^8.2.1\",\n    \"sinon\": \"^4.5.0\",\n    \"terser-webpack-plugin\": \"^4.2.3\",\n    \"typescript\": \"^4.0.5\",\n    \"url-search-params\": \"^0.10.0\",\n    \"webpack\": \"^4.44.2\",\n    \"webpack-dev-server\": \"^3.11.0\"\n  },\n  \"browser\": {\n    \"./lib/adapters/http.js\": \"./lib/adapters/xhr.js\"\n  },\n  \"jsdelivr\": \"dist/axios.min.js\",\n  \"unpkg\": \"dist/axios.min.js\",\n  \"typings\": \"./index.d.ts\",\n  \"dependencies\": {\n    \"follow-redirects\": \"^1.14.0\"\n  },\n  \"bundlesize\": [\n    {\n      \"path\": \"./dist/axios.min.js\",\n      \"threshold\": \"5kB\"\n    }\n  ]\n}\n", "\n\nmodule.exports = function isCancel(value) {\n  return !!(value && value.__CANCEL__);\n};\n", "\n\nvar utils = require('../utils');\n\n/**\n * Config-specific merge-function which creates a new config-object\n * by merging two configuration objects together.\n *\n * @param {Object} config1\n * @param {Object} config2\n * @returns {Object} New object resulting from merging config2 to config1\n */\nmodule.exports = function mergeConfig(config1, config2) {\n  // eslint-disable-next-line no-param-reassign\n  config2 = config2 || {};\n  var config = {};\n\n  var valueFromConfig2Keys = ['url', 'method', 'data'];\n  var mergeDeepPropertiesKeys = ['headers', 'auth', 'proxy', 'params'];\n  var defaultToConfig2Keys = [\n    'baseURL', 'transformRequest', 'transformResponse', 'paramsSerializer',\n    'timeout', 'timeoutMessage', 'withCredentials', 'adapter', 'responseType', 'xsrfCookieName',\n    'xsrfHeaderName', 'onUploadProgress', 'onDownloadProgress', 'decompress',\n    'maxContentLength', 'maxBodyLength', 'maxRedirects', 'transport', 'httpAgent',\n    'httpsAgent', 'cancelToken', 'socketPath', 'responseEncoding'\n  ];\n  var directMergeKeys = ['validateStatus'];\n\n  function getMergedValue(target, source) {\n    if (utils.isPlainObject(target) && utils.isPlainObject(source)) {\n      return utils.merge(target, source);\n    } else if (utils.isPlainObject(source)) {\n      return utils.merge({}, source);\n    } else if (utils.isArray(source)) {\n      return source.slice();\n    }\n    return source;\n  }\n\n  function mergeDeepProperties(prop) {\n    if (!utils.isUndefined(config2[prop])) {\n      config[prop] = getMergedValue(config1[prop], config2[prop]);\n    } else if (!utils.isUndefined(config1[prop])) {\n      config[prop] = getMergedValue(undefined, config1[prop]);\n    }\n  }\n\n  utils.forEach(valueFromConfig2Keys, function valueFromConfig2(prop) {\n    if (!utils.isUndefined(config2[prop])) {\n      config[prop] = getMergedValue(undefined, config2[prop]);\n    }\n  });\n\n  utils.forEach(mergeDeepPropertiesKeys, mergeDeepProperties);\n\n  utils.forEach(defaultToConfig2Keys, function defaultToConfig2(prop) {\n    if (!utils.isUndefined(config2[prop])) {\n      config[prop] = getMergedValue(undefined, config2[prop]);\n    } else if (!utils.isUndefined(config1[prop])) {\n      config[prop] = getMergedValue(undefined, config1[prop]);\n    }\n  });\n\n  utils.forEach(directMergeKeys, function merge(prop) {\n    if (prop in config2) {\n      config[prop] = getMergedValue(config1[prop], config2[prop]);\n    } else if (prop in config1) {\n      config[prop] = getMergedValue(undefined, config1[prop]);\n    }\n  });\n\n  var axiosKeys = valueFromConfig2Keys\n    .concat(mergeDeepPropertiesKeys)\n    .concat(defaultToConfig2Keys)\n    .concat(directMergeKeys);\n\n  var otherKeys = Object\n    .keys(config1)\n    .concat(Object.keys(config2))\n    .filter(function filterAxiosKeys(key) {\n      return axiosKeys.indexOf(key) === -1;\n    });\n\n  utils.forEach(otherKeys, mergeDeepProperties);\n\n  return config;\n};\n", "\n\nvar pkg = require('./../../package.json');\n\nvar validators = {};\n\n// eslint-disable-next-line func-names\n['object', 'boolean', 'number', 'function', 'string', 'symbol'].forEach(function(type, i) {\n  validators[type] = function validator(thing) {\n    return typeof thing === type || 'a' + (i < 1 ? 'n ' : ' ') + type;\n  };\n});\n\nvar deprecatedWarnings = {};\nvar currentVerArr = pkg.version.split('.');\n\n/**\n * Compare package versions\n * @param {string} version\n * @param {string?} thanVersion\n * @returns {boolean}\n */\nfunction isOlderVersion(version, thanVersion) {\n  var pkgVersionArr = thanVersion ? thanVersion.split('.') : currentVerArr;\n  var destVer = version.split('.');\n  for (var i = 0; i < 3; i++) {\n    if (pkgVersionArr[i] > destVer[i]) {\n      return true;\n    } else if (pkgVersionArr[i] < destVer[i]) {\n      return false;\n    }\n  }\n  return false;\n}\n\n/**\n * Transitional option validator\n * @param {function|boolean?} validator\n * @param {string?} version\n * @param {string} message\n * @returns {function}\n */\nvalidators.transitional = function transitional(validator, version, message) {\n  var isDeprecated = version && isOlderVersion(version);\n\n  function formatMessage(opt, desc) {\n    return '[Axios v' + pkg.version + '] Transitional option \\'' + opt + '\\'' + desc + (message ? '. ' + message : '');\n  }\n\n  // eslint-disable-next-line func-names\n  return function(value, opt, opts) {\n    if (validator === false) {\n      throw new Error(formatMessage(opt, ' has been removed in ' + version));\n    }\n\n    if (isDeprecated && !deprecatedWarnings[opt]) {\n      deprecatedWarnings[opt] = true;\n      // eslint-disable-next-line no-console\n      console.warn(\n        formatMessage(\n          opt,\n          ' has been deprecated since v' + version + ' and will be removed in the near future'\n        )\n      );\n    }\n\n    return validator ? validator(value, opt, opts) : true;\n  };\n};\n\n/**\n * Assert object's properties type\n * @param {object} options\n * @param {object} schema\n * @param {boolean?} allowUnknown\n */\n\nfunction assertOptions(options, schema, allowUnknown) {\n  if (typeof options !== 'object') {\n    throw new TypeError('options must be an object');\n  }\n  var keys = Object.keys(options);\n  var i = keys.length;\n  while (i-- > 0) {\n    var opt = keys[i];\n    var validator = schema[opt];\n    if (validator) {\n      var value = options[opt];\n      var result = value === undefined || validator(value, opt, options);\n      if (result !== true) {\n        throw new TypeError('option ' + opt + ' must be ' + result);\n      }\n      continue;\n    }\n    if (allowUnknown !== true) {\n      throw Error('Unknown option ' + opt);\n    }\n  }\n}\n\nmodule.exports = {\n  isOlderVersion: isOlderVersion,\n  assertOptions: assertOptions,\n  validators: validators\n};\n", "\n\n/**\n * A `Cancel` is an object that is thrown when an operation is canceled.\n *\n * @class\n * @param {string=} message The message.\n */\nfunction Cancel(message) {\n  this.message = message;\n}\n\nCancel.prototype.toString = function toString() {\n  return 'Cancel' + (this.message ? ': ' + this.message : '');\n};\n\nCancel.prototype.__CANCEL__ = true;\n\nmodule.exports = Cancel;\n", "\n\nvar Cancel = require('./Cancel');\n\n/**\n * A `CancelToken` is an object that can be used to request cancellation of an operation.\n *\n * @class\n * @param {Function} executor The executor function.\n */\nfunction CancelToken(executor) {\n  if (typeof executor !== 'function') {\n    throw new TypeError('executor must be a function.');\n  }\n\n  var resolvePromise;\n  this.promise = new Promise(function promiseExecutor(resolve) {\n    resolvePromise = resolve;\n  });\n\n  var token = this;\n  executor(function cancel(message) {\n    if (token.reason) {\n      // Cancellation has already been requested\n      return;\n    }\n\n    token.reason = new Cancel(message);\n    resolvePromise(token.reason);\n  });\n}\n\n/**\n * Throws a `Cancel` if cancellation has been requested.\n */\nCancelToken.prototype.throwIfRequested = function throwIfRequested() {\n  if (this.reason) {\n    throw this.reason;\n  }\n};\n\n/**\n * Returns an object that contains a new `CancelToken` and a function that, when called,\n * cancels the `CancelToken`.\n */\nCancelToken.source = function source() {\n  var cancel;\n  var token = new CancelToken(function executor(c) {\n    cancel = c;\n  });\n  return {\n    token: token,\n    cancel: cancel\n  };\n};\n\nmodule.exports = CancelToken;\n", "\n\n/**\n * Syntactic sugar for invoking a function and expanding an array for arguments.\n *\n * Common use case would be to use `Function.prototype.apply`.\n *\n *  ```js\n *  function f(x, y, z) {}\n *  var args = [1, 2, 3];\n *  f.apply(null, args);\n *  ```\n *\n * With `spread` this example can be re-written.\n *\n *  ```js\n *  spread(function(x, y, z) {})([1, 2, 3]);\n *  ```\n *\n * @param {Function} callback\n * @returns {Function}\n */\nmodule.exports = function spread(callback) {\n  return function wrap(arr) {\n    return callback.apply(null, arr);\n  };\n};\n", "\n\n/**\n * Determines whether the payload is an error thrown by Axios\n *\n * @param {*} payload The value to test\n * @returns {boolean} True if the payload is an error thrown by <PERSON><PERSON><PERSON>, otherwise false\n */\nmodule.exports = function isAxiosError(payload) {\n  return (typeof payload === 'object') && (payload.isAxiosError === true);\n};\n"]}