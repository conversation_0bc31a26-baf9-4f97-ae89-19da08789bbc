{"description": "项目配置文件", "packOptions": {"ignore": [], "include": []}, "setting": {"urlCheck": false, "es6": true, "enhance": true, "postcss": true, "preloadBackgroundData": false, "minified": true, "newFeature": false, "coverView": true, "nodeModules": true, "autoAudits": false, "showShadowRootInWxmlPanel": true, "scopeDataCheck": false, "uglifyFileName": true, "checkInvalidKey": true, "checkSiteMap": true, "uploadWithSourceMap": true, "compileHotReLoad": false, "useMultiFrameRuntime": true, "useApiHook": true, "useApiHostProcess": true, "babelSetting": {"ignore": [], "disablePlugins": [], "outputPath": ""}, "enableEngineNative": false, "bundle": false, "useIsolateContext": true, "useCompilerModule": true, "userConfirmedUseCompilerModuleSwitch": false, "userConfirmedBundleSwitch": false, "packNpmManually": false, "packNpmRelationList": [], "minifyWXSS": true, "useCloudFunction": true, "cloudfunctionRoot": "cloudfunctions/", "useStaticServer": true, "ignoreUploadUnusedFiles": true}, "compileType": "miniprogram", "libVersion": "2.19.4", "appid": "wx7f6208eacc6720e4", "projectname": "管理系统", "cloudfunctionRoot": "cloudfunctions/", "cloud": true, "envId": "wlksapp-4g54hauu5cbf43dc", "condition": {}, "editorSetting": {"tabIndent": "insertSpaces", "tabSize": 2}, "cloudfunctionTemplateRoot": "cloudfunctionTemplate/"}