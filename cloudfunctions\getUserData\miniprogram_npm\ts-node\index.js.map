{"version": 3, "sources": ["index.js", "../package.json"], "names": [], "mappings": ";;;;;;;AAAA;AACA;AACA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.js", "sourcesContent": ["\nObject.defineProperty(exports, \"__esModule\", { value: true });\nconst path_1 = require(\"path\");\nconst sourceMapSupport = require(\"source-map-support\");\nconst ynModule = require(\"yn\");\nconst make_error_1 = require(\"make-error\");\nconst util = require(\"util\");\n/**\n * Registered `ts-node` instance information.\n */\nexports.REGISTER_INSTANCE = Symbol.for('ts-node.register.instance');\n/**\n * @internal\n */\nexports.INSPECT_CUSTOM = util.inspect.custom || 'inspect';\n/**\n * Wrapper around yn module that returns `undefined` instead of `null`.\n * This is implemented by yn v4, but we're staying on v3 to avoid v4's node 10 requirement.\n */\nfunction yn(value) {\n    var _a;\n    return (_a = ynModule(value)) !== null && _a !== void 0 ? _a : undefined;\n}\n/**\n * Debugging `ts-node`.\n */\nconst shouldDebug = yn(process.env.TS_NODE_DEBUG);\n/** @internal */\nexports.debug = shouldDebug ?\n    (...args) => console.log(`[ts-node ${new Date().toISOString()}]`, ...args)\n    : () => undefined;\nconst debugFn = shouldDebug ?\n    (key, fn) => {\n        let i = 0;\n        return (x) => {\n            exports.debug(key, x, ++i);\n            return fn(x);\n        };\n    } :\n    (_, fn) => fn;\n/**\n * Export the current version.\n */\nexports.VERSION = require('../package.json').version;\n/**\n * Like `Object.assign`, but ignores `undefined` properties.\n */\nfunction assign(initialValue, ...sources) {\n    for (const source of sources) {\n        for (const key of Object.keys(source)) {\n            const value = source[key];\n            if (value !== undefined)\n                initialValue[key] = value;\n        }\n    }\n    return initialValue;\n}\n/**\n * Default register options, including values specified via environment\n * variables.\n */\nexports.DEFAULTS = {\n    dir: process.env.TS_NODE_DIR,\n    emit: yn(process.env.TS_NODE_EMIT),\n    scope: yn(process.env.TS_NODE_SCOPE),\n    files: yn(process.env.TS_NODE_FILES),\n    pretty: yn(process.env.TS_NODE_PRETTY),\n    compiler: process.env.TS_NODE_COMPILER,\n    compilerOptions: parse(process.env.TS_NODE_COMPILER_OPTIONS),\n    ignore: split(process.env.TS_NODE_IGNORE),\n    project: process.env.TS_NODE_PROJECT,\n    skipProject: yn(process.env.TS_NODE_SKIP_PROJECT),\n    skipIgnore: yn(process.env.TS_NODE_SKIP_IGNORE),\n    preferTsExts: yn(process.env.TS_NODE_PREFER_TS_EXTS),\n    ignoreDiagnostics: split(process.env.TS_NODE_IGNORE_DIAGNOSTICS),\n    transpileOnly: yn(process.env.TS_NODE_TRANSPILE_ONLY),\n    typeCheck: yn(process.env.TS_NODE_TYPE_CHECK),\n    compilerHost: yn(process.env.TS_NODE_COMPILER_HOST),\n    logError: yn(process.env.TS_NODE_LOG_ERROR)\n};\n/**\n * Default TypeScript compiler options required by `ts-node`.\n */\nconst TS_NODE_COMPILER_OPTIONS = {\n    sourceMap: true,\n    inlineSourceMap: false,\n    inlineSources: true,\n    declaration: false,\n    noEmit: false,\n    outDir: '.ts-node'\n};\n/**\n * Split a string array of values.\n */\nfunction split(value) {\n    return typeof value === 'string' ? value.split(/ *, */g) : undefined;\n}\nexports.split = split;\n/**\n * Parse a string as JSON.\n */\nfunction parse(value) {\n    return typeof value === 'string' ? JSON.parse(value) : undefined;\n}\nexports.parse = parse;\n/**\n * Replace backslashes with forward slashes.\n */\nfunction normalizeSlashes(value) {\n    return value.replace(/\\\\/g, '/');\n}\nexports.normalizeSlashes = normalizeSlashes;\n/**\n * TypeScript diagnostics error.\n */\nclass TSError extends make_error_1.BaseError {\n    constructor(diagnosticText, diagnosticCodes) {\n        super(`⨯ Unable to compile TypeScript:\\n${diagnosticText}`);\n        this.diagnosticText = diagnosticText;\n        this.diagnosticCodes = diagnosticCodes;\n        this.name = 'TSError';\n    }\n    /**\n     * @internal\n     */\n    [exports.INSPECT_CUSTOM]() {\n        return this.diagnosticText;\n    }\n}\nexports.TSError = TSError;\n/**\n * Cached fs operation wrapper.\n */\nfunction cachedLookup(fn) {\n    const cache = new Map();\n    return (arg) => {\n        if (!cache.has(arg)) {\n            cache.set(arg, fn(arg));\n        }\n        return cache.get(arg);\n    };\n}\n/** @internal */\nfunction getExtensions(config) {\n    const tsExtensions = ['.ts'];\n    const jsExtensions = [];\n    // Enable additional extensions when JSX or `allowJs` is enabled.\n    if (config.options.jsx)\n        tsExtensions.push('.tsx');\n    if (config.options.allowJs)\n        jsExtensions.push('.js');\n    if (config.options.jsx && config.options.allowJs)\n        jsExtensions.push('.jsx');\n    return { tsExtensions, jsExtensions };\n}\nexports.getExtensions = getExtensions;\n/**\n * Register TypeScript compiler instance onto node.js\n */\nfunction register(opts = {}) {\n    const originalJsHandler = require.extensions['.js']; // tslint:disable-line\n    const service = create(opts);\n    const { tsExtensions, jsExtensions } = getExtensions(service.config);\n    const extensions = [...tsExtensions, ...jsExtensions];\n    // Expose registered instance globally.\n    process[exports.REGISTER_INSTANCE] = service;\n    // Register the extensions.\n    registerExtensions(service.options.preferTsExts, extensions, service, originalJsHandler);\n    return service;\n}\nexports.register = register;\n/**\n * Create TypeScript compiler instance.\n */\nfunction create(rawOptions = {}) {\n    var _a, _b;\n    const dir = (_a = rawOptions.dir) !== null && _a !== void 0 ? _a : exports.DEFAULTS.dir;\n    const compilerName = (_b = rawOptions.compiler) !== null && _b !== void 0 ? _b : exports.DEFAULTS.compiler;\n    const cwd = dir ? path_1.resolve(dir) : process.cwd();\n    /**\n     * Load the typescript compiler. It is required to load the tsconfig but might\n     * be changed by the tsconfig, so we sometimes have to do this twice.\n     */\n    function loadCompiler(name) {\n        const compiler = require.resolve(name || 'typescript', { paths: [cwd, __dirname] });\n        const ts = require(compiler);\n        return { compiler, ts };\n    }\n    // Compute minimum options to read the config file.\n    let { compiler, ts } = loadCompiler(compilerName);\n    // Read config file and merge new options between env and CLI options.\n    const { config, options: tsconfigOptions } = readConfig(cwd, ts, rawOptions);\n    const options = assign({}, exports.DEFAULTS, tsconfigOptions || {}, rawOptions);\n    // If `compiler` option changed based on tsconfig, re-load the compiler.\n    if (options.compiler !== compilerName) {\n        ({ compiler, ts } = loadCompiler(options.compiler));\n    }\n    const readFile = options.readFile || ts.sys.readFile;\n    const fileExists = options.fileExists || ts.sys.fileExists;\n    const transpileOnly = options.transpileOnly === true || options.typeCheck === false;\n    const transformers = options.transformers || undefined;\n    const ignoreDiagnostics = [\n        6059,\n        18002,\n        18003,\n        ...(options.ignoreDiagnostics || [])\n    ].map(Number);\n    const configDiagnosticList = filterDiagnostics(config.errors, ignoreDiagnostics);\n    const outputCache = new Map();\n    const isScoped = options.scope ? (relname) => relname.charAt(0) !== '.' : () => true;\n    const shouldIgnore = createIgnore(options.skipIgnore ? [] : (options.ignore || ['(?:^|/)node_modules/']).map(str => new RegExp(str)));\n    const diagnosticHost = {\n        getNewLine: () => ts.sys.newLine,\n        getCurrentDirectory: () => cwd,\n        getCanonicalFileName: ts.sys.useCaseSensitiveFileNames ? x => x : x => x.toLowerCase()\n    };\n    // Install source map support and read from memory cache.\n    sourceMapSupport.install({\n        environment: 'node',\n        retrieveFile(path) {\n            var _a;\n            return ((_a = outputCache.get(normalizeSlashes(path))) === null || _a === void 0 ? void 0 : _a.content) || '';\n        }\n    });\n    const formatDiagnostics = process.stdout.isTTY || options.pretty\n        ? (ts.formatDiagnosticsWithColorAndContext || ts.formatDiagnostics)\n        : ts.formatDiagnostics;\n    function createTSError(diagnostics) {\n        const diagnosticText = formatDiagnostics(diagnostics, diagnosticHost);\n        const diagnosticCodes = diagnostics.map(x => x.code);\n        return new TSError(diagnosticText, diagnosticCodes);\n    }\n    function reportTSError(configDiagnosticList) {\n        const error = createTSError(configDiagnosticList);\n        if (options.logError) {\n            // Print error in red color and continue execution.\n            console.error('\\x1b[31m%s\\x1b[0m', error);\n        }\n        else {\n            // Throw error and exit the script.\n            throw error;\n        }\n    }\n    // Render the configuration errors.\n    if (configDiagnosticList.length)\n        reportTSError(configDiagnosticList);\n    /**\n     * Get the extension for a transpiled file.\n     */\n    const getExtension = config.options.jsx === ts.JsxEmit.Preserve ?\n        ((path) => /\\.[tj]sx$/.test(path) ? '.jsx' : '.js') :\n        ((_) => '.js');\n    /**\n     * Create the basic required function using transpile mode.\n     */\n    let getOutput;\n    let getTypeInfo;\n    const getOutputTranspileOnly = (code, fileName, overrideCompilerOptions) => {\n        const result = ts.transpileModule(code, {\n            fileName,\n            compilerOptions: overrideCompilerOptions ? Object.assign(Object.assign({}, config.options), overrideCompilerOptions) : config.options,\n            reportDiagnostics: true\n        });\n        const diagnosticList = filterDiagnostics(result.diagnostics || [], ignoreDiagnostics);\n        if (diagnosticList.length)\n            reportTSError(diagnosticList);\n        return [result.outputText, result.sourceMapText];\n    };\n    // Use full language services when the fast option is disabled.\n    if (!transpileOnly) {\n        const fileContents = new Map();\n        const rootFileNames = config.fileNames.slice();\n        const cachedReadFile = cachedLookup(debugFn('readFile', readFile));\n        // Use language services by default (TODO: invert next major version).\n        if (!options.compilerHost) {\n            let projectVersion = 1;\n            const fileVersions = new Map(rootFileNames.map(fileName => [fileName, 0]));\n            const getCustomTransformers = () => {\n                if (typeof transformers === 'function') {\n                    const program = service.getProgram();\n                    return program ? transformers(program) : undefined;\n                }\n                return transformers;\n            };\n            // Create the compiler host for type checking.\n            const serviceHost = {\n                getProjectVersion: () => String(projectVersion),\n                getScriptFileNames: () => Array.from(fileVersions.keys()),\n                getScriptVersion: (fileName) => {\n                    const version = fileVersions.get(fileName);\n                    return version ? version.toString() : '';\n                },\n                getScriptSnapshot(fileName) {\n                    let contents = fileContents.get(fileName);\n                    // Read contents into TypeScript memory cache.\n                    if (contents === undefined) {\n                        contents = cachedReadFile(fileName);\n                        if (contents === undefined)\n                            return;\n                        fileVersions.set(fileName, 1);\n                        fileContents.set(fileName, contents);\n                    }\n                    return ts.ScriptSnapshot.fromString(contents);\n                },\n                readFile: cachedReadFile,\n                readDirectory: ts.sys.readDirectory,\n                getDirectories: cachedLookup(debugFn('getDirectories', ts.sys.getDirectories)),\n                fileExists: cachedLookup(debugFn('fileExists', fileExists)),\n                directoryExists: cachedLookup(debugFn('directoryExists', ts.sys.directoryExists)),\n                getNewLine: () => ts.sys.newLine,\n                useCaseSensitiveFileNames: () => ts.sys.useCaseSensitiveFileNames,\n                getCurrentDirectory: () => cwd,\n                getCompilationSettings: () => config.options,\n                getDefaultLibFileName: () => ts.getDefaultLibFilePath(config.options),\n                getCustomTransformers: getCustomTransformers\n            };\n            const registry = ts.createDocumentRegistry(ts.sys.useCaseSensitiveFileNames, cwd);\n            const service = ts.createLanguageService(serviceHost, registry);\n            const updateMemoryCache = (contents, fileName) => {\n                // Add to `rootFiles` when discovered for the first time.\n                if (!fileVersions.has(fileName)) {\n                    rootFileNames.push(fileName);\n                }\n                const previousVersion = fileVersions.get(fileName) || 0;\n                const previousContents = fileContents.get(fileName);\n                // Avoid incrementing cache when nothing has changed.\n                if (contents !== previousContents) {\n                    fileVersions.set(fileName, previousVersion + 1);\n                    fileContents.set(fileName, contents);\n                    // Increment project version for every file change.\n                    projectVersion++;\n                }\n            };\n            let previousProgram = undefined;\n            getOutput = (code, fileName) => {\n                updateMemoryCache(code, fileName);\n                const programBefore = service.getProgram();\n                if (programBefore !== previousProgram) {\n                    exports.debug(`compiler rebuilt Program instance when getting output for ${fileName}`);\n                }\n                const output = service.getEmitOutput(fileName);\n                // Get the relevant diagnostics - this is 3x faster than `getPreEmitDiagnostics`.\n                const diagnostics = service.getSemanticDiagnostics(fileName)\n                    .concat(service.getSyntacticDiagnostics(fileName));\n                const programAfter = service.getProgram();\n                exports.debug('invariant: Is service.getProject() identical before and after getting emit output and diagnostics? (should always be true) ', programBefore === programAfter);\n                previousProgram = programAfter;\n                const diagnosticList = filterDiagnostics(diagnostics, ignoreDiagnostics);\n                if (diagnosticList.length)\n                    reportTSError(diagnosticList);\n                if (output.emitSkipped) {\n                    throw new TypeError(`${path_1.relative(cwd, fileName)}: Emit skipped`);\n                }\n                // Throw an error when requiring `.d.ts` files.\n                if (output.outputFiles.length === 0) {\n                    throw new TypeError(`Unable to require file: ${path_1.relative(cwd, fileName)}\\n` +\n                        'This is usually the result of a faulty configuration or import. ' +\n                        'Make sure there is a `.js`, `.json` or other executable extension with ' +\n                        'loader attached before `ts-node` available.');\n                }\n                return [output.outputFiles[1].text, output.outputFiles[0].text];\n            };\n            getTypeInfo = (code, fileName, position) => {\n                updateMemoryCache(code, fileName);\n                const info = service.getQuickInfoAtPosition(fileName, position);\n                const name = ts.displayPartsToString(info ? info.displayParts : []);\n                const comment = ts.displayPartsToString(info ? info.documentation : []);\n                return { name, comment };\n            };\n        }\n        else {\n            const sys = Object.assign(Object.assign(Object.assign({}, ts.sys), diagnosticHost), { readFile: (fileName) => {\n                    const cacheContents = fileContents.get(fileName);\n                    if (cacheContents !== undefined)\n                        return cacheContents;\n                    return cachedReadFile(fileName);\n                }, readDirectory: ts.sys.readDirectory, getDirectories: cachedLookup(debugFn('getDirectories', ts.sys.getDirectories)), fileExists: cachedLookup(debugFn('fileExists', fileExists)), directoryExists: cachedLookup(debugFn('directoryExists', ts.sys.directoryExists)), resolvePath: cachedLookup(debugFn('resolvePath', ts.sys.resolvePath)), realpath: ts.sys.realpath ? cachedLookup(debugFn('realpath', ts.sys.realpath)) : undefined });\n            const host = ts.createIncrementalCompilerHost\n                ? ts.createIncrementalCompilerHost(config.options, sys)\n                : Object.assign(Object.assign({}, sys), { getSourceFile: (fileName, languageVersion) => {\n                        const contents = sys.readFile(fileName);\n                        if (contents === undefined)\n                            return;\n                        return ts.createSourceFile(fileName, contents, languageVersion);\n                    }, getDefaultLibLocation: () => normalizeSlashes(path_1.dirname(compiler)), getDefaultLibFileName: () => normalizeSlashes(path_1.join(path_1.dirname(compiler), ts.getDefaultLibFileName(config.options))), useCaseSensitiveFileNames: () => sys.useCaseSensitiveFileNames });\n            // Fallback for older TypeScript releases without incremental API.\n            let builderProgram = ts.createIncrementalProgram\n                ? ts.createIncrementalProgram({\n                    rootNames: rootFileNames.slice(),\n                    options: config.options,\n                    host: host,\n                    configFileParsingDiagnostics: config.errors,\n                    projectReferences: config.projectReferences\n                })\n                : ts.createEmitAndSemanticDiagnosticsBuilderProgram(rootFileNames.slice(), config.options, host, undefined, config.errors, config.projectReferences);\n            // Read and cache custom transformers.\n            const customTransformers = typeof transformers === 'function'\n                ? transformers(builderProgram.getProgram())\n                : transformers;\n            // Set the file contents into cache manually.\n            const updateMemoryCache = (contents, fileName) => {\n                const sourceFile = builderProgram.getSourceFile(fileName);\n                fileContents.set(fileName, contents);\n                // Add to `rootFiles` when discovered by compiler for the first time.\n                if (sourceFile === undefined) {\n                    rootFileNames.push(fileName);\n                }\n                // Update program when file changes.\n                if (sourceFile === undefined || sourceFile.text !== contents) {\n                    builderProgram = ts.createEmitAndSemanticDiagnosticsBuilderProgram(rootFileNames.slice(), config.options, host, builderProgram, config.errors, config.projectReferences);\n                }\n            };\n            getOutput = (code, fileName) => {\n                const output = ['', ''];\n                updateMemoryCache(code, fileName);\n                const sourceFile = builderProgram.getSourceFile(fileName);\n                if (!sourceFile)\n                    throw new TypeError(`Unable to read file: ${fileName}`);\n                const program = builderProgram.getProgram();\n                const diagnostics = ts.getPreEmitDiagnostics(program, sourceFile);\n                const diagnosticList = filterDiagnostics(diagnostics, ignoreDiagnostics);\n                if (diagnosticList.length)\n                    reportTSError(diagnosticList);\n                const result = builderProgram.emit(sourceFile, (path, file, writeByteOrderMark) => {\n                    if (path.endsWith('.map')) {\n                        output[1] = file;\n                    }\n                    else {\n                        output[0] = file;\n                    }\n                    if (options.emit)\n                        sys.writeFile(path, file, writeByteOrderMark);\n                }, undefined, undefined, customTransformers);\n                if (result.emitSkipped) {\n                    throw new TypeError(`${path_1.relative(cwd, fileName)}: Emit skipped`);\n                }\n                // Throw an error when requiring files that cannot be compiled.\n                if (output[0] === '') {\n                    if (program.isSourceFileFromExternalLibrary(sourceFile)) {\n                        throw new TypeError(`Unable to compile file from external library: ${path_1.relative(cwd, fileName)}`);\n                    }\n                    throw new TypeError(`Unable to require file: ${path_1.relative(cwd, fileName)}\\n` +\n                        'This is usually the result of a faulty configuration or import. ' +\n                        'Make sure there is a `.js`, `.json` or other executable extension with ' +\n                        'loader attached before `ts-node` available.');\n                }\n                return output;\n            };\n            getTypeInfo = (code, fileName, position) => {\n                updateMemoryCache(code, fileName);\n                const sourceFile = builderProgram.getSourceFile(fileName);\n                if (!sourceFile)\n                    throw new TypeError(`Unable to read file: ${fileName}`);\n                const node = getTokenAtPosition(ts, sourceFile, position);\n                const checker = builderProgram.getProgram().getTypeChecker();\n                const symbol = checker.getSymbolAtLocation(node);\n                if (!symbol)\n                    return { name: '', comment: '' };\n                const type = checker.getTypeOfSymbolAtLocation(symbol, node);\n                const signatures = [...type.getConstructSignatures(), ...type.getCallSignatures()];\n                return {\n                    name: signatures.length ? signatures.map(x => checker.signatureToString(x)).join('\\n') : checker.typeToString(type),\n                    comment: ts.displayPartsToString(symbol ? symbol.getDocumentationComment(checker) : [])\n                };\n            };\n            // Write `.tsbuildinfo` when `--build` is enabled.\n            if (options.emit && config.options.incremental) {\n                process.on('exit', () => {\n                    // Emits `.tsbuildinfo` to filesystem.\n                    builderProgram.getProgram().emitBuildInfo();\n                });\n            }\n        }\n    }\n    else {\n        if (typeof transformers === 'function') {\n            throw new TypeError('Transformers function is unavailable in \"--transpile-only\"');\n        }\n        getOutput = getOutputTranspileOnly;\n        getTypeInfo = () => {\n            throw new TypeError('Type information is unavailable in \"--transpile-only\"');\n        };\n    }\n    const cannotCompileViaBothCodepathsErrorMessage = 'Cannot compile the same file via both `require()` and ESM hooks codepaths. ' +\n        'This breaks source-map-support, which cannot tell the difference between the two sourcemaps. ' +\n        'To avoid this problem, load each .ts file as only ESM or only CommonJS.';\n    // Create a simple TypeScript compiler proxy.\n    function compile(code, fileName, lineOffset = 0) {\n        const normalizedFileName = normalizeSlashes(fileName);\n        const [value, sourceMap] = getOutput(code, normalizedFileName);\n        const output = updateOutput(value, normalizedFileName, sourceMap, getExtension);\n        outputCache.set(normalizedFileName, { content: output });\n        return output;\n    }\n    let active = true;\n    const enabled = (enabled) => enabled === undefined ? active : (active = !!enabled);\n    const ignored = (fileName) => {\n        if (!active)\n            return true;\n        const relname = path_1.relative(cwd, fileName);\n        if (!config.options.allowJs) {\n            const ext = path_1.extname(fileName);\n            if (ext === '.js' || ext === '.jsx')\n                return true;\n        }\n        return !isScoped(relname) || shouldIgnore(relname);\n    };\n    return { ts, config, compile, getTypeInfo, ignored, enabled, options };\n}\nexports.create = create;\n/**\n * Check if the filename should be ignored.\n */\nfunction createIgnore(ignore) {\n    return (relname) => {\n        const path = normalizeSlashes(relname);\n        return ignore.some(x => x.test(path));\n    };\n}\n/**\n * \"Refreshes\" an extension on `require.extensions`.\n *\n * @param {string} ext\n */\nfunction reorderRequireExtension(ext) {\n    const old = require.extensions[ext]; // tslint:disable-line\n    delete require.extensions[ext]; // tslint:disable-line\n    require.extensions[ext] = old; // tslint:disable-line\n}\n/**\n * Register the extensions to support when importing files.\n */\nfunction registerExtensions(preferTsExts, extensions, register, originalJsHandler) {\n    // Register new extensions.\n    for (const ext of extensions) {\n        registerExtension(ext, register, originalJsHandler);\n    }\n    if (preferTsExts) {\n        // tslint:disable-next-line\n        const preferredExtensions = new Set([...extensions, ...Object.keys(require.extensions)]);\n        for (const ext of preferredExtensions)\n            reorderRequireExtension(ext);\n    }\n}\n/**\n * Register the extension for node.\n */\nfunction registerExtension(ext, register, originalHandler) {\n    const old = require.extensions[ext] || originalHandler; // tslint:disable-line\n    require.extensions[ext] = function (m, filename) {\n        if (register.ignored(filename))\n            return old(m, filename);\n        const _compile = m._compile;\n        m._compile = function (code, fileName) {\n            exports.debug('module._compile', fileName);\n            return _compile.call(this, register.compile(code, fileName), fileName);\n        };\n        return old(m, filename);\n    };\n}\n/**\n * Do post-processing on config options to support `ts-node`.\n */\nfunction fixConfig(ts, config) {\n    // Delete options that *should not* be passed through.\n    delete config.options.out;\n    delete config.options.outFile;\n    delete config.options.composite;\n    delete config.options.declarationDir;\n    delete config.options.declarationMap;\n    delete config.options.emitDeclarationOnly;\n    // Target ES5 output by default (instead of ES3).\n    if (config.options.target === undefined) {\n        config.options.target = ts.ScriptTarget.ES5;\n    }\n    // Target CommonJS modules by default (instead of magically switching to ES6 when the target is ES6).\n    if (config.options.module === undefined) {\n        config.options.module = ts.ModuleKind.CommonJS;\n    }\n    return config;\n}\n/**\n * Load TypeScript configuration. Returns the parsed TypeScript config and\n * any `ts-node` options specified in the config file.\n */\nfunction readConfig(cwd, ts, rawOptions) {\n    var _a, _b;\n    let config = { compilerOptions: {} };\n    let basePath = cwd;\n    let configFileName = undefined;\n    const { fileExists = ts.sys.fileExists, readFile = ts.sys.readFile, skipProject = exports.DEFAULTS.skipProject, project = exports.DEFAULTS.project } = rawOptions;\n    // Read project configuration when available.\n    if (!skipProject) {\n        configFileName = project\n            ? path_1.resolve(cwd, project)\n            : ts.findConfigFile(cwd, fileExists);\n        if (configFileName) {\n            const result = ts.readConfigFile(configFileName, readFile);\n            // Return diagnostics.\n            if (result.error) {\n                return {\n                    config: { errors: [result.error], fileNames: [], options: {} },\n                    options: {}\n                };\n            }\n            config = result.config;\n            basePath = path_1.dirname(configFileName);\n        }\n    }\n    // Fix ts-node options that come from tsconfig.json\n    const tsconfigOptions = Object.assign({}, config['ts-node']);\n    // Remove resolution of \"files\".\n    const files = (_b = (_a = rawOptions.files) !== null && _a !== void 0 ? _a : tsconfigOptions.files) !== null && _b !== void 0 ? _b : exports.DEFAULTS.files;\n    if (!files) {\n        config.files = [];\n        config.include = [];\n    }\n    // Override default configuration options `ts-node` requires.\n    config.compilerOptions = Object.assign({}, config.compilerOptions, exports.DEFAULTS.compilerOptions, tsconfigOptions.compilerOptions, rawOptions.compilerOptions, TS_NODE_COMPILER_OPTIONS);\n    const fixedConfig = fixConfig(ts, ts.parseJsonConfigFileContent(config, {\n        fileExists,\n        readFile,\n        readDirectory: ts.sys.readDirectory,\n        useCaseSensitiveFileNames: ts.sys.useCaseSensitiveFileNames\n    }, basePath, undefined, configFileName));\n    return { config: fixedConfig, options: tsconfigOptions };\n}\n/**\n * Update the output remapping the source map.\n */\nfunction updateOutput(outputText, fileName, sourceMap, getExtension) {\n    const base64Map = Buffer.from(updateSourceMap(sourceMap, fileName), 'utf8').toString('base64');\n    const sourceMapContent = `data:application/json;charset=utf-8;base64,${base64Map}`;\n    const sourceMapLength = `${path_1.basename(fileName)}.map`.length + (getExtension(fileName).length - path_1.extname(fileName).length);\n    return outputText.slice(0, -sourceMapLength) + sourceMapContent;\n}\n/**\n * Update the source map contents for improved output.\n */\nfunction updateSourceMap(sourceMapText, fileName) {\n    const sourceMap = JSON.parse(sourceMapText);\n    sourceMap.file = fileName;\n    sourceMap.sources = [fileName];\n    delete sourceMap.sourceRoot;\n    return JSON.stringify(sourceMap);\n}\n/**\n * Filter diagnostics.\n */\nfunction filterDiagnostics(diagnostics, ignore) {\n    return diagnostics.filter(x => ignore.indexOf(x.code) === -1);\n}\n/**\n * Get token at file position.\n *\n * Reference: https://github.com/microsoft/TypeScript/blob/fcd9334f57d85b73dd66ad2d21c02e84822f4841/src/services/utilities.ts#L705-L731\n */\nfunction getTokenAtPosition(ts, sourceFile, position) {\n    let current = sourceFile;\n    outer: while (true) {\n        for (const child of current.getChildren(sourceFile)) {\n            const start = child.getFullStart();\n            if (start > position)\n                break;\n            const end = child.getEnd();\n            if (position <= end) {\n                current = child;\n                continue outer;\n            }\n        }\n        return current;\n    }\n}\n//# sourceMappingURL=index.js.map", "module.exports = {\n  \"name\": \"ts-node\",\n  \"version\": \"8.10.2\",\n  \"description\": \"TypeScript execution environment and REPL for node.js, with source map support\",\n  \"main\": \"dist/index.js\",\n  \"types\": \"dist/index.d.ts\",\n  \"bin\": {\n    \"ts-node\": \"dist/bin.js\",\n    \"ts-script\": \"dist/bin-script-deprecated.js\",\n    \"ts-node-script\": \"dist/bin-script.js\",\n    \"ts-node-transpile-only\": \"dist/bin-transpile.js\"\n  },\n  \"files\": [\n    \"dist/\",\n    \"dist-raw/\",\n    \"register/\",\n    \"esm.mjs\",\n    \"LICENSE\",\n    \"tsconfig.schema.json\",\n    \"tsconfig.schemastore-schema.json\"\n  ],\n  \"scripts\": {\n    \"lint\": \"tslint \\\"src/**/*.ts\\\" --project tsconfig.json\",\n    \"lint-fix\": \"tslint \\\"src/**/*.ts\\\" --project tsconfig.json --fix\",\n    \"clean\": \"rimraf dist && rimraf tsconfig.schema.json && rimraf tsconfig.schemastore-schema.json && rimraf tests/ts-node-packed.tgz\",\n    \"build\": \"npm run build-nopack && npm run build-pack\",\n    \"build-nopack\": \"npm run clean && npm run build-tsc && npm run build-configSchema\",\n    \"build-tsc\": \"tsc\",\n    \"build-configSchema\": \"typescript-json-schema --topRef --refs --validationKeywords allOf --out tsconfig.schema.json tsconfig.json TsConfigSchema && node --require ./register ./scripts/create-merged-schema\",\n    \"build-pack\": \"node ./scripts/build-pack.js\",\n    \"test-spec\": \"mocha dist/**/*.spec.js -R spec --bail\",\n    \"test-cov\": \"istanbul cover node_modules/mocha/bin/_mocha -- \\\"dist/**/*.spec.js\\\" -R spec --bail\",\n    \"test\": \"npm run build && npm run lint && npm run test-cov\",\n    \"prepare\": \"npm run build-nopack\"\n  },\n  \"engines\": {\n    \"node\": \">=6.0.0\"\n  },\n  \"repository\": {\n    \"type\": \"git\",\n    \"url\": \"git://github.com/TypeStrong/ts-node.git\"\n  },\n  \"keywords\": [\n    \"typescript\",\n    \"node\",\n    \"runtime\",\n    \"environment\",\n    \"ts\",\n    \"compiler\"\n  ],\n  \"author\": {\n    \"name\": \"Blake Embrey\",\n    \"email\": \"<EMAIL>\",\n    \"url\": \"http://blakeembrey.me\"\n  },\n  \"license\": \"MIT\",\n  \"bugs\": {\n    \"url\": \"https://github.com/TypeStrong/ts-node/issues\"\n  },\n  \"homepage\": \"https://github.com/TypeStrong/ts-node\",\n  \"devDependencies\": {\n    \"@types/chai\": \"^4.0.4\",\n    \"@types/diff\": \"^4.0.2\",\n    \"@types/mocha\": \"^5.2.7\",\n    \"@types/node\": \"13.13.5\",\n    \"@types/proxyquire\": \"^1.3.28\",\n    \"@types/react\": \"^16.0.2\",\n    \"@types/semver\": \"^7.1.0\",\n    \"@types/source-map-support\": \"^0.5.0\",\n    \"axios\": \"^0.19.0\",\n    \"chai\": \"^4.0.1\",\n    \"istanbul\": \"^0.4.0\",\n    \"mocha\": \"^6.2.2\",\n    \"ntypescript\": \"^1.201507091536.1\",\n    \"proxyquire\": \"^2.0.0\",\n    \"react\": \"^16.0.0\",\n    \"rimraf\": \"^3.0.0\",\n    \"semver\": \"^7.1.3\",\n    \"tslint\": \"^6.1.0\",\n    \"tslint-config-standard\": \"^9.0.0\",\n    \"typescript\": \"3.8.3\",\n    \"typescript-json-schema\": \"^0.42.0\",\n    \"util.promisify\": \"^1.0.1\"\n  },\n  \"peerDependencies\": {\n    \"typescript\": \">=2.7\"\n  },\n  \"dependencies\": {\n    \"arg\": \"^4.1.0\",\n    \"diff\": \"^4.0.1\",\n    \"make-error\": \"^1.1.1\",\n    \"source-map-support\": \"^0.5.17\",\n    \"yn\": \"3.1.1\"\n  }\n}\n"]}