{"version": 3, "sources": ["index.js", "ber/index.js", "ber/errors.js", "ber/types.js", "ber/reader.js", "ber/writer.js"], "names": [], "mappings": ";;;;;;;AAAA;AACA;AACA;ACFA,ADGA;AC<PERSON>,ADGA;ACFA,ADGA;AELA,ADGA,ADGA;AELA,ADGA,ADGA;AELA,ADGA,ADGA;AELA,ADGA,AENA,AHSA;AELA,ADGA,AENA,AHSA;AELA,ADGA,AENA,AHSA;AELA,ADGA,AGTA,ADGA,AHSA;AELA,ADGA,AGTA,ADGA,AHSA;AELA,ADGA,AGTA,ADGA,AHSA;AELA,ADGA,AGTA,ADGA,AENA,ALeA;AELA,ADGA,AGTA,ADGA,AENA,ALeA;AELA,ADGA,AGTA,ADGA,AENA,ALeA;AELA,ADGA,AGTA,ADGA,AENA,ALeA;AELA,ADGA,AGTA,ADGA,AENA,ALeA;ACFA,AGTA,ADGA,AENA,ALeA;ACFA,AGTA,ADGA,AENA;AJaA,AGTA,ADGA,AENA;AJaA,AGTA,ADGA,AENA;AJaA,AGTA,ADGA,AENA;AJaA,AGTA,ADGA,AENA;AJaA,AGTA,ADGA,AENA;AJaA,AGTA,ADGA,AENA;AJaA,AGTA,ADGA,AENA;AJaA,AGTA,ADGA,AENA;AJaA,AGTA,ADGA,AENA;ADIA,ADGA,AENA;ADIA,ADGA,AENA;ADIA,ADGA,AENA;ADIA,ADGA,AENA;ADIA,ADGA,AENA;ADIA,ADGA,AENA;ADIA,ADGA,AENA;ADIA,ADGA,AENA;ADIA,ADGA,AENA;ADIA,ADGA,AENA;ADIA,ADGA,AENA;ADIA,ADGA,AENA;ADIA,ADGA,AENA;ADIA,ADGA,AENA;ADIA,ADGA,AENA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.js", "sourcesContent": ["// Copyright 2011 <PERSON> <<EMAIL>> All rights reserved.\n\n// If you have no idea what ASN.1 or BER is, see this:\n// ftp://ftp.rsa.com/pub/pkcs/ascii/layman.asc\n\nvar Ber = require('./ber/index');\n\n\n\n// --- Exported API\n\nmodule.exports = {\n\n  Ber: Ber,\n\n  BerReader: Ber.Reader,\n\n  BerWriter: Ber.Writer\n\n};\n", "// Copyright 2011 <PERSON> <<EMAIL>> All rights reserved.\n\nvar errors = require('./errors');\nvar types = require('./types');\n\nvar Reader = require('./reader');\nvar Writer = require('./writer');\n\n\n// --- Exports\n\nmodule.exports = {\n\n  Reader: Reader,\n\n  Writer: Writer\n\n};\n\nfor (var t in types) {\n  if (types.hasOwnProperty(t))\n    module.exports[t] = types[t];\n}\nfor (var e in errors) {\n  if (errors.hasOwnProperty(e))\n    module.exports[e] = errors[e];\n}\n", "// Copyright 2011 <PERSON> <<EMAIL>> All rights reserved.\n\n\nmodule.exports = {\n\n  newInvalidAsn1Error: function (msg) {\n    var e = new Error();\n    e.name = 'InvalidAsn1Error';\n    e.message = msg || '';\n    return e;\n  }\n\n};\n", "// Copyright 2011 <PERSON> <<EMAIL>> All rights reserved.\n\n\nmodule.exports = {\n  EOC: 0,\n  Boolean: 1,\n  Integer: 2,\n  BitString: 3,\n  OctetString: 4,\n  Null: 5,\n  OID: 6,\n  ObjectDescriptor: 7,\n  External: 8,\n  Real: 9, // float\n  Enumeration: 10,\n  PDV: 11,\n  Utf8String: 12,\n  RelativeOID: 13,\n  Sequence: 16,\n  Set: 17,\n  NumericString: 18,\n  PrintableString: 19,\n  T61String: 20,\n  VideotexString: 21,\n  IA5String: 22,\n  UTCTime: 23,\n  GeneralizedTime: 24,\n  GraphicString: 25,\n  VisibleString: 26,\n  GeneralString: 28,\n  UniversalString: 29,\n  CharacterString: 30,\n  BMPString: 31,\n  Constructor: 32,\n  Context: 128\n};\n", "// Copyright 2011 <PERSON> <<EMAIL>> All rights reserved.\n\nvar assert = require('assert');\nvar Buffer = require('safer-buffer').Buffer;\n\nvar ASN1 = require('./types');\nvar errors = require('./errors');\n\n\n// --- Globals\n\nvar newInvalidAsn1Error = errors.newInvalidAsn1Error;\n\n\n\n// --- API\n\nfunction Reader(data) {\n  if (!data || !Buffer.isBuffer(data))\n    throw new TypeError('data must be a node Buffer');\n\n  this._buf = data;\n  this._size = data.length;\n\n  // These hold the \"current\" state\n  this._len = 0;\n  this._offset = 0;\n}\n\nObject.defineProperty(Reader.prototype, 'length', {\n  enumerable: true,\n  get: function () { return (this._len); }\n});\n\nObject.defineProperty(Reader.prototype, 'offset', {\n  enumerable: true,\n  get: function () { return (this._offset); }\n});\n\nObject.defineProperty(Reader.prototype, 'remain', {\n  get: function () { return (this._size - this._offset); }\n});\n\nObject.defineProperty(Reader.prototype, 'buffer', {\n  get: function () { return (this._buf.slice(this._offset)); }\n});\n\n\n/**\n * Reads a single byte and advances offset; you can pass in `true` to make this\n * a \"peek\" operation (i.e., get the byte, but don't advance the offset).\n *\n * @param {Boolean} peek true means don't move offset.\n * @return {Number} the next byte, null if not enough data.\n */\nReader.prototype.readByte = function (peek) {\n  if (this._size - this._offset < 1)\n    return null;\n\n  var b = this._buf[this._offset] & 0xff;\n\n  if (!peek)\n    this._offset += 1;\n\n  return b;\n};\n\n\nReader.prototype.peek = function () {\n  return this.readByte(true);\n};\n\n\n/**\n * Reads a (potentially) variable length off the BER buffer.  This call is\n * not really meant to be called directly, as callers have to manipulate\n * the internal buffer afterwards.\n *\n * As a result of this call, you can call `Reader.length`, until the\n * next thing called that does a readLength.\n *\n * @return {Number} the amount of offset to advance the buffer.\n * @throws {InvalidAsn1Error} on bad ASN.1\n */\nReader.prototype.readLength = function (offset) {\n  if (offset === undefined)\n    offset = this._offset;\n\n  if (offset >= this._size)\n    return null;\n\n  var lenB = this._buf[offset++] & 0xff;\n  if (lenB === null)\n    return null;\n\n  if ((lenB & 0x80) === 0x80) {\n    lenB &= 0x7f;\n\n    if (lenB === 0)\n      throw newInvalidAsn1Error('Indefinite length not supported');\n\n    if (lenB > 4)\n      throw newInvalidAsn1Error('encoding too long');\n\n    if (this._size - offset < lenB)\n      return null;\n\n    this._len = 0;\n    for (var i = 0; i < lenB; i++)\n      this._len = (this._len << 8) + (this._buf[offset++] & 0xff);\n\n  } else {\n    // Wasn't a variable length\n    this._len = lenB;\n  }\n\n  return offset;\n};\n\n\n/**\n * Parses the next sequence in this BER buffer.\n *\n * To get the length of the sequence, call `Reader.length`.\n *\n * @return {Number} the sequence's tag.\n */\nReader.prototype.readSequence = function (tag) {\n  var seq = this.peek();\n  if (seq === null)\n    return null;\n  if (tag !== undefined && tag !== seq)\n    throw newInvalidAsn1Error('Expected 0x' + tag.toString(16) +\n                              ': got 0x' + seq.toString(16));\n\n  var o = this.readLength(this._offset + 1); // stored in `length`\n  if (o === null)\n    return null;\n\n  this._offset = o;\n  return seq;\n};\n\n\nReader.prototype.readInt = function () {\n  return this._readTag(ASN1.Integer);\n};\n\n\nReader.prototype.readBoolean = function () {\n  return (this._readTag(ASN1.Boolean) === 0 ? false : true);\n};\n\n\nReader.prototype.readEnumeration = function () {\n  return this._readTag(ASN1.Enumeration);\n};\n\n\nReader.prototype.readString = function (tag, retbuf) {\n  if (!tag)\n    tag = ASN1.OctetString;\n\n  var b = this.peek();\n  if (b === null)\n    return null;\n\n  if (b !== tag)\n    throw newInvalidAsn1Error('Expected 0x' + tag.toString(16) +\n                              ': got 0x' + b.toString(16));\n\n  var o = this.readLength(this._offset + 1); // stored in `length`\n\n  if (o === null)\n    return null;\n\n  if (this.length > this._size - o)\n    return null;\n\n  this._offset = o;\n\n  if (this.length === 0)\n    return retbuf ? Buffer.alloc(0) : '';\n\n  var str = this._buf.slice(this._offset, this._offset + this.length);\n  this._offset += this.length;\n\n  return retbuf ? str : str.toString('utf8');\n};\n\nReader.prototype.readOID = function (tag) {\n  if (!tag)\n    tag = ASN1.OID;\n\n  var b = this.readString(tag, true);\n  if (b === null)\n    return null;\n\n  var values = [];\n  var value = 0;\n\n  for (var i = 0; i < b.length; i++) {\n    var byte = b[i] & 0xff;\n\n    value <<= 7;\n    value += byte & 0x7f;\n    if ((byte & 0x80) === 0) {\n      values.push(value);\n      value = 0;\n    }\n  }\n\n  value = values.shift();\n  values.unshift(value % 40);\n  values.unshift((value / 40) >> 0);\n\n  return values.join('.');\n};\n\n\nReader.prototype._readTag = function (tag) {\n  assert.ok(tag !== undefined);\n\n  var b = this.peek();\n\n  if (b === null)\n    return null;\n\n  if (b !== tag)\n    throw newInvalidAsn1Error('Expected 0x' + tag.toString(16) +\n                              ': got 0x' + b.toString(16));\n\n  var o = this.readLength(this._offset + 1); // stored in `length`\n  if (o === null)\n    return null;\n\n  if (this.length > 4)\n    throw newInvalidAsn1Error('Integer too long: ' + this.length);\n\n  if (this.length > this._size - o)\n    return null;\n  this._offset = o;\n\n  var fb = this._buf[this._offset];\n  var value = 0;\n\n  for (var i = 0; i < this.length; i++) {\n    value <<= 8;\n    value |= (this._buf[this._offset++] & 0xff);\n  }\n\n  if ((fb & 0x80) === 0x80 && i !== 4)\n    value -= (1 << (i * 8));\n\n  return value >> 0;\n};\n\n\n\n// --- Exported API\n\nmodule.exports = Reader;\n", "// Copyright 2011 <PERSON> <<EMAIL>> All rights reserved.\n\nvar assert = require('assert');\nvar Buffer = require('safer-buffer').Buffer;\nvar ASN1 = require('./types');\nvar errors = require('./errors');\n\n\n// --- Globals\n\nvar newInvalidAsn1Error = errors.newInvalidAsn1Error;\n\nvar DEFAULT_OPTS = {\n  size: 1024,\n  growthFactor: 8\n};\n\n\n// --- Helpers\n\nfunction merge(from, to) {\n  assert.ok(from);\n  assert.equal(typeof (from), 'object');\n  assert.ok(to);\n  assert.equal(typeof (to), 'object');\n\n  var keys = Object.getOwnPropertyNames(from);\n  keys.forEach(function (key) {\n    if (to[key])\n      return;\n\n    var value = Object.getOwnPropertyDescriptor(from, key);\n    Object.defineProperty(to, key, value);\n  });\n\n  return to;\n}\n\n\n\n// --- API\n\nfunction Writer(options) {\n  options = merge(DEFAULT_OPTS, options || {});\n\n  this._buf = Buffer.alloc(options.size || 1024);\n  this._size = this._buf.length;\n  this._offset = 0;\n  this._options = options;\n\n  // A list of offsets in the buffer where we need to insert\n  // sequence tag/len pairs.\n  this._seq = [];\n}\n\nObject.defineProperty(Writer.prototype, 'buffer', {\n  get: function () {\n    if (this._seq.length)\n      throw newInvalidAsn1Error(this._seq.length + ' unended sequence(s)');\n\n    return (this._buf.slice(0, this._offset));\n  }\n});\n\nWriter.prototype.writeByte = function (b) {\n  if (typeof (b) !== 'number')\n    throw new TypeError('argument must be a Number');\n\n  this._ensure(1);\n  this._buf[this._offset++] = b;\n};\n\n\nWriter.prototype.writeInt = function (i, tag) {\n  if (typeof (i) !== 'number')\n    throw new TypeError('argument must be a Number');\n  if (typeof (tag) !== 'number')\n    tag = ASN1.Integer;\n\n  var sz = 4;\n\n  while ((((i & 0xff800000) === 0) || ((i & 0xff800000) === 0xff800000 >> 0)) &&\n        (sz > 1)) {\n    sz--;\n    i <<= 8;\n  }\n\n  if (sz > 4)\n    throw newInvalidAsn1Error('BER ints cannot be > 0xffffffff');\n\n  this._ensure(2 + sz);\n  this._buf[this._offset++] = tag;\n  this._buf[this._offset++] = sz;\n\n  while (sz-- > 0) {\n    this._buf[this._offset++] = ((i & 0xff000000) >>> 24);\n    i <<= 8;\n  }\n\n};\n\n\nWriter.prototype.writeNull = function () {\n  this.writeByte(ASN1.Null);\n  this.writeByte(0x00);\n};\n\n\nWriter.prototype.writeEnumeration = function (i, tag) {\n  if (typeof (i) !== 'number')\n    throw new TypeError('argument must be a Number');\n  if (typeof (tag) !== 'number')\n    tag = ASN1.Enumeration;\n\n  return this.writeInt(i, tag);\n};\n\n\nWriter.prototype.writeBoolean = function (b, tag) {\n  if (typeof (b) !== 'boolean')\n    throw new TypeError('argument must be a Boolean');\n  if (typeof (tag) !== 'number')\n    tag = ASN1.Boolean;\n\n  this._ensure(3);\n  this._buf[this._offset++] = tag;\n  this._buf[this._offset++] = 0x01;\n  this._buf[this._offset++] = b ? 0xff : 0x00;\n};\n\n\nWriter.prototype.writeString = function (s, tag) {\n  if (typeof (s) !== 'string')\n    throw new TypeError('argument must be a string (was: ' + typeof (s) + ')');\n  if (typeof (tag) !== 'number')\n    tag = ASN1.OctetString;\n\n  var len = Buffer.byteLength(s);\n  this.writeByte(tag);\n  this.writeLength(len);\n  if (len) {\n    this._ensure(len);\n    this._buf.write(s, this._offset);\n    this._offset += len;\n  }\n};\n\n\nWriter.prototype.writeBuffer = function (buf, tag) {\n  if (typeof (tag) !== 'number')\n    throw new TypeError('tag must be a number');\n  if (!Buffer.isBuffer(buf))\n    throw new TypeError('argument must be a buffer');\n\n  this.writeByte(tag);\n  this.writeLength(buf.length);\n  this._ensure(buf.length);\n  buf.copy(this._buf, this._offset, 0, buf.length);\n  this._offset += buf.length;\n};\n\n\nWriter.prototype.writeStringArray = function (strings) {\n  if ((!strings instanceof Array))\n    throw new TypeError('argument must be an Array[String]');\n\n  var self = this;\n  strings.forEach(function (s) {\n    self.writeString(s);\n  });\n};\n\n// This is really to solve DER cases, but whatever for now\nWriter.prototype.writeOID = function (s, tag) {\n  if (typeof (s) !== 'string')\n    throw new TypeError('argument must be a string');\n  if (typeof (tag) !== 'number')\n    tag = ASN1.OID;\n\n  if (!/^([0-9]+\\.){3,}[0-9]+$/.test(s))\n    throw new Error('argument is not a valid OID string');\n\n  function encodeOctet(bytes, octet) {\n    if (octet < 128) {\n        bytes.push(octet);\n    } else if (octet < 16384) {\n        bytes.push((octet >>> 7) | 0x80);\n        bytes.push(octet & 0x7F);\n    } else if (octet < 2097152) {\n      bytes.push((octet >>> 14) | 0x80);\n      bytes.push(((octet >>> 7) | 0x80) & 0xFF);\n      bytes.push(octet & 0x7F);\n    } else if (octet < 268435456) {\n      bytes.push((octet >>> 21) | 0x80);\n      bytes.push(((octet >>> 14) | 0x80) & 0xFF);\n      bytes.push(((octet >>> 7) | 0x80) & 0xFF);\n      bytes.push(octet & 0x7F);\n    } else {\n      bytes.push(((octet >>> 28) | 0x80) & 0xFF);\n      bytes.push(((octet >>> 21) | 0x80) & 0xFF);\n      bytes.push(((octet >>> 14) | 0x80) & 0xFF);\n      bytes.push(((octet >>> 7) | 0x80) & 0xFF);\n      bytes.push(octet & 0x7F);\n    }\n  }\n\n  var tmp = s.split('.');\n  var bytes = [];\n  bytes.push(parseInt(tmp[0], 10) * 40 + parseInt(tmp[1], 10));\n  tmp.slice(2).forEach(function (b) {\n    encodeOctet(bytes, parseInt(b, 10));\n  });\n\n  var self = this;\n  this._ensure(2 + bytes.length);\n  this.writeByte(tag);\n  this.writeLength(bytes.length);\n  bytes.forEach(function (b) {\n    self.writeByte(b);\n  });\n};\n\n\nWriter.prototype.writeLength = function (len) {\n  if (typeof (len) !== 'number')\n    throw new TypeError('argument must be a Number');\n\n  this._ensure(4);\n\n  if (len <= 0x7f) {\n    this._buf[this._offset++] = len;\n  } else if (len <= 0xff) {\n    this._buf[this._offset++] = 0x81;\n    this._buf[this._offset++] = len;\n  } else if (len <= 0xffff) {\n    this._buf[this._offset++] = 0x82;\n    this._buf[this._offset++] = len >> 8;\n    this._buf[this._offset++] = len;\n  } else if (len <= 0xffffff) {\n    this._buf[this._offset++] = 0x83;\n    this._buf[this._offset++] = len >> 16;\n    this._buf[this._offset++] = len >> 8;\n    this._buf[this._offset++] = len;\n  } else {\n    throw newInvalidAsn1Error('Length too long (> 4 bytes)');\n  }\n};\n\nWriter.prototype.startSequence = function (tag) {\n  if (typeof (tag) !== 'number')\n    tag = ASN1.Sequence | ASN1.Constructor;\n\n  this.writeByte(tag);\n  this._seq.push(this._offset);\n  this._ensure(3);\n  this._offset += 3;\n};\n\n\nWriter.prototype.endSequence = function () {\n  var seq = this._seq.pop();\n  var start = seq + 3;\n  var len = this._offset - start;\n\n  if (len <= 0x7f) {\n    this._shift(start, len, -2);\n    this._buf[seq] = len;\n  } else if (len <= 0xff) {\n    this._shift(start, len, -1);\n    this._buf[seq] = 0x81;\n    this._buf[seq + 1] = len;\n  } else if (len <= 0xffff) {\n    this._buf[seq] = 0x82;\n    this._buf[seq + 1] = len >> 8;\n    this._buf[seq + 2] = len;\n  } else if (len <= 0xffffff) {\n    this._shift(start, len, 1);\n    this._buf[seq] = 0x83;\n    this._buf[seq + 1] = len >> 16;\n    this._buf[seq + 2] = len >> 8;\n    this._buf[seq + 3] = len;\n  } else {\n    throw newInvalidAsn1Error('Sequence too long');\n  }\n};\n\n\nWriter.prototype._shift = function (start, len, shift) {\n  assert.ok(start !== undefined);\n  assert.ok(len !== undefined);\n  assert.ok(shift);\n\n  this._buf.copy(this._buf, start + shift, start, start + len);\n  this._offset += shift;\n};\n\nWriter.prototype._ensure = function (len) {\n  assert.ok(len);\n\n  if (this._size - this._offset < len) {\n    var sz = this._size * this._options.growthFactor;\n    if (sz - this._offset < len)\n      sz += len;\n\n    var buf = Buffer.alloc(sz);\n\n    this._buf.copy(buf, 0, 0, this._offset);\n    this._buf = buf;\n    this._size = sz;\n  }\n};\n\n\n\n// --- Exported API\n\nmodule.exports = Writer;\n"]}