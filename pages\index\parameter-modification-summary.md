# 参数模式调节逻辑修改总结

## 修改概述
根据用户需求，将参数调节逻辑改为直接调用内网IP设备的摄像头参数API，同时保留前端Canvas模拟绘制功能。区分本地视频和设备连接两种模式。

## 主要修改内容

### 1. 参数调节时机控制和模式区分
- **修改位置**: `pages/index/index.js` 中的 `updateParameterImmediate` 和 `updateVideoParams` 方法
- **修改内容**:
  - **本地视频模式** (`isLocalVideo: true`)：只使用Canvas模拟绘制，不调用设备API
  - **设备连接模式** (`isLocalVideo: false`)：需要 `isReceivingVideo: true` 且 `deviceIpAddress` 存在才能调节参数
  - 确保只有在"开始接收视频"按钮点击后并且restart成功后才能进行设备参数调节

### 2. 参数映射表更新
- **修改位置**: 两个参数更新方法中的 `parameterMap` 对象
- **更新内容**: 根据用户提供的API规范添加参数范围注释
  - 亮度：brightness (0-240)
  - 对比度：contrast (0-255)
  - 饱和度：saturation (0-255)
  - 自动白平衡：white_balance_temperature_auto (0-1)
  - 增益：gain (0-100)
  - 电力线频率：power_line_frequency (0-2)
  - 白平衡：white_balance_temperature (2600-6500)
  - 清晰度：sharpness (0-255)
  - 自动曝光：exposure_auto (0-3)
  - 曝光值：exposure_absolute (5-2500)
  - 摄像头水平转动：pan_absolute (-36000-36000)
  - 摄像头垂直转动：tilt_absolute (-36000-36000)
  - 摄像头焦点移动：focus_absolute (0-255)
  - 摄像头移动速度：zoom_absolute (100-190)
  - 电压：setVoltage (5000-20000)

### 3. 双重参数处理机制
- **本地视频模式**: 只使用Canvas模拟绘制，不调用设备API
- **设备连接模式**: 同时使用Canvas预览和设备API调用
- **处理顺序**:
  1. 立即更新UI状态
  2. 应用Canvas预览效果
  3. 防抖后发送设备API请求（仅设备连接模式）

### 4. 录制视频完成后的行为修改
- **修改位置**: `_saveAndUploadDeviceVideo` 方法
- **修改内容**: 移除设备录制完成后自动切换到参数模式的逻辑
- **新行为**: 设备录制完成后不自动切换，用户需要手动进入参数模式

### 4. UI状态提示
- **修改位置**: `pages/index/index.wxml` 参数模式开头
- **添加内容**: 
  - 设备未连接时显示警告提示
  - 设备已连接时显示成功提示和IP地址
- **样式**: 在 `pages/index/index.wxss` 中添加对应的CSS样式

### 5. 连接成功提示优化
- **修改位置**: `_simpleRestartCheck` 方法的成功回调
- **修改内容**: 将提示文本改为"设备连接成功，可调节参数"，明确告知用户参数调节功能已启用

## API调用格式
所有参数调节都通过以下格式的HTTP GET请求发送到设备：
```
http://{deviceIp}/cgi-bin/set_camera_param.cgi?{paramName}={value}
```

例如：
- 亮度调节: `http://***************/cgi-bin/set_camera_param.cgi?brightness=115`
- 对比度调节: `http://***************/cgi-bin/set_camera_param.cgi?contrast=115`

## 功能特点
1. **模式区分**: 自动识别本地视频和设备连接模式，采用不同的参数处理策略
2. **安全性**: 设备连接模式下只有连接成功后才能调节参数
3. **实时性**: 前端Canvas立即显示参数效果
4. **可靠性**: 设备API调用带有防抖和重试机制
5. **用户友好**: 清晰的状态提示告知用户当前是否可以调节参数
6. **兼容性**: 保留了原有的Canvas模拟绘制功能

## 使用流程

### 本地视频上传模式
1. 点击"本地上传视频"按钮上传视频
2. 自动进入参数模式（保持原有逻辑）
3. 调节参数时只使用Canvas模拟绘制，不调用设备API

### 设备连接模式
1. 点击"开始接收视频"按钮连接设备
2. 等待restart成功的提示
3. 手动点击"进入参数模式"
4. 调节参数时同时使用Canvas预览和设备API调用

### 设备录制模式
1. 连接设备后点击"开始录制"按钮
2. 录制完成后不自动切换到参数模式
3. 用户需要手动进入参数模式进行分析

## 注意事项
- 录制时间参数已集成到开始录制按钮中，不在参数列表中显示
- 电压参数使用不同的API端点：`set_mcu_param.cgi`
- 本地视频模式下参数调节只影响前端显示，不会调用设备API
- 设备连接模式下参数调节会同时更新前端显示和设备硬件设置
