package com.herohan.uvcdemo;



import wseemann.media.FFmpegMediaMetadataRetriever;
import androidx.appcompat.app.AppCompatActivity;
import android.os.Bundle;
import org.opencv.android.Utils;
import org.opencv.core.CvType;
import java.text.DecimalFormat;

import android.database.sqlite.SQLiteDatabase;

import org.opencv.core.Mat;
import org.opencv.imgproc.Imgproc;
import android.media.MediaMetadataRetriever;
import android.media.ThumbnailUtils;
import android.provider.MediaStore;

import java.util.function.Supplier;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.Future;
import android.graphics.Bitmap;
import android.graphics.Color;
import android.media.MediaMetadataRetriever;
import android.os.Environment;
import android.provider.MediaStore;

import org.opencv.android.Utils.*;

import java.io.File;
import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.atomic.AtomicReference;
import java.util.*;
import java.util.stream.*;
import android.content.Context;
import java.util.concurrent.atomic.*;
import java.util.concurrent.Future;
import android.graphics.BitmapFactory;
import java.util.ArrayList;
import java.util.concurrent.Executors;
import android.graphics.Bitmap;
import android.graphics.Color;
import android.media.MediaMetadataRetriever;
import android.os.Environment;
import android.os.Handler;
import android.os.Looper;
import android.os.Message;
import android.provider.MediaStore;
import android.widget.ImageView;
import android.widget.TextView;
import java.io.File;
import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.io.IOException;
import java.util.concurrent.Callable;

import android.content.Intent;
import android.view.View;
import android.widget.Button;
import android.widget.EditText;
import android.database.Cursor;

import android.widget.TextView;
import android.widget.Toast;
import android.Manifest;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.graphics.Bitmap;
import android.graphics.Color;
import android.graphics.SurfaceTexture;
import android.media.MediaMetadataRetriever;
import android.net.Uri;
import android.os.Build;
import android.os.Bundle;
import android.os.Environment;
import java.util.concurrent.ExecutionException;
import android.provider.MediaStore;
import android.util.Log;
import android.view.Surface;
import android.view.TextureView;
import android.view.View;
import android.widget.Button;
import java.io.File;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.TextView;
import android.widget.Toast;
import androidx.annotation.NonNull;
import androidx.annotation.RequiresApi;
import androidx.appcompat.app.AppCompatActivity;
import androidx.core.app.ActivityCompat;

import org.opencv.core.Core;
import org.opencv.core.Mat;
import java.io.*;

import org.opencv.core.MatOfRect;
import org.opencv.core.Rect;
import org.opencv.core.Scalar;
import org.opencv.imgcodecs.Imgcodecs;
import org.opencv.imgproc.Imgproc;
import java.io.File;
import android.content.ContentValues;

import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.io.IOException;
import org.opencv.android.Utils;
import org.opencv.android.LoaderCallbackInterface;
import org.opencv.android.OpenCVLoader;



public class GrayValueActivity extends AppCompatActivity{
    static {
        if (OpenCVLoader.initDebug()) {
            Log.d("MainActivity", "OpenCV successfully loaded!");
        } else {
            Log.d("MainActivity", "OpenCV not loaded!");
        }
    }
    private TextView textView,shipin1,textViewname,textView2;
    private Button backBtn,btnZs,jisuan,btn5,btn10,btn20,btn30,btn50,
            btnl,btnr,btnrr,btnSp,btnsp1,btnsp2,btnsp3,btnsp4,btnsp5,xstp;

    private EditText shipin;
    volatile String shipinName = null;
    volatile int zuida = -1;
    volatile String sss1 = null;
    volatile String newestVideoName = null;
    volatile String newestVideoName1 = null;
    volatile String newestVideoName2 = null;
    volatile String newestVideoName3 = null;
    volatile String newestVideoName4 = null;

    private MyDatabaseHelper dbHelper;
    @Override
    protected void onCreate(Bundle savedInstanceState) {


        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_gray_value);
        setTitle("视频分析");



        // 指定文件夹路径
        String folderPath = "/storage/emulated/0/Movies/双电极ECL即时检测/";
// 获取指定文件夹内的文件列表
        File folder = new File(folderPath);
        File[] files = folder.listFiles(new FilenameFilter() {
            @Override
            public boolean accept(File dir, String name) {
                return name.toLowerCase().endsWith(".avi"); // 只筛选 avi 格式的视频文件
            }
        });

// 找到最新的视频文件名
        if (files != null && files.length > 0) {
            Arrays.sort(files, new Comparator<File>() {
                public int compare(File f1, File f2) {
                    return Long.compare(f2.lastModified(), f1.lastModified());
                }
            });
            int index = files[0].getName().lastIndexOf(".");
            newestVideoName =  files[0].getName().substring(0, index);
//            index = files[1].getName().lastIndexOf(".");
//            newestVideoName1 =  files[1].getName().substring(0, index);
//
//            index = files[2].getName().lastIndexOf(".");
//            newestVideoName2 =  files[2].getName().substring(0, index);
//            index = files[3].getName().lastIndexOf(".");
//            newestVideoName3 =  files[3].getName().substring(0, index);
//            index = files[4].getName().lastIndexOf(".");
//            newestVideoName4 =  files[4].getName().substring(0, index);


            shipinName = newestVideoName;

        }






            initView();





    }
    // 加载 OpenCV 库


    public void initView(){

        textView = findViewById(R.id.textView);
        textViewname = findViewById(R.id.textViewname);
        textView2 = findViewById(R.id.textView2);
        shipin = findViewById(R.id.shipin);
        jisuan = findViewById(R.id.btn_jisuan);
        btnSp = findViewById(R.id.btnSp);

        xstp = findViewById(R.id.xstp);


        shipin1 = findViewById(R.id.shipin1);


       String aviPath = "/storage/emulated/0/Movies/双电极ECL即时检测/"+shipinName+".avi";
        final MediaMetadataRetriever mmr2 = new MediaMetadataRetriever();
        mmr2.setDataSource(aviPath);
        String durationString2 = mmr2.extractMetadata(MediaMetadataRetriever.METADATA_KEY_DURATION);



        shipin1.setText("当前视频:"+newestVideoName+".avi,时长:"+durationString2+"ms");

        btnsp1=findViewById(R.id.btnsp1);
        btnsp2=findViewById(R.id.btnsp2);
        btnsp3=findViewById(R.id.btnsp3);
        btnsp5=findViewById(R.id.btnsp5);
        btnsp4=findViewById(R.id.btnsp4);
        btnl = findViewById(R.id.btn_jisuanl);
        btnr = findViewById(R.id.btn_jisuanr);
        btnrr = findViewById(R.id.btn_jisuanrr);
        backBtn = findViewById(R.id.BACK);



        xstp.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {

                MyDatabaseHelper dbHelper = new MyDatabaseHelper(GrayValueActivity.this);
                dbHelper.addUser(textView.getText().toString(),textViewname.getText().toString());

                Toast.makeText(GrayValueActivity.this, "本次分析数据保存成功", Toast.LENGTH_SHORT).show();
            }
        });
        btnSp.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                shipinName=shipin.getText().toString();

                String aviPath = null;

                try {
                    aviPath = "/storage/emulated/0/Movies/双电极ECL即时检测/"+shipinName+".avi";
                    final MediaMetadataRetriever mmr2 = new MediaMetadataRetriever();
                    mmr2.setDataSource(aviPath);
                    String durationString2 = mmr2.extractMetadata(MediaMetadataRetriever.METADATA_KEY_DURATION);

                    shipin1.setText("当前视频:"+shipinName+" ,时长: "+durationString2+"ms");
                    mmr2.release();
                } catch (Exception e) {
                    e.printStackTrace();
                    Toast.makeText(getApplicationContext(),"视频"+shipinName+"不存在,若确定存在,请检查是否开启app的文件访问权限", Toast.LENGTH_LONG).show();
                    return;
                }






                Toast.makeText(getApplicationContext(), "选择的视频为:"+shipinName,
                        Toast.LENGTH_SHORT).show();

            }
        });




//        View.OnClickListener onClickListener = new View.OnClickListener() {
//            @Override
//            public void onClick(View view) {
//                String id = view.getTag().toString();
//                shipinName = ("newestVideoName"+id);
//
//
//
//                String aviPath12 = null;
//
//                try {
//                    aviPath12 = "/storage/emulated/0/Movies/三电极ECL/"+shipinName+".avi";
//                    final MediaMetadataRetriever mmr21 = new MediaMetadataRetriever();
//                    mmr21.setDataSource(aviPath12);
//                    String durationString21 = mmr21.extractMetadata(MediaMetadataRetriever.METADATA_KEY_DURATION);
//
//                    shipin1.setText("当前视频:"+shipinName+" ,时长: "+durationString21+"ms");
//                    mmr21.release();
//                } catch (Exception e) {
//                    e.printStackTrace();
//                    Toast.makeText(getApplicationContext(),"视频"+shipinName+"不存在,若确定存在,请检查是否开启app的文件访问权限", Toast.LENGTH_LONG).show();
//                    return;
//                }
//
//
//
//
//            }
//        };

//        btnsp1.setOnClickListener(onClickListener);
//        btnsp2.setOnClickListener(onClickListener);
//        btnsp3.setOnClickListener(onClickListener);
//        btnsp4.setOnClickListener(onClickListener);
//        btnsp5.setOnClickListener(onClickListener);


        btnsp1.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                String aviPath12 = null;

                try {
                    aviPath12 = "/storage/emulated/0/Movies/双电极ECL即时检测/"+newestVideoName+".avi";
                    final MediaMetadataRetriever mmr21 = new MediaMetadataRetriever();
                    mmr21.setDataSource(aviPath12);
                    String durationString21 = mmr21.extractMetadata(MediaMetadataRetriever.METADATA_KEY_DURATION);

                    shipin1.setText("当前视频:"+newestVideoName+" ,时长: "+durationString21+"ms");
                    mmr21.release();
                } catch (Exception e) {
                    e.printStackTrace();
                    Toast.makeText(getApplicationContext(),"视频"+newestVideoName+"不存在,若确定存在,请检查是否开启app的文件访问权限", Toast.LENGTH_LONG).show();
                    return;
                }
            }
        });

        btnsp2.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                String aviPath12 = null;
                shipinName = newestVideoName1;
                try {
                    aviPath12 = "/storage/emulated/0/Movies/双电极ECL即时检测/"+newestVideoName1+".avi";
                    final MediaMetadataRetriever mmr21 = new MediaMetadataRetriever();
                    mmr21.setDataSource(aviPath12);
                    String durationString21 = mmr21.extractMetadata(MediaMetadataRetriever.METADATA_KEY_DURATION);

                    shipin1.setText("当前视频:"+newestVideoName1+" ,时长: "+durationString21+"ms");
                    mmr21.release();
                } catch (Exception e) {
                    e.printStackTrace();
                    Toast.makeText(getApplicationContext(),"视频"+newestVideoName1+"不存在,若确定存在,请检查是否开启app的文件访问权限", Toast.LENGTH_LONG).show();
                    return;
                }
            }
        });
        btnsp3.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                String aviPath12 = null;
                shipinName = newestVideoName2;
                try {
                    aviPath12 = "/storage/emulated/0/Movies/双电极ECL即时检测/"+newestVideoName2+".avi";
                    final MediaMetadataRetriever mmr21 = new MediaMetadataRetriever();
                    mmr21.setDataSource(aviPath12);
                    String durationString21 = mmr21.extractMetadata(MediaMetadataRetriever.METADATA_KEY_DURATION);

                    shipin1.setText("当前视频:"+newestVideoName2+" ,时长: "+durationString21+"ms");
                    mmr21.release();
                } catch (Exception e) {
                    e.printStackTrace();
                    Toast.makeText(getApplicationContext(),"视频"+newestVideoName2+"不存在,若确定存在,请检查是否开启app的文件访问权限", Toast.LENGTH_LONG).show();
                    return;
                }
            }
        });
        btnsp4.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                String aviPath12 = null;
                shipinName = newestVideoName3;
                try {
                    aviPath12 = "/storage/emulated/0/Movies/双电极ECL即时检测/"+newestVideoName3+".avi";
                    final MediaMetadataRetriever mmr21 = new MediaMetadataRetriever();
                    mmr21.setDataSource(aviPath12);
                    String durationString21 = mmr21.extractMetadata(MediaMetadataRetriever.METADATA_KEY_DURATION);

                    shipin1.setText("当前视频:"+newestVideoName3+" ,时长: "+durationString21+"ms");
                    mmr21.release();
                } catch (Exception e) {
                    e.printStackTrace();
                    Toast.makeText(getApplicationContext(),"视频"+newestVideoName3+"不存在,若确定存在,请检查是否开启app的文件访问权限", Toast.LENGTH_LONG).show();
                    return;
                }
            }
        });
        btnsp5.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                String aviPath12 = null;
                shipinName = newestVideoName4;
                try {
                    aviPath12 = "/storage/emulated/0/Movies/双电极ECL即时检测/"+newestVideoName4+".avi";
                    final MediaMetadataRetriever mmr21 = new MediaMetadataRetriever();
                    mmr21.setDataSource(aviPath12);
                    String durationString21 = mmr21.extractMetadata(MediaMetadataRetriever.METADATA_KEY_DURATION);

                    shipin1.setText("当前视频:"+newestVideoName4+" ,时长: "+durationString21+"ms");
                    mmr21.release();
                } catch (Exception e) {
                    e.printStackTrace();
                    Toast.makeText(getApplicationContext(),"视频"+newestVideoName4+"不存在,若确定存在,请检查是否开启app的文件访问权限", Toast.LENGTH_LONG).show();
                    return;
                }
            }
        });







        backBtn.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                Intent intent=new Intent(GrayValueActivity.this,EntryActivity.class);
                startActivity(intent);
            }
        });

        jisuan.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                jisuanecl();
            }
        });

        btnl.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                jisuanecll();
            }
        });

        btnr.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
               jisuaneclr();
            }
        });
        btnrr.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                jisuaneclrr();
            }
        });

    }



    public void xianshi(String imagePath){


        // 首先获取图片路径
        String image1Path = imagePath;

// 根据路径查询图片
        String[] projection = { MediaStore.Images.Media.DATA };
        String selection = MediaStore.Images.Media.DATA + "=?";
        String[] selectionArgs = { image1Path };
        Cursor cursor = getContentResolver().query(
                MediaStore.Images.Media.EXTERNAL_CONTENT_URI,
                projection,
                selection,
                selectionArgs,
                null
        );

// 获取图片路径列的索引
        int column_index_data = cursor.getColumnIndexOrThrow(MediaStore.Images.Media.DATA);


        int x = 455;
        int y = 220;
        int width = 500;
        int height = 500;



// 如果找到了图片，读取图片并显示到ImageView
        if (cursor.moveToFirst()) {
            String path = cursor.getString(column_index_data);
            Bitmap bitmap = BitmapFactory.decodeFile(path);
            // 裁剪图片
            Bitmap croppedBitmap = Bitmap.createBitmap(bitmap, x, y, width, height);
            File croppedImageFile = new File("/storage/emulated/0/Pictures/Gallery/owner/HSJ/zkq"+System.currentTimeMillis()+".jpg");
            try (FileOutputStream fos = new FileOutputStream(croppedImageFile)) {
                croppedBitmap.compress(Bitmap.CompressFormat.JPEG, 100, fos);
            } catch (IOException e) {
                e.printStackTrace();
            }

// 插入到图库中

// 发送广播，通知图库更新
            sendBroadcast(new Intent(Intent.ACTION_MEDIA_SCANNER_SCAN_FILE, Uri.fromFile(croppedImageFile)));



            ImageView imageView = findViewById(R.id.imageView);
            imageView.setImageBitmap(croppedBitmap);
        }

// 记得关闭Cursor
        cursor.close();
        DELETE();






    }










    public void xianshi2(String imagePath){


        // 首先获取图片路径
        String image1Path = imagePath;

// 根据路径查询图片
        String[] projection = { MediaStore.Images.Media.DATA };
        String selection = MediaStore.Images.Media.DATA + "=?";
        String[] selectionArgs = { image1Path };
        Cursor cursor = getContentResolver().query(
                MediaStore.Images.Media.EXTERNAL_CONTENT_URI,
                projection,
                selection,
                selectionArgs,
                null
        );

// 获取图片路径列的索引
        int column_index_data = cursor.getColumnIndexOrThrow(MediaStore.Images.Media.DATA);


        int x = 940;
        int y = 220;
        int width = 500;
        int height = 500;



// 如果找到了图片，读取图片并显示到ImageView
        if (cursor.moveToFirst()) {
            String path = cursor.getString(column_index_data);
            Bitmap bitmap = BitmapFactory.decodeFile(path);
            // 裁剪图片
            Bitmap croppedBitmap = Bitmap.createBitmap(bitmap, x, y, width, height);


            File croppedImageFile = new File("/storage/emulated/0/Pictures/Gallery/owner/LBY/jcq"+System.currentTimeMillis()+".jpg");
            try (FileOutputStream fos = new FileOutputStream(croppedImageFile)) {
                croppedBitmap.compress(Bitmap.CompressFormat.JPEG, 100, fos);
            } catch (IOException e) {
                e.printStackTrace();
            }

// 插入到图库中

// 发送广播，通知图库更新
            sendBroadcast(new Intent(Intent.ACTION_MEDIA_SCANNER_SCAN_FILE, Uri.fromFile(croppedImageFile)));
            ImageView imageView = findViewById(R.id.imageView);
            imageView.setImageBitmap(croppedBitmap);
        }

// 记得关闭Cursor
        cursor.close();
        DELETE();

    }






    public void xianshi3(String imagePath){


        // 首先获取图片路径
        String image1Path = imagePath;

// 根据路径查询图片
        String[] projection = { MediaStore.Images.Media.DATA };
        String selection = MediaStore.Images.Media.DATA + "=?";
        String[] selectionArgs = { image1Path };
        Cursor cursor = getContentResolver().query(
                MediaStore.Images.Media.EXTERNAL_CONTENT_URI,
                projection,
                selection,
                selectionArgs,
                null
        );

// 获取图片路径列的索引
        int column_index_data = cursor.getColumnIndexOrThrow(MediaStore.Images.Media.DATA);


        int x = 780;
        int y = 300;
        int width = 450;
        int height = 450;



// 如果找到了图片，读取图片并显示到ImageView
        if (cursor.moveToFirst()) {
            String path = cursor.getString(column_index_data);
            Bitmap bitmap = BitmapFactory.decodeFile(path);
            // 裁剪图片
            Bitmap croppedBitmap = Bitmap.createBitmap(bitmap, x, y, width, height);
            File croppedImageFile = new File("/storage/emulated/0/Pictures/Gallery/owner/LBY/jcq"+System.currentTimeMillis()+".jpg");
            try (FileOutputStream fos = new FileOutputStream(croppedImageFile)) {
                croppedBitmap.compress(Bitmap.CompressFormat.JPEG, 100, fos);
            } catch (IOException e) {
                e.printStackTrace();
            }

// 插入到图库中

// 发送广播，通知图库更新
            sendBroadcast(new Intent(Intent.ACTION_MEDIA_SCANNER_SCAN_FILE, Uri.fromFile(croppedImageFile)));



            ImageView imageView = findViewById(R.id.imageView);
            imageView.setImageBitmap(croppedBitmap);
        }

// 记得关闭Cursor
        cursor.close();
        DELETE();






    }




    public void xianshi4(String imagePath){


        // 首先获取图片路径
        String image1Path = imagePath;

// 根据路径查询图片
        String[] projection = { MediaStore.Images.Media.DATA };
        String selection = MediaStore.Images.Media.DATA + "=?";
        String[] selectionArgs = { image1Path };
        Cursor cursor = getContentResolver().query(
                MediaStore.Images.Media.EXTERNAL_CONTENT_URI,
                projection,
                selection,
                selectionArgs,
                null
        );

// 获取图片路径列的索引
        int column_index_data = cursor.getColumnIndexOrThrow(MediaStore.Images.Media.DATA);


        int x = 1230;
        int y = 300;
        int width = 500;
        int height = 500;



// 如果找到了图片，读取图片并显示到ImageView
        if (cursor.moveToFirst()) {
            String path = cursor.getString(column_index_data);
            Bitmap bitmap = BitmapFactory.decodeFile(path);
            // 裁剪图片
            Bitmap croppedBitmap = Bitmap.createBitmap(bitmap, x, y, width, height);
            File croppedImageFile = new File("/storage/emulated/0/Pictures/Gallery/owner/LBY/jcq"+System.currentTimeMillis()+".jpg");
            try (FileOutputStream fos = new FileOutputStream(croppedImageFile)) {
                croppedBitmap.compress(Bitmap.CompressFormat.JPEG, 100, fos);
            } catch (IOException e) {
                e.printStackTrace();
            }

// 插入到图库中

// 发送广播，通知图库更新
            sendBroadcast(new Intent(Intent.ACTION_MEDIA_SCANNER_SCAN_FILE, Uri.fromFile(croppedImageFile)));



            ImageView imageView = findViewById(R.id.imageView);
            imageView.setImageBitmap(croppedBitmap);
        }

// 记得关闭Cursor
        cursor.close();
        DELETE();






    }



    private void jisuanecl() {
        String mp4Path = null;
        try {
            mp4Path = "/storage/emulated/0/Movies/双电极ECL即时检测/"+shipinName+".avi";
            // mp4Path = "storage/emulated/0/USBCamera/123.mp4";
        } catch (Exception e) {

            e.printStackTrace();
        }
        final MediaMetadataRetriever mmr = new MediaMetadataRetriever();


        try {
            mmr.setDataSource(mp4Path);
            String durationString = mmr.extractMetadata(MediaMetadataRetriever.METADATA_KEY_DURATION);
            long duration = Long.parseLong(durationString);
        } catch (Exception e) {
            e.printStackTrace();
            Toast.makeText(this,"选择的视频不存在，如确定存在，请检查是否开启app的文件访问权限", Toast.LENGTH_LONG).show();
            return;
        }

        //检查有没有存储权限
        if (!Environment.getExternalStorageState().equals(Environment.MEDIA_MOUNTED)) {
            Toast.makeText(this, "请至权限中心打开应用权限", Toast.LENGTH_SHORT).show();
        } else {
            AtomicReference<String> maxNameRef = new AtomicReference<>();
            AtomicReference<Double> maxRef = new AtomicReference<>(0.0);
            AtomicReference<Integer> currRef = new AtomicReference<>(0);
            ExecutorService executorService = Executors.newFixedThreadPool(10);
            List<Future<Bitmap>> futures = new ArrayList<>();



            String durationString111 = mmr.extractMetadata(MediaMetadataRetriever.METADATA_KEY_DURATION);


            List<Bitmap> bitmaps = IntStream.range(0, Integer.parseInt(durationString111)/200)
                    .parallel()
                    .mapToObj(i -> (Supplier<Bitmap>) () -> {
                        Bitmap frameBitmap1 = mmr.getFrameAtTime((i * 200000), FFmpegMediaMetadataRetriever.OPTION_CLOSEST);
                        File appDir = new File(getApplicationContext().getExternalFilesDir(null).getPath() + shipinName);
                        if (!appDir.exists()) {
                            appDir.mkdir();
                        }
                        String fileName = String.valueOf(i+1)+shipinName;
                        File file = new File(appDir, fileName);

                        int[] pixels = new int[500*500];
                        // frameBitmap1.getPixels(pixels, 0, 300, 960, 260, 300, 400);
//                        frameBitmap1.getPixels(pixels, 0, 500, 990, 220, 500, 500);
                        frameBitmap1.getPixels(pixels, 0, 500, 455, 220, 500,500);
//                        frameBitmap1.getPixels(pixels, 0, 450, 1230, 300, 450, 450);
                        double g = 0;
                        double rr = 0;
                        double bb = 0;
                        double gg = 0;

                        for (int iii = 0; iii < pixels.length; iii++) {
                            int color = pixels[iii];
                            double r1 = Color.red(color);
                            double g1 = Color.green(color);
                            double b1 = Color.blue(color);

                            rr += r1;
                            bb += b1;
                            gg += g1;

                        }

                        g = (rr*299+gg*587+bb*114)/1000;
                        if (g > maxRef.get()) {
                            maxRef.set(g);
                            maxNameRef.set(fileName);
                            currRef.set(i);
                        }
                        try {
                            FileOutputStream fos = new FileOutputStream(file);
                            frameBitmap1.compress(Bitmap.CompressFormat.JPEG, 100, fos);
                            fos.flush();
                            fos.close();
                        } catch (FileNotFoundException e) {
                            e.printStackTrace();
                        } catch (IOException e) {
                            e.printStackTrace();
                        }

                        // 将照片插入到相册中
                        try {
                            MediaStore.Images.Media.insertImage(getApplicationContext().getContentResolver(), file.getAbsolutePath(), fileName, null);
                        } catch (FileNotFoundException e) {
                            e.printStackTrace();
                        }

                        return frameBitmap1;
                    })
                    .map(Supplier::get)
                    .collect(Collectors.toList());



//// 遍历futures列表，等待所有任务完成并将结果添加到Bitmap对象列表中
//            List<Bitmap> bitmaps1 = new ArrayList<>();
//            for (Future<Bitmap> future : futures) {
//                try {
//                    Bitmap bitmap = future.get();
//                    bitmaps1.add(bitmap);
//                } catch (InterruptedException | ExecutionException e) {
//                    e.printStackTrace();
//                }
//            }

// 关闭线程池
            executorService.shutdown();
            sss1 = "/storage/emulated/0/Pictures/"+maxNameRef.get()+".jpg";
//            wjjPath =
            int sd2 = Integer.valueOf(currRef.get())+1;
            double yy = ((maxRef.get()/1000000)-5.284)/2.09;
            double answer =  Math.pow(10, yy);
            DecimalFormat df = new DecimalFormat("#.###");
            String result = df.format(answer);
            textView.setText(""+maxRef.get());
            textView2.setText("NDV浓度:    "+result);
            zuida=sd2;
            xianshi(sss1);
        }



        mmr.release();


    }


















    private void jisuanecll() {
        String mp4Path = null;
        try {
            mp4Path = "/storage/emulated/0/Movies/双电极ECL即时检测/"+shipinName+".avi";
            // mp4Path = "storage/emulated/0/USBCamera/123.mp4";
        } catch (Exception e) {

            e.printStackTrace();
        }
        final MediaMetadataRetriever mmr = new MediaMetadataRetriever();


        try {
            mmr.setDataSource(mp4Path);
            String durationString = mmr.extractMetadata(MediaMetadataRetriever.METADATA_KEY_DURATION);
            long duration = Long.parseLong(durationString);
        } catch (Exception e) {
            e.printStackTrace();
            Toast.makeText(this,"选择的视频不存在，如确定存在，请检查是否开启app的文件访问权限", Toast.LENGTH_LONG).show();
            return;
        }

        //检查有没有存储权限
        if (!Environment.getExternalStorageState().equals(Environment.MEDIA_MOUNTED)) {
            Toast.makeText(this, "请至权限中心打开应用权限", Toast.LENGTH_SHORT).show();
        } else {
            AtomicReference<String> maxNameRef = new AtomicReference<>();
            AtomicReference<Double> maxRef = new AtomicReference<>(0.0);
            AtomicReference<Integer> currRef = new AtomicReference<>(0);
            ExecutorService executorService = Executors.newFixedThreadPool(10);
            List<Future<Bitmap>> futures = new ArrayList<>();



            String durationString111 = mmr.extractMetadata(MediaMetadataRetriever.METADATA_KEY_DURATION);


            List<Bitmap> bitmaps = IntStream.range(0, Integer.parseInt(durationString111)/200)
                    .parallel()
                    .mapToObj(i -> (Supplier<Bitmap>) () -> {
                        Bitmap frameBitmap1 = mmr.getFrameAtTime((i * 200000), FFmpegMediaMetadataRetriever.OPTION_CLOSEST);
                        File appDir = new File(getApplicationContext().getExternalFilesDir(null).getPath() + shipinName);
                        if (!appDir.exists()) {
                            appDir.mkdir();
                        }
                        String fileName = String.valueOf(i+1)+shipinName;
                        File file = new File(appDir, fileName);

                        int[] pixels = new int[500*500];
                        // frameBitmap1.getPixels(pixels, 0, 300, 960, 260, 300, 400);
//                        frameBitmap1.getPixels(pixels, 0, 500, 490, 280, 500, 500);
                        frameBitmap1.getPixels(pixels, 0, 500, 940, 220, 500, 500);
//                        frameBitmap1.getPixels(pixels, 0, 450, 1230, 300, 450, 450);
                        double g = 0;
                        double rr = 0;
                        double bb = 0;
                        double gg = 0;






                        for (int iii = 0; iii < pixels.length; iii++) {
                            int color = pixels[iii];
                            double r1 = Color.red(color);
                            double g1 = Color.green(color);
                            double b1 = Color.blue(color);

                            rr += r1;
                            bb += b1;
                            gg += g1;

                        }

                        g = (rr*299+gg*587+bb*114)/1000;
                        if (g > maxRef.get()) {
                            maxRef.set(g);
                            maxNameRef.set(fileName);
                            currRef.set(i);
                        }
                        try {
                            FileOutputStream fos = new FileOutputStream(file);
                            frameBitmap1.compress(Bitmap.CompressFormat.JPEG, 100, fos);
                            fos.flush();
                            fos.close();
                        } catch (FileNotFoundException e) {
                            e.printStackTrace();
                        } catch (IOException e) {
                            e.printStackTrace();
                        }

                        // 将照片插入到相册中
                        try {
                            MediaStore.Images.Media.insertImage(getApplicationContext().getContentResolver(), file.getAbsolutePath(), fileName, null);
                        } catch (FileNotFoundException e) {
                            e.printStackTrace();
                        }

                        return frameBitmap1;
                    })
                    .map(Supplier::get)
                    .collect(Collectors.toList());



//// 遍历futures列表，等待所有任务完成并将结果添加到Bitmap对象列表中
//            List<Bitmap> bitmaps1 = new ArrayList<>();
//            for (Future<Bitmap> future : futures) {
//                try {
//                    Bitmap bitmap = future.get();
//                    bitmaps1.add(bitmap);
//                } catch (InterruptedException | ExecutionException e) {
//                    e.printStackTrace();
//                }
//            }

// 关闭线程池
            executorService.shutdown();
            sss1 = "/storage/emulated/0/Pictures/"+maxNameRef.get()+".jpg";
//            wjjPath =
            int sd2 = Integer.valueOf(currRef.get())+1;
            double yy = ((maxRef.get()/1000000)-5.284)/2.09;
            double answer =  Math.pow(10, yy);
            DecimalFormat df = new DecimalFormat("#.###");
            String result = df.format(answer);
            textView.setText(""+maxRef.get());
            zuida=sd2;
            textView2.setText("NDV浓度:    "+result);
            xianshi2(sss1);
        }



        mmr.release();
    }



    private void jisuaneclr() {

    }












    private void DELETE() {

        File folder = new File("/storage/emulated/0/Pictures/");

        File[] files = folder.listFiles();


        List<File> matchedFiles = new ArrayList<>();
        for (File file : files) {
            if (file.getName().endsWith(shipinName+".jpg")) {
                matchedFiles.add(file);
            }
        }

        if (matchedFiles != null) {
            for (File file : matchedFiles) {
//                if (file.isFile() &&(file.getName().equals(String.valueOf(zuida)+shipinName+".jpg"))) {
//                   continue;
//                }else{
//                    file.delete();
//                }
                file.delete();
            }
        }


    }


    private void jisuaneclrr() {

    }



}
