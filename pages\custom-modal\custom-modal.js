Component({
    properties: {
      isVisible: {
        type: Boolean,
        value: false
      },
      title: {
        type: String,
        value: '提示'
      },
      content: {
        type: String,
        value: ''
      },
      cancelText: {
        type: String,
        value: '否'
      },
      confirmText: {
        type: String,
        value: '是'
      }
    },
  
    methods: {
      handleCancel() {
        this.triggerEvent('cancel');
      },
  
      handleConfirm() {
        this.triggerEvent('confirm');
      }
    }
  });