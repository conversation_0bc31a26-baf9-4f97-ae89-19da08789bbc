/* 个人页面样式 */
/* 自定义导航栏 - Skyline渲染引擎，支持动态适配 */
.custom-navbar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  /* height通过内联样式动态设置 */
  min-height: 88rpx; /* 最小高度保证兼容性 */
  background: #fff;
  display: flex;
  align-items: flex-end; /* 改为底部对齐，确保标题在导航栏底部 */
  justify-content: center;
  z-index: 9999;
  border-bottom: 1rpx solid #e5e5e5;
  transform: translate3d(0, 0, 0);
  will-change: transform;
  /* 添加安全区域适配 */
  box-sizing: border-box;
}

.navbar-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
  /* 确保标题在导航栏底部正确显示 */
  padding-bottom: 12rpx;
  line-height: 1.2;
}
/* 性能优化总结:
 * 1. 硬件加速相关属性:
 *    - will-change: transform, opacity
 *    - backface-visibility: hidden
 *    - perspective: 1000
 *    - transform: translateZ(0)
 *    - transform-style: preserve-3d
 *
 * 2. 动画性能优化:
 *    - 使用transform和opacity进行动画而不是all
 *    - GPU加速相关属性
 *    - 优化贝塞尔曲线
 *    - 3D变换相关属性促使GPU渲染
 *
 * 3. 微信小程序不规范选择器修复:
 *    - 替换page标签选择器为.page-container类选择器
 *    - 替换:active伪类为对应的active类(如.button-active)
 *    - 移除::-webkit-scrollbar等伪元素选择器
 *    - 替换媒体查询为对应的类选择器(.small-screen, .medium-screen)
 */

/* 页面根元素容器类 */
.page-container {
  background-color: #f7f8fa;
  background: #f7f8fa;
  z-index: 999;
  display: flex;
  flex-direction: column;
}

/* 按钮样式 - 移除按钮伪类选择器 */
.action-btn-no-border {
  border: none;
}

/* 用户信息点击状态类 */
.userinfo-btn-active {
  transform: scale(0.98);
  background: rgba(255, 255, 255, 0.3);
}

/* 按钮点击状态类 */
.action-btn-pressed {
  transform: scale(0.98);
  opacity: 0.9;
}

/* 添加按钮点击效果类 */
.back-btn-active {
  transform: translateY(2rpx);
  box-shadow: 0 2rpx 6rpx rgba(90, 120, 213, 0.2);
  opacity: 0.9;
}

.button-hover-class {
  opacity: 0.8;
}

.container {
  background-color: #f7f8fa;
  padding-bottom: calc(110rpx + env(safe-area-inset-bottom));
  /* padding-top通过内联样式动态设置 */
}

/* 用户信息部分 */
.user-section {
  background: linear-gradient(135deg, #4a90e2, #7c4dff);
  padding: 40rpx 0 105rpx 0; /* 调整底部padding为105rpx，让蓝紫色背景向下延伸 */
  position: relative;
  overflow: hidden;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
}

.user-section::after {
  content: '';
  position: absolute;
  bottom: -60%;
  left: -10%;
  width: 120%;
  height: 200rpx;
  background: rgba(255, 255, 255, 0.1);
  transform: rotate(-5deg);
}

.userinfo {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 30rpx;
  position: relative;
  z-index: 1;
  transition: all 0.3s ease;
}

.avatar-wrapper {
  padding: 0;
  width: 160rpx;
  height: 160rpx;
  border-radius: 50%;
  margin-bottom: 10rpx;
  background-color: transparent;
}

.avatar-wrapper::after {
  border: none;
}

.userinfo-avatar {
  width: 160rpx;
  height: 160rpx;
  border-radius: 50%;
  background-size: cover;
  background-color: white;
}

.login-btn, .logout-btn {
  width: 240rpx;
  height: 80rpx;
  margin-top: 24rpx;
  font-size: 28rpx;
  padding: 0;
  line-height: 80rpx;
  text-align: center;
  color: #fff;
  border-radius: 40rpx;
  transition: all 0.3s ease;
}

.login-btn {
  background: rgba(255, 255, 255, 0.2);
  border: 2rpx solid rgba(255, 255, 255, 0.5);
}

.logout-btn {
  background: linear-gradient(90deg, #6ba8f7, #a585ff);
  border: 2rpx solid rgba(255, 255, 255, 0.8);
  box-shadow: 0 4rpx 12rpx rgba(107, 168, 247, 0.3);
}

.login-btn-active, .logout-btn-active {
  transform: scale(0.98);
  background: rgba(255, 255, 255, 0.3);
}

.userinfo-nickname {
  color: #fff;
  font-size: 34rpx;
  margin-top: 24rpx;
  font-weight: 500;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
  background: transparent;
  text-align: center;
  width: 100%;
}

.userinfo-nickname::placeholder {
  color: rgba(255, 255, 255, 0.8);
}

.space-line {
  height: 16rpx;
  background-color: transparent;
}

.menu-list {
  padding: 20rpx;
}

.weui-cells {
  border-radius: 16rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.05);
  overflow: hidden;
  margin: 0;
}

.weui-cell {
  padding: 32rpx;
  position: relative;
  display: flex;
  align-items: center;
  background: #fff;
  border-bottom: 1rpx solid rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
}

.weui-cell-active {
  background: #f9f9f9;
}

.weui-cell-last {
  border-bottom: none;
}

.weui-cell__hd {
  margin-right: 24rpx;
  width: 48rpx;
  height: 48rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.weui-cell__hd .cell-image {
  width: 40rpx;
  height: 40rpx;
  opacity: 0.9;
}

.weui-cell__bd {
  flex: 1;
  font-size: 30rpx;
  color: #333;
  font-weight: 500;
}

.weui-cell__ft {
  color: #bbb;
  font-size: 24rpx;
}

.weui-cell__ft_in-access::after {
  content: " ";
  display: inline-block;
  height: 12rpx;
  width: 12rpx;
  border-width: 2rpx 2rpx 0 0;
  border-color: #bbb;
  border-style: solid;
  transform: matrix(0.71, 0.71, -0.71, 0.71, 0, 0);
  margin-left: 16rpx;
}

.badge {
  position: absolute;
  top: 50%;
  right: 80rpx;
  transform: translateY(-50%);
  background: #ff4d4f;
  color: #fff;
  font-size: 22rpx;
  width: 32rpx;
  height: 32rpx;
  line-height: 32rpx;
  text-align: center;
  border-radius: 50%;
  font-weight: bold;
  box-shadow: 0 2rpx 8rpx rgba(255, 77, 79, 0.3);
}

/* 数据显示模式 - 支持动态适配导航栏高度 */
.data-display {
  position: fixed;
  /* top和height通过内联样式动态设置 */
  left: 0;
  width: 100%;
  background: linear-gradient(160deg, #f3f1fc 0%, #eceafc 50%, #e8e5fb 100%);
  z-index: 999;
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
  will-change: transform, opacity; /* 提高动画性能 */
}

/* 添加背景遮罩层 - 用于模式切换时的深度感知 */
.data-display::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0);
  z-index: 0;
  pointer-events: none;
  transition: background-color 0.15s cubic-bezier(0.05, 0.7, 0.1, 1);
  will-change: background-color;
}

/* 多选模式下的背景遮罩层 - 40%透明度 */
.data-display.isMultiSelectMode::before {
  background-color: rgba(0, 0, 0, 0.4);
  z-index: 0;
}

/* 返回按钮容器 - 单选模式专用，确保位于底部且可见 */
.bottom-buttons:not(.multi-select-mode) {
  position: fixed;
  bottom: calc(110rpx + env(safe-area-inset-bottom)); /* 直接定位在底部导航栏上方 */
  left: 0;
  right: 0;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 30rpx 0;
  z-index: 1500;
  background: rgba(246, 244, 255, 0.85); /* 半透明浅紫色背景，与整体风格协调 */
  backdrop-filter: blur(10rpx); /* 添加模糊效果增强视觉融合 */
  width: 100%;
  border-radius: 0;
  box-shadow: 0 -2rpx 10rpx rgba(90, 120, 213, 0.08); /* 淡化阴影 */
  border-top: 1rpx solid rgba(180, 170, 255, 0.15); /* 淡色边框 */
  border-bottom: none;
  transition: all 0.3s ease;
}

/* 返回按钮样式 */
.back-btn {
  width: 90%;
  height: 88rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  color: #fff;
  font-size: 32rpx;
  font-weight: 500;
  letter-spacing: 4rpx;
  background: linear-gradient(90deg, #78b9ff 0%, #c58eff 100%);
  border-radius: 44rpx;
  box-shadow: 0 4rpx 12rpx rgba(90, 120, 213, 0.3);
  z-index: 1501;
  position: relative;
  overflow: hidden;
  transition: all 0.3s ease;
}

/* 添加按钮内部高光效果 */
.back-btn::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(180deg, rgba(255, 255, 255, 0.3), rgba(255, 255, 255, 0));
  z-index: 1;
  border-radius: 40rpx;
}

/* 添加按钮点击效果 */
.back-btn:active {
  transform: translateY(2rpx);
  box-shadow: 0 2rpx 6rpx rgba(90, 120, 213, 0.2);
  opacity: 0.9;
}

.data-header {
  padding: 30rpx 30rpx 25rpx 30rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  background: linear-gradient(145deg, rgba(246, 244, 255, 0.9), rgba(241, 239, 252, 0.9));
  box-shadow: 0 2rpx 8rpx rgba(90, 120, 213, 0.1);
  flex-shrink: 0;
  border-radius: 12rpx;
  border: 1px solid rgba(180, 170, 255, 0.2);
  transition: all 0.4s cubic-bezier(0.05, 0.7, 0.1, 1);
  transform: translateY(0);
  opacity: 1;
  will-change: transform, opacity, height;
}

/* 模式过渡状态下的数据头部 */
.mode-transition .data-header {
  opacity: 0;
  transform: translateY(-40rpx);
  transition: all 0.6s cubic-bezier(0.22, 0.61, 0.36, 1);
}

/* 标题动画 */
.header-title {
  font-size: 40rpx;
  font-weight: 600;
  color: #303133;
  line-height: 1.3;
  flex-shrink: 0;
  position: static;
  letter-spacing: normal;
  text-shadow: none;
  padding-left: 0;
  border-left: none;
  display: flex;
  align-items: center;
  width: auto;
  transition: all 0.5s cubic-bezier(0.22, 0.61, 0.36, 1);
  animation: title-fade-in 0.5s cubic-bezier(0.05, 0.7, 0.1, 1) forwards;
}

@keyframes title-fade-in {
  0% {
    opacity: 0.7;
    transform: translateY(-10rpx);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 多选模式下的标题样式 */
.isMultiSelectMode .header-title {
  color: #4a90e2;
  transform: scale(1.05);
  transition: all 0.5s cubic-bezier(0.22, 0.61, 0.36, 1);
}

/* 非多选模式：搜索框容器 */
.search-container {
  display: flex;
  align-items: center;
  margin-top: 20rpx;
  background: rgba(255, 255, 255, 0.6);
  border-radius: 32rpx;
  padding: 8rpx 24rpx;
  width: 95%;
  border: 1rpx solid rgba(180, 170, 255, 0.2);
  box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.05);
  transition: all 0.6s cubic-bezier(0.22, 0.61, 0.36, 1);
  transform-origin: center;
  will-change: transform, opacity;
  
  /* 搜索框动画初始状态 */
  opacity: 0;
  transform: translateY(-20rpx) scale(0.95);
  animation: search-slide-in 0.6s cubic-bezier(0.05, 0.7, 0.1, 1) forwards;
  animation-delay: 0.15s; /* 稍微延迟以便在标题转换后开始 */
}

/* 搜索框出现的动画 */
@keyframes search-slide-in {
  0% {
    opacity: 0;
    transform: translateY(-20rpx) scale(0.95);
  }
  100% {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

/* 模式过渡中的搜索框容器 */
.mode-transition .search-container {
  opacity: 0;
  transform: translateY(15rpx) scale(0.95);
  transition: all 0.6s cubic-bezier(0.22, 0.61, 0.36, 1);
  animation: none; /* 过渡期间禁用动画 */
}

/* 搜索输入框 */
.search-input {
  flex: 1;
  height: 64rpx;
  font-size: 28rpx;
  color: #333;
  padding: 0 10rpx;
  transition: all 0.25s cubic-bezier(0.05, 0.7, 0.1, 1);
}

/* 搜索输入框聚焦效果 */
.search-input-focused {
  background: rgba(255, 255, 255, 0.85);
  box-shadow: 0 2rpx 8rpx rgba(90, 120, 213, 0.15);
}

/* 多选模式：图表按钮容器 */
.cancel-btn-container {
  margin-top: 20rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  transition: all 0.4s cubic-bezier(0.05, 0.7, 0.1, 1);
  transform: translateY(0);
  opacity: 1; 
  will-change: transform, opacity;
  animation: fade-slide-down 0.45s cubic-bezier(0.05, 0.7, 0.1, 1);
}

@keyframes fade-slide-down {
  from {
    opacity: 0;
    transform: translateY(-20rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 图表生成按钮 */
.generate-btn {
  background: linear-gradient(90deg, #4A90E2, #5B86E5);
  color: #fff;
  font-size: 28rpx;
  padding: 12rpx 36rpx;
  border-radius: 32rpx;
  box-shadow: 0 4rpx 12rpx rgba(90, 120, 213, 0.3);
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  will-change: transform, box-shadow;
}

.generate-btn:active {
  transform: scale(0.97);
  box-shadow: 0 2rpx 6rpx rgba(90, 120, 213, 0.2);
}

/* 模式过渡中的底部按钮 */
.mode-transition ~ .bottom-buttons {
  transform: translateY(100%);
  opacity: 0;
  transition: all 0.7s cubic-bezier(0.22, 0.61, 0.36, 1);
}

/* 底部按钮容器 - 多选模式 */
.bottom-buttons.multi-select-mode {
  position: fixed;
  bottom: calc(110rpx + env(safe-area-inset-bottom));
  left: 0;
  right: 0;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 30rpx;
  background: rgba(246, 244, 255, 0.95);
  backdrop-filter: blur(10rpx);
  box-shadow: 0 -2rpx 10rpx rgba(90, 120, 213, 0.1);
  border-top: 1rpx solid rgba(180, 170, 255, 0.15);
  z-index: 1500;
}

/* 底部按钮的动画效果 */
.bottom-buttons.multi-select-mode {
  transform: translateY(0);
  opacity: 1;
  transition: transform 0.7s cubic-bezier(0.22, 0.61, 0.36, 1),
              opacity 0.7s cubic-bezier(0.22, 0.61, 0.36, 1);
  will-change: transform, opacity;
}

/* 模式过渡中的底部按钮 - 消失动画 */
.mode-transition ~ .bottom-buttons.multi-select-mode {
  transform: translateY(100%);
  opacity: 0;
  transition: transform 0.7s cubic-bezier(0.22, 0.61, 0.36, 1),
              opacity 0.7s cubic-bezier(0.22, 0.61, 0.36, 1);
  z-index: 1500;
}

/* 单选模式下的按钮组 */
.single-mode-buttons {
  display: flex;
  width: 100%;
  justify-content: space-between;
  gap: 20rpx;
}

/* 返回按钮样式优化 */
.back-btn {
  width: 70%;
  height: 88rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  color: #fff;
  font-size: 32rpx;
  font-weight: 500;
  letter-spacing: 4rpx;
  background: linear-gradient(90deg, #78b9ff 0%, #c58eff 100%);
  border-radius: 44rpx;
  box-shadow: 0 4rpx 12rpx rgba(90, 120, 213, 0.3);
  position: relative;
  overflow: hidden;
  transition: all 0.3s ease;
}

/* 添加选择按钮 */
.select-btn {
  width: 30%;
  height: 88rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  color: #4A90E2;
  font-size: 32rpx;
  font-weight: 500;
  background: rgba(255, 255, 255, 0.8);
  border: 1rpx solid rgba(74, 144, 226, 0.4);
  border-radius: 44rpx;
  box-shadow: 0 4rpx 12rpx rgba(90, 120, 213, 0.15);
  transition: all 0.3s ease;
}

.select-btn:active {
  transform: translateY(2rpx);
  box-shadow: 0 2rpx 6rpx rgba(90, 120, 213, 0.1);
  opacity: 0.9;
}

/* 多选模式按钮组 */
.btn-group {
  display: flex;
  width: 100%;
  justify-content: space-between;
}

/* 等宽按钮 */
.equal-width-btn {
  flex: 1;
  max-width: 31%;
  height: 80rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 28rpx;
  font-weight: 500;
  border-radius: 40rpx;
  transition: all 0.3s ease;
}

/* 删除按钮 */
.delete-btn {
  background: linear-gradient(90deg, #FF5252, #FF1744);
  color: #fff;
  box-shadow: 0 4rpx 12rpx rgba(255, 23, 68, 0.3);
}

/* 导出按钮 */
.export-btn {
  background: linear-gradient(90deg, #26C6DA, #00ACC1);
  color: #fff;
  box-shadow: 0 4rpx 12rpx rgba(0, 172, 193, 0.3);
}

/* 取消按钮 */
.cancel-btn {
  background: linear-gradient(90deg, #BDBDBD, #9E9E9E);
  color: #fff;
  box-shadow: 0 4rpx 12rpx rgba(189, 189, 189, 0.3);
}

/* 按钮悬停/点击效果 */
/* 已禁用按钮悬停效果，避免颜色滞留问题 */
/*
.button-hover {
  transform: translateY(2rpx) scale(0.98);
  box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.15);
  opacity: 0.9;
}
*/

/* 底部空间占位符 - 解决滚动视图底部遮挡问题 */
.bottom-space-holder {
  height: 0;
  transition: height 0.4s cubic-bezier(0.05, 0.7, 0.1, 1);
}

.bottom-space-holder.active {
  height: 180rpx;
}

/* 数据列表过渡动画优化 */
.data-list {
  flex: 1;
  padding: 0 20rpx;
  box-sizing: border-box;
  transition: opacity 0.7s cubic-bezier(0.22, 0.61, 0.36, 1);
}

.data-list.mode-transition {
  opacity: 0.5;
  transform: scale(0.98);
  transition: all 0.4s cubic-bezier(0.05, 0.7, 0.1, 1);
}

.data-main {
  width: 100%;
  position: relative;
  padding-right: 16rpx;
}

/* TC值样式优化 */
.tc-value {
  font-size: 30rpx;
  font-weight: 500;
  color: #333;
  max-width: 60%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* 标签容器样式 - 多选模式（保持绝对定位） */
.analysis-type-container-multi {
  margin-left: 10rpx;
  align-self: center;
  position: absolute;
  right: 90rpx;
  top: 0;
  cursor: pointer;
  width: 80rpx;
  height: 40rpx;
  perspective: 800rpx;
  z-index: 5;
}

/* 标签容器样式 - 单选模式（参考多选模式，使用绝对定位） */
.analysis-type-container-single {
  margin-left: 10rpx;
  align-self: center;
  position: absolute;
  right: 90rpx;
  top: 0;
  cursor: pointer;
  width: 80rpx;
  height: 40rpx;
  perspective: 800rpx;
  z-index: 5;
}

/* 标签样式 */
.analysis-type {
  position: relative;
  width: 100%;
  height: 100%;
  transition: transform 0.6s;
  transform-style: preserve-3d;
  transform-origin: center center;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
  border-radius: 6rpx;
}

/* 翻转效果 */
.analysis-type.flipping {
  transform: rotateX(180deg);
  transition: transform 0.6s cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

/* 标签正面和背面共享样式 */
.type-front, .type-back {
  position: absolute;
  width: 100%;
  height: 100%;
  backface-visibility: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 22rpx;
  font-weight: normal;
  color: #fff;
  border-radius: 6rpx;
  box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.1);
}

/* 统一标签样式定义 - 覆盖所有之前的定义 */
.type-front {
  background: linear-gradient(90deg, #78b9ff 0%, #c58eff 100%) !important;
  transform: rotateX(0deg);
}

.type-back {
  background: linear-gradient(90deg, #FF9800 0%, #E53935 100%) !important;
  transform: rotateX(180deg);
}

/* 时间行样式调整 */
.time-row {
  font-size: 24rpx;
  color: #999;
  text-align: right;
  margin-top: 12rpx;
  width: 100%;
  display: block;
}

/* 详情区域背景 */
.data-details {
  background: linear-gradient(145deg, rgba(246, 244, 255, 0.9), rgba(241, 239, 252, 0.9));
  border-radius: 12rpx;
  padding: 16rpx;
  margin-top: 10rpx;
  border: 1px solid rgba(180, 170, 255, 0.2);
  box-shadow: 0 2rpx 8rpx rgba(90, 120, 213, 0.1);
  transform: translateY(0);
  transition: all 0.4s cubic-bezier(0.05, 0.7, 0.1, 1);
  will-change: transform, opacity;
}

.detail-row {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  padding: 10rpx 0;
  position: relative;
  width: 100%;
  flex-wrap: wrap;
  gap: 16rpx;
}

.detail-left {
  display: flex;
  flex-direction: row;
  flex: 1;
  overflow: hidden;
}

.detail-label {
  font-size: 28rpx;
  color: #666;
  margin-right: 8rpx;
}

.detail-value {
  font-size: 28rpx;
  color: #333;
}

/* 区域块样式调整 - 统一协调的背景色 */
.area-block {
  display: flex;
  flex-direction: row;
  padding: 6rpx 10rpx;
  background: transparent; /* 改为透明，以适应整体背景色 */
  border-radius: 8rpx;
  margin-right: 8rpx;
}

/* 移除独特渐变色，保持统一 */
.area-block:nth-child(1),
.area-block:nth-child(2) {
  background: transparent; /* 改为透明，以适应整体背景色 */
  border-color: rgba(180, 170, 255, 0.2);
}

/* 确保所有模式下区域块样式统一 */
.data-item.multi-select-mode .area-block,
.data-item.multi-select-mode .area-block:nth-child(1),
.data-item.multi-select-mode .area-block:nth-child(2) {
  background: transparent; /* 保持透明背景，与周围背景一致 */
  box-shadow: none; /* 移除阴影 */
  border: none; /* 移除边框 */
}

/* 区块颜色差异优化 - 保留不同文字颜色但不使用背景色区分 */
.area-block:nth-child(1) .detail-value {
  color: #4a90e2;
  font-weight: 700;
}

.area-block:nth-child(2) .detail-value {
  color: #34d399;
  font-weight: 700;
}

/* 按钮容器优化 */
.btn-container {
  display: flex;
  gap: 10rpx;
  width: 100%;
  justify-content: flex-end;
  margin-top: 10rpx;
}

/* 按钮通用样式 */
.action-btn {
  margin: 0;
  padding: 0 24rpx !important;
  min-height: 60rpx !important;
  height: 60rpx;
  font-size: 24rpx !important;
  line-height: 60rpx !important;
  font-weight: normal;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  border-radius: 30rpx;
  box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.1);
  position: relative;
  background: linear-gradient(145deg, rgba(250, 250, 255, 0.95), rgba(245, 243, 254, 0.95)) !important;
  flex-shrink: 0;
}

/* 按钮图标 */
.action-btn .btn-icon {
  width: 28rpx;
  height: 28rpx;
  margin-right: 8rpx;
  flex-shrink: 0;
}

/* 主要按钮 - 查看视频/图片 */
.action-btn.primary {
  background: linear-gradient(90deg, #78b9ff 0%, #c58eff 100%) !important;
  color: #fff;
  border: none;
  min-width: 180rpx;
  max-width: none;
  margin-top: 6rpx;
  align-self: flex-end;
  height: 70rpx !important;
  line-height: 70rpx !important;
  font-size: 28rpx !important;
  padding: 0 30rpx !important;
  box-shadow: 0 4rpx 16rpx rgba(90, 120, 213, 0.4);
}

.action-btn-primary-active {
  background: linear-gradient(90deg, #6db0ff 0%, #b57aff 100%) !important;
  transform: scale(0.98);
}

/* 视频按钮 */
.action-btn.video {
  background: linear-gradient(145deg, #eef6ff, #e5eefa) !important;
  color: #4a90e2;
  border: 1px solid rgba(197, 221, 251, 0.8);
  width: 120rpx;
}

.action-btn.video.selected {
  background-color: #4a90e2 !important;
  color: white;
  border-color: #4a90e2;
}

/* 图片按钮 */
.action-btn.image {
  background: linear-gradient(145deg, #eefbf7, #e5f8f2) !important;
  color: #34d399;
  border: 1px solid rgba(197, 240, 230, 0.8);
  width: 120rpx;
}

.action-btn.image.selected {
  background-color: #34d399 !important;
  color: white;
  border-color: #34d399;
}

/* 禁用Button默认边框 */
.action-btn::after {
  border: none;
}

/* 按钮点击效果 */
.action-btn-active {
  transform: scale(0.98);
  opacity: 0.9;
}

/* 小屏幕适配 */
.small-screen .data-item {
  padding: 20rpx 16rpx;
  margin: 16rpx 12rpx;
}

.small-screen .area-block {
  min-width: 160rpx;
}

.small-screen .tc-value {
  font-size: 28rpx;
}

.small-screen .detail-value {
  font-size: 28rpx;
}

.small-screen .action-btn.primary {
  min-width: 140rpx;
  padding: 0 16rpx !important;
  font-size: 22rpx !important;
}

/* 中等屏幕适配 */
.medium-screen .action-btn.video,
.medium-screen .action-btn.image {
  width: 110rpx;
}

/* 大屏幕适配 */
.large-screen .action-btn {
  padding: 0 30rpx !important;
  height: 64rpx;
  min-height: 64rpx !important;
  font-size: 28rpx !important;
  line-height: 64rpx !important;
}

.large-screen .action-btn.primary {
  min-width: 200rpx;
}

.large-screen .action-btn.video,
.large-screen .action-btn.image {
  width: 130rpx;
}

.large-screen .action-btn .btn-icon {
  width: 30rpx;
  height: 30rpx;
  margin-right: 8rpx;
}

/* 参数标签样式 */
.param-tag {
  font-size: 22rpx;
  color: #666;
  background: linear-gradient(145deg, rgba(243, 246, 250, 0.9), rgba(238, 241, 247, 0.9));
  padding: 4rpx 12rpx;
  border-radius: 16rpx;
  border: 1px solid rgba(180, 170, 255, 0.15);
}

.param-count {
  font-size: 22rpx;
  color: #999;
}

/* 分析类型标记 */
.analysis-type-container {
  display: inline-block;
  margin-left: 12rpx;
  perspective: 1000px;
  vertical-align: middle;
  cursor: pointer;
  flex-shrink: 0;
  width: 60rpx;
  height: 36rpx;
}

.analysis-type {
  position: relative;
  width: 100%;
  height: 100%;
  font-size: 22rpx;
  color: #fff;
  transform-style: preserve-3d;
  transition: transform 0.6s cubic-bezier(0.4, 0.0, 0.2, 1);
  transform: rotateX(0deg);
  will-change: transform;
}

.analysis-type.flipping {
  animation: flip-animation 0.6s cubic-bezier(0.4, 0.0, 0.2, 1);
}

@keyframes flip-animation {
  0% { transform: rotateX(0deg); }
  100% { transform: rotateX(180deg); }
}

.analysis-type.temp {
  transform: rotateX(180deg);
}

.type-front, .type-back {
  position: absolute;
  width: 100%;
  height: 100%;
  backface-visibility: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 12rpx;
  font-weight: normal;
}

.type-front {
  background: linear-gradient(90deg, #78b9ff 0%, #c58eff 100%) !important;
  transform: rotateX(0deg);
}

.type-back {
  background: linear-gradient(90deg, #FF9800 0%, #E53935 100%) !important;
  transform: rotateX(180deg);
}

/* 取消按钮样式 */
.cancel-btn {
  font-size: 28rpx;
  color: #fff;
  padding: 12rpx 36rpx;
  border-radius: 32rpx;
  background: linear-gradient(90deg, #c58eff 0%, #78b9ff 100%);
  box-shadow: 0 4rpx 16rpx rgba(90, 120, 213, 0.4);
  transition: all 0.3s ease;
  margin-left: 20rpx;
  flex-shrink: 0;
}

.cancel-btn-active {
  opacity: 0.8;
  transform: scale(0.98);
}

/* 多选模式下的勾选框 - 定位到数据内容区域的右上角 */
.my-checkbox {
  position: absolute;
  right: 20rpx;
  top: 20rpx; /* 固定距离顶部的位置，更准确 */
  width: 40rpx;
  height: 40rpx;
  border-radius: 12rpx; /* 改为圆角矩形，更现代的设计 */
  border: 3rpx solid #e0e0e0;
  background: #fff;
  box-sizing: border-box;
  transition: all 0.4s cubic-bezier(0.05, 0.7, 0.1, 1);
  will-change: transform, opacity, border-color, background;
  z-index: 15; /* 提高z-index确保不被数据组标签覆盖 */
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.isMultiSelectMode .my-checkbox {
  opacity: 1;
  transform: scale(1); /* 移除垂直居中的transform */
  transition: all 0.4s cubic-bezier(0.05, 0.7, 0.1, 1);
}

/* 添加涟漪反馈效果 */
.isMultiSelectMode .my-checkbox::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  background: rgba(74, 144, 226, 0.2);
  border-radius: 50%;
  transform: translate(-50%, -50%);
  opacity: 0;
  z-index: -1;
  transition: all 0.5s cubic-bezier(0.05, 0.7, 0.1, 1);
}

.isMultiSelectMode .my-checkbox:active::before {
  width: 60rpx;
  height: 60rpx;
  opacity: 1;
  transition: all 0.4s cubic-bezier(0.05, 0.7, 0.1, 1);
}

.my-checkbox.checked {
  background: linear-gradient(135deg, #4a90e2, #7c4dff);
  border-color: #4a90e2;
  transition: all 0.4s cubic-bezier(0.05, 0.7, 0.1, 1);
  transform: scale(1.1); /* 移除垂直居中，只保留放大效果 */
  box-shadow: 0 4rpx 12rpx rgba(74, 144, 226, 0.3);
}

.my-checkbox.checked::after {
  content: '';
  transition: all 0.3s cubic-bezier(0.05, 0.7, 0.1, 1) 0.1s;
  position: absolute;
  top: 35%;
  left: 50%;
  transform: translate(-50%, -50%) rotate(45deg);
  width: 8rpx;
  height: 16rpx;
  border-bottom: 4rpx solid #fff;
  border-right: 4rpx solid #fff;
  animation: checkmark-appear 0.3s cubic-bezier(0.05, 0.7, 0.1, 1) forwards;
}

@keyframes checkmark-appear {
  0% {
    opacity: 0;
    transform: translate(-50%, -50%) rotate(45deg) scale(0.5);
  }
  100% {
    opacity: 1;
    transform: translate(-50%, -50%) rotate(45deg) scale(1);
  }
}

/* 调整多选模式下的内容偏移 */
.data-content {
  transition: all 0.4s cubic-bezier(0.05, 0.7, 0.1, 1);
  margin-left: 0;
  padding: 4rpx 0;
  will-change: margin-left, transform;
  width: 100%;
}

.isMultiSelectMode .data-content {
  margin-left: 0; /* 复选框移到右侧后，恢复原始左边距 */
  margin-right: 70rpx; /* 为右侧复选框预留空间 */
  transition: all 0.4s cubic-bezier(0.05, 0.7, 0.1, 1);
  transform: translateX(0);
}

/* 数据项基础样式 - 单选模式 */
.data-item {
  background: linear-gradient(145deg, #f9f8ff 0%, #f3f1fc 100%);
  border-radius: 16rpx;
  padding: 24rpx 20rpx;
  margin: 20rpx 16rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
  position: relative;
  transition: all 0.6s cubic-bezier(0.22, 0.61, 0.36, 1); /* 更平滑的过渡时间和曲线 */
  overflow: visible;
  border-left: 4rpx solid #4a90e2;
  margin-top: 28rpx;
  will-change: transform, opacity;
  opacity: 1;
}

/* 多选模式下数据项样式 */
.data-item.multi-select-mode {
  margin: 20rpx 16rpx;
  margin-top: 28rpx;
  opacity: 0; /* 初始不可见 */
  transform: translateY(20rpx); /* 轻微向下偏移，为动画做准备 */
  transition: opacity 0.7s cubic-bezier(0.22, 0.61, 0.36, 1), 
              transform 0.7s cubic-bezier(0.22, 0.61, 0.36, 1); /* 分开指定属性实现更精细的控制 */
}

/* 显示多选模式下的数据项 */
.animateItems .data-item.multi-select-mode {
  opacity: 1;
  transform: translateY(0);
}

/* 添加逐个出现的延迟效果 - 每个项目延迟50ms，最多10个 */
.animateItems .data-item.multi-select-mode:nth-child(1) {
  transition-delay: 0.05s;
}

.animateItems .data-item.multi-select-mode:nth-child(2) {
  transition-delay: 0.1s;
}

.animateItems .data-item.multi-select-mode:nth-child(3) {
  transition-delay: 0.15s;
}

.animateItems .data-item.multi-select-mode:nth-child(4) {
  transition-delay: 0.2s;
}

.animateItems .data-item.multi-select-mode:nth-child(5) {
  transition-delay: 0.25s;
}

.animateItems .data-item.multi-select-mode:nth-child(6) {
  transition-delay: 0.3s;
}

.animateItems .data-item.multi-select-mode:nth-child(7) {
  transition-delay: 0.35s;
}

.animateItems .data-item.multi-select-mode:nth-child(8) {
  transition-delay: 0.4s;
}

.animateItems .data-item.multi-select-mode:nth-child(9) {
  transition-delay: 0.45s;
}

.animateItems .data-item.multi-select-mode:nth-child(10) {
  transition-delay: 0.5s;
}

/* 数据列表动画效果 */

/* 定义数据项弹出动画 - 优化为包含位移和透明度变化 */
@keyframes item-pop-in {
  from {
    opacity: 0;
    transform: translateY(20rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 添加淡出动画，用于退出多选模式 */
.data-list.mode-transition .data-item {
    opacity: 0;
  transition: opacity 0.6s cubic-bezier(0.22, 0.61, 0.36, 1),
              transform 0.6s cubic-bezier(0.22, 0.61, 0.36, 1);
}

/* 底部按钮淡出动画 - 保留原有效果 */
.data-list.mode-transition ~ .bottom-buttons {
    transform: translateY(100%);
    opacity: 0;
  transition: all 0.7s cubic-bezier(0.22, 0.61, 0.36, 1);
}

/* 添加单选模式下的数据组标签 - 使用更精确的选择器 */
.data-item:not(.multi-select-mode)::before {
  content: "数据组 " attr(data-group);
  position: absolute;
  top: -14rpx;
  left: 24rpx;
  font-size: 24rpx;
  padding: 8rpx 24rpx;
  border-radius: 24rpx;
  z-index: 10;
  letter-spacing: 1rpx;
  background: linear-gradient(90deg, #c58eff 0%, #78b9ff 100%); /* 反向渐变色 */
  color: white;
  min-width: 120rpx;
  text-align: center;
  box-shadow: 0 4rpx 16rpx rgba(90, 120, 213, 0.4);
}

/* 数据内容区域 */
.data-content {
  width: 100%;
}

/* 多选模式下的 header 样式 */
.isMultiSelectMode .data-header {
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  background: linear-gradient(145deg, rgba(246, 244, 255, 0.9), rgba(241, 239, 252, 0.9));
  border-radius: 12rpx;
  border: 1px solid rgba(180, 170, 255, 0.2);
}

/* 多选模式下的标题样式 */
.isMultiSelectMode .header-title {
  flex-grow: 1;
}

/* 多选模式下的取消按钮 */
.isMultiSelectMode .cancel-btn {
  flex-shrink: 0;
}

/* TC值和标签样式 */
.data-main {
  display: flex;
  flex-direction: row;
  align-items: center;
  flex-wrap: wrap;
  width: 100%;
  position: relative;
  padding-bottom: 6rpx;
}

/* TC值样式 */
.tc-value {
  font-size: 32rpx;
  color: #333;
  font-weight: 600;
  max-width: 60%;
  white-space: normal;
  word-break: break-all;
  overflow: visible;
  line-height: 1.3;
  flex-shrink: 1;
  margin-right: 8rpx;
}

/* 时间样式 */
.time-row {
  font-size: 24rpx;
  color: #999;
  margin-left: auto;
  white-space: nowrap;
  flex-shrink: 0;
}

/* 标签样式 */
.analysis-type {
  font-size: 22rpx;
  padding: 4rpx 10rpx;
  border-radius: 6rpx;
  color: #fff;
  margin-left: 8rpx;
  font-weight: normal;
  flex-shrink: 0;
}

.analysis-type.formal {
  background: linear-gradient(90deg, #78b9ff 0%, #c58eff 100%);
}

.analysis-type.temp {
  background: linear-gradient(90deg, #c58eff 0%, #78b9ff 100%);
}

/* 数据详情区域 */
/* 删除重复样式 */

/* 详情行样式 */
.detail-row {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  padding: 8rpx 0;
  width: 100%;
  flex-wrap: wrap;
  gap: 16rpx;
}

/* 区域容器 */
.detail-left {
  display: flex;
  flex-direction: row;
  flex: 1;
  overflow: hidden;
}

/* 区块样式 */
.area-block {
  display: flex;
  flex-direction: row;
  padding: 6rpx 10rpx;
  background: transparent; /* 从#fafafa改为透明，与周围背景一致 */
  border-radius: 8rpx;
  margin-right: 8rpx;
}

/* 标签样式 */
.detail-label {
  font-size: 28rpx;
  color: #666;
  margin-right: 8rpx;
}

/* 数值样式 */
.detail-value {
  font-size: 28rpx;
  color: #333;
}

/* C区和T区的颜色区分 */
.area-block:nth-child(1) .detail-value {
  color: #4a90e2;
}

.area-block:nth-child(2) .detail-value {
  color: #34d399;
}

/* 统一单选和多选模式下的区域块样式 */
.multi-select-mode .area-block,
.data-item:not(.multi-select-mode) .area-block {
  padding: 6rpx 10rpx;
  background: transparent; /* 从#fafafa改为透明，与周围背景一致 */
  border-radius: 8rpx;
  margin-right: 8rpx;
  align-items: center;
  width: 48%;
  overflow: hidden;
}

/* 统一单选和多选模式下的数值样式 */
.multi-select-mode .detail-value,
.data-item:not(.multi-select-mode) .detail-value {
  min-width: 0;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  word-break: keep-all;
}

/* 数值过长时的特殊处理 - 点击或长按可显示完整内容 */
.detail-value-expanded {
  white-space: normal;
  overflow: visible;
  text-overflow: clip;
  word-break: break-all;
  position: relative;
  z-index: 10;
  background: #f0f0f0;
  box-shadow: 0 0 5rpx rgba(0,0,0,0.1);
}

/* 按钮样式 */
.action-btn.primary {
  margin-left: auto;
  margin-top: 8rpx;
}

/* 参数行样式 */
.param-row {
  margin-top: 12rpx;
  display: flex;
  align-items: center;
  gap: 10rpx;
  background: transparent;
}

/* 媒体选项中的文本 */
.media-option .option-text {
  font-size: 30rpx;
  font-weight: 500;
  position: relative;
  z-index: 1;
  letter-spacing: 8rpx;
  color: #3498db;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.02);
  font-family: "PingFang SC", -apple-system, BlinkMacSystemFont, "Helvetica Neue", Helvetica, Arial, sans-serif;
}

/* 导出选项中的文本 */
.export-option .option-text {
  font-size: 28rpx;
  color: #333;
}

/* 数据组标签样式，与主界面按钮保持一致 */
.data-item.multi-select-mode::before {
  content: "数据组 " attr(data-group);
  position: absolute;
  top: -25rpx;
  left: 24rpx;
  font-size: 24rpx;
  padding: 8rpx 24rpx;
  border-radius: 24rpx;
  z-index: 10;
  letter-spacing: 1rpx;
  background: linear-gradient(90deg, #78b9ff 0%, #c58eff 100%); /* 主界面按钮渐变色 */
  color: white;
  min-width: 120rpx;
  text-align: center;
  box-shadow: 0 4rpx 16rpx rgba(90, 120, 213, 0.4);
  transition: all 0.7s cubic-bezier(0.22, 0.61, 0.36, 1);
  opacity: 0; /* 初始透明 */
  transform: translateY(-5rpx);
}

/* 动画显示时的标签淡入 */
.animateItems .data-item.multi-select-mode::before {
  opacity: 1;
  transform: translateY(0);
  transition-delay: 0.15s; /* 轻微延迟，让标签出现比数据项慢一点 */
}

/* 多选模式下的数据项样式 - 实现上浮和放大效果，增加数据组之间的间距 */
.data-item.multi-select-mode {
  transform: translateY(-8rpx) scale(1.05);
  box-shadow: 0 8rpx 20rpx rgba(0, 0, 0, 0.08);
  z-index: 1; /* 提高z-index，实现空间深度感知 */
  transition: all 0.7s cubic-bezier(0.22, 0.61, 0.36, 1);
  will-change: transform, opacity, box-shadow;
  margin-top: 48rpx; /* 减少顶部间距，但保持组间有一定间隔 */
  margin-bottom: 40rpx; /* 减少底部间距，改善整体布局紧凑性 */
  opacity: 0; /* 初始状态为透明 */
}

/* 退出多选模式时的数据块聚拢效果 */
.data-item:not(.multi-select-mode) {
  transition: all 0.7s cubic-bezier(0.22, 0.61, 0.36, 1);
  transform: translateY(0) scale(1.0);
}

/* 移除不同索引的数据组标签不同颜色的设置，统一使用主界面按钮颜色 */
.data-item.multi-select-mode:nth-child(3n)::before {
  background: linear-gradient(90deg, #78b9ff 0%, #c58eff 100%); /* 修改为主界面按钮渐变色 */
  box-shadow: 0 4rpx 16rpx rgba(90, 120, 213, 0.4);
}

.data-item.multi-select-mode:nth-child(3n+1)::before {
  background: linear-gradient(90deg, #78b9ff 0%, #c58eff 100%); /* 修改为主界面按钮渐变色 */
  box-shadow: 0 4rpx 16rpx rgba(90, 120, 213, 0.4);
}

/* 未选中状态下的按钮样式 */
.data-item:not(.selected-item) .media-btn {
  opacity: 0.5;
  pointer-events: none;
  cursor: not-allowed;
  background-color: #f5f5f5 !important;
  border-color: #ddd;
  color: #999 !important;
  box-shadow: none;
  transition: all 0.4s cubic-bezier(0.05, 0.7, 0.1, 1);
  transform: scale(0.9);
  transform-origin: center center;
  will-change: transform, opacity, background-color, border-color;
}

/* 选中状态下的媒体按钮默认样式 */
.data-item.selected-item .media-btn {
  opacity: 1;
  pointer-events: auto;
  cursor: pointer;
  box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.08);
  transition: all 0.4s cubic-bezier(0.05, 0.7, 0.1, 1);
  transform: scale(1);
  transform-origin: center center;
  will-change: transform, opacity, background-color, border-color;
}

/* 选中状态下的视频按钮 */
.data-item.selected-item .media-btn.video {
  background-color: rgba(74, 144, 226, 0.05) !important;
  color: #4a90e2 !important;
  border-color: rgba(74, 144, 226, 0.3);
}

.data-item.selected-item .media-btn.video.selected {
  background-color: #4a90e2 !important;
  color: white !important;
  border-color: #4a90e2;
}

/* 选中状态下的图片按钮 */
.data-item.selected-item .media-btn.image {
  background-color: rgba(52, 211, 153, 0.05) !important;
  color: #34d399 !important;
  border-color: rgba(52, 211, 153, 0.3);
}

.data-item.selected-item .media-btn.image.selected {
  background-color: #34d399 !important;
  color: white !important;
  border-color: #34d399;
}

/* 返回按钮 */
.back-btn {
  width: 90%;
  height: 90rpx;
  line-height: 90rpx;
  text-align: center;
  border-radius: 45rpx;
  font-size: 32rpx;
  color: #fff;
  background: linear-gradient(90deg, #78b9ff 0%, #c58eff 100%);
  margin: 0 auto;
  position: relative;
  transition: transform 0.3s ease, opacity 0.3s ease;
  box-shadow: 0 4rpx 16rpx rgba(90, 120, 213, 0.4);
}

.back-btn:active {
  transform: translateY(2rpx);
  box-shadow: 0 2rpx 6rpx rgba(90, 120, 213, 0.2);
  opacity: 0.9;
}

/* 底部按钮区域 - 基础样式 */
.bottom-buttons {
  width: 100%;
  z-index: 1500;
  display: flex;
  justify-content: center;
  align-items: center;
  box-sizing: border-box;
}

/* 单选模式下的底部按钮区域样式 */
.bottom-buttons:not(.multi-select-mode) {
  position: fixed;
  bottom: calc(110rpx + env(safe-area-inset-bottom)); /* 直接定位在底部导航栏上方 */
  left: 0;
  right: 0;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 30rpx 0;
  z-index: 1500;
  background: rgba(246, 244, 255, 0.85); /* 半透明浅紫色背景，与整体风格协调 */
  backdrop-filter: blur(10rpx); /* 添加模糊效果增强视觉融合 */
  width: 100%;
  border-radius: 0;
  box-shadow: 0 -2rpx 10rpx rgba(90, 120, 213, 0.08); /* 淡化阴影 */
  border-top: 1rpx solid rgba(180, 170, 255, 0.15); /* 淡色边框 */
  border-bottom: none;
  transition: all 0.3s ease;
}

/* 这里有样式冲突已处理 */

/* 底部按钮淡入动画 */
/* 底部按钮动画已统一到.isMultiSelectMode .bottom-buttons.multi-select-mode中 */

/* 按钮组样式 - 确保与单选模式一致 */
.btn-group {
  display: flex;
  justify-content: center; /* 居中对齐，与单选模式一致 */
  align-items: center;
  width: 90%; /* 与单选模式返回按钮宽度一致 */
  margin: 0 auto; /* 居中显示 */
  gap: 15rpx; /* 使用固定间距 */
}

/* 删除、导出和生成图表按钮的基础样式 */
.delete-btn, .export-btn, .generate-btn {
  flex: 1;
  min-width: 180rpx;
  height: 90rpx;
  line-height: 90rpx;
  transition: all 0.4s cubic-bezier(0.05, 0.7, 0.1, 1);
  transform: scale(1);
  transform-origin: center center;
  will-change: transform, opacity, background-color;
  text-align: center;
  border-radius: 45rpx;
  font-size: 30rpx;
  color: #fff;
  letter-spacing: 2rpx;
  font-weight: 500;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  position: relative;
  overflow: hidden;
  box-shadow: 0 6rpx 20rpx rgba(0, 0, 0, 0.2);
}

/* 删除按钮样式 */
.delete-btn {
  /* 通用样式已在.btn-group .delete-btn中定义 */
  background: linear-gradient(90deg, #c58eff 0%, #78b9ff 100%);
  box-shadow: 0 4rpx 16rpx rgba(90, 120, 213, 0.4);
}

/* 添加高光效果以增强视觉一致性 */
.delete-btn::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 50%;
  background: linear-gradient(to bottom, rgba(255, 255, 255, 0.3), rgba(255, 255, 255, 0));
  border-radius: 45rpx 45rpx 0 0;
}

/* 导出按钮样式 */
.export-btn {
  /* 通用样式已在.btn-group .export-btn中定义 */
  background: linear-gradient(90deg, #c58eff 0%, #78b9ff 100%);
  box-shadow: 0 4rpx 16rpx rgba(90, 120, 213, 0.4);
}

/* 生成图表按钮样式 */
.generate-btn {
  flex: 1;
  min-width: 180rpx;
  height: 90rpx;
  line-height: 90rpx;
  text-align: center;
  border-radius: 45rpx;
  font-size: 30rpx;
  color: #fff;
  letter-spacing: 2rpx;
  font-weight: 500;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  position: relative;
  overflow: hidden;
  /* 修改为取消按钮的紫色渐变 */
  background: linear-gradient(90deg, #8E54E9, #4776E6);
  box-shadow: 0 4rpx 12rpx rgba(142, 84, 233, 0.3);
}

/* 返回按钮样式 */
.back-btn {
  width: 90%;
  height: 88rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  color: #fff;
  font-size: 32rpx;
  font-weight: 500;
  letter-spacing: 4rpx;
  background: linear-gradient(90deg, #78b9ff 0%, #c58eff 100%);
  border-radius: 44rpx;
  box-shadow: 0 4rpx 12rpx rgba(90, 120, 213, 0.3);
  position: relative;
  overflow: hidden;
  transition: all 0.3s ease;
}

/* 添加按钮内部高光效果 */
.back-btn::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(180deg, rgba(255, 255, 255, 0.3), rgba(255, 255, 255, 0));
  z-index: 1;
  border-radius: 40rpx;
}

/* 所有按钮的点击效果 */
.back-btn:active, .delete-btn:active, .export-btn:active, .generate-btn:active {
  transform: translateY(2rpx) scale(0.98);
  box-shadow: 0 2rpx 6rpx rgba(90, 120, 213, 0.2);
  opacity: 0.9;
}

/* 已禁用按钮悬停效果，避免颜色滞留问题 */
/*
.button-hover {
  opacity: 0.8;
}
*/

/* 个人页面样式 */
/* 性能优化总结:
 * 1. 硬件加速相关属性:
 *    - will-change: transform, opacity
 *    - backface-visibility: hidden
 *    - perspective: 1000
 *    - transform: translateZ(0)
 *    - transform-style: preserve-3d
 *
 * 2. 动画性能优化:
 *    - 使用transform和opacity进行动画而不是all
 *    - GPU加速相关属性
 *    - 优化贝塞尔曲线
 *    - 3D变换相关属性促使GPU渲染
 *
 * 3. 微信小程序不规范选择器修复:
 *    - 替换page标签选择器为.page-container类选择器
 *    - 替换:active伪类为对应的active类(如.button-active)
 *    - 移除::-webkit-scrollbar等伪元素选择器
 *    - 替换媒体查询为对应的类选择器(.small-screen, .medium-screen)
 */

/* 页面根元素容器类 */
.page-container {
  background-color: #f7f8fa;
  background: #f7f8fa;
  z-index: 999;
  display: flex;
  flex-direction: column;
}

/* 按钮样式 - 移除按钮伪类选择器 */
.action-btn-no-border {
  border: none;
}

/* 用户信息点击状态类 */
.userinfo-btn-active {
  transform: scale(0.98);
  background: rgba(255, 255, 255, 0.3);
}

/* 按钮点击状态类 */
.action-btn-pressed {
  transform: scale(0.98);
  opacity: 0.9;
}

/* 添加按钮点击效果类 */
.back-btn-active {
  transform: translateY(2rpx);
  box-shadow: 0 2rpx 6rpx rgba(90, 120, 213, 0.2);
  opacity: 0.9;
}

.button-hover-class {
  opacity: 0.8;
}

.container {
  background-color: #f7f8fa;
  padding-bottom: calc(110rpx + env(safe-area-inset-bottom));
  /* padding-top通过内联样式动态设置 */
}

/* 用户信息部分 */
.user-section {
  background: linear-gradient(135deg, #4a90e2, #7c4dff);
  padding: 40rpx 0 105rpx 0; /* 调整底部padding为105rpx，让蓝紫色背景向下延伸 */
  position: relative;
  overflow: hidden;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
}

.user-section::after {
  content: '';
  position: absolute;
  bottom: -60%;
  left: -10%;
  width: 120%;
  height: 200rpx;
  background: rgba(255, 255, 255, 0.1);
  transform: rotate(-5deg);
}

.userinfo {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 30rpx;
  position: relative;
  z-index: 1;
  transition: all 0.3s ease;
}

.avatar-wrapper {
  padding: 0;
  width: 160rpx;
  height: 160rpx;
  border-radius: 50%;
  margin-bottom: 10rpx;
  background-color: transparent;
}

.avatar-wrapper::after {
  border: none;
}

.userinfo-avatar {
  width: 160rpx;
  height: 160rpx;
  border-radius: 50%;
  background-size: cover;
  background-color: white;
}

.login-btn, .logout-btn {
  width: 240rpx;
  height: 80rpx;
  margin-top: 24rpx;
  font-size: 28rpx;
  padding: 0;
  line-height: 80rpx;
  text-align: center;
  color: #fff;
  border-radius: 40rpx;
  transition: all 0.3s ease;
}

.login-btn {
  background: rgba(255, 255, 255, 0.2);
  border: 2rpx solid rgba(255, 255, 255, 0.5);
}

.logout-btn {
  background: linear-gradient(90deg, #6ba8f7, #a585ff);
  border: 2rpx solid rgba(255, 255, 255, 0.8);
  box-shadow: 0 4rpx 12rpx rgba(107, 168, 247, 0.3);
}

.login-btn-active, .logout-btn-active {
  transform: scale(0.98);
  background: rgba(255, 255, 255, 0.3);
}

.userinfo-nickname {
  color: #fff;
  font-size: 34rpx;
  margin-top: 24rpx;
  font-weight: 500;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
  background: transparent;
  text-align: center;
  width: 100%;
}

.userinfo-nickname::placeholder {
  color: rgba(255, 255, 255, 0.8);
}

.space-line {
  height: 16rpx;
  background-color: transparent;
}

.menu-list {
  padding: 20rpx;
}

.weui-cells {
  border-radius: 16rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.05);
  overflow: hidden;
  margin: 0;
}

.weui-cell {
  padding: 32rpx;
  position: relative;
  display: flex;
  align-items: center;
  background: #fff;
  border-bottom: 1rpx solid rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
}

.weui-cell-active {
  background: #f9f9f9;
}

.weui-cell-last {
  border-bottom: none;
}

.weui-cell__hd {
  margin-right: 24rpx;
  width: 48rpx;
  height: 48rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.weui-cell__hd .cell-image {
  width: 40rpx;
  height: 40rpx;
  opacity: 0.9;
}

.weui-cell__bd {
  flex: 1;
  font-size: 30rpx;
  color: #333;
  font-weight: 500;
}

.weui-cell__ft {
  color: #bbb;
  font-size: 24rpx;
}

.weui-cell__ft_in-access::after {
  content: " ";
  display: inline-block;
  height: 12rpx;
  width: 12rpx;
  border-width: 2rpx 2rpx 0 0;
  border-color: #bbb;
  border-style: solid;
  transform: matrix(0.71, 0.71, -0.71, 0.71, 0, 0);
  margin-left: 16rpx;
}

.badge {
  position: absolute;
  top: 50%;
  right: 80rpx;
  transform: translateY(-50%);
  background: #ff4d4f;
  color: #fff;
  font-size: 22rpx;
  width: 32rpx;
  height: 32rpx;
  line-height: 32rpx;
  text-align: center;
  border-radius: 50%;
  font-weight: bold;
  box-shadow: 0 2rpx 8rpx rgba(255, 77, 79, 0.3);
}

/* 数据显示模式 - 支持动态适配导航栏高度 */
.data-display {
  position: fixed;
  /* top和height通过内联样式动态设置 */
  left: 0;
  width: 100%;
  background: linear-gradient(160deg, #f3f1fc 0%, #eceafc 50%, #e8e5fb 100%);
  z-index: 999;
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
  will-change: transform, opacity; /* 提高动画性能 */
}

/* 添加背景遮罩层 - 用于模式切换时的深度感知 */
.data-display::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0);
  z-index: 0;
  pointer-events: none;
  transition: background-color 0.15s cubic-bezier(0.05, 0.7, 0.1, 1);
  will-change: background-color;
}

/* 多选模式下的背景遮罩层 - 40%透明度 */
.data-display.isMultiSelectMode::before {
  background-color: rgba(0, 0, 0, 0.4);
  z-index: 0;
}

/* 返回按钮容器 - 单选模式专用，确保位于底部且可见 */
.bottom-buttons:not(.multi-select-mode) {
  position: fixed;
  bottom: calc(110rpx + env(safe-area-inset-bottom)); /* 直接定位在底部导航栏上方 */
  left: 0;
  right: 0;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 30rpx 0;
  z-index: 1500;
  background: rgba(246, 244, 255, 0.85); /* 半透明浅紫色背景，与整体风格协调 */
  backdrop-filter: blur(10rpx); /* 添加模糊效果增强视觉融合 */
  width: 100%;
  border-radius: 0;
  box-shadow: 0 -2rpx 10rpx rgba(90, 120, 213, 0.08); /* 淡化阴影 */
  border-top: 1rpx solid rgba(180, 170, 255, 0.15); /* 淡色边框 */
  border-bottom: none;
  transition: all 0.3s ease;
}

/* 返回按钮样式 */
.back-btn {
  width: 90%;
  height: 88rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  color: #fff;
  font-size: 32rpx;
  font-weight: 500;
  letter-spacing: 4rpx;
  background: linear-gradient(90deg, #78b9ff 0%, #c58eff 100%);
  border-radius: 44rpx;
  box-shadow: 0 4rpx 12rpx rgba(90, 120, 213, 0.3);
  z-index: 1501;
  position: relative;
  overflow: hidden;
  transition: all 0.3s ease;
}

/* 添加按钮内部高光效果 */
.back-btn::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(180deg, rgba(255, 255, 255, 0.3), rgba(255, 255, 255, 0));
  z-index: 1;
  border-radius: 40rpx;
}

/* 添加按钮点击效果 */
.back-btn:active {
  transform: translateY(2rpx);
  box-shadow: 0 2rpx 6rpx rgba(90, 120, 213, 0.2);
  opacity: 0.9;
}

.data-header {
  padding: 30rpx 30rpx 25rpx 30rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  background: linear-gradient(145deg, rgba(246, 244, 255, 0.9), rgba(241, 239, 252, 0.9));
  box-shadow: 0 2rpx 8rpx rgba(90, 120, 213, 0.1);
  flex-shrink: 0;
  border-radius: 12rpx;
  border: 1px solid rgba(180, 170, 255, 0.2);
  transition: all 0.4s cubic-bezier(0.05, 0.7, 0.1, 1);
  transform: translateY(0);
  opacity: 1;
  will-change: transform, opacity, height;
}

/* 模式过渡状态下的数据头部 */
.mode-transition .data-header {
  opacity: 0;
  transform: translateY(-40rpx);
  transition: all 0.6s cubic-bezier(0.22, 0.61, 0.36, 1);
}

/* 标题动画 */
.header-title {
  font-size: 40rpx;
  font-weight: 600;
  color: #303133;
  line-height: 1.3;
  flex-shrink: 0;
  position: static;
  letter-spacing: normal;
  text-shadow: none;
  padding-left: 0;
  border-left: none;
  display: flex;
  align-items: center;
  width: auto;
  transition: all 0.5s cubic-bezier(0.22, 0.61, 0.36, 1);
  animation: title-fade-in 0.5s cubic-bezier(0.05, 0.7, 0.1, 1) forwards;
}

@keyframes title-fade-in {
  0% {
    opacity: 0.7;
    transform: translateY(-10rpx);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 多选模式下的标题样式 */
.isMultiSelectMode .header-title {
  color: #4a90e2;
  transform: scale(1.05);
  transition: all 0.5s cubic-bezier(0.22, 0.61, 0.36, 1);
}

/* 非多选模式：搜索框容器 */
.search-container {
  display: flex;
  align-items: center;
  margin-top: 20rpx;
  background: rgba(255, 255, 255, 0.6);
  border-radius: 32rpx;
  padding: 8rpx 24rpx;
  width: 95%;
  border: 1rpx solid rgba(180, 170, 255, 0.2);
  box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.05);
  transition: all 0.6s cubic-bezier(0.22, 0.61, 0.36, 1);
  transform-origin: center;
  will-change: transform, opacity;
  
  /* 添加搜索框动画初始状态 */
  opacity: 0;
  transform: translateY(-20rpx) scale(0.95);
  animation: search-slide-in 0.6s cubic-bezier(0.05, 0.7, 0.1, 1) forwards;
  animation-delay: 0.15s; /* 稍微延迟以便在标题转换后开始 */
}

/* 搜索框出现的动画 */
@keyframes search-slide-in {
  0% {
    opacity: 0;
    transform: translateY(-20rpx) scale(0.95);
  }
  100% {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

/* 模式过渡中的搜索框容器 */
.mode-transition .search-container {
  opacity: 0;
  transform: translateY(15rpx) scale(0.95);
  transition: all 0.6s cubic-bezier(0.22, 0.61, 0.36, 1);
  animation: none; /* 过渡期间禁用动画 */
}

/* 搜索输入框 */
.search-input {
  flex: 1;
  height: 64rpx;
  font-size: 28rpx;
  color: #333;
  padding: 0 10rpx;
  transition: all 0.25s cubic-bezier(0.05, 0.7, 0.1, 1);
}

/* 搜索输入框聚焦效果 */
.search-input-focused {
  background: rgba(255, 255, 255, 0.85);
  box-shadow: 0 2rpx 8rpx rgba(90, 120, 213, 0.15);
}

/* 多选模式：图表按钮容器 */
.cancel-btn-container {
  margin-top: 20rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  transition: all 0.4s cubic-bezier(0.05, 0.7, 0.1, 1);
  transform: translateY(0);
  opacity: 1; 
  will-change: transform, opacity;
  animation: fade-slide-down 0.45s cubic-bezier(0.05, 0.7, 0.1, 1);
}

@keyframes fade-slide-down {
  from {
    opacity: 0;
    transform: translateY(-20rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 图表生成按钮 */
.generate-btn {
  background: linear-gradient(90deg, #4A90E2, #5B86E5);
  color: #fff;
  font-size: 28rpx;
  padding: 12rpx 36rpx;
  border-radius: 32rpx;
  box-shadow: 0 4rpx 12rpx rgba(90, 120, 213, 0.3);
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  will-change: transform, box-shadow;
}

.generate-btn:active {
  transform: scale(0.97);
  box-shadow: 0 2rpx 6rpx rgba(90, 120, 213, 0.2);
}

/* 模式过渡中的底部按钮 */
.mode-transition ~ .bottom-buttons {
  transform: translateY(100%);
  opacity: 0;
  transition: all 0.7s cubic-bezier(0.22, 0.61, 0.36, 1);
}

/* 底部按钮容器 - 多选模式 */
.bottom-buttons.multi-select-mode {
  position: fixed;
  bottom: calc(110rpx + env(safe-area-inset-bottom));
  left: 0;
  right: 0;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 30rpx;
  background: rgba(246, 244, 255, 0.95);
  backdrop-filter: blur(10rpx);
  box-shadow: 0 -2rpx 10rpx rgba(90, 120, 213, 0.1);
  border-top: 1rpx solid rgba(180, 170, 255, 0.15);
  z-index: 1500;
}

/* 底部按钮的动画效果 */
.bottom-buttons.multi-select-mode {
  transform: translateY(0);
  opacity: 1;
  transition: transform 0.7s cubic-bezier(0.22, 0.61, 0.36, 1),
              opacity 0.7s cubic-bezier(0.22, 0.61, 0.36, 1);
  will-change: transform, opacity;
}

/* 模式过渡中的底部按钮 - 消失动画 */
.mode-transition ~ .bottom-buttons.multi-select-mode {
  transform: translateY(100%);
  opacity: 0;
  transition: transform 0.7s cubic-bezier(0.22, 0.61, 0.36, 1),
              opacity 0.7s cubic-bezier(0.22, 0.61, 0.36, 1);
  z-index: 1500;
}

/* 单选模式下的按钮组 */
.single-mode-buttons {
  display: flex;
  width: 100%;
  justify-content: space-between;
  gap: 20rpx;
}

/* 返回按钮样式优化 */
.back-btn {
  width: 70%;
  height: 88rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  color: #fff;
  font-size: 32rpx;
  font-weight: 500;
  letter-spacing: 4rpx;
  background: linear-gradient(90deg, #78b9ff 0%, #c58eff 100%);
  border-radius: 44rpx;
  box-shadow: 0 4rpx 12rpx rgba(90, 120, 213, 0.3);
  position: relative;
  overflow: hidden;
  transition: all 0.3s ease;
}

/* 添加选择按钮 */
.select-btn {
  width: 30%;
  height: 88rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  color: #4A90E2;
  font-size: 32rpx;
  font-weight: 500;
  background: rgba(255, 255, 255, 0.8);
  border: 1rpx solid rgba(74, 144, 226, 0.4);
  border-radius: 44rpx;
  box-shadow: 0 4rpx 12rpx rgba(90, 120, 213, 0.15);
  transition: all 0.3s ease;
}

.select-btn:active {
  transform: translateY(2rpx);
  box-shadow: 0 2rpx 6rpx rgba(90, 120, 213, 0.1);
  opacity: 0.9;
}

/* 多选模式按钮组 */
.btn-group {
  display: flex;
  width: 100%;
  justify-content: space-between;
}

/* 等宽按钮 */
.equal-width-btn {
  flex: 1;
  max-width: 31%;
  height: 80rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 28rpx;
  font-weight: 500;
  border-radius: 40rpx;
  transition: all 0.3s ease;
}

/* 删除按钮 */
.delete-btn {
  background: linear-gradient(90deg, #FF5252, #FF1744);
  color: #fff;
  box-shadow: 0 4rpx 12rpx rgba(255, 23, 68, 0.3);
}

/* 导出按钮 */
.export-btn {
  background: linear-gradient(90deg, #26C6DA, #00ACC1);
  color: #fff;
  box-shadow: 0 4rpx 12rpx rgba(0, 172, 193, 0.3);
}

/* 取消按钮 */
.cancel-btn {
  background: linear-gradient(90deg, #BDBDBD, #9E9E9E);
  color: #fff;
  box-shadow: 0 4rpx 12rpx rgba(189, 189, 189, 0.3);
}

/* 按钮悬停/点击效果 */
/* 已禁用按钮悬停效果，避免颜色滞留问题 */
/*
.button-hover {
  transform: translateY(2rpx) scale(0.98);
  box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.15);
  opacity: 0.9;
}
*/

/* 底部空间占位符 - 解决滚动视图底部遮挡问题 */
.bottom-space-holder {
  height: 0;
  transition: height 0.4s cubic-bezier(0.05, 0.7, 0.1, 1);
}

.bottom-space-holder.active {
  height: 180rpx;
}

/* 数据列表过渡动画优化 */
.data-list {
  flex: 1;
  padding: 0 20rpx;
  box-sizing: border-box;
  transition: opacity 0.7s cubic-bezier(0.22, 0.61, 0.36, 1);
}

.data-list.mode-transition {
  opacity: 0.5;
  transform: scale(0.98);
  transition: all 0.4s cubic-bezier(0.05, 0.7, 0.1, 1);
}

.data-main {
  width: 100%;
  position: relative;
  padding-right: 16rpx;
}

/* TC值样式优化 */
.tc-value {
  font-size: 30rpx;
  font-weight: 500;
  color: #333;
  max-width: 60%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* 标签容器样式 - 多选模式（保持绝对定位） */
.analysis-type-container-multi {
  margin-left: 10rpx;
  align-self: center;
  position: absolute;
  right: 90rpx;
  top: 0;
  cursor: pointer;
  width: 80rpx;
  height: 40rpx;
  perspective: 800rpx;
  z-index: 5;
}

/* 标签容器样式 - 单选模式（参考多选模式，使用绝对定位） */
.analysis-type-container-single {
  margin-left: 10rpx;
  align-self: center;
  position: absolute;
  right: 90rpx;
  top: 0;
  cursor: pointer;
  width: 80rpx;
  height: 40rpx;
  perspective: 800rpx;
  z-index: 5;
}

/* 标签样式 */
.analysis-type {
  position: relative;
  width: 100%;
  height: 100%;
  transition: transform 0.6s;
  transform-style: preserve-3d;
  transform-origin: center center;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
  border-radius: 6rpx;
}

/* 翻转效果 */
.analysis-type.flipping {
  transform: rotateX(180deg);
  transition: transform 0.6s cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

/* 标签正面和背面共享样式 */
.type-front, .type-back {
  position: absolute;
  width: 100%;
  height: 100%;
  backface-visibility: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 22rpx;
  font-weight: normal;
  color: #fff;
  border-radius: 6rpx;
  box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.1);
}

.type-front {
  background: linear-gradient(90deg, #78b9ff 0%, #c58eff 100%) !important;
  transform: rotateX(0deg);
}

.type-back {
  background: linear-gradient(90deg, #FF9800 0%, #E53935 100%) !important;
  transform: rotateX(180deg);
}

/* 时间行样式调整 */
.time-row {
  font-size: 24rpx;
  color: #999;
  text-align: right;
  margin-top: 12rpx;
  width: 100%;
  display: block;
}

/* 详情区域背景 */
.data-details {
  background: linear-gradient(145deg, rgba(246, 244, 255, 0.9), rgba(241, 239, 252, 0.9));
  border-radius: 12rpx;
  padding: 16rpx;
  margin-top: 10rpx;
  border: 1px solid rgba(180, 170, 255, 0.2);
  box-shadow: 0 2rpx 8rpx rgba(90, 120, 213, 0.1);
  transform: translateY(0);
  transition: all 0.4s cubic-bezier(0.05, 0.7, 0.1, 1);
  will-change: transform, opacity;
}

.detail-row {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  padding: 10rpx 0;
  position: relative;
  width: 100%;
  flex-wrap: wrap;
  gap: 16rpx;
}

.detail-left {
  display: flex;
  flex-direction: row;
  flex: 1;
  overflow: hidden;
}

.detail-label {
  font-size: 28rpx;
  color: #666;
  margin-right: 8rpx;
}

.detail-value {
  font-size: 28rpx;
  color: #333;
}

/* 区域块样式调整 - 统一协调的背景色 */
.area-block {
  display: flex;
  flex-direction: row;
  padding: 6rpx 10rpx;
  background: transparent; /* 改为透明，以适应整体背景色 */
  border-radius: 8rpx;
  margin-right: 8rpx;
}

/* 移除独特渐变色，保持统一 */
.area-block:nth-child(1),
.area-block:nth-child(2) {
  background: transparent; /* 改为透明，以适应整体背景色 */
  border-color: rgba(180, 170, 255, 0.2);
}

/* 确保所有模式下区域块样式统一 */
.data-item.multi-select-mode .area-block,
.data-item.multi-select-mode .area-block:nth-child(1),
.data-item.multi-select-mode .area-block:nth-child(2) {
  background: transparent; /* 保持透明背景，与周围背景一致 */
  box-shadow: none; /* 移除阴影 */
  border: none; /* 移除边框 */
}

/* 区块颜色差异优化 - 保留不同文字颜色但不使用背景色区分 */
.area-block:nth-child(1) .detail-value {
  color: #4a90e2;
  font-weight: 700;
}

.area-block:nth-child(2) .detail-value {
  color: #34d399;
  font-weight: 700;
}

/* 按钮容器优化 */
.btn-container {
  display: flex;
  gap: 10rpx;
  width: 100%;
  justify-content: flex-end;
  margin-top: 10rpx;
}

/* 按钮通用样式 */
.action-btn {
  margin: 0;
  padding: 0 24rpx !important;
  min-height: 60rpx !important;
  height: 60rpx;
  font-size: 24rpx !important;
  line-height: 60rpx !important;
  font-weight: normal;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  border-radius: 30rpx;
  box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.1);
  position: relative;
  background: linear-gradient(145deg, rgba(250, 250, 255, 0.95), rgba(245, 243, 254, 0.95)) !important;
  flex-shrink: 0;
}

/* 按钮图标 */
.action-btn .btn-icon {
  width: 28rpx;
  height: 28rpx;
  margin-right: 8rpx;
  flex-shrink: 0;
}

/* 主要按钮 - 查看视频/图片 */
.action-btn.primary {
  background: linear-gradient(90deg, #78b9ff 0%, #c58eff 100%) !important;
  color: #fff;
  border: none;
  min-width: 180rpx;
  max-width: none;
  margin-top: 6rpx;
  align-self: flex-end;
  height: 70rpx !important;
  line-height: 70rpx !important;
  font-size: 28rpx !important;
  padding: 0 30rpx !important;
  box-shadow: 0 4rpx 16rpx rgba(90, 120, 213, 0.4);
}

.action-btn-primary-active {
  background: linear-gradient(90deg, #6db0ff 0%, #b57aff 100%) !important;
  transform: scale(0.98);
}

/* 视频按钮 */
.action-btn.video {
  background: linear-gradient(145deg, #eef6ff, #e5eefa) !important;
  color: #4a90e2;
  border: 1px solid rgba(197, 221, 251, 0.8);
  width: 120rpx;
}

.action-btn.video.selected {
  background-color: #4a90e2 !important;
  color: white;
  border-color: #4a90e2;
}

/* 图片按钮 */
.action-btn.image {
  background: linear-gradient(145deg, #eefbf7, #e5f8f2) !important;
  color: #34d399;
  border: 1px solid rgba(197, 240, 230, 0.8);
  width: 120rpx;
}

.action-btn.image.selected {
  background-color: #34d399 !important;
  color: white;
  border-color: #34d399;
}

/* 禁用Button默认边框 */
.action-btn::after {
  border: none;
}

/* 按钮点击效果 */
.action-btn-active {
  transform: scale(0.98);
  opacity: 0.9;
}

/* 小屏幕适配 */
.small-screen .data-item {
  padding: 20rpx 16rpx;
  margin: 16rpx 12rpx;
}

.small-screen .area-block {
  min-width: 160rpx;
}

.small-screen .tc-value {
  font-size: 28rpx;
}

.small-screen .detail-value {
  font-size: 28rpx;
}

.small-screen .action-btn.primary {
  min-width: 140rpx;
  padding: 0 16rpx !important;
  font-size: 22rpx !important;
}

/* 中等屏幕适配 */
.medium-screen .action-btn.video,
.medium-screen .action-btn.image {
  width: 110rpx;
}

/* 大屏幕适配 */
.large-screen .action-btn {
  padding: 0 30rpx !important;
  height: 64rpx;
  min-height: 64rpx !important;
  font-size: 28rpx !important;
  line-height: 64rpx !important;
}

.large-screen .action-btn.primary {
  min-width: 200rpx;
}

.large-screen .action-btn.video,
.large-screen .action-btn.image {
  width: 130rpx;
}

.large-screen .action-btn .btn-icon {
  width: 30rpx;
  height: 30rpx;
  margin-right: 8rpx;
}

/* 参数标签样式 */
.param-tag {
  font-size: 22rpx;
  color: #666;
  background: linear-gradient(145deg, rgba(243, 246, 250, 0.9), rgba(238, 241, 247, 0.9));
  padding: 4rpx 12rpx;
  border-radius: 16rpx;
  border: 1px solid rgba(180, 170, 255, 0.15);
}

.param-count {
  font-size: 22rpx;
  color: #999;
}

/* 分析类型标记 */
.analysis-type-container {
  display: inline-block;
  margin-left: 12rpx;
  perspective: 1000px;
  vertical-align: middle;
  cursor: pointer;
  flex-shrink: 0;
  width: 60rpx;
  height: 36rpx;
}

.analysis-type {
  position: relative;
  width: 100%;
  height: 100%;
  font-size: 22rpx;
  color: #fff;
  transform-style: preserve-3d;
  transition: transform 0.6s cubic-bezier(0.4, 0.0, 0.2, 1);
  transform: rotateX(0deg);
  will-change: transform;
}

.analysis-type.flipping {
  animation: flip-animation 0.6s cubic-bezier(0.4, 0.0, 0.2, 1);
}

@keyframes flip-animation {
  0% { transform: rotateX(0deg); }
  100% { transform: rotateX(180deg); }
}

.analysis-type.temp {
  transform: rotateX(180deg);
}

.type-front, .type-back {
  position: absolute;
  width: 100%;
  height: 100%;
  backface-visibility: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 12rpx;
  font-weight: normal;
}

.type-front {
  background: linear-gradient(90deg, #78b9ff 0%, #c58eff 100%) !important;
  transform: rotateX(0deg);
}

.type-back {
  background: linear-gradient(90deg, #FF9800 0%, #E53935 100%) !important;
  transform: rotateX(180deg);
}

/* 取消按钮样式 */
.cancel-btn {
  font-size: 28rpx;
  color: #fff;
  padding: 12rpx 36rpx;
  border-radius: 32rpx;
  background: linear-gradient(90deg, #c58eff 0%, #78b9ff 100%);
  box-shadow: 0 4rpx 16rpx rgba(90, 120, 213, 0.4);
  transition: all 0.3s ease;
  margin-left: 20rpx;
  flex-shrink: 0;
}

.cancel-btn-active {
  opacity: 0.8;
  transform: scale(0.98);
}

/* 多选模式下的勾选框 - 定位到数据内容区域的右上角 */
.my-checkbox {
  position: absolute;
  right: 20rpx;
  top: 20rpx; /* 固定距离顶部的位置，更准确 */
  width: 40rpx;
  height: 40rpx;
  border-radius: 12rpx; /* 改为圆角矩形，更现代的设计 */
  border: 3rpx solid #e0e0e0;
  background: #fff;
  box-sizing: border-box;
  transition: all 0.4s cubic-bezier(0.05, 0.7, 0.1, 1);
  will-change: transform, opacity, border-color, background;
  z-index: 15; /* 提高z-index确保不被数据组标签覆盖 */
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.isMultiSelectMode .my-checkbox {
  opacity: 1;
  transform: scale(1); /* 移除垂直居中的transform */
  transition: all 0.4s cubic-bezier(0.05, 0.7, 0.1, 1);
}

/* 添加涟漪反馈效果 */
.isMultiSelectMode .my-checkbox::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  background: rgba(74, 144, 226, 0.2);
  border-radius: 50%;
  transform: translate(-50%, -50%);
  opacity: 0;
  z-index: -1;
  transition: all 0.5s cubic-bezier(0.05, 0.7, 0.1, 1);
}

.isMultiSelectMode .my-checkbox:active::before {
  width: 60rpx;
  height: 60rpx;
  opacity: 1;
  transition: all 0.4s cubic-bezier(0.05, 0.7, 0.1, 1);
}

.my-checkbox.checked {
  background: linear-gradient(135deg, #4a90e2, #7c4dff);
  border-color: #4a90e2;
  transition: all 0.4s cubic-bezier(0.05, 0.7, 0.1, 1);
  transform: scale(1.1); /* 移除垂直居中，只保留放大效果 */
  box-shadow: 0 4rpx 12rpx rgba(74, 144, 226, 0.3);
}

.my-checkbox.checked::after {
  content: '';
  transition: all 0.3s cubic-bezier(0.05, 0.7, 0.1, 1) 0.1s;
  position: absolute;
  top: 35%;
  left: 50%;
  transform: translate(-50%, -50%) rotate(45deg);
  width: 8rpx;
  height: 16rpx;
  border-bottom: 4rpx solid #fff;
  border-right: 4rpx solid #fff;
  animation: checkmark-appear 0.3s cubic-bezier(0.05, 0.7, 0.1, 1) forwards;
}

@keyframes checkmark-appear {
  0% {
    opacity: 0;
    transform: translate(-50%, -50%) rotate(45deg) scale(0.5);
  }
  100% {
    opacity: 1;
    transform: translate(-50%, -50%) rotate(45deg) scale(1);
  }
}

/* 调整多选模式下的内容偏移 */
.data-content {
  transition: all 0.4s cubic-bezier(0.05, 0.7, 0.1, 1);
  margin-left: 0;
  padding: 4rpx 0;
  will-change: margin-left, transform;
  width: 100%;
}

.isMultiSelectMode .data-content {
  margin-left: 0; /* 复选框移到右侧后，恢复原始左边距 */
  margin-right: 70rpx; /* 为右侧复选框预留空间 */
  transition: all 0.4s cubic-bezier(0.05, 0.7, 0.1, 1);
  transform: translateX(0);
}

/* 数据项基础样式 - 单选模式 */
.data-item {
  background: linear-gradient(145deg, #f9f8ff 0%, #f3f1fc 100%);
  border-radius: 16rpx;
  padding: 24rpx 20rpx;
  margin: 20rpx 16rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
  position: relative;
  transition: all 0.6s cubic-bezier(0.22, 0.61, 0.36, 1); /* 更平滑的过渡时间和曲线 */
  overflow: visible;
  border-left: 4rpx solid #4a90e2;
  margin-top: 28rpx;
  will-change: transform, opacity;
  opacity: 1;
}

/* 多选模式下数据项样式 */
.data-item.multi-select-mode {
  margin: 20rpx 16rpx;
  margin-top: 28rpx;
  opacity: 0; /* 初始不可见 */
  transform: translateY(20rpx); /* 轻微向下偏移，为动画做准备 */
  transition: opacity 0.7s cubic-bezier(0.22, 0.61, 0.36, 1), 
              transform 0.7s cubic-bezier(0.22, 0.61, 0.36, 1); /* 分开指定属性实现更精细的控制 */
}

/* 显示多选模式下的数据项 */
.animateItems .data-item.multi-select-mode {
  opacity: 1;
  transform: translateY(0);
}

/* 添加逐个出现的延迟效果 - 每个项目延迟50ms，最多10个 */
.animateItems .data-item.multi-select-mode:nth-child(1) {
  transition-delay: 0.05s;
}

.animateItems .data-item.multi-select-mode:nth-child(2) {
  transition-delay: 0.1s;
}

.animateItems .data-item.multi-select-mode:nth-child(3) {
  transition-delay: 0.15s;
}

.animateItems .data-item.multi-select-mode:nth-child(4) {
  transition-delay: 0.2s;
}

.animateItems .data-item.multi-select-mode:nth-child(5) {
  transition-delay: 0.25s;
}

.animateItems .data-item.multi-select-mode:nth-child(6) {
  transition-delay: 0.3s;
}

.animateItems .data-item.multi-select-mode:nth-child(7) {
  transition-delay: 0.35s;
}

.animateItems .data-item.multi-select-mode:nth-child(8) {
  transition-delay: 0.4s;
}

.animateItems .data-item.multi-select-mode:nth-child(9) {
  transition-delay: 0.45s;
}

.animateItems .data-item.multi-select-mode:nth-child(10) {
  transition-delay: 0.5s;
}

/* 数据列表动画效果 */

/* 定义数据项弹出动画 - 优化为包含位移和透明度变化 */
@keyframes item-pop-in {
  from {
    opacity: 0;
    transform: translateY(20rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 添加淡出动画，用于退出多选模式 */
.data-list.mode-transition .data-item {
    opacity: 0;
  transition: opacity 0.6s cubic-bezier(0.22, 0.61, 0.36, 1),
              transform 0.6s cubic-bezier(0.22, 0.61, 0.36, 1);
}

/* 底部按钮淡出动画 - 保留原有效果 */
.data-list.mode-transition ~ .bottom-buttons {
    transform: translateY(100%);
    opacity: 0;
  transition: all 0.7s cubic-bezier(0.22, 0.61, 0.36, 1);
}

/* 添加单选模式下的数据组标签 - 使用更精确的选择器 */
.data-item:not(.multi-select-mode)::before {
  content: "数据组 " attr(data-group);
  position: absolute;
  top: -14rpx;
  left: 24rpx;
  font-size: 24rpx;
  padding: 8rpx 24rpx;
  border-radius: 24rpx;
  z-index: 10;
  letter-spacing: 1rpx;
  background: linear-gradient(90deg, #c58eff 0%, #78b9ff 100%); /* 反向渐变色 */
  color: white;
  min-width: 120rpx;
  text-align: center;
  box-shadow: 0 4rpx 16rpx rgba(90, 120, 213, 0.4);
}

/* 数据内容区域 */
.data-content {
  width: 100%;
}

/* 多选模式下的 header 样式 */
.isMultiSelectMode .data-header {
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  background: linear-gradient(145deg, rgba(246, 244, 255, 0.9), rgba(241, 239, 252, 0.9));
  border-radius: 12rpx;
  border: 1px solid rgba(180, 170, 255, 0.2);
}

/* 多选模式下的标题样式 */
.isMultiSelectMode .header-title {
  flex-grow: 1;
}

/* 多选模式下的取消按钮 */
.isMultiSelectMode .cancel-btn {
  flex-shrink: 0;
}

/* TC值和标签样式 */
.data-main {
  display: flex;
  flex-direction: row;
  align-items: center;
  flex-wrap: wrap;
  width: 100%;
  position: relative;
  padding-bottom: 6rpx;
}

/* TC值样式 */
.tc-value {
  font-size: 32rpx;
  color: #333;
  font-weight: 600;
  max-width: 60%;
  white-space: normal;
  word-break: break-all;
  overflow: visible;
  line-height: 1.3;
  flex-shrink: 1;
  margin-right: 8rpx;
}

/* 时间样式 */
.time-row {
  font-size: 24rpx;
  color: #999;
  margin-left: auto;
  white-space: nowrap;
  flex-shrink: 0;
}

/* 标签样式 */
.analysis-type {
  font-size: 22rpx;
  padding: 4rpx 10rpx;
  border-radius: 6rpx;
  color: #fff;
  margin-left: 8rpx;
  font-weight: normal;
  flex-shrink: 0;
}

.analysis-type.formal {
  background: linear-gradient(90deg, #78b9ff 0%, #c58eff 100%) !important;
}

.analysis-type.temp {
  background: linear-gradient(90deg, #FF9800 0%, #E53935 100%) !important;
}

/* 数据详情区域 */
/* 删除重复样式 */

/* 详情行样式 */
.detail-row {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  padding: 8rpx 0;
  width: 100%;
  flex-wrap: wrap;
  gap: 16rpx;
}

/* 区域容器 */
.detail-left {
  display: flex;
  flex-direction: row;
  flex: 1;
  overflow: hidden;
}

/* 区块样式 */
.area-block {
  display: flex;
  flex-direction: row;
  padding: 6rpx 10rpx;
  background: transparent; /* 从#fafafa改为透明，与周围背景一致 */
  border-radius: 8rpx;
  margin-right: 8rpx;
}

/* 标签样式 */
.detail-label {
  font-size: 28rpx;
  color: #666;
  margin-right: 8rpx;
}

/* 数值样式 */
.detail-value {
  font-size: 28rpx;
  color: #333;
}

/* C区和T区的颜色区分 */
.area-block:nth-child(1) .detail-value {
  color: #4a90e2;
}

.area-block:nth-child(2) .detail-value {
  color: #34d399;
}

/* 统一单选和多选模式下的区域块样式 */
.multi-select-mode .area-block,
.data-item:not(.multi-select-mode) .area-block {
  padding: 6rpx 10rpx;
  background: transparent; /* 从#fafafa改为透明，与周围背景一致 */
  border-radius: 8rpx;
  margin-right: 8rpx;
  align-items: center;
  width: 48%;
  overflow: hidden;
}

/* 统一单选和多选模式下的数值样式 */
.multi-select-mode .detail-value,
.data-item:not(.multi-select-mode) .detail-value {
  min-width: 0;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  word-break: keep-all;
}

/* 数值过长时的特殊处理 - 点击或长按可显示完整内容 */
.detail-value-expanded {
  white-space: normal;
  overflow: visible;
  text-overflow: clip;
  word-break: break-all;
  position: relative;
  z-index: 10;
  background: #f0f0f0;
  box-shadow: 0 0 5rpx rgba(0,0,0,0.1);
}

/* 按钮样式 */
.action-btn.primary {
  margin-left: auto;
  margin-top: 8rpx;
}

/* 参数行样式 */
.param-row {
  margin-top: 12rpx;
  display: flex;
  align-items: center;
  gap: 10rpx;
  background: transparent;
}

/* 媒体选项中的文本 */
.media-option .option-text {
  font-size: 30rpx;
  font-weight: 500;
  position: relative;
  z-index: 1;
  letter-spacing: 8rpx;
  color: #3498db;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.02);
  font-family: "PingFang SC", -apple-system, BlinkMacSystemFont, "Helvetica Neue", Helvetica, Arial, sans-serif;
}

/* 导出选项中的文本 */
.export-option .option-text {
  font-size: 28rpx;
  color: #333;
}

/* 数据组标签样式，与主界面按钮保持一致 */
.data-item.multi-select-mode::before {
  content: "数据组 " attr(data-group);
  position: absolute;
  top: -25rpx;
  left: 24rpx;
  font-size: 24rpx;
  padding: 8rpx 24rpx;
  border-radius: 24rpx;
  z-index: 10;
  letter-spacing: 1rpx;
  background: linear-gradient(90deg, #78b9ff 0%, #c58eff 100%); /* 主界面按钮渐变色 */
  color: white;
  min-width: 120rpx;
  text-align: center;
  box-shadow: 0 4rpx 16rpx rgba(90, 120, 213, 0.4);
  transition: all 0.7s cubic-bezier(0.22, 0.61, 0.36, 1);
  opacity: 0; /* 初始透明 */
  transform: translateY(-5rpx);
}

/* 动画显示时的标签淡入 */
.animateItems .data-item.multi-select-mode::before {
  opacity: 1;
  transform: translateY(0);
  transition-delay: 0.15s; /* 轻微延迟，让标签出现比数据项慢一点 */
}

/* 多选模式下的数据项样式 - 实现上浮和放大效果，增加数据组之间的间距 */
.data-item.multi-select-mode {
  transform: translateY(-8rpx) scale(1.05);
  box-shadow: 0 8rpx 20rpx rgba(0, 0, 0, 0.08);
  z-index: 1; /* 提高z-index，实现空间深度感知 */
  transition: all 0.7s cubic-bezier(0.22, 0.61, 0.36, 1);
  will-change: transform, opacity, box-shadow;
  margin-top: 48rpx; /* 减少顶部间距，但保持组间有一定间隔 */
  margin-bottom: 40rpx; /* 减少底部间距，改善整体布局紧凑性 */
  opacity: 0; /* 初始状态为透明 */
}

/* 退出多选模式时的数据块聚拢效果 */
.data-item:not(.multi-select-mode) {
  transition: all 0.7s cubic-bezier(0.22, 0.61, 0.36, 1);
  transform: translateY(0) scale(1.0);
}

/* 移除不同索引的数据组标签不同颜色的设置，统一使用主界面按钮颜色 */
.data-item.multi-select-mode:nth-child(3n)::before {
  background: linear-gradient(90deg, #78b9ff 0%, #c58eff 100%); /* 修改为主界面按钮渐变色 */
  box-shadow: 0 4rpx 16rpx rgba(90, 120, 213, 0.4);
}

.data-item.multi-select-mode:nth-child(3n+1)::before {
  background: linear-gradient(90deg, #78b9ff 0%, #c58eff 100%); /* 修改为主界面按钮渐变色 */
  box-shadow: 0 4rpx 16rpx rgba(90, 120, 213, 0.4);
}

/* 未选中状态下的按钮样式 */
.data-item:not(.selected-item) .media-btn {
  opacity: 0.5;
  pointer-events: none;
  cursor: not-allowed;
  background-color: #f5f5f5 !important;
  border-color: #ddd;
  color: #999 !important;
  box-shadow: none;
  transition: all 0.4s cubic-bezier(0.05, 0.7, 0.1, 1);
  transform: scale(0.9);
  transform-origin: center center;
  will-change: transform, opacity, background-color, border-color;
}

/* 选中状态下的媒体按钮默认样式 */
.data-item.selected-item .media-btn {
  opacity: 1;
  pointer-events: auto;
  cursor: pointer;
  box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.08);
  transition: all 0.4s cubic-bezier(0.05, 0.7, 0.1, 1);
  transform: scale(1);
  transform-origin: center center;
  will-change: transform, opacity, background-color, border-color;
}

/* 选中状态下的视频按钮 */
.data-item.selected-item .media-btn.video {
  background-color: rgba(74, 144, 226, 0.05) !important;
  color: #4a90e2 !important;
  border-color: rgba(74, 144, 226, 0.3);
}

.data-item.selected-item .media-btn.video.selected {
  background-color: #4a90e2 !important;
  color: white !important;
  border-color: #4a90e2;
}

/* 选中状态下的图片按钮 */
.data-item.selected-item .media-btn.image {
  background-color: rgba(52, 211, 153, 0.05) !important;
  color: #34d399 !important;
  border-color: rgba(52, 211, 153, 0.3);
}

.data-item.selected-item .media-btn.image.selected {
  background-color: #34d399 !important;
  color: white !important;
  border-color: #34d399;
}

/* 返回按钮 */
.back-btn {
  width: 90%;
  height: 90rpx;
  line-height: 90rpx;
  text-align: center;
  border-radius: 45rpx;
  font-size: 32rpx;
  color: #fff;
  background: linear-gradient(90deg, #78b9ff 0%, #c58eff 100%);
  margin: 0 auto;
  position: relative;
  transition: transform 0.3s ease, opacity 0.3s ease;
  box-shadow: 0 4rpx 16rpx rgba(90, 120, 213, 0.4);
}

.back-btn:active {
  transform: translateY(2rpx);
  box-shadow: 0 2rpx 6rpx rgba(90, 120, 213, 0.2);
  opacity: 0.9;
}

/* 底部按钮区域 - 基础样式 */
.bottom-buttons {
  width: 100%;
  z-index: 1500;
  display: flex;
  justify-content: center;
  align-items: center;
  box-sizing: border-box;
}

/* 单选模式下的底部按钮区域样式 */
.bottom-buttons:not(.multi-select-mode) {
  position: fixed;
  bottom: calc(110rpx + env(safe-area-inset-bottom)); /* 直接定位在底部导航栏上方 */
  left: 0;
  right: 0;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 30rpx 0;
  z-index: 1500;
  background: rgba(246, 244, 255, 0.85); /* 半透明浅紫色背景，与整体风格协调 */
  backdrop-filter: blur(10rpx); /* 添加模糊效果增强视觉融合 */
  width: 100%;
  border-radius: 0;
  box-shadow: 0 -2rpx 10rpx rgba(90, 120, 213, 0.08); /* 淡化阴影 */
  border-top: 1rpx solid rgba(180, 170, 255, 0.15); /* 淡色边框 */
  border-bottom: none;
  transition: all 0.3s ease;
}

/* 这里有样式冲突已处理 */

/* 底部按钮淡入动画 */
/* 底部按钮动画已统一到.isMultiSelectMode .bottom-buttons.multi-select-mode中 */

/* 按钮组样式 - 确保与单选模式一致 */
.btn-group {
  display: flex;
  justify-content: center; /* 居中对齐，与单选模式一致 */
  align-items: center;
  width: 90%; /* 与单选模式返回按钮宽度一致 */
  margin: 0 auto; /* 居中显示 */
  gap: 15rpx; /* 使用固定间距 */
}

/* 删除、导出和生成图表按钮的基础样式 */
.delete-btn, .export-btn, .generate-btn {
  flex: 1;
  min-width: 180rpx;
  height: 90rpx;
  line-height: 90rpx;
  transition: all 0.4s cubic-bezier(0.05, 0.7, 0.1, 1);
  transform: scale(1);
  transform-origin: center center;
  will-change: transform, opacity, background-color;
  text-align: center;
  border-radius: 45rpx;
  font-size: 30rpx;
  color: #fff;
  letter-spacing: 2rpx;
  font-weight: 500;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  position: relative;
  overflow: hidden;
  box-shadow: 0 6rpx 20rpx rgba(0, 0, 0, 0.2);
}

/* 删除按钮样式 */
.delete-btn {
  /* 通用样式已在.btn-group .delete-btn中定义 */
  background: linear-gradient(90deg, #c58eff 0%, #78b9ff 100%);
  box-shadow: 0 4rpx 16rpx rgba(90, 120, 213, 0.4);
}

/* 添加高光效果以增强视觉一致性 */
.delete-btn::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 50%;
  background: linear-gradient(to bottom, rgba(255, 255, 255, 0.3), rgba(255, 255, 255, 0));
  border-radius: 45rpx 45rpx 0 0;
}

/* 导出按钮样式 */
.export-btn {
  /* 通用样式已在.btn-group .export-btn中定义 */
  background: linear-gradient(90deg, #c58eff 0%, #78b9ff 100%);
  box-shadow: 0 4rpx 16rpx rgba(90, 120, 213, 0.4);
}

/* 生成图表按钮样式 */
.generate-btn {
  flex: 1;
  min-width: 180rpx;
  height: 90rpx;
  line-height: 90rpx;
  text-align: center;
  border-radius: 45rpx;
  font-size: 30rpx;
  color: #fff;
  letter-spacing: 2rpx;
  font-weight: 500;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  position: relative;
  overflow: hidden;
  /* 修改为取消按钮的紫色渐变 */
  background: linear-gradient(90deg, #8E54E9, #4776E6);
  box-shadow: 0 4rpx 12rpx rgba(142, 84, 233, 0.3);
}

/* 返回按钮样式 */
.back-btn {
  width: 90%;
  height: 88rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  color: #fff;
  font-size: 32rpx;
  font-weight: 500;
  letter-spacing: 4rpx;
  background: linear-gradient(90deg, #78b9ff 0%, #c58eff 100%);
  border-radius: 44rpx;
  box-shadow: 0 4rpx 12rpx rgba(90, 120, 213, 0.3);
  position: relative;
  overflow: hidden;
  transition: all 0.3s ease;
}

/* 添加按钮内部高光效果 */
.back-btn::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(180deg, rgba(255, 255, 255, 0.3), rgba(255, 255, 255, 0));
  z-index: 1;
  border-radius: 40rpx;
}

/* 所有按钮的点击效果 */
.back-btn:active, .delete-btn:active, .export-btn:active, .generate-btn:active {
  transform: translateY(2rpx) scale(0.98);
  box-shadow: 0 2rpx 6rpx rgba(90, 120, 213, 0.2);
  opacity: 0.9;
}

/* 已禁用按钮悬停效果，避免颜色滞留问题 */
/*
.button-hover {
  opacity: 0.8;
}
*/
/* 个人页面样式 */
/* 性能优化总结:
 * 1. 硬件加速相关属性:
 *    - will-change: transform, opacity
 *    - backface-visibility: hidden
 *    - perspective: 1000
 *    - transform: translateZ(0)
 *    - transform-style: preserve-3d
 *
 * 2. 动画性能优化:
 *    - 使用transform和opacity进行动画而不是all
 *    - GPU加速相关属性
 *    - 优化贝塞尔曲线
 *    - 3D变换相关属性促使GPU渲染
 *
 * 3. 微信小程序不规范选择器修复:
 *    - 替换page标签选择器为.page-container类选择器
 *    - 替换:active伪类为对应的active类(如.button-active)
 *    - 移除::-webkit-scrollbar等伪元素选择器
 *    - 替换媒体查询为对应的类选择器(.small-screen, .medium-screen)
 */

/* 页面根元素容器类 */
.page-container {
  background-color: #f7f8fa;
  background: #f7f8fa;
  z-index: 999;
  display: flex;
  flex-direction: column;
}

/* 按钮样式 - 移除按钮伪类选择器 */
.action-btn-no-border {
  border: none;
}

/* 用户信息点击状态类 */
.userinfo-btn-active {
  transform: scale(0.98);
  background: rgba(255, 255, 255, 0.3);
}

/* 按钮点击状态类 */
.action-btn-pressed {
  transform: scale(0.98);
  opacity: 0.9;
}

/* 添加按钮点击效果类 */
.back-btn-active {
  transform: translateY(2rpx);
  box-shadow: 0 2rpx 6rpx rgba(90, 120, 213, 0.2);
  opacity: 0.9;
}

.button-hover-class {
  opacity: 0.8;
}

.container {
  background-color: #f7f8fa;
  padding-bottom: calc(110rpx + env(safe-area-inset-bottom));
  /* padding-top通过内联样式动态设置 */
}

/* 用户信息部分 */
.user-section {
  background: linear-gradient(135deg, #4a90e2, #7c4dff);
  padding: 40rpx 0 105rpx 0; /* 调整底部padding为105rpx，让蓝紫色背景向下延伸 */
  position: relative;
  overflow: hidden;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
}

.user-section::after {
  content: '';
  position: absolute;
  bottom: -60%;
  left: -10%;
  width: 120%;
  height: 200rpx;
  background: rgba(255, 255, 255, 0.1);
  transform: rotate(-5deg);
}

.userinfo {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 30rpx;
  position: relative;
  z-index: 1;
  transition: all 0.3s ease;
}

.avatar-wrapper {
  padding: 0;
  width: 160rpx;
  height: 160rpx;
  border-radius: 50%;
  margin-bottom: 10rpx;
  background-color: transparent;
}

.avatar-wrapper::after {
  border: none;
}

.userinfo-avatar {
  width: 160rpx;
  height: 160rpx;
  border-radius: 50%;
  background-size: cover;
  background-color: white;
}

.login-btn, .logout-btn {
  width: 240rpx;
  height: 80rpx;
  margin-top: 24rpx;
  font-size: 28rpx;
  padding: 0;
  line-height: 80rpx;
  text-align: center;
  color: #fff;
  border-radius: 40rpx;
  transition: all 0.3s ease;
}

.login-btn {
  background: rgba(255, 255, 255, 0.2);
  border: 2rpx solid rgba(255, 255, 255, 0.5);
}

.logout-btn {
  background: linear-gradient(90deg, #6ba8f7, #a585ff);
  border: 2rpx solid rgba(255, 255, 255, 0.8);
  box-shadow: 0 4rpx 12rpx rgba(107, 168, 247, 0.3);
}

.login-btn-active, .logout-btn-active {
  transform: scale(0.98);
  background: rgba(255, 255, 255, 0.3);
}

.userinfo-nickname {
  color: #fff;
  font-size: 34rpx;
  margin-top: 24rpx;
  font-weight: 500;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
  background: transparent;
  text-align: center;
  width: 100%;
}

.userinfo-nickname::placeholder {
  color: rgba(255, 255, 255, 0.8);
}

.space-line {
  height: 16rpx;
  background-color: transparent;
}

.menu-list {
  padding: 20rpx;
}

.weui-cells {
  border-radius: 16rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.05);
  overflow: hidden;
  margin: 0;
}

.weui-cell {
  padding: 32rpx;
  position: relative;
  display: flex;
  align-items: center;
  background: #fff;
  border-bottom: 1rpx solid rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
}

.weui-cell-active {
  background: #f9f9f9;
}

.weui-cell-last {
  border-bottom: none;
}

.weui-cell__hd {
  margin-right: 24rpx;
  width: 48rpx;
  height: 48rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.weui-cell__hd .cell-image {
  width: 40rpx;
  height: 40rpx;
  opacity: 0.9;
}

.weui-cell__bd {
  flex: 1;
  font-size: 30rpx;
  color: #333;
  font-weight: 500;
}

.weui-cell__ft {
  color: #bbb;
  font-size: 24rpx;
}

.weui-cell__ft_in-access::after {
  content: " ";
  display: inline-block;
  height: 12rpx;
  width: 12rpx;
  border-width: 2rpx 2rpx 0 0;
  border-color: #bbb;
  border-style: solid;
  transform: matrix(0.71, 0.71, -0.71, 0.71, 0, 0);
  margin-left: 16rpx;
}

.badge {
  position: absolute;
  top: 50%;
  right: 80rpx;
  transform: translateY(-50%);
  background: #ff4d4f;
  color: #fff;
  font-size: 22rpx;
  width: 32rpx;
  height: 32rpx;
  line-height: 32rpx;
  text-align: center;
  border-radius: 50%;
  font-weight: bold;
  box-shadow: 0 2rpx 8rpx rgba(255, 77, 79, 0.3);
}

/* 数据显示模式 - 支持动态适配导航栏高度 */
.data-display {
  position: fixed;
  /* top和height通过内联样式动态设置 */
  left: 0;
  width: 100%;
  background: linear-gradient(160deg, #f3f1fc 0%, #eceafc 50%, #e8e5fb 100%);
  z-index: 999;
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
  will-change: transform, opacity; /* 提高动画性能 */
}

/* 添加背景遮罩层 - 用于模式切换时的深度感知 */
.data-display::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0);
  z-index: 0;
  pointer-events: none;
  transition: background-color 0.15s cubic-bezier(0.05, 0.7, 0.1, 1);
  will-change: background-color;
}

/* 多选模式下的背景遮罩层 - 40%透明度 */
.data-display.isMultiSelectMode::before {
  background-color: rgba(0, 0, 0, 0.4);
  z-index: 0;
}

/* 返回按钮容器 - 单选模式专用，确保位于底部且可见 */
.bottom-buttons:not(.multi-select-mode) {
  position: fixed;
  bottom: calc(110rpx + env(safe-area-inset-bottom)); /* 直接定位在底部导航栏上方 */
  left: 0;
  right: 0;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 30rpx 0;
  z-index: 1500;
  background: rgba(246, 244, 255, 0.85); /* 半透明浅紫色背景，与整体风格协调 */
  backdrop-filter: blur(10rpx); /* 添加模糊效果增强视觉融合 */
  width: 100%;
  border-radius: 0;
  box-shadow: 0 -2rpx 10rpx rgba(90, 120, 213, 0.08); /* 淡化阴影 */
  border-top: 1rpx solid rgba(180, 170, 255, 0.15); /* 淡色边框 */
  border-bottom: none;
  transition: all 0.3s ease;
}

/* 返回按钮样式 */
.back-btn {
  width: 90%;
  height: 88rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  color: #fff;
  font-size: 32rpx;
  font-weight: 500;
  letter-spacing: 4rpx;
  background: linear-gradient(90deg, #78b9ff 0%, #c58eff 100%);
  border-radius: 44rpx;
  box-shadow: 0 4rpx 12rpx rgba(90, 120, 213, 0.3);
  z-index: 1501;
  position: relative;
  overflow: hidden;
  transition: all 0.3s ease;
}

/* 添加按钮内部高光效果 */
.back-btn::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(180deg, rgba(255, 255, 255, 0.3), rgba(255, 255, 255, 0));
  z-index: 1;
  border-radius: 40rpx;
}

/* 添加按钮点击效果 */
.back-btn:active {
  transform: translateY(2rpx);
  box-shadow: 0 2rpx 6rpx rgba(90, 120, 213, 0.2);
  opacity: 0.9;
}

.data-header {
  padding: 30rpx 30rpx 25rpx 30rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  background: linear-gradient(145deg, rgba(246, 244, 255, 0.9), rgba(241, 239, 252, 0.9));
  box-shadow: 0 2rpx 8rpx rgba(90, 120, 213, 0.1);
  flex-shrink: 0;
  border-radius: 12rpx;
  border: 1px solid rgba(180, 170, 255, 0.2);
  transition: all 0.4s cubic-bezier(0.05, 0.7, 0.1, 1);
  transform: translateY(0);
  opacity: 1;
  will-change: transform, opacity, height;
}

/* 模式过渡状态下的数据头部 */
.mode-transition .data-header {
  opacity: 0;
  transform: translateY(-40rpx);
  transition: all 0.6s cubic-bezier(0.22, 0.61, 0.36, 1);
}

/* 标题动画 */
.header-title {
  font-size: 40rpx;
  font-weight: 600;
  color: #303133;
  line-height: 1.3;
  flex-shrink: 0;
  position: static;
  letter-spacing: normal;
  text-shadow: none;
  padding-left: 0;
  border-left: none;
  display: flex;
  align-items: center;
  width: auto;
  transition: all 0.5s cubic-bezier(0.22, 0.61, 0.36, 1);
  animation: title-fade-in 0.5s cubic-bezier(0.05, 0.7, 0.1, 1) forwards;
}

@keyframes title-fade-in {
  0% {
    opacity: 0.7;
    transform: translateY(-10rpx);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 多选模式下的标题样式 */
.isMultiSelectMode .header-title {
  color: #4a90e2;
  transform: scale(1.05);
  transition: all 0.5s cubic-bezier(0.22, 0.61, 0.36, 1);
}

/* 非多选模式：搜索框容器 */
.search-container {
  display: flex;
  align-items: center;
  margin-top: 20rpx;
  background: rgba(255, 255, 255, 0.6);
  border-radius: 32rpx;
  padding: 8rpx 24rpx;
  width: 95%;
  border: 1rpx solid rgba(180, 170, 255, 0.2);
  box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.05);
  transition: all 0.6s cubic-bezier(0.22, 0.61, 0.36, 1);
  transform-origin: center;
  will-change: transform, opacity;
  
  /* 添加搜索框动画初始状态 */
  opacity: 0;
  transform: translateY(-20rpx) scale(0.95);
  animation: search-slide-in 0.6s cubic-bezier(0.05, 0.7, 0.1, 1) forwards;
  animation-delay: 0.15s; /* 稍微延迟以便在标题转换后开始 */
}

/* 搜索框出现的动画 */
@keyframes search-slide-in {
  0% {
    opacity: 0;
    transform: translateY(-20rpx) scale(0.95);
  }
  100% {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

/* 模式过渡中的搜索框容器 */
.mode-transition .search-container {
  opacity: 0;
  transform: translateY(15rpx) scale(0.95);
  transition: all 0.6s cubic-bezier(0.22, 0.61, 0.36, 1);
  animation: none; /* 过渡期间禁用动画 */
}

/* 搜索输入框 */
.search-input {
  flex: 1;
  height: 64rpx;
  font-size: 28rpx;
  color: #333;
  padding: 0 10rpx;
  transition: all 0.25s cubic-bezier(0.05, 0.7, 0.1, 1);
}

/* 搜索输入框聚焦效果 */
.search-input-focused {
  background: rgba(255, 255, 255, 0.85);
  box-shadow: 0 2rpx 8rpx rgba(90, 120, 213, 0.15);
}

/* 多选模式：图表按钮容器 */
.cancel-btn-container {
  margin-top: 20rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  transition: all 0.4s cubic-bezier(0.05, 0.7, 0.1, 1);
  transform: translateY(0);
  opacity: 1; 
  will-change: transform, opacity;
  animation: fade-slide-down 0.45s cubic-bezier(0.05, 0.7, 0.1, 1);
}

@keyframes fade-slide-down {
  from {
    opacity: 0;
    transform: translateY(-20rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 图表生成按钮 */
.generate-btn {
  background: linear-gradient(90deg, #4A90E2, #5B86E5);
  color: #fff;
  font-size: 28rpx;
  padding: 12rpx 36rpx;
  border-radius: 32rpx;
  box-shadow: 0 4rpx 12rpx rgba(90, 120, 213, 0.3);
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  will-change: transform, box-shadow;
}

.generate-btn:active {
  transform: scale(0.97);
  box-shadow: 0 2rpx 6rpx rgba(90, 120, 213, 0.2);
}

/* 模式过渡中的底部按钮 */
.mode-transition ~ .bottom-buttons {
  transform: translateY(100%);
  opacity: 0;
  transition: all 0.7s cubic-bezier(0.22, 0.61, 0.36, 1);
}

/* 底部按钮容器 - 多选模式 */
.bottom-buttons.multi-select-mode {
  position: fixed;
  bottom: calc(110rpx + env(safe-area-inset-bottom));
  left: 0;
  right: 0;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 30rpx;
  background: rgba(246, 244, 255, 0.95);
  backdrop-filter: blur(10rpx);
  box-shadow: 0 -2rpx 10rpx rgba(90, 120, 213, 0.1);
  border-top: 1rpx solid rgba(180, 170, 255, 0.15);
  z-index: 1500;
}

/* 底部按钮的动画效果 */
.bottom-buttons.multi-select-mode {
  transform: translateY(0);
  opacity: 1;
  transition: transform 0.7s cubic-bezier(0.22, 0.61, 0.36, 1),
              opacity 0.7s cubic-bezier(0.22, 0.61, 0.36, 1);
  will-change: transform, opacity;
}

/* 模式过渡中的底部按钮 - 消失动画 */
.mode-transition ~ .bottom-buttons.multi-select-mode {
  transform: translateY(100%);
  opacity: 0;
  transition: transform 0.7s cubic-bezier(0.22, 0.61, 0.36, 1),
              opacity 0.7s cubic-bezier(0.22, 0.61, 0.36, 1);
  z-index: 1500;
}

/* 单选模式下的按钮组 */
.single-mode-buttons {
  display: flex;
  width: 100%;
  justify-content: space-between;
  gap: 20rpx;
}

/* 返回按钮样式优化 */
.back-btn {
  width: 70%;
  height: 88rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  color: #fff;
  font-size: 32rpx;
  font-weight: 500;
  letter-spacing: 4rpx;
  background: linear-gradient(90deg, #78b9ff 0%, #c58eff 100%);
  border-radius: 44rpx;
  box-shadow: 0 4rpx 12rpx rgba(90, 120, 213, 0.3);
  position: relative;
  overflow: hidden;
  transition: all 0.3s ease;
}

/* 添加选择按钮 */
.select-btn {
  width: 30%;
  height: 88rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  color: #4A90E2;
  font-size: 32rpx;
  font-weight: 500;
  background: rgba(255, 255, 255, 0.8);
  border: 1rpx solid rgba(74, 144, 226, 0.4);
  border-radius: 44rpx;
  box-shadow: 0 4rpx 12rpx rgba(90, 120, 213, 0.15);
  transition: all 0.3s ease;
}

.select-btn:active {
  transform: translateY(2rpx);
  box-shadow: 0 2rpx 6rpx rgba(90, 120, 213, 0.1);
  opacity: 0.9;
}

/* 多选模式按钮组 */
.btn-group {
  display: flex;
  width: 100%;
  justify-content: space-between;
}

/* 等宽按钮 */
.equal-width-btn {
  flex: 1;
  max-width: 31%;
  height: 80rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 28rpx;
  font-weight: 500;
  border-radius: 40rpx;
  transition: all 0.3s ease;
}

/* 删除按钮 */
.delete-btn {
  background: linear-gradient(90deg, #FF5252, #FF1744);
  color: #fff;
  box-shadow: 0 4rpx 12rpx rgba(255, 23, 68, 0.3);
}

/* 导出按钮 */
.export-btn {
  background: linear-gradient(90deg, #26C6DA, #00ACC1);
  color: #fff;
  box-shadow: 0 4rpx 12rpx rgba(0, 172, 193, 0.3);
}

/* 取消按钮 */
.cancel-btn {
  background: linear-gradient(90deg, #BDBDBD, #9E9E9E);
  color: #fff;
  box-shadow: 0 4rpx 12rpx rgba(189, 189, 189, 0.3);
}

/* 按钮悬停/点击效果 */
/* 已禁用按钮悬停效果，避免颜色滞留问题 */
/*
.button-hover {
  transform: translateY(2rpx) scale(0.98);
  box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.15);
  opacity: 0.9;
}
*/

/* 底部空间占位符 - 解决滚动视图底部遮挡问题 */
.bottom-space-holder {
  height: 0;
  transition: height 0.4s cubic-bezier(0.05, 0.7, 0.1, 1);
}

.bottom-space-holder.active {
  height: 180rpx;
}

/* 数据列表过渡动画优化 */
.data-list {
  flex: 1;
  padding: 0 20rpx;
  box-sizing: border-box;
  transition: opacity 0.7s cubic-bezier(0.22, 0.61, 0.36, 1);
}

.data-list.mode-transition {
  opacity: 0.5;
  transform: scale(0.98);
  transition: all 0.4s cubic-bezier(0.05, 0.7, 0.1, 1);
}

.data-main {
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  align-items: center;
  justify-content: flex-start;
  width: 100%;
  position: relative;
  padding-right: 16rpx;
}

/* TC值样式优化 */
.tc-value {
  font-size: 30rpx;
  font-weight: 500;
  color: #333;
  max-width: 65%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* 标签容器样式 */
.analysis-type-container {
  margin-left: 10rpx;
  align-self: center;
  position: absolute;
  right: 0;
  top: 0;
  cursor: pointer;
  width: 80rpx;
  height: 40rpx;
  perspective: 800rpx;
  z-index: 5;
}

/* 标签样式 */
.analysis-type {
  position: relative;
  width: 100%;
  height: 100%;
  transition: transform 0.6s;
  transform-style: preserve-3d;
  transform-origin: center center;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
  border-radius: 6rpx;
}

/* 翻转效果 */
.analysis-type.flipping {
  transform: rotateX(180deg);
  transition: transform 0.6s cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

/* 标签正面和背面共享样式 */
.type-front, .type-back {
  position: absolute;
  width: 100%;
  height: 100%;
  backface-visibility: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 22rpx;
  font-weight: normal;
  color: #fff;
  border-radius: 6rpx;
  box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.1);
}

.type-front {
  background: linear-gradient(90deg, #78b9ff 0%, #c58eff 100%) !important;
  transform: rotateX(0deg);
}

.type-back {
  background: linear-gradient(90deg, #FF9800 0%, #E53935 100%) !important;
  transform: rotateX(180deg);
}

/* 时间行样式调整 */
.time-row {
  font-size: 24rpx;
  color: #999;
  text-align: right;
  margin-top: 12rpx;
  width: 100%;
  display: block;
}

/* 详情区域背景 */
.data-details {
  background: linear-gradient(145deg, rgba(246, 244, 255, 0.9), rgba(241, 239, 252, 0.9));
  border-radius: 12rpx;
  padding: 16rpx;
  margin-top: 10rpx;
  border: 1px solid rgba(180, 170, 255, 0.2);
  box-shadow: 0 2rpx 8rpx rgba(90, 120, 213, 0.1);
  transform: translateY(0);
  transition: all 0.4s cubic-bezier(0.05, 0.7, 0.1, 1);
  will-change: transform, opacity;
}

.detail-row {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  padding: 10rpx 0;
  position: relative;
  width: 100%;
  flex-wrap: wrap;
  gap: 16rpx;
}

.detail-left {
  display: flex;
  flex-direction: row;
  flex: 1;
  overflow: hidden;
}

.detail-label {
  font-size: 28rpx;
  color: #666;
  margin-right: 8rpx;
}

.detail-value {
  font-size: 28rpx;
  color: #333;
}

/* 区域块样式调整 - 统一协调的背景色 */
.area-block {
  display: flex;
  flex-direction: row;
  padding: 6rpx 10rpx;
  background: transparent; /* 改为透明，以适应整体背景色 */
  border-radius: 8rpx;
  margin-right: 8rpx;
}

/* 移除独特渐变色，保持统一 */
.area-block:nth-child(1),
.area-block:nth-child(2) {
  background: transparent; /* 改为透明，以适应整体背景色 */
  border-color: rgba(180, 170, 255, 0.2);
}

/* 确保所有模式下区域块样式统一 */
.data-item.multi-select-mode .area-block,
.data-item.multi-select-mode .area-block:nth-child(1),
.data-item.multi-select-mode .area-block:nth-child(2) {
  background: transparent; /* 保持透明背景，与周围背景一致 */
  box-shadow: none; /* 移除阴影 */
  border: none; /* 移除边框 */
}

/* 区块颜色差异优化 - 保留不同文字颜色但不使用背景色区分 */
.area-block:nth-child(1) .detail-value {
  color: #4a90e2;
  font-weight: 700;
}

.area-block:nth-child(2) .detail-value {
  color: #34d399;
  font-weight: 700;
}

/* 按钮容器优化 */
.btn-container {
  display: flex;
  gap: 10rpx;
  width: 100%;
  justify-content: flex-end;
  margin-top: 10rpx;
}

/* 按钮通用样式 */
.action-btn {
  margin: 0;
  padding: 0 24rpx !important;
  min-height: 60rpx !important;
  height: 60rpx;
  font-size: 24rpx !important;
  line-height: 60rpx !important;
  font-weight: normal;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  border-radius: 30rpx;
  box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.1);
  position: relative;
  background: linear-gradient(145deg, rgba(250, 250, 255, 0.95), rgba(245, 243, 254, 0.95)) !important;
  flex-shrink: 0;
}

/* 按钮图标 */
.action-btn .btn-icon {
  width: 28rpx;
  height: 28rpx;
  margin-right: 8rpx;
  flex-shrink: 0;
}

/* 主要按钮 - 查看视频/图片 */
.action-btn.primary {
  background: linear-gradient(90deg, #78b9ff 0%, #c58eff 100%) !important;
  color: #fff;
  border: none;
  min-width: 180rpx;
  max-width: none;
  margin-top: 6rpx;
  align-self: flex-end;
  height: 70rpx !important;
  line-height: 70rpx !important;
  font-size: 28rpx !important;
  padding: 0 30rpx !important;
  box-shadow: 0 4rpx 16rpx rgba(90, 120, 213, 0.4);
}

.action-btn-primary-active {
  background: linear-gradient(90deg, #6db0ff 0%, #b57aff 100%) !important;
  transform: scale(0.98);
}

/* 视频按钮 */
.action-btn.video {
  background: linear-gradient(145deg, #eef6ff, #e5eefa) !important;
  color: #4a90e2;
  border: 1px solid rgba(197, 221, 251, 0.8);
  width: 120rpx;
}

.action-btn.video.selected {
  background-color: #4a90e2 !important;
  color: white;
  border-color: #4a90e2;
}

/* 图片按钮 */
.action-btn.image {
  background: linear-gradient(145deg, #eefbf7, #e5f8f2) !important;
  color: #34d399;
  border: 1px solid rgba(197, 240, 230, 0.8);
  width: 120rpx;
}

.action-btn.image.selected {
  background-color: #34d399 !important;
  color: white;
  border-color: #34d399;
}

/* 禁用Button默认边框 */
.action-btn::after {
  border: none;
}

/* 按钮点击效果 */
.action-btn-active {
  transform: scale(0.98);
  opacity: 0.9;
}

/* 小屏幕适配 */
.small-screen .data-item {
  padding: 20rpx 16rpx;
  margin: 16rpx 12rpx;
}

.small-screen .area-block {
  min-width: 160rpx;
}

.small-screen .tc-value {
  font-size: 28rpx;
}

.small-screen .detail-value {
  font-size: 28rpx;
}

.small-screen .action-btn.primary {
  min-width: 140rpx;
  padding: 0 16rpx !important;
  font-size: 22rpx !important;
}

/* 中等屏幕适配 */
.medium-screen .action-btn.video,
.medium-screen .action-btn.image {
  width: 110rpx;
}

/* 大屏幕适配 */
.large-screen .action-btn {
  padding: 0 30rpx !important;
  height: 64rpx;
  min-height: 64rpx !important;
  font-size: 28rpx !important;
  line-height: 64rpx !important;
}

.large-screen .action-btn.primary {
  min-width: 200rpx;
}

.large-screen .action-btn.video,
.large-screen .action-btn.image {
  width: 130rpx;
}

.large-screen .action-btn .btn-icon {
  width: 30rpx;
  height: 30rpx;
  margin-right: 8rpx;
}

/* 参数标签样式 */
.param-tag {
  font-size: 22rpx;
  color: #666;
  background: linear-gradient(145deg, rgba(243, 246, 250, 0.9), rgba(238, 241, 247, 0.9));
  padding: 4rpx 12rpx;
  border-radius: 16rpx;
  border: 1px solid rgba(180, 170, 255, 0.15);
}

.param-count {
  font-size: 22rpx;
  color: #999;
}

/* 分析类型标记 */
.analysis-type-container {
  display: inline-block;
  margin-left: 12rpx;
  perspective: 1000px;
  vertical-align: middle;
  cursor: pointer;
  flex-shrink: 0;
  width: 60rpx;
  height: 36rpx;
}

.analysis-type {
  position: relative;
  width: 100%;
  height: 100%;
  font-size: 22rpx;
  color: #fff;
  transform-style: preserve-3d;
  transition: transform 0.6s cubic-bezier(0.4, 0.0, 0.2, 1);
  transform: rotateX(0deg);
  will-change: transform;
}

.analysis-type.flipping {
  animation: flip-animation 0.6s cubic-bezier(0.4, 0.0, 0.2, 1);
}

@keyframes flip-animation {
  0% { transform: rotateX(0deg); }
  100% { transform: rotateX(180deg); }
}

.analysis-type.temp {
  transform: rotateX(180deg);
}

.type-front, .type-back {
  position: absolute;
  width: 100%;
  height: 100%;
  backface-visibility: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 12rpx;
  font-weight: normal;
}

.type-front {
  background: linear-gradient(90deg, #78b9ff 0%, #c58eff 100%) !important;
  transform: rotateX(0deg);
}

.type-back {
  background: linear-gradient(90deg, #FF9800 0%, #E53935 100%) !important;
  transform: rotateX(180deg);
}

/* 取消按钮样式 */
.cancel-btn {
  font-size: 28rpx;
  color: #fff;
  padding: 12rpx 36rpx;
  border-radius: 32rpx;
  background: linear-gradient(90deg, #c58eff 0%, #78b9ff 100%);
  box-shadow: 0 4rpx 16rpx rgba(90, 120, 213, 0.4);
  transition: all 0.3s ease;
  margin-left: 20rpx;
  flex-shrink: 0;
}

.cancel-btn-active {
  opacity: 0.8;
  transform: scale(0.98);
}

/* 多选模式下的勾选框 - 定位到数据内容区域的右上角 */
.my-checkbox {
  position: absolute;
  right: 20rpx;
  top: 20rpx; /* 固定距离顶部的位置，更准确 */
  width: 40rpx;
  height: 40rpx;
  border-radius: 12rpx; /* 改为圆角矩形，更现代的设计 */
  border: 3rpx solid #e0e0e0;
  background: #fff;
  box-sizing: border-box;
  transition: all 0.4s cubic-bezier(0.05, 0.7, 0.1, 1);
  will-change: transform, opacity, border-color, background;
  z-index: 15; /* 提高z-index确保不被数据组标签覆盖 */
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.isMultiSelectMode .my-checkbox {
  opacity: 1;
  transform: scale(1); /* 移除垂直居中的transform */
  transition: all 0.4s cubic-bezier(0.05, 0.7, 0.1, 1);
}

/* 添加涟漪反馈效果 */
.isMultiSelectMode .my-checkbox::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  background: rgba(74, 144, 226, 0.2);
  border-radius: 50%;
  transform: translate(-50%, -50%);
  opacity: 0;
  z-index: -1;
  transition: all 0.5s cubic-bezier(0.05, 0.7, 0.1, 1);
}

.isMultiSelectMode .my-checkbox:active::before {
  width: 60rpx;
  height: 60rpx;
  opacity: 1;
  transition: all 0.4s cubic-bezier(0.05, 0.7, 0.1, 1);
}

.my-checkbox.checked {
  background: linear-gradient(135deg, #4a90e2, #7c4dff);
  border-color: #4a90e2;
  transition: all 0.4s cubic-bezier(0.05, 0.7, 0.1, 1);
  transform: scale(1.1); /* 移除垂直居中，只保留放大效果 */
  box-shadow: 0 4rpx 12rpx rgba(74, 144, 226, 0.3);
}

.my-checkbox.checked::after {
  content: '';
  transition: all 0.3s cubic-bezier(0.05, 0.7, 0.1, 1) 0.1s;
  position: absolute;
  top: 35%;
  left: 50%;
  transform: translate(-50%, -50%) rotate(45deg);
  width: 8rpx;
  height: 16rpx;
  border-bottom: 4rpx solid #fff;
  border-right: 4rpx solid #fff;
  animation: checkmark-appear 0.3s cubic-bezier(0.05, 0.7, 0.1, 1) forwards;
}

@keyframes checkmark-appear {
  0% {
    opacity: 0;
    transform: translate(-50%, -50%) rotate(45deg) scale(0.5);
  }
  100% {
    opacity: 1;
    transform: translate(-50%, -50%) rotate(45deg) scale(1);
  }
}

/* 调整多选模式下的内容偏移 */
.data-content {
  transition: all 0.4s cubic-bezier(0.05, 0.7, 0.1, 1);
  margin-left: 0;
  padding: 4rpx 0;
  will-change: margin-left, transform;
  width: 100%;
}

.isMultiSelectMode .data-content {
  margin-left: 0; /* 复选框移到右侧后，恢复原始左边距 */
  margin-right: 70rpx; /* 为右侧复选框预留空间 */
  transition: all 0.4s cubic-bezier(0.05, 0.7, 0.1, 1);
  transform: translateX(0);
}

/* 数据项基础样式 - 单选模式 */
.data-item {
  background: linear-gradient(145deg, #f9f8ff 0%, #f3f1fc 100%);
  border-radius: 16rpx;
  padding: 24rpx 20rpx;
  margin: 20rpx 16rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
  position: relative;
  transition: all 0.6s cubic-bezier(0.22, 0.61, 0.36, 1); /* 更平滑的过渡时间和曲线 */
  overflow: visible;
  border-left: 4rpx solid #4a90e2;
  margin-top: 28rpx;
  will-change: transform, opacity;
  opacity: 1;
}

/* 多选模式下数据项样式 */
.data-item.multi-select-mode {
  margin: 20rpx 16rpx;
  margin-top: 28rpx;
  opacity: 0; /* 初始不可见 */
  transform: translateY(20rpx); /* 轻微向下偏移，为动画做准备 */
  transition: opacity 0.7s cubic-bezier(0.22, 0.61, 0.36, 1), 
              transform 0.7s cubic-bezier(0.22, 0.61, 0.36, 1); /* 分开指定属性实现更精细的控制 */
}

/* 显示多选模式下的数据项 */
.animateItems .data-item.multi-select-mode {
  opacity: 1;
  transform: translateY(0);
}

/* 添加逐个出现的延迟效果 - 每个项目延迟50ms，最多10个 */
.animateItems .data-item.multi-select-mode:nth-child(1) {
  transition-delay: 0.05s;
}

.animateItems .data-item.multi-select-mode:nth-child(2) {
  transition-delay: 0.1s;
}

.animateItems .data-item.multi-select-mode:nth-child(3) {
  transition-delay: 0.15s;
}

.animateItems .data-item.multi-select-mode:nth-child(4) {
  transition-delay: 0.2s;
}

.animateItems .data-item.multi-select-mode:nth-child(5) {
  transition-delay: 0.25s;
}

.animateItems .data-item.multi-select-mode:nth-child(6) {
  transition-delay: 0.3s;
}

.animateItems .data-item.multi-select-mode:nth-child(7) {
  transition-delay: 0.35s;
}

.animateItems .data-item.multi-select-mode:nth-child(8) {
  transition-delay: 0.4s;
}

.animateItems .data-item.multi-select-mode:nth-child(9) {
  transition-delay: 0.45s;
}

.animateItems .data-item.multi-select-mode:nth-child(10) {
  transition-delay: 0.5s;
}

/* 数据列表动画效果 */

/* 定义数据项弹出动画 - 优化为包含位移和透明度变化 */
@keyframes item-pop-in {
  from {
    opacity: 0;
    transform: translateY(20rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 添加淡出动画，用于退出多选模式 */
.data-list.mode-transition .data-item {
    opacity: 0;
  transition: opacity 0.6s cubic-bezier(0.22, 0.61, 0.36, 1),
              transform 0.6s cubic-bezier(0.22, 0.61, 0.36, 1);
}

/* 底部按钮淡出动画 - 保留原有效果 */
.data-list.mode-transition ~ .bottom-buttons {
    transform: translateY(100%);
    opacity: 0;
  transition: all 0.7s cubic-bezier(0.22, 0.61, 0.36, 1);
}

/* 添加单选模式下的数据组标签 - 使用更精确的选择器 */
.data-item:not(.multi-select-mode)::before {
  content: "数据组 " attr(data-group);
  position: absolute;
  top: -14rpx;
  left: 24rpx;
  font-size: 24rpx;
  padding: 8rpx 24rpx;
  border-radius: 24rpx;
  z-index: 10;
  letter-spacing: 1rpx;
  background: linear-gradient(90deg, #c58eff 0%, #78b9ff 100%); /* 反向渐变色 */
  color: white;
  min-width: 120rpx;
  text-align: center;
  box-shadow: 0 4rpx 16rpx rgba(90, 120, 213, 0.4);
}

/* 数据内容区域 */
.data-content {
  width: 100%;
}

/* 多选模式下的 header 样式 */
.isMultiSelectMode .data-header {
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  background: linear-gradient(145deg, rgba(246, 244, 255, 0.9), rgba(241, 239, 252, 0.9));
  border-radius: 12rpx;
  border: 1px solid rgba(180, 170, 255, 0.2);
}

/* 多选模式下的标题样式 */
.isMultiSelectMode .header-title {
  flex-grow: 1;
}

/* 多选模式下的取消按钮 */
.isMultiSelectMode .cancel-btn {
  flex-shrink: 0;
}

/* TC值和标签样式 */
.data-main {
  display: flex;
  flex-direction: row;
  align-items: center;
  flex-wrap: wrap;
  width: 100%;
  position: relative;
  padding-bottom: 6rpx;
}

/* TC值样式 */
.tc-value {
  font-size: 32rpx;
  color: #333;
  font-weight: 600;
  max-width: 60%;
  white-space: normal;
  word-break: break-all;
  overflow: visible;
  line-height: 1.3;
  flex-shrink: 1;
  margin-right: 8rpx;
}

/* 时间样式 */
.time-row {
  font-size: 24rpx;
  color: #999;
  margin-left: auto;
  white-space: nowrap;
  flex-shrink: 0;
}

/* 标签样式 */
.analysis-type {
  font-size: 22rpx;
  padding: 4rpx 10rpx;
  border-radius: 6rpx;
  color: #fff;
  margin-left: 8rpx;
  font-weight: normal;
  flex-shrink: 0;
}

.analysis-type.formal {
  background: linear-gradient(90deg, #78b9ff 0%, #c58eff 100%) !important;
}

.analysis-type.temp {
  background: linear-gradient(90deg, #FF9800 0%, #E53935 100%) !important;
}

/* 数据详情区域 */
/* 删除重复样式 */

/* 详情行样式 */
.detail-row {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  padding: 8rpx 0;
  width: 100%;
  flex-wrap: wrap;
  gap: 16rpx;
}

/* 区域容器 */
.detail-left {
  display: flex;
  flex-direction: row;
  flex: 1;
  overflow: hidden;
}

/* 区块样式 */
.area-block {
  display: flex;
  flex-direction: row;
  padding: 6rpx 10rpx;
  background: transparent; /* 从#fafafa改为透明，与周围背景一致 */
  border-radius: 8rpx;
  margin-right: 8rpx;
}

/* 标签样式 */
.detail-label {
  font-size: 28rpx;
  color: #666;
  margin-right: 8rpx;
}

/* 数值样式 */
.detail-value {
  font-size: 28rpx;
  color: #333;
}

/* C区和T区的颜色区分 */
.area-block:nth-child(1) .detail-value {
  color: #4a90e2;
}

.area-block:nth-child(2) .detail-value {
  color: #34d399;
}

/* 统一单选和多选模式下的区域块样式 */
.multi-select-mode .area-block,
.data-item:not(.multi-select-mode) .area-block {
  padding: 6rpx 10rpx;
  background: transparent; /* 从#fafafa改为透明，与周围背景一致 */
  border-radius: 8rpx;
  margin-right: 8rpx;
  align-items: center;
  width: 48%;
  overflow: hidden;
}

/* 统一单选和多选模式下的数值样式 */
.multi-select-mode .detail-value,
.data-item:not(.multi-select-mode) .detail-value {
  min-width: 0;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  word-break: keep-all;
}

/* 数值过长时的特殊处理 - 点击或长按可显示完整内容 */
.detail-value-expanded {
  white-space: normal;
  overflow: visible;
  text-overflow: clip;
  word-break: break-all;
  position: relative;
  z-index: 10;
  background: #f0f0f0;
  box-shadow: 0 0 5rpx rgba(0,0,0,0.1);
}

/* 按钮样式 */
.action-btn.primary {
  margin-left: auto;
  margin-top: 8rpx;
}

/* 参数行样式 */
.param-row {
  margin-top: 12rpx;
  display: flex;
  align-items: center;
  gap: 10rpx;
  background: transparent;
}

/* 媒体选项中的文本 */
.media-option .option-text {
  font-size: 30rpx;
  font-weight: 500;
  position: relative;
  z-index: 1;
  letter-spacing: 8rpx;
  color: #3498db;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.02);
  font-family: "PingFang SC", -apple-system, BlinkMacSystemFont, "Helvetica Neue", Helvetica, Arial, sans-serif;
}

/* 导出选项中的文本 */
.export-option .option-text {
  font-size: 28rpx;
  color: #333;
}

/* 数据组标签样式，与主界面按钮保持一致 */
.data-item.multi-select-mode::before {
  content: "数据组 " attr(data-group);
  position: absolute;
  top: -25rpx;
  left: 24rpx;
  font-size: 24rpx;
  padding: 8rpx 24rpx;
  border-radius: 24rpx;
  z-index: 10;
  letter-spacing: 1rpx;
  background: linear-gradient(90deg, #78b9ff 0%, #c58eff 100%); /* 主界面按钮渐变色 */
  color: white;
  min-width: 120rpx;
  text-align: center;
  box-shadow: 0 4rpx 16rpx rgba(90, 120, 213, 0.4);
  transition: all 0.7s cubic-bezier(0.22, 0.61, 0.36, 1);
  opacity: 0; /* 初始透明 */
  transform: translateY(-5rpx);
}

/* 动画显示时的标签淡入 */
.animateItems .data-item.multi-select-mode::before {
  opacity: 1;
  transform: translateY(0);
  transition-delay: 0.15s; /* 轻微延迟，让标签出现比数据项慢一点 */
}

/* 多选模式下的数据项样式 - 实现上浮和放大效果，增加数据组之间的间距 */
.data-item.multi-select-mode {
  transform: translateY(-8rpx) scale(1.05);
  box-shadow: 0 8rpx 20rpx rgba(0, 0, 0, 0.08);
  z-index: 1; /* 提高z-index，实现空间深度感知 */
  transition: all 0.7s cubic-bezier(0.22, 0.61, 0.36, 1);
  will-change: transform, opacity, box-shadow;
  margin-top: 48rpx; /* 减少顶部间距，但保持组间有一定间隔 */
  margin-bottom: 40rpx; /* 减少底部间距，改善整体布局紧凑性 */
  opacity: 0; /* 初始状态为透明 */
}

/* 退出多选模式时的数据块聚拢效果 */
.data-item:not(.multi-select-mode) {
  transition: all 0.7s cubic-bezier(0.22, 0.61, 0.36, 1);
  transform: translateY(0) scale(1.0);
}

/* 移除不同索引的数据组标签不同颜色的设置，统一使用主界面按钮颜色 */
.data-item.multi-select-mode:nth-child(3n)::before {
  background: linear-gradient(90deg, #78b9ff 0%, #c58eff 100%); /* 修改为主界面按钮渐变色 */
  box-shadow: 0 4rpx 16rpx rgba(90, 120, 213, 0.4);
}

.data-item.multi-select-mode:nth-child(3n+1)::before {
  background: linear-gradient(90deg, #78b9ff 0%, #c58eff 100%); /* 修改为主界面按钮渐变色 */
  box-shadow: 0 4rpx 16rpx rgba(90, 120, 213, 0.4);
}

/* 未选中状态下的按钮样式 */
.data-item:not(.selected-item) .media-btn {
  opacity: 0.5;
  pointer-events: none;
  cursor: not-allowed;
  background-color: #f5f5f5 !important;
  border-color: #ddd;
  color: #999 !important;
  box-shadow: none;
  transition: all 0.4s cubic-bezier(0.05, 0.7, 0.1, 1);
  transform: scale(0.9);
  transform-origin: center center;
  will-change: transform, opacity, background-color, border-color;
}

/* 选中状态下的媒体按钮默认样式 */
.data-item.selected-item .media-btn {
  opacity: 1;
  pointer-events: auto;
  cursor: pointer;
  box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.08);
  transition: all 0.4s cubic-bezier(0.05, 0.7, 0.1, 1);
  transform: scale(1);
  transform-origin: center center;
  will-change: transform, opacity, background-color, border-color;
}

/* 选中状态下的视频按钮 */
.data-item.selected-item .media-btn.video {
  background-color: rgba(74, 144, 226, 0.05) !important;
  color: #4a90e2 !important;
  border-color: rgba(74, 144, 226, 0.3);
}

.data-item.selected-item .media-btn.video.selected {
  background-color: #4a90e2 !important;
  color: white !important;
  border-color: #4a90e2;
}

/* 选中状态下的图片按钮 */
.data-item.selected-item .media-btn.image {
  background-color: rgba(52, 211, 153, 0.05) !important;
  color: #34d399 !important;
  border-color: rgba(52, 211, 153, 0.3);
}

.data-item.selected-item .media-btn.image.selected {
  background-color: #34d399 !important;
  color: white !important;
  border-color: #34d399;
}

/* 返回按钮 */
.back-btn {
  width: 90%;
  height: 90rpx;
  line-height: 90rpx;
  text-align: center;
  border-radius: 45rpx;
  font-size: 32rpx;
  color: #fff;
  background: linear-gradient(90deg, #78b9ff 0%, #c58eff 100%);
  margin: 0 auto;
  position: relative;
  transition: transform 0.3s ease, opacity 0.3s ease;
  box-shadow: 0 4rpx 16rpx rgba(90, 120, 213, 0.4);
}

.back-btn:active {
  transform: translateY(2rpx);
  box-shadow: 0 2rpx 6rpx rgba(90, 120, 213, 0.2);
  opacity: 0.9;
}

/* 底部按钮区域 - 基础样式 */
.bottom-buttons {
  width: 100%;
  z-index: 1500;
  display: flex;
  justify-content: center;
  align-items: center;
  box-sizing: border-box;
}

/* 单选模式下的底部按钮区域样式 */
.bottom-buttons:not(.multi-select-mode) {
  position: fixed;
  bottom: calc(110rpx + env(safe-area-inset-bottom)); /* 直接定位在底部导航栏上方 */
  left: 0;
  right: 0;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 30rpx 0;
  z-index: 1500;
  background: rgba(246, 244, 255, 0.85); /* 半透明浅紫色背景，与整体风格协调 */
  backdrop-filter: blur(10rpx); /* 添加模糊效果增强视觉融合 */
  width: 100%;
  border-radius: 0;
  box-shadow: 0 -2rpx 10rpx rgba(90, 120, 213, 0.08); /* 淡化阴影 */
  border-top: 1rpx solid rgba(180, 170, 255, 0.15); /* 淡色边框 */
  border-bottom: none;
  transition: all 0.3s ease;
}

/* 这里有样式冲突已处理 */

/* 底部按钮淡入动画 */
/* 底部按钮动画已统一到.isMultiSelectMode .bottom-buttons.multi-select-mode中 */

/* 按钮组样式 - 确保与单选模式一致 */
.btn-group {
  display: flex;
  justify-content: center; /* 居中对齐，与单选模式一致 */
  align-items: center;
  width: 90%; /* 与单选模式返回按钮宽度一致 */
  margin: 0 auto; /* 居中显示 */
  gap: 15rpx; /* 使用固定间距 */
}

/* 删除、导出和生成图表按钮的基础样式 */
.delete-btn, .export-btn, .generate-btn {
  flex: 1;
  min-width: 180rpx;
  height: 90rpx;
  line-height: 90rpx;
  transition: all 0.4s cubic-bezier(0.05, 0.7, 0.1, 1);
  transform: scale(1);
  transform-origin: center center;
  will-change: transform, opacity, background-color;
  text-align: center;
  border-radius: 45rpx;
  font-size: 30rpx;
  color: #fff;
  letter-spacing: 2rpx;
  font-weight: 500;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  position: relative;
  overflow: hidden;
  box-shadow: 0 6rpx 20rpx rgba(0, 0, 0, 0.2);
}

/* 删除按钮样式 */
.delete-btn {
  /* 通用样式已在.btn-group .delete-btn中定义 */
  background: linear-gradient(90deg, #c58eff 0%, #78b9ff 100%);
  box-shadow: 0 4rpx 16rpx rgba(90, 120, 213, 0.4);
}

/* 添加高光效果以增强视觉一致性 */
.delete-btn::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 50%;
  background: linear-gradient(to bottom, rgba(255, 255, 255, 0.3), rgba(255, 255, 255, 0));
  border-radius: 45rpx 45rpx 0 0;
}

/* 导出按钮样式 */
.export-btn {
  /* 通用样式已在.btn-group .export-btn中定义 */
  background: linear-gradient(90deg, #c58eff 0%, #78b9ff 100%);
  box-shadow: 0 4rpx 16rpx rgba(90, 120, 213, 0.4);
}

/* 生成图表按钮样式 */
.generate-btn {
  flex: 1;
  min-width: 180rpx;
  height: 90rpx;
  line-height: 90rpx;
  text-align: center;
  border-radius: 45rpx;
  font-size: 30rpx;
  color: #fff;
  letter-spacing: 2rpx;
  font-weight: 500;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  position: relative;
  overflow: hidden;
  /* 修改为取消按钮的紫色渐变 */
  background: linear-gradient(90deg, #8E54E9, #4776E6);
  box-shadow: 0 4rpx 12rpx rgba(142, 84, 233, 0.3);
}

/* 返回按钮样式 */
.back-btn {
  width: 90%;
  height: 88rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  color: #fff;
  font-size: 32rpx;
  font-weight: 500;
  letter-spacing: 4rpx;
  background: linear-gradient(90deg, #78b9ff 0%, #c58eff 100%);
  border-radius: 44rpx;
  box-shadow: 0 4rpx 12rpx rgba(90, 120, 213, 0.3);
  position: relative;
  overflow: hidden;
  transition: all 0.3s ease;
}

/* 添加按钮内部高光效果 */
.back-btn::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(180deg, rgba(255, 255, 255, 0.3), rgba(255, 255, 255, 0));
  z-index: 1;
  border-radius: 44rpx;
}

/* 所有按钮的点击效果 */
.back-btn:active, .delete-btn:active, .export-btn:active, .generate-btn:active {
  transform: translateY(2rpx) scale(0.98);
  box-shadow: 0 2rpx 6rpx rgba(90, 120, 213, 0.2);
  opacity: 0.9;
}

/* 已禁用按钮悬停效果，避免颜色滞留问题 */
/*
.button-hover {
  opacity: 0.8;
}
*/

/* 参数详情卡片 */
.detail-card {
  position: absolute;
  top: 100%;
  left: 0;
  width: 100%;
  background: #fff;
  border-radius: 12rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
  z-index: 100;
  display: none;
}

.show-detail .detail-card {
  display: block;
}

/* 图表相关样式 */
.chart-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100vh;
  z-index: 1001;
  background: #fff;
  display: flex;
  flex-direction: column;
}

.chart-container {
  width: 100%;
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: #fff;
  position: relative;
  z-index: 1002;
  overflow: hidden;
}

.chart-header {
  height: 80rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 30rpx;
  border-bottom: 1rpx solid #eee;
  background: #fff;
  z-index: 2;
}

.chart-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
}

.chart-controls {
  display: flex;
  align-items: center;
  gap: 20rpx;
}

.reset-btn {
  padding: 12rpx 24rpx;
  font-size: 24rpx;
  color: #4A90E2;
  border: 1px solid #4A90E2;
  border-radius: 32rpx;
  transition: all 0.2s ease;
  line-height: 1.5;
}

.reset-btn:active {
  opacity: 0.8;
  transform: scale(0.98);
}

.chart-controls .export-btn {
  padding: 12rpx 24rpx;
  font-size: 24rpx;
  color: #fff;
  /* 使用主界面按钮渐变色 */
  background: linear-gradient(90deg, #78b9ff 0%, #c58eff 100%);
  border-radius: 32rpx;
  transition: all 0.2s ease;
  width: auto;
  height: auto;
  line-height: 1.5;
  letter-spacing: normal;
  box-shadow: 0 4rpx 16rpx rgba(90, 120, 213, 0.4);
}

.chart-controls .export-btn:active {
  opacity: 0.8;
  transform: scale(0.98);
}

.close-btn {
  padding: 10rpx;
  font-size: 32rpx;
  color: #666;
  margin-left: 10rpx;
}

.chart-scroll-container {
  flex: 1;
  width: 100%;
  height: calc(100vh - 80rpx);
  transform-origin: 0 0;
  transition: transform 0.1s ease-out;
  touch-action: none;
}

.tc-chart {
  width: 100%;
  height: 100%;
  background: #fff;
}

.temp-tag {
  font-size: 24rpx;
  color: #ff9800;
  background: rgba(255, 152, 0, 0.1);
  padding: 4rpx 12rpx;
  border-radius: 4rpx;
  margin-left: 12rpx;
}

/* 参数详情卡片样式 */
.param-detail-mask {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100vh;
  background: rgba(0, 0, 0, 0);
  z-index: 3000;
  display: flex;
  justify-content: center;
  align-items: flex-start;
  transition: all 0.6s cubic-bezier(0.16, 1, 0.3, 1);
  opacity: 0;
  visibility: hidden;
  backdrop-filter: blur(0px);
  padding-top: 12vh;
  padding-bottom: 25vh;
  will-change: opacity, backdrop-filter, background;
}

.param-detail-mask.show {
  background: rgba(0, 0, 0, 0.75);
  opacity: 1;
  visibility: visible;
  backdrop-filter: blur(8px);
  transition: background 0.4s cubic-bezier(0.16, 1, 0.3, 1),
              opacity 0.4s cubic-bezier(0.16, 1, 0.3, 1),
              backdrop-filter 0.4s cubic-bezier(0.16, 1, 0.3, 1),
              visibility 0s linear;
}

.param-detail-mask.hiding {
  background: rgba(0, 0, 0, 0);
  opacity: 0;
  backdrop-filter: blur(0px);
  transition: background 0.3s cubic-bezier(0.4, 0, 0.2, 1),
              opacity 0.3s cubic-bezier(0.4, 0, 0.2, 1),
              backdrop-filter 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  pointer-events: none !important;
}

/* 优化主卡片的动画性能 */
.param-detail-card {
  width: 720rpx;
  background: linear-gradient(135deg, #ffffff, #f8f9fa);
  border-radius: 32rpx;
  padding: 0;
  box-shadow: 0 20rpx 60rpx rgba(0, 0, 0, 0.3), 0 4rpx 12rpx rgba(0, 0, 0, 0.15), inset 0 0 0 2rpx rgba(255, 255, 255, 0.9);
  position: relative;
  opacity: 0;
  border: none;
  overflow: hidden;
  will-change: transform, opacity;
  /* 初始状态 */
  transform: scale(0.8) translateY(30rpx);
  /* 强制每次都重新应用动画 */
  animation: none;
  backface-visibility: hidden;
  perspective: 1000;
  /* 添加内部阴影，解决边缘白边问题 */
  box-sizing: border-box;
  outline: none;
  -webkit-mask-image: -webkit-radial-gradient(white, black); /* 修复Safari中的白边问题 */
}

.param-detail-header {
  display: flex;
  flex-direction: column;
  padding: 40rpx;
  background: linear-gradient(135deg, #4a90e2, #7c4dff);
  color: #fff;
  position: relative;
  overflow: hidden;
  opacity: 0;
  transform: translateY(-10rpx);
  animation: header-fade-in 0.35s cubic-bezier(0.16, 1, 0.3, 1) forwards;
  animation-delay: 0.15s;
  will-change: opacity, transform;
}

.param-detail-header::after {
  content: '';
  position: absolute;
  bottom: -30rpx;
  right: -30rpx;
  width: 150rpx;
  height: 150rpx;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.1);
  z-index: 1;
}

.param-detail-header::before {
  content: '';
  position: absolute;
  top: -40rpx;
  left: -40rpx;
  width: 120rpx;
  height: 120rpx;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.08);
  z-index: 1;
}

.param-detail-title {
  font-size: 38rpx;
  color: #ffffff;
  font-weight: 600;
  letter-spacing: 1rpx;
  margin-bottom: 10rpx;
  position: relative;
  z-index: 2;
}

.param-detail-time {
  font-size: 26rpx;
  color: rgba(255, 255, 255, 0.85);
  position: relative;
  z-index: 2;
}

.param-detail-content {
  max-height: 50vh;
  overflow-y: auto;
  padding: 30rpx 40rpx;
  background: #ffffff;
  opacity: 0;
  animation: content-fade-in 0.25s ease-out forwards;
  animation-delay: 0.15s;
}

/* 隐藏时的动画, 使用简化的动画 */
.param-detail-card.hiding {
  animation-name: simpleScaleOut;
  animation-duration: 0.3s;
  animation-timing-function: cubic-bezier(0.33, 0, 0.67, 1);
  animation-fill-mode: forwards;
  animation-delay: 0s;
  will-change: transform, opacity;
  pointer-events: none;
}

/* 卡片弹出动画 */
@keyframes card-pop-in {
  0% {
    opacity: 0;
    transform: scale(0.8) translateY(30rpx);
  }
  70% {
    opacity: 1;
    transform: scale(1.02) translateY(-5rpx);
  }
  100% {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

/* 添加缺失的退出动画定义 */
@keyframes simpleScaleOut {
  0% {
    opacity: 1;
    transform: scale(1);
  }
  100% {
    opacity: 0;
    transform: scale(0.85);
  }
}
  
/* Q弹子元素动画效果 - 更丝滑缓慢 */
.param-detail-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16rpx 15rpx 16rpx 20rpx;
  margin-bottom: 12rpx;
  border-radius: 16rpx;
  position: relative;
  background: #f8f9fc;
  border-left: 4rpx solid transparent;
  opacity: 0;
  transform: scale(0.7) translateY(20rpx) translateX(15rpx);
  will-change: transform, opacity;
  transition: opacity 0.4s cubic-bezier(0.05, 0.7, 0.1, 1), transform 0.4s cubic-bezier(0.34, 1.56, 0.64, 1);
  backface-visibility: hidden;
  contain: content;
}

/* 当卡片显示时，让所有项目Q弹显示 */
.param-detail-mask.show .param-detail-item {
  opacity: 1;
  transform: scale(1) translateY(0) translateX(0);
}

/* 添加悬停效果增强交互感 */
.param-detail-item:active {
  transform: scale(0.98) translateY(0) translateX(0);
  transition: transform 0.1s ease;
  background: #f0f2f8;
}

/* 组标题Q弹动画效果 - 更丝滑缓慢 */
.param-group-title {
  font-size: 24rpx;
  color: #6b7280;
  margin-bottom: 12rpx;
  font-weight: 500;
  position: relative;
  padding-left: 16rpx;
  opacity: 0;
  transform: scale(0.8) translateY(10rpx) translateX(-8rpx);
  transition: opacity 0.4s cubic-bezier(0.05, 0.7, 0.1, 1), transform 0.4s cubic-bezier(0.34, 1.56, 0.64, 1);
  will-change: transform, opacity;
  backface-visibility: hidden;
}

/* 组标题Q弹显示 */
.param-detail-mask.show .param-group-title {
  opacity: 1;
  transform: scale(1) translateY(0) translateX(0);
}

/* 标题和卡片内容的滑入效果 */
.param-detail-header {
  opacity: 0;
  transform: translateY(-5rpx);
  transition: opacity 0.3s cubic-bezier(0.05, 0.7, 0.1, 1), transform 0.3s cubic-bezier(0.05, 0.7, 0.1, 1);
  transition-delay: 0.05s; /* 减少延迟时间 */
}

.param-detail-mask.show .param-detail-header {
  opacity: 1;
  transform: translateY(0);
  transition: opacity 0.3s cubic-bezier(0.05, 0.7, 0.1, 1), transform 0.3s cubic-bezier(0.05, 0.7, 0.1, 1);
  transition-delay: 0.05s;
}

.param-detail-content {
  opacity: 0;
  transform: translateY(5rpx);
  transition: opacity 0.3s cubic-bezier(0.05, 0.7, 0.1, 1), transform 0.3s cubic-bezier(0.05, 0.7, 0.1, 1);
  transition-delay: 0.1s; /* 减少延迟时间 */
  will-change: opacity, transform;
}

.param-detail-mask.show .param-detail-content {
  opacity: 1;
  transform: translateY(0);
}

/* Q弹显示效果 - 参数项逐个出现 */
.param-detail-mask.show .param-detail-item {
  opacity: 1;
  transform: scale(1) translateY(0);
}

/* 参数组标题的Q弹动画 - 更流畅的节奏 */
.param-detail-mask.show .param-group:nth-child(1) .param-group-title { transition-delay: 0.15s; }
.param-detail-mask.show .param-group:nth-child(2) .param-group-title { transition-delay: 0.4s; }
.param-detail-mask.show .param-group:nth-child(3) .param-group-title { transition-delay: 0.65s; }

/* 基础指标组内的项目 - Q弹逐项出现，更流畅 */
.param-detail-mask.show .param-group:nth-child(1) .param-detail-item:nth-child(2) { transition-delay: 0.2s; }
.param-detail-mask.show .param-group:nth-child(1) .param-detail-item:nth-child(3) { transition-delay: 0.25s; }
.param-detail-mask.show .param-group:nth-child(1) .param-detail-item:nth-child(4) { transition-delay: 0.3s; }
.param-detail-mask.show .param-group:nth-child(1) .param-detail-item:nth-child(5) { transition-delay: 0.35s; }

/* 图像参数组内的项目 - Q弹逐项出现，更流畅 */
.param-detail-mask.show .param-group:nth-child(2) .param-detail-item:nth-child(2) { transition-delay: 0.45s; }
.param-detail-mask.show .param-group:nth-child(2) .param-detail-item:nth-child(3) { transition-delay: 0.5s; }
.param-detail-mask.show .param-group:nth-child(2) .param-detail-item:nth-child(4) { transition-delay: 0.55s; }
.param-detail-mask.show .param-group:nth-child(2) .param-detail-item:nth-child(5) { transition-delay: 0.6s; }
.param-detail-mask.show .param-group:nth-child(2) .param-detail-item:nth-child(6) { transition-delay: 0.65s; }

/* 高级设置组内的项目 - Q弹逐项出现，更流畅 */
.param-detail-mask.show .param-group:nth-child(3) .param-detail-item:nth-child(2) { transition-delay: 0.7s; }
.param-detail-mask.show .param-group:nth-child(3) .param-detail-item:nth-child(3) { transition-delay: 0.75s; }
.param-detail-mask.show .param-group:nth-child(3) .param-detail-item:nth-child(4) { transition-delay: 0.8s; }
.param-detail-mask.show .param-group:nth-child(3) .param-detail-item:nth-child(5) { transition-delay: 0.85s; }
.param-detail-mask.show .param-group:nth-child(3) .param-detail-item:nth-child(6) { transition-delay: 0.9s; }
.param-detail-mask.show .param-group:nth-child(3) .param-detail-item:nth-child(7) { transition-delay: 0.95s; }
.param-detail-mask.show .param-group:nth-child(3) .param-detail-item:nth-child(8) { transition-delay: 1.0s; }
.param-detail-mask.show .param-group:nth-child(3) .param-detail-item:nth-child(9) { transition-delay: 1.05s; }
.param-detail-mask.show .param-group:nth-child(3) .param-detail-item:nth-child(10) { transition-delay: 1.1s; }

/* 图标点Q弹样式 - 更丝滑缓慢 */
.icon-dot {
  display: inline-block;
  width: 8rpx;
  height: 8rpx;
  border-radius: 50%;
  background: linear-gradient(135deg, #4a90e2, #7c4dff);
  margin-right: 8rpx;
  opacity: 0;
  transform: scale(0.5);
  transition: opacity 0.4s cubic-bezier(0.05, 0.7, 0.1, 1), transform 0.4s cubic-bezier(0.34, 1.56, 0.64, 1);
  transition-delay: 0.05s;
}

.param-detail-mask.show .param-detail-item .icon-dot {
  opacity: 1;
  transform: scale(1);
}

/* 参数值容器的动画 */
.param-detail-value {
  opacity: 0;
  transform: translateX(10rpx);
  transition: opacity 0.4s cubic-bezier(0.05, 0.7, 0.1, 1), transform 0.4s cubic-bezier(0.34, 1.56, 0.64, 1);
  transition-delay: 0.05s; /* 继承父元素延迟后额外增加的延迟 */
}

.param-detail-mask.show .param-detail-item .param-detail-value {
  opacity: 1;
  transform: translateX(0);
}

/* 自定义参数详情内容滚动条 */
.param-detail-scroll-container {
  max-height: 50vh;
  overflow-y: auto;
  padding: 30rpx 40rpx; 
  background: #ffffff;
}

.param-detail-item:nth-child(odd) {
  border-left-color: rgba(74, 144, 226, 0.7);
}

.param-detail-item:nth-child(even) {
  border-left-color: rgba(124, 77, 255, 0.7);
}

.param-detail-item:last-child {
  margin-bottom: 0;
}

.param-detail-item-active {
  transform: scale(0.99);
  background: #f5f7fa;
}

.param-detail-label {
  font-size: 24rpx; /* 减小标签字体 */
  color: #4a5568;
  font-weight: 500;
  display: flex;
  align-items: center;
  flex-grow: 0.5; /* 适度增加标签区域宽度 */
  flex-shrink: 1;
  margin-right: 20rpx; /* 保持与值之间的距离 */
  white-space: nowrap;
  max-width: 35%; /* 微调标签最大宽度 */
}

.param-detail-value {
  font-size: 24rpx;
  color: #2c3e50;
  font-weight: 600;
  text-align: left; /* 左对齐 */
  white-space: nowrap;
  line-height: 1.5;
  min-width: 30rpx;
  display: flex;
  align-items: center;
  justify-content: flex-start; /* 左对齐 */
  width: 50%;
  margin-left: auto;
  padding: 6rpx 15rpx;
  background: rgba(124, 77, 255, 0.03);
  border-radius: 30rpx;
  border: 1rpx solid rgba(74, 144, 226, 0.1);
  box-shadow: 0 2rpx 4rpx rgba(74, 144, 226, 0.03);
}

.param-detail-unit {
  display: inline-block;
  font-size: 16rpx;
  color: #8a94a6;
  margin-left: 2rpx;
  white-space: nowrap;
  opacity: 0.7;
  line-height: 1;
}

/* 滚动条样式优化 */
.param-detail-content::-webkit-scrollbar {
  width: 6rpx;
  background: transparent;
}

.param-detail-content::-webkit-scrollbar-thumb {
  background: rgba(124, 77, 255, 0.2);
  border-radius: 3rpx;
}

.param-detail-content::-webkit-scrollbar-track {
  background: transparent;
}

/* 当卡片显示时，让所有项目逐一滑入 */
.param-detail-mask.show .param-detail-item {
  transform: translateX(0) scale(1);
  opacity: 1;
}

.data-item.multi-select-mode .tc-value {
  max-width: 100%;
  margin-bottom: 6rpx;
  font-size: 36rpx;
}

.data-item.multi-select-mode .time-row {
  margin: 8rpx 0 0;
  width: 100%;
  text-align: right;
}

/* 进一步优化多选模式按钮区域 */
.data-item.multi-select-mode .media-btn {
  height: 70rpx;
  min-width: 180rpx;
  max-width: 280rpx;
  padding: 0 16rpx;
  margin: 0;
  border-radius: 35rpx;
  background-color: #fff;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  border: 1px solid #eee;
  position: relative;
  flex-shrink: 0;
  box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.08);
  z-index: 1;
  transition: all 0.4s cubic-bezier(0.05, 0.7, 0.1, 1);
  -webkit-tap-highlight-color: transparent;
  will-change: transform, opacity, background-color, border-color, box-shadow;
  transform: scale(1);
  opacity: 1;
  transform-origin: center center;
}

.data-item.multi-select-mode .btn-container {
  display: flex !important;
  justify-content: flex-end;
  margin-top: 16rpx;
  gap: 20rpx;
  width: 100%;
  flex-wrap: nowrap;
  align-items: center;
  padding: 4rpx 0;
  position: relative;
  z-index: 10;
}

/* 确保按钮内容显示 */
.data-item.multi-select-mode .media-btn .btn-content {
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  width: 100% !important;
  height: 70rpx !important;
  text-align: center !important;
  flex-direction: row !important;
  box-sizing: border-box !important;
  margin: 0 !important;
  padding: 0 !important;
  line-height: 70rpx !important;
}

.data-item.multi-select-mode .media-btn .btn-icon {
  width: 28rpx;
  height: 28rpx;
  margin-right: 8rpx;
  margin-top: 0;
  margin-bottom: 0;
  margin-left: 0;
  flex-shrink: 0;
  vertical-align: middle;
}

.data-item.multi-select-mode .media-btn .btn-text {
  font-size: 26rpx;
  color: inherit;
  white-space: nowrap;
  flex-shrink: 0;
  text-align: center;
  line-height: 70rpx;
  height: 70rpx;
  margin: 0;
  padding: 0;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 视频按钮样式 */
.data-item.multi-select-mode .media-btn.video {
  color: #4a90e2;
  border-color: rgba(74, 144, 226, 0.3);
  background: linear-gradient(90deg, #78b9ff 0%, #c58eff 100%);
  color: white;
}

.data-item.multi-select-mode .media-btn.video.selected {
  background: linear-gradient(90deg, #78b9ff 0%, #c58eff 100%) !important;
  border-color: #4a90e2;
  box-shadow: 0 4rpx 16rpx rgba(90, 120, 213, 0.4);
}

/* 图片按钮样式 */
.data-item.multi-select-mode .media-btn.image {
  color: #34d399;
  border-color: rgba(52, 211, 153, 0.3);
  background: linear-gradient(90deg, #78b9ff 0%, #c58eff 100%);
  color: white;
}

.data-item.multi-select-mode .media-btn.image.selected {
  background: linear-gradient(90deg, #78b9ff 0%, #c58eff 100%) !important;
  border-color: #34d399;
  box-shadow: 0 4rpx 16rpx rgba(90, 120, 213, 0.4);
}

/* 为多选模式按钮添加点击效果 */
.data-item.multi-select-mode .media-btn:active {
  transform: scale(0.96) translateY(1rpx);
  opacity: 0.85;
  box-shadow: 0 1rpx 3rpx rgba(0, 0, 0, 0.08);
  transition: transform 0.08s ease-out, opacity 0.08s ease-out, background-color 0.08s ease, border-color 0.08s ease, box-shadow 0.08s ease-out;
}

/* 区分未选中和选中的按钮点击效果 */
.data-item.multi-select-mode .media-btn.video:not(.selected):active {
  background: linear-gradient(90deg, #b57aff 0%, #6db0ff 100%) !important;
  border-color: rgba(74, 144, 226, 0.5);
}

.data-item.multi-select-mode .media-btn.video.selected:active {
  background: linear-gradient(90deg, #b57aff 0%, #6db0ff 100%) !important;
  border-color: #3a7bc8;
}

.data-item.multi-select-mode .media-btn.image:not(.selected):active {
  background: linear-gradient(90deg, #b57aff 0%, #6db0ff 100%) !important;
  border-color: rgba(52, 211, 153, 0.5);
}

.data-item.multi-select-mode .media-btn.image.selected:active {
  background: linear-gradient(90deg, #b57aff 0%, #6db0ff 100%) !important;
  border-color: #29b781;
}

/* 确保按钮显示在适当位置 */
.detail-left {
  margin-bottom: 0;
}

/* 小屏幕适配优化 */
.small-screen .data-item.multi-select-mode .media-btn {
  min-width: 140rpx;
  max-width: 200rpx;
  padding: 0 14rpx;
}

.small-screen .data-item.multi-select-mode .media-btn .btn-icon {
  width: 24rpx;
  height: 24rpx;
  margin-right: 6rpx;
}

.small-screen .data-item.multi-select-mode .media-btn .btn-text {
  font-size: 24rpx;
  line-height: normal;
}

/* 修复多选模式的布局 */
.data-item.multi-select-mode .data-details {
  background: linear-gradient(145deg, rgba(248, 249, 252, 0.8), rgba(246, 244, 255, 0.8));
  border-radius: 12rpx;
  padding: 20rpx;
  margin-top: 16rpx;
  border: 1px solid rgba(180, 170, 255, 0.15);
  box-shadow: inset 0 1rpx 3rpx rgba(90, 120, 213, 0.05);
  transform: translateY(20rpx);
  transition: all 0.4s cubic-bezier(0.05, 0.7, 0.1, 1);
  transition-delay: 0.15s;
  will-change: transform, opacity;
}

.data-item.multi-select-mode .detail-row {
  flex-direction: row;
  align-items: center;
  padding: 8rpx 0;
}

.data-item.multi-select-mode .detail-left {
  display: flex;
  flex-direction: row;
  flex: 1;
  overflow: hidden;
}

.data-item.multi-select-mode .area-block {
  display: flex;
  flex-direction: row;
  padding: 6rpx 10rpx;
  background: transparent;
  border-radius: 8rpx;
  margin-right: 8rpx;
}

.data-item.multi-select-mode .detail-label {
  font-size: 28rpx;
  color: #666;
  margin-right: 8rpx;
}

.data-item.multi-select-mode .detail-value {
  font-size: 28rpx;
  color: #333;
}

.data-item.multi-select-mode .area-block:nth-child(1) .detail-value {
  color: #4a90e2;
}

.data-item.multi-select-mode .area-block:nth-child(2) .detail-value {
  color: #34d399;
}

/* 优化多选模式下的按钮区域 */
.data-item.multi-select-mode .btn-container {
  display: flex !important;
}

/* 媒体选择卡片样式 */
.media-select-mask {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 1000;
  display: flex;
  justify-content: center;
  align-items: center;
  opacity: 0;
  visibility: hidden;
  transition: opacity 0.3s, visibility 0.3s;
  backdrop-filter: blur(4px);
}

.media-select-mask.show {
  opacity: 1;
  visibility: visible;
}

.media-select-card {
  width: 650rpx;
  background: rgba(246, 244, 255, 0.85);
  border-radius: 24rpx;
  padding: 50rpx 40rpx;
  box-shadow: 0 10rpx 35rpx rgba(0, 0, 0, 0.1);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  opacity: 0;
  transform: translateY(30px);
  backdrop-filter: blur(10rpx);
  border: 1rpx solid rgba(255, 255, 255, 0.7);
}

.media-select-mask.show .media-select-card {
  transform: translateY(0);
  opacity: 1;
}

.media-select-title {
  font-size: 36rpx;
  color: #333;
  text-align: center;
  margin-bottom: 45rpx;
  font-weight: 600;
  position: relative;
}

.media-select-title:after {
  content: '';
  position: absolute;
  bottom: -15rpx;
  left: 50%;
  transform: translateX(-50%);
  width: 50rpx;
  height: 6rpx;
  background: linear-gradient(90deg, #6ba8f7, #4a90e2);
  border-radius: 3rpx;
}

.media-select-options {
  display: flex;
  justify-content: center;
  align-items: center;
  flex-wrap: wrap;
  gap: 40rpx;
  margin-top: 20rpx;
}

.media-option {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 28rpx 25rpx;
  border-radius: 16rpx;
  transition: all 0.25s ease;
  width: 200rpx;
  height: 100rpx;
  margin: 0;
  position: relative;
  background-color: rgba(255, 255, 255, 0.7);
  border: 1rpx solid rgba(240, 242, 245, 0.8);
  box-shadow: 0 6rpx 16rpx rgba(0, 0, 0, 0.08);
  text-align: center;
  overflow: hidden;
}

.media-option:active {
  background-color: rgba(248, 249, 250, 0.9);
  transform: scale(0.98) translateY(1rpx);
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);
}

.option-text {
  font-size: 30rpx;
  font-weight: 500;
  text-align: center;
  white-space: nowrap;
  line-height: 1.4;
  margin-top: 0;
  position: relative;
  z-index: 2;
}

/* 恢复针对不同选项设置不同颜色 */
.media-option:nth-child(1) .option-text {
  color: #4a90e2;
}

.media-option:nth-child(2) .option-text {
  color: #34d399;
}

.media-option:nth-child(3) .option-text {
  color: #f59e0b;
}

/* 添加按钮背景渐变效果 */
.media-option:nth-child(1)::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 6rpx;
  background: linear-gradient(90deg, #4a90e2, #6ba8f7);
  border-radius: 0 0 16rpx 16rpx;
}

.media-option:nth-child(2)::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 6rpx;
  background: linear-gradient(90deg, #34d399, #10b981);
  border-radius: 0 0 16rpx 16rpx;
}

.media-option:nth-child(3)::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 6rpx;
  background: linear-gradient(90deg, #f59e0b, #fbbf24);
  border-radius: 0 0 16rpx 16rpx;
}

/* 本地图片预览 */
.local-preview-mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.7);
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  pointer-events: none;
  transition: opacity 0.3s;
}

.local-preview-mask.show {
  opacity: 1;
  pointer-events: auto;
}

.local-preview-container {
  width: 90%;
  height: 80%;
  background-color: #fff;
  border-radius: 12rpx;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.local-preview-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx;
  border-bottom: 1px solid #eee;
}

.local-preview-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.local-preview-close {
  font-size: 40rpx;
  color: #999;
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.local-preview-scroll {
  flex: 1;
  padding: 20rpx;
}

.local-preview-image {
  width: 100%;
  margin-bottom: 20rpx;
  border-radius: 8rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
}

/* 视频播放器样式 */
.video-player-mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.8);
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  pointer-events: none;
  transition: opacity 0.3s;
}

.video-player-mask.show {
  opacity: 1;
  pointer-events: auto;
}

.video-player-container {
  width: 90%;
  height: 80%;
  background-color: #000;
  border-radius: 12rpx;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.video-player-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx;
  background-color: #222;
  border-bottom: 1px solid #333;
}

.video-player-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #fff;
}

.video-player-close {
  font-size: 40rpx;
  color: #fff;
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.video-player-content {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #000;
}

.video-player-content .video-instance {
  width: 100%;
  height: 100%;
  max-height: calc(100% - 80rpx);
}

/* 新增：搜索框样式 */
.search-container {
  position: relative;
  display: flex;
  align-items: center;
  flex-grow: 0;
  width: 90%;
  max-width: 650rpx;
  margin-left: 0;
  margin-top: 25rpx;
}

/* 更新：使用 CSS 绘制搜索图标 */
.search-container::before {
  content: '';
  position: absolute;
  left: 30rpx;
  top: 50%;
  transform: translateY(-50%) rotate(-45deg);
  width: 28rpx;
  height: 28rpx;
  border: 4rpx solid #a8a8a8;
  border-radius: 50%;
  z-index: 1;
}
/* 绘制放大镜手柄 */
.search-container::after {
  content: '';
  position: absolute;
  left: 52rpx;
  top: calc(50% + 14rpx);
  transform: translateY(-50%) rotate(-45deg);
  width: 5rpx;
  height: 15rpx;
  background-color: #a8a8a8;
  border-radius: 3rpx;
  z-index: 1;
}

/* 搜索框样式 */
.search-input {
  height: 72rpx;
  width: 100%;
  padding: 0 35rpx 0 85rpx;
  border: 1rpx solid #e8eaec;
  border-radius: 36rpx;
  font-size: 28rpx;
  color: #333;
  background-color: #fff;
  text-align: left;
  box-shadow: 0 3rpx 10rpx rgba(0, 0, 0, 0.05);
  margin-top: 0;
  position: relative;
  z-index: 0;
  transition: box-shadow 0.2s ease-in-out, border-color 0.2s ease-in-out;
}

/* 新增：聚焦时的样式 */
.search-input-focused {
  border-color: #d0e4ff !important; /* 使用 !important 提高优先级，确保覆盖默认边框 */
  box-shadow: 0 5rpx 15rpx rgba(74, 144, 226, 0.08) !important; /* 使用 !important 提高优先级 */
}

/* 列表区域 */
.data-list {
  flex: 1;
  width: 100%;
  min-height: 0;
  overflow-y: auto;
  -webkit-overflow-scrolling: touch;
  box-sizing: border-box;
  padding-top: 20rpx;
  /* 调整单选模式下的底部内边距，考虑返回按钮区域和导航栏的高度 */
  padding-bottom: calc(88rpx + 60rpx + 110rpx + env(safe-area-inset-bottom)); /* 返回按钮高度 + 按钮区域padding + 导航栏高度 + 安全区域 */
  transition: opacity 0.5s ease-out, transform 0.5s ease-out;
  will-change: opacity, transform;
  transform: translateZ(0);
  backface-visibility: hidden;
}

.data-list.mode-transition {
  opacity: 0;
  transform: translateY(-15rpx);
  pointer-events: none;
}

/* 多选模式下的列表样式 */
.data-display.isMultiSelectMode .data-list {
  /* 在多选模式下几乎不需要底部padding，因为.bottom-space-holder将处理空间 */
  padding-bottom: 10rpx;
}

/* 无数据提示样式 */
.no-data {
  text-align: center;
  color: #999;
  font-size: 28rpx;
  padding-top: 100rpx;
}

/* 新增：取消按钮容器样式 - 模拟搜索框容器布局 */
.cancel-btn-container {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 90%;
  max-width: 650rpx;
  margin-top: 25rpx;
  height: 72rpx;
  transition: all 0.35s cubic-bezier(0.22, 1, 0.36, 1);
  will-change: transform, opacity, visibility;
}

/* 调整取消按钮本身样式 */
.cancel-btn {
  font-size: 30rpx;
  color: #fff;
  padding: 0 60rpx;
  height: 72rpx;
  line-height: 72rpx;
  border-radius: 36rpx;
  background: linear-gradient(90deg, #8E54E9, #4776E6);
  box-shadow: 0 4rpx 12rpx rgba(142, 84, 233, 0.3);
  transition: all 0.35s cubic-bezier(0.22, 1, 0.36, 1);
  margin-left: 0;
  will-change: transform, opacity, box-shadow, padding;
}

.cancel-btn-active {
  opacity: 0.8;
  transform: scale(0.98);
}

/* 标题样式 (保持不变) */
.header-title {
  /* ... */
}

/* 移除之前针对多选模式的特殊 header 样式 */
/* 
.isMultiSelectMode .data-header { ... } 
.isMultiSelectMode .header-title { ... } 
.isMultiSelectMode .cancel-btn { ... }
*/

/* ... (其他样式) ... */

/* ----- 媒体按钮选中状态 ----- */

/* Ensure these styles apply specifically within multi-select mode */

/* Default visual state for media buttons when the item is *selected* (enabled) */
.data-item.multi-select-mode.selected-item .media-btn {
  opacity: 1; 
  pointer-events: auto;
  cursor: pointer;
  /* Default background/border for enabled but unselected buttons */
  background: linear-gradient(90deg, #c58eff 0%, #78b9ff 100%); /* 统一使用反向渐变色 */
  border: 1rpx solid rgba(180, 170, 255, 0.3); 
  box-shadow: 0 4rpx 16rpx rgba(90, 120, 213, 0.4); /* 统一阴影效果 */
  color: white; /* 文字颜色改为白色 */
  transition: all 0.35s cubic-bezier(0.22, 1, 0.36, 1);
  will-change: opacity, transform;
}

/* Styles for media buttons when the item is *NOT* selected (disabled) */
.data-item.multi-select-mode:not(.selected-item) .media-btn {
  opacity: 0.6; /* 适当的不透明度 */
  pointer-events: none; /* 保持不变 - 禁用点击 */
  cursor: default; /* 保持不变 - 更改光标 */
  background: linear-gradient(90deg, #c58eff 0%, #78b9ff 100%) !important; /* 保持相同的渐变色 */
  border-color: rgba(180, 170, 255, 0.2) !important; /* 更淡的边框 */
  box-shadow: none !important; /* 去除阴影效果 */
  transition: all 0.35s cubic-bezier(0.22, 1, 0.36, 1);
  will-change: opacity, transform;
  filter: opacity(0.6) saturate(0.4) !important; /* 降低饱和度使颜色变淡 */
}

/* Dim the text and icon for disabled buttons */
.data-item.multi-select-mode:not(.selected-item) .media-btn .btn-text {
  color: rgba(255, 255, 255, 0.7) !important; /* 半透明白色文本 */
}
.data-item.multi-select-mode:not(.selected-item) .media-btn .btn-icon {
  opacity: 0.7 !important; /* 适当的图标透明度 */
}

/* Styles for SELECTED media buttons (video/image itself is selected) */
.data-item.multi-select-mode.selected-item .media-btn.video.selected {
  background: linear-gradient(90deg, #c58eff 0%, #78b9ff 100%) !important; /* 反向渐变色 */
  border-color: rgba(74, 144, 226, 0.6) !important; /* 蓝色边框但更淡 */
  box-shadow: 0 4rpx 16rpx rgba(90, 120, 213, 0.4) !important; /* 统一阴影效果 */
}

.data-item.multi-select-mode.selected-item .media-btn.video.selected .btn-text,
.data-item.multi-select-mode.selected-item .media-btn.video.selected .btn-icon {
  color: #fff !important; /* 保持不变 - 白色文字 */
  font-weight: 500;
  opacity: 1 !important;
}

.data-item.multi-select-mode.selected-item .media-btn.image.selected {
  background: linear-gradient(90deg, #c58eff 0%, #78b9ff 100%) !important; /* 反向渐变色 */
  border-color: rgba(52, 211, 153, 0.6) !important; /* 绿色边框但更淡 */
  box-shadow: 0 4rpx 16rpx rgba(90, 120, 213, 0.4) !important; /* 统一阴影效果 */
}

.data-item.multi-select-mode.selected-item .media-btn.image.selected .btn-text,
.data-item.multi-select-mode.selected-item .media-btn.image.selected .btn-icon {
  color: #fff !important; /* 保持不变 - 白色文字 */
  font-weight: 500;
  opacity: 1 !important;
}

/* Styles for UNSELECTED media buttons when item IS selected (enabled state) */
.data-item.multi-select-mode.selected-item .media-btn.video:not(.selected),
.data-item.multi-select-mode.selected-item .media-btn.image:not(.selected) {
   background: linear-gradient(90deg, #c58eff 0%, #78b9ff 100%) !important; /* 保持相同的渐变色 */
   color: #fff !important;
   border-color: rgba(180, 170, 255, 0.3);
   opacity: 0.65; /* 降低不透明度 */
   filter: saturate(0.6) brightness(1.15); /* 降低饱和度，稍微提高亮度 */
   pointer-events: auto; /* 允许点击 */
   cursor: pointer;
   box-shadow: 0 2rpx 8rpx rgba(90, 120, 213, 0.2) !important; /* 较弱的阴影效果 */
}

/* 未选中状态下的文字和图标 */
.data-item.multi-select-mode.selected-item .media-btn:not(.selected) .btn-text {
    color: rgba(255, 255, 255, 0.9) !important; /* 稍微透明的白色文本 */
}

.data-item.multi-select-mode.selected-item .media-btn:not(.selected) .btn-icon {
    opacity: 0.9; /* 图标稍微淡化 */
    color: rgba(255, 255, 255, 0.9) !important;
}

/* Restore text/icon color for enabled but unselected buttons */
.data-item.multi-select-mode.selected-item .media-btn:not(.selected) .btn-text {
    color: #fff !important; /* 文本颜色为白色 */
}

.data-item.multi-select-mode.selected-item .media-btn:not(.selected) .btn-icon {
    opacity: 0.9; /* 图标基本完全可见 */
    color: white !important;
}

/* ----- 媒体按钮选中状态 END ----- */



/* 小屏幕强制修复 */
.small-screen .data-item.multi-select-mode .media-btn .btn-icon {
  width: 24rpx !important;
  height: 24rpx !important;
  margin-right: 6rpx !important;
}

.small-screen .data-item.multi-select-mode .media-btn .btn-text {
  font-size: 24rpx !important;
}





/* 自定义底部导航栏样式 */
.custom-tabbar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  height: 110rpx;
  background-color: #fff;
  display: flex;
  justify-content: space-around;
  align-items: center;
  border-top: 1rpx solid #f0f0f0;
  z-index: 1001;
  padding-bottom: env(safe-area-inset-bottom);
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.tab-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 33.33%;
  height: 100%;
  position: relative;
  transition: all 0.3s ease;
}

.tab-text {
  margin-top: 10rpx;
  font-size: 24rpx;
  color: #8a8a8a;
  transition: color 0.3s ease;
}

.tab-item.active .tab-text {
  color: #4a90e2;
  font-weight: 500;
}

/* 主界面图标 - 房子形状 */
.tab-icon-home {
  position: relative;
  width: 54rpx;
  height: 48rpx;
  display: flex;
  justify-content: center;
  align-items: center;
}

/* 房子屋顶 */
.tab-icon-home::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 0;
  height: 0;
  border-left: 27rpx solid transparent;
  border-right: 27rpx solid transparent;
  border-bottom: 24rpx solid #8a8a8a;
  transition: border-bottom-color 0.3s ease;
}

/* 房子主体 */
.tab-icon-home::after {
  content: '';
  position: absolute;
  top: 20rpx;
  left: 6rpx;
  width: 42rpx;
  height: 28rpx;
  background-color: #8a8a8a;
  border-radius: 0 0 4rpx 4rpx;
  transition: background-color 0.3s ease;
}

/* 房子门 */
.home-door {
  position: absolute;
  bottom: 0;
  left: 18rpx;
  width: 18rpx;
  height: 18rpx;
  background-color: white;
  border-radius: 3rpx 3rpx 0 0;
  z-index: 1;
}

/* 房子窗户 */
.home-window {
  position: absolute;
  top: 24rpx;
  left: 30rpx;
  width: 10rpx;
  height: 10rpx;
  background-color: white;
  border-radius: 2rpx;
  z-index: 1;
}

/* 烟囱 */
.home-chimney {
  position: absolute;
  top: 5rpx;
  right: 10rpx;
  width: 8rpx;
  height: 14rpx;
  background-color: #8a8a8a;
  border-radius: 2rpx 2rpx 0 0;
  z-index: 0;
  transition: background-color 0.3s ease;
}

/* 添加图标 */
.tab-icon-add {
  position: relative;
  width: 54rpx;
  height: 48rpx;
  display: flex;
  justify-content: center;
  align-items: center;
}

/* 设备外壳 */
.tab-icon-add::before {
  content: '';
  position: absolute;
  top: 6rpx;
  left: 10rpx;
  width: 34rpx;
  height: 40rpx;
  border-radius: 10rpx;
  background-color: #8a8a8a;
  transition: all 0.3s ease;
}

/* 设备屏幕 */
.tab-icon-add::after {
  content: '';
  position: absolute;
  top: 12rpx;
  left: 16rpx;
  width: 22rpx;
  height: 18rpx;
  border-radius: 4rpx;
  background-color: white;
  transition: all 0.3s ease;
}

/* 设备按钮 */
.add-connector {
  position: absolute;
  bottom: 12rpx;
  left: 24rpx;
  width: 6rpx;
  height: 6rpx;
  border-radius: 50%;
  background-color: white;
  transition: all 0.3s ease;
}

/* 横向按钮 */
.add-plus-horizontal {
  position: absolute;
  top: 22rpx;
  left: 20rpx;
  width: 14rpx;
  height: 2rpx;
  background-color: #8a8a8a;
  border-radius: 1rpx;
  transition: all 0.3s ease;
}

/* 纵向按钮 */
.add-plus-vertical {
  position: absolute;
  top: 16rpx;
  left: 26rpx;
  width: 2rpx;
  height: 14rpx;
  background-color: #8a8a8a;
  border-radius: 1rpx;
  transition: all 0.3s ease;
}

/* CSS 图标 - 我的 */
.tab-icon-me {
  position: relative;
  width: 54rpx;
  height: 48rpx;
  display: flex;
  justify-content: center;
  align-items: center;
}

/* 主圆 - 形成头像主体 */
.tab-icon-me::before {
  content: '';
  position: absolute;
  top: 3rpx;
  left: 12rpx;
  width: 30rpx;
  height: 30rpx;
  border-radius: 50%;
  background: linear-gradient(135deg, #9e9e9e, #707070);
  box-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.2);
  transition: all 0.3s ease;
}

/* 内部光晕 - 增加质感 */
.tab-icon-me::after {
  content: '';
  position: absolute;
  top: 7rpx;
  left: 16rpx;
  width: 10rpx;
  height: 10rpx;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.6);
  filter: blur(1rpx);
  transition: all 0.3s ease;
}

/* 装饰元素左 - 星形光点 */
.me-ear-left {
  position: absolute;
  top: 8rpx;
  left: 36rpx;
  width: 4rpx;
  height: 4rpx;
  border-radius: 50%;
  background-color: white;
  box-shadow: 0 0 4rpx rgba(255, 255, 255, 0.9);
  transition: all 0.3s ease;
}

/* 装饰元素右 - 更小的光点 */
.me-ear-right {
  position: absolute;
  top: 16rpx;
  left: 8rpx;
  width: 3rpx;
  height: 3rpx;
  border-radius: 50%;
  background-color: white;
  box-shadow: 0 0 3rpx rgba(255, 255, 255, 0.8);
  transition: all 0.3s ease;
}

/* 底部弧线 - 衬托头像 */
.me-arm-left {
  position: absolute;
  bottom: 4rpx;
  left: 14rpx;
  width: 26rpx;
  height: 14rpx;
  border-radius: 50%;
  border-bottom: 3rpx solid #8a8a8a;
  border-left: 3rpx solid transparent;
  border-right: 3rpx solid transparent;
  border-top: 3rpx solid transparent;
  box-sizing: border-box;
  transition: all 0.3s ease;
}

/* 底部装饰元素 - 增加层次感 */
.me-arm-right {
  position: absolute;
  bottom: 11rpx;
  left: 19rpx;
  width: 16rpx;
  height: 2rpx;
  background-color: #8a8a8a;
  border-radius: 2rpx;
  transition: all 0.3s ease;
}

/* 激活状态 */
.tab-item.active .tab-icon-me::before {
  background: linear-gradient(135deg, #4a90e2, #3674d0);
  box-shadow: 0 2rpx 8rpx rgba(74, 144, 226, 0.4);
}

.tab-item.active .tab-icon-me::after {
  background: rgba(255, 255, 255, 0.8);
  box-shadow: 0 0 6rpx rgba(255, 255, 255, 0.4);
}

.tab-item.active .me-ear-left,
.tab-item.active .me-ear-right {
  background-color: white;
  box-shadow: 0 0 6rpx rgba(255, 255, 255, 1);
}

.tab-item.active .me-arm-left {
  border-bottom-color: #4a90e2;
  box-shadow: 0 2rpx 4rpx rgba(74, 144, 226, 0.2);
}

.tab-item.active .me-arm-right {
  background-color: #4a90e2;
  box-shadow: 0 1rpx 3rpx rgba(74, 144, 226, 0.2);
}

/* 添加点击效果 */
.tab-item:active {
  opacity: 0.8;
}

/* 恢复底部空间占位符样式，并确保其高度正确，仅在多选模式生效 (通过 WXML 中的 wx:if 控制) */
.bottom-space-holder {
  /* 默认不占用空间 */
  height: 0;
  width: 100%;
  flex-shrink: 0;
  background-color: transparent;
  transition: height 0.4s cubic-bezier(0.05, 0.7, 0.1, 1);
  will-change: height;
}

/* 当.bottom-space-holder有.active类时，它才会占据实际高度 */
.bottom-space-holder.active {
  /* 精确计算为：多选按钮栏高度(88rpx) + 它的padding(60rpx) + 自定义Tab栏(110rpx) + 底部安全区域 */
  height: calc(88rpx + 60rpx + 110rpx + env(safe-area-inset-bottom));
  margin-bottom: 20rpx; /* 添加适当的底部边距以提供更好的视觉效果 */
  transition: height 0.4s cubic-bezier(0.05, 0.7, 0.1, 1);
}

/* 底部按钮区域基础样式 */
.bottom-buttons {
  transition: transform 0.7s cubic-bezier(0.22, 0.61, 0.36, 1),
              opacity 0.7s cubic-bezier(0.22, 0.61, 0.36, 1);
  will-change: transform, opacity, background-color;
}

/* 顶部的生成图表按钮样式 - 完全匹配视频/图片按钮尺寸 */
.cancel-btn-container .generate-btn {
  height: 60rpx !important;
  line-height: 60rpx !important;
  width: 110rpx !important;
  min-width: unset !important;
  flex: 0 0 auto !important;
  font-size: 22rpx !important;
  color: #fff !important;
  padding: 0 !important;
  border-radius: 30rpx !important;
  background: linear-gradient(90deg, #8E54E9, #4776E6) !important;
  box-shadow: 0 3rpx 8rpx rgba(142, 84, 233, 0.4) !important;
  margin: 0 !important;
  margin-left: 16rpx !important;
  letter-spacing: -0.5rpx !important;
  white-space: nowrap !important;
  overflow: hidden !important;
  text-overflow: ellipsis !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
}

/* 底部取消按钮样式 - 调整为与左侧其他两个按钮一致 */
.btn-group .cancel-btn {
  /* 删除所有单独的样式设置，使用通用按钮样式 */
  /* 已经在.btn-group .delete-btn, .btn-group .export-btn, .btn-group .cancel-btn中定义了共同样式 */
}

/* 底部取消按钮添加与其他底部按钮一致的高光效果 */
.btn-group .cancel-btn::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 50%;
  background: linear-gradient(to bottom, rgba(255, 255, 255, 0.3), rgba(255, 255, 255, 0));
  border-radius: 45rpx 45rpx 0 0;
}

/* 按钮组样式 - 确保平均分配空间 */
.btn-group {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  gap: 15rpx; /* 使用固定间距 */
}

/* 底部所有按钮的统一样式 */
.btn-group .delete-btn,
.btn-group .export-btn, 
.btn-group .cancel-btn {
  flex: 1 1 33.33%; /* 重要：强制每个按钮占据三分之一宽度 */
  min-width: 0; /* 覆盖可能影响等宽的min-width */
  max-width: none;
  height: 88rpx; /* 与单选模式的返回按钮保持一致 */
  line-height: 88rpx;
  text-align: center;
  border-radius: 44rpx;
  font-size: 30rpx;
  color: #fff;
  letter-spacing: 2rpx;
  font-weight: 500;
  position: relative;
  overflow: hidden;
  background: linear-gradient(90deg, #c58eff 0%, #78b9ff 100%);
  box-shadow: 0 4rpx 16rpx rgba(90, 120, 213, 0.4);
  margin: 0; /* 确保没有外边距影响等宽 */
  padding: 0; /* 确保内边距不影响宽度 */
  transform: translateZ(0); /* 促使GPU渲染 */
  backface-visibility: hidden; /* 提高动画性能 */
  will-change: transform, opacity;
  transition: transform 0.3s ease, box-shadow 0.3s ease, opacity 0.3s ease;
}

/* 多选模式下的区块样式 - 保持紧凑 */
.multi-select-mode .area-block {
  padding: 6rpx 10rpx;
  background: transparent; /* 从#fafafa改为透明，与周围背景一致 */
  border-radius: 8rpx;
  margin-right: 8rpx;
  align-items: center;
  width: 48%;
  overflow: hidden;
}

/* 改进单选模式下的区块布局 */
.data-item:not(.multi-select-mode) .area-block {
  padding: 8rpx 12rpx !important; /* 增加内边距 */
  height: auto !important; /* 确保高度自适应内容 */
  min-height: 44rpx !important; /* 设置更大的最小高度 */
  width: 46% !important; /* 稍微减小宽度，避免挤压 */
  margin-bottom: 8rpx !important; /* 增加底部间距 */
  align-items: flex-start !important; /* 确保内容从顶部开始 */
}

/* 改进整行布局 */
.data-item:not(.multi-select-mode) .detail-left {
  flex-wrap: wrap; /* 允许换行 */
  gap: 8rpx; /* 添加间距 */
  margin-bottom: 8rpx; /* 底部间距 */
}

/* 多选模式下的数值样式 - 单行显示带省略号 */
.multi-select-mode .detail-value {
  color: #333;
  font-size: 28rpx;
  min-width: 0;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  word-break: keep-all;
}

/* 单选模式下的数值样式 - 完整显示内容 */
.data-item:not(.multi-select-mode) .detail-value {
  color: #333;
  font-size: 28rpx;
  word-break: break-word; /* 允许在任何字符处换行 */
  white-space: normal; /* 恢复正常的文本换行行为 */
}

/* C区和T区的数值颜色差异化 */
.area-block.c-area .detail-value {
  color: #1989fa;
}

.area-block.t-area .detail-value {
  color: #07c160;
}

/* 数值点击展开效果 - 在点击时显示完整内容（适用于多选模式） */
.multi-select-mode .detail-value:active {
  white-space: normal;
  overflow: visible;
  text-overflow: clip;
  word-break: break-all;
  position: relative;
  z-index: 10;
  background: #f0f0f0;
  box-shadow: 0 0 5rpx rgba(0,0,0,0.1);
}

/* 确保单选模式下可以完整显示数值 */
.data-item:not(.multi-select-mode) .detail-value {
  white-space: normal !important; /* 强制正常换行 */
  word-break: break-word !important; /* 允许在任何字符处换行 */
  overflow: visible !important; /* 确保内容不被截断 */
  text-overflow: clip !important; /* 确保没有省略号 */
  min-height: 36rpx; /* 保证最小高度 */
  max-width: 100%; /* 允许使用最大宽度 */
  text-align: left; /* 左对齐文本更易读 */
}

/* 确保单选模式下完整显示数值 */
.data-item:not(.multi-select-mode) .detail-value {
  white-space: normal !important;
  word-break: break-word !important;
  overflow: visible !important;
  text-overflow: clip !important;
  line-height: 1.4 !important;
  font-size: 28rpx !important;
  text-align: left !important;
  padding-left: 4rpx !important;
  max-width: none !important; /* 移除最大宽度限制 */
}

/* 单选模式下区块布局调整 */
.data-item:not(.multi-select-mode) .detail-left {
  display: flex;
  flex-direction: column !important; /* 改为纵向排列 */
  width: 100% !important;
}

.data-item:not(.multi-select-mode) .area-block {
  width: 100% !important; /* 占据整行宽度 */
  margin-bottom: 10rpx !important;
  padding: 8rpx 12rpx !important;
}

/* 增强C区和T区的颜色区分 */
.area-block.c-area .detail-label {
  color: #0076ff;
  font-weight: 500;
}

.area-block.t-area .detail-label {
  color: #00a94f;
  font-weight: 500;
}

.area-block.c-area .detail-value {
  color: #1989fa;
}

.area-block.t-area .detail-value {
  color: #07c160;
}

/* 单选模式下增强颜色对比 */
.data-item:not(.multi-select-mode) .area-block.c-area {
  background-color: rgba(25, 137, 250, 0.05);
  border-left: 4rpx solid #1989fa;
}

.data-item:not(.multi-select-mode) .area-block.t-area {
  background-color: rgba(7, 193, 96, 0.05);
  border-left: 4rpx solid #07c160;
}

/* 删除单选模式下增强颜色对比的样式 */
.data-item:not(.multi-select-mode) .area-block.c-area {
  background-color: transparent; /* 移除特殊背景色 */
  border-left: none; /* 移除左侧边框 */
}

.data-item:not(.multi-select-mode) .area-block.t-area {
  background-color: transparent; /* 移除特殊背景色 */
  border-left: none; /* 移除左侧边框 */
}

/* 确保单选模式下的区块与多选模式一致 */
.data-item:not(.multi-select-mode) .area-block {
  background: transparent; /* 改为透明背景，与周围背景一致 */
  border-radius: 8rpx;
  margin-bottom: 10rpx !important;
  padding: 8rpx 12rpx !important;
}

/* 增强C区和T区文本颜色的优先级 */
.area-block.c-area .detail-value {
  color: #1989fa !important; /* 增加!important确保优先级 */
}

.area-block.t-area .detail-value {
  color: #07c160 !important; /* 增加!important确保优先级 */
}

/* 增强标签颜色优先级 */
.area-block.c-area .detail-label {
  color: #0076ff !important;
  font-weight: 500;
}

.area-block.t-area .detail-label {
  color: #00a94f !important;
  font-weight: 500;
}

/* 简化底部按钮区域样式 - 统一背景设置 */
.bottom-buttons.multi-select-mode {
  background: rgba(246, 244, 255, 0.9);
  backdrop-filter: blur(15rpx);
  border-top: 1rpx solid rgba(180, 170, 255, 0.2);
  box-shadow: 0 -4rpx 20rpx rgba(90, 120, 213, 0.1);
}

/* 确保按钮组背景透明 */
.bottom-buttons.multi-select-mode .btn-group {
  background: transparent;
}

/* 参数组样式 */
.param-group {
  margin-bottom: 24rpx;
  position: relative;
}

.param-group:last-child {
  margin-bottom: 0;
}

/* 优化参数组标题的空间 */
.param-group-title {
  font-size: 24rpx; /* 稍微减小字体 */
  color: #6b7280;
  margin-bottom: 12rpx; /* 减少与下方参数项的间距 */
  font-weight: 500;
  position: relative;
  padding-left: 16rpx;
  transform: translateX(20rpx) translateZ(0);
  opacity: 0;
  transition: transform 0.5s cubic-bezier(0.25, 1.05, 0.35, 1), opacity 0.5s cubic-bezier(0.25, 1.05, 0.35, 1);
  will-change: transform, opacity;
  backface-visibility: hidden;
}

.param-group-title::before {
  content: '';
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 6rpx;
  height: 20rpx;
  background: linear-gradient(to bottom, #4a90e2, #7c4dff);
  border-radius: 3rpx;
}

.icon-dot {
  display: inline-block;
  width: 8rpx; /* 减小点的大小 */
  height: 8rpx; /* 减小点的大小 */
  border-radius: 50%;
  background: linear-gradient(135deg, #4a90e2, #7c4dff);
  margin-right: 8rpx; /* 减少右侧间距 */
  transform: scale(0);
  transition: transform 0.7s cubic-bezier(0.18, 1.55, 0.4, 1);
}

.param-detail-label {
  display: flex;
  align-items: center;
}

/* 参数详情项优化 */
.param-detail-item {
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.03);
  margin-bottom: 16rpx;
  position: relative;
  overflow: hidden;
}

.param-detail-item::after {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  width: 30rpx;
  height: 30rpx;
  opacity: 0.05;
  background: linear-gradient(135deg, transparent, #4a90e2);
  border-bottom-left-radius: 30rpx;
}

/* 已在上方定义，此处删除重复样式 */

/* 已在上方定义，此处删除重复样式 */

/* 为每个参数项设置逐个显示的延迟 */
.param-detail-mask.show .param-detail-item:nth-child(2) { transition-delay: 0.08s; }
.param-detail-mask.show .param-detail-item:nth-child(3) { transition-delay: 0.12s; }
.param-detail-mask.show .param-detail-item:nth-child(4) { transition-delay: 0.16s; }
.param-detail-mask.show .param-detail-item:nth-child(5) { transition-delay: 0.20s; }
.param-detail-mask.show .param-detail-item:nth-child(6) { transition-delay: 0.24s; }
.param-detail-mask.show .param-detail-item:nth-child(7) { transition-delay: 0.28s; }
.param-detail-mask.show .param-detail-item:nth-child(8) { transition-delay: 0.32s; }
.param-detail-mask.show .param-detail-item:nth-child(9) { transition-delay: 0.36s; }
.param-detail-mask.show .param-detail-item:nth-child(10) { transition-delay: 0.40s; }
.param-detail-mask.show .param-detail-item:nth-child(11) { transition-delay: 0.44s; }
.param-detail-mask.show .param-detail-item:nth-child(12) { transition-delay: 0.48s; }
.param-detail-mask.show .param-detail-item:nth-child(13) { transition-delay: 0.52s; }
.param-detail-mask.show .param-detail-item:nth-child(14) { transition-delay: 0.56s; }
.param-detail-mask.show .param-detail-item:nth-child(15) { transition-delay: 0.60s; }
.param-detail-mask.show .param-detail-item:nth-child(16) { transition-delay: 0.64s; }
.param-detail-mask.show .param-detail-item:nth-child(17) { transition-delay: 0.68s; }
.param-detail-mask.show .param-detail-item:nth-child(18) { transition-delay: 0.72s; }

/* 组标题按顺序出现 */
.param-detail-mask.show .param-group-title {
  transform: translateX(0);
  opacity: 1;
}

.param-group:nth-child(1) .param-group-title { transition-delay: 0.03s; }
.param-group:nth-child(2) .param-group-title { transition-delay: 0.10s; }
.param-group:nth-child(3) .param-group-title { transition-delay: 0.17s; }

/* 调整每个组内参数的延迟，取消之前的固定延迟设置 */
.param-group:nth-child(1) .param-detail-item:nth-child(2) {
  transition-delay: 0.15s;
}

.param-group:nth-child(1) .param-detail-item:nth-child(3) {
  transition-delay: 0.22s;
}

.param-group:nth-child(2) .param-detail-item:nth-child(2) {
  transition-delay: 0.29s;
}

.param-group:nth-child(2) .param-detail-item:nth-child(3) {
  transition-delay: 0.36s;
}

.param-group:nth-child(2) .param-detail-item:nth-child(4) {
  transition-delay: 0.43s;
}

.param-group:nth-child(2) .param-detail-item:nth-child(5) {
  transition-delay: 0.50s;
}

.param-group:nth-child(3) .param-detail-item:nth-child(2) {
  transition-delay: 0.57s;
}

.param-group:nth-child(3) .param-detail-item:nth-child(3) {
  transition-delay: 0.64s;
}

.param-group:nth-child(3) .param-detail-item:nth-child(4) {
  transition-delay: 0.71s;
}

.param-group:nth-child(3) .param-detail-item:nth-child(5) {
  transition-delay: 0.78s;
}

/* 为每个参数项设置依次出现的动画 */
.param-detail-mask.show .param-detail-item {
  transform: translateX(0);
  opacity: 1;
}

/* 参数组标题也添加动画效果 */
.param-group-title {
  font-size: 26rpx;
  color: #6b7280;
  margin-bottom: 16rpx;
  font-weight: 500;
  position: relative;
  padding-left: 16rpx;
  transform: translateX(20rpx) translateZ(0); /* 加入translateZ强制GPU渲染 */
  opacity: 0;
  transition: transform 0.6s cubic-bezier(0.18, 1.25, 0.4, 1), opacity 0.6s cubic-bezier(0.18, 1.25, 0.4, 1);
  will-change: transform, opacity;
  backface-visibility: hidden;
}

.param-group-title::before {
  content: '';
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 6rpx;
  height: 20rpx;
  background: linear-gradient(to bottom, #4a90e2, #7c4dff);
  border-radius: 3rpx;
}

.param-group:nth-child(1) .param-group-title {
  transition-delay: 0.07s;
}

.param-group:nth-child(2) .param-group-title {
  transition-delay: 0.19s;
}

.param-group:nth-child(3) .param-group-title {
  transition-delay: 0.43s;
}

.param-detail-mask.show .param-group-title {
  transform: translateX(0) translateZ(0);
  opacity: 1;
}

/* 图标点样式 */
.icon-dot {
  display: inline-block;
  width: 8rpx;
  height: 8rpx;
  border-radius: 50%;
  background: linear-gradient(135deg, #4a90e2, #7c4dff);
  margin-right: 8rpx;
  transform: scale(0) rotate(90deg);
  transition: transform 0.5s cubic-bezier(0.34, 1.56, 0.64, 1);
  will-change: transform;
}

.param-detail-mask.show .icon-dot {
  transform: scale(1) rotate(0deg);
  transition-delay: 0.2s;
}

/* 添加自定义滚动条替代方案 */
.wx-scroll-view-custom-style {
  /* 自定义滚动条外观可以在JS中动态添加或通过组件内部样式实现 */
  scrollbar-width: thin; /* 这只是示例，微信小程序可能不支持 */
  overflow-y: auto;
  background-clip: padding-box;
}

/* 所有按钮的点击效果 */
.back-btn:active, .delete-btn:active, .export-btn:active, .generate-btn:active {
  transform: translateY(2rpx) scale(0.98);
  box-shadow: 0 2rpx 6rpx rgba(90, 120, 213, 0.2);
  opacity: 0.9;
}

/* 修改为: */
.bottom-btn-active {
  transform: translateY(2rpx) scale(0.98);
  box-shadow: 0 2rpx 6rpx rgba(90, 120, 213, 0.2);
  opacity: 0.9;
}

/* 确保多选模式下底部三个按钮等宽 */
.equal-width-btn {
  flex: 1 1 0 !important;
  width: calc(33.33% - 10rpx) !important; /* 考虑间距后的计算宽度 */
  min-width: 0 !important;
  max-width: none !important;
  box-sizing: border-box !important;
  padding: 0 !important;
  margin: 0 !important;
}

/* 确保卡片动画不被覆盖, 调整动画持续时间和曲线 */
.param-detail-mask.show .param-detail-card {
  animation: bounceScaleIn 0.4s cubic-bezier(0.34, 1.56, 0.64, 1) forwards;
  will-change: transform, opacity;
  transform-origin: center;
  animation-delay: 0.05s;
}

/* 隐藏时的动画，简化并加速 */
.param-detail-card.hiding {
  animation: bounceScaleOut 0.25s cubic-bezier(0.4, 0, 0.2, 1) forwards !important;
  will-change: transform, opacity;
  pointer-events: none;
}

/* Q弹进入退出动画 - 更加顺畅的弹性效果 */
@keyframes bounceScaleIn {
  0% {
    transform: scale(0.4) translateY(60rpx);
    opacity: 0;
  }
  60% {
    transform: scale(1.02) translateY(-3rpx);
    opacity: 0.9;
  }
  100% {
    transform: scale(1) translateY(0);
    opacity: 1;
  }
}

@keyframes bounceScaleOut {
  0% {
    transform: scale(1) translateY(0);
    opacity: 1;
  }
  100% {
    transform: scale(0.4) translateY(60rpx);
    opacity: 0;
  }
}

/* 单选模式下的按钮容器 */
.single-mode-buttons {
  display: flex;
  justify-content: space-between;
  width: 90%;
  gap: 20rpx;
}

/* 返回按钮样式 - 占2/3宽度 */
.back-btn {
  flex: 2;
  height: 88rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  color: #fff;
  font-size: 32rpx;
  font-weight: 500;
  letter-spacing: 4rpx;
  background: linear-gradient(90deg, #78b9ff 0%, #c58eff 100%);
  border-radius: 44rpx;
  box-shadow: 0 6rpx 16rpx rgba(90, 120, 213, 0.3), 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
  position: relative;
  overflow: hidden;
  transition: all 0.3s ease;
  border: 1rpx solid rgba(255, 255, 255, 0.25);
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* 选择按钮样式 - 占1/3宽度 */
.select-btn {
  flex: 1;
  height: 88rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  color: #fff;
  font-size: 32rpx;
  font-weight: 500;
  letter-spacing: 4rpx;
  background: linear-gradient(90deg, #78b9ff 0%, #c58eff 100%);
  border-radius: 44rpx;
  box-shadow: 0 6rpx 16rpx rgba(90, 120, 213, 0.3), 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
  position: relative;
  overflow: hidden;
  transition: all 0.3s ease;
  border: 1rpx solid rgba(255, 255, 255, 0.25);
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* 按钮内部高光效果 */
.back-btn::before,
.select-btn::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 50%;
  border-radius: 44rpx 44rpx 100rpx 100rpx / 44rpx 44rpx 60rpx 60rpx;
  background: linear-gradient(180deg, rgba(255, 255, 255, 0.35) 0%, rgba(255, 255, 255, 0) 100%);
  opacity: 1;
  z-index: 2;
  pointer-events: none;
}

/* 按钮外发光效果 */
.back-btn::after,
.select-btn::after {
  content: "";
  position: absolute;
  top: -1rpx;
  left: -1rpx;
  right: -1rpx;
  bottom: -1rpx;
  background: linear-gradient(90deg, #78b9ff, #c58eff);
  z-index: -1;
  border-radius: 45rpx;
  filter: blur(5rpx);
  opacity: 0.7;
  will-change: opacity;
  transition: opacity 0.2s ease, filter 0.2s ease;
}

/* 按钮点击效果 */
.back-btn:active,
.select-btn:active {
  transform: translateY(3rpx) scale(0.98);
  box-shadow: 0 2rpx 6rpx rgba(90, 120, 213, 0.2), 0 1rpx 2rpx rgba(0, 0, 0, 0.1);
  transition: all 0.1s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  border-color: rgba(255, 255, 255, 0.15);
}

.back-btn:active::before,
.select-btn:active::before {
  opacity: 0.6;
  background: linear-gradient(180deg, rgba(255, 255, 255, 0.15) 0%, rgba(255, 255, 255, 0) 100%);
}

.back-btn:active::after,
.select-btn:active::after {
  opacity: 0.5;
  filter: blur(3rpx);
}





/* 最高优先级的居中修复 - 覆盖所有可能的冲突 */
.data-item.multi-select-mode.selected-item .media-btn.video .btn-text,
.data-item.multi-select-mode.selected-item .media-btn.image .btn-text,
.data-item.multi-select-mode.selected-item .media-btn.video.selected .btn-text,
.data-item.multi-select-mode.selected-item .media-btn.image.selected .btn-text,
.data-item.multi-select-mode.selected-item .media-btn:not(.selected) .btn-text,
.data-item.multi-select-mode:not(.selected-item) .media-btn .btn-text,
.data-item.multi-select-mode .media-btn .btn-text {
  font-size: 26rpx !important;
  white-space: nowrap !important;
  text-align: center !important;
  line-height: 70rpx !important;
  height: 70rpx !important;
  margin: 0 !important;
  padding: 0 !important;
  flex-shrink: 0 !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  vertical-align: middle !important;
  box-sizing: border-box !important;
}

/* 保存进度卡片样式 - 参考参数详情卡片的精美设计 */
.save-progress-mask {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100vh;
  background: rgba(0, 0, 0, 0);
  z-index: 3000;
  display: flex;
  justify-content: center;
  align-items: center;
  transition: all 0.6s cubic-bezier(0.16, 1, 0.3, 1);
  opacity: 0;
  visibility: hidden;
  backdrop-filter: blur(0px);
  will-change: opacity, backdrop-filter, background;
}

.save-progress-mask.show {
  opacity: 1;
  visibility: visible;
  background: rgba(0, 0, 0, 0.4);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
}

.save-progress-card {
  width: 720rpx;
  background: linear-gradient(135deg, #ffffff, #f8f9fa);
  border-radius: 32rpx;
  padding: 0;
  box-shadow: 0 20rpx 60rpx rgba(0, 0, 0, 0.3), 0 4rpx 12rpx rgba(0, 0, 0, 0.15), inset 0 0 0 2rpx rgba(255, 255, 255, 0.9);
  position: relative;
  opacity: 0;
  border: none;
  overflow: hidden;
  will-change: transform, opacity;
  /* 初始状态 */
  transform: scale(0.8) translateY(30rpx);
  /* 强制每次都重新应用动画 */
  animation: none;
  backface-visibility: hidden;
  perspective: 1000;
  /* 添加内部阴影，解决边缘白边问题 */
  box-sizing: border-box;
  outline: none;
  -webkit-mask-image: -webkit-radial-gradient(white, black); /* 修复Safari中的白边问题 */
}

/* 保存进度卡片显示状态 */
.save-progress-card.show {
  opacity: 1;
  transform: scale(1) translateY(0);
  animation: card-pop-in 0.4s cubic-bezier(0.34, 1.56, 0.64, 1) forwards;
}

/* 卡片弹出动画 */
@keyframes card-pop-in {
  0% {
    opacity: 0;
    transform: scale(0.8) translateY(30rpx);
  }
  100% {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

/* 保存进度卡片头部 - 完全参考参数详情卡片的渐变设计 */
.save-progress-header {
  background: linear-gradient(135deg, #4a90e2 0%, #7c4dff 50%, #9c27b0 100%);
  padding: 40rpx 45rpx 30rpx;
  position: relative;
  overflow: hidden;
}

/* 头部装饰背景 - 更精美的光效 */
.save-progress-header::before {
  content: '';
  position: absolute;
  top: -50%;
  right: -20%;
  width: 300rpx;
  height: 300rpx;
  background: radial-gradient(circle, rgba(255, 255, 255, 0.15) 0%, rgba(255, 255, 255, 0.05) 50%, transparent 70%);
  border-radius: 50%;
  animation: headerGlow 4s infinite alternate;
}

.save-progress-header::after {
  content: '';
  position: absolute;
  bottom: -40%;
  left: -15%;
  width: 250rpx;
  height: 250rpx;
  background: radial-gradient(circle, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.03) 50%, transparent 70%);
  border-radius: 50%;
  animation: headerGlow 5s infinite alternate-reverse;
}

@keyframes headerGlow {
  0% {
    opacity: 0.4;
    transform: scale(1) rotate(0deg);
  }
  100% {
    opacity: 0.7;
    transform: scale(1.2) rotate(10deg);
  }
}

.save-progress-title {
  font-size: 38rpx;
  color: #ffffff;
  text-align: center;
  font-weight: 600;
  position: relative;
  letter-spacing: 1rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 15rpx;
  margin: 0;
  z-index: 2;
}

/* 保存进度内容区域 - 参考参数详情卡片的布局 */
.save-progress-content {
  padding: 40rpx;
  background: #ffffff;
  position: relative;
}

.save-progress-title::before {
  content: '';
  width: 8rpx;
  height: 8rpx;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 50%;
  box-shadow: 0 0 12rpx rgba(255, 255, 255, 0.4);
  animation: titlePulse 2s infinite;
}

@keyframes titlePulse {
  0%, 100% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.2);
    opacity: 0.8;
  }
}

/* 进度条容器 - 更精美的设计 */
.save-progress-bar-container {
  width: 100%;
  height: 24rpx;
  background: linear-gradient(145deg, rgba(246, 244, 255, 0.9), rgba(241, 239, 252, 0.9));
  border-radius: 12rpx;
  overflow: hidden;
  position: relative;
  border: 1rpx solid rgba(180, 170, 255, 0.3);
  box-shadow: inset 0 2rpx 6rpx rgba(90, 120, 213, 0.15), 0 2rpx 8rpx rgba(124, 77, 255, 0.1);
  margin: 25rpx 0;
}

.save-progress-bar {
  height: 100%;
  background: linear-gradient(90deg, #4a90e2 0%, #7c4dff 50%, #9c27b0 100%);
  border-radius: 12rpx;
  transition: width 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  position: relative;
  box-shadow: 0 4rpx 16rpx rgba(124, 77, 255, 0.4);
}

.save-progress-bar::before {
  content: '';
  position: absolute;
  top: 2rpx;
  left: 2rpx;
  right: 2rpx;
  height: 6rpx;
  background: linear-gradient(90deg, rgba(255, 255, 255, 0.6), rgba(255, 255, 255, 0.2));
  border-radius: 8rpx;
}

.save-progress-bar::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.5), transparent);
  animation: progressShine 2.5s infinite;
  border-radius: 12rpx;
}

@keyframes progressShine {
  0% {
    transform: translateX(-100%);
    opacity: 0;
  }
  50% {
    opacity: 1;
  }
  100% {
    transform: translateX(100%);
    opacity: 0;
  }
}

.save-progress-text {
  font-size: 32rpx;
  color: #555;
  text-align: center;
  line-height: 1.5;
  font-weight: 500;
  letter-spacing: 0.3px;
}

/* 进度信息分组 - 参考参数详情卡片的分组样式 */
.save-progress-group {
  margin-bottom: 30rpx;
}

.save-progress-group-title {
  font-size: 28rpx;
  color: #7c4dff;
  font-weight: 600;
  margin-bottom: 20rpx;
  padding-left: 20rpx;
  position: relative;
  letter-spacing: 0.5rpx;
}

.save-progress-group-title::before {
  content: '';
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 6rpx;
  height: 20rpx;
  background: linear-gradient(135deg, #4a90e2, #7c4dff);
  border-radius: 3rpx;
}

/* 进度项样式 - 参考参数详情卡片的项目样式 */
.save-progress-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16rpx 15rpx 16rpx 20rpx;
  margin-bottom: 12rpx;
  border-radius: 16rpx;
  position: relative;
  background: #f8f9fc;
  border-left: 4rpx solid transparent;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.03);
  overflow: hidden;
}

/* 不同进度项的左边框颜色 */
.save-progress-item:nth-child(2) {
  border-left-color: #4a90e2;
}

.save-progress-item:nth-child(4) {
  border-left-color: #7c4dff;
}

/* 进度项装饰效果 */
.save-progress-item::after {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  width: 30rpx;
  height: 30rpx;
  opacity: 0.05;
  background: linear-gradient(135deg, transparent, #4a90e2);
  border-bottom-left-radius: 30rpx;
}

.save-progress-percentage {
  font-size: 56rpx;
  font-weight: 700;
  background: linear-gradient(135deg, #4a90e2, #7c4dff);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-align: center;
  position: relative;
}

.save-progress-percentage::after {
  content: '';
  position: absolute;
  bottom: -8rpx;
  left: 50%;
  transform: translateX(-50%);
  width: 40rpx;
  height: 2rpx;
  background: linear-gradient(90deg, #4a90e2, #6ba8f7);
  border-radius: 1rpx;
  opacity: 0.6;
}

/* 当前任务显示 - 参考参数详情卡片的项目样式 */
.save-progress-current-task {
  font-size: 26rpx;
  color: #555;
  min-height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  line-height: 1.4;
  padding: 16rpx 15rpx 16rpx 20rpx;
  background: #f8f9fc;
  border-radius: 16rpx;
  border-left: 4rpx solid #7c4dff;
  position: relative;
  overflow: hidden;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.03);
  margin-top: 20rpx;
}

/* 当前任务装饰效果 - 参考参数详情卡片 */
.save-progress-current-task::after {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  width: 30rpx;
  height: 30rpx;
  opacity: 0.05;
  background: linear-gradient(135deg, transparent, #7c4dff);
  border-bottom-left-radius: 30rpx;
}

/* 进度项标签样式 */
.save-progress-label {
  display: flex;
  align-items: center;
  font-size: 26rpx;
  color: #666;
  font-weight: 500;
}

/* 进度项数值样式 */
.save-progress-value {
  font-size: 24rpx;
  color: #2c3e50;
  font-weight: 600;
  text-align: right;
  padding: 6rpx 15rpx;
  background: rgba(124, 77, 255, 0.03);
  border-radius: 30rpx;
  border: 1rpx solid rgba(124, 77, 255, 0.1);
  box-shadow: 0 2rpx 4rpx rgba(124, 77, 255, 0.03);
}

/* 图标点样式 - 参考参数详情卡片 */
.save-progress-content .icon-dot {
  display: inline-block;
  width: 8rpx;
  height: 8rpx;
  border-radius: 50%;
  background: linear-gradient(135deg, #4a90e2, #7c4dff);
  margin-right: 8rpx;
  transform: scale(1);
  transition: transform 0.7s cubic-bezier(0.18, 1.55, 0.4, 1);
}

.save-progress-icon {
  width: 90rpx;
  height: 90rpx;
  margin-bottom: 30rpx;
  animation: saveIconFloat 3s infinite;
  filter: drop-shadow(0 6rpx 16rpx rgba(74, 144, 226, 0.25));
  position: relative;
}

.save-progress-icon::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 120rpx;
  height: 120rpx;
  background: radial-gradient(circle, rgba(74, 144, 226, 0.1) 0%, transparent 70%);
  border-radius: 50%;
  animation: iconGlow 2s infinite alternate;
  z-index: -1;
}

@keyframes saveIconFloat {
  0%, 100% {
    transform: translateY(0) scale(1);
    opacity: 1;
  }
  50% {
    transform: translateY(-8rpx) scale(1.05);
    opacity: 0.9;
  }
}

@keyframes iconGlow {
  0% {
    opacity: 0.3;
    transform: translate(-50%, -50%) scale(1);
  }
  100% {
    opacity: 0.6;
    transform: translate(-50%, -50%) scale(1.1);
  }
}

/* 保存结果卡片 - 与保存进度卡片保持一致的设计 */
.save-results-card {
  width: 720rpx;
  background: linear-gradient(135deg, #ffffff, #f8f9fa);
  border-radius: 32rpx;
  padding: 0;
  box-shadow: 0 20rpx 60rpx rgba(0, 0, 0, 0.3), 0 4rpx 12rpx rgba(0, 0, 0, 0.15), inset 0 0 0 2rpx rgba(255, 255, 255, 0.9);
  position: relative;
  opacity: 0;
  border: none;
  overflow: hidden;
  will-change: transform, opacity;
  /* 初始状态 */
  transform: scale(0.8) translateY(30rpx);
  /* 强制每次都重新应用动画 */
  animation: none;
  backface-visibility: hidden;
  perspective: 1000;
  /* 添加内部阴影，解决边缘白边问题 */
  box-sizing: border-box;
  outline: none;
  -webkit-mask-image: -webkit-radial-gradient(white, black);
}

/* 保存结果卡片显示状态 */
.save-results-card.show {
  opacity: 1;
  transform: scale(1) translateY(0);
  animation: card-pop-in 0.4s cubic-bezier(0.34, 1.56, 0.64, 1) forwards;
}

/* 保存结果卡片头部 - 使用与参数详情卡片头部一致的颜色 */
.save-results-header {
  background: linear-gradient(135deg, #4a90e2, #7c4dff);
  padding: 40rpx 45rpx 30rpx;
  position: relative;
  overflow: hidden;
}

/* 头部装饰背景 */
.save-results-header::before {
  content: '';
  position: absolute;
  top: -50%;
  right: -20%;
  width: 300rpx;
  height: 300rpx;
  background: radial-gradient(circle, rgba(255, 255, 255, 0.15) 0%, rgba(255, 255, 255, 0.05) 50%, transparent 70%);
  border-radius: 50%;
  animation: headerGlow 4s infinite alternate;
}

.save-results-header::after {
  content: '';
  position: absolute;
  bottom: -40%;
  left: -15%;
  width: 250rpx;
  height: 250rpx;
  background: radial-gradient(circle, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.03) 50%, transparent 70%);
  border-radius: 50%;
  animation: headerGlow 5s infinite alternate-reverse;
}

.save-results-title {
  font-size: 38rpx;
  color: #ffffff;
  text-align: center;
  font-weight: 600;
  position: relative;
  letter-spacing: 1rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 15rpx;
  margin: 0;
  z-index: 2;
}

.save-results-title::before {
  content: '';
  width: 8rpx;
  height: 8rpx;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 50%;
  box-shadow: 0 0 12rpx rgba(255, 255, 255, 0.4);
  animation: titlePulse 2s infinite;
}

/* 保存结果内容区域 */
.save-results-content {
  padding: 40rpx;
  background: #ffffff;
  position: relative;
}

/* 保存结果分组样式 */
.save-results-group {
  margin-bottom: 30rpx;
}

.save-results-group-title {
  font-size: 28rpx;
  color: #4a90e2;
  font-weight: 600;
  margin-bottom: 20rpx;
  padding-left: 20rpx;
  position: relative;
  letter-spacing: 0.5rpx;
}

.save-results-group-title.error {
  color: #ff4757;
}

.save-results-group-title::before {
  content: '';
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 6rpx;
  height: 20rpx;
  background: linear-gradient(135deg, #4a90e2, #7c4dff);
  border-radius: 3rpx;
}

.save-results-group-title.error::before {
  background: linear-gradient(135deg, #ff4757, #ff6b7a);
}

/* 保存结果项样式 */
.save-results-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16rpx 15rpx 16rpx 20rpx;
  margin-bottom: 12rpx;
  border-radius: 16rpx;
  position: relative;
  background: #f8f9fc;
  border-left: 4rpx solid transparent;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.03);
  overflow: hidden;
}

/* 不同结果项的左边框颜色 */
.save-results-item:nth-child(2) {
  border-left-color: #4a90e2;
}

.save-results-item:nth-child(3) {
  border-left-color: #5a9de8;
}

.save-results-item:nth-child(4) {
  border-left-color: #7c4dff;
}

/* 结果项装饰效果 */
.save-results-item::after {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  width: 30rpx;
  height: 30rpx;
  opacity: 0.05;
  background: linear-gradient(135deg, transparent, #4a90e2);
  border-bottom-left-radius: 30rpx;
}

/* 保存结果标签样式 */
.save-results-label {
  display: flex;
  align-items: center;
  font-size: 26rpx;
  color: #666;
  font-weight: 500;
}

/* 保存结果数值样式 */
.save-results-value {
  font-size: 24rpx;
  color: #2c3e50;
  font-weight: 600;
  text-align: right;
  padding: 6rpx 15rpx;
  background: rgba(74, 144, 226, 0.03);
  border-radius: 30rpx;
  border: 1rpx solid rgba(74, 144, 226, 0.1);
  box-shadow: 0 2rpx 4rpx rgba(74, 144, 226, 0.03);
}

/* 错误项样式 */
.save-results-error-item {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 16rpx 15rpx 16rpx 20rpx;
  margin-bottom: 12rpx;
  border-radius: 16rpx;
  position: relative;
  background: rgba(255, 245, 245, 0.8);
  border-left: 4rpx solid #ff4757;
  box-shadow: 0 2rpx 8rpx rgba(255, 71, 87, 0.1);
  overflow: hidden;
}

.save-results-error-label {
  display: flex;
  align-items: center;
  font-size: 26rpx;
  color: #ff4757;
  font-weight: 500;
  flex-shrink: 0;
}

.save-results-error-value {
  font-size: 22rpx;
  color: #ff4757;
  text-align: right;
  line-height: 1.4;
  margin-left: 20rpx;
  word-break: break-all;
}

/* 图标点样式 - 保存结果 */
.save-results-content .icon-dot {
  display: inline-block;
  width: 8rpx;
  height: 8rpx;
  border-radius: 50%;
  background: linear-gradient(135deg, #4a90e2, #7c4dff);
  margin-right: 8rpx;
  transform: scale(1);
  transition: transform 0.7s cubic-bezier(0.18, 1.55, 0.4, 1);
}

.save-results-content .icon-dot.error {
  background: linear-gradient(135deg, #ff4757, #ff6b7a);
}

/* 旧样式已删除，使用新的保存结果项样式 */

/* 确定按钮样式 */
.save-results-close-btn {
  width: 100%;
  height: 100rpx;
  background: linear-gradient(135deg, #4a90e2 0%, #7c4dff 100%);
  color: white;
  border: none;
  border-radius: 20rpx;
  font-size: 32rpx;
  font-weight: 600;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  box-shadow: 0 8rpx 20rpx rgba(74, 144, 226, 0.3);
  position: relative;
  overflow: hidden;
  letter-spacing: 0.5px;
  margin-top: 30rpx;
}

.save-results-close-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 50%;
  background: linear-gradient(180deg, rgba(255, 255, 255, 0.2), transparent);
  border-radius: 20rpx 20rpx 0 0;
}

.save-results-close-btn:active {
  transform: scale(0.98) translateY(2rpx);
  box-shadow: 0 4rpx 12rpx rgba(74, 144, 226, 0.2);
}

.save-results-close-btn:hover {
  box-shadow: 0 12rpx 28rpx rgba(74, 144, 226, 0.4);
  transform: translateY(-2rpx);
}

/* 模式切换动画 */
.data-container {
  transition: opacity 0.5s ease-out, transform 0.5s ease-out;
  will-change: opacity, transform;
}

.data-container.mode-transition {
  opacity: 0;
  transform: translateY(-20rpx);
}

/* 添加标题渐入动画 */
@keyframes header-fade-in {
  0% {
    opacity: 0;
    transform: translateY(-10rpx);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 添加内容渐入动画 */
@keyframes content-fade-in {
  0% {
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}
  
/* Q弹子元素动画效果 - 更丝滑缓慢 */
.param-detail-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16rpx 15rpx 16rpx 20rpx;
  margin-bottom: 12rpx;
  border-radius: 16rpx;
  position: relative;
  background: #f8f9fc;
  border-left: 4rpx solid transparent;
  opacity: 0;
  transform: scale(0.7) translateY(20rpx) translateX(15rpx);
  will-change: transform, opacity;
  transition: opacity 0.4s cubic-bezier(0.05, 0.7, 0.1, 1), transform 0.4s cubic-bezier(0.34, 1.56, 0.64, 1);
  backface-visibility: hidden;
  contain: content;
}
