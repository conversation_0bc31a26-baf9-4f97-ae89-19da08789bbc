{"version": 3, "sources": ["ajv.js", "compile/index.js", "compile/resolve.js", "compile/util.js", "compile/ucs2length.js", "compile/schema_obj.js", "compile/error_classes.js", "dotjs/validate.js", "cache.js", "compile/formats.js", "compile/rules.js", "dotjs/index.js", "dotjs/ref.js", "dotjs/allOf.js", "dotjs/anyOf.js", "dotjs/comment.js", "dotjs/const.js", "dotjs/contains.js", "dotjs/dependencies.js", "dotjs/enum.js", "dotjs/format.js", "dotjs/if.js", "dotjs/items.js", "dotjs/_limit.js", "dotjs/_limitItems.js", "dotjs/_limitLength.js", "dotjs/_limitProperties.js", "dotjs/multipleOf.js", "dotjs/not.js", "dotjs/oneOf.js", "dotjs/pattern.js", "dotjs/properties.js", "dotjs/propertyNames.js", "dotjs/required.js", "dotjs/uniqueItems.js", "data.js", "compile/async.js", "keyword.js", "dotjs/custom.js", "definition_schema.js", "refs/json-schema-draft-07.json", "refs/data.json"], "names": [], "mappings": ";;;;;;;AAAA;AACA;AACA;AACA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA,ACHA;AFOA,ACHA,ACHA;AFOA,ACHA,ACHA;AFOA,ACHA,ACHA,ACHA;AHUA,ACHA,ACHA,ACHA;AHUA,ACHA,ACHA,ACHA;AHUA,ACHA,ACHA,AENA,ADGA;AHUA,ACHA,ACHA,AENA,ADGA;AHUA,ACHA,ACHA,AENA,ADGA;AHUA,ACHA,ACHA,AGTA,ADGA,ADGA;AHUA,ACHA,ACHA,AGTA,ADGA,ADGA;AHUA,ACHA,ACHA,AGTA,ADGA,ADGA;AHUA,AMlBA,ALeA,ACHA,AGTA,ADGA,ADGA;AHUA,AMlBA,ALeA,ACHA,AGTA,ADGA,ADGA;AHUA,AMlBA,ALeA,ACHA,AGTA,ADGA,ADGA;AHUA,AMlBA,ALeA,ACHA,AGTA,ADGA,ADGA,AIZA;APsBA,AMlBA,ALeA,ACHA,AGTA,ADGA,ADGA,AIZA;APsBA,AMlBA,ALeA,ACHA,AGTA,ADGA,ADGA,AIZA;APsBA,AQxBA,AFMA,ALeA,ACHA,AGTA,ADGA,ADGA,AIZA;APsBA,AQxBA,AFMA,ALeA,ACHA,AENA,ADGA,AIZA;APsBA,AQxBA,AFMA,ALeA,ACHA,AENA,ADGA,AIZA;APsBA,AQxBA,AFMA,AGTA,ARwBA,ACHA,AENA,ADGA,AIZA;APsBA,AQxBA,AFMA,AGTA,ARwBA,ACHA,AENA,ADGA,AIZA;APsBA,AQxBA,AFMA,AGTA,ARwBA,ACHA,AENA,ADGA,AIZA;APsBA,AQxBA,AFMA,AGTA,ARwBA,ACHA,AQxBA,ANkBA,ADGA,AIZA;APsBA,AQxBA,AFMA,AGTA,ARwBA,ACHA,AQxBA,ANkBA,ADGA,AIZA;APsBA,AQxBA,AFMA,AGTA,ARwBA,ACHA,AQxBA,ANkBA,ADGA,AIZA;APsBA,AQxBA,AFMA,AGTA,ARwBA,ACHA,AQxBA,APqBA,AQxBA,AJYA;APsBA,AQxBA,AFMA,AGTA,ARwBA,ACHA,AQxBA,APqBA,AQxBA,AJYA;APsBA,AQxBA,AFMA,AGTA,ARwBA,ACHA,AQxBA,APqBA,AQxBA,AJYA;APsBA,AQxBA,AFMA,AGTA,ARwBA,ACHA,AQxBA,APqBA,AQxBA,ACHA,ALeA;APsBA,AQxBA,AFMA,AGTA,ARwBA,ACHA,AQxBA,APqBA,AQxBA,ACHA,ALeA;APsBA,AQxBA,AFMA,AGTA,ARwBA,ACHA,AQxBA,APqBA,AQxBA,ACHA,ALeA;APsBA,AQxBA,AFMA,AGTA,ARwBA,ACHA,AQxBA,APqBA,AU9BA,AFMA,ACHA,ALeA;APsBA,AQxBA,AFMA,AGTA,ARwBA,ACHA,AQxBA,APqBA,AU9BA,AFMA,ACHA,ALeA;APsBA,AQxBA,AFMA,AGTA,ARwBA,ACHA,AQxBA,APqBA,AU9BA,AFMA,ACHA,ALeA;APsBA,AQxBA,AFMA,AGTA,ARwBA,ACHA,AQxBA,APqBA,AU9BA,ACHA,AHSA,ACHA,ALeA;APsBA,AQxBA,AFMA,AGTA,ARwBA,ACHA,AQxBA,APqBA,AU9BA,ACHA,AHSA,ACHA,ALeA;APsBA,AQxBA,AFMA,AGTA,ARwBA,ACHA,AQxBA,APqBA,AU9BA,ACHA,AHSA,ACHA,ALeA;APsBA,AQxBA,AFMA,AGTA,ARwBA,ACHA,AQxBA,APqBA,AU9BA,ACHA,ACHA,AJYA,ACHA,ALeA;APsBA,AQxBA,AFMA,AGTA,ARwBA,ACHA,AQxBA,APqBA,AU9BA,ACHA,ACHA,AJYA,ACHA,ALeA;APsBA,AQxBA,AFMA,AGTA,ARwBA,ACHA,AQxBA,APqBA,AU9BA,ACHA,ACHA,AJYA,ACHA,ALeA;APsBA,AQxBA,AFMA,AGTA,ARwBA,ACHA,AQxBA,APqBA,AU9BA,ACHA,ACHA,ACHA,ALeA,ACHA,ALeA;APsBA,AQxBA,AFMA,AGTA,ARwBA,ACHA,AQxBA,APqBA,AU9BA,ACHA,ACHA,ACHA,ALeA,ACHA,ALeA;APsBA,AQxBA,AFMA,AGTA,ARwBA,ACHA,AQxBA,APqBA,AU9BA,ACHA,ACHA,ACHA,ALeA,ACHA,ALeA;APsBA,AMlBA,AGTA,ARwBA,ACHA,AQxBA,APqBA,AU9BA,ACHA,ACHA,ACHA,ACHA,ANkBA,ACHA,ALeA;APsBA,AMlBA,AGTA,ARwBA,ACHA,AQxBA,APqBA,AU9BA,ACHA,ACHA,ACHA,ACHA,ANkBA,ACHA,ALeA;APsBA,AS3BA,ARwBA,ACHA,AQxBA,APqBA,AU9BA,ACHA,ACHA,ACHA,ACHA,ANkBA,ACHA,ALeA;APsBA,AS3BA,ARwBA,ACHA,AQxBA,APqBA,AU9BA,ACHA,ACHA,ACHA,ACHA,ACHA,APqBA,ACHA,ALeA;APsBA,AS3BA,ARwBA,ACHA,AQxBA,APqBA,AU9BA,ACHA,ACHA,ACHA,ACHA,ACHA,APqBA,ACHA,ALeA;APsBA,AS3BA,ARwBA,ACHA,AQxBA,APqBA,AU9BA,ACHA,ACHA,ACHA,ACHA,ACHA,APqBA,ACHA,ALeA;APsBA,AS3BA,ARwBA,ACHA,AQxBA,APqBA,AU9BA,ACHA,ACHA,ACHA,ACHA,ACHA,ACHA,ARwBA,ACHA,ALeA;APsBA,AS3BA,ARwBA,ACHA,AQxBA,APqBA,AU9BA,ACHA,ACHA,ACHA,ACHA,ACHA,ACHA,ARwBA,ACHA,ALeA;APsBA,AS3BA,ARwBA,ACHA,AQxBA,APqBA,AU9BA,ACHA,ACHA,ACHA,ACHA,ACHA,ACHA,ARwBA,ACHA,ALeA;APsBA,AS3BA,ARwBA,ACHA,AQxBA,APqBA,AU9BA,ACHA,AENA,ACHA,ACHA,ACHA,ACHA,AT2BA,ACHA,ALeA;APsBA,AS3BA,ARwBA,ACHA,AQxBA,APqBA,AU9BA,ACHA,AENA,ACHA,ACHA,ACHA,ACHA,AT2BA,ACHA,ALeA;APsBA,AS3BA,ARwBA,ACHA,AQxBA,APqBA,AU9BA,ACHA,AENA,ACHA,ACHA,ACHA,ACHA,AT2BA,ACHA,ALeA;APsBA,AS3BA,ARwBA,ACHA,AQxBA,APqBA,AU9BA,ACHA,AENA,ACHA,ACHA,ACHA,ACHA,ACHA,AV8BA,ACHA,ALeA;APsBA,AS3BA,ARwBA,ACHA,AQxBA,APqBA,AU9BA,ACHA,AENA,ACHA,ACHA,ACHA,ACHA,ACHA,AV8BA,ACHA,ALeA;APsBA,AS3BA,ARwBA,ACHA,AQxBA,APqBA,AU9BA,ACHA,AENA,ACHA,ACHA,ACHA,ACHA,ACHA,AV8BA,ACHA,ALeA;APsBA,AS3BA,ARwBA,ACHA,AQxBA,APqBA,AU9BA,ACHA,AENA,ACHA,ACHA,ACHA,ACHA,ACHA,AV8BA,AWjCA,AV8BA,ALeA;APsBA,AS3BA,ARwBA,ACHA,AQxBA,APqBA,AU9BA,ACHA,AENA,ACHA,ACHA,ACHA,ACHA,ACHA,ACHA,AV8BA,ALeA;APsBA,AS3BA,ARwBA,ACHA,AQxBA,APqBA,AU9BA,ACHA,AENA,ACHA,ACHA,ACHA,ACHA,ACHA,ACHA,AV8BA,ALeA;APsBA,AS3BA,ARwBA,ACHA,AQxBA,APqBA,AoB5DA,AV8BA,ACHA,AENA,ACHA,ACHA,ACHA,ACHA,ACHA,ACHA,AV8BA,ALeA;APsBA,AS3BA,ARwBA,ACHA,AQxBA,APqBA,AoB5DA,AV8BA,ACHA,AENA,ACHA,ACHA,ACHA,ACHA,ACHA,ACHA,AV8BA,ALeA;APsBA,AS3BA,ARwBA,ACHA,AQxBA,APqBA,AoB5DA,AV8BA,ACHA,AENA,ACHA,ACHA,ACHA,ACHA,ACHA,ACHA,AV8BA,ALeA;APsBA,AS3BA,ARwBA,ACHA,AQxBA,APqBA,AoB5DA,ACHA,AXiCA,ACHA,AENA,ACHA,ACHA,ACHA,ACHA,ACHA,ACHA,AV8BA,ALeA;APsBA,AS3BA,ARwBA,ACHA,AQxBA,APqBA,AoB5DA,ACHA,AXiCA,ACHA,AENA,ACHA,ACHA,ACHA,ACHA,ACHA,ACHA,AV8BA,ALeA;APsBA,AS3BA,ARwBA,ACHA,AQxBA,APqBA,AoB5DA,ACHA,AXiCA,ACHA,AENA,ACHA,ACHA,ACHA,ACHA,ACHA,ACHA,AV8BA,ALeA;APsBA,AS3BA,ARwBA,ACHA,AQxBA,APqBA,AoB5DA,ACHA,ACHA,AZoCA,ACHA,AENA,ACHA,ACHA,ACHA,ACHA,ACHA,ACHA,AV8BA,ALeA;APsBA,AS3BA,ARwBA,ACHA,AQxBA,APqBA,AoB5DA,ACHA,ACHA,AZoCA,ACHA,AENA,ACHA,ACHA,ACHA,ACHA,ACHA,ACHA,AV8BA,ALeA;APsBA,AS3BA,ARwBA,ACHA,AQxBA,APqBA,AoB5DA,ACHA,ACHA,AZoCA,ACHA,AENA,ACHA,ACHA,ACHA,ACHA,ACHA,ACHA,AV8BA,ALeA;APsBA,AS3BA,ARwBA,ACHA,AQxBA,APqBA,AoB5DA,ACHA,ACHA,ACHA,AbuCA,ACHA,AENA,ACHA,ACHA,ACHA,ACHA,ACHA,ACHA,AV8BA,ALeA;APsBA,AS3BA,ARwBA,ACHA,AQxBA,APqBA,AoB5DA,ACHA,ACHA,ACHA,AbuCA,ACHA,AENA,ACHA,ACHA,ACHA,ACHA,ACHA,ACHA,AV8BA,ALeA;APsBA,AS3BA,ARwBA,ACHA,AQxBA,APqBA,AoB5DA,ACHA,ACHA,ACHA,AbuCA,ACHA,AENA,ACHA,ACHA,ACHA,ACHA,ACHA,ACHA,AV8BA,ALeA;APsBA,AS3BA,ARwBA,ACHA,AQxBA,APqBA,AoB5DA,ACHA,ACHA,ACHA,AbuCA,ACHA,AENA,ACHA,ACHA,ACHA,ACHA,ACHA,ACHA,AKfA,Af6CA,ALeA;APsBA,AS3BA,ARwBA,ACHA,AQxBA,APqBA,AoB5DA,ACHA,ACHA,ACHA,AZoCA,AENA,ACHA,ACHA,ACHA,ACHA,ACHA,ACHA,AKfA,Af6CA,ALeA;APsBA,AS3BA,ARwBA,ACHA,AQxBA,APqBA,AoB5DA,ACHA,ACHA,ACHA,AZoCA,AENA,ACHA,ACHA,ACHA,ACHA,ACHA,ACHA,AKfA,Af6CA,ALeA;APsBA,AS3BA,ARwBA,ACHA,AQxBA,APqBA,AoB5DA,ACHA,ACHA,ACHA,AZoCA,AENA,ACHA,ACHA,ACHA,ACHA,ACHA,ACHA,AKfA,ACHA,AhBgDA,ALeA;APsBA,AS3BA,ARwBA,ACHA,AQxBA,APqBA,AoB5DA,ACHA,ACHA,ACHA,AZoCA,AENA,ACHA,ACHA,ACHA,ACHA,ACHA,ACHA,AKfA,ACHA,AhBgDA,ALeA;APsBA,AS3BA,ARwBA,ACHA,AQxBA,APqBA,AoB5DA,ACHA,ACHA,ACHA,AZoCA,AENA,ACHA,ACHA,ACHA,ACHA,ACHA,ACHA,AKfA,ACHA,AhBgDA,ALeA;APsBA,AS3BA,ARwBA,ACHA,AQxBA,APqBA,AoB5DA,ACHA,ACHA,ACHA,AZoCA,AENA,ACHA,ACHA,ACHA,ACHA,ACHA,ACHA,AKfA,ACHA,ACHA,AjBmDA,ALeA;APsBA,AS3BA,ARwBA,ACHA,AQxBA,APqBA,AoB5DA,ACHA,ACHA,ACHA,AZoCA,AENA,ACHA,ACHA,ACHA,ACHA,ACHA,ACHA,AKfA,ACHA,ACHA,AjBmDA,ALeA;APsBA,AS3BA,ARwBA,ACHA,AQxBA,APqBA,AoB5DA,ACHA,ACHA,ACHA,AZoCA,AENA,ACHA,ACHA,ACHA,ACHA,ACHA,ACHA,AKfA,ACHA,ACHA,AjBmDA,ALeA;APsBA,AS3BA,ARwBA,ACHA,AQxBA,APqBA,AoB5DA,ACHA,ACHA,ACHA,AZoCA,AENA,ACHA,ACHA,ACHA,ACHA,ACHA,ACHA,AKfA,ACHA,ACHA,ACHA,AlBsDA,ALeA;APsBA,AS3BA,ARwBA,ACHA,AQxBA,APqBA,AoB5DA,ACHA,ACHA,ACHA,AZoCA,AENA,ACHA,ACHA,ACHA,ACHA,ACHA,ACHA,AKfA,ACHA,ACHA,ACHA,AlBsDA,ALeA;APsBA,AS3BA,ARwBA,ACHA,AQxBA,APqBA,AoB5DA,ACHA,ACHA,ACHA,AZoCA,AENA,ACHA,ACHA,ACHA,ACHA,ACHA,ACHA,AKfA,ACHA,ACHA,ACHA,AlBsDA,ALeA;APsBA,AS3BA,ARwBA,ACHA,AQxBA,APqBA,AoB5DA,ACHA,ACHA,ACHA,AZoCA,AENA,ACHA,ACHA,ACHA,ACHA,ACHA,ACHA,AKfA,ACHA,ACHA,ACHA,ACHA,AnByDA,ALeA;APsBA,AS3BA,ARwBA,ACHA,AQxBA,APqBA,AoB5DA,ACHA,ACHA,ACHA,AZoCA,AENA,ACHA,ACHA,ACHA,ACHA,ACHA,ACHA,AKfA,ACHA,ACHA,ACHA,ACHA,AnByDA,ALeA;APsBA,AS3BA,ARwBA,ACHA,AQxBA,APqBA,AoB5DA,ACHA,ACHA,ACHA,AZoCA,AENA,ACHA,ACHA,ACHA,ACHA,ACHA,ACHA,AKfA,ACHA,ACHA,ACHA,ACHA,AnByDA,ALeA;APsBA,AS3BA,ARwBA,ACHA,AQxBA,APqBA,AoB5DA,ACHA,ACHA,ACHA,AZoCA,AENA,ACHA,ACHA,ACHA,ACHA,ACHA,ACHA,AKfA,ACHA,ACHA,ACHA,ACHA,ACHA,ApB4DA,ALeA;APsBA,AS3BA,ARwBA,ACHA,ACHA,AoB5DA,ACHA,ACHA,ACHA,AZoCA,AENA,ACHA,ACHA,ACHA,ACHA,ACHA,ACHA,AKfA,ACHA,ACHA,ACHA,ACHA,ACHA,ApB4DA,ALeA;APsBA,AS3BA,ARwBA,ACHA,ACHA,AoB5DA,ACHA,ACHA,ACHA,AZoCA,AENA,ACHA,ACHA,ACHA,ACHA,ACHA,ACHA,AKfA,ACHA,ACHA,ACHA,ACHA,ACHA,ApB4DA,ALeA;APsBA,AS3BA,ARwBA,ACHA,ACHA,AoB5DA,ACHA,ACHA,ACHA,AZoCA,AENA,ACHA,ACHA,ACHA,ACHA,ACHA,ACHA,AKfA,ACHA,ACHA,ACHA,ACHA,ACHA,ApB4DA,AqB/DA,A1B8EA;APsBA,AS3BA,ARwBA,ACHA,ACHA,AoB5DA,ACHA,ACHA,ACHA,AZoCA,AENA,ACHA,ACHA,ACHA,ACHA,ACHA,ACHA,AKfA,ACHA,ACHA,ACHA,ACHA,ACHA,ApB4DA,AqB/DA,A1B8EA;APsBA,AS3BA,ARwBA,ACHA,ACHA,AoB5DA,ACHA,ACHA,ACHA,AZoCA,AENA,ACHA,ACHA,ACHA,ACHA,ACHA,ACHA,AKfA,ACHA,ACHA,ACHA,ACHA,ACHA,ApB4DA,AqB/DA,A1B8EA;APsBA,AS3BA,ARwBA,ACHA,ACHA,AoB5DA,ACHA,ACHA,ACHA,AZoCA,AENA,ACHA,ACHA,ACHA,ACHA,ACHA,ACHA,AKfA,ACHA,ACHA,ACHA,ACHA,ACHA,ApB4DA,AqB/DA,ACHA,A3BiFA;APsBA,AS3BA,ARwBA,ACHA,ACHA,AoB5DA,ACHA,ACHA,ACHA,AZoCA,AENA,ACHA,ACHA,ACHA,ACHA,ACHA,ACHA,AKfA,ACHA,ACHA,ACHA,ACHA,ACHA,ApB4DA,AqB/DA,ACHA,A3BiFA;APsBA,AS3BA,ARwBA,ACHA,ACHA,AoB5DA,ACHA,ACHA,ACHA,AZoCA,AENA,ACHA,ACHA,ACHA,ACHA,ACHA,ACHA,AKfA,ACHA,ACHA,ACHA,ACHA,ACHA,ApB4DA,AqB/DA,ACHA,A3BiFA;APsBA,AS3BA,ARwBA,ACHA,ACHA,AgChGA,AZoCA,ACHA,ACHA,ACHA,AZoCA,AGTA,ACHA,ACHA,ACHA,ACHA,ACHA,AKfA,ACHA,ACHA,ACHA,ACHA,ACHA,ApB4DA,AqB/DA,ACHA,A3BiFA;APsBA,AS3BA,ARwBA,ACHA,ACHA,AgChGA,AZoCA,ACHA,ACHA,ACHA,AZoCA,AGTA,ACHA,ACHA,ACHA,ACHA,ACHA,AKfA,ACHA,ACHA,ACHA,ACHA,ACHA,ApB4DA,AqB/DA,ACHA,A3BiFA;APsBA,AS3BA,ARwBA,ACHA,ACHA,AgChGA,AZoCA,ACHA,ACHA,ACHA,AZoCA,AGTA,ACHA,ACHA,ACHA,ACHA,ACHA,AKfA,ACHA,ACHA,ACHA,ACHA,ACHA,ApB4DA,AqB/DA,ACHA,A3BiFA;APsBA,AoC5GA,A3BiFA,ARwBA,ACHA,ACHA,AgChGA,AZoCA,ACHA,ACHA,ACHA,AZoCA,AGTA,ACHA,ACHA,ACHA,ACHA,ACHA,AKfA,ACHA,ACHA,ACHA,ACHA,ACHA,ApB4DA,AqB/DA,ACHA,A3BiFA;APsBA,AoC5GA,A3BiFA,ARwBA,ACHA,ACHA,AgChGA,AZoCA,ACHA,ACHA,ACHA,AZoCA,AGTA,ACHA,ACHA,ACHA,ACHA,ACHA,AKfA,ACHA,ACHA,ACHA,ACHA,ACHA,ApB4DA,AqB/DA,ACHA,A3BiFA;APsBA,AoC5GA,A3BiFA,ARwBA,ACHA,ACHA,AgChGA,AZoCA,ACHA,ACHA,ACHA,AZoCA,AGTA,ACHA,ACHA,ACHA,ACHA,ACHA,AKfA,ACHA,ACHA,ACHA,ACHA,ACHA,ApB4DA,AqB/DA,ACHA,A3BiFA;APsBA,AoC5GA,A3BiFA,ARwBA,ACHA,ACHA,AgChGA,AZoCA,ACHA,ACHA,ACHA,AZoCA,AGTA,ACHA,ACHA,ACHA,ACHA,ACHA,AKfA,ACHA,ACHA,ACHA,ACHA,ACHA,ApB4DA,AqB/DA,ACHA,A3BiFA,A8B1FA;ArCgHA,AoC5GA,A3BiFA,ARwBA,ACHA,ACHA,AgChGA,AZoCA,ACHA,ACHA,ACHA,AZoCA,AGTA,ACHA,ACHA,ACHA,ACHA,ACHA,AKfA,ACHA,ACHA,ACHA,ACHA,ACHA,ApB4DA,AqB/DA,ACHA,A3BiFA,A8B1FA;ArCgHA,AoC5GA,A3BiFA,ARwBA,ACHA,ACHA,AgChGA,AZoCA,ACHA,ACHA,ACHA,AZoCA,AGTA,ACHA,ACHA,ACHA,ACHA,ACHA,AKfA,ACHA,ACHA,ACHA,ACHA,ACHA,ApB4DA,AqB/DA,ACHA,A3BiFA,A8B1FA;ArCgHA,AoC5GA,A3BiFA,ARwBA,ACHA,ACHA,AgChGA,AZoCA,ACHA,ACHA,ACHA,AZoCA,AGTA,AqB/DA,ApB4DA,ACHA,ACHA,ACHA,ACHA,AKfA,ACHA,ACHA,ACHA,ACHA,ACHA,ApB4DA,AqB/DA,ACHA,A3BiFA,A8B1FA;ArCgHA,AoC5GA,A3BiFA,ARwBA,ACHA,ACHA,AgChGA,AZoCA,ACHA,ACHA,ACHA,AZoCA,AGTA,AqB/DA,ApB4DA,ACHA,ACHA,ACHA,ACHA,AKfA,ACHA,ACHA,ACHA,ACHA,ACHA,ApB4DA,AqB/DA,ACHA,A3BiFA,A8B1FA;ArCgHA,AoC5GA,A3BiFA,ARwBA,ACHA,ACHA,AgChGA,AZoCA,ACHA,ACHA,ACHA,AT2BA,AqB/DA,ApB4DA,ACHA,ACHA,ACHA,ACHA,AKfA,ACHA,ACHA,ACHA,ACHA,ACHA,ApB4DA,AqB/DA,ACHA,A3BiFA,A8B1FA;ArCgHA,AoC5GA,A3BiFA,ARwBA,ACHA,ACHA,AgChGA,AIZA,AhBgDA,ACHA,ACHA,ACHA,AT2BA,AqB/DA,ApB4DA,ACHA,ACHA,ACHA,ACHA,AKfA,ACHA,ACHA,ACHA,ACHA,ACHA,ApB4DA,AqB/DA,ACHA,A3BiFA,A8B1FA;ArCgHA,AoC5GA,A3BiFA,ARwBA,ACHA,ACHA,AgChGA,AIZA,AhBgDA,ACHA,ACHA,ACHA,AT2BA,AqB/DA,ApB4DA,ACHA,ACHA,ACHA,ACHA,AKfA,ACHA,ACHA,ACHA,ACHA,ACHA,ApB4DA,AqB/DA,ACHA,A3BiFA,A8B1FA;ArCgHA,AoC5GA,A3BiFA,ARwBA,ACHA,ACHA,AgChGA,AIZA,AhBgDA,ACHA,ACHA,ACHA,AT2BA,AqB/DA,ApB4DA,ACHA,ACHA,ACHA,ACHA,AKfA,ACHA,ACHA,ACHA,ACHA,ACHA,ApB4DA,AqB/DA,ACHA,A3BiFA,A8B1FA;ArCgHA,AoC5GA,A3BiFA,ARwBA,ACHA,ACHA,AgChGA,AIZA,AhBgDA,ACHA,ACHA,ACHA,AT2BA,AqB/DA,ApB4DA,ACHA,ACHA,ACHA,ACHA,AKfA,ACHA,ACHA,ACHA,ACHA,ACHA,ApB4DA,AqB/DA,ACHA,A3BiFA,A8B1FA,AGTA;AxCyHA,AoC5GA,A3BiFA,ARwBA,ACHA,ACHA,AgChGA,AIZA,AhBgDA,ACHA,ACHA,ACHA,AT2BA,AqB/DA,ApB4DA,ACHA,ACHA,ACHA,ACHA,AKfA,ACHA,ACHA,ACHA,ACHA,ACHA,ApB4DA,AqB/DA,ACHA,A3BiFA,A8B1FA,AGTA;AxCyHA,AoC5GA,A3BiFA,ARwBA,ACHA,ACHA,AgChGA,AIZA,AhBgDA,ACHA,ACHA,ACHA,AT2BA,AqB/DA,ApB4DA,ACHA,ACHA,ACHA,ACHA,AKfA,ACHA,ACHA,ACHA,ACHA,ACHA,ApB4DA,AqB/DA,ACHA,A3BiFA,A8B1FA,AGTA;AxCyHA,AoC5GA,A3BiFA,ARwBA,ACHA,ACHA,AgChGA,AIZA,AhBgDA,ACHA,ACHA,ACHA,AT2BA,AqB/DA,ApB4DA,ACHA,ACHA,ACHA,ACHA,AKfA,ACHA,ACHA,ACHA,ACHA,ACHA,ApB4DA,AqB/DA,ACHA,A3BiFA,A8B1FA,AIZA,ADGA;AxCyHA,AoC5GA,A3BiFA,ARwBA,ACHA,ACHA,AgChGA,AIZA,AhBgDA,ACHA,ACHA,ACHA,AT2BA,AqB/DA,ApB4DA,AENA,ACHA,ACHA,AKfA,ACHA,ACHA,ACHA,ACHA,ACHA,ApB4DA,AqB/DA,ACHA,A3BiFA,A8B1FA,AIZA,ADGA;AxCyHA,AoC5GA,A3BiFA,ARwBA,ACHA,ACHA,AgChGA,AIZA,AhBgDA,ACHA,ACHA,ACHA,AT2BA,AqB/DA,ApB4DA,AENA,ACHA,ACHA,AKfA,ACHA,ACHA,ACHA,ACHA,ACHA,ApB4DA,AqB/DA,ACHA,A3BiFA,A8B1FA,AIZA,ADGA;AxCyHA,AoC5GA,A3BiFA,ARwBA,ACHA,ACHA,AgChGA,AIZA,AhBgDA,ACHA,ACHA,ACHA,AT2BA,AqB/DA,ApB4DA,AENA,ACHA,ACHA,AKfA,ACHA,ACHA,ACHA,ACHA,ACHA,ApB4DA,AqB/DA,ACHA,A3BiFA,A8B1FA,AIZA,ADGA;AxCyHA,AoC5GA,A3BiFA,ARwBA,ACHA,ACHA,AgChGA,AIZA,AhBgDA,ACHA,ACHA,ACHA,AT2BA,AqB/DA,ApB4DA,AENA,ACHA,ACHA,AKfA,ACHA,ACHA,ACHA,ACHA,ACHA,ApB4DA,AqB/DA,ACHA,A3BiFA,A8B1FA,AIZA,ADGA;AxCyHA,AoC5GA,A3BiFA,ARwBA,ACHA,ACHA,AgChGA,AIZA,AhBgDA,ACHA,ACHA,ACHA,AT2BA,AqB/DA,ApB4DA,AENA,ACHA,ACHA,AKfA,ACHA,ACHA,ACHA,ACHA,ACHA,ApB4DA,AqB/DA,ACHA,A3BiFA,A8B1FA,AIZA,ADGA;AxCyHA,AoC5GA,A3BiFA,ARwBA,ACHA,ACHA,AgChGA,AIZA,AhBgDA,ACHA,ACHA,ACHA,AT2BA,AqB/DA,ApB4DA,AENA,ACHA,ACHA,AKfA,ACHA,ACHA,ACHA,ACHA,ACHA,ApB4DA,AqB/DA,ACHA,A3BiFA,A8B1FA,AIZA,ADGA;AxCyHA,AoC5GA,A3BiFA,ARwBA,ACHA,ACHA,AgChGA,AIZA,AhBgDA,ACHA,ACHA,ACHA,AT2BA,AqB/DA,ApB4DA,AENA,ACHA,ACHA,AKfA,ACHA,ACHA,ACHA,ACHA,ACHA,ApB4DA,AqB/DA,ACHA,A3BiFA,A8B1FA,AIZA,ADGA;AxCyHA,AoC5GA,A3BiFA,ARwBA,ACHA,ACHA,AgChGA,AIZA,AhBgDA,ACHA,ACHA,ACHA,AT2BA,AqB/DA,ApB4DA,AENA,ACHA,ACHA,AKfA,ACHA,ACHA,ACHA,ACHA,ACHA,ApB4DA,AqB/DA,ACHA,A3BiFA,A8B1FA,AIZA,ADGA;AxCyHA,AoC5GA,A3BiFA,ARwBA,ACHA,ACHA,AgChGA,AIZA,AhBgDA,ACHA,ACHA,ACHA,AT2BA,AqB/DA,ApB4DA,AENA,ACHA,ACHA,AKfA,ACHA,ACHA,ACHA,ACHA,ACHA,ApB4DA,AqB/DA,ACHA,A3BiFA,A8B1FA,AIZA,ADGA;AxCyHA,AoC5GA,A3BiFA,ARwBA,ACHA,ACHA,AgChGA,AIZA,AhBgDA,ACHA,ACHA,ACHA,AYpCA,ApB4DA,AENA,ACHA,ACHA,AKfA,ACHA,ACHA,ACHA,ACHA,ACHA,ApB4DA,AqB/DA,ACHA,A3BiFA,A8B1FA,AIZA,ADGA;AxCyHA,AoC5GA,A3BiFA,ARwBA,ACHA,ACHA,AgChGA,AIZA,AhBgDA,ACHA,ACHA,ACHA,AYpCA,ApB4DA,AENA,ACHA,ACHA,AKfA,ACHA,ACHA,ACHA,ACHA,ACHA,ApB4DA,AqB/DA,ACHA,A3BiFA,A8B1FA,AIZA,ADGA;AxCyHA,AoC5GA,A3BiFA,ARwBA,ACHA,ACHA,AgChGA,AIZA,AhBgDA,ACHA,ACHA,ACHA,AYpCA,ApB4DA,AENA,ACHA,ACHA,AKfA,ACHA,ACHA,ACHA,ACHA,ACHA,ApB4DA,AqB/DA,ACHA,A3BiFA,A8B1FA,AIZA,ADGA;AxCyHA,AoC5GA,A3BiFA,ARwBA,ACHA,ACHA,AgChGA,AIZA,AhBgDA,ACHA,ACHA,ACHA,AYpCA,ApB4DA,AENA,ACHA,ACHA,AKfA,ACHA,ACHA,ACHA,ACHA,ACHA,ApB4DA,AqB/DA,ACHA,A3BiFA,A8B1FA,AIZA,ADGA;AxCyHA,AoC5GA,A3BiFA,ARwBA,ACHA,ACHA,AgChGA,AIZA,AhBgDA,ACHA,ACHA,ACHA,AYpCA,ApB4DA,AENA,ACHA,ACHA,AKfA,ACHA,ACHA,ACHA,ACHA,ACHA,ApB4DA,AqB/DA,ACHA,A3BiFA,A8B1FA,AIZA,ADGA;AxCyHA,AoC5GA,A3BiFA,ARwBA,ACHA,ACHA,AgChGA,AIZA,AhBgDA,ACHA,ACHA,ACHA,AYpCA,ApB4DA,AENA,ACHA,ACHA,AKfA,ACHA,ACHA,ACHA,ACHA,ACHA,ApB4DA,AqB/DA,ACHA,A3BiFA,A8B1FA,AIZA,ADGA;AxCyHA,AoC5GA,A3BiFA,ARwBA,ACHA,ACHA,AgChGA,AIZA,AhBgDA,ACHA,ACHA,ACHA,AYpCA,ApB4DA,AENA,ACHA,ACHA,AKfA,ACHA,ACHA,ACHA,ACHA,ACHA,ApB4DA,AqB/DA,ACHA,A3BiFA,A8B1FA,AIZA,ADGA;AxCyHA,AoC5GA,A3BiFA,ARwBA,ACHA,ACHA,AgChGA,AIZA,AhBgDA,ACHA,ACHA,ACHA,AYpCA,ApB4DA,AENA,ACHA,ACHA,AKfA,ACHA,ACHA,ACHA,ACHA,ACHA,ApB4DA,AqB/DA,ACHA,A3BiFA,A8B1FA,AIZA,ADGA;AxCyHA,AoC5GA,A3BiFA,ARwBA,ACHA,ACHA,AgChGA,AIZA,AhBgDA,ACHA,ACHA,ACHA,AYpCA,ApB4DA,AENA,ACHA,ACHA,AKfA,ACHA,ACHA,ACHA,ACHA,ACHA,ApB4DA,AqB/DA,ACHA,A3BiFA,A8B1FA,AGTA;AxCyHA,AoC5GA,A3BiFA,ARwBA,ACHA,ACHA,AgChGA,AIZA,AhBgDA,ACHA,ACHA,ACHA,AYpCA,ApB4DA,AENA,ACHA,ACHA,AKfA,ACHA,ACHA,ACHA,ACHA,ACHA,ApB4DA,AqB/DA,ACHA,A3BiFA,A8B1FA,AGTA;AxCyHA,AoC5GA,A3BiFA,ARwBA,ACHA,ACHA,AgChGA,AIZA,AhBgDA,ACHA,ACHA,ACHA,AYpCA,ApB4DA,AENA,ACHA,ACHA,AKfA,ACHA,ACHA,ACHA,ACHA,ACHA,ApB4DA,AqB/DA,ACHA,A3BiFA,A8B1FA,AGTA;AxCyHA,AoC5GA,A3BiFA,ARwBA,ACHA,ACHA,AgChGA,AIZA,AhBgDA,ACHA,ACHA,ACHA,AYpCA,ApB4DA,AENA,ACHA,ACHA,AKfA,ACHA,ACHA,ACHA,ACHA,ACHA,ApB4DA,AqB/DA,ACHA,A3BiFA,A8B1FA,AGTA;AxCyHA,AoC5GA,A3BiFA,ARwBA,ACHA,ACHA,AgChGA,AIZA,AhBgDA,ACHA,ACHA,ACHA,AYpCA,ApB4DA,AENA,ACHA,ACHA,AKfA,ACHA,ACHA,ACHA,ACHA,ACHA,ApB4DA,AqB/DA,ACHA,A3BiFA,A8B1FA,AGTA;AxCyHA,AoC5GA,A3BiFA,ARwBA,ACHA,ACHA,AgChGA,AIZA,AhBgDA,ACHA,ACHA,ACHA,AYpCA,ApB4DA,AENA,ACHA,ACHA,AKfA,ACHA,ACHA,ACHA,ACHA,ACHA,ApB4DA,AqB/DA,ACHA,A3BiFA,A8B1FA,AGTA;AxCyHA,AoC5GA,A3BiFA,ARwBA,ACHA,ACHA,AgChGA,AIZA,AhBgDA,ACHA,ACHA,ACHA,AYpCA,ApB4DA,AENA,ACHA,ACHA,AKfA,ACHA,ACHA,ACHA,ACHA,ACHA,ApB4DA,AqB/DA,ACHA,A3BiFA,A8B1FA,AGTA;AxCyHA,AoC5GA,A3BiFA,ARwBA,ACHA,ACHA,AgChGA,AIZA,AhBgDA,ACHA,ACHA,ACHA,AYpCA,ApB4DA,AENA,ACHA,ACHA,AKfA,ACHA,ACHA,ACHA,ACHA,ACHA,ApB4DA,AqB/DA,ACHA,A3BiFA,A8B1FA,AGTA;AxCyHA,AoC5GA,A3BiFA,ARwBA,ACHA,ACHA,AgChGA,AIZA,AhBgDA,ACHA,ACHA,ACHA,AYpCA,ApB4DA,AENA,ACHA,ACHA,AKfA,ACHA,ACHA,ACHA,ACHA,ACHA,ApB4DA,AqB/DA,ACHA,A3BiFA,A8B1FA,AGTA;AxCyHA,AoC5GA,A3BiFA,ARwBA,ACHA,ACHA,AgChGA,AIZA,AhBgDA,ACHA,ACHA,ACHA,AYpCA,ApB4DA,AENA,ACHA,ACHA,AKfA,ACHA,ACHA,ACHA,ACHA,ACHA,ApB4DA,AqB/DA,ACHA,A3BiFA,A8B1FA,AGTA;AxCyHA,AoC5GA,A3BiFA,ARwBA,ACHA,ACHA,AgChGA,AIZA,AhBgDA,ACHA,ACHA,ACHA,AYpCA,ApB4DA,AENA,ACHA,ACHA,AKfA,ACHA,ACHA,ACHA,ACHA,ACHA,ApB4DA,AqB/DA,ACHA,A3BiFA,A8B1FA,AGTA;AxCyHA,AoC5GA,A3BiFA,ARwBA,ACHA,ACHA,AgChGA,AIZA,AhBgDA,ACHA,ACHA,ACHA,AYpCA,ApB4DA,AENA,ACHA,ACHA,AKfA,ACHA,ACHA,ACHA,ACHA,ACHA,ApB4DA,AqB/DA,ACHA,A3BiFA,A8B1FA,AGTA;AxCyHA,AoC5GA,A3BiFA,ARwBA,ACHA,ACHA,AgChGA,AIZA,AhBgDA,AENA,ACHA,AYpCA,ApB4DA,AENA,ACHA,ACHA,AKfA,ACHA,ACHA,ACHA,ACHA,ACHA,ApB4DA,AqB/DA,ACHA,A3BiFA,A8B1FA,AGTA;AxCyHA,AoC5GA,A3BiFA,ARwBA,ACHA,ACHA,AgChGA,AIZA,AhBgDA,AENA,ACHA,AYpCA,ApB4DA,AENA,ACHA,ACHA,AKfA,ACHA,ACHA,ACHA,ACHA,ACHA,ApB4DA,AqB/DA,ACHA,A3BiFA,A8B1FA,AGTA;AxCyHA,AoC5GA,A3BiFA,ARwBA,ACHA,ACHA,AoB5DA,AENA,ACHA,AYpCA,ApB4DA,AENA,ACHA,ACHA,AKfA,ACHA,ACHA,ACHA,ACHA,ACHA,ApB4DA,AqB/DA,ACHA,A3BiFA,A8B1FA,AGTA;AxCyHA,AoC5GA,A3BiFA,ARwBA,ACHA,ACHA,AoB5DA,AENA,ACHA,AYpCA,ApB4DA,AENA,ACHA,ACHA,AKfA,ACHA,ACHA,ACHA,ACHA,ACHA,ApB4DA,AqB/DA,ACHA,A3BiFA,A8B1FA,AGTA;AxCyHA,AoC5GA,A3BiFA,ARwBA,ACHA,ACHA,AoB5DA,AENA,ACHA,AYpCA,ApB4DA,AENA,ACHA,ACHA,AKfA,ACHA,ACHA,ACHA,ACHA,ACHA,ApB4DA,AqB/DA,ACHA,A3BiFA,A8B1FA,AGTA;AxCyHA,AoC5GA,A3BiFA,ARwBA,ACHA,ACHA,AoB5DA,AENA,ACHA,AYpCA,ApB4DA,AENA,ACHA,ACHA,AKfA,ACHA,ACHA,ACHA,ACHA,ACHA,ApB4DA,AqB/DA,ACHA,A3BiFA,A8B1FA,AGTA;AxCyHA,AoC5GA,A3BiFA,ARwBA,ACHA,ACHA,AoB5DA,AENA,AavCA,ApB4DA,AENA,ACHA,ACHA,AKfA,ACHA,ACHA,ACHA,ACHA,ACHA,ApB4DA,AqB/DA,ACHA,A3BiFA,A8B1FA,AGTA;AxCyHA,AoC5GA,A3BiFA,ARwBA,ACHA,ACHA,AoB5DA,AENA,AavCA,ApB4DA,AENA,ACHA,ACHA,AKfA,ACHA,ACHA,ACHA,ACHA,ACHA,ApB4DA,AqB/DA,ACHA,A3BiFA,A8B1FA,AGTA;AxCyHA,AoC5GA,A3BiFA,ARwBA,ACHA,ACHA,AoB5DA,Ae7CA,ApB4DA,AENA,ACHA,ACHA,AKfA,ACHA,AENA,ACHA,ACHA,ACHA,ACHA,A3BiFA,A8B1FA,AGTA;AxCyHA,AoC5GA,A3BiFA,ARwBA,ACHA,ACHA,AoB5DA,Ae7CA,ApB4DA,AENA,ACHA,ACHA,AMlBA,AENA,ACHA,ACHA,ACHA,ACHA,A3BiFA,A8B1FA,AGTA;AxCyHA,AoC5GA,A3BiFA,ARwBA,ACHA,ACHA,AoB5DA,Ae7CA,ApB4DA,AENA,ACHA,ACHA,AMlBA,AENA,ACHA,ACHA,ACHA,ACHA,A3BiFA,A8B1FA,AGTA;AxCyHA,AoC5GA,A3BiFA,ARwBA,ACHA,ACHA,AoB5DA,Ae7CA,ApB4DA,AENA,ACHA,ACHA,AMlBA,AENA,ACHA,ACHA,ACHA,ACHA,A3BiFA,A8B1FA,AGTA;AxCyHA,AoC5GA,A3BiFA,ARwBA,ACHA,ACHA,AoB5DA,Ae7CA,ApB4DA,AENA,ACHA,ACHA,AMlBA,AENA,ACHA,ACHA,ACHA,ACHA,A3BiFA,A8B1FA,AGTA;AxCyHA,AoC5GA,A3BiFA,ARwBA,ACHA,ACHA,AoB5DA,Ae7CA,ApB4DA,AENA,ACHA,ACHA,AMlBA,AGTA,ACHA,ACHA,ACHA,A3BiFA,A8B1FA,AGTA;AxCyHA,AoC5GA,A3BiFA,ARwBA,ACHA,ACHA,AoB5DA,Ae7CA,ApB4DA,AENA,AENA,AMlBA,AGTA,ACHA,ACHA,ACHA,A3BiFA,A8B1FA,AGTA;AxCyHA,AoC5GA,A3BiFA,ARwBA,ACHA,ACHA,AoB5DA,Ae7CA,ApB4DA,AENA,AENA,AMlBA,AGTA,ACHA,ACHA,ACHA,A3BiFA,A8B1FA,AGTA;AxCyHA,AoC5GA,A3BiFA,ARwBA,ACHA,ACHA,AoB5DA,Ae7CA,ApB4DA,AENA,AENA,AS3BA,ACHA,ACHA,ACHA,A3BiFA,A8B1FA,AGTA;AxCyHA,AoC5GA,AnCyGA,ACHA,ACHA,AoB5DA,Ae7CA,ApB4DA,AENA,AENA,AS3BA,ACHA,ACHA,ACHA,A3BiFA,A8B1FA,AGTA;AxCyHA,AoC5GA,AnCyGA,ACHA,ACHA,AoB5DA,Ae7CA,ApB4DA,AENA,AENA,AS3BA,ACHA,ACHA,ACHA,A3BiFA,A8B1FA,AGTA;AxCyHA,AoC5GA,AnCyGA,ACHA,ACHA,AoB5DA,Ae7CA,ApB4DA,AENA,AENA,AS3BA,ACHA,ACHA,ACHA,A3BiFA,A8B1FA,AGTA;AxCyHA,AoC5GA,AnCyGA,ACHA,ACHA,AoB5DA,Ae7CA,ApB4DA,AENA,AENA,AS3BA,ACHA,ACHA,ACHA,A3BiFA,A8B1FA,AGTA;AxCyHA,AoC5GA,AnCyGA,ACHA,ACHA,AoB5DA,Ae7CA,ApB4DA,AENA,AENA,AS3BA,ACHA,ACHA,ACHA,A3BiFA,A8B1FA,AGTA;AxCyHA,AoC5GA,AnCyGA,ACHA,ACHA,AoB5DA,Ae7CA,ApB4DA,AENA,AENA,AS3BA,ACHA,ACHA,ACHA,A3BiFA,A8B1FA,AGTA;AxCyHA,AoC5GA,AnCyGA,ACHA,ACHA,AoB5DA,Ae7CA,ApB4DA,AENA,AENA,AS3BA,ACHA,ACHA,ACHA,A3BiFA,A8B1FA,AGTA;AxCyHA,AoC5GA,AnCyGA,ACHA,ACHA,AoB5DA,Ae7CA,ApB4DA,AENA,AENA,AS3BA,ACHA,ACHA,ACHA,A3BiFA,A8B1FA,AGTA;AxCyHA,AoC5GA,AnCyGA,ACHA,ACHA,AoB5DA,Ae7CA,ApB4DA,AENA,AENA,AS3BA,AENA,ACHA,A3BiFA,A8B1FA,AGTA;AxCyHA,AoC5GA,AnCyGA,ACHA,ACHA,AoB5DA,Ae7CA,ApB4DA,AENA,AENA,AS3BA,AENA,ACHA,A3BiFA,A8B1FA,AGTA;AxCyHA,AoC5GA,AnCyGA,ACHA,ACHA,AoB5DA,Ae7CA,ApB4DA,AENA,AENA,AS3BA,AENA,ACHA,A3BiFA,A8B1FA,AGTA;AxCyHA,AoC5GA,AnCyGA,ACHA,ACHA,AoB5DA,Ae7CA,ApB4DA,AENA,AENA,AS3BA,AENA,ACHA,A3BiFA,A8B1FA,AGTA;AxCyHA,AoC5GA,AnCyGA,ACHA,ACHA,AoB5DA,Ae7CA,ApB4DA,AENA,AENA,AS3BA,AENA,ACHA,A3BiFA,A8B1FA,AGTA;AxCyHA,AoC5GA,AnCyGA,ACHA,ACHA,AoB5DA,Ae7CA,ApB4DA,AENA,AENA,AS3BA,AENA,ACHA,A3BiFA,A8B1FA,AGTA;AxCyHA,AoC5GA,AnCyGA,ACHA,ACHA,AoB5DA,Ae7CA,ApB4DA,AENA,AENA,AS3BA,AENA,ACHA,A3BiFA,A8B1FA,AGTA;AxCyHA,AoC5GA,AnCyGA,ACHA,ACHA,AoB5DA,Ae7CA,ApB4DA,AENA,AENA,AS3BA,AENA,ACHA,A3BiFA,A8B1FA,AGTA;AxCyHA,AoC5GA,AnCyGA,ACHA,ACHA,AoB5DA,Ae7CA,ApB4DA,AENA,AENA,AS3BA,AENA,ACHA,A3BiFA,A8B1FA,AGTA;AxCyHA,AoC5GA,AnCyGA,ACHA,ACHA,AoB5DA,Ae7CA,ApB4DA,AENA,AENA,AS3BA,AENA,ACHA,A3BiFA,A8B1FA,AGTA;AxCyHA,AoC5GA,AnCyGA,ACHA,ACHA,AoB5DA,Ae7CA,ApB4DA,AENA,AENA,AS3BA,AENA,ACHA,A3BiFA,A8B1FA,AGTA;AxCyHA,AoC5GA,AnCyGA,ACHA,ACHA,AoB5DA,Ae7CA,ApB4DA,AENA,AENA,AS3BA,AENA,A1B8EA,A8B1FA,AGTA;AxCyHA,AoC5GA,AnCyGA,ACHA,ACHA,AoB5DA,Ae7CA,ApB4DA,AENA,AENA,AS3BA,AENA,A1B8EA,A8B1FA,AGTA;AxCyHA,AoC5GA,AnCyGA,ACHA,ACHA,AoB5DA,Ae7CA,ApB4DA,AENA,AENA,AS3BA,AENA,A1B8EA,A8B1FA,AGTA;AxCyHA,AoC5GA,AnCyGA,ACHA,ACHA,AoB5DA,Ae7CA,ApB4DA,AENA,AENA,AS3BA,AENA,A1B8EA,A8B1FA,AGTA;AxCyHA,AoC5GA,AnCyGA,ACHA,ACHA,AoB5DA,Ae7CA,ApB4DA,AENA,AENA,AS3BA,AENA,A1B8EA,A8B1FA,AGTA;AxCyHA,AoC5GA,AnCyGA,ACHA,ACHA,AoB5DA,Ae7CA,ApB4DA,AENA,AENA,AS3BA,AENA,A1B8EA,A8B1FA,AGTA;AxCyHA,AoC5GA,AnCyGA,ACHA,ACHA,AoB5DA,Ae7CA,ApB4DA,AENA,AENA,AS3BA,AENA,A1B8EA,A8B1FA,AGTA;AxCyHA,AoC5GA,AnCyGA,ACHA,ACHA,AoB5DA,Ae7CA,ApB4DA,AENA,AENA,AS3BA,AENA,A1B8EA,A8B1FA,AGTA;AxCyHA,AoC5GA,AnCyGA,ACHA,ACHA,AoB5DA,Ae7CA,ApB4DA,AENA,AENA,AS3BA,AENA,A1B8EA,A8B1FA,AGTA;AxCyHA,AoC5GA,AnCyGA,ACHA,ACHA,AoB5DA,Ae7CA,ApB4DA,AENA,AENA,AS3BA,AENA,A1B8EA,A8B1FA,AGTA;AxCyHA,ACHA,ACHA,ACHA,AoB5DA,Ae7CA,ApB4DA,AENA,AENA,AS3BA,AENA,A1B8EA,A8B1FA,AGTA;AxCyHA,ACHA,ACHA,ACHA,AoB5DA,Ae7CA,ApB4DA,AENA,AENA,AS3BA,AENA,A1B8EA,A8B1FA,AGTA;AxCyHA,ACHA,ACHA,ACHA,AoB5DA,Ae7CA,ApB4DA,AENA,AENA,AS3BA,AENA,A1B8EA,A8B1FA,AGTA;AxCyHA,ACHA,ACHA,ACHA,AoB5DA,Ae7CA,ApB4DA,AENA,AENA,AS3BA,AENA,A1B8EA,A8B1FA,AGTA;AxCyHA,ACHA,ACHA,ACHA,AoB5DA,Ae7CA,ApB4DA,AENA,AENA,AS3BA,AENA,A1B8EA,A8B1FA,AGTA;AxCyHA,ACHA,ACHA,ACHA,AoB5DA,Ae7CA,ApB4DA,AENA,AENA,AS3BA,AENA,A1B8EA,A8B1FA,AGTA;AxCyHA,ACHA,ACHA,ACHA,AoB5DA,Ae7CA,ApB4DA,AENA,AENA,AS3BA,AENA,A1B8EA,A8B1FA,AGTA;AxCyHA,ACHA,ACHA,ACHA,AoB5DA,Ae7CA,ApB4DA,AENA,AENA,AS3BA,AENA,A1B8EA,A8B1FA,AGTA;AxCyHA,ACHA,ACHA,ACHA,AoB5DA,Ae7CA,ApB4DA,AENA,AWjCA,AENA,A1B8EA,A8B1FA,AGTA;AxCyHA,ACHA,ACHA,ACHA,AoB5DA,Ae7CA,ApB4DA,AENA,AWjCA,AENA,A1B8EA,A8B1FA,AGTA;AxCyHA,ACHA,ACHA,ACHA,AoB5DA,Ae7CA,ApB4DA,AENA,AWjCA,AENA,A1B8EA,A8B1FA,AGTA;AxCyHA,ACHA,ACHA,ACHA,AoB5DA,Ae7CA,ApB4DA,AENA,AWjCA,AENA,A1B8EA,A8B1FA,AGTA;AxCyHA,ACHA,ACHA,ACHA,AoB5DA,Ae7CA,ApB4DA,AavCA,AENA,A1B8EA,A8B1FA,AGTA;AxCyHA,ACHA,ACHA,ACHA,AoB5DA,Ae7CA,ApB4DA,AavCA,AENA,A1B8EA,A8B1FA,AGTA;AxCyHA,ACHA,ACHA,ACHA,AoB5DA,Ae7CA,ApB4DA,AavCA,AENA,A1B8EA,A8B1FA,AGTA;AxCyHA,ACHA,ACHA,ACHA,AoB5DA,Ae7CA,ApB4DA,AavCA,AENA,A1B8EA,A8B1FA,AGTA;AxCyHA,ACHA,ACHA,ACHA,AoB5DA,Ae7CA,ApB4DA,AavCA,AENA,A1B8EA,A8B1FA,AGTA;AxCyHA,ACHA,ACHA,ACHA,AoB5DA,Ae7CA,ApB4DA,AavCA,AENA,A1B8EA,A8B1FA,AGTA;AxCyHA,ACHA,ACHA,ACHA,AoB5DA,Ae7CA,ApB4DA,AavCA,AENA,A1B8EA,A8B1FA,AGTA;AxCyHA,ACHA,ACHA,ACHA,AoB5DA,Ae7CA,ApB4DA,AavCA,AENA,A1B8EA,A8B1FA,AGTA;AxCyHA,ACHA,ACHA,ACHA,AoB5DA,Ae7CA,ApB4DA,AavCA,AENA,A1B8EA,A8B1FA,AGTA;AxCyHA,ACHA,ACHA,ACHA,AoB5DA,Ae7CA,ApB4DA,AavCA,AENA,A1B8EA,A8B1FA,AGTA;AxCyHA,ACHA,ACHA,ACHA,AoB5DA,Ae7CA,ApB4DA,AavCA,AENA,A1B8EA,A8B1FA,AGTA;AxCyHA,ACHA,ACHA,ACHA,AoB5DA,Ae7CA,ApB4DA,AavCA,AENA,A1B8EA,A8B1FA,AGTA;AxCyHA,ACHA,ACHA,ACHA,AoB5DA,Ae7CA,APqBA,AENA,A1B8EA,A8B1FA,AGTA;AxCyHA,ACHA,ACHA,ACHA,AoB5DA,Ae7CA,APqBA,AENA,A1B8EA,A8B1FA,AGTA;AxCyHA,ACHA,ACHA,ACHA,AoB5DA,Ae7CA,APqBA,AENA,A1B8EA,A8B1FA,AGTA;AxCyHA,ACHA,ACHA,ACHA,AoB5DA,Ae7CA,APqBA,AENA,A1B8EA,A8B1FA,AGTA;AxCyHA,ACHA,ACHA,ACHA,AoB5DA,Ae7CA,APqBA,AENA,A1B8EA,A8B1FA,AGTA;AxCyHA,ACHA,ACHA,ACHA,AoB5DA,Ae7CA,APqBA,AENA,A1B8EA,A8B1FA,AGTA;AxCyHA,ACHA,ACHA,ACHA,AoB5DA,Ae7CA,APqBA,AENA,A1B8EA,A8B1FA,AGTA;AxCyHA,ACHA,ACHA,ACHA,AoB5DA,Ae7CA,APqBA,AENA,A1B8EA,A8B1FA,AGTA;AxCyHA,ACHA,ACHA,ACHA,AoB5DA,Ae7CA,APqBA,AENA,A1B8EA,A8B1FA,AGTA;AxCyHA,ACHA,ACHA,ACHA,AoB5DA,Ae7CA,APqBA,AENA,A1B8EA,A8B1FA,AGTA;AxCyHA,ACHA,ACHA,ACHA,AmCzGA,APqBA,AENA,A1B8EA,A8B1FA,AGTA;AxCyHA,ACHA,ACHA,ACHA,AmCzGA,APqBA,AENA,A1B8EA,A8B1FA,AGTA;AxCyHA,ACHA,ACHA,ACHA,AmCzGA,APqBA,AENA,A1B8EA,A8B1FA,AGTA;AxCyHA,ACHA,ACHA,ACHA,AmCzGA,APqBA,AENA,A1B8EA,A8B1FA,AGTA;AxCyHA,ACHA,ACHA,ACHA,AmCzGA,APqBA,AENA,A1B8EA,A8B1FA,AGTA;AxCyHA,ACHA,ACHA,ACHA,AmCzGA,APqBA,AENA,A1B8EA,A8B1FA,AGTA;AxCyHA,ACHA,ACHA,ACHA,AmCzGA,APqBA,AENA,A1B8EA,A8B1FA,AGTA;AxCyHA,ACHA,ACHA,ACHA,AmCzGA,APqBA,AENA,A1B8EA,A8B1FA,AGTA;AxCyHA,ACHA,ACHA,ACHA,AmCzGA,APqBA,AENA,A1B8EA,A8B1FA,AGTA;AxCyHA,ACHA,ACHA,ACHA,AmCzGA,APqBA,AENA,A1B8EA,A8B1FA,AGTA;AxCyHA,ACHA,ACHA,ACHA,AmCzGA,APqBA,AENA,A1B8EA,A8B1FA,AGTA;AxCyHA,ACHA,ACHA,ACHA,AmCzGA,APqBA,AENA,A1B8EA,A8B1FA,AGTA;AxCyHA,ACHA,ACHA,ACHA,AmCzGA,APqBA,AENA,A1B8EA,A8B1FA,AGTA;AxCyHA,ACHA,ACHA,ACHA,AmCzGA,APqBA,AENA,A1B8EA,A8B1FA,AGTA;AxCyHA,ACHA,ACHA,ACHA,AmCzGA,APqBA,AENA,A1B8EA,A8B1FA,AGTA;AxCyHA,ACHA,ACHA,ACHA,AmCzGA,APqBA,AENA,A1B8EA,A8B1FA,AGTA;AxCyHA,ACHA,ACHA,AoC5GA,APqBA,AENA,A1B8EA,A8B1FA,AGTA;AxCyHA,ACHA,ACHA,AoC5GA,APqBA,AENA,A1B8EA,A8B1FA,AGTA;AxCyHA,ACHA,ACHA,AoC5GA,APqBA,AENA,A1B8EA,A8B1FA,AGTA;AxCyHA,ACHA,ACHA,AoC5GA,APqBA,AENA,A1B8EA,A8B1FA,AGTA;AxCyHA,ACHA,ACHA,AoC5GA,APqBA,AENA,A1B8EA,A8B1FA,AGTA;AxCyHA,ACHA,ACHA,AoC5GA,APqBA,AENA,A1B8EA,A8B1FA,AGTA;AxCyHA,ACHA,ACHA,AoC5GA,APqBA,AENA,A1B8EA,A8B1FA,AGTA;AxCyHA,ACHA,ACHA,AoC5GA,APqBA,AENA,A1B8EA,A8B1FA,AGTA;AxCyHA,ACHA,ACHA,AoC5GA,APqBA,AENA,A1B8EA,A8B1FA,AGTA;AxCyHA,ACHA,ACHA,AoC5GA,APqBA,AENA,A1B8EA,AiCnGA;AxCyHA,ACHA,ACHA,AoC5GA,APqBA,AENA,A1B8EA,AiCnGA;AxCyHA,ACHA,ACHA,AoC5GA,APqBA,AENA,A1B8EA,AiCnGA;AxCyHA,ACHA,ACHA,AoC5GA,APqBA,AENA,A1B8EA,AiCnGA;AxCyHA,ACHA,ACHA,AoC5GA,APqBA,AENA,A1B8EA,AiCnGA;AxCyHA,ACHA,ACHA,AoC5GA,APqBA,AENA,A1B8EA,AiCnGA;AxCyHA,ACHA,ACHA,AoC5GA,APqBA,AENA,A1B8EA,AiCnGA;AxCyHA,ACHA,ACHA,AoC5GA,APqBA,AENA,A1B8EA,AiCnGA;AxCyHA,ACHA,ACHA,AoC5GA,APqBA,AENA,A1B8EA,AiCnGA;AxCyHA,ACHA,ACHA,AoC5GA,APqBA,AENA,A1B8EA,AiCnGA;AxCyHA,ACHA,ACHA,AoC5GA,APqBA,AENA,A1B8EA,AiCnGA;AxCyHA,ACHA,ACHA,AoC5GA,APqBA,AENA,A1B8EA,AiCnGA;AxCyHA,ACHA,ACHA,AoC5GA,APqBA,AENA,A1B8EA,AiCnGA;AxCyHA,ACHA,ACHA,AoC5GA,APqBA,AENA,A1B8EA,AiCnGA;AxCyHA,ACHA,ACHA,AoC5GA,APqBA,AENA,A1B8EA,AiCnGA;AxCyHA,ACHA,ACHA,AoC5GA,APqBA,AENA,A1B8EA,AiCnGA;AxCyHA,ACHA,ACHA,AoC5GA,APqBA,AENA,A1B8EA,AiCnGA;AxCyHA,ACHA,ACHA,AoC5GA,APqBA,AENA,A1B8EA,AiCnGA;AxCyHA,ACHA,ACHA,AoC5GA,APqBA,AENA,A1B8EA,AiCnGA;AxCyHA,ACHA,AqC/GA,APqBA,AENA,A1B8EA,AiCnGA;AxCyHA,ACHA,AqC/GA,APqBA,AENA,A1B8EA,AiCnGA;AxCyHA,ACHA,AqC/GA,APqBA,AENA,A1B8EA,AiCnGA;AxCyHA,ACHA,AqC/GA,APqBA,AENA,A1B8EA,AiCnGA;AxCyHA,ACHA,AqC/GA,APqBA,AENA,A1B8EA,AiCnGA;AxCyHA,ACHA,AqC/GA,APqBA,AENA,A1B8EA,AiCnGA;AxCyHA,ACHA,AqC/GA,APqBA,AENA,A1B8EA,AiCnGA;AxCyHA,ACHA,AqC/GA,APqBA,AENA,A1B8EA,AiCnGA;AxCyHA,ACHA,AqC/GA,APqBA,AENA,A1B8EA,AiCnGA;AxCyHA,ACHA,AqC/GA,APqBA,AENA,A1B8EA,AiCnGA;AxCyHA,ACHA,AqC/GA,APqBA,AENA,A1B8EA,AiCnGA;AxCyHA,ACHA,AqC/GA,APqBA,AENA,A1B8EA,AiCnGA;AxCyHA,ACHA,AqC/GA,APqBA,AENA,A1B8EA;APsBA,ACHA,AqC/GA,APqBA,AENA,A1B8EA;APsBA,ACHA,AqC/GA,APqBA,AENA,A1B8EA;APsBA,ACHA,AqC/GA,APqBA,AENA,A1B8EA;APsBA,ACHA,AqC/GA,APqBA,AENA,A1B8EA;APsBA,ACHA,AqC/GA,APqBA,AENA,A1B8EA;APsBA,ACHA,AqC/GA,APqBA,AENA,A1B8EA;APsBA,ACHA,AqC/GA,APqBA,AENA,A1B8EA;APsBA,ACHA,AqC/GA,APqBA,AENA,A1B8EA;APsBA,ACHA,AqC/GA,APqBA,AENA,A1B8EA;APsBA,ACHA,AqC/GA,APqBA,AENA,A1B8EA;APsBA,ACHA,AqC/GA,APqBA,AENA,A1B8EA;APsBA,ACHA,AqC/GA,APqBA,AENA,A1B8EA;APsBA,ACHA,AqC/GA,APqBA,AENA,A1B8EA;APsBA,ACHA,AqC/GA,APqBA,AENA,A1B8EA;APsBA,ACHA,AqC/GA,APqBA,AENA,A1B8EA;APsBA,ACHA,AqC/GA,APqBA,AENA,A1B8EA;APsBA,ACHA,AqC/GA,APqBA,AENA,A1B8EA;APsBA,ACHA,AqC/GA,APqBA,AENA,A1B8EA;APsBA,ACHA,AqC/GA,APqBA,AENA,A1B8EA;APsBA,ACHA,AqC/GA,APqBA,AENA,A1B8EA;APsBA,ACHA,AqC/GA,APqBA,AENA,A1B8EA;APsBA,ACHA,AqC/GA,APqBA,AENA,A1B8EA;APsBA,ACHA,AqC/GA,APqBA,AENA,A1B8EA;APsBA,ACHA,AqC/GA,APqBA,AENA,A1B8EA;APsBA,ACHA,AqC/GA,APqBA,AENA,A1B8EA;APsBA,ACHA,AqC/GA,APqBA,AENA,A1B8EA;APsBA,ACHA,AqC/GA,APqBA,AENA,A1B8EA;APsBA,ACHA,AqC/GA,APqBA,AENA,A1B8EA;APsBA,ACHA,AqC/GA,APqBA,AENA,A1B8EA;APsBA,ACHA,AqC/GA,APqBA,AENA,A1B8EA;APsBA,ACHA,AqC/GA,APqBA,AENA,A1B8EA;APsBA,ACHA,AqC/GA,APqBA,AENA,A1B8EA;APsBA,ACHA,AqC/GA,APqBA,AENA,A1B8EA;APsBA,ACHA,AqC/GA,APqBA,AENA,A1B8EA;APsBA,ACHA,AqC/GA,APqBA,AENA,A1B8EA;APsBA,ACHA,AqC/GA,APqBA,AENA,A1B8EA;APsBA,ACHA,AqC/GA,APqBA,AENA,A1B8EA;APsBA,ACHA,AqC/GA,APqBA,AENA,A1B8EA;APsBA,ACHA,AqC/GA,APqBA,AENA,A1B8EA;APsBA,ACHA,AqC/GA,APqBA,AENA,A1B8EA;APsBA,ACHA,AqC/GA,APqBA,AENA,A1B8EA;APsBA,ACHA,AqC/GA,APqBA,AENA,A1B8EA;APsBA,ACHA,AqC/GA,APqBA,AENA,A1B8EA;APsBA,ACHA,AqC/GA,APqBA,AENA,A1B8EA;APsBA,ACHA,AqC/GA,APqBA,AENA,A1B8EA;APsBA,ACHA,AqC/GA,APqBA,AENA,A1B8EA;APsBA,ACHA,AqC/GA,APqBA,AENA,A1B8EA;APsBA,ACHA,AqC/GA,APqBA,AENA,A1B8EA;APsBA,ACHA,AqC/GA,APqBA,AENA,A1B8EA;APsBA,ACHA,AqC/GA,APqBA,AENA,A1B8EA;APsBA,ACHA,AqC/GA,APqBA,AENA,A1B8EA;APsBA,ACHA,AqC/GA,APqBA,AENA,A1B8EA;APsBA,ACHA,AqC/GA,APqBA,AENA,A1B8EA;APsBA,ACHA,A8B1FA,AENA,A1B8EA;APsBA,ACHA,A8B1FA,AENA,A1B8EA;APsBA,ACHA,A8B1FA,AENA,A1B8EA;APsBA,ACHA,A8B1FA,AENA,A1B8EA;APsBA,ACHA,A8B1FA,AENA,A1B8EA;APsBA,ACHA,A8B1FA,AENA,A1B8EA;APsBA,ACHA,A8B1FA,AENA,A1B8EA;APsBA,ACHA,A8B1FA,AENA,A1B8EA;APsBA,ACHA,A8B1FA,AENA,A1B8EA;APsBA,ACHA,A8B1FA,AENA,A1B8EA;APsBA,ACHA,A8B1FA,AENA,A1B8EA;APsBA,ACHA,A8B1FA,AENA,A1B8EA;APsBA,ACHA,A8B1FA,AENA,A1B8EA;APsBA,ACHA,A8B1FA,AENA,A1B8EA;APsBA,ACHA,A8B1FA,AENA,A1B8EA;APsBA,ACHA,A8B1FA,AENA,A1B8EA;APsBA,ACHA,A8B1FA,AENA,A1B8EA;APsBA,ACHA,A8B1FA,AENA,A1B8EA;APsBA,ACHA,A8B1FA,AENA,A1B8EA;APsBA,ACHA,A8B1FA,AENA,A1B8EA;APsBA,ACHA,A8B1FA,AENA,A1B8EA;APsBA,ACHA,A8B1FA,AENA,A1B8EA;APsBA,ACHA,A8B1FA,AENA,A1B8EA;APsBA,ACHA,A8B1FA,AENA,A1B8EA;APsBA,ACHA,A8B1FA,AENA,A1B8EA;APsBA,ACHA,A8B1FA,AENA,A1B8EA;APsBA,ACHA,A8B1FA,AENA,A1B8EA;APsBA,ACHA,A8B1FA,AxBwEA;APsBA,ACHA,A8B1FA,AxBwEA;APsBA,ACHA,A8B1FA,AxBwEA;APsBA,ACHA,A8B1FA,AxBwEA;APsBA,ACHA,A8B1FA,AxBwEA;APsBA,ACHA,A8B1FA,AxBwEA;APsBA,ACHA,A8B1FA,AxBwEA;APsBA,ACHA,A8B1FA,AxBwEA;APsBA,ACHA,A8B1FA,AxBwEA;APsBA,ACHA,A8B1FA,AxBwEA;APsBA,ACHA,A8B1FA,AxBwEA;APsBA,ACHA,A8B1FA,AxBwEA;APsBA,ACHA,A8B1FA,AxBwEA;APsBA,ACHA,A8B1FA,AxBwEA;APsBA,ACHA,A8B1FA,AxBwEA;APsBA,ACHA,A8B1FA,AxBwEA;APsBA,ACHA,A8B1FA,AxBwEA;APsBA,ACHA,A8B1FA,AxBwEA;APsBA,ACHA,A8B1FA,AxBwEA;APsBA,ACHA,A8B1FA,AxBwEA;APsBA,ACHA,A8B1FA,AxBwEA;APsBA,A+B7FA,AxBwEA;APsBA,A+B7FA,AxBwEA;APsBA,A+B7FA,AxBwEA;APsBA,A+B7FA,AxBwEA;APsBA,A+B7FA,AxBwEA;APsBA,A+B7FA,AxBwEA;APsBA,A+B7FA,AxBwEA;APsBA,A+B7FA,AxBwEA;APsBA,A+B7FA,AxBwEA;APsBA,A+B7FA,AxBwEA;APsBA,A+B7FA,AxBwEA;APsBA,A+B7FA,AxBwEA;APsBA,A+B7FA,AxBwEA;APsBA,A+B7FA,AxBwEA;APsBA,A+B7FA,AxBwEA;APsBA,A+B7FA,AxBwEA;APsBA,A+B7FA,AxBwEA;APsBA,A+B7FA,AxBwEA;APsBA,A+B7FA,AxBwEA;APsBA,A+B7FA,AxBwEA;APsBA,A+B7FA,AxBwEA;APsBA,A+B7FA,AxBwEA;APsBA,A+B7FA,AxBwEA;APsBA,A+B7FA,AxBwEA;APsBA,A+B7FA,AxBwEA;APsBA,A+B7FA,AxBwEA;APsBA,A+B7FA,AxBwEA;APsBA,A+B7FA,AxBwEA;APsBA,A+B7FA,AxBwEA;APsBA,A+B7FA,AxBwEA;APsBA,A+B7FA,AxBwEA;APsBA,A+B7FA,AxBwEA;APsBA,A+B7FA,AxBwEA;APsBA,A+B7FA,AxBwEA;APsBA,A+B7FA,AxBwEA;APsBA,A+B7FA,AxBwEA;APsBA,A+B7FA,AxBwEA;APsBA,A+B7FA,AxBwEA;APsBA,AOrBA;APsBA,AOrBA;APsBA,AOrBA;APsBA,AOrBA;APsBA,AOrBA;APsBA,AOrBA;APsBA,AOrBA;APsBA,AOrBA;APsBA,AOrBA;APsBA,AOrBA;APsBA,AOrBA;APsBA,AOrBA;APsBA,AOrBA;APsBA,AOrBA;APsBA,AOrBA;APsBA,AOrBA;APsBA,AOrBA;APsBA,AOrBA;APsBA,AOrBA;APsBA,AOrBA;APsBA,AOrBA;APsBA,AOrBA;APsBA,AOrBA;APsBA,AOrBA;APsBA,AOrBA;APsBA,AOrBA;APsBA,AOrBA;APsBA,AOrBA;APsBA,AOrBA;APsBA,AOrBA;APsBA,AOrBA;APsBA,AOrBA;APsBA,AOrBA;APsBA,AOrBA;APsBA,AOrBA;APsBA,AOrBA;APsBA,AOrBA;APsBA,AOrBA;APsBA,AOrBA;APsBA,AOrBA;APsBA,AOrBA;APsBA,AOrBA;APsBA,AOrBA;APsBA,AOrBA;APsBA,AOrBA;APsBA,AOrBA;APsBA,AOrBA;APsBA,AOrBA;APsBA,AOrBA;APsBA,AOrBA;APsBA,AOrBA;APsBA,AOrBA;APsBA,AOrBA;APsBA,AOrBA;APsBA,AOrBA;APsBA,AOrBA;APsBA,AOrBA;APsBA,AOrBA;APsBA,AOrBA;APsBA,AOrBA;APsBA,AOrBA;APsBA,AOrBA;APsBA,AOrBA;APsBA,AOrBA;APsBA,AOrBA;APsBA,AOrBA;APsBA,AOrBA;APsBA,AOrBA;APsBA,AOrBA;APsBA,AOrBA;APsBA,AOrBA;APsBA,AOrBA;APsBA,AOrBA;APsBA,AOrBA;APsBA,AOrBA;APsBA;AACA;AACA", "file": "index.js", "sourcesContent": ["\n\nvar compileSchema = require('./compile')\n  , resolve = require('./compile/resolve')\n  , Cache = require('./cache')\n  , SchemaObject = require('./compile/schema_obj')\n  , stableStringify = require('fast-json-stable-stringify')\n  , formats = require('./compile/formats')\n  , rules = require('./compile/rules')\n  , $dataMetaSchema = require('./data')\n  , util = require('./compile/util');\n\nmodule.exports = Ajv;\n\nAjv.prototype.validate = validate;\nAjv.prototype.compile = compile;\nAjv.prototype.addSchema = addSchema;\nAjv.prototype.addMetaSchema = addMetaSchema;\nAjv.prototype.validateSchema = validateSchema;\nAjv.prototype.getSchema = getSchema;\nAjv.prototype.removeSchema = removeSchema;\nAjv.prototype.addFormat = addFormat;\nAjv.prototype.errorsText = errorsText;\n\nAjv.prototype._addSchema = _addSchema;\nAjv.prototype._compile = _compile;\n\nAjv.prototype.compileAsync = require('./compile/async');\nvar customKeyword = require('./keyword');\nAjv.prototype.addKeyword = customKeyword.add;\nAjv.prototype.getKeyword = customKeyword.get;\nAjv.prototype.removeKeyword = customKeyword.remove;\nAjv.prototype.validateKeyword = customKeyword.validate;\n\nvar errorClasses = require('./compile/error_classes');\nAjv.ValidationError = errorClasses.Validation;\nAjv.MissingRefError = errorClasses.MissingRef;\nAjv.$dataMetaSchema = $dataMetaSchema;\n\nvar META_SCHEMA_ID = 'http://json-schema.org/draft-07/schema';\n\nvar META_IGNORE_OPTIONS = [ 'removeAdditional', 'useDefaults', 'coerceTypes', 'strictDefaults' ];\nvar META_SUPPORT_DATA = ['/properties'];\n\n/**\n * Creates validator instance.\n * Usage: `Ajv(opts)`\n * @param {Object} opts optional options\n * @return {Object} ajv instance\n */\nfunction Ajv(opts) {\n  if (!(this instanceof Ajv)) return new Ajv(opts);\n  opts = this._opts = util.copy(opts) || {};\n  setLogger(this);\n  this._schemas = {};\n  this._refs = {};\n  this._fragments = {};\n  this._formats = formats(opts.format);\n\n  this._cache = opts.cache || new Cache;\n  this._loadingSchemas = {};\n  this._compilations = [];\n  this.RULES = rules();\n  this._getId = chooseGetId(opts);\n\n  opts.loopRequired = opts.loopRequired || Infinity;\n  if (opts.errorDataPath == 'property') opts._errorDataPathProperty = true;\n  if (opts.serialize === undefined) opts.serialize = stableStringify;\n  this._metaOpts = getMetaSchemaOptions(this);\n\n  if (opts.formats) addInitialFormats(this);\n  if (opts.keywords) addInitialKeywords(this);\n  addDefaultMetaSchema(this);\n  if (typeof opts.meta == 'object') this.addMetaSchema(opts.meta);\n  if (opts.nullable) this.addKeyword('nullable', {metaSchema: {type: 'boolean'}});\n  addInitialSchemas(this);\n}\n\n\n\n/**\n * Validate data using schema\n * Schema will be compiled and cached (using serialized JSON as key. [fast-json-stable-stringify](https://github.com/epoberezkin/fast-json-stable-stringify) is used to serialize.\n * @this   Ajv\n * @param  {String|Object} schemaKeyRef key, ref or schema object\n * @param  {Any} data to be validated\n * @return {Boolean} validation result. Errors from the last validation will be available in `ajv.errors` (and also in compiled schema: `schema.errors`).\n */\nfunction validate(schemaKeyRef, data) {\n  var v;\n  if (typeof schemaKeyRef == 'string') {\n    v = this.getSchema(schemaKeyRef);\n    if (!v) throw new Error('no schema with key or ref \"' + schemaKeyRef + '\"');\n  } else {\n    var schemaObj = this._addSchema(schemaKeyRef);\n    v = schemaObj.validate || this._compile(schemaObj);\n  }\n\n  var valid = v(data);\n  if (v.$async !== true) this.errors = v.errors;\n  return valid;\n}\n\n\n/**\n * Create validating function for passed schema.\n * @this   Ajv\n * @param  {Object} schema schema object\n * @param  {Boolean} _meta true if schema is a meta-schema. Used internally to compile meta schemas of custom keywords.\n * @return {Function} validating function\n */\nfunction compile(schema, _meta) {\n  var schemaObj = this._addSchema(schema, undefined, _meta);\n  return schemaObj.validate || this._compile(schemaObj);\n}\n\n\n/**\n * Adds schema to the instance.\n * @this   Ajv\n * @param {Object|Array} schema schema or array of schemas. If array is passed, `key` and other parameters will be ignored.\n * @param {String} key Optional schema key. Can be passed to `validate` method instead of schema object or id/ref. One schema per instance can have empty `id` and `key`.\n * @param {Boolean} _skipValidation true to skip schema validation. Used internally, option validateSchema should be used instead.\n * @param {Boolean} _meta true if schema is a meta-schema. Used internally, addMetaSchema should be used instead.\n * @return {Ajv} this for method chaining\n */\nfunction addSchema(schema, key, _skipValidation, _meta) {\n  if (Array.isArray(schema)){\n    for (var i=0; i<schema.length; i++) this.addSchema(schema[i], undefined, _skipValidation, _meta);\n    return this;\n  }\n  var id = this._getId(schema);\n  if (id !== undefined && typeof id != 'string')\n    throw new Error('schema id must be string');\n  key = resolve.normalizeId(key || id);\n  checkUnique(this, key);\n  this._schemas[key] = this._addSchema(schema, _skipValidation, _meta, true);\n  return this;\n}\n\n\n/**\n * Add schema that will be used to validate other schemas\n * options in META_IGNORE_OPTIONS are alway set to false\n * @this   Ajv\n * @param {Object} schema schema object\n * @param {String} key optional schema key\n * @param {Boolean} skipValidation true to skip schema validation, can be used to override validateSchema option for meta-schema\n * @return {Ajv} this for method chaining\n */\nfunction addMetaSchema(schema, key, skipValidation) {\n  this.addSchema(schema, key, skipValidation, true);\n  return this;\n}\n\n\n/**\n * Validate schema\n * @this   Ajv\n * @param {Object} schema schema to validate\n * @param {Boolean} throwOrLogError pass true to throw (or log) an error if invalid\n * @return {Boolean} true if schema is valid\n */\nfunction validateSchema(schema, throwOrLogError) {\n  var $schema = schema.$schema;\n  if ($schema !== undefined && typeof $schema != 'string')\n    throw new Error('$schema must be a string');\n  $schema = $schema || this._opts.defaultMeta || defaultMeta(this);\n  if (!$schema) {\n    this.logger.warn('meta-schema not available');\n    this.errors = null;\n    return true;\n  }\n  var valid = this.validate($schema, schema);\n  if (!valid && throwOrLogError) {\n    var message = 'schema is invalid: ' + this.errorsText();\n    if (this._opts.validateSchema == 'log') this.logger.error(message);\n    else throw new Error(message);\n  }\n  return valid;\n}\n\n\nfunction defaultMeta(self) {\n  var meta = self._opts.meta;\n  self._opts.defaultMeta = typeof meta == 'object'\n                            ? self._getId(meta) || meta\n                            : self.getSchema(META_SCHEMA_ID)\n                              ? META_SCHEMA_ID\n                              : undefined;\n  return self._opts.defaultMeta;\n}\n\n\n/**\n * Get compiled schema from the instance by `key` or `ref`.\n * @this   Ajv\n * @param  {String} keyRef `key` that was passed to `addSchema` or full schema reference (`schema.id` or resolved id).\n * @return {Function} schema validating function (with property `schema`).\n */\nfunction getSchema(keyRef) {\n  var schemaObj = _getSchemaObj(this, keyRef);\n  switch (typeof schemaObj) {\n    case 'object': return schemaObj.validate || this._compile(schemaObj);\n    case 'string': return this.getSchema(schemaObj);\n    case 'undefined': return _getSchemaFragment(this, keyRef);\n  }\n}\n\n\nfunction _getSchemaFragment(self, ref) {\n  var res = resolve.schema.call(self, { schema: {} }, ref);\n  if (res) {\n    var schema = res.schema\n      , root = res.root\n      , baseId = res.baseId;\n    var v = compileSchema.call(self, schema, root, undefined, baseId);\n    self._fragments[ref] = new SchemaObject({\n      ref: ref,\n      fragment: true,\n      schema: schema,\n      root: root,\n      baseId: baseId,\n      validate: v\n    });\n    return v;\n  }\n}\n\n\nfunction _getSchemaObj(self, keyRef) {\n  keyRef = resolve.normalizeId(keyRef);\n  return self._schemas[keyRef] || self._refs[keyRef] || self._fragments[keyRef];\n}\n\n\n/**\n * Remove cached schema(s).\n * If no parameter is passed all schemas but meta-schemas are removed.\n * If RegExp is passed all schemas with key/id matching pattern but meta-schemas are removed.\n * Even if schema is referenced by other schemas it still can be removed as other schemas have local references.\n * @this   Ajv\n * @param  {String|Object|RegExp} schemaKeyRef key, ref, pattern to match key/ref or schema object\n * @return {Ajv} this for method chaining\n */\nfunction removeSchema(schemaKeyRef) {\n  if (schemaKeyRef instanceof RegExp) {\n    _removeAllSchemas(this, this._schemas, schemaKeyRef);\n    _removeAllSchemas(this, this._refs, schemaKeyRef);\n    return this;\n  }\n  switch (typeof schemaKeyRef) {\n    case 'undefined':\n      _removeAllSchemas(this, this._schemas);\n      _removeAllSchemas(this, this._refs);\n      this._cache.clear();\n      return this;\n    case 'string':\n      var schemaObj = _getSchemaObj(this, schemaKeyRef);\n      if (schemaObj) this._cache.del(schemaObj.cacheKey);\n      delete this._schemas[schemaKeyRef];\n      delete this._refs[schemaKeyRef];\n      return this;\n    case 'object':\n      var serialize = this._opts.serialize;\n      var cacheKey = serialize ? serialize(schemaKeyRef) : schemaKeyRef;\n      this._cache.del(cacheKey);\n      var id = this._getId(schemaKeyRef);\n      if (id) {\n        id = resolve.normalizeId(id);\n        delete this._schemas[id];\n        delete this._refs[id];\n      }\n  }\n  return this;\n}\n\n\nfunction _removeAllSchemas(self, schemas, regex) {\n  for (var keyRef in schemas) {\n    var schemaObj = schemas[keyRef];\n    if (!schemaObj.meta && (!regex || regex.test(keyRef))) {\n      self._cache.del(schemaObj.cacheKey);\n      delete schemas[keyRef];\n    }\n  }\n}\n\n\n/* @this   Ajv */\nfunction _addSchema(schema, skipValidation, meta, shouldAddSchema) {\n  if (typeof schema != 'object' && typeof schema != 'boolean')\n    throw new Error('schema should be object or boolean');\n  var serialize = this._opts.serialize;\n  var cacheKey = serialize ? serialize(schema) : schema;\n  var cached = this._cache.get(cacheKey);\n  if (cached) return cached;\n\n  shouldAddSchema = shouldAddSchema || this._opts.addUsedSchema !== false;\n\n  var id = resolve.normalizeId(this._getId(schema));\n  if (id && shouldAddSchema) checkUnique(this, id);\n\n  var willValidate = this._opts.validateSchema !== false && !skipValidation;\n  var recursiveMeta;\n  if (willValidate && !(recursiveMeta = id && id == resolve.normalizeId(schema.$schema)))\n    this.validateSchema(schema, true);\n\n  var localRefs = resolve.ids.call(this, schema);\n\n  var schemaObj = new SchemaObject({\n    id: id,\n    schema: schema,\n    localRefs: localRefs,\n    cacheKey: cacheKey,\n    meta: meta\n  });\n\n  if (id[0] != '#' && shouldAddSchema) this._refs[id] = schemaObj;\n  this._cache.put(cacheKey, schemaObj);\n\n  if (willValidate && recursiveMeta) this.validateSchema(schema, true);\n\n  return schemaObj;\n}\n\n\n/* @this   Ajv */\nfunction _compile(schemaObj, root) {\n  if (schemaObj.compiling) {\n    schemaObj.validate = callValidate;\n    callValidate.schema = schemaObj.schema;\n    callValidate.errors = null;\n    callValidate.root = root ? root : callValidate;\n    if (schemaObj.schema.$async === true)\n      callValidate.$async = true;\n    return callValidate;\n  }\n  schemaObj.compiling = true;\n\n  var currentOpts;\n  if (schemaObj.meta) {\n    currentOpts = this._opts;\n    this._opts = this._metaOpts;\n  }\n\n  var v;\n  try { v = compileSchema.call(this, schemaObj.schema, root, schemaObj.localRefs); }\n  catch(e) {\n    delete schemaObj.validate;\n    throw e;\n  }\n  finally {\n    schemaObj.compiling = false;\n    if (schemaObj.meta) this._opts = currentOpts;\n  }\n\n  schemaObj.validate = v;\n  schemaObj.refs = v.refs;\n  schemaObj.refVal = v.refVal;\n  schemaObj.root = v.root;\n  return v;\n\n\n  /* @this   {*} - custom context, see passContext option */\n  function callValidate() {\n    /* jshint validthis: true */\n    var _validate = schemaObj.validate;\n    var result = _validate.apply(this, arguments);\n    callValidate.errors = _validate.errors;\n    return result;\n  }\n}\n\n\nfunction chooseGetId(opts) {\n  switch (opts.schemaId) {\n    case 'auto': return _get$IdOrId;\n    case 'id': return _getId;\n    default: return _get$Id;\n  }\n}\n\n/* @this   Ajv */\nfunction _getId(schema) {\n  if (schema.$id) this.logger.warn('schema $id ignored', schema.$id);\n  return schema.id;\n}\n\n/* @this   Ajv */\nfunction _get$Id(schema) {\n  if (schema.id) this.logger.warn('schema id ignored', schema.id);\n  return schema.$id;\n}\n\n\nfunction _get$IdOrId(schema) {\n  if (schema.$id && schema.id && schema.$id != schema.id)\n    throw new Error('schema $id is different from id');\n  return schema.$id || schema.id;\n}\n\n\n/**\n * Convert array of error message objects to string\n * @this   Ajv\n * @param  {Array<Object>} errors optional array of validation errors, if not passed errors from the instance are used.\n * @param  {Object} options optional options with properties `separator` and `dataVar`.\n * @return {String} human readable string with all errors descriptions\n */\nfunction errorsText(errors, options) {\n  errors = errors || this.errors;\n  if (!errors) return 'No errors';\n  options = options || {};\n  var separator = options.separator === undefined ? ', ' : options.separator;\n  var dataVar = options.dataVar === undefined ? 'data' : options.dataVar;\n\n  var text = '';\n  for (var i=0; i<errors.length; i++) {\n    var e = errors[i];\n    if (e) text += dataVar + e.dataPath + ' ' + e.message + separator;\n  }\n  return text.slice(0, -separator.length);\n}\n\n\n/**\n * Add custom format\n * @this   Ajv\n * @param {String} name format name\n * @param {String|RegExp|Function} format string is converted to RegExp; function should return boolean (true when valid)\n * @return {Ajv} this for method chaining\n */\nfunction addFormat(name, format) {\n  if (typeof format == 'string') format = new RegExp(format);\n  this._formats[name] = format;\n  return this;\n}\n\n\nfunction addDefaultMetaSchema(self) {\n  var $dataSchema;\n  if (self._opts.$data) {\n    $dataSchema = require('./refs/data.json');\n    self.addMetaSchema($dataSchema, $dataSchema.$id, true);\n  }\n  if (self._opts.meta === false) return;\n  var metaSchema = require('./refs/json-schema-draft-07.json');\n  if (self._opts.$data) metaSchema = $dataMetaSchema(metaSchema, META_SUPPORT_DATA);\n  self.addMetaSchema(metaSchema, META_SCHEMA_ID, true);\n  self._refs['http://json-schema.org/schema'] = META_SCHEMA_ID;\n}\n\n\nfunction addInitialSchemas(self) {\n  var optsSchemas = self._opts.schemas;\n  if (!optsSchemas) return;\n  if (Array.isArray(optsSchemas)) self.addSchema(optsSchemas);\n  else for (var key in optsSchemas) self.addSchema(optsSchemas[key], key);\n}\n\n\nfunction addInitialFormats(self) {\n  for (var name in self._opts.formats) {\n    var format = self._opts.formats[name];\n    self.addFormat(name, format);\n  }\n}\n\n\nfunction addInitialKeywords(self) {\n  for (var name in self._opts.keywords) {\n    var keyword = self._opts.keywords[name];\n    self.addKeyword(name, keyword);\n  }\n}\n\n\nfunction checkUnique(self, id) {\n  if (self._schemas[id] || self._refs[id])\n    throw new Error('schema with key or id \"' + id + '\" already exists');\n}\n\n\nfunction getMetaSchemaOptions(self) {\n  var metaOpts = util.copy(self._opts);\n  for (var i=0; i<META_IGNORE_OPTIONS.length; i++)\n    delete metaOpts[META_IGNORE_OPTIONS[i]];\n  return metaOpts;\n}\n\n\nfunction setLogger(self) {\n  var logger = self._opts.logger;\n  if (logger === false) {\n    self.logger = {log: noop, warn: noop, error: noop};\n  } else {\n    if (logger === undefined) logger = console;\n    if (!(typeof logger == 'object' && logger.log && logger.warn && logger.error))\n      throw new Error('logger must implement log, warn and error methods');\n    self.logger = logger;\n  }\n}\n\n\nfunction noop() {}\n", "\n\nvar resolve = require('./resolve')\n  , util = require('./util')\n  , errorClasses = require('./error_classes')\n  , stableStringify = require('fast-json-stable-stringify');\n\nvar validateGenerator = require('../dotjs/validate');\n\n/**\n * Functions below are used inside compiled validations function\n */\n\nvar ucs2length = util.ucs2length;\nvar equal = require('fast-deep-equal');\n\n// this error is thrown by async schemas to return validation errors via exception\nvar ValidationError = errorClasses.Validation;\n\nmodule.exports = compile;\n\n\n/**\n * Compiles schema to validation function\n * @this   Ajv\n * @param  {Object} schema schema object\n * @param  {Object} root object with information about the root schema for this schema\n * @param  {Object} localRefs the hash of local references inside the schema (created by resolve.id), used for inline resolution\n * @param  {String} baseId base ID for IDs in the schema\n * @return {Function} validation function\n */\nfunction compile(schema, root, localRefs, baseId) {\n  /* jshint validthis: true, evil: true */\n  /* eslint no-shadow: 0 */\n  var self = this\n    , opts = this._opts\n    , refVal = [ undefined ]\n    , refs = {}\n    , patterns = []\n    , patternsHash = {}\n    , defaults = []\n    , defaultsHash = {}\n    , customRules = [];\n\n  root = root || { schema: schema, refVal: refVal, refs: refs };\n\n  var c = checkCompiling.call(this, schema, root, baseId);\n  var compilation = this._compilations[c.index];\n  if (c.compiling) return (compilation.callValidate = callValidate);\n\n  var formats = this._formats;\n  var RULES = this.RULES;\n\n  try {\n    var v = localCompile(schema, root, localRefs, baseId);\n    compilation.validate = v;\n    var cv = compilation.callValidate;\n    if (cv) {\n      cv.schema = v.schema;\n      cv.errors = null;\n      cv.refs = v.refs;\n      cv.refVal = v.refVal;\n      cv.root = v.root;\n      cv.$async = v.$async;\n      if (opts.sourceCode) cv.source = v.source;\n    }\n    return v;\n  } finally {\n    endCompiling.call(this, schema, root, baseId);\n  }\n\n  /* @this   {*} - custom context, see passContext option */\n  function callValidate() {\n    /* jshint validthis: true */\n    var validate = compilation.validate;\n    var result = validate.apply(this, arguments);\n    callValidate.errors = validate.errors;\n    return result;\n  }\n\n  function localCompile(_schema, _root, localRefs, baseId) {\n    var isRoot = !_root || (_root && _root.schema == _schema);\n    if (_root.schema != root.schema)\n      return compile.call(self, _schema, _root, localRefs, baseId);\n\n    var $async = _schema.$async === true;\n\n    var sourceCode = validateGenerator({\n      isTop: true,\n      schema: _schema,\n      isRoot: isRoot,\n      baseId: baseId,\n      root: _root,\n      schemaPath: '',\n      errSchemaPath: '#',\n      errorPath: '\"\"',\n      MissingRefError: errorClasses.MissingRef,\n      RULES: RULES,\n      validate: validateGenerator,\n      util: util,\n      resolve: resolve,\n      resolveRef: resolveRef,\n      usePattern: usePattern,\n      useDefault: useDefault,\n      useCustomRule: useCustomRule,\n      opts: opts,\n      formats: formats,\n      logger: self.logger,\n      self: self\n    });\n\n    sourceCode = vars(refVal, refValCode) + vars(patterns, patternCode)\n                   + vars(defaults, defaultCode) + vars(customRules, customRuleCode)\n                   + sourceCode;\n\n    if (opts.processCode) sourceCode = opts.processCode(sourceCode, _schema);\n    // console.log('\\n\\n\\n *** \\n', JSON.stringify(sourceCode));\n    var validate;\n    try {\n      var makeValidate = new Function(\n        'self',\n        'RULES',\n        'formats',\n        'root',\n        'refVal',\n        'defaults',\n        'customRules',\n        'equal',\n        'ucs2length',\n        'ValidationError',\n        sourceCode\n      );\n\n      validate = makeValidate(\n        self,\n        RULES,\n        formats,\n        root,\n        refVal,\n        defaults,\n        customRules,\n        equal,\n        ucs2length,\n        ValidationError\n      );\n\n      refVal[0] = validate;\n    } catch(e) {\n      self.logger.error('Error compiling schema, function code:', sourceCode);\n      throw e;\n    }\n\n    validate.schema = _schema;\n    validate.errors = null;\n    validate.refs = refs;\n    validate.refVal = refVal;\n    validate.root = isRoot ? validate : _root;\n    if ($async) validate.$async = true;\n    if (opts.sourceCode === true) {\n      validate.source = {\n        code: sourceCode,\n        patterns: patterns,\n        defaults: defaults\n      };\n    }\n\n    return validate;\n  }\n\n  function resolveRef(baseId, ref, isRoot) {\n    ref = resolve.url(baseId, ref);\n    var refIndex = refs[ref];\n    var _refVal, refCode;\n    if (refIndex !== undefined) {\n      _refVal = refVal[refIndex];\n      refCode = 'refVal[' + refIndex + ']';\n      return resolvedRef(_refVal, refCode);\n    }\n    if (!isRoot && root.refs) {\n      var rootRefId = root.refs[ref];\n      if (rootRefId !== undefined) {\n        _refVal = root.refVal[rootRefId];\n        refCode = addLocalRef(ref, _refVal);\n        return resolvedRef(_refVal, refCode);\n      }\n    }\n\n    refCode = addLocalRef(ref);\n    var v = resolve.call(self, localCompile, root, ref);\n    if (v === undefined) {\n      var localSchema = localRefs && localRefs[ref];\n      if (localSchema) {\n        v = resolve.inlineRef(localSchema, opts.inlineRefs)\n            ? localSchema\n            : compile.call(self, localSchema, root, localRefs, baseId);\n      }\n    }\n\n    if (v === undefined) {\n      removeLocalRef(ref);\n    } else {\n      replaceLocalRef(ref, v);\n      return resolvedRef(v, refCode);\n    }\n  }\n\n  function addLocalRef(ref, v) {\n    var refId = refVal.length;\n    refVal[refId] = v;\n    refs[ref] = refId;\n    return 'refVal' + refId;\n  }\n\n  function removeLocalRef(ref) {\n    delete refs[ref];\n  }\n\n  function replaceLocalRef(ref, v) {\n    var refId = refs[ref];\n    refVal[refId] = v;\n  }\n\n  function resolvedRef(refVal, code) {\n    return typeof refVal == 'object' || typeof refVal == 'boolean'\n            ? { code: code, schema: refVal, inline: true }\n            : { code: code, $async: refVal && !!refVal.$async };\n  }\n\n  function usePattern(regexStr) {\n    var index = patternsHash[regexStr];\n    if (index === undefined) {\n      index = patternsHash[regexStr] = patterns.length;\n      patterns[index] = regexStr;\n    }\n    return 'pattern' + index;\n  }\n\n  function useDefault(value) {\n    switch (typeof value) {\n      case 'boolean':\n      case 'number':\n        return '' + value;\n      case 'string':\n        return util.toQuotedString(value);\n      case 'object':\n        if (value === null) return 'null';\n        var valueStr = stableStringify(value);\n        var index = defaultsHash[valueStr];\n        if (index === undefined) {\n          index = defaultsHash[valueStr] = defaults.length;\n          defaults[index] = value;\n        }\n        return 'default' + index;\n    }\n  }\n\n  function useCustomRule(rule, schema, parentSchema, it) {\n    if (self._opts.validateSchema !== false) {\n      var deps = rule.definition.dependencies;\n      if (deps && !deps.every(function(keyword) {\n        return Object.prototype.hasOwnProperty.call(parentSchema, keyword);\n      }))\n        throw new Error('parent schema must have all required keywords: ' + deps.join(','));\n\n      var validateSchema = rule.definition.validateSchema;\n      if (validateSchema) {\n        var valid = validateSchema(schema);\n        if (!valid) {\n          var message = 'keyword schema is invalid: ' + self.errorsText(validateSchema.errors);\n          if (self._opts.validateSchema == 'log') self.logger.error(message);\n          else throw new Error(message);\n        }\n      }\n    }\n\n    var compile = rule.definition.compile\n      , inline = rule.definition.inline\n      , macro = rule.definition.macro;\n\n    var validate;\n    if (compile) {\n      validate = compile.call(self, schema, parentSchema, it);\n    } else if (macro) {\n      validate = macro.call(self, schema, parentSchema, it);\n      if (opts.validateSchema !== false) self.validateSchema(validate, true);\n    } else if (inline) {\n      validate = inline.call(self, it, rule.keyword, schema, parentSchema);\n    } else {\n      validate = rule.definition.validate;\n      if (!validate) return;\n    }\n\n    if (validate === undefined)\n      throw new Error('custom keyword \"' + rule.keyword + '\"failed to compile');\n\n    var index = customRules.length;\n    customRules[index] = validate;\n\n    return {\n      code: 'customRule' + index,\n      validate: validate\n    };\n  }\n}\n\n\n/**\n * Checks if the schema is currently compiled\n * @this   Ajv\n * @param  {Object} schema schema to compile\n * @param  {Object} root root object\n * @param  {String} baseId base schema ID\n * @return {Object} object with properties \"index\" (compilation index) and \"compiling\" (boolean)\n */\nfunction checkCompiling(schema, root, baseId) {\n  /* jshint validthis: true */\n  var index = compIndex.call(this, schema, root, baseId);\n  if (index >= 0) return { index: index, compiling: true };\n  index = this._compilations.length;\n  this._compilations[index] = {\n    schema: schema,\n    root: root,\n    baseId: baseId\n  };\n  return { index: index, compiling: false };\n}\n\n\n/**\n * Removes the schema from the currently compiled list\n * @this   Ajv\n * @param  {Object} schema schema to compile\n * @param  {Object} root root object\n * @param  {String} baseId base schema ID\n */\nfunction endCompiling(schema, root, baseId) {\n  /* jshint validthis: true */\n  var i = compIndex.call(this, schema, root, baseId);\n  if (i >= 0) this._compilations.splice(i, 1);\n}\n\n\n/**\n * Index of schema compilation in the currently compiled list\n * @this   Ajv\n * @param  {Object} schema schema to compile\n * @param  {Object} root root object\n * @param  {String} baseId base schema ID\n * @return {Integer} compilation index\n */\nfunction compIndex(schema, root, baseId) {\n  /* jshint validthis: true */\n  for (var i=0; i<this._compilations.length; i++) {\n    var c = this._compilations[i];\n    if (c.schema == schema && c.root == root && c.baseId == baseId) return i;\n  }\n  return -1;\n}\n\n\nfunction patternCode(i, patterns) {\n  return 'var pattern' + i + ' = new RegExp(' + util.toQuotedString(patterns[i]) + ');';\n}\n\n\nfunction defaultCode(i) {\n  return 'var default' + i + ' = defaults[' + i + '];';\n}\n\n\nfunction refValCode(i, refVal) {\n  return refVal[i] === undefined ? '' : 'var refVal' + i + ' = refVal[' + i + '];';\n}\n\n\nfunction customRuleCode(i) {\n  return 'var customRule' + i + ' = customRules[' + i + '];';\n}\n\n\nfunction vars(arr, statement) {\n  if (!arr.length) return '';\n  var code = '';\n  for (var i=0; i<arr.length; i++)\n    code += statement(i, arr);\n  return code;\n}\n", "\n\nvar URI = require('uri-js')\n  , equal = require('fast-deep-equal')\n  , util = require('./util')\n  , SchemaObject = require('./schema_obj')\n  , traverse = require('json-schema-traverse');\n\nmodule.exports = resolve;\n\nresolve.normalizeId = normalizeId;\nresolve.fullPath = getFullPath;\nresolve.url = resolveUrl;\nresolve.ids = resolveIds;\nresolve.inlineRef = inlineRef;\nresolve.schema = resolveSchema;\n\n/**\n * [resolve and compile the references ($ref)]\n * @this   Ajv\n * @param  {Function} compile reference to schema compilation funciton (localCompile)\n * @param  {Object} root object with information about the root schema for the current schema\n * @param  {String} ref reference to resolve\n * @return {Object|Function} schema object (if the schema can be inlined) or validation function\n */\nfunction resolve(compile, root, ref) {\n  /* jshint validthis: true */\n  var refVal = this._refs[ref];\n  if (typeof refVal == 'string') {\n    if (this._refs[refVal]) refVal = this._refs[refVal];\n    else return resolve.call(this, compile, root, refVal);\n  }\n\n  refVal = refVal || this._schemas[ref];\n  if (refVal instanceof SchemaObject) {\n    return inlineRef(refVal.schema, this._opts.inlineRefs)\n            ? refVal.schema\n            : refVal.validate || this._compile(refVal);\n  }\n\n  var res = resolveSchema.call(this, root, ref);\n  var schema, v, baseId;\n  if (res) {\n    schema = res.schema;\n    root = res.root;\n    baseId = res.baseId;\n  }\n\n  if (schema instanceof SchemaObject) {\n    v = schema.validate || compile.call(this, schema.schema, root, undefined, baseId);\n  } else if (schema !== undefined) {\n    v = inlineRef(schema, this._opts.inlineRefs)\n        ? schema\n        : compile.call(this, schema, root, undefined, baseId);\n  }\n\n  return v;\n}\n\n\n/**\n * Resolve schema, its root and baseId\n * @this Ajv\n * @param  {Object} root root object with properties schema, refVal, refs\n * @param  {String} ref  reference to resolve\n * @return {Object} object with properties schema, root, baseId\n */\nfunction resolveSchema(root, ref) {\n  /* jshint validthis: true */\n  var p = URI.parse(ref)\n    , refPath = _getFullPath(p)\n    , baseId = getFullPath(this._getId(root.schema));\n  if (Object.keys(root.schema).length === 0 || refPath !== baseId) {\n    var id = normalizeId(refPath);\n    var refVal = this._refs[id];\n    if (typeof refVal == 'string') {\n      return resolveRecursive.call(this, root, refVal, p);\n    } else if (refVal instanceof SchemaObject) {\n      if (!refVal.validate) this._compile(refVal);\n      root = refVal;\n    } else {\n      refVal = this._schemas[id];\n      if (refVal instanceof SchemaObject) {\n        if (!refVal.validate) this._compile(refVal);\n        if (id == normalizeId(ref))\n          return { schema: refVal, root: root, baseId: baseId };\n        root = refVal;\n      } else {\n        return;\n      }\n    }\n    if (!root.schema) return;\n    baseId = getFullPath(this._getId(root.schema));\n  }\n  return getJsonPointer.call(this, p, baseId, root.schema, root);\n}\n\n\n/* @this Ajv */\nfunction resolveRecursive(root, ref, parsedRef) {\n  /* jshint validthis: true */\n  var res = resolveSchema.call(this, root, ref);\n  if (res) {\n    var schema = res.schema;\n    var baseId = res.baseId;\n    root = res.root;\n    var id = this._getId(schema);\n    if (id) baseId = resolveUrl(baseId, id);\n    return getJsonPointer.call(this, parsedRef, baseId, schema, root);\n  }\n}\n\n\nvar PREVENT_SCOPE_CHANGE = util.toHash(['properties', 'patternProperties', 'enum', 'dependencies', 'definitions']);\n/* @this Ajv */\nfunction getJsonPointer(parsedRef, baseId, schema, root) {\n  /* jshint validthis: true */\n  parsedRef.fragment = parsedRef.fragment || '';\n  if (parsedRef.fragment.slice(0,1) != '/') return;\n  var parts = parsedRef.fragment.split('/');\n\n  for (var i = 1; i < parts.length; i++) {\n    var part = parts[i];\n    if (part) {\n      part = util.unescapeFragment(part);\n      schema = schema[part];\n      if (schema === undefined) break;\n      var id;\n      if (!PREVENT_SCOPE_CHANGE[part]) {\n        id = this._getId(schema);\n        if (id) baseId = resolveUrl(baseId, id);\n        if (schema.$ref) {\n          var $ref = resolveUrl(baseId, schema.$ref);\n          var res = resolveSchema.call(this, root, $ref);\n          if (res) {\n            schema = res.schema;\n            root = res.root;\n            baseId = res.baseId;\n          }\n        }\n      }\n    }\n  }\n  if (schema !== undefined && schema !== root.schema)\n    return { schema: schema, root: root, baseId: baseId };\n}\n\n\nvar SIMPLE_INLINED = util.toHash([\n  'type', 'format', 'pattern',\n  'maxLength', 'minLength',\n  'maxProperties', 'minProperties',\n  'maxItems', 'minItems',\n  'maximum', 'minimum',\n  'uniqueItems', 'multipleOf',\n  'required', 'enum'\n]);\nfunction inlineRef(schema, limit) {\n  if (limit === false) return false;\n  if (limit === undefined || limit === true) return checkNoRef(schema);\n  else if (limit) return countKeys(schema) <= limit;\n}\n\n\nfunction checkNoRef(schema) {\n  var item;\n  if (Array.isArray(schema)) {\n    for (var i=0; i<schema.length; i++) {\n      item = schema[i];\n      if (typeof item == 'object' && !checkNoRef(item)) return false;\n    }\n  } else {\n    for (var key in schema) {\n      if (key == '$ref') return false;\n      item = schema[key];\n      if (typeof item == 'object' && !checkNoRef(item)) return false;\n    }\n  }\n  return true;\n}\n\n\nfunction countKeys(schema) {\n  var count = 0, item;\n  if (Array.isArray(schema)) {\n    for (var i=0; i<schema.length; i++) {\n      item = schema[i];\n      if (typeof item == 'object') count += countKeys(item);\n      if (count == Infinity) return Infinity;\n    }\n  } else {\n    for (var key in schema) {\n      if (key == '$ref') return Infinity;\n      if (SIMPLE_INLINED[key]) {\n        count++;\n      } else {\n        item = schema[key];\n        if (typeof item == 'object') count += countKeys(item) + 1;\n        if (count == Infinity) return Infinity;\n      }\n    }\n  }\n  return count;\n}\n\n\nfunction getFullPath(id, normalize) {\n  if (normalize !== false) id = normalizeId(id);\n  var p = URI.parse(id);\n  return _getFullPath(p);\n}\n\n\nfunction _getFullPath(p) {\n  return URI.serialize(p).split('#')[0] + '#';\n}\n\n\nvar TRAILING_SLASH_HASH = /#\\/?$/;\nfunction normalizeId(id) {\n  return id ? id.replace(TRAILING_SLASH_HASH, '') : '';\n}\n\n\nfunction resolveUrl(baseId, id) {\n  id = normalizeId(id);\n  return URI.resolve(baseId, id);\n}\n\n\n/* @this Ajv */\nfunction resolveIds(schema) {\n  var schemaId = normalizeId(this._getId(schema));\n  var baseIds = {'': schemaId};\n  var fullPaths = {'': getFullPath(schemaId, false)};\n  var localRefs = {};\n  var self = this;\n\n  traverse(schema, {allKeys: true}, function(sch, jsonPtr, rootSchema, parentJsonPtr, parentKeyword, parentSchema, keyIndex) {\n    if (jsonPtr === '') return;\n    var id = self._getId(sch);\n    var baseId = baseIds[parentJsonPtr];\n    var fullPath = fullPaths[parentJsonPtr] + '/' + parentKeyword;\n    if (keyIndex !== undefined)\n      fullPath += '/' + (typeof keyIndex == 'number' ? keyIndex : util.escapeFragment(keyIndex));\n\n    if (typeof id == 'string') {\n      id = baseId = normalizeId(baseId ? URI.resolve(baseId, id) : id);\n\n      var refVal = self._refs[id];\n      if (typeof refVal == 'string') refVal = self._refs[refVal];\n      if (refVal && refVal.schema) {\n        if (!equal(sch, refVal.schema))\n          throw new Error('id \"' + id + '\" resolves to more than one schema');\n      } else if (id != normalizeId(fullPath)) {\n        if (id[0] == '#') {\n          if (localRefs[id] && !equal(sch, localRefs[id]))\n            throw new Error('id \"' + id + '\" resolves to more than one schema');\n          localRefs[id] = sch;\n        } else {\n          self._refs[id] = fullPath;\n        }\n      }\n    }\n    baseIds[jsonPtr] = baseId;\n    fullPaths[jsonPtr] = fullPath;\n  });\n\n  return localRefs;\n}\n", "\n\n\nmodule.exports = {\n  copy: copy,\n  checkDataType: checkDataType,\n  checkDataTypes: checkDataTypes,\n  coerceToTypes: coerceToTypes,\n  toHash: toHash,\n  getProperty: getProperty,\n  escapeQuotes: escapeQuotes,\n  equal: require('fast-deep-equal'),\n  ucs2length: require('./ucs2length'),\n  varOccurences: varOccurences,\n  varReplace: varReplace,\n  schemaHasRules: schemaHasRules,\n  schemaHasRulesExcept: schemaHasRulesExcept,\n  schemaUnknownRules: schemaUnknownRules,\n  toQuotedString: toQuotedString,\n  getPathExpr: getPathExpr,\n  getPath: getPath,\n  getData: getData,\n  unescapeFragment: unescapeFragment,\n  unescapeJsonPointer: unescapeJsonPointer,\n  escapeFragment: escapeFragment,\n  escapeJsonPointer: escapeJsonPointer\n};\n\n\nfunction copy(o, to) {\n  to = to || {};\n  for (var key in o) to[key] = o[key];\n  return to;\n}\n\n\nfunction checkDataType(dataType, data, strictNumbers, negate) {\n  var EQUAL = negate ? ' !== ' : ' === '\n    , AND = negate ? ' || ' : ' && '\n    , OK = negate ? '!' : ''\n    , NOT = negate ? '' : '!';\n  switch (dataType) {\n    case 'null': return data + EQUAL + 'null';\n    case 'array': return OK + 'Array.isArray(' + data + ')';\n    case 'object': return '(' + OK + data + AND +\n                          'typeof ' + data + EQUAL + '\"object\"' + AND +\n                          NOT + 'Array.isArray(' + data + '))';\n    case 'integer': return '(typeof ' + data + EQUAL + '\"number\"' + AND +\n                           NOT + '(' + data + ' % 1)' +\n                           AND + data + EQUAL + data +\n                           (strictNumbers ? (AND + OK + 'isFinite(' + data + ')') : '') + ')';\n    case 'number': return '(typeof ' + data + EQUAL + '\"' + dataType + '\"' +\n                          (strictNumbers ? (AND + OK + 'isFinite(' + data + ')') : '') + ')';\n    default: return 'typeof ' + data + EQUAL + '\"' + dataType + '\"';\n  }\n}\n\n\nfunction checkDataTypes(dataTypes, data, strictNumbers) {\n  switch (dataTypes.length) {\n    case 1: return checkDataType(dataTypes[0], data, strictNumbers, true);\n    default:\n      var code = '';\n      var types = toHash(dataTypes);\n      if (types.array && types.object) {\n        code = types.null ? '(': '(!' + data + ' || ';\n        code += 'typeof ' + data + ' !== \"object\")';\n        delete types.null;\n        delete types.array;\n        delete types.object;\n      }\n      if (types.number) delete types.integer;\n      for (var t in types)\n        code += (code ? ' && ' : '' ) + checkDataType(t, data, strictNumbers, true);\n\n      return code;\n  }\n}\n\n\nvar COERCE_TO_TYPES = toHash([ 'string', 'number', 'integer', 'boolean', 'null' ]);\nfunction coerceToTypes(optionCoerceTypes, dataTypes) {\n  if (Array.isArray(dataTypes)) {\n    var types = [];\n    for (var i=0; i<dataTypes.length; i++) {\n      var t = dataTypes[i];\n      if (COERCE_TO_TYPES[t]) types[types.length] = t;\n      else if (optionCoerceTypes === 'array' && t === 'array') types[types.length] = t;\n    }\n    if (types.length) return types;\n  } else if (COERCE_TO_TYPES[dataTypes]) {\n    return [dataTypes];\n  } else if (optionCoerceTypes === 'array' && dataTypes === 'array') {\n    return ['array'];\n  }\n}\n\n\nfunction toHash(arr) {\n  var hash = {};\n  for (var i=0; i<arr.length; i++) hash[arr[i]] = true;\n  return hash;\n}\n\n\nvar IDENTIFIER = /^[a-z$_][a-z$_0-9]*$/i;\nvar SINGLE_QUOTE = /'|\\\\/g;\nfunction getProperty(key) {\n  return typeof key == 'number'\n          ? '[' + key + ']'\n          : IDENTIFIER.test(key)\n            ? '.' + key\n            : \"['\" + escapeQuotes(key) + \"']\";\n}\n\n\nfunction escapeQuotes(str) {\n  return str.replace(SINGLE_QUOTE, '\\\\$&')\n            .replace(/\\n/g, '\\\\n')\n            .replace(/\\r/g, '\\\\r')\n            .replace(/\\f/g, '\\\\f')\n            .replace(/\\t/g, '\\\\t');\n}\n\n\nfunction varOccurences(str, dataVar) {\n  dataVar += '[^0-9]';\n  var matches = str.match(new RegExp(dataVar, 'g'));\n  return matches ? matches.length : 0;\n}\n\n\nfunction varReplace(str, dataVar, expr) {\n  dataVar += '([^0-9])';\n  expr = expr.replace(/\\$/g, '$$$$');\n  return str.replace(new RegExp(dataVar, 'g'), expr + '$1');\n}\n\n\nfunction schemaHasRules(schema, rules) {\n  if (typeof schema == 'boolean') return !schema;\n  for (var key in schema) if (rules[key]) return true;\n}\n\n\nfunction schemaHasRulesExcept(schema, rules, exceptKeyword) {\n  if (typeof schema == 'boolean') return !schema && exceptKeyword != 'not';\n  for (var key in schema) if (key != exceptKeyword && rules[key]) return true;\n}\n\n\nfunction schemaUnknownRules(schema, rules) {\n  if (typeof schema == 'boolean') return;\n  for (var key in schema) if (!rules[key]) return key;\n}\n\n\nfunction toQuotedString(str) {\n  return '\\'' + escapeQuotes(str) + '\\'';\n}\n\n\nfunction getPathExpr(currentPath, expr, jsonPointers, isNumber) {\n  var path = jsonPointers // false by default\n              ? '\\'/\\' + ' + expr + (isNumber ? '' : '.replace(/~/g, \\'~0\\').replace(/\\\\//g, \\'~1\\')')\n              : (isNumber ? '\\'[\\' + ' + expr + ' + \\']\\'' : '\\'[\\\\\\'\\' + ' + expr + ' + \\'\\\\\\']\\'');\n  return joinPaths(currentPath, path);\n}\n\n\nfunction getPath(currentPath, prop, jsonPointers) {\n  var path = jsonPointers // false by default\n              ? toQuotedString('/' + escapeJsonPointer(prop))\n              : toQuotedString(getProperty(prop));\n  return joinPaths(currentPath, path);\n}\n\n\nvar JSON_POINTER = /^\\/(?:[^~]|~0|~1)*$/;\nvar RELATIVE_JSON_POINTER = /^([0-9]+)(#|\\/(?:[^~]|~0|~1)*)?$/;\nfunction getData($data, lvl, paths) {\n  var up, jsonPointer, data, matches;\n  if ($data === '') return 'rootData';\n  if ($data[0] == '/') {\n    if (!JSON_POINTER.test($data)) throw new Error('Invalid JSON-pointer: ' + $data);\n    jsonPointer = $data;\n    data = 'rootData';\n  } else {\n    matches = $data.match(RELATIVE_JSON_POINTER);\n    if (!matches) throw new Error('Invalid JSON-pointer: ' + $data);\n    up = +matches[1];\n    jsonPointer = matches[2];\n    if (jsonPointer == '#') {\n      if (up >= lvl) throw new Error('Cannot access property/index ' + up + ' levels up, current level is ' + lvl);\n      return paths[lvl - up];\n    }\n\n    if (up > lvl) throw new Error('Cannot access data ' + up + ' levels up, current level is ' + lvl);\n    data = 'data' + ((lvl - up) || '');\n    if (!jsonPointer) return data;\n  }\n\n  var expr = data;\n  var segments = jsonPointer.split('/');\n  for (var i=0; i<segments.length; i++) {\n    var segment = segments[i];\n    if (segment) {\n      data += getProperty(unescapeJsonPointer(segment));\n      expr += ' && ' + data;\n    }\n  }\n  return expr;\n}\n\n\nfunction joinPaths (a, b) {\n  if (a == '\"\"') return b;\n  return (a + ' + ' + b).replace(/([^\\\\])' \\+ '/g, '$1');\n}\n\n\nfunction unescapeFragment(str) {\n  return unescapeJsonPointer(decodeURIComponent(str));\n}\n\n\nfunction escapeFragment(str) {\n  return encodeURIComponent(escapeJsonPointer(str));\n}\n\n\nfunction escapeJsonPointer(str) {\n  return str.replace(/~/g, '~0').replace(/\\//g, '~1');\n}\n\n\nfunction unescapeJsonPointer(str) {\n  return str.replace(/~1/g, '/').replace(/~0/g, '~');\n}\n", "\n\n// https://mathiasbynens.be/notes/javascript-encoding\n// https://github.com/bestiejs/punycode.js - punycode.ucs2.decode\nmodule.exports = function ucs2length(str) {\n  var length = 0\n    , len = str.length\n    , pos = 0\n    , value;\n  while (pos < len) {\n    length++;\n    value = str.charCodeAt(pos++);\n    if (value >= 0xD800 && value <= 0xDBFF && pos < len) {\n      // high surrogate, and there is a next character\n      value = str.charCodeAt(pos);\n      if ((value & 0xFC00) == 0xDC00) pos++; // low surrogate\n    }\n  }\n  return length;\n};\n", "\n\nvar util = require('./util');\n\nmodule.exports = SchemaObject;\n\nfunction SchemaObject(obj) {\n  util.copy(obj, this);\n}\n", "\n\nvar resolve = require('./resolve');\n\nmodule.exports = {\n  Validation: errorSubclass(ValidationError),\n  MissingRef: errorSubclass(MissingRefError)\n};\n\n\nfunction ValidationError(errors) {\n  this.message = 'validation failed';\n  this.errors = errors;\n  this.ajv = this.validation = true;\n}\n\n\nMissingRefError.message = function (baseId, ref) {\n  return 'can\\'t resolve reference ' + ref + ' from id ' + baseId;\n};\n\n\nfunction MissingRefError(baseId, ref, message) {\n  this.message = message || MissingRefError.message(baseId, ref);\n  this.missingRef = resolve.url(baseId, ref);\n  this.missingSchema = resolve.normalizeId(resolve.fullPath(this.missingRef));\n}\n\n\nfunction errorSubclass(Subclass) {\n  Subclass.prototype = Object.create(Error.prototype);\n  Subclass.prototype.constructor = Subclass;\n  return Subclass;\n}\n", "\nmodule.exports = function generate_validate(it, $keyword, $ruleType) {\n  var out = '';\n  var $async = it.schema.$async === true,\n    $refKeywords = it.util.schemaHasRulesExcept(it.schema, it.RULES.all, '$ref'),\n    $id = it.self._getId(it.schema);\n  if (it.opts.strictKeywords) {\n    var $unknownKwd = it.util.schemaUnknownRules(it.schema, it.RULES.keywords);\n    if ($unknownKwd) {\n      var $keywordsMsg = 'unknown keyword: ' + $unknownKwd;\n      if (it.opts.strictKeywords === 'log') it.logger.warn($keywordsMsg);\n      else throw new Error($keywordsMsg);\n    }\n  }\n  if (it.isTop) {\n    out += ' var validate = ';\n    if ($async) {\n      it.async = true;\n      out += 'async ';\n    }\n    out += 'function(data, dataPath, parentData, parentDataProperty, rootData) { \\'use strict\\'; ';\n    if ($id && (it.opts.sourceCode || it.opts.processCode)) {\n      out += ' ' + ('/\\*# sourceURL=' + $id + ' */') + ' ';\n    }\n  }\n  if (typeof it.schema == 'boolean' || !($refKeywords || it.schema.$ref)) {\n    var $keyword = 'false schema';\n    var $lvl = it.level;\n    var $dataLvl = it.dataLevel;\n    var $schema = it.schema[$keyword];\n    var $schemaPath = it.schemaPath + it.util.getProperty($keyword);\n    var $errSchemaPath = it.errSchemaPath + '/' + $keyword;\n    var $breakOnError = !it.opts.allErrors;\n    var $errorKeyword;\n    var $data = 'data' + ($dataLvl || '');\n    var $valid = 'valid' + $lvl;\n    if (it.schema === false) {\n      if (it.isTop) {\n        $breakOnError = true;\n      } else {\n        out += ' var ' + ($valid) + ' = false; ';\n      }\n      var $$outStack = $$outStack || [];\n      $$outStack.push(out);\n      out = ''; /* istanbul ignore else */\n      if (it.createErrors !== false) {\n        out += ' { keyword: \\'' + ($errorKeyword || 'false schema') + '\\' , dataPath: (dataPath || \\'\\') + ' + (it.errorPath) + ' , schemaPath: ' + (it.util.toQuotedString($errSchemaPath)) + ' , params: {} ';\n        if (it.opts.messages !== false) {\n          out += ' , message: \\'boolean schema is false\\' ';\n        }\n        if (it.opts.verbose) {\n          out += ' , schema: false , parentSchema: validate.schema' + (it.schemaPath) + ' , data: ' + ($data) + ' ';\n        }\n        out += ' } ';\n      } else {\n        out += ' {} ';\n      }\n      var __err = out;\n      out = $$outStack.pop();\n      if (!it.compositeRule && $breakOnError) {\n        /* istanbul ignore if */\n        if (it.async) {\n          out += ' throw new ValidationError([' + (__err) + ']); ';\n        } else {\n          out += ' validate.errors = [' + (__err) + ']; return false; ';\n        }\n      } else {\n        out += ' var err = ' + (__err) + ';  if (vErrors === null) vErrors = [err]; else vErrors.push(err); errors++; ';\n      }\n    } else {\n      if (it.isTop) {\n        if ($async) {\n          out += ' return data; ';\n        } else {\n          out += ' validate.errors = null; return true; ';\n        }\n      } else {\n        out += ' var ' + ($valid) + ' = true; ';\n      }\n    }\n    if (it.isTop) {\n      out += ' }; return validate; ';\n    }\n    return out;\n  }\n  if (it.isTop) {\n    var $top = it.isTop,\n      $lvl = it.level = 0,\n      $dataLvl = it.dataLevel = 0,\n      $data = 'data';\n    it.rootId = it.resolve.fullPath(it.self._getId(it.root.schema));\n    it.baseId = it.baseId || it.rootId;\n    delete it.isTop;\n    it.dataPathArr = [\"\"];\n    if (it.schema.default !== undefined && it.opts.useDefaults && it.opts.strictDefaults) {\n      var $defaultMsg = 'default is ignored in the schema root';\n      if (it.opts.strictDefaults === 'log') it.logger.warn($defaultMsg);\n      else throw new Error($defaultMsg);\n    }\n    out += ' var vErrors = null; ';\n    out += ' var errors = 0;     ';\n    out += ' if (rootData === undefined) rootData = data; ';\n  } else {\n    var $lvl = it.level,\n      $dataLvl = it.dataLevel,\n      $data = 'data' + ($dataLvl || '');\n    if ($id) it.baseId = it.resolve.url(it.baseId, $id);\n    if ($async && !it.async) throw new Error('async schema in sync schema');\n    out += ' var errs_' + ($lvl) + ' = errors;';\n  }\n  var $valid = 'valid' + $lvl,\n    $breakOnError = !it.opts.allErrors,\n    $closingBraces1 = '',\n    $closingBraces2 = '';\n  var $errorKeyword;\n  var $typeSchema = it.schema.type,\n    $typeIsArray = Array.isArray($typeSchema);\n  if ($typeSchema && it.opts.nullable && it.schema.nullable === true) {\n    if ($typeIsArray) {\n      if ($typeSchema.indexOf('null') == -1) $typeSchema = $typeSchema.concat('null');\n    } else if ($typeSchema != 'null') {\n      $typeSchema = [$typeSchema, 'null'];\n      $typeIsArray = true;\n    }\n  }\n  if ($typeIsArray && $typeSchema.length == 1) {\n    $typeSchema = $typeSchema[0];\n    $typeIsArray = false;\n  }\n  if (it.schema.$ref && $refKeywords) {\n    if (it.opts.extendRefs == 'fail') {\n      throw new Error('$ref: validation keywords used in schema at path \"' + it.errSchemaPath + '\" (see option extendRefs)');\n    } else if (it.opts.extendRefs !== true) {\n      $refKeywords = false;\n      it.logger.warn('$ref: keywords ignored in schema at path \"' + it.errSchemaPath + '\"');\n    }\n  }\n  if (it.schema.$comment && it.opts.$comment) {\n    out += ' ' + (it.RULES.all.$comment.code(it, '$comment'));\n  }\n  if ($typeSchema) {\n    if (it.opts.coerceTypes) {\n      var $coerceToTypes = it.util.coerceToTypes(it.opts.coerceTypes, $typeSchema);\n    }\n    var $rulesGroup = it.RULES.types[$typeSchema];\n    if ($coerceToTypes || $typeIsArray || $rulesGroup === true || ($rulesGroup && !$shouldUseGroup($rulesGroup))) {\n      var $schemaPath = it.schemaPath + '.type',\n        $errSchemaPath = it.errSchemaPath + '/type';\n      var $schemaPath = it.schemaPath + '.type',\n        $errSchemaPath = it.errSchemaPath + '/type',\n        $method = $typeIsArray ? 'checkDataTypes' : 'checkDataType';\n      out += ' if (' + (it.util[$method]($typeSchema, $data, it.opts.strictNumbers, true)) + ') { ';\n      if ($coerceToTypes) {\n        var $dataType = 'dataType' + $lvl,\n          $coerced = 'coerced' + $lvl;\n        out += ' var ' + ($dataType) + ' = typeof ' + ($data) + '; var ' + ($coerced) + ' = undefined; ';\n        if (it.opts.coerceTypes == 'array') {\n          out += ' if (' + ($dataType) + ' == \\'object\\' && Array.isArray(' + ($data) + ') && ' + ($data) + '.length == 1) { ' + ($data) + ' = ' + ($data) + '[0]; ' + ($dataType) + ' = typeof ' + ($data) + '; if (' + (it.util.checkDataType(it.schema.type, $data, it.opts.strictNumbers)) + ') ' + ($coerced) + ' = ' + ($data) + '; } ';\n        }\n        out += ' if (' + ($coerced) + ' !== undefined) ; ';\n        var arr1 = $coerceToTypes;\n        if (arr1) {\n          var $type, $i = -1,\n            l1 = arr1.length - 1;\n          while ($i < l1) {\n            $type = arr1[$i += 1];\n            if ($type == 'string') {\n              out += ' else if (' + ($dataType) + ' == \\'number\\' || ' + ($dataType) + ' == \\'boolean\\') ' + ($coerced) + ' = \\'\\' + ' + ($data) + '; else if (' + ($data) + ' === null) ' + ($coerced) + ' = \\'\\'; ';\n            } else if ($type == 'number' || $type == 'integer') {\n              out += ' else if (' + ($dataType) + ' == \\'boolean\\' || ' + ($data) + ' === null || (' + ($dataType) + ' == \\'string\\' && ' + ($data) + ' && ' + ($data) + ' == +' + ($data) + ' ';\n              if ($type == 'integer') {\n                out += ' && !(' + ($data) + ' % 1)';\n              }\n              out += ')) ' + ($coerced) + ' = +' + ($data) + '; ';\n            } else if ($type == 'boolean') {\n              out += ' else if (' + ($data) + ' === \\'false\\' || ' + ($data) + ' === 0 || ' + ($data) + ' === null) ' + ($coerced) + ' = false; else if (' + ($data) + ' === \\'true\\' || ' + ($data) + ' === 1) ' + ($coerced) + ' = true; ';\n            } else if ($type == 'null') {\n              out += ' else if (' + ($data) + ' === \\'\\' || ' + ($data) + ' === 0 || ' + ($data) + ' === false) ' + ($coerced) + ' = null; ';\n            } else if (it.opts.coerceTypes == 'array' && $type == 'array') {\n              out += ' else if (' + ($dataType) + ' == \\'string\\' || ' + ($dataType) + ' == \\'number\\' || ' + ($dataType) + ' == \\'boolean\\' || ' + ($data) + ' == null) ' + ($coerced) + ' = [' + ($data) + ']; ';\n            }\n          }\n        }\n        out += ' else {   ';\n        var $$outStack = $$outStack || [];\n        $$outStack.push(out);\n        out = ''; /* istanbul ignore else */\n        if (it.createErrors !== false) {\n          out += ' { keyword: \\'' + ($errorKeyword || 'type') + '\\' , dataPath: (dataPath || \\'\\') + ' + (it.errorPath) + ' , schemaPath: ' + (it.util.toQuotedString($errSchemaPath)) + ' , params: { type: \\'';\n          if ($typeIsArray) {\n            out += '' + ($typeSchema.join(\",\"));\n          } else {\n            out += '' + ($typeSchema);\n          }\n          out += '\\' } ';\n          if (it.opts.messages !== false) {\n            out += ' , message: \\'should be ';\n            if ($typeIsArray) {\n              out += '' + ($typeSchema.join(\",\"));\n            } else {\n              out += '' + ($typeSchema);\n            }\n            out += '\\' ';\n          }\n          if (it.opts.verbose) {\n            out += ' , schema: validate.schema' + ($schemaPath) + ' , parentSchema: validate.schema' + (it.schemaPath) + ' , data: ' + ($data) + ' ';\n          }\n          out += ' } ';\n        } else {\n          out += ' {} ';\n        }\n        var __err = out;\n        out = $$outStack.pop();\n        if (!it.compositeRule && $breakOnError) {\n          /* istanbul ignore if */\n          if (it.async) {\n            out += ' throw new ValidationError([' + (__err) + ']); ';\n          } else {\n            out += ' validate.errors = [' + (__err) + ']; return false; ';\n          }\n        } else {\n          out += ' var err = ' + (__err) + ';  if (vErrors === null) vErrors = [err]; else vErrors.push(err); errors++; ';\n        }\n        out += ' } if (' + ($coerced) + ' !== undefined) {  ';\n        var $parentData = $dataLvl ? 'data' + (($dataLvl - 1) || '') : 'parentData',\n          $parentDataProperty = $dataLvl ? it.dataPathArr[$dataLvl] : 'parentDataProperty';\n        out += ' ' + ($data) + ' = ' + ($coerced) + '; ';\n        if (!$dataLvl) {\n          out += 'if (' + ($parentData) + ' !== undefined)';\n        }\n        out += ' ' + ($parentData) + '[' + ($parentDataProperty) + '] = ' + ($coerced) + '; } ';\n      } else {\n        var $$outStack = $$outStack || [];\n        $$outStack.push(out);\n        out = ''; /* istanbul ignore else */\n        if (it.createErrors !== false) {\n          out += ' { keyword: \\'' + ($errorKeyword || 'type') + '\\' , dataPath: (dataPath || \\'\\') + ' + (it.errorPath) + ' , schemaPath: ' + (it.util.toQuotedString($errSchemaPath)) + ' , params: { type: \\'';\n          if ($typeIsArray) {\n            out += '' + ($typeSchema.join(\",\"));\n          } else {\n            out += '' + ($typeSchema);\n          }\n          out += '\\' } ';\n          if (it.opts.messages !== false) {\n            out += ' , message: \\'should be ';\n            if ($typeIsArray) {\n              out += '' + ($typeSchema.join(\",\"));\n            } else {\n              out += '' + ($typeSchema);\n            }\n            out += '\\' ';\n          }\n          if (it.opts.verbose) {\n            out += ' , schema: validate.schema' + ($schemaPath) + ' , parentSchema: validate.schema' + (it.schemaPath) + ' , data: ' + ($data) + ' ';\n          }\n          out += ' } ';\n        } else {\n          out += ' {} ';\n        }\n        var __err = out;\n        out = $$outStack.pop();\n        if (!it.compositeRule && $breakOnError) {\n          /* istanbul ignore if */\n          if (it.async) {\n            out += ' throw new ValidationError([' + (__err) + ']); ';\n          } else {\n            out += ' validate.errors = [' + (__err) + ']; return false; ';\n          }\n        } else {\n          out += ' var err = ' + (__err) + ';  if (vErrors === null) vErrors = [err]; else vErrors.push(err); errors++; ';\n        }\n      }\n      out += ' } ';\n    }\n  }\n  if (it.schema.$ref && !$refKeywords) {\n    out += ' ' + (it.RULES.all.$ref.code(it, '$ref')) + ' ';\n    if ($breakOnError) {\n      out += ' } if (errors === ';\n      if ($top) {\n        out += '0';\n      } else {\n        out += 'errs_' + ($lvl);\n      }\n      out += ') { ';\n      $closingBraces2 += '}';\n    }\n  } else {\n    var arr2 = it.RULES;\n    if (arr2) {\n      var $rulesGroup, i2 = -1,\n        l2 = arr2.length - 1;\n      while (i2 < l2) {\n        $rulesGroup = arr2[i2 += 1];\n        if ($shouldUseGroup($rulesGroup)) {\n          if ($rulesGroup.type) {\n            out += ' if (' + (it.util.checkDataType($rulesGroup.type, $data, it.opts.strictNumbers)) + ') { ';\n          }\n          if (it.opts.useDefaults) {\n            if ($rulesGroup.type == 'object' && it.schema.properties) {\n              var $schema = it.schema.properties,\n                $schemaKeys = Object.keys($schema);\n              var arr3 = $schemaKeys;\n              if (arr3) {\n                var $propertyKey, i3 = -1,\n                  l3 = arr3.length - 1;\n                while (i3 < l3) {\n                  $propertyKey = arr3[i3 += 1];\n                  var $sch = $schema[$propertyKey];\n                  if ($sch.default !== undefined) {\n                    var $passData = $data + it.util.getProperty($propertyKey);\n                    if (it.compositeRule) {\n                      if (it.opts.strictDefaults) {\n                        var $defaultMsg = 'default is ignored for: ' + $passData;\n                        if (it.opts.strictDefaults === 'log') it.logger.warn($defaultMsg);\n                        else throw new Error($defaultMsg);\n                      }\n                    } else {\n                      out += ' if (' + ($passData) + ' === undefined ';\n                      if (it.opts.useDefaults == 'empty') {\n                        out += ' || ' + ($passData) + ' === null || ' + ($passData) + ' === \\'\\' ';\n                      }\n                      out += ' ) ' + ($passData) + ' = ';\n                      if (it.opts.useDefaults == 'shared') {\n                        out += ' ' + (it.useDefault($sch.default)) + ' ';\n                      } else {\n                        out += ' ' + (JSON.stringify($sch.default)) + ' ';\n                      }\n                      out += '; ';\n                    }\n                  }\n                }\n              }\n            } else if ($rulesGroup.type == 'array' && Array.isArray(it.schema.items)) {\n              var arr4 = it.schema.items;\n              if (arr4) {\n                var $sch, $i = -1,\n                  l4 = arr4.length - 1;\n                while ($i < l4) {\n                  $sch = arr4[$i += 1];\n                  if ($sch.default !== undefined) {\n                    var $passData = $data + '[' + $i + ']';\n                    if (it.compositeRule) {\n                      if (it.opts.strictDefaults) {\n                        var $defaultMsg = 'default is ignored for: ' + $passData;\n                        if (it.opts.strictDefaults === 'log') it.logger.warn($defaultMsg);\n                        else throw new Error($defaultMsg);\n                      }\n                    } else {\n                      out += ' if (' + ($passData) + ' === undefined ';\n                      if (it.opts.useDefaults == 'empty') {\n                        out += ' || ' + ($passData) + ' === null || ' + ($passData) + ' === \\'\\' ';\n                      }\n                      out += ' ) ' + ($passData) + ' = ';\n                      if (it.opts.useDefaults == 'shared') {\n                        out += ' ' + (it.useDefault($sch.default)) + ' ';\n                      } else {\n                        out += ' ' + (JSON.stringify($sch.default)) + ' ';\n                      }\n                      out += '; ';\n                    }\n                  }\n                }\n              }\n            }\n          }\n          var arr5 = $rulesGroup.rules;\n          if (arr5) {\n            var $rule, i5 = -1,\n              l5 = arr5.length - 1;\n            while (i5 < l5) {\n              $rule = arr5[i5 += 1];\n              if ($shouldUseRule($rule)) {\n                var $code = $rule.code(it, $rule.keyword, $rulesGroup.type);\n                if ($code) {\n                  out += ' ' + ($code) + ' ';\n                  if ($breakOnError) {\n                    $closingBraces1 += '}';\n                  }\n                }\n              }\n            }\n          }\n          if ($breakOnError) {\n            out += ' ' + ($closingBraces1) + ' ';\n            $closingBraces1 = '';\n          }\n          if ($rulesGroup.type) {\n            out += ' } ';\n            if ($typeSchema && $typeSchema === $rulesGroup.type && !$coerceToTypes) {\n              out += ' else { ';\n              var $schemaPath = it.schemaPath + '.type',\n                $errSchemaPath = it.errSchemaPath + '/type';\n              var $$outStack = $$outStack || [];\n              $$outStack.push(out);\n              out = ''; /* istanbul ignore else */\n              if (it.createErrors !== false) {\n                out += ' { keyword: \\'' + ($errorKeyword || 'type') + '\\' , dataPath: (dataPath || \\'\\') + ' + (it.errorPath) + ' , schemaPath: ' + (it.util.toQuotedString($errSchemaPath)) + ' , params: { type: \\'';\n                if ($typeIsArray) {\n                  out += '' + ($typeSchema.join(\",\"));\n                } else {\n                  out += '' + ($typeSchema);\n                }\n                out += '\\' } ';\n                if (it.opts.messages !== false) {\n                  out += ' , message: \\'should be ';\n                  if ($typeIsArray) {\n                    out += '' + ($typeSchema.join(\",\"));\n                  } else {\n                    out += '' + ($typeSchema);\n                  }\n                  out += '\\' ';\n                }\n                if (it.opts.verbose) {\n                  out += ' , schema: validate.schema' + ($schemaPath) + ' , parentSchema: validate.schema' + (it.schemaPath) + ' , data: ' + ($data) + ' ';\n                }\n                out += ' } ';\n              } else {\n                out += ' {} ';\n              }\n              var __err = out;\n              out = $$outStack.pop();\n              if (!it.compositeRule && $breakOnError) {\n                /* istanbul ignore if */\n                if (it.async) {\n                  out += ' throw new ValidationError([' + (__err) + ']); ';\n                } else {\n                  out += ' validate.errors = [' + (__err) + ']; return false; ';\n                }\n              } else {\n                out += ' var err = ' + (__err) + ';  if (vErrors === null) vErrors = [err]; else vErrors.push(err); errors++; ';\n              }\n              out += ' } ';\n            }\n          }\n          if ($breakOnError) {\n            out += ' if (errors === ';\n            if ($top) {\n              out += '0';\n            } else {\n              out += 'errs_' + ($lvl);\n            }\n            out += ') { ';\n            $closingBraces2 += '}';\n          }\n        }\n      }\n    }\n  }\n  if ($breakOnError) {\n    out += ' ' + ($closingBraces2) + ' ';\n  }\n  if ($top) {\n    if ($async) {\n      out += ' if (errors === 0) return data;           ';\n      out += ' else throw new ValidationError(vErrors); ';\n    } else {\n      out += ' validate.errors = vErrors; ';\n      out += ' return errors === 0;       ';\n    }\n    out += ' }; return validate;';\n  } else {\n    out += ' var ' + ($valid) + ' = errors === errs_' + ($lvl) + ';';\n  }\n\n  function $shouldUseGroup($rulesGroup) {\n    var rules = $rulesGroup.rules;\n    for (var i = 0; i < rules.length; i++)\n      if ($shouldUseRule(rules[i])) return true;\n  }\n\n  function $shouldUseRule($rule) {\n    return it.schema[$rule.keyword] !== undefined || ($rule.implements && $ruleImplementsSomeKeyword($rule));\n  }\n\n  function $ruleImplementsSomeKeyword($rule) {\n    var impl = $rule.implements;\n    for (var i = 0; i < impl.length; i++)\n      if (it.schema[impl[i]] !== undefined) return true;\n  }\n  return out;\n}\n", "\n\n\nvar Cache = module.exports = function Cache() {\n  this._cache = {};\n};\n\n\nCache.prototype.put = function Cache_put(key, value) {\n  this._cache[key] = value;\n};\n\n\nCache.prototype.get = function Cache_get(key) {\n  return this._cache[key];\n};\n\n\nCache.prototype.del = function Cache_del(key) {\n  delete this._cache[key];\n};\n\n\nCache.prototype.clear = function Cache_clear() {\n  this._cache = {};\n};\n", "\n\nvar util = require('./util');\n\nvar DATE = /^(\\d\\d\\d\\d)-(\\d\\d)-(\\d\\d)$/;\nvar DAYS = [0,31,28,31,30,31,30,31,31,30,31,30,31];\nvar TIME = /^(\\d\\d):(\\d\\d):(\\d\\d)(\\.\\d+)?(z|[+-]\\d\\d(?::?\\d\\d)?)?$/i;\nvar HOSTNAME = /^(?=.{1,253}\\.?$)[a-z0-9](?:[a-z0-9-]{0,61}[a-z0-9])?(?:\\.[a-z0-9](?:[-0-9a-z]{0,61}[0-9a-z])?)*\\.?$/i;\nvar URI = /^(?:[a-z][a-z0-9+\\-.]*:)(?:\\/?\\/(?:(?:[a-z0-9\\-._~!$&'()*+,;=:]|%[0-9a-f]{2})*@)?(?:\\[(?:(?:(?:(?:[0-9a-f]{1,4}:){6}|::(?:[0-9a-f]{1,4}:){5}|(?:[0-9a-f]{1,4})?::(?:[0-9a-f]{1,4}:){4}|(?:(?:[0-9a-f]{1,4}:){0,1}[0-9a-f]{1,4})?::(?:[0-9a-f]{1,4}:){3}|(?:(?:[0-9a-f]{1,4}:){0,2}[0-9a-f]{1,4})?::(?:[0-9a-f]{1,4}:){2}|(?:(?:[0-9a-f]{1,4}:){0,3}[0-9a-f]{1,4})?::[0-9a-f]{1,4}:|(?:(?:[0-9a-f]{1,4}:){0,4}[0-9a-f]{1,4})?::)(?:[0-9a-f]{1,4}:[0-9a-f]{1,4}|(?:(?:25[0-5]|2[0-4]\\d|[01]?\\d\\d?)\\.){3}(?:25[0-5]|2[0-4]\\d|[01]?\\d\\d?))|(?:(?:[0-9a-f]{1,4}:){0,5}[0-9a-f]{1,4})?::[0-9a-f]{1,4}|(?:(?:[0-9a-f]{1,4}:){0,6}[0-9a-f]{1,4})?::)|[Vv][0-9a-f]+\\.[a-z0-9\\-._~!$&'()*+,;=:]+)\\]|(?:(?:25[0-5]|2[0-4]\\d|[01]?\\d\\d?)\\.){3}(?:25[0-5]|2[0-4]\\d|[01]?\\d\\d?)|(?:[a-z0-9\\-._~!$&'()*+,;=]|%[0-9a-f]{2})*)(?::\\d*)?(?:\\/(?:[a-z0-9\\-._~!$&'()*+,;=:@]|%[0-9a-f]{2})*)*|\\/(?:(?:[a-z0-9\\-._~!$&'()*+,;=:@]|%[0-9a-f]{2})+(?:\\/(?:[a-z0-9\\-._~!$&'()*+,;=:@]|%[0-9a-f]{2})*)*)?|(?:[a-z0-9\\-._~!$&'()*+,;=:@]|%[0-9a-f]{2})+(?:\\/(?:[a-z0-9\\-._~!$&'()*+,;=:@]|%[0-9a-f]{2})*)*)(?:\\?(?:[a-z0-9\\-._~!$&'()*+,;=:@/?]|%[0-9a-f]{2})*)?(?:#(?:[a-z0-9\\-._~!$&'()*+,;=:@/?]|%[0-9a-f]{2})*)?$/i;\nvar URIREF = /^(?:[a-z][a-z0-9+\\-.]*:)?(?:\\/?\\/(?:(?:[a-z0-9\\-._~!$&'()*+,;=:]|%[0-9a-f]{2})*@)?(?:\\[(?:(?:(?:(?:[0-9a-f]{1,4}:){6}|::(?:[0-9a-f]{1,4}:){5}|(?:[0-9a-f]{1,4})?::(?:[0-9a-f]{1,4}:){4}|(?:(?:[0-9a-f]{1,4}:){0,1}[0-9a-f]{1,4})?::(?:[0-9a-f]{1,4}:){3}|(?:(?:[0-9a-f]{1,4}:){0,2}[0-9a-f]{1,4})?::(?:[0-9a-f]{1,4}:){2}|(?:(?:[0-9a-f]{1,4}:){0,3}[0-9a-f]{1,4})?::[0-9a-f]{1,4}:|(?:(?:[0-9a-f]{1,4}:){0,4}[0-9a-f]{1,4})?::)(?:[0-9a-f]{1,4}:[0-9a-f]{1,4}|(?:(?:25[0-5]|2[0-4]\\d|[01]?\\d\\d?)\\.){3}(?:25[0-5]|2[0-4]\\d|[01]?\\d\\d?))|(?:(?:[0-9a-f]{1,4}:){0,5}[0-9a-f]{1,4})?::[0-9a-f]{1,4}|(?:(?:[0-9a-f]{1,4}:){0,6}[0-9a-f]{1,4})?::)|[Vv][0-9a-f]+\\.[a-z0-9\\-._~!$&'()*+,;=:]+)\\]|(?:(?:25[0-5]|2[0-4]\\d|[01]?\\d\\d?)\\.){3}(?:25[0-5]|2[0-4]\\d|[01]?\\d\\d?)|(?:[a-z0-9\\-._~!$&'\"()*+,;=]|%[0-9a-f]{2})*)(?::\\d*)?(?:\\/(?:[a-z0-9\\-._~!$&'\"()*+,;=:@]|%[0-9a-f]{2})*)*|\\/(?:(?:[a-z0-9\\-._~!$&'\"()*+,;=:@]|%[0-9a-f]{2})+(?:\\/(?:[a-z0-9\\-._~!$&'\"()*+,;=:@]|%[0-9a-f]{2})*)*)?|(?:[a-z0-9\\-._~!$&'\"()*+,;=:@]|%[0-9a-f]{2})+(?:\\/(?:[a-z0-9\\-._~!$&'\"()*+,;=:@]|%[0-9a-f]{2})*)*)?(?:\\?(?:[a-z0-9\\-._~!$&'\"()*+,;=:@/?]|%[0-9a-f]{2})*)?(?:#(?:[a-z0-9\\-._~!$&'\"()*+,;=:@/?]|%[0-9a-f]{2})*)?$/i;\n// uri-template: https://tools.ietf.org/html/rfc6570\nvar URITEMPLATE = /^(?:(?:[^\\x00-\\x20\"'<>%\\\\^`{|}]|%[0-9a-f]{2})|\\{[+#./;?&=,!@|]?(?:[a-z0-9_]|%[0-9a-f]{2})+(?::[1-9][0-9]{0,3}|\\*)?(?:,(?:[a-z0-9_]|%[0-9a-f]{2})+(?::[1-9][0-9]{0,3}|\\*)?)*\\})*$/i;\n// For the source: https://gist.github.com/dperini/729294\n// For test cases: https://mathiasbynens.be/demo/url-regex\n// @todo Delete current URL in favour of the commented out URL rule when this issue is fixed https://github.com/eslint/eslint/issues/7983.\n// var URL = /^(?:(?:https?|ftp):\\/\\/)(?:\\S+(?::\\S*)?@)?(?:(?!10(?:\\.\\d{1,3}){3})(?!127(?:\\.\\d{1,3}){3})(?!169\\.254(?:\\.\\d{1,3}){2})(?!192\\.168(?:\\.\\d{1,3}){2})(?!172\\.(?:1[6-9]|2\\d|3[0-1])(?:\\.\\d{1,3}){2})(?:[1-9]\\d?|1\\d\\d|2[01]\\d|22[0-3])(?:\\.(?:1?\\d{1,2}|2[0-4]\\d|25[0-5])){2}(?:\\.(?:[1-9]\\d?|1\\d\\d|2[0-4]\\d|25[0-4]))|(?:(?:[a-z\\u{00a1}-\\u{ffff}0-9]+-)*[a-z\\u{00a1}-\\u{ffff}0-9]+)(?:\\.(?:[a-z\\u{00a1}-\\u{ffff}0-9]+-)*[a-z\\u{00a1}-\\u{ffff}0-9]+)*(?:\\.(?:[a-z\\u{00a1}-\\u{ffff}]{2,})))(?::\\d{2,5})?(?:\\/[^\\s]*)?$/iu;\nvar URL = /^(?:(?:http[s\\u017F]?|ftp):\\/\\/)(?:(?:[\\0-\\x08\\x0E-\\x1F!-\\x9F\\xA1-\\u167F\\u1681-\\u1FFF\\u200B-\\u2027\\u202A-\\u202E\\u2030-\\u205E\\u2060-\\u2FFF\\u3001-\\uD7FF\\uE000-\\uFEFE\\uFF00-\\uFFFF]|[\\uD800-\\uDBFF][\\uDC00-\\uDFFF]|[\\uD800-\\uDBFF](?![\\uDC00-\\uDFFF])|(?:[^\\uD800-\\uDBFF]|^)[\\uDC00-\\uDFFF])+(?::(?:[\\0-\\x08\\x0E-\\x1F!-\\x9F\\xA1-\\u167F\\u1681-\\u1FFF\\u200B-\\u2027\\u202A-\\u202E\\u2030-\\u205E\\u2060-\\u2FFF\\u3001-\\uD7FF\\uE000-\\uFEFE\\uFF00-\\uFFFF]|[\\uD800-\\uDBFF][\\uDC00-\\uDFFF]|[\\uD800-\\uDBFF](?![\\uDC00-\\uDFFF])|(?:[^\\uD800-\\uDBFF]|^)[\\uDC00-\\uDFFF])*)?@)?(?:(?!10(?:\\.[0-9]{1,3}){3})(?!127(?:\\.[0-9]{1,3}){3})(?!169\\.254(?:\\.[0-9]{1,3}){2})(?!192\\.168(?:\\.[0-9]{1,3}){2})(?!172\\.(?:1[6-9]|2[0-9]|3[01])(?:\\.[0-9]{1,3}){2})(?:[1-9][0-9]?|1[0-9][0-9]|2[01][0-9]|22[0-3])(?:\\.(?:1?[0-9]{1,2}|2[0-4][0-9]|25[0-5])){2}(?:\\.(?:[1-9][0-9]?|1[0-9][0-9]|2[0-4][0-9]|25[0-4]))|(?:(?:(?:[0-9a-z\\xA1-\\uD7FF\\uE000-\\uFFFF]|[\\uD800-\\uDBFF](?![\\uDC00-\\uDFFF])|(?:[^\\uD800-\\uDBFF]|^)[\\uDC00-\\uDFFF])+-)*(?:[0-9a-z\\xA1-\\uD7FF\\uE000-\\uFFFF]|[\\uD800-\\uDBFF](?![\\uDC00-\\uDFFF])|(?:[^\\uD800-\\uDBFF]|^)[\\uDC00-\\uDFFF])+)(?:\\.(?:(?:[0-9a-z\\xA1-\\uD7FF\\uE000-\\uFFFF]|[\\uD800-\\uDBFF](?![\\uDC00-\\uDFFF])|(?:[^\\uD800-\\uDBFF]|^)[\\uDC00-\\uDFFF])+-)*(?:[0-9a-z\\xA1-\\uD7FF\\uE000-\\uFFFF]|[\\uD800-\\uDBFF](?![\\uDC00-\\uDFFF])|(?:[^\\uD800-\\uDBFF]|^)[\\uDC00-\\uDFFF])+)*(?:\\.(?:(?:[a-z\\xA1-\\uD7FF\\uE000-\\uFFFF]|[\\uD800-\\uDBFF](?![\\uDC00-\\uDFFF])|(?:[^\\uD800-\\uDBFF]|^)[\\uDC00-\\uDFFF]){2,})))(?::[0-9]{2,5})?(?:\\/(?:[\\0-\\x08\\x0E-\\x1F!-\\x9F\\xA1-\\u167F\\u1681-\\u1FFF\\u200B-\\u2027\\u202A-\\u202E\\u2030-\\u205E\\u2060-\\u2FFF\\u3001-\\uD7FF\\uE000-\\uFEFE\\uFF00-\\uFFFF]|[\\uD800-\\uDBFF][\\uDC00-\\uDFFF]|[\\uD800-\\uDBFF](?![\\uDC00-\\uDFFF])|(?:[^\\uD800-\\uDBFF]|^)[\\uDC00-\\uDFFF])*)?$/i;\nvar UUID = /^(?:urn:uuid:)?[0-9a-f]{8}-(?:[0-9a-f]{4}-){3}[0-9a-f]{12}$/i;\nvar JSON_POINTER = /^(?:\\/(?:[^~/]|~0|~1)*)*$/;\nvar JSON_POINTER_URI_FRAGMENT = /^#(?:\\/(?:[a-z0-9_\\-.!$&'()*+,;:=@]|%[0-9a-f]{2}|~0|~1)*)*$/i;\nvar RELATIVE_JSON_POINTER = /^(?:0|[1-9][0-9]*)(?:#|(?:\\/(?:[^~/]|~0|~1)*)*)$/;\n\n\nmodule.exports = formats;\n\nfunction formats(mode) {\n  mode = mode == 'full' ? 'full' : 'fast';\n  return util.copy(formats[mode]);\n}\n\n\nformats.fast = {\n  // date: http://tools.ietf.org/html/rfc3339#section-5.6\n  date: /^\\d\\d\\d\\d-[0-1]\\d-[0-3]\\d$/,\n  // date-time: http://tools.ietf.org/html/rfc3339#section-5.6\n  time: /^(?:[0-2]\\d:[0-5]\\d:[0-5]\\d|23:59:60)(?:\\.\\d+)?(?:z|[+-]\\d\\d(?::?\\d\\d)?)?$/i,\n  'date-time': /^\\d\\d\\d\\d-[0-1]\\d-[0-3]\\d[t\\s](?:[0-2]\\d:[0-5]\\d:[0-5]\\d|23:59:60)(?:\\.\\d+)?(?:z|[+-]\\d\\d(?::?\\d\\d)?)$/i,\n  // uri: https://github.com/mafintosh/is-my-json-valid/blob/master/formats.js\n  uri: /^(?:[a-z][a-z0-9+\\-.]*:)(?:\\/?\\/)?[^\\s]*$/i,\n  'uri-reference': /^(?:(?:[a-z][a-z0-9+\\-.]*:)?\\/?\\/)?(?:[^\\\\\\s#][^\\s#]*)?(?:#[^\\\\\\s]*)?$/i,\n  'uri-template': URITEMPLATE,\n  url: URL,\n  // email (sources from jsen validator):\n  // http://stackoverflow.com/questions/201323/using-a-regular-expression-to-validate-an-email-address#answer-8829363\n  // http://www.w3.org/TR/html5/forms.html#valid-e-mail-address (search for 'willful violation')\n  email: /^[a-z0-9.!#$%&'*+/=?^_`{|}~-]+@[a-z0-9](?:[a-z0-9-]{0,61}[a-z0-9])?(?:\\.[a-z0-9](?:[a-z0-9-]{0,61}[a-z0-9])?)*$/i,\n  hostname: HOSTNAME,\n  // optimized https://www.safaribooksonline.com/library/view/regular-expressions-cookbook/9780596802837/ch07s16.html\n  ipv4: /^(?:(?:25[0-5]|2[0-4]\\d|[01]?\\d\\d?)\\.){3}(?:25[0-5]|2[0-4]\\d|[01]?\\d\\d?)$/,\n  // optimized http://stackoverflow.com/questions/53497/regular-expression-that-matches-valid-ipv6-addresses\n  ipv6: /^\\s*(?:(?:(?:[0-9a-f]{1,4}:){7}(?:[0-9a-f]{1,4}|:))|(?:(?:[0-9a-f]{1,4}:){6}(?::[0-9a-f]{1,4}|(?:(?:25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]?\\d)(?:\\.(?:25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]?\\d)){3})|:))|(?:(?:[0-9a-f]{1,4}:){5}(?:(?:(?::[0-9a-f]{1,4}){1,2})|:(?:(?:25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]?\\d)(?:\\.(?:25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]?\\d)){3})|:))|(?:(?:[0-9a-f]{1,4}:){4}(?:(?:(?::[0-9a-f]{1,4}){1,3})|(?:(?::[0-9a-f]{1,4})?:(?:(?:25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]?\\d)(?:\\.(?:25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]?\\d)){3}))|:))|(?:(?:[0-9a-f]{1,4}:){3}(?:(?:(?::[0-9a-f]{1,4}){1,4})|(?:(?::[0-9a-f]{1,4}){0,2}:(?:(?:25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]?\\d)(?:\\.(?:25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]?\\d)){3}))|:))|(?:(?:[0-9a-f]{1,4}:){2}(?:(?:(?::[0-9a-f]{1,4}){1,5})|(?:(?::[0-9a-f]{1,4}){0,3}:(?:(?:25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]?\\d)(?:\\.(?:25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]?\\d)){3}))|:))|(?:(?:[0-9a-f]{1,4}:){1}(?:(?:(?::[0-9a-f]{1,4}){1,6})|(?:(?::[0-9a-f]{1,4}){0,4}:(?:(?:25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]?\\d)(?:\\.(?:25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]?\\d)){3}))|:))|(?::(?:(?:(?::[0-9a-f]{1,4}){1,7})|(?:(?::[0-9a-f]{1,4}){0,5}:(?:(?:25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]?\\d)(?:\\.(?:25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]?\\d)){3}))|:)))(?:%.+)?\\s*$/i,\n  regex: regex,\n  // uuid: http://tools.ietf.org/html/rfc4122\n  uuid: UUID,\n  // JSON-pointer: https://tools.ietf.org/html/rfc6901\n  // uri fragment: https://tools.ietf.org/html/rfc3986#appendix-A\n  'json-pointer': JSON_POINTER,\n  'json-pointer-uri-fragment': JSON_POINTER_URI_FRAGMENT,\n  // relative JSON-pointer: http://tools.ietf.org/html/draft-luff-relative-json-pointer-00\n  'relative-json-pointer': RELATIVE_JSON_POINTER\n};\n\n\nformats.full = {\n  date: date,\n  time: time,\n  'date-time': date_time,\n  uri: uri,\n  'uri-reference': URIREF,\n  'uri-template': URITEMPLATE,\n  url: URL,\n  email: /^[a-z0-9!#$%&'*+/=?^_`{|}~-]+(?:\\.[a-z0-9!#$%&'*+/=?^_`{|}~-]+)*@(?:[a-z0-9](?:[a-z0-9-]*[a-z0-9])?\\.)+[a-z0-9](?:[a-z0-9-]*[a-z0-9])?$/i,\n  hostname: HOSTNAME,\n  ipv4: /^(?:(?:25[0-5]|2[0-4]\\d|[01]?\\d\\d?)\\.){3}(?:25[0-5]|2[0-4]\\d|[01]?\\d\\d?)$/,\n  ipv6: /^\\s*(?:(?:(?:[0-9a-f]{1,4}:){7}(?:[0-9a-f]{1,4}|:))|(?:(?:[0-9a-f]{1,4}:){6}(?::[0-9a-f]{1,4}|(?:(?:25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]?\\d)(?:\\.(?:25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]?\\d)){3})|:))|(?:(?:[0-9a-f]{1,4}:){5}(?:(?:(?::[0-9a-f]{1,4}){1,2})|:(?:(?:25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]?\\d)(?:\\.(?:25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]?\\d)){3})|:))|(?:(?:[0-9a-f]{1,4}:){4}(?:(?:(?::[0-9a-f]{1,4}){1,3})|(?:(?::[0-9a-f]{1,4})?:(?:(?:25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]?\\d)(?:\\.(?:25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]?\\d)){3}))|:))|(?:(?:[0-9a-f]{1,4}:){3}(?:(?:(?::[0-9a-f]{1,4}){1,4})|(?:(?::[0-9a-f]{1,4}){0,2}:(?:(?:25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]?\\d)(?:\\.(?:25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]?\\d)){3}))|:))|(?:(?:[0-9a-f]{1,4}:){2}(?:(?:(?::[0-9a-f]{1,4}){1,5})|(?:(?::[0-9a-f]{1,4}){0,3}:(?:(?:25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]?\\d)(?:\\.(?:25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]?\\d)){3}))|:))|(?:(?:[0-9a-f]{1,4}:){1}(?:(?:(?::[0-9a-f]{1,4}){1,6})|(?:(?::[0-9a-f]{1,4}){0,4}:(?:(?:25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]?\\d)(?:\\.(?:25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]?\\d)){3}))|:))|(?::(?:(?:(?::[0-9a-f]{1,4}){1,7})|(?:(?::[0-9a-f]{1,4}){0,5}:(?:(?:25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]?\\d)(?:\\.(?:25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]?\\d)){3}))|:)))(?:%.+)?\\s*$/i,\n  regex: regex,\n  uuid: UUID,\n  'json-pointer': JSON_POINTER,\n  'json-pointer-uri-fragment': JSON_POINTER_URI_FRAGMENT,\n  'relative-json-pointer': RELATIVE_JSON_POINTER\n};\n\n\nfunction isLeapYear(year) {\n  // https://tools.ietf.org/html/rfc3339#appendix-C\n  return year % 4 === 0 && (year % 100 !== 0 || year % 400 === 0);\n}\n\n\nfunction date(str) {\n  // full-date from http://tools.ietf.org/html/rfc3339#section-5.6\n  var matches = str.match(DATE);\n  if (!matches) return false;\n\n  var year = +matches[1];\n  var month = +matches[2];\n  var day = +matches[3];\n\n  return month >= 1 && month <= 12 && day >= 1 &&\n          day <= (month == 2 && isLeapYear(year) ? 29 : DAYS[month]);\n}\n\n\nfunction time(str, full) {\n  var matches = str.match(TIME);\n  if (!matches) return false;\n\n  var hour = matches[1];\n  var minute = matches[2];\n  var second = matches[3];\n  var timeZone = matches[5];\n  return ((hour <= 23 && minute <= 59 && second <= 59) ||\n          (hour == 23 && minute == 59 && second == 60)) &&\n         (!full || timeZone);\n}\n\n\nvar DATE_TIME_SEPARATOR = /t|\\s/i;\nfunction date_time(str) {\n  // http://tools.ietf.org/html/rfc3339#section-5.6\n  var dateTime = str.split(DATE_TIME_SEPARATOR);\n  return dateTime.length == 2 && date(dateTime[0]) && time(dateTime[1], true);\n}\n\n\nvar NOT_URI_FRAGMENT = /\\/|:/;\nfunction uri(str) {\n  // http://jmrware.com/articles/2009/uri_regexp/URI_regex.html + optional protocol + required \".\"\n  return NOT_URI_FRAGMENT.test(str) && URI.test(str);\n}\n\n\nvar Z_ANCHOR = /[^\\\\]\\\\Z/;\nfunction regex(str) {\n  if (Z_ANCHOR.test(str)) return false;\n  try {\n    new RegExp(str);\n    return true;\n  } catch(e) {\n    return false;\n  }\n}\n", "\n\nvar ruleModules = require('../dotjs')\n  , toHash = require('./util').toHash;\n\nmodule.exports = function rules() {\n  var RULES = [\n    { type: 'number',\n      rules: [ { 'maximum': ['exclusiveMaximum'] },\n               { 'minimum': ['exclusiveMinimum'] }, 'multipleOf', 'format'] },\n    { type: 'string',\n      rules: [ 'maxLength', 'minLength', 'pattern', 'format' ] },\n    { type: 'array',\n      rules: [ 'maxItems', 'minItems', 'items', 'contains', 'uniqueItems' ] },\n    { type: 'object',\n      rules: [ 'maxProperties', 'minProperties', 'required', 'dependencies', 'propertyNames',\n               { 'properties': ['additionalProperties', 'patternProperties'] } ] },\n    { rules: [ '$ref', 'const', 'enum', 'not', 'anyOf', 'oneOf', 'allOf', 'if' ] }\n  ];\n\n  var ALL = [ 'type', '$comment' ];\n  var KEYWORDS = [\n    '$schema', '$id', 'id', '$data', '$async', 'title',\n    'description', 'default', 'definitions',\n    'examples', 'readOnly', 'writeOnly',\n    'contentMediaType', 'contentEncoding',\n    'additionalItems', 'then', 'else'\n  ];\n  var TYPES = [ 'number', 'integer', 'string', 'array', 'object', 'boolean', 'null' ];\n  RULES.all = toHash(ALL);\n  RULES.types = toHash(TYPES);\n\n  RULES.forEach(function (group) {\n    group.rules = group.rules.map(function (keyword) {\n      var implKeywords;\n      if (typeof keyword == 'object') {\n        var key = Object.keys(keyword)[0];\n        implKeywords = keyword[key];\n        keyword = key;\n        implKeywords.forEach(function (k) {\n          ALL.push(k);\n          RULES.all[k] = true;\n        });\n      }\n      ALL.push(keyword);\n      var rule = RULES.all[keyword] = {\n        keyword: keyword,\n        code: ruleModules[keyword],\n        implements: implKeywords\n      };\n      return rule;\n    });\n\n    RULES.all.$comment = {\n      keyword: '$comment',\n      code: ruleModules.$comment\n    };\n\n    if (group.type) RULES.types[group.type] = group;\n  });\n\n  RULES.keywords = toHash(ALL.concat(KEYWORDS));\n  RULES.custom = {};\n\n  return RULES;\n};\n", "\n\n//all requires must be explicit because browserify won't work with dynamic requires\nmodule.exports = {\n  '$ref': require('./ref'),\n  allOf: require('./allOf'),\n  anyOf: require('./anyOf'),\n  '$comment': require('./comment'),\n  const: require('./const'),\n  contains: require('./contains'),\n  dependencies: require('./dependencies'),\n  'enum': require('./enum'),\n  format: require('./format'),\n  'if': require('./if'),\n  items: require('./items'),\n  maximum: require('./_limit'),\n  minimum: require('./_limit'),\n  maxItems: require('./_limitItems'),\n  minItems: require('./_limitItems'),\n  maxLength: require('./_limitLength'),\n  minLength: require('./_limitLength'),\n  maxProperties: require('./_limitProperties'),\n  minProperties: require('./_limitProperties'),\n  multipleOf: require('./multipleOf'),\n  not: require('./not'),\n  oneOf: require('./oneOf'),\n  pattern: require('./pattern'),\n  properties: require('./properties'),\n  propertyNames: require('./propertyNames'),\n  required: require('./required'),\n  uniqueItems: require('./uniqueItems'),\n  validate: require('./validate')\n};\n", "\nmodule.exports = function generate_ref(it, $keyword, $ruleType) {\n  var out = ' ';\n  var $lvl = it.level;\n  var $dataLvl = it.dataLevel;\n  var $schema = it.schema[$keyword];\n  var $errSchemaPath = it.errSchemaPath + '/' + $keyword;\n  var $breakOnError = !it.opts.allErrors;\n  var $data = 'data' + ($dataLvl || '');\n  var $valid = 'valid' + $lvl;\n  var $async, $refCode;\n  if ($schema == '#' || $schema == '#/') {\n    if (it.isRoot) {\n      $async = it.async;\n      $refCode = 'validate';\n    } else {\n      $async = it.root.schema.$async === true;\n      $refCode = 'root.refVal[0]';\n    }\n  } else {\n    var $refVal = it.resolveRef(it.baseId, $schema, it.isRoot);\n    if ($refVal === undefined) {\n      var $message = it.MissingRefError.message(it.baseId, $schema);\n      if (it.opts.missingRefs == 'fail') {\n        it.logger.error($message);\n        var $$outStack = $$outStack || [];\n        $$outStack.push(out);\n        out = ''; /* istanbul ignore else */\n        if (it.createErrors !== false) {\n          out += ' { keyword: \\'' + ('$ref') + '\\' , dataPath: (dataPath || \\'\\') + ' + (it.errorPath) + ' , schemaPath: ' + (it.util.toQuotedString($errSchemaPath)) + ' , params: { ref: \\'' + (it.util.escapeQuotes($schema)) + '\\' } ';\n          if (it.opts.messages !== false) {\n            out += ' , message: \\'can\\\\\\'t resolve reference ' + (it.util.escapeQuotes($schema)) + '\\' ';\n          }\n          if (it.opts.verbose) {\n            out += ' , schema: ' + (it.util.toQuotedString($schema)) + ' , parentSchema: validate.schema' + (it.schemaPath) + ' , data: ' + ($data) + ' ';\n          }\n          out += ' } ';\n        } else {\n          out += ' {} ';\n        }\n        var __err = out;\n        out = $$outStack.pop();\n        if (!it.compositeRule && $breakOnError) {\n          /* istanbul ignore if */\n          if (it.async) {\n            out += ' throw new ValidationError([' + (__err) + ']); ';\n          } else {\n            out += ' validate.errors = [' + (__err) + ']; return false; ';\n          }\n        } else {\n          out += ' var err = ' + (__err) + ';  if (vErrors === null) vErrors = [err]; else vErrors.push(err); errors++; ';\n        }\n        if ($breakOnError) {\n          out += ' if (false) { ';\n        }\n      } else if (it.opts.missingRefs == 'ignore') {\n        it.logger.warn($message);\n        if ($breakOnError) {\n          out += ' if (true) { ';\n        }\n      } else {\n        throw new it.MissingRefError(it.baseId, $schema, $message);\n      }\n    } else if ($refVal.inline) {\n      var $it = it.util.copy(it);\n      $it.level++;\n      var $nextValid = 'valid' + $it.level;\n      $it.schema = $refVal.schema;\n      $it.schemaPath = '';\n      $it.errSchemaPath = $schema;\n      var $code = it.validate($it).replace(/validate\\.schema/g, $refVal.code);\n      out += ' ' + ($code) + ' ';\n      if ($breakOnError) {\n        out += ' if (' + ($nextValid) + ') { ';\n      }\n    } else {\n      $async = $refVal.$async === true || (it.async && $refVal.$async !== false);\n      $refCode = $refVal.code;\n    }\n  }\n  if ($refCode) {\n    var $$outStack = $$outStack || [];\n    $$outStack.push(out);\n    out = '';\n    if (it.opts.passContext) {\n      out += ' ' + ($refCode) + '.call(this, ';\n    } else {\n      out += ' ' + ($refCode) + '( ';\n    }\n    out += ' ' + ($data) + ', (dataPath || \\'\\')';\n    if (it.errorPath != '\"\"') {\n      out += ' + ' + (it.errorPath);\n    }\n    var $parentData = $dataLvl ? 'data' + (($dataLvl - 1) || '') : 'parentData',\n      $parentDataProperty = $dataLvl ? it.dataPathArr[$dataLvl] : 'parentDataProperty';\n    out += ' , ' + ($parentData) + ' , ' + ($parentDataProperty) + ', rootData)  ';\n    var __callValidate = out;\n    out = $$outStack.pop();\n    if ($async) {\n      if (!it.async) throw new Error('async schema referenced by sync schema');\n      if ($breakOnError) {\n        out += ' var ' + ($valid) + '; ';\n      }\n      out += ' try { await ' + (__callValidate) + '; ';\n      if ($breakOnError) {\n        out += ' ' + ($valid) + ' = true; ';\n      }\n      out += ' } catch (e) { if (!(e instanceof ValidationError)) throw e; if (vErrors === null) vErrors = e.errors; else vErrors = vErrors.concat(e.errors); errors = vErrors.length; ';\n      if ($breakOnError) {\n        out += ' ' + ($valid) + ' = false; ';\n      }\n      out += ' } ';\n      if ($breakOnError) {\n        out += ' if (' + ($valid) + ') { ';\n      }\n    } else {\n      out += ' if (!' + (__callValidate) + ') { if (vErrors === null) vErrors = ' + ($refCode) + '.errors; else vErrors = vErrors.concat(' + ($refCode) + '.errors); errors = vErrors.length; } ';\n      if ($breakOnError) {\n        out += ' else { ';\n      }\n    }\n  }\n  return out;\n}\n", "\nmodule.exports = function generate_allOf(it, $keyword, $ruleType) {\n  var out = ' ';\n  var $schema = it.schema[$keyword];\n  var $schemaPath = it.schemaPath + it.util.getProperty($keyword);\n  var $errSchemaPath = it.errSchemaPath + '/' + $keyword;\n  var $breakOnError = !it.opts.allErrors;\n  var $it = it.util.copy(it);\n  var $closingBraces = '';\n  $it.level++;\n  var $nextValid = 'valid' + $it.level;\n  var $currentBaseId = $it.baseId,\n    $allSchemasEmpty = true;\n  var arr1 = $schema;\n  if (arr1) {\n    var $sch, $i = -1,\n      l1 = arr1.length - 1;\n    while ($i < l1) {\n      $sch = arr1[$i += 1];\n      if ((it.opts.strictKeywords ? (typeof $sch == 'object' && Object.keys($sch).length > 0) || $sch === false : it.util.schemaHasRules($sch, it.RULES.all))) {\n        $allSchemasEmpty = false;\n        $it.schema = $sch;\n        $it.schemaPath = $schemaPath + '[' + $i + ']';\n        $it.errSchemaPath = $errSchemaPath + '/' + $i;\n        out += '  ' + (it.validate($it)) + ' ';\n        $it.baseId = $currentBaseId;\n        if ($breakOnError) {\n          out += ' if (' + ($nextValid) + ') { ';\n          $closingBraces += '}';\n        }\n      }\n    }\n  }\n  if ($breakOnError) {\n    if ($allSchemasEmpty) {\n      out += ' if (true) { ';\n    } else {\n      out += ' ' + ($closingBraces.slice(0, -1)) + ' ';\n    }\n  }\n  return out;\n}\n", "\nmodule.exports = function generate_anyOf(it, $keyword, $ruleType) {\n  var out = ' ';\n  var $lvl = it.level;\n  var $dataLvl = it.dataLevel;\n  var $schema = it.schema[$keyword];\n  var $schemaPath = it.schemaPath + it.util.getProperty($keyword);\n  var $errSchemaPath = it.errSchemaPath + '/' + $keyword;\n  var $breakOnError = !it.opts.allErrors;\n  var $data = 'data' + ($dataLvl || '');\n  var $valid = 'valid' + $lvl;\n  var $errs = 'errs__' + $lvl;\n  var $it = it.util.copy(it);\n  var $closingBraces = '';\n  $it.level++;\n  var $nextValid = 'valid' + $it.level;\n  var $noEmptySchema = $schema.every(function($sch) {\n    return (it.opts.strictKeywords ? (typeof $sch == 'object' && Object.keys($sch).length > 0) || $sch === false : it.util.schemaHasRules($sch, it.RULES.all));\n  });\n  if ($noEmptySchema) {\n    var $currentBaseId = $it.baseId;\n    out += ' var ' + ($errs) + ' = errors; var ' + ($valid) + ' = false;  ';\n    var $wasComposite = it.compositeRule;\n    it.compositeRule = $it.compositeRule = true;\n    var arr1 = $schema;\n    if (arr1) {\n      var $sch, $i = -1,\n        l1 = arr1.length - 1;\n      while ($i < l1) {\n        $sch = arr1[$i += 1];\n        $it.schema = $sch;\n        $it.schemaPath = $schemaPath + '[' + $i + ']';\n        $it.errSchemaPath = $errSchemaPath + '/' + $i;\n        out += '  ' + (it.validate($it)) + ' ';\n        $it.baseId = $currentBaseId;\n        out += ' ' + ($valid) + ' = ' + ($valid) + ' || ' + ($nextValid) + '; if (!' + ($valid) + ') { ';\n        $closingBraces += '}';\n      }\n    }\n    it.compositeRule = $it.compositeRule = $wasComposite;\n    out += ' ' + ($closingBraces) + ' if (!' + ($valid) + ') {   var err =   '; /* istanbul ignore else */\n    if (it.createErrors !== false) {\n      out += ' { keyword: \\'' + ('anyOf') + '\\' , dataPath: (dataPath || \\'\\') + ' + (it.errorPath) + ' , schemaPath: ' + (it.util.toQuotedString($errSchemaPath)) + ' , params: {} ';\n      if (it.opts.messages !== false) {\n        out += ' , message: \\'should match some schema in anyOf\\' ';\n      }\n      if (it.opts.verbose) {\n        out += ' , schema: validate.schema' + ($schemaPath) + ' , parentSchema: validate.schema' + (it.schemaPath) + ' , data: ' + ($data) + ' ';\n      }\n      out += ' } ';\n    } else {\n      out += ' {} ';\n    }\n    out += ';  if (vErrors === null) vErrors = [err]; else vErrors.push(err); errors++; ';\n    if (!it.compositeRule && $breakOnError) {\n      /* istanbul ignore if */\n      if (it.async) {\n        out += ' throw new ValidationError(vErrors); ';\n      } else {\n        out += ' validate.errors = vErrors; return false; ';\n      }\n    }\n    out += ' } else {  errors = ' + ($errs) + '; if (vErrors !== null) { if (' + ($errs) + ') vErrors.length = ' + ($errs) + '; else vErrors = null; } ';\n    if (it.opts.allErrors) {\n      out += ' } ';\n    }\n  } else {\n    if ($breakOnError) {\n      out += ' if (true) { ';\n    }\n  }\n  return out;\n}\n", "\nmodule.exports = function generate_comment(it, $keyword, $ruleType) {\n  var out = ' ';\n  var $schema = it.schema[$keyword];\n  var $errSchemaPath = it.errSchemaPath + '/' + $keyword;\n  var $breakOnError = !it.opts.allErrors;\n  var $comment = it.util.toQuotedString($schema);\n  if (it.opts.$comment === true) {\n    out += ' console.log(' + ($comment) + ');';\n  } else if (typeof it.opts.$comment == 'function') {\n    out += ' self._opts.$comment(' + ($comment) + ', ' + (it.util.toQuotedString($errSchemaPath)) + ', validate.root.schema);';\n  }\n  return out;\n}\n", "\nmodule.exports = function generate_const(it, $keyword, $ruleType) {\n  var out = ' ';\n  var $lvl = it.level;\n  var $dataLvl = it.dataLevel;\n  var $schema = it.schema[$keyword];\n  var $schemaPath = it.schemaPath + it.util.getProperty($keyword);\n  var $errSchemaPath = it.errSchemaPath + '/' + $keyword;\n  var $breakOnError = !it.opts.allErrors;\n  var $data = 'data' + ($dataLvl || '');\n  var $valid = 'valid' + $lvl;\n  var $isData = it.opts.$data && $schema && $schema.$data,\n    $schemaValue;\n  if ($isData) {\n    out += ' var schema' + ($lvl) + ' = ' + (it.util.getData($schema.$data, $dataLvl, it.dataPathArr)) + '; ';\n    $schemaValue = 'schema' + $lvl;\n  } else {\n    $schemaValue = $schema;\n  }\n  if (!$isData) {\n    out += ' var schema' + ($lvl) + ' = validate.schema' + ($schemaPath) + ';';\n  }\n  out += 'var ' + ($valid) + ' = equal(' + ($data) + ', schema' + ($lvl) + '); if (!' + ($valid) + ') {   ';\n  var $$outStack = $$outStack || [];\n  $$outStack.push(out);\n  out = ''; /* istanbul ignore else */\n  if (it.createErrors !== false) {\n    out += ' { keyword: \\'' + ('const') + '\\' , dataPath: (dataPath || \\'\\') + ' + (it.errorPath) + ' , schemaPath: ' + (it.util.toQuotedString($errSchemaPath)) + ' , params: { allowedValue: schema' + ($lvl) + ' } ';\n    if (it.opts.messages !== false) {\n      out += ' , message: \\'should be equal to constant\\' ';\n    }\n    if (it.opts.verbose) {\n      out += ' , schema: validate.schema' + ($schemaPath) + ' , parentSchema: validate.schema' + (it.schemaPath) + ' , data: ' + ($data) + ' ';\n    }\n    out += ' } ';\n  } else {\n    out += ' {} ';\n  }\n  var __err = out;\n  out = $$outStack.pop();\n  if (!it.compositeRule && $breakOnError) {\n    /* istanbul ignore if */\n    if (it.async) {\n      out += ' throw new ValidationError([' + (__err) + ']); ';\n    } else {\n      out += ' validate.errors = [' + (__err) + ']; return false; ';\n    }\n  } else {\n    out += ' var err = ' + (__err) + ';  if (vErrors === null) vErrors = [err]; else vErrors.push(err); errors++; ';\n  }\n  out += ' }';\n  if ($breakOnError) {\n    out += ' else { ';\n  }\n  return out;\n}\n", "\nmodule.exports = function generate_contains(it, $keyword, $ruleType) {\n  var out = ' ';\n  var $lvl = it.level;\n  var $dataLvl = it.dataLevel;\n  var $schema = it.schema[$keyword];\n  var $schemaPath = it.schemaPath + it.util.getProperty($keyword);\n  var $errSchemaPath = it.errSchemaPath + '/' + $keyword;\n  var $breakOnError = !it.opts.allErrors;\n  var $data = 'data' + ($dataLvl || '');\n  var $valid = 'valid' + $lvl;\n  var $errs = 'errs__' + $lvl;\n  var $it = it.util.copy(it);\n  var $closingBraces = '';\n  $it.level++;\n  var $nextValid = 'valid' + $it.level;\n  var $idx = 'i' + $lvl,\n    $dataNxt = $it.dataLevel = it.dataLevel + 1,\n    $nextData = 'data' + $dataNxt,\n    $currentBaseId = it.baseId,\n    $nonEmptySchema = (it.opts.strictKeywords ? (typeof $schema == 'object' && Object.keys($schema).length > 0) || $schema === false : it.util.schemaHasRules($schema, it.RULES.all));\n  out += 'var ' + ($errs) + ' = errors;var ' + ($valid) + ';';\n  if ($nonEmptySchema) {\n    var $wasComposite = it.compositeRule;\n    it.compositeRule = $it.compositeRule = true;\n    $it.schema = $schema;\n    $it.schemaPath = $schemaPath;\n    $it.errSchemaPath = $errSchemaPath;\n    out += ' var ' + ($nextValid) + ' = false; for (var ' + ($idx) + ' = 0; ' + ($idx) + ' < ' + ($data) + '.length; ' + ($idx) + '++) { ';\n    $it.errorPath = it.util.getPathExpr(it.errorPath, $idx, it.opts.jsonPointers, true);\n    var $passData = $data + '[' + $idx + ']';\n    $it.dataPathArr[$dataNxt] = $idx;\n    var $code = it.validate($it);\n    $it.baseId = $currentBaseId;\n    if (it.util.varOccurences($code, $nextData) < 2) {\n      out += ' ' + (it.util.varReplace($code, $nextData, $passData)) + ' ';\n    } else {\n      out += ' var ' + ($nextData) + ' = ' + ($passData) + '; ' + ($code) + ' ';\n    }\n    out += ' if (' + ($nextValid) + ') break; }  ';\n    it.compositeRule = $it.compositeRule = $wasComposite;\n    out += ' ' + ($closingBraces) + ' if (!' + ($nextValid) + ') {';\n  } else {\n    out += ' if (' + ($data) + '.length == 0) {';\n  }\n  var $$outStack = $$outStack || [];\n  $$outStack.push(out);\n  out = ''; /* istanbul ignore else */\n  if (it.createErrors !== false) {\n    out += ' { keyword: \\'' + ('contains') + '\\' , dataPath: (dataPath || \\'\\') + ' + (it.errorPath) + ' , schemaPath: ' + (it.util.toQuotedString($errSchemaPath)) + ' , params: {} ';\n    if (it.opts.messages !== false) {\n      out += ' , message: \\'should contain a valid item\\' ';\n    }\n    if (it.opts.verbose) {\n      out += ' , schema: validate.schema' + ($schemaPath) + ' , parentSchema: validate.schema' + (it.schemaPath) + ' , data: ' + ($data) + ' ';\n    }\n    out += ' } ';\n  } else {\n    out += ' {} ';\n  }\n  var __err = out;\n  out = $$outStack.pop();\n  if (!it.compositeRule && $breakOnError) {\n    /* istanbul ignore if */\n    if (it.async) {\n      out += ' throw new ValidationError([' + (__err) + ']); ';\n    } else {\n      out += ' validate.errors = [' + (__err) + ']; return false; ';\n    }\n  } else {\n    out += ' var err = ' + (__err) + ';  if (vErrors === null) vErrors = [err]; else vErrors.push(err); errors++; ';\n  }\n  out += ' } else { ';\n  if ($nonEmptySchema) {\n    out += '  errors = ' + ($errs) + '; if (vErrors !== null) { if (' + ($errs) + ') vErrors.length = ' + ($errs) + '; else vErrors = null; } ';\n  }\n  if (it.opts.allErrors) {\n    out += ' } ';\n  }\n  return out;\n}\n", "\nmodule.exports = function generate_dependencies(it, $keyword, $ruleType) {\n  var out = ' ';\n  var $lvl = it.level;\n  var $dataLvl = it.dataLevel;\n  var $schema = it.schema[$keyword];\n  var $schemaPath = it.schemaPath + it.util.getProperty($keyword);\n  var $errSchemaPath = it.errSchemaPath + '/' + $keyword;\n  var $breakOnError = !it.opts.allErrors;\n  var $data = 'data' + ($dataLvl || '');\n  var $errs = 'errs__' + $lvl;\n  var $it = it.util.copy(it);\n  var $closingBraces = '';\n  $it.level++;\n  var $nextValid = 'valid' + $it.level;\n  var $schemaDeps = {},\n    $propertyDeps = {},\n    $ownProperties = it.opts.ownProperties;\n  for ($property in $schema) {\n    if ($property == '__proto__') continue;\n    var $sch = $schema[$property];\n    var $deps = Array.isArray($sch) ? $propertyDeps : $schemaDeps;\n    $deps[$property] = $sch;\n  }\n  out += 'var ' + ($errs) + ' = errors;';\n  var $currentErrorPath = it.errorPath;\n  out += 'var missing' + ($lvl) + ';';\n  for (var $property in $propertyDeps) {\n    $deps = $propertyDeps[$property];\n    if ($deps.length) {\n      out += ' if ( ' + ($data) + (it.util.getProperty($property)) + ' !== undefined ';\n      if ($ownProperties) {\n        out += ' && Object.prototype.hasOwnProperty.call(' + ($data) + ', \\'' + (it.util.escapeQuotes($property)) + '\\') ';\n      }\n      if ($breakOnError) {\n        out += ' && ( ';\n        var arr1 = $deps;\n        if (arr1) {\n          var $propertyKey, $i = -1,\n            l1 = arr1.length - 1;\n          while ($i < l1) {\n            $propertyKey = arr1[$i += 1];\n            if ($i) {\n              out += ' || ';\n            }\n            var $prop = it.util.getProperty($propertyKey),\n              $useData = $data + $prop;\n            out += ' ( ( ' + ($useData) + ' === undefined ';\n            if ($ownProperties) {\n              out += ' || ! Object.prototype.hasOwnProperty.call(' + ($data) + ', \\'' + (it.util.escapeQuotes($propertyKey)) + '\\') ';\n            }\n            out += ') && (missing' + ($lvl) + ' = ' + (it.util.toQuotedString(it.opts.jsonPointers ? $propertyKey : $prop)) + ') ) ';\n          }\n        }\n        out += ')) {  ';\n        var $propertyPath = 'missing' + $lvl,\n          $missingProperty = '\\' + ' + $propertyPath + ' + \\'';\n        if (it.opts._errorDataPathProperty) {\n          it.errorPath = it.opts.jsonPointers ? it.util.getPathExpr($currentErrorPath, $propertyPath, true) : $currentErrorPath + ' + ' + $propertyPath;\n        }\n        var $$outStack = $$outStack || [];\n        $$outStack.push(out);\n        out = ''; /* istanbul ignore else */\n        if (it.createErrors !== false) {\n          out += ' { keyword: \\'' + ('dependencies') + '\\' , dataPath: (dataPath || \\'\\') + ' + (it.errorPath) + ' , schemaPath: ' + (it.util.toQuotedString($errSchemaPath)) + ' , params: { property: \\'' + (it.util.escapeQuotes($property)) + '\\', missingProperty: \\'' + ($missingProperty) + '\\', depsCount: ' + ($deps.length) + ', deps: \\'' + (it.util.escapeQuotes($deps.length == 1 ? $deps[0] : $deps.join(\", \"))) + '\\' } ';\n          if (it.opts.messages !== false) {\n            out += ' , message: \\'should have ';\n            if ($deps.length == 1) {\n              out += 'property ' + (it.util.escapeQuotes($deps[0]));\n            } else {\n              out += 'properties ' + (it.util.escapeQuotes($deps.join(\", \")));\n            }\n            out += ' when property ' + (it.util.escapeQuotes($property)) + ' is present\\' ';\n          }\n          if (it.opts.verbose) {\n            out += ' , schema: validate.schema' + ($schemaPath) + ' , parentSchema: validate.schema' + (it.schemaPath) + ' , data: ' + ($data) + ' ';\n          }\n          out += ' } ';\n        } else {\n          out += ' {} ';\n        }\n        var __err = out;\n        out = $$outStack.pop();\n        if (!it.compositeRule && $breakOnError) {\n          /* istanbul ignore if */\n          if (it.async) {\n            out += ' throw new ValidationError([' + (__err) + ']); ';\n          } else {\n            out += ' validate.errors = [' + (__err) + ']; return false; ';\n          }\n        } else {\n          out += ' var err = ' + (__err) + ';  if (vErrors === null) vErrors = [err]; else vErrors.push(err); errors++; ';\n        }\n      } else {\n        out += ' ) { ';\n        var arr2 = $deps;\n        if (arr2) {\n          var $propertyKey, i2 = -1,\n            l2 = arr2.length - 1;\n          while (i2 < l2) {\n            $propertyKey = arr2[i2 += 1];\n            var $prop = it.util.getProperty($propertyKey),\n              $missingProperty = it.util.escapeQuotes($propertyKey),\n              $useData = $data + $prop;\n            if (it.opts._errorDataPathProperty) {\n              it.errorPath = it.util.getPath($currentErrorPath, $propertyKey, it.opts.jsonPointers);\n            }\n            out += ' if ( ' + ($useData) + ' === undefined ';\n            if ($ownProperties) {\n              out += ' || ! Object.prototype.hasOwnProperty.call(' + ($data) + ', \\'' + (it.util.escapeQuotes($propertyKey)) + '\\') ';\n            }\n            out += ') {  var err =   '; /* istanbul ignore else */\n            if (it.createErrors !== false) {\n              out += ' { keyword: \\'' + ('dependencies') + '\\' , dataPath: (dataPath || \\'\\') + ' + (it.errorPath) + ' , schemaPath: ' + (it.util.toQuotedString($errSchemaPath)) + ' , params: { property: \\'' + (it.util.escapeQuotes($property)) + '\\', missingProperty: \\'' + ($missingProperty) + '\\', depsCount: ' + ($deps.length) + ', deps: \\'' + (it.util.escapeQuotes($deps.length == 1 ? $deps[0] : $deps.join(\", \"))) + '\\' } ';\n              if (it.opts.messages !== false) {\n                out += ' , message: \\'should have ';\n                if ($deps.length == 1) {\n                  out += 'property ' + (it.util.escapeQuotes($deps[0]));\n                } else {\n                  out += 'properties ' + (it.util.escapeQuotes($deps.join(\", \")));\n                }\n                out += ' when property ' + (it.util.escapeQuotes($property)) + ' is present\\' ';\n              }\n              if (it.opts.verbose) {\n                out += ' , schema: validate.schema' + ($schemaPath) + ' , parentSchema: validate.schema' + (it.schemaPath) + ' , data: ' + ($data) + ' ';\n              }\n              out += ' } ';\n            } else {\n              out += ' {} ';\n            }\n            out += ';  if (vErrors === null) vErrors = [err]; else vErrors.push(err); errors++; } ';\n          }\n        }\n      }\n      out += ' }   ';\n      if ($breakOnError) {\n        $closingBraces += '}';\n        out += ' else { ';\n      }\n    }\n  }\n  it.errorPath = $currentErrorPath;\n  var $currentBaseId = $it.baseId;\n  for (var $property in $schemaDeps) {\n    var $sch = $schemaDeps[$property];\n    if ((it.opts.strictKeywords ? (typeof $sch == 'object' && Object.keys($sch).length > 0) || $sch === false : it.util.schemaHasRules($sch, it.RULES.all))) {\n      out += ' ' + ($nextValid) + ' = true; if ( ' + ($data) + (it.util.getProperty($property)) + ' !== undefined ';\n      if ($ownProperties) {\n        out += ' && Object.prototype.hasOwnProperty.call(' + ($data) + ', \\'' + (it.util.escapeQuotes($property)) + '\\') ';\n      }\n      out += ') { ';\n      $it.schema = $sch;\n      $it.schemaPath = $schemaPath + it.util.getProperty($property);\n      $it.errSchemaPath = $errSchemaPath + '/' + it.util.escapeFragment($property);\n      out += '  ' + (it.validate($it)) + ' ';\n      $it.baseId = $currentBaseId;\n      out += ' }  ';\n      if ($breakOnError) {\n        out += ' if (' + ($nextValid) + ') { ';\n        $closingBraces += '}';\n      }\n    }\n  }\n  if ($breakOnError) {\n    out += '   ' + ($closingBraces) + ' if (' + ($errs) + ' == errors) {';\n  }\n  return out;\n}\n", "\nmodule.exports = function generate_enum(it, $keyword, $ruleType) {\n  var out = ' ';\n  var $lvl = it.level;\n  var $dataLvl = it.dataLevel;\n  var $schema = it.schema[$keyword];\n  var $schemaPath = it.schemaPath + it.util.getProperty($keyword);\n  var $errSchemaPath = it.errSchemaPath + '/' + $keyword;\n  var $breakOnError = !it.opts.allErrors;\n  var $data = 'data' + ($dataLvl || '');\n  var $valid = 'valid' + $lvl;\n  var $isData = it.opts.$data && $schema && $schema.$data,\n    $schemaValue;\n  if ($isData) {\n    out += ' var schema' + ($lvl) + ' = ' + (it.util.getData($schema.$data, $dataLvl, it.dataPathArr)) + '; ';\n    $schemaValue = 'schema' + $lvl;\n  } else {\n    $schemaValue = $schema;\n  }\n  var $i = 'i' + $lvl,\n    $vSchema = 'schema' + $lvl;\n  if (!$isData) {\n    out += ' var ' + ($vSchema) + ' = validate.schema' + ($schemaPath) + ';';\n  }\n  out += 'var ' + ($valid) + ';';\n  if ($isData) {\n    out += ' if (schema' + ($lvl) + ' === undefined) ' + ($valid) + ' = true; else if (!Array.isArray(schema' + ($lvl) + ')) ' + ($valid) + ' = false; else {';\n  }\n  out += '' + ($valid) + ' = false;for (var ' + ($i) + '=0; ' + ($i) + '<' + ($vSchema) + '.length; ' + ($i) + '++) if (equal(' + ($data) + ', ' + ($vSchema) + '[' + ($i) + '])) { ' + ($valid) + ' = true; break; }';\n  if ($isData) {\n    out += '  }  ';\n  }\n  out += ' if (!' + ($valid) + ') {   ';\n  var $$outStack = $$outStack || [];\n  $$outStack.push(out);\n  out = ''; /* istanbul ignore else */\n  if (it.createErrors !== false) {\n    out += ' { keyword: \\'' + ('enum') + '\\' , dataPath: (dataPath || \\'\\') + ' + (it.errorPath) + ' , schemaPath: ' + (it.util.toQuotedString($errSchemaPath)) + ' , params: { allowedValues: schema' + ($lvl) + ' } ';\n    if (it.opts.messages !== false) {\n      out += ' , message: \\'should be equal to one of the allowed values\\' ';\n    }\n    if (it.opts.verbose) {\n      out += ' , schema: validate.schema' + ($schemaPath) + ' , parentSchema: validate.schema' + (it.schemaPath) + ' , data: ' + ($data) + ' ';\n    }\n    out += ' } ';\n  } else {\n    out += ' {} ';\n  }\n  var __err = out;\n  out = $$outStack.pop();\n  if (!it.compositeRule && $breakOnError) {\n    /* istanbul ignore if */\n    if (it.async) {\n      out += ' throw new ValidationError([' + (__err) + ']); ';\n    } else {\n      out += ' validate.errors = [' + (__err) + ']; return false; ';\n    }\n  } else {\n    out += ' var err = ' + (__err) + ';  if (vErrors === null) vErrors = [err]; else vErrors.push(err); errors++; ';\n  }\n  out += ' }';\n  if ($breakOnError) {\n    out += ' else { ';\n  }\n  return out;\n}\n", "\nmodule.exports = function generate_format(it, $keyword, $ruleType) {\n  var out = ' ';\n  var $lvl = it.level;\n  var $dataLvl = it.dataLevel;\n  var $schema = it.schema[$keyword];\n  var $schemaPath = it.schemaPath + it.util.getProperty($keyword);\n  var $errSchemaPath = it.errSchemaPath + '/' + $keyword;\n  var $breakOnError = !it.opts.allErrors;\n  var $data = 'data' + ($dataLvl || '');\n  if (it.opts.format === false) {\n    if ($breakOnError) {\n      out += ' if (true) { ';\n    }\n    return out;\n  }\n  var $isData = it.opts.$data && $schema && $schema.$data,\n    $schemaValue;\n  if ($isData) {\n    out += ' var schema' + ($lvl) + ' = ' + (it.util.getData($schema.$data, $dataLvl, it.dataPathArr)) + '; ';\n    $schemaValue = 'schema' + $lvl;\n  } else {\n    $schemaValue = $schema;\n  }\n  var $unknownFormats = it.opts.unknownFormats,\n    $allowUnknown = Array.isArray($unknownFormats);\n  if ($isData) {\n    var $format = 'format' + $lvl,\n      $isObject = 'isObject' + $lvl,\n      $formatType = 'formatType' + $lvl;\n    out += ' var ' + ($format) + ' = formats[' + ($schemaValue) + ']; var ' + ($isObject) + ' = typeof ' + ($format) + ' == \\'object\\' && !(' + ($format) + ' instanceof RegExp) && ' + ($format) + '.validate; var ' + ($formatType) + ' = ' + ($isObject) + ' && ' + ($format) + '.type || \\'string\\'; if (' + ($isObject) + ') { ';\n    if (it.async) {\n      out += ' var async' + ($lvl) + ' = ' + ($format) + '.async; ';\n    }\n    out += ' ' + ($format) + ' = ' + ($format) + '.validate; } if (  ';\n    if ($isData) {\n      out += ' (' + ($schemaValue) + ' !== undefined && typeof ' + ($schemaValue) + ' != \\'string\\') || ';\n    }\n    out += ' (';\n    if ($unknownFormats != 'ignore') {\n      out += ' (' + ($schemaValue) + ' && !' + ($format) + ' ';\n      if ($allowUnknown) {\n        out += ' && self._opts.unknownFormats.indexOf(' + ($schemaValue) + ') == -1 ';\n      }\n      out += ') || ';\n    }\n    out += ' (' + ($format) + ' && ' + ($formatType) + ' == \\'' + ($ruleType) + '\\' && !(typeof ' + ($format) + ' == \\'function\\' ? ';\n    if (it.async) {\n      out += ' (async' + ($lvl) + ' ? await ' + ($format) + '(' + ($data) + ') : ' + ($format) + '(' + ($data) + ')) ';\n    } else {\n      out += ' ' + ($format) + '(' + ($data) + ') ';\n    }\n    out += ' : ' + ($format) + '.test(' + ($data) + '))))) {';\n  } else {\n    var $format = it.formats[$schema];\n    if (!$format) {\n      if ($unknownFormats == 'ignore') {\n        it.logger.warn('unknown format \"' + $schema + '\" ignored in schema at path \"' + it.errSchemaPath + '\"');\n        if ($breakOnError) {\n          out += ' if (true) { ';\n        }\n        return out;\n      } else if ($allowUnknown && $unknownFormats.indexOf($schema) >= 0) {\n        if ($breakOnError) {\n          out += ' if (true) { ';\n        }\n        return out;\n      } else {\n        throw new Error('unknown format \"' + $schema + '\" is used in schema at path \"' + it.errSchemaPath + '\"');\n      }\n    }\n    var $isObject = typeof $format == 'object' && !($format instanceof RegExp) && $format.validate;\n    var $formatType = $isObject && $format.type || 'string';\n    if ($isObject) {\n      var $async = $format.async === true;\n      $format = $format.validate;\n    }\n    if ($formatType != $ruleType) {\n      if ($breakOnError) {\n        out += ' if (true) { ';\n      }\n      return out;\n    }\n    if ($async) {\n      if (!it.async) throw new Error('async format in sync schema');\n      var $formatRef = 'formats' + it.util.getProperty($schema) + '.validate';\n      out += ' if (!(await ' + ($formatRef) + '(' + ($data) + '))) { ';\n    } else {\n      out += ' if (! ';\n      var $formatRef = 'formats' + it.util.getProperty($schema);\n      if ($isObject) $formatRef += '.validate';\n      if (typeof $format == 'function') {\n        out += ' ' + ($formatRef) + '(' + ($data) + ') ';\n      } else {\n        out += ' ' + ($formatRef) + '.test(' + ($data) + ') ';\n      }\n      out += ') { ';\n    }\n  }\n  var $$outStack = $$outStack || [];\n  $$outStack.push(out);\n  out = ''; /* istanbul ignore else */\n  if (it.createErrors !== false) {\n    out += ' { keyword: \\'' + ('format') + '\\' , dataPath: (dataPath || \\'\\') + ' + (it.errorPath) + ' , schemaPath: ' + (it.util.toQuotedString($errSchemaPath)) + ' , params: { format:  ';\n    if ($isData) {\n      out += '' + ($schemaValue);\n    } else {\n      out += '' + (it.util.toQuotedString($schema));\n    }\n    out += '  } ';\n    if (it.opts.messages !== false) {\n      out += ' , message: \\'should match format \"';\n      if ($isData) {\n        out += '\\' + ' + ($schemaValue) + ' + \\'';\n      } else {\n        out += '' + (it.util.escapeQuotes($schema));\n      }\n      out += '\"\\' ';\n    }\n    if (it.opts.verbose) {\n      out += ' , schema:  ';\n      if ($isData) {\n        out += 'validate.schema' + ($schemaPath);\n      } else {\n        out += '' + (it.util.toQuotedString($schema));\n      }\n      out += '         , parentSchema: validate.schema' + (it.schemaPath) + ' , data: ' + ($data) + ' ';\n    }\n    out += ' } ';\n  } else {\n    out += ' {} ';\n  }\n  var __err = out;\n  out = $$outStack.pop();\n  if (!it.compositeRule && $breakOnError) {\n    /* istanbul ignore if */\n    if (it.async) {\n      out += ' throw new ValidationError([' + (__err) + ']); ';\n    } else {\n      out += ' validate.errors = [' + (__err) + ']; return false; ';\n    }\n  } else {\n    out += ' var err = ' + (__err) + ';  if (vErrors === null) vErrors = [err]; else vErrors.push(err); errors++; ';\n  }\n  out += ' } ';\n  if ($breakOnError) {\n    out += ' else { ';\n  }\n  return out;\n}\n", "\nmodule.exports = function generate_if(it, $keyword, $ruleType) {\n  var out = ' ';\n  var $lvl = it.level;\n  var $dataLvl = it.dataLevel;\n  var $schema = it.schema[$keyword];\n  var $schemaPath = it.schemaPath + it.util.getProperty($keyword);\n  var $errSchemaPath = it.errSchemaPath + '/' + $keyword;\n  var $breakOnError = !it.opts.allErrors;\n  var $data = 'data' + ($dataLvl || '');\n  var $valid = 'valid' + $lvl;\n  var $errs = 'errs__' + $lvl;\n  var $it = it.util.copy(it);\n  $it.level++;\n  var $nextValid = 'valid' + $it.level;\n  var $thenSch = it.schema['then'],\n    $elseSch = it.schema['else'],\n    $thenPresent = $thenSch !== undefined && (it.opts.strictKeywords ? (typeof $thenSch == 'object' && Object.keys($thenSch).length > 0) || $thenSch === false : it.util.schemaHasRules($thenSch, it.RULES.all)),\n    $elsePresent = $elseSch !== undefined && (it.opts.strictKeywords ? (typeof $elseSch == 'object' && Object.keys($elseSch).length > 0) || $elseSch === false : it.util.schemaHasRules($elseSch, it.RULES.all)),\n    $currentBaseId = $it.baseId;\n  if ($thenPresent || $elsePresent) {\n    var $ifClause;\n    $it.createErrors = false;\n    $it.schema = $schema;\n    $it.schemaPath = $schemaPath;\n    $it.errSchemaPath = $errSchemaPath;\n    out += ' var ' + ($errs) + ' = errors; var ' + ($valid) + ' = true;  ';\n    var $wasComposite = it.compositeRule;\n    it.compositeRule = $it.compositeRule = true;\n    out += '  ' + (it.validate($it)) + ' ';\n    $it.baseId = $currentBaseId;\n    $it.createErrors = true;\n    out += '  errors = ' + ($errs) + '; if (vErrors !== null) { if (' + ($errs) + ') vErrors.length = ' + ($errs) + '; else vErrors = null; }  ';\n    it.compositeRule = $it.compositeRule = $wasComposite;\n    if ($thenPresent) {\n      out += ' if (' + ($nextValid) + ') {  ';\n      $it.schema = it.schema['then'];\n      $it.schemaPath = it.schemaPath + '.then';\n      $it.errSchemaPath = it.errSchemaPath + '/then';\n      out += '  ' + (it.validate($it)) + ' ';\n      $it.baseId = $currentBaseId;\n      out += ' ' + ($valid) + ' = ' + ($nextValid) + '; ';\n      if ($thenPresent && $elsePresent) {\n        $ifClause = 'ifClause' + $lvl;\n        out += ' var ' + ($ifClause) + ' = \\'then\\'; ';\n      } else {\n        $ifClause = '\\'then\\'';\n      }\n      out += ' } ';\n      if ($elsePresent) {\n        out += ' else { ';\n      }\n    } else {\n      out += ' if (!' + ($nextValid) + ') { ';\n    }\n    if ($elsePresent) {\n      $it.schema = it.schema['else'];\n      $it.schemaPath = it.schemaPath + '.else';\n      $it.errSchemaPath = it.errSchemaPath + '/else';\n      out += '  ' + (it.validate($it)) + ' ';\n      $it.baseId = $currentBaseId;\n      out += ' ' + ($valid) + ' = ' + ($nextValid) + '; ';\n      if ($thenPresent && $elsePresent) {\n        $ifClause = 'ifClause' + $lvl;\n        out += ' var ' + ($ifClause) + ' = \\'else\\'; ';\n      } else {\n        $ifClause = '\\'else\\'';\n      }\n      out += ' } ';\n    }\n    out += ' if (!' + ($valid) + ') {   var err =   '; /* istanbul ignore else */\n    if (it.createErrors !== false) {\n      out += ' { keyword: \\'' + ('if') + '\\' , dataPath: (dataPath || \\'\\') + ' + (it.errorPath) + ' , schemaPath: ' + (it.util.toQuotedString($errSchemaPath)) + ' , params: { failingKeyword: ' + ($ifClause) + ' } ';\n      if (it.opts.messages !== false) {\n        out += ' , message: \\'should match \"\\' + ' + ($ifClause) + ' + \\'\" schema\\' ';\n      }\n      if (it.opts.verbose) {\n        out += ' , schema: validate.schema' + ($schemaPath) + ' , parentSchema: validate.schema' + (it.schemaPath) + ' , data: ' + ($data) + ' ';\n      }\n      out += ' } ';\n    } else {\n      out += ' {} ';\n    }\n    out += ';  if (vErrors === null) vErrors = [err]; else vErrors.push(err); errors++; ';\n    if (!it.compositeRule && $breakOnError) {\n      /* istanbul ignore if */\n      if (it.async) {\n        out += ' throw new ValidationError(vErrors); ';\n      } else {\n        out += ' validate.errors = vErrors; return false; ';\n      }\n    }\n    out += ' }   ';\n    if ($breakOnError) {\n      out += ' else { ';\n    }\n  } else {\n    if ($breakOnError) {\n      out += ' if (true) { ';\n    }\n  }\n  return out;\n}\n", "\nmodule.exports = function generate_items(it, $keyword, $ruleType) {\n  var out = ' ';\n  var $lvl = it.level;\n  var $dataLvl = it.dataLevel;\n  var $schema = it.schema[$keyword];\n  var $schemaPath = it.schemaPath + it.util.getProperty($keyword);\n  var $errSchemaPath = it.errSchemaPath + '/' + $keyword;\n  var $breakOnError = !it.opts.allErrors;\n  var $data = 'data' + ($dataLvl || '');\n  var $valid = 'valid' + $lvl;\n  var $errs = 'errs__' + $lvl;\n  var $it = it.util.copy(it);\n  var $closingBraces = '';\n  $it.level++;\n  var $nextValid = 'valid' + $it.level;\n  var $idx = 'i' + $lvl,\n    $dataNxt = $it.dataLevel = it.dataLevel + 1,\n    $nextData = 'data' + $dataNxt,\n    $currentBaseId = it.baseId;\n  out += 'var ' + ($errs) + ' = errors;var ' + ($valid) + ';';\n  if (Array.isArray($schema)) {\n    var $additionalItems = it.schema.additionalItems;\n    if ($additionalItems === false) {\n      out += ' ' + ($valid) + ' = ' + ($data) + '.length <= ' + ($schema.length) + '; ';\n      var $currErrSchemaPath = $errSchemaPath;\n      $errSchemaPath = it.errSchemaPath + '/additionalItems';\n      out += '  if (!' + ($valid) + ') {   ';\n      var $$outStack = $$outStack || [];\n      $$outStack.push(out);\n      out = ''; /* istanbul ignore else */\n      if (it.createErrors !== false) {\n        out += ' { keyword: \\'' + ('additionalItems') + '\\' , dataPath: (dataPath || \\'\\') + ' + (it.errorPath) + ' , schemaPath: ' + (it.util.toQuotedString($errSchemaPath)) + ' , params: { limit: ' + ($schema.length) + ' } ';\n        if (it.opts.messages !== false) {\n          out += ' , message: \\'should NOT have more than ' + ($schema.length) + ' items\\' ';\n        }\n        if (it.opts.verbose) {\n          out += ' , schema: false , parentSchema: validate.schema' + (it.schemaPath) + ' , data: ' + ($data) + ' ';\n        }\n        out += ' } ';\n      } else {\n        out += ' {} ';\n      }\n      var __err = out;\n      out = $$outStack.pop();\n      if (!it.compositeRule && $breakOnError) {\n        /* istanbul ignore if */\n        if (it.async) {\n          out += ' throw new ValidationError([' + (__err) + ']); ';\n        } else {\n          out += ' validate.errors = [' + (__err) + ']; return false; ';\n        }\n      } else {\n        out += ' var err = ' + (__err) + ';  if (vErrors === null) vErrors = [err]; else vErrors.push(err); errors++; ';\n      }\n      out += ' } ';\n      $errSchemaPath = $currErrSchemaPath;\n      if ($breakOnError) {\n        $closingBraces += '}';\n        out += ' else { ';\n      }\n    }\n    var arr1 = $schema;\n    if (arr1) {\n      var $sch, $i = -1,\n        l1 = arr1.length - 1;\n      while ($i < l1) {\n        $sch = arr1[$i += 1];\n        if ((it.opts.strictKeywords ? (typeof $sch == 'object' && Object.keys($sch).length > 0) || $sch === false : it.util.schemaHasRules($sch, it.RULES.all))) {\n          out += ' ' + ($nextValid) + ' = true; if (' + ($data) + '.length > ' + ($i) + ') { ';\n          var $passData = $data + '[' + $i + ']';\n          $it.schema = $sch;\n          $it.schemaPath = $schemaPath + '[' + $i + ']';\n          $it.errSchemaPath = $errSchemaPath + '/' + $i;\n          $it.errorPath = it.util.getPathExpr(it.errorPath, $i, it.opts.jsonPointers, true);\n          $it.dataPathArr[$dataNxt] = $i;\n          var $code = it.validate($it);\n          $it.baseId = $currentBaseId;\n          if (it.util.varOccurences($code, $nextData) < 2) {\n            out += ' ' + (it.util.varReplace($code, $nextData, $passData)) + ' ';\n          } else {\n            out += ' var ' + ($nextData) + ' = ' + ($passData) + '; ' + ($code) + ' ';\n          }\n          out += ' }  ';\n          if ($breakOnError) {\n            out += ' if (' + ($nextValid) + ') { ';\n            $closingBraces += '}';\n          }\n        }\n      }\n    }\n    if (typeof $additionalItems == 'object' && (it.opts.strictKeywords ? (typeof $additionalItems == 'object' && Object.keys($additionalItems).length > 0) || $additionalItems === false : it.util.schemaHasRules($additionalItems, it.RULES.all))) {\n      $it.schema = $additionalItems;\n      $it.schemaPath = it.schemaPath + '.additionalItems';\n      $it.errSchemaPath = it.errSchemaPath + '/additionalItems';\n      out += ' ' + ($nextValid) + ' = true; if (' + ($data) + '.length > ' + ($schema.length) + ') {  for (var ' + ($idx) + ' = ' + ($schema.length) + '; ' + ($idx) + ' < ' + ($data) + '.length; ' + ($idx) + '++) { ';\n      $it.errorPath = it.util.getPathExpr(it.errorPath, $idx, it.opts.jsonPointers, true);\n      var $passData = $data + '[' + $idx + ']';\n      $it.dataPathArr[$dataNxt] = $idx;\n      var $code = it.validate($it);\n      $it.baseId = $currentBaseId;\n      if (it.util.varOccurences($code, $nextData) < 2) {\n        out += ' ' + (it.util.varReplace($code, $nextData, $passData)) + ' ';\n      } else {\n        out += ' var ' + ($nextData) + ' = ' + ($passData) + '; ' + ($code) + ' ';\n      }\n      if ($breakOnError) {\n        out += ' if (!' + ($nextValid) + ') break; ';\n      }\n      out += ' } }  ';\n      if ($breakOnError) {\n        out += ' if (' + ($nextValid) + ') { ';\n        $closingBraces += '}';\n      }\n    }\n  } else if ((it.opts.strictKeywords ? (typeof $schema == 'object' && Object.keys($schema).length > 0) || $schema === false : it.util.schemaHasRules($schema, it.RULES.all))) {\n    $it.schema = $schema;\n    $it.schemaPath = $schemaPath;\n    $it.errSchemaPath = $errSchemaPath;\n    out += '  for (var ' + ($idx) + ' = ' + (0) + '; ' + ($idx) + ' < ' + ($data) + '.length; ' + ($idx) + '++) { ';\n    $it.errorPath = it.util.getPathExpr(it.errorPath, $idx, it.opts.jsonPointers, true);\n    var $passData = $data + '[' + $idx + ']';\n    $it.dataPathArr[$dataNxt] = $idx;\n    var $code = it.validate($it);\n    $it.baseId = $currentBaseId;\n    if (it.util.varOccurences($code, $nextData) < 2) {\n      out += ' ' + (it.util.varReplace($code, $nextData, $passData)) + ' ';\n    } else {\n      out += ' var ' + ($nextData) + ' = ' + ($passData) + '; ' + ($code) + ' ';\n    }\n    if ($breakOnError) {\n      out += ' if (!' + ($nextValid) + ') break; ';\n    }\n    out += ' }';\n  }\n  if ($breakOnError) {\n    out += ' ' + ($closingBraces) + ' if (' + ($errs) + ' == errors) {';\n  }\n  return out;\n}\n", "\nmodule.exports = function generate__limit(it, $keyword, $ruleType) {\n  var out = ' ';\n  var $lvl = it.level;\n  var $dataLvl = it.dataLevel;\n  var $schema = it.schema[$keyword];\n  var $schemaPath = it.schemaPath + it.util.getProperty($keyword);\n  var $errSchemaPath = it.errSchemaPath + '/' + $keyword;\n  var $breakOnError = !it.opts.allErrors;\n  var $errorKeyword;\n  var $data = 'data' + ($dataLvl || '');\n  var $isData = it.opts.$data && $schema && $schema.$data,\n    $schemaValue;\n  if ($isData) {\n    out += ' var schema' + ($lvl) + ' = ' + (it.util.getData($schema.$data, $dataLvl, it.dataPathArr)) + '; ';\n    $schemaValue = 'schema' + $lvl;\n  } else {\n    $schemaValue = $schema;\n  }\n  var $isMax = $keyword == 'maximum',\n    $exclusiveKeyword = $isMax ? 'exclusiveMaximum' : 'exclusiveMinimum',\n    $schemaExcl = it.schema[$exclusiveKeyword],\n    $isDataExcl = it.opts.$data && $schemaExcl && $schemaExcl.$data,\n    $op = $isMax ? '<' : '>',\n    $notOp = $isMax ? '>' : '<',\n    $errorKeyword = undefined;\n  if (!($isData || typeof $schema == 'number' || $schema === undefined)) {\n    throw new Error($keyword + ' must be number');\n  }\n  if (!($isDataExcl || $schemaExcl === undefined || typeof $schemaExcl == 'number' || typeof $schemaExcl == 'boolean')) {\n    throw new Error($exclusiveKeyword + ' must be number or boolean');\n  }\n  if ($isDataExcl) {\n    var $schemaValueExcl = it.util.getData($schemaExcl.$data, $dataLvl, it.dataPathArr),\n      $exclusive = 'exclusive' + $lvl,\n      $exclType = 'exclType' + $lvl,\n      $exclIsNumber = 'exclIsNumber' + $lvl,\n      $opExpr = 'op' + $lvl,\n      $opStr = '\\' + ' + $opExpr + ' + \\'';\n    out += ' var schemaExcl' + ($lvl) + ' = ' + ($schemaValueExcl) + '; ';\n    $schemaValueExcl = 'schemaExcl' + $lvl;\n    out += ' var ' + ($exclusive) + '; var ' + ($exclType) + ' = typeof ' + ($schemaValueExcl) + '; if (' + ($exclType) + ' != \\'boolean\\' && ' + ($exclType) + ' != \\'undefined\\' && ' + ($exclType) + ' != \\'number\\') { ';\n    var $errorKeyword = $exclusiveKeyword;\n    var $$outStack = $$outStack || [];\n    $$outStack.push(out);\n    out = ''; /* istanbul ignore else */\n    if (it.createErrors !== false) {\n      out += ' { keyword: \\'' + ($errorKeyword || '_exclusiveLimit') + '\\' , dataPath: (dataPath || \\'\\') + ' + (it.errorPath) + ' , schemaPath: ' + (it.util.toQuotedString($errSchemaPath)) + ' , params: {} ';\n      if (it.opts.messages !== false) {\n        out += ' , message: \\'' + ($exclusiveKeyword) + ' should be boolean\\' ';\n      }\n      if (it.opts.verbose) {\n        out += ' , schema: validate.schema' + ($schemaPath) + ' , parentSchema: validate.schema' + (it.schemaPath) + ' , data: ' + ($data) + ' ';\n      }\n      out += ' } ';\n    } else {\n      out += ' {} ';\n    }\n    var __err = out;\n    out = $$outStack.pop();\n    if (!it.compositeRule && $breakOnError) {\n      /* istanbul ignore if */\n      if (it.async) {\n        out += ' throw new ValidationError([' + (__err) + ']); ';\n      } else {\n        out += ' validate.errors = [' + (__err) + ']; return false; ';\n      }\n    } else {\n      out += ' var err = ' + (__err) + ';  if (vErrors === null) vErrors = [err]; else vErrors.push(err); errors++; ';\n    }\n    out += ' } else if ( ';\n    if ($isData) {\n      out += ' (' + ($schemaValue) + ' !== undefined && typeof ' + ($schemaValue) + ' != \\'number\\') || ';\n    }\n    out += ' ' + ($exclType) + ' == \\'number\\' ? ( (' + ($exclusive) + ' = ' + ($schemaValue) + ' === undefined || ' + ($schemaValueExcl) + ' ' + ($op) + '= ' + ($schemaValue) + ') ? ' + ($data) + ' ' + ($notOp) + '= ' + ($schemaValueExcl) + ' : ' + ($data) + ' ' + ($notOp) + ' ' + ($schemaValue) + ' ) : ( (' + ($exclusive) + ' = ' + ($schemaValueExcl) + ' === true) ? ' + ($data) + ' ' + ($notOp) + '= ' + ($schemaValue) + ' : ' + ($data) + ' ' + ($notOp) + ' ' + ($schemaValue) + ' ) || ' + ($data) + ' !== ' + ($data) + ') { var op' + ($lvl) + ' = ' + ($exclusive) + ' ? \\'' + ($op) + '\\' : \\'' + ($op) + '=\\'; ';\n    if ($schema === undefined) {\n      $errorKeyword = $exclusiveKeyword;\n      $errSchemaPath = it.errSchemaPath + '/' + $exclusiveKeyword;\n      $schemaValue = $schemaValueExcl;\n      $isData = $isDataExcl;\n    }\n  } else {\n    var $exclIsNumber = typeof $schemaExcl == 'number',\n      $opStr = $op;\n    if ($exclIsNumber && $isData) {\n      var $opExpr = '\\'' + $opStr + '\\'';\n      out += ' if ( ';\n      if ($isData) {\n        out += ' (' + ($schemaValue) + ' !== undefined && typeof ' + ($schemaValue) + ' != \\'number\\') || ';\n      }\n      out += ' ( ' + ($schemaValue) + ' === undefined || ' + ($schemaExcl) + ' ' + ($op) + '= ' + ($schemaValue) + ' ? ' + ($data) + ' ' + ($notOp) + '= ' + ($schemaExcl) + ' : ' + ($data) + ' ' + ($notOp) + ' ' + ($schemaValue) + ' ) || ' + ($data) + ' !== ' + ($data) + ') { ';\n    } else {\n      if ($exclIsNumber && $schema === undefined) {\n        $exclusive = true;\n        $errorKeyword = $exclusiveKeyword;\n        $errSchemaPath = it.errSchemaPath + '/' + $exclusiveKeyword;\n        $schemaValue = $schemaExcl;\n        $notOp += '=';\n      } else {\n        if ($exclIsNumber) $schemaValue = Math[$isMax ? 'min' : 'max']($schemaExcl, $schema);\n        if ($schemaExcl === ($exclIsNumber ? $schemaValue : true)) {\n          $exclusive = true;\n          $errorKeyword = $exclusiveKeyword;\n          $errSchemaPath = it.errSchemaPath + '/' + $exclusiveKeyword;\n          $notOp += '=';\n        } else {\n          $exclusive = false;\n          $opStr += '=';\n        }\n      }\n      var $opExpr = '\\'' + $opStr + '\\'';\n      out += ' if ( ';\n      if ($isData) {\n        out += ' (' + ($schemaValue) + ' !== undefined && typeof ' + ($schemaValue) + ' != \\'number\\') || ';\n      }\n      out += ' ' + ($data) + ' ' + ($notOp) + ' ' + ($schemaValue) + ' || ' + ($data) + ' !== ' + ($data) + ') { ';\n    }\n  }\n  $errorKeyword = $errorKeyword || $keyword;\n  var $$outStack = $$outStack || [];\n  $$outStack.push(out);\n  out = ''; /* istanbul ignore else */\n  if (it.createErrors !== false) {\n    out += ' { keyword: \\'' + ($errorKeyword || '_limit') + '\\' , dataPath: (dataPath || \\'\\') + ' + (it.errorPath) + ' , schemaPath: ' + (it.util.toQuotedString($errSchemaPath)) + ' , params: { comparison: ' + ($opExpr) + ', limit: ' + ($schemaValue) + ', exclusive: ' + ($exclusive) + ' } ';\n    if (it.opts.messages !== false) {\n      out += ' , message: \\'should be ' + ($opStr) + ' ';\n      if ($isData) {\n        out += '\\' + ' + ($schemaValue);\n      } else {\n        out += '' + ($schemaValue) + '\\'';\n      }\n    }\n    if (it.opts.verbose) {\n      out += ' , schema:  ';\n      if ($isData) {\n        out += 'validate.schema' + ($schemaPath);\n      } else {\n        out += '' + ($schema);\n      }\n      out += '         , parentSchema: validate.schema' + (it.schemaPath) + ' , data: ' + ($data) + ' ';\n    }\n    out += ' } ';\n  } else {\n    out += ' {} ';\n  }\n  var __err = out;\n  out = $$outStack.pop();\n  if (!it.compositeRule && $breakOnError) {\n    /* istanbul ignore if */\n    if (it.async) {\n      out += ' throw new ValidationError([' + (__err) + ']); ';\n    } else {\n      out += ' validate.errors = [' + (__err) + ']; return false; ';\n    }\n  } else {\n    out += ' var err = ' + (__err) + ';  if (vErrors === null) vErrors = [err]; else vErrors.push(err); errors++; ';\n  }\n  out += ' } ';\n  if ($breakOnError) {\n    out += ' else { ';\n  }\n  return out;\n}\n", "\nmodule.exports = function generate__limitItems(it, $keyword, $ruleType) {\n  var out = ' ';\n  var $lvl = it.level;\n  var $dataLvl = it.dataLevel;\n  var $schema = it.schema[$keyword];\n  var $schemaPath = it.schemaPath + it.util.getProperty($keyword);\n  var $errSchemaPath = it.errSchemaPath + '/' + $keyword;\n  var $breakOnError = !it.opts.allErrors;\n  var $errorKeyword;\n  var $data = 'data' + ($dataLvl || '');\n  var $isData = it.opts.$data && $schema && $schema.$data,\n    $schemaValue;\n  if ($isData) {\n    out += ' var schema' + ($lvl) + ' = ' + (it.util.getData($schema.$data, $dataLvl, it.dataPathArr)) + '; ';\n    $schemaValue = 'schema' + $lvl;\n  } else {\n    $schemaValue = $schema;\n  }\n  if (!($isData || typeof $schema == 'number')) {\n    throw new Error($keyword + ' must be number');\n  }\n  var $op = $keyword == 'maxItems' ? '>' : '<';\n  out += 'if ( ';\n  if ($isData) {\n    out += ' (' + ($schemaValue) + ' !== undefined && typeof ' + ($schemaValue) + ' != \\'number\\') || ';\n  }\n  out += ' ' + ($data) + '.length ' + ($op) + ' ' + ($schemaValue) + ') { ';\n  var $errorKeyword = $keyword;\n  var $$outStack = $$outStack || [];\n  $$outStack.push(out);\n  out = ''; /* istanbul ignore else */\n  if (it.createErrors !== false) {\n    out += ' { keyword: \\'' + ($errorKeyword || '_limitItems') + '\\' , dataPath: (dataPath || \\'\\') + ' + (it.errorPath) + ' , schemaPath: ' + (it.util.toQuotedString($errSchemaPath)) + ' , params: { limit: ' + ($schemaValue) + ' } ';\n    if (it.opts.messages !== false) {\n      out += ' , message: \\'should NOT have ';\n      if ($keyword == 'maxItems') {\n        out += 'more';\n      } else {\n        out += 'fewer';\n      }\n      out += ' than ';\n      if ($isData) {\n        out += '\\' + ' + ($schemaValue) + ' + \\'';\n      } else {\n        out += '' + ($schema);\n      }\n      out += ' items\\' ';\n    }\n    if (it.opts.verbose) {\n      out += ' , schema:  ';\n      if ($isData) {\n        out += 'validate.schema' + ($schemaPath);\n      } else {\n        out += '' + ($schema);\n      }\n      out += '         , parentSchema: validate.schema' + (it.schemaPath) + ' , data: ' + ($data) + ' ';\n    }\n    out += ' } ';\n  } else {\n    out += ' {} ';\n  }\n  var __err = out;\n  out = $$outStack.pop();\n  if (!it.compositeRule && $breakOnError) {\n    /* istanbul ignore if */\n    if (it.async) {\n      out += ' throw new ValidationError([' + (__err) + ']); ';\n    } else {\n      out += ' validate.errors = [' + (__err) + ']; return false; ';\n    }\n  } else {\n    out += ' var err = ' + (__err) + ';  if (vErrors === null) vErrors = [err]; else vErrors.push(err); errors++; ';\n  }\n  out += '} ';\n  if ($breakOnError) {\n    out += ' else { ';\n  }\n  return out;\n}\n", "\nmodule.exports = function generate__limitLength(it, $keyword, $ruleType) {\n  var out = ' ';\n  var $lvl = it.level;\n  var $dataLvl = it.dataLevel;\n  var $schema = it.schema[$keyword];\n  var $schemaPath = it.schemaPath + it.util.getProperty($keyword);\n  var $errSchemaPath = it.errSchemaPath + '/' + $keyword;\n  var $breakOnError = !it.opts.allErrors;\n  var $errorKeyword;\n  var $data = 'data' + ($dataLvl || '');\n  var $isData = it.opts.$data && $schema && $schema.$data,\n    $schemaValue;\n  if ($isData) {\n    out += ' var schema' + ($lvl) + ' = ' + (it.util.getData($schema.$data, $dataLvl, it.dataPathArr)) + '; ';\n    $schemaValue = 'schema' + $lvl;\n  } else {\n    $schemaValue = $schema;\n  }\n  if (!($isData || typeof $schema == 'number')) {\n    throw new Error($keyword + ' must be number');\n  }\n  var $op = $keyword == 'maxLength' ? '>' : '<';\n  out += 'if ( ';\n  if ($isData) {\n    out += ' (' + ($schemaValue) + ' !== undefined && typeof ' + ($schemaValue) + ' != \\'number\\') || ';\n  }\n  if (it.opts.unicode === false) {\n    out += ' ' + ($data) + '.length ';\n  } else {\n    out += ' ucs2length(' + ($data) + ') ';\n  }\n  out += ' ' + ($op) + ' ' + ($schemaValue) + ') { ';\n  var $errorKeyword = $keyword;\n  var $$outStack = $$outStack || [];\n  $$outStack.push(out);\n  out = ''; /* istanbul ignore else */\n  if (it.createErrors !== false) {\n    out += ' { keyword: \\'' + ($errorKeyword || '_limitLength') + '\\' , dataPath: (dataPath || \\'\\') + ' + (it.errorPath) + ' , schemaPath: ' + (it.util.toQuotedString($errSchemaPath)) + ' , params: { limit: ' + ($schemaValue) + ' } ';\n    if (it.opts.messages !== false) {\n      out += ' , message: \\'should NOT be ';\n      if ($keyword == 'maxLength') {\n        out += 'longer';\n      } else {\n        out += 'shorter';\n      }\n      out += ' than ';\n      if ($isData) {\n        out += '\\' + ' + ($schemaValue) + ' + \\'';\n      } else {\n        out += '' + ($schema);\n      }\n      out += ' characters\\' ';\n    }\n    if (it.opts.verbose) {\n      out += ' , schema:  ';\n      if ($isData) {\n        out += 'validate.schema' + ($schemaPath);\n      } else {\n        out += '' + ($schema);\n      }\n      out += '         , parentSchema: validate.schema' + (it.schemaPath) + ' , data: ' + ($data) + ' ';\n    }\n    out += ' } ';\n  } else {\n    out += ' {} ';\n  }\n  var __err = out;\n  out = $$outStack.pop();\n  if (!it.compositeRule && $breakOnError) {\n    /* istanbul ignore if */\n    if (it.async) {\n      out += ' throw new ValidationError([' + (__err) + ']); ';\n    } else {\n      out += ' validate.errors = [' + (__err) + ']; return false; ';\n    }\n  } else {\n    out += ' var err = ' + (__err) + ';  if (vErrors === null) vErrors = [err]; else vErrors.push(err); errors++; ';\n  }\n  out += '} ';\n  if ($breakOnError) {\n    out += ' else { ';\n  }\n  return out;\n}\n", "\nmodule.exports = function generate__limitProperties(it, $keyword, $ruleType) {\n  var out = ' ';\n  var $lvl = it.level;\n  var $dataLvl = it.dataLevel;\n  var $schema = it.schema[$keyword];\n  var $schemaPath = it.schemaPath + it.util.getProperty($keyword);\n  var $errSchemaPath = it.errSchemaPath + '/' + $keyword;\n  var $breakOnError = !it.opts.allErrors;\n  var $errorKeyword;\n  var $data = 'data' + ($dataLvl || '');\n  var $isData = it.opts.$data && $schema && $schema.$data,\n    $schemaValue;\n  if ($isData) {\n    out += ' var schema' + ($lvl) + ' = ' + (it.util.getData($schema.$data, $dataLvl, it.dataPathArr)) + '; ';\n    $schemaValue = 'schema' + $lvl;\n  } else {\n    $schemaValue = $schema;\n  }\n  if (!($isData || typeof $schema == 'number')) {\n    throw new Error($keyword + ' must be number');\n  }\n  var $op = $keyword == 'maxProperties' ? '>' : '<';\n  out += 'if ( ';\n  if ($isData) {\n    out += ' (' + ($schemaValue) + ' !== undefined && typeof ' + ($schemaValue) + ' != \\'number\\') || ';\n  }\n  out += ' Object.keys(' + ($data) + ').length ' + ($op) + ' ' + ($schemaValue) + ') { ';\n  var $errorKeyword = $keyword;\n  var $$outStack = $$outStack || [];\n  $$outStack.push(out);\n  out = ''; /* istanbul ignore else */\n  if (it.createErrors !== false) {\n    out += ' { keyword: \\'' + ($errorKeyword || '_limitProperties') + '\\' , dataPath: (dataPath || \\'\\') + ' + (it.errorPath) + ' , schemaPath: ' + (it.util.toQuotedString($errSchemaPath)) + ' , params: { limit: ' + ($schemaValue) + ' } ';\n    if (it.opts.messages !== false) {\n      out += ' , message: \\'should NOT have ';\n      if ($keyword == 'maxProperties') {\n        out += 'more';\n      } else {\n        out += 'fewer';\n      }\n      out += ' than ';\n      if ($isData) {\n        out += '\\' + ' + ($schemaValue) + ' + \\'';\n      } else {\n        out += '' + ($schema);\n      }\n      out += ' properties\\' ';\n    }\n    if (it.opts.verbose) {\n      out += ' , schema:  ';\n      if ($isData) {\n        out += 'validate.schema' + ($schemaPath);\n      } else {\n        out += '' + ($schema);\n      }\n      out += '         , parentSchema: validate.schema' + (it.schemaPath) + ' , data: ' + ($data) + ' ';\n    }\n    out += ' } ';\n  } else {\n    out += ' {} ';\n  }\n  var __err = out;\n  out = $$outStack.pop();\n  if (!it.compositeRule && $breakOnError) {\n    /* istanbul ignore if */\n    if (it.async) {\n      out += ' throw new ValidationError([' + (__err) + ']); ';\n    } else {\n      out += ' validate.errors = [' + (__err) + ']; return false; ';\n    }\n  } else {\n    out += ' var err = ' + (__err) + ';  if (vErrors === null) vErrors = [err]; else vErrors.push(err); errors++; ';\n  }\n  out += '} ';\n  if ($breakOnError) {\n    out += ' else { ';\n  }\n  return out;\n}\n", "\nmodule.exports = function generate_multipleOf(it, $keyword, $ruleType) {\n  var out = ' ';\n  var $lvl = it.level;\n  var $dataLvl = it.dataLevel;\n  var $schema = it.schema[$keyword];\n  var $schemaPath = it.schemaPath + it.util.getProperty($keyword);\n  var $errSchemaPath = it.errSchemaPath + '/' + $keyword;\n  var $breakOnError = !it.opts.allErrors;\n  var $data = 'data' + ($dataLvl || '');\n  var $isData = it.opts.$data && $schema && $schema.$data,\n    $schemaValue;\n  if ($isData) {\n    out += ' var schema' + ($lvl) + ' = ' + (it.util.getData($schema.$data, $dataLvl, it.dataPathArr)) + '; ';\n    $schemaValue = 'schema' + $lvl;\n  } else {\n    $schemaValue = $schema;\n  }\n  if (!($isData || typeof $schema == 'number')) {\n    throw new Error($keyword + ' must be number');\n  }\n  out += 'var division' + ($lvl) + ';if (';\n  if ($isData) {\n    out += ' ' + ($schemaValue) + ' !== undefined && ( typeof ' + ($schemaValue) + ' != \\'number\\' || ';\n  }\n  out += ' (division' + ($lvl) + ' = ' + ($data) + ' / ' + ($schemaValue) + ', ';\n  if (it.opts.multipleOfPrecision) {\n    out += ' Math.abs(Math.round(division' + ($lvl) + ') - division' + ($lvl) + ') > 1e-' + (it.opts.multipleOfPrecision) + ' ';\n  } else {\n    out += ' division' + ($lvl) + ' !== parseInt(division' + ($lvl) + ') ';\n  }\n  out += ' ) ';\n  if ($isData) {\n    out += '  )  ';\n  }\n  out += ' ) {   ';\n  var $$outStack = $$outStack || [];\n  $$outStack.push(out);\n  out = ''; /* istanbul ignore else */\n  if (it.createErrors !== false) {\n    out += ' { keyword: \\'' + ('multipleOf') + '\\' , dataPath: (dataPath || \\'\\') + ' + (it.errorPath) + ' , schemaPath: ' + (it.util.toQuotedString($errSchemaPath)) + ' , params: { multipleOf: ' + ($schemaValue) + ' } ';\n    if (it.opts.messages !== false) {\n      out += ' , message: \\'should be multiple of ';\n      if ($isData) {\n        out += '\\' + ' + ($schemaValue);\n      } else {\n        out += '' + ($schemaValue) + '\\'';\n      }\n    }\n    if (it.opts.verbose) {\n      out += ' , schema:  ';\n      if ($isData) {\n        out += 'validate.schema' + ($schemaPath);\n      } else {\n        out += '' + ($schema);\n      }\n      out += '         , parentSchema: validate.schema' + (it.schemaPath) + ' , data: ' + ($data) + ' ';\n    }\n    out += ' } ';\n  } else {\n    out += ' {} ';\n  }\n  var __err = out;\n  out = $$outStack.pop();\n  if (!it.compositeRule && $breakOnError) {\n    /* istanbul ignore if */\n    if (it.async) {\n      out += ' throw new ValidationError([' + (__err) + ']); ';\n    } else {\n      out += ' validate.errors = [' + (__err) + ']; return false; ';\n    }\n  } else {\n    out += ' var err = ' + (__err) + ';  if (vErrors === null) vErrors = [err]; else vErrors.push(err); errors++; ';\n  }\n  out += '} ';\n  if ($breakOnError) {\n    out += ' else { ';\n  }\n  return out;\n}\n", "\nmodule.exports = function generate_not(it, $keyword, $ruleType) {\n  var out = ' ';\n  var $lvl = it.level;\n  var $dataLvl = it.dataLevel;\n  var $schema = it.schema[$keyword];\n  var $schemaPath = it.schemaPath + it.util.getProperty($keyword);\n  var $errSchemaPath = it.errSchemaPath + '/' + $keyword;\n  var $breakOnError = !it.opts.allErrors;\n  var $data = 'data' + ($dataLvl || '');\n  var $errs = 'errs__' + $lvl;\n  var $it = it.util.copy(it);\n  $it.level++;\n  var $nextValid = 'valid' + $it.level;\n  if ((it.opts.strictKeywords ? (typeof $schema == 'object' && Object.keys($schema).length > 0) || $schema === false : it.util.schemaHasRules($schema, it.RULES.all))) {\n    $it.schema = $schema;\n    $it.schemaPath = $schemaPath;\n    $it.errSchemaPath = $errSchemaPath;\n    out += ' var ' + ($errs) + ' = errors;  ';\n    var $wasComposite = it.compositeRule;\n    it.compositeRule = $it.compositeRule = true;\n    $it.createErrors = false;\n    var $allErrorsOption;\n    if ($it.opts.allErrors) {\n      $allErrorsOption = $it.opts.allErrors;\n      $it.opts.allErrors = false;\n    }\n    out += ' ' + (it.validate($it)) + ' ';\n    $it.createErrors = true;\n    if ($allErrorsOption) $it.opts.allErrors = $allErrorsOption;\n    it.compositeRule = $it.compositeRule = $wasComposite;\n    out += ' if (' + ($nextValid) + ') {   ';\n    var $$outStack = $$outStack || [];\n    $$outStack.push(out);\n    out = ''; /* istanbul ignore else */\n    if (it.createErrors !== false) {\n      out += ' { keyword: \\'' + ('not') + '\\' , dataPath: (dataPath || \\'\\') + ' + (it.errorPath) + ' , schemaPath: ' + (it.util.toQuotedString($errSchemaPath)) + ' , params: {} ';\n      if (it.opts.messages !== false) {\n        out += ' , message: \\'should NOT be valid\\' ';\n      }\n      if (it.opts.verbose) {\n        out += ' , schema: validate.schema' + ($schemaPath) + ' , parentSchema: validate.schema' + (it.schemaPath) + ' , data: ' + ($data) + ' ';\n      }\n      out += ' } ';\n    } else {\n      out += ' {} ';\n    }\n    var __err = out;\n    out = $$outStack.pop();\n    if (!it.compositeRule && $breakOnError) {\n      /* istanbul ignore if */\n      if (it.async) {\n        out += ' throw new ValidationError([' + (__err) + ']); ';\n      } else {\n        out += ' validate.errors = [' + (__err) + ']; return false; ';\n      }\n    } else {\n      out += ' var err = ' + (__err) + ';  if (vErrors === null) vErrors = [err]; else vErrors.push(err); errors++; ';\n    }\n    out += ' } else {  errors = ' + ($errs) + '; if (vErrors !== null) { if (' + ($errs) + ') vErrors.length = ' + ($errs) + '; else vErrors = null; } ';\n    if (it.opts.allErrors) {\n      out += ' } ';\n    }\n  } else {\n    out += '  var err =   '; /* istanbul ignore else */\n    if (it.createErrors !== false) {\n      out += ' { keyword: \\'' + ('not') + '\\' , dataPath: (dataPath || \\'\\') + ' + (it.errorPath) + ' , schemaPath: ' + (it.util.toQuotedString($errSchemaPath)) + ' , params: {} ';\n      if (it.opts.messages !== false) {\n        out += ' , message: \\'should NOT be valid\\' ';\n      }\n      if (it.opts.verbose) {\n        out += ' , schema: validate.schema' + ($schemaPath) + ' , parentSchema: validate.schema' + (it.schemaPath) + ' , data: ' + ($data) + ' ';\n      }\n      out += ' } ';\n    } else {\n      out += ' {} ';\n    }\n    out += ';  if (vErrors === null) vErrors = [err]; else vErrors.push(err); errors++; ';\n    if ($breakOnError) {\n      out += ' if (false) { ';\n    }\n  }\n  return out;\n}\n", "\nmodule.exports = function generate_oneOf(it, $keyword, $ruleType) {\n  var out = ' ';\n  var $lvl = it.level;\n  var $dataLvl = it.dataLevel;\n  var $schema = it.schema[$keyword];\n  var $schemaPath = it.schemaPath + it.util.getProperty($keyword);\n  var $errSchemaPath = it.errSchemaPath + '/' + $keyword;\n  var $breakOnError = !it.opts.allErrors;\n  var $data = 'data' + ($dataLvl || '');\n  var $valid = 'valid' + $lvl;\n  var $errs = 'errs__' + $lvl;\n  var $it = it.util.copy(it);\n  var $closingBraces = '';\n  $it.level++;\n  var $nextValid = 'valid' + $it.level;\n  var $currentBaseId = $it.baseId,\n    $prevValid = 'prevValid' + $lvl,\n    $passingSchemas = 'passingSchemas' + $lvl;\n  out += 'var ' + ($errs) + ' = errors , ' + ($prevValid) + ' = false , ' + ($valid) + ' = false , ' + ($passingSchemas) + ' = null; ';\n  var $wasComposite = it.compositeRule;\n  it.compositeRule = $it.compositeRule = true;\n  var arr1 = $schema;\n  if (arr1) {\n    var $sch, $i = -1,\n      l1 = arr1.length - 1;\n    while ($i < l1) {\n      $sch = arr1[$i += 1];\n      if ((it.opts.strictKeywords ? (typeof $sch == 'object' && Object.keys($sch).length > 0) || $sch === false : it.util.schemaHasRules($sch, it.RULES.all))) {\n        $it.schema = $sch;\n        $it.schemaPath = $schemaPath + '[' + $i + ']';\n        $it.errSchemaPath = $errSchemaPath + '/' + $i;\n        out += '  ' + (it.validate($it)) + ' ';\n        $it.baseId = $currentBaseId;\n      } else {\n        out += ' var ' + ($nextValid) + ' = true; ';\n      }\n      if ($i) {\n        out += ' if (' + ($nextValid) + ' && ' + ($prevValid) + ') { ' + ($valid) + ' = false; ' + ($passingSchemas) + ' = [' + ($passingSchemas) + ', ' + ($i) + ']; } else { ';\n        $closingBraces += '}';\n      }\n      out += ' if (' + ($nextValid) + ') { ' + ($valid) + ' = ' + ($prevValid) + ' = true; ' + ($passingSchemas) + ' = ' + ($i) + '; }';\n    }\n  }\n  it.compositeRule = $it.compositeRule = $wasComposite;\n  out += '' + ($closingBraces) + 'if (!' + ($valid) + ') {   var err =   '; /* istanbul ignore else */\n  if (it.createErrors !== false) {\n    out += ' { keyword: \\'' + ('oneOf') + '\\' , dataPath: (dataPath || \\'\\') + ' + (it.errorPath) + ' , schemaPath: ' + (it.util.toQuotedString($errSchemaPath)) + ' , params: { passingSchemas: ' + ($passingSchemas) + ' } ';\n    if (it.opts.messages !== false) {\n      out += ' , message: \\'should match exactly one schema in oneOf\\' ';\n    }\n    if (it.opts.verbose) {\n      out += ' , schema: validate.schema' + ($schemaPath) + ' , parentSchema: validate.schema' + (it.schemaPath) + ' , data: ' + ($data) + ' ';\n    }\n    out += ' } ';\n  } else {\n    out += ' {} ';\n  }\n  out += ';  if (vErrors === null) vErrors = [err]; else vErrors.push(err); errors++; ';\n  if (!it.compositeRule && $breakOnError) {\n    /* istanbul ignore if */\n    if (it.async) {\n      out += ' throw new ValidationError(vErrors); ';\n    } else {\n      out += ' validate.errors = vErrors; return false; ';\n    }\n  }\n  out += '} else {  errors = ' + ($errs) + '; if (vErrors !== null) { if (' + ($errs) + ') vErrors.length = ' + ($errs) + '; else vErrors = null; }';\n  if (it.opts.allErrors) {\n    out += ' } ';\n  }\n  return out;\n}\n", "\nmodule.exports = function generate_pattern(it, $keyword, $ruleType) {\n  var out = ' ';\n  var $lvl = it.level;\n  var $dataLvl = it.dataLevel;\n  var $schema = it.schema[$keyword];\n  var $schemaPath = it.schemaPath + it.util.getProperty($keyword);\n  var $errSchemaPath = it.errSchemaPath + '/' + $keyword;\n  var $breakOnError = !it.opts.allErrors;\n  var $data = 'data' + ($dataLvl || '');\n  var $isData = it.opts.$data && $schema && $schema.$data,\n    $schemaValue;\n  if ($isData) {\n    out += ' var schema' + ($lvl) + ' = ' + (it.util.getData($schema.$data, $dataLvl, it.dataPathArr)) + '; ';\n    $schemaValue = 'schema' + $lvl;\n  } else {\n    $schemaValue = $schema;\n  }\n  var $regexp = $isData ? '(new RegExp(' + $schemaValue + '))' : it.usePattern($schema);\n  out += 'if ( ';\n  if ($isData) {\n    out += ' (' + ($schemaValue) + ' !== undefined && typeof ' + ($schemaValue) + ' != \\'string\\') || ';\n  }\n  out += ' !' + ($regexp) + '.test(' + ($data) + ') ) {   ';\n  var $$outStack = $$outStack || [];\n  $$outStack.push(out);\n  out = ''; /* istanbul ignore else */\n  if (it.createErrors !== false) {\n    out += ' { keyword: \\'' + ('pattern') + '\\' , dataPath: (dataPath || \\'\\') + ' + (it.errorPath) + ' , schemaPath: ' + (it.util.toQuotedString($errSchemaPath)) + ' , params: { pattern:  ';\n    if ($isData) {\n      out += '' + ($schemaValue);\n    } else {\n      out += '' + (it.util.toQuotedString($schema));\n    }\n    out += '  } ';\n    if (it.opts.messages !== false) {\n      out += ' , message: \\'should match pattern \"';\n      if ($isData) {\n        out += '\\' + ' + ($schemaValue) + ' + \\'';\n      } else {\n        out += '' + (it.util.escapeQuotes($schema));\n      }\n      out += '\"\\' ';\n    }\n    if (it.opts.verbose) {\n      out += ' , schema:  ';\n      if ($isData) {\n        out += 'validate.schema' + ($schemaPath);\n      } else {\n        out += '' + (it.util.toQuotedString($schema));\n      }\n      out += '         , parentSchema: validate.schema' + (it.schemaPath) + ' , data: ' + ($data) + ' ';\n    }\n    out += ' } ';\n  } else {\n    out += ' {} ';\n  }\n  var __err = out;\n  out = $$outStack.pop();\n  if (!it.compositeRule && $breakOnError) {\n    /* istanbul ignore if */\n    if (it.async) {\n      out += ' throw new ValidationError([' + (__err) + ']); ';\n    } else {\n      out += ' validate.errors = [' + (__err) + ']; return false; ';\n    }\n  } else {\n    out += ' var err = ' + (__err) + ';  if (vErrors === null) vErrors = [err]; else vErrors.push(err); errors++; ';\n  }\n  out += '} ';\n  if ($breakOnError) {\n    out += ' else { ';\n  }\n  return out;\n}\n", "\nmodule.exports = function generate_properties(it, $keyword, $ruleType) {\n  var out = ' ';\n  var $lvl = it.level;\n  var $dataLvl = it.dataLevel;\n  var $schema = it.schema[$keyword];\n  var $schemaPath = it.schemaPath + it.util.getProperty($keyword);\n  var $errSchemaPath = it.errSchemaPath + '/' + $keyword;\n  var $breakOnError = !it.opts.allErrors;\n  var $data = 'data' + ($dataLvl || '');\n  var $errs = 'errs__' + $lvl;\n  var $it = it.util.copy(it);\n  var $closingBraces = '';\n  $it.level++;\n  var $nextValid = 'valid' + $it.level;\n  var $key = 'key' + $lvl,\n    $idx = 'idx' + $lvl,\n    $dataNxt = $it.dataLevel = it.dataLevel + 1,\n    $nextData = 'data' + $dataNxt,\n    $dataProperties = 'dataProperties' + $lvl;\n  var $schemaKeys = Object.keys($schema || {}).filter(notProto),\n    $pProperties = it.schema.patternProperties || {},\n    $pPropertyKeys = Object.keys($pProperties).filter(notProto),\n    $aProperties = it.schema.additionalProperties,\n    $someProperties = $schemaKeys.length || $pPropertyKeys.length,\n    $noAdditional = $aProperties === false,\n    $additionalIsSchema = typeof $aProperties == 'object' && Object.keys($aProperties).length,\n    $removeAdditional = it.opts.removeAdditional,\n    $checkAdditional = $noAdditional || $additionalIsSchema || $removeAdditional,\n    $ownProperties = it.opts.ownProperties,\n    $currentBaseId = it.baseId;\n  var $required = it.schema.required;\n  if ($required && !(it.opts.$data && $required.$data) && $required.length < it.opts.loopRequired) {\n    var $requiredHash = it.util.toHash($required);\n  }\n\n  function notProto(p) {\n    return p !== '__proto__';\n  }\n  out += 'var ' + ($errs) + ' = errors;var ' + ($nextValid) + ' = true;';\n  if ($ownProperties) {\n    out += ' var ' + ($dataProperties) + ' = undefined;';\n  }\n  if ($checkAdditional) {\n    if ($ownProperties) {\n      out += ' ' + ($dataProperties) + ' = ' + ($dataProperties) + ' || Object.keys(' + ($data) + '); for (var ' + ($idx) + '=0; ' + ($idx) + '<' + ($dataProperties) + '.length; ' + ($idx) + '++) { var ' + ($key) + ' = ' + ($dataProperties) + '[' + ($idx) + ']; ';\n    } else {\n      out += ' for (var ' + ($key) + ' in ' + ($data) + ') { ';\n    }\n    if ($someProperties) {\n      out += ' var isAdditional' + ($lvl) + ' = !(false ';\n      if ($schemaKeys.length) {\n        if ($schemaKeys.length > 8) {\n          out += ' || validate.schema' + ($schemaPath) + '.hasOwnProperty(' + ($key) + ') ';\n        } else {\n          var arr1 = $schemaKeys;\n          if (arr1) {\n            var $propertyKey, i1 = -1,\n              l1 = arr1.length - 1;\n            while (i1 < l1) {\n              $propertyKey = arr1[i1 += 1];\n              out += ' || ' + ($key) + ' == ' + (it.util.toQuotedString($propertyKey)) + ' ';\n            }\n          }\n        }\n      }\n      if ($pPropertyKeys.length) {\n        var arr2 = $pPropertyKeys;\n        if (arr2) {\n          var $pProperty, $i = -1,\n            l2 = arr2.length - 1;\n          while ($i < l2) {\n            $pProperty = arr2[$i += 1];\n            out += ' || ' + (it.usePattern($pProperty)) + '.test(' + ($key) + ') ';\n          }\n        }\n      }\n      out += ' ); if (isAdditional' + ($lvl) + ') { ';\n    }\n    if ($removeAdditional == 'all') {\n      out += ' delete ' + ($data) + '[' + ($key) + ']; ';\n    } else {\n      var $currentErrorPath = it.errorPath;\n      var $additionalProperty = '\\' + ' + $key + ' + \\'';\n      if (it.opts._errorDataPathProperty) {\n        it.errorPath = it.util.getPathExpr(it.errorPath, $key, it.opts.jsonPointers);\n      }\n      if ($noAdditional) {\n        if ($removeAdditional) {\n          out += ' delete ' + ($data) + '[' + ($key) + ']; ';\n        } else {\n          out += ' ' + ($nextValid) + ' = false; ';\n          var $currErrSchemaPath = $errSchemaPath;\n          $errSchemaPath = it.errSchemaPath + '/additionalProperties';\n          var $$outStack = $$outStack || [];\n          $$outStack.push(out);\n          out = ''; /* istanbul ignore else */\n          if (it.createErrors !== false) {\n            out += ' { keyword: \\'' + ('additionalProperties') + '\\' , dataPath: (dataPath || \\'\\') + ' + (it.errorPath) + ' , schemaPath: ' + (it.util.toQuotedString($errSchemaPath)) + ' , params: { additionalProperty: \\'' + ($additionalProperty) + '\\' } ';\n            if (it.opts.messages !== false) {\n              out += ' , message: \\'';\n              if (it.opts._errorDataPathProperty) {\n                out += 'is an invalid additional property';\n              } else {\n                out += 'should NOT have additional properties';\n              }\n              out += '\\' ';\n            }\n            if (it.opts.verbose) {\n              out += ' , schema: false , parentSchema: validate.schema' + (it.schemaPath) + ' , data: ' + ($data) + ' ';\n            }\n            out += ' } ';\n          } else {\n            out += ' {} ';\n          }\n          var __err = out;\n          out = $$outStack.pop();\n          if (!it.compositeRule && $breakOnError) {\n            /* istanbul ignore if */\n            if (it.async) {\n              out += ' throw new ValidationError([' + (__err) + ']); ';\n            } else {\n              out += ' validate.errors = [' + (__err) + ']; return false; ';\n            }\n          } else {\n            out += ' var err = ' + (__err) + ';  if (vErrors === null) vErrors = [err]; else vErrors.push(err); errors++; ';\n          }\n          $errSchemaPath = $currErrSchemaPath;\n          if ($breakOnError) {\n            out += ' break; ';\n          }\n        }\n      } else if ($additionalIsSchema) {\n        if ($removeAdditional == 'failing') {\n          out += ' var ' + ($errs) + ' = errors;  ';\n          var $wasComposite = it.compositeRule;\n          it.compositeRule = $it.compositeRule = true;\n          $it.schema = $aProperties;\n          $it.schemaPath = it.schemaPath + '.additionalProperties';\n          $it.errSchemaPath = it.errSchemaPath + '/additionalProperties';\n          $it.errorPath = it.opts._errorDataPathProperty ? it.errorPath : it.util.getPathExpr(it.errorPath, $key, it.opts.jsonPointers);\n          var $passData = $data + '[' + $key + ']';\n          $it.dataPathArr[$dataNxt] = $key;\n          var $code = it.validate($it);\n          $it.baseId = $currentBaseId;\n          if (it.util.varOccurences($code, $nextData) < 2) {\n            out += ' ' + (it.util.varReplace($code, $nextData, $passData)) + ' ';\n          } else {\n            out += ' var ' + ($nextData) + ' = ' + ($passData) + '; ' + ($code) + ' ';\n          }\n          out += ' if (!' + ($nextValid) + ') { errors = ' + ($errs) + '; if (validate.errors !== null) { if (errors) validate.errors.length = errors; else validate.errors = null; } delete ' + ($data) + '[' + ($key) + ']; }  ';\n          it.compositeRule = $it.compositeRule = $wasComposite;\n        } else {\n          $it.schema = $aProperties;\n          $it.schemaPath = it.schemaPath + '.additionalProperties';\n          $it.errSchemaPath = it.errSchemaPath + '/additionalProperties';\n          $it.errorPath = it.opts._errorDataPathProperty ? it.errorPath : it.util.getPathExpr(it.errorPath, $key, it.opts.jsonPointers);\n          var $passData = $data + '[' + $key + ']';\n          $it.dataPathArr[$dataNxt] = $key;\n          var $code = it.validate($it);\n          $it.baseId = $currentBaseId;\n          if (it.util.varOccurences($code, $nextData) < 2) {\n            out += ' ' + (it.util.varReplace($code, $nextData, $passData)) + ' ';\n          } else {\n            out += ' var ' + ($nextData) + ' = ' + ($passData) + '; ' + ($code) + ' ';\n          }\n          if ($breakOnError) {\n            out += ' if (!' + ($nextValid) + ') break; ';\n          }\n        }\n      }\n      it.errorPath = $currentErrorPath;\n    }\n    if ($someProperties) {\n      out += ' } ';\n    }\n    out += ' }  ';\n    if ($breakOnError) {\n      out += ' if (' + ($nextValid) + ') { ';\n      $closingBraces += '}';\n    }\n  }\n  var $useDefaults = it.opts.useDefaults && !it.compositeRule;\n  if ($schemaKeys.length) {\n    var arr3 = $schemaKeys;\n    if (arr3) {\n      var $propertyKey, i3 = -1,\n        l3 = arr3.length - 1;\n      while (i3 < l3) {\n        $propertyKey = arr3[i3 += 1];\n        var $sch = $schema[$propertyKey];\n        if ((it.opts.strictKeywords ? (typeof $sch == 'object' && Object.keys($sch).length > 0) || $sch === false : it.util.schemaHasRules($sch, it.RULES.all))) {\n          var $prop = it.util.getProperty($propertyKey),\n            $passData = $data + $prop,\n            $hasDefault = $useDefaults && $sch.default !== undefined;\n          $it.schema = $sch;\n          $it.schemaPath = $schemaPath + $prop;\n          $it.errSchemaPath = $errSchemaPath + '/' + it.util.escapeFragment($propertyKey);\n          $it.errorPath = it.util.getPath(it.errorPath, $propertyKey, it.opts.jsonPointers);\n          $it.dataPathArr[$dataNxt] = it.util.toQuotedString($propertyKey);\n          var $code = it.validate($it);\n          $it.baseId = $currentBaseId;\n          if (it.util.varOccurences($code, $nextData) < 2) {\n            $code = it.util.varReplace($code, $nextData, $passData);\n            var $useData = $passData;\n          } else {\n            var $useData = $nextData;\n            out += ' var ' + ($nextData) + ' = ' + ($passData) + '; ';\n          }\n          if ($hasDefault) {\n            out += ' ' + ($code) + ' ';\n          } else {\n            if ($requiredHash && $requiredHash[$propertyKey]) {\n              out += ' if ( ' + ($useData) + ' === undefined ';\n              if ($ownProperties) {\n                out += ' || ! Object.prototype.hasOwnProperty.call(' + ($data) + ', \\'' + (it.util.escapeQuotes($propertyKey)) + '\\') ';\n              }\n              out += ') { ' + ($nextValid) + ' = false; ';\n              var $currentErrorPath = it.errorPath,\n                $currErrSchemaPath = $errSchemaPath,\n                $missingProperty = it.util.escapeQuotes($propertyKey);\n              if (it.opts._errorDataPathProperty) {\n                it.errorPath = it.util.getPath($currentErrorPath, $propertyKey, it.opts.jsonPointers);\n              }\n              $errSchemaPath = it.errSchemaPath + '/required';\n              var $$outStack = $$outStack || [];\n              $$outStack.push(out);\n              out = ''; /* istanbul ignore else */\n              if (it.createErrors !== false) {\n                out += ' { keyword: \\'' + ('required') + '\\' , dataPath: (dataPath || \\'\\') + ' + (it.errorPath) + ' , schemaPath: ' + (it.util.toQuotedString($errSchemaPath)) + ' , params: { missingProperty: \\'' + ($missingProperty) + '\\' } ';\n                if (it.opts.messages !== false) {\n                  out += ' , message: \\'';\n                  if (it.opts._errorDataPathProperty) {\n                    out += 'is a required property';\n                  } else {\n                    out += 'should have required property \\\\\\'' + ($missingProperty) + '\\\\\\'';\n                  }\n                  out += '\\' ';\n                }\n                if (it.opts.verbose) {\n                  out += ' , schema: validate.schema' + ($schemaPath) + ' , parentSchema: validate.schema' + (it.schemaPath) + ' , data: ' + ($data) + ' ';\n                }\n                out += ' } ';\n              } else {\n                out += ' {} ';\n              }\n              var __err = out;\n              out = $$outStack.pop();\n              if (!it.compositeRule && $breakOnError) {\n                /* istanbul ignore if */\n                if (it.async) {\n                  out += ' throw new ValidationError([' + (__err) + ']); ';\n                } else {\n                  out += ' validate.errors = [' + (__err) + ']; return false; ';\n                }\n              } else {\n                out += ' var err = ' + (__err) + ';  if (vErrors === null) vErrors = [err]; else vErrors.push(err); errors++; ';\n              }\n              $errSchemaPath = $currErrSchemaPath;\n              it.errorPath = $currentErrorPath;\n              out += ' } else { ';\n            } else {\n              if ($breakOnError) {\n                out += ' if ( ' + ($useData) + ' === undefined ';\n                if ($ownProperties) {\n                  out += ' || ! Object.prototype.hasOwnProperty.call(' + ($data) + ', \\'' + (it.util.escapeQuotes($propertyKey)) + '\\') ';\n                }\n                out += ') { ' + ($nextValid) + ' = true; } else { ';\n              } else {\n                out += ' if (' + ($useData) + ' !== undefined ';\n                if ($ownProperties) {\n                  out += ' &&   Object.prototype.hasOwnProperty.call(' + ($data) + ', \\'' + (it.util.escapeQuotes($propertyKey)) + '\\') ';\n                }\n                out += ' ) { ';\n              }\n            }\n            out += ' ' + ($code) + ' } ';\n          }\n        }\n        if ($breakOnError) {\n          out += ' if (' + ($nextValid) + ') { ';\n          $closingBraces += '}';\n        }\n      }\n    }\n  }\n  if ($pPropertyKeys.length) {\n    var arr4 = $pPropertyKeys;\n    if (arr4) {\n      var $pProperty, i4 = -1,\n        l4 = arr4.length - 1;\n      while (i4 < l4) {\n        $pProperty = arr4[i4 += 1];\n        var $sch = $pProperties[$pProperty];\n        if ((it.opts.strictKeywords ? (typeof $sch == 'object' && Object.keys($sch).length > 0) || $sch === false : it.util.schemaHasRules($sch, it.RULES.all))) {\n          $it.schema = $sch;\n          $it.schemaPath = it.schemaPath + '.patternProperties' + it.util.getProperty($pProperty);\n          $it.errSchemaPath = it.errSchemaPath + '/patternProperties/' + it.util.escapeFragment($pProperty);\n          if ($ownProperties) {\n            out += ' ' + ($dataProperties) + ' = ' + ($dataProperties) + ' || Object.keys(' + ($data) + '); for (var ' + ($idx) + '=0; ' + ($idx) + '<' + ($dataProperties) + '.length; ' + ($idx) + '++) { var ' + ($key) + ' = ' + ($dataProperties) + '[' + ($idx) + ']; ';\n          } else {\n            out += ' for (var ' + ($key) + ' in ' + ($data) + ') { ';\n          }\n          out += ' if (' + (it.usePattern($pProperty)) + '.test(' + ($key) + ')) { ';\n          $it.errorPath = it.util.getPathExpr(it.errorPath, $key, it.opts.jsonPointers);\n          var $passData = $data + '[' + $key + ']';\n          $it.dataPathArr[$dataNxt] = $key;\n          var $code = it.validate($it);\n          $it.baseId = $currentBaseId;\n          if (it.util.varOccurences($code, $nextData) < 2) {\n            out += ' ' + (it.util.varReplace($code, $nextData, $passData)) + ' ';\n          } else {\n            out += ' var ' + ($nextData) + ' = ' + ($passData) + '; ' + ($code) + ' ';\n          }\n          if ($breakOnError) {\n            out += ' if (!' + ($nextValid) + ') break; ';\n          }\n          out += ' } ';\n          if ($breakOnError) {\n            out += ' else ' + ($nextValid) + ' = true; ';\n          }\n          out += ' }  ';\n          if ($breakOnError) {\n            out += ' if (' + ($nextValid) + ') { ';\n            $closingBraces += '}';\n          }\n        }\n      }\n    }\n  }\n  if ($breakOnError) {\n    out += ' ' + ($closingBraces) + ' if (' + ($errs) + ' == errors) {';\n  }\n  return out;\n}\n", "\nmodule.exports = function generate_propertyNames(it, $keyword, $ruleType) {\n  var out = ' ';\n  var $lvl = it.level;\n  var $dataLvl = it.dataLevel;\n  var $schema = it.schema[$keyword];\n  var $schemaPath = it.schemaPath + it.util.getProperty($keyword);\n  var $errSchemaPath = it.errSchemaPath + '/' + $keyword;\n  var $breakOnError = !it.opts.allErrors;\n  var $data = 'data' + ($dataLvl || '');\n  var $errs = 'errs__' + $lvl;\n  var $it = it.util.copy(it);\n  var $closingBraces = '';\n  $it.level++;\n  var $nextValid = 'valid' + $it.level;\n  out += 'var ' + ($errs) + ' = errors;';\n  if ((it.opts.strictKeywords ? (typeof $schema == 'object' && Object.keys($schema).length > 0) || $schema === false : it.util.schemaHasRules($schema, it.RULES.all))) {\n    $it.schema = $schema;\n    $it.schemaPath = $schemaPath;\n    $it.errSchemaPath = $errSchemaPath;\n    var $key = 'key' + $lvl,\n      $idx = 'idx' + $lvl,\n      $i = 'i' + $lvl,\n      $invalidName = '\\' + ' + $key + ' + \\'',\n      $dataNxt = $it.dataLevel = it.dataLevel + 1,\n      $nextData = 'data' + $dataNxt,\n      $dataProperties = 'dataProperties' + $lvl,\n      $ownProperties = it.opts.ownProperties,\n      $currentBaseId = it.baseId;\n    if ($ownProperties) {\n      out += ' var ' + ($dataProperties) + ' = undefined; ';\n    }\n    if ($ownProperties) {\n      out += ' ' + ($dataProperties) + ' = ' + ($dataProperties) + ' || Object.keys(' + ($data) + '); for (var ' + ($idx) + '=0; ' + ($idx) + '<' + ($dataProperties) + '.length; ' + ($idx) + '++) { var ' + ($key) + ' = ' + ($dataProperties) + '[' + ($idx) + ']; ';\n    } else {\n      out += ' for (var ' + ($key) + ' in ' + ($data) + ') { ';\n    }\n    out += ' var startErrs' + ($lvl) + ' = errors; ';\n    var $passData = $key;\n    var $wasComposite = it.compositeRule;\n    it.compositeRule = $it.compositeRule = true;\n    var $code = it.validate($it);\n    $it.baseId = $currentBaseId;\n    if (it.util.varOccurences($code, $nextData) < 2) {\n      out += ' ' + (it.util.varReplace($code, $nextData, $passData)) + ' ';\n    } else {\n      out += ' var ' + ($nextData) + ' = ' + ($passData) + '; ' + ($code) + ' ';\n    }\n    it.compositeRule = $it.compositeRule = $wasComposite;\n    out += ' if (!' + ($nextValid) + ') { for (var ' + ($i) + '=startErrs' + ($lvl) + '; ' + ($i) + '<errors; ' + ($i) + '++) { vErrors[' + ($i) + '].propertyName = ' + ($key) + '; }   var err =   '; /* istanbul ignore else */\n    if (it.createErrors !== false) {\n      out += ' { keyword: \\'' + ('propertyNames') + '\\' , dataPath: (dataPath || \\'\\') + ' + (it.errorPath) + ' , schemaPath: ' + (it.util.toQuotedString($errSchemaPath)) + ' , params: { propertyName: \\'' + ($invalidName) + '\\' } ';\n      if (it.opts.messages !== false) {\n        out += ' , message: \\'property name \\\\\\'' + ($invalidName) + '\\\\\\' is invalid\\' ';\n      }\n      if (it.opts.verbose) {\n        out += ' , schema: validate.schema' + ($schemaPath) + ' , parentSchema: validate.schema' + (it.schemaPath) + ' , data: ' + ($data) + ' ';\n      }\n      out += ' } ';\n    } else {\n      out += ' {} ';\n    }\n    out += ';  if (vErrors === null) vErrors = [err]; else vErrors.push(err); errors++; ';\n    if (!it.compositeRule && $breakOnError) {\n      /* istanbul ignore if */\n      if (it.async) {\n        out += ' throw new ValidationError(vErrors); ';\n      } else {\n        out += ' validate.errors = vErrors; return false; ';\n      }\n    }\n    if ($breakOnError) {\n      out += ' break; ';\n    }\n    out += ' } }';\n  }\n  if ($breakOnError) {\n    out += ' ' + ($closingBraces) + ' if (' + ($errs) + ' == errors) {';\n  }\n  return out;\n}\n", "\nmodule.exports = function generate_required(it, $keyword, $ruleType) {\n  var out = ' ';\n  var $lvl = it.level;\n  var $dataLvl = it.dataLevel;\n  var $schema = it.schema[$keyword];\n  var $schemaPath = it.schemaPath + it.util.getProperty($keyword);\n  var $errSchemaPath = it.errSchemaPath + '/' + $keyword;\n  var $breakOnError = !it.opts.allErrors;\n  var $data = 'data' + ($dataLvl || '');\n  var $valid = 'valid' + $lvl;\n  var $isData = it.opts.$data && $schema && $schema.$data,\n    $schemaValue;\n  if ($isData) {\n    out += ' var schema' + ($lvl) + ' = ' + (it.util.getData($schema.$data, $dataLvl, it.dataPathArr)) + '; ';\n    $schemaValue = 'schema' + $lvl;\n  } else {\n    $schemaValue = $schema;\n  }\n  var $vSchema = 'schema' + $lvl;\n  if (!$isData) {\n    if ($schema.length < it.opts.loopRequired && it.schema.properties && Object.keys(it.schema.properties).length) {\n      var $required = [];\n      var arr1 = $schema;\n      if (arr1) {\n        var $property, i1 = -1,\n          l1 = arr1.length - 1;\n        while (i1 < l1) {\n          $property = arr1[i1 += 1];\n          var $propertySch = it.schema.properties[$property];\n          if (!($propertySch && (it.opts.strictKeywords ? (typeof $propertySch == 'object' && Object.keys($propertySch).length > 0) || $propertySch === false : it.util.schemaHasRules($propertySch, it.RULES.all)))) {\n            $required[$required.length] = $property;\n          }\n        }\n      }\n    } else {\n      var $required = $schema;\n    }\n  }\n  if ($isData || $required.length) {\n    var $currentErrorPath = it.errorPath,\n      $loopRequired = $isData || $required.length >= it.opts.loopRequired,\n      $ownProperties = it.opts.ownProperties;\n    if ($breakOnError) {\n      out += ' var missing' + ($lvl) + '; ';\n      if ($loopRequired) {\n        if (!$isData) {\n          out += ' var ' + ($vSchema) + ' = validate.schema' + ($schemaPath) + '; ';\n        }\n        var $i = 'i' + $lvl,\n          $propertyPath = 'schema' + $lvl + '[' + $i + ']',\n          $missingProperty = '\\' + ' + $propertyPath + ' + \\'';\n        if (it.opts._errorDataPathProperty) {\n          it.errorPath = it.util.getPathExpr($currentErrorPath, $propertyPath, it.opts.jsonPointers);\n        }\n        out += ' var ' + ($valid) + ' = true; ';\n        if ($isData) {\n          out += ' if (schema' + ($lvl) + ' === undefined) ' + ($valid) + ' = true; else if (!Array.isArray(schema' + ($lvl) + ')) ' + ($valid) + ' = false; else {';\n        }\n        out += ' for (var ' + ($i) + ' = 0; ' + ($i) + ' < ' + ($vSchema) + '.length; ' + ($i) + '++) { ' + ($valid) + ' = ' + ($data) + '[' + ($vSchema) + '[' + ($i) + ']] !== undefined ';\n        if ($ownProperties) {\n          out += ' &&   Object.prototype.hasOwnProperty.call(' + ($data) + ', ' + ($vSchema) + '[' + ($i) + ']) ';\n        }\n        out += '; if (!' + ($valid) + ') break; } ';\n        if ($isData) {\n          out += '  }  ';\n        }\n        out += '  if (!' + ($valid) + ') {   ';\n        var $$outStack = $$outStack || [];\n        $$outStack.push(out);\n        out = ''; /* istanbul ignore else */\n        if (it.createErrors !== false) {\n          out += ' { keyword: \\'' + ('required') + '\\' , dataPath: (dataPath || \\'\\') + ' + (it.errorPath) + ' , schemaPath: ' + (it.util.toQuotedString($errSchemaPath)) + ' , params: { missingProperty: \\'' + ($missingProperty) + '\\' } ';\n          if (it.opts.messages !== false) {\n            out += ' , message: \\'';\n            if (it.opts._errorDataPathProperty) {\n              out += 'is a required property';\n            } else {\n              out += 'should have required property \\\\\\'' + ($missingProperty) + '\\\\\\'';\n            }\n            out += '\\' ';\n          }\n          if (it.opts.verbose) {\n            out += ' , schema: validate.schema' + ($schemaPath) + ' , parentSchema: validate.schema' + (it.schemaPath) + ' , data: ' + ($data) + ' ';\n          }\n          out += ' } ';\n        } else {\n          out += ' {} ';\n        }\n        var __err = out;\n        out = $$outStack.pop();\n        if (!it.compositeRule && $breakOnError) {\n          /* istanbul ignore if */\n          if (it.async) {\n            out += ' throw new ValidationError([' + (__err) + ']); ';\n          } else {\n            out += ' validate.errors = [' + (__err) + ']; return false; ';\n          }\n        } else {\n          out += ' var err = ' + (__err) + ';  if (vErrors === null) vErrors = [err]; else vErrors.push(err); errors++; ';\n        }\n        out += ' } else { ';\n      } else {\n        out += ' if ( ';\n        var arr2 = $required;\n        if (arr2) {\n          var $propertyKey, $i = -1,\n            l2 = arr2.length - 1;\n          while ($i < l2) {\n            $propertyKey = arr2[$i += 1];\n            if ($i) {\n              out += ' || ';\n            }\n            var $prop = it.util.getProperty($propertyKey),\n              $useData = $data + $prop;\n            out += ' ( ( ' + ($useData) + ' === undefined ';\n            if ($ownProperties) {\n              out += ' || ! Object.prototype.hasOwnProperty.call(' + ($data) + ', \\'' + (it.util.escapeQuotes($propertyKey)) + '\\') ';\n            }\n            out += ') && (missing' + ($lvl) + ' = ' + (it.util.toQuotedString(it.opts.jsonPointers ? $propertyKey : $prop)) + ') ) ';\n          }\n        }\n        out += ') {  ';\n        var $propertyPath = 'missing' + $lvl,\n          $missingProperty = '\\' + ' + $propertyPath + ' + \\'';\n        if (it.opts._errorDataPathProperty) {\n          it.errorPath = it.opts.jsonPointers ? it.util.getPathExpr($currentErrorPath, $propertyPath, true) : $currentErrorPath + ' + ' + $propertyPath;\n        }\n        var $$outStack = $$outStack || [];\n        $$outStack.push(out);\n        out = ''; /* istanbul ignore else */\n        if (it.createErrors !== false) {\n          out += ' { keyword: \\'' + ('required') + '\\' , dataPath: (dataPath || \\'\\') + ' + (it.errorPath) + ' , schemaPath: ' + (it.util.toQuotedString($errSchemaPath)) + ' , params: { missingProperty: \\'' + ($missingProperty) + '\\' } ';\n          if (it.opts.messages !== false) {\n            out += ' , message: \\'';\n            if (it.opts._errorDataPathProperty) {\n              out += 'is a required property';\n            } else {\n              out += 'should have required property \\\\\\'' + ($missingProperty) + '\\\\\\'';\n            }\n            out += '\\' ';\n          }\n          if (it.opts.verbose) {\n            out += ' , schema: validate.schema' + ($schemaPath) + ' , parentSchema: validate.schema' + (it.schemaPath) + ' , data: ' + ($data) + ' ';\n          }\n          out += ' } ';\n        } else {\n          out += ' {} ';\n        }\n        var __err = out;\n        out = $$outStack.pop();\n        if (!it.compositeRule && $breakOnError) {\n          /* istanbul ignore if */\n          if (it.async) {\n            out += ' throw new ValidationError([' + (__err) + ']); ';\n          } else {\n            out += ' validate.errors = [' + (__err) + ']; return false; ';\n          }\n        } else {\n          out += ' var err = ' + (__err) + ';  if (vErrors === null) vErrors = [err]; else vErrors.push(err); errors++; ';\n        }\n        out += ' } else { ';\n      }\n    } else {\n      if ($loopRequired) {\n        if (!$isData) {\n          out += ' var ' + ($vSchema) + ' = validate.schema' + ($schemaPath) + '; ';\n        }\n        var $i = 'i' + $lvl,\n          $propertyPath = 'schema' + $lvl + '[' + $i + ']',\n          $missingProperty = '\\' + ' + $propertyPath + ' + \\'';\n        if (it.opts._errorDataPathProperty) {\n          it.errorPath = it.util.getPathExpr($currentErrorPath, $propertyPath, it.opts.jsonPointers);\n        }\n        if ($isData) {\n          out += ' if (' + ($vSchema) + ' && !Array.isArray(' + ($vSchema) + ')) {  var err =   '; /* istanbul ignore else */\n          if (it.createErrors !== false) {\n            out += ' { keyword: \\'' + ('required') + '\\' , dataPath: (dataPath || \\'\\') + ' + (it.errorPath) + ' , schemaPath: ' + (it.util.toQuotedString($errSchemaPath)) + ' , params: { missingProperty: \\'' + ($missingProperty) + '\\' } ';\n            if (it.opts.messages !== false) {\n              out += ' , message: \\'';\n              if (it.opts._errorDataPathProperty) {\n                out += 'is a required property';\n              } else {\n                out += 'should have required property \\\\\\'' + ($missingProperty) + '\\\\\\'';\n              }\n              out += '\\' ';\n            }\n            if (it.opts.verbose) {\n              out += ' , schema: validate.schema' + ($schemaPath) + ' , parentSchema: validate.schema' + (it.schemaPath) + ' , data: ' + ($data) + ' ';\n            }\n            out += ' } ';\n          } else {\n            out += ' {} ';\n          }\n          out += ';  if (vErrors === null) vErrors = [err]; else vErrors.push(err); errors++; } else if (' + ($vSchema) + ' !== undefined) { ';\n        }\n        out += ' for (var ' + ($i) + ' = 0; ' + ($i) + ' < ' + ($vSchema) + '.length; ' + ($i) + '++) { if (' + ($data) + '[' + ($vSchema) + '[' + ($i) + ']] === undefined ';\n        if ($ownProperties) {\n          out += ' || ! Object.prototype.hasOwnProperty.call(' + ($data) + ', ' + ($vSchema) + '[' + ($i) + ']) ';\n        }\n        out += ') {  var err =   '; /* istanbul ignore else */\n        if (it.createErrors !== false) {\n          out += ' { keyword: \\'' + ('required') + '\\' , dataPath: (dataPath || \\'\\') + ' + (it.errorPath) + ' , schemaPath: ' + (it.util.toQuotedString($errSchemaPath)) + ' , params: { missingProperty: \\'' + ($missingProperty) + '\\' } ';\n          if (it.opts.messages !== false) {\n            out += ' , message: \\'';\n            if (it.opts._errorDataPathProperty) {\n              out += 'is a required property';\n            } else {\n              out += 'should have required property \\\\\\'' + ($missingProperty) + '\\\\\\'';\n            }\n            out += '\\' ';\n          }\n          if (it.opts.verbose) {\n            out += ' , schema: validate.schema' + ($schemaPath) + ' , parentSchema: validate.schema' + (it.schemaPath) + ' , data: ' + ($data) + ' ';\n          }\n          out += ' } ';\n        } else {\n          out += ' {} ';\n        }\n        out += ';  if (vErrors === null) vErrors = [err]; else vErrors.push(err); errors++; } } ';\n        if ($isData) {\n          out += '  }  ';\n        }\n      } else {\n        var arr3 = $required;\n        if (arr3) {\n          var $propertyKey, i3 = -1,\n            l3 = arr3.length - 1;\n          while (i3 < l3) {\n            $propertyKey = arr3[i3 += 1];\n            var $prop = it.util.getProperty($propertyKey),\n              $missingProperty = it.util.escapeQuotes($propertyKey),\n              $useData = $data + $prop;\n            if (it.opts._errorDataPathProperty) {\n              it.errorPath = it.util.getPath($currentErrorPath, $propertyKey, it.opts.jsonPointers);\n            }\n            out += ' if ( ' + ($useData) + ' === undefined ';\n            if ($ownProperties) {\n              out += ' || ! Object.prototype.hasOwnProperty.call(' + ($data) + ', \\'' + (it.util.escapeQuotes($propertyKey)) + '\\') ';\n            }\n            out += ') {  var err =   '; /* istanbul ignore else */\n            if (it.createErrors !== false) {\n              out += ' { keyword: \\'' + ('required') + '\\' , dataPath: (dataPath || \\'\\') + ' + (it.errorPath) + ' , schemaPath: ' + (it.util.toQuotedString($errSchemaPath)) + ' , params: { missingProperty: \\'' + ($missingProperty) + '\\' } ';\n              if (it.opts.messages !== false) {\n                out += ' , message: \\'';\n                if (it.opts._errorDataPathProperty) {\n                  out += 'is a required property';\n                } else {\n                  out += 'should have required property \\\\\\'' + ($missingProperty) + '\\\\\\'';\n                }\n                out += '\\' ';\n              }\n              if (it.opts.verbose) {\n                out += ' , schema: validate.schema' + ($schemaPath) + ' , parentSchema: validate.schema' + (it.schemaPath) + ' , data: ' + ($data) + ' ';\n              }\n              out += ' } ';\n            } else {\n              out += ' {} ';\n            }\n            out += ';  if (vErrors === null) vErrors = [err]; else vErrors.push(err); errors++; } ';\n          }\n        }\n      }\n    }\n    it.errorPath = $currentErrorPath;\n  } else if ($breakOnError) {\n    out += ' if (true) {';\n  }\n  return out;\n}\n", "\nmodule.exports = function generate_uniqueItems(it, $keyword, $ruleType) {\n  var out = ' ';\n  var $lvl = it.level;\n  var $dataLvl = it.dataLevel;\n  var $schema = it.schema[$keyword];\n  var $schemaPath = it.schemaPath + it.util.getProperty($keyword);\n  var $errSchemaPath = it.errSchemaPath + '/' + $keyword;\n  var $breakOnError = !it.opts.allErrors;\n  var $data = 'data' + ($dataLvl || '');\n  var $valid = 'valid' + $lvl;\n  var $isData = it.opts.$data && $schema && $schema.$data,\n    $schemaValue;\n  if ($isData) {\n    out += ' var schema' + ($lvl) + ' = ' + (it.util.getData($schema.$data, $dataLvl, it.dataPathArr)) + '; ';\n    $schemaValue = 'schema' + $lvl;\n  } else {\n    $schemaValue = $schema;\n  }\n  if (($schema || $isData) && it.opts.uniqueItems !== false) {\n    if ($isData) {\n      out += ' var ' + ($valid) + '; if (' + ($schemaValue) + ' === false || ' + ($schemaValue) + ' === undefined) ' + ($valid) + ' = true; else if (typeof ' + ($schemaValue) + ' != \\'boolean\\') ' + ($valid) + ' = false; else { ';\n    }\n    out += ' var i = ' + ($data) + '.length , ' + ($valid) + ' = true , j; if (i > 1) { ';\n    var $itemType = it.schema.items && it.schema.items.type,\n      $typeIsArray = Array.isArray($itemType);\n    if (!$itemType || $itemType == 'object' || $itemType == 'array' || ($typeIsArray && ($itemType.indexOf('object') >= 0 || $itemType.indexOf('array') >= 0))) {\n      out += ' outer: for (;i--;) { for (j = i; j--;) { if (equal(' + ($data) + '[i], ' + ($data) + '[j])) { ' + ($valid) + ' = false; break outer; } } } ';\n    } else {\n      out += ' var itemIndices = {}, item; for (;i--;) { var item = ' + ($data) + '[i]; ';\n      var $method = 'checkDataType' + ($typeIsArray ? 's' : '');\n      out += ' if (' + (it.util[$method]($itemType, 'item', it.opts.strictNumbers, true)) + ') continue; ';\n      if ($typeIsArray) {\n        out += ' if (typeof item == \\'string\\') item = \\'\"\\' + item; ';\n      }\n      out += ' if (typeof itemIndices[item] == \\'number\\') { ' + ($valid) + ' = false; j = itemIndices[item]; break; } itemIndices[item] = i; } ';\n    }\n    out += ' } ';\n    if ($isData) {\n      out += '  }  ';\n    }\n    out += ' if (!' + ($valid) + ') {   ';\n    var $$outStack = $$outStack || [];\n    $$outStack.push(out);\n    out = ''; /* istanbul ignore else */\n    if (it.createErrors !== false) {\n      out += ' { keyword: \\'' + ('uniqueItems') + '\\' , dataPath: (dataPath || \\'\\') + ' + (it.errorPath) + ' , schemaPath: ' + (it.util.toQuotedString($errSchemaPath)) + ' , params: { i: i, j: j } ';\n      if (it.opts.messages !== false) {\n        out += ' , message: \\'should NOT have duplicate items (items ## \\' + j + \\' and \\' + i + \\' are identical)\\' ';\n      }\n      if (it.opts.verbose) {\n        out += ' , schema:  ';\n        if ($isData) {\n          out += 'validate.schema' + ($schemaPath);\n        } else {\n          out += '' + ($schema);\n        }\n        out += '         , parentSchema: validate.schema' + (it.schemaPath) + ' , data: ' + ($data) + ' ';\n      }\n      out += ' } ';\n    } else {\n      out += ' {} ';\n    }\n    var __err = out;\n    out = $$outStack.pop();\n    if (!it.compositeRule && $breakOnError) {\n      /* istanbul ignore if */\n      if (it.async) {\n        out += ' throw new ValidationError([' + (__err) + ']); ';\n      } else {\n        out += ' validate.errors = [' + (__err) + ']; return false; ';\n      }\n    } else {\n      out += ' var err = ' + (__err) + ';  if (vErrors === null) vErrors = [err]; else vErrors.push(err); errors++; ';\n    }\n    out += ' } ';\n    if ($breakOnError) {\n      out += ' else { ';\n    }\n  } else {\n    if ($breakOnError) {\n      out += ' if (true) { ';\n    }\n  }\n  return out;\n}\n", "\n\nvar KEYWORDS = [\n  'multipleOf',\n  'maximum',\n  'exclusiveMaximum',\n  'minimum',\n  'exclusiveMinimum',\n  'maxLength',\n  'minLength',\n  'pattern',\n  'additionalItems',\n  'maxItems',\n  'minItems',\n  'uniqueItems',\n  'maxProperties',\n  'minProperties',\n  'required',\n  'additionalProperties',\n  'enum',\n  'format',\n  'const'\n];\n\nmodule.exports = function (metaSchema, keywordsJsonPointers) {\n  for (var i=0; i<keywordsJsonPointers.length; i++) {\n    metaSchema = JSON.parse(JSON.stringify(metaSchema));\n    var segments = keywordsJsonPointers[i].split('/');\n    var keywords = metaSchema;\n    var j;\n    for (j=1; j<segments.length; j++)\n      keywords = keywords[segments[j]];\n\n    for (j=0; j<KEYWORDS.length; j++) {\n      var key = KEYWORDS[j];\n      var schema = keywords[key];\n      if (schema) {\n        keywords[key] = {\n          anyOf: [\n            schema,\n            { $ref: 'https://raw.githubusercontent.com/ajv-validator/ajv/master/lib/refs/data.json#' }\n          ]\n        };\n      }\n    }\n  }\n\n  return metaSchema;\n};\n", "\n\nvar MissingRefError = require('./error_classes').MissingRef;\n\nmodule.exports = compileAsync;\n\n\n/**\n * Creates validating function for passed schema with asynchronous loading of missing schemas.\n * `loadSchema` option should be a function that accepts schema uri and returns promise that resolves with the schema.\n * @this  Ajv\n * @param {Object}   schema schema object\n * @param {Boolean}  meta optional true to compile meta-schema; this parameter can be skipped\n * @param {Function} callback an optional node-style callback, it is called with 2 parameters: error (or null) and validating function.\n * @return {Promise} promise that resolves with a validating function.\n */\nfunction compileAsync(schema, meta, callback) {\n  /* eslint no-shadow: 0 */\n  /* global Promise */\n  /* jshint validthis: true */\n  var self = this;\n  if (typeof this._opts.loadSchema != 'function')\n    throw new Error('options.loadSchema should be a function');\n\n  if (typeof meta == 'function') {\n    callback = meta;\n    meta = undefined;\n  }\n\n  var p = loadMetaSchemaOf(schema).then(function () {\n    var schemaObj = self._addSchema(schema, undefined, meta);\n    return schemaObj.validate || _compileAsync(schemaObj);\n  });\n\n  if (callback) {\n    p.then(\n      function(v) { callback(null, v); },\n      callback\n    );\n  }\n\n  return p;\n\n\n  function loadMetaSchemaOf(sch) {\n    var $schema = sch.$schema;\n    return $schema && !self.getSchema($schema)\n            ? compileAsync.call(self, { $ref: $schema }, true)\n            : Promise.resolve();\n  }\n\n\n  function _compileAsync(schemaObj) {\n    try { return self._compile(schemaObj); }\n    catch(e) {\n      if (e instanceof MissingRefError) return loadMissingSchema(e);\n      throw e;\n    }\n\n\n    function loadMissingSchema(e) {\n      var ref = e.missingSchema;\n      if (added(ref)) throw new Error('Schema ' + ref + ' is loaded but ' + e.missingRef + ' cannot be resolved');\n\n      var schemaPromise = self._loadingSchemas[ref];\n      if (!schemaPromise) {\n        schemaPromise = self._loadingSchemas[ref] = self._opts.loadSchema(ref);\n        schemaPromise.then(removePromise, removePromise);\n      }\n\n      return schemaPromise.then(function (sch) {\n        if (!added(ref)) {\n          return loadMetaSchemaOf(sch).then(function () {\n            if (!added(ref)) self.addSchema(sch, ref, undefined, meta);\n          });\n        }\n      }).then(function() {\n        return _compileAsync(schemaObj);\n      });\n\n      function removePromise() {\n        delete self._loadingSchemas[ref];\n      }\n\n      function added(ref) {\n        return self._refs[ref] || self._schemas[ref];\n      }\n    }\n  }\n}\n", "\n\nvar IDENTIFIER = /^[a-z_$][a-z0-9_$-]*$/i;\nvar customRuleCode = require('./dotjs/custom');\nvar definitionSchema = require('./definition_schema');\n\nmodule.exports = {\n  add: addKeyword,\n  get: getKeyword,\n  remove: removeKeyword,\n  validate: validateKeyword\n};\n\n\n/**\n * Define custom keyword\n * @this  Ajv\n * @param {String} keyword custom keyword, should be unique (including different from all standard, custom and macro keywords).\n * @param {Object} definition keyword definition object with properties `type` (type(s) which the keyword applies to), `validate` or `compile`.\n * @return {Ajv} this for method chaining\n */\nfunction addKeyword(keyword, definition) {\n  /* jshint validthis: true */\n  /* eslint no-shadow: 0 */\n  var RULES = this.RULES;\n  if (RULES.keywords[keyword])\n    throw new Error('Keyword ' + keyword + ' is already defined');\n\n  if (!IDENTIFIER.test(keyword))\n    throw new Error('Keyword ' + keyword + ' is not a valid identifier');\n\n  if (definition) {\n    this.validateKeyword(definition, true);\n\n    var dataType = definition.type;\n    if (Array.isArray(dataType)) {\n      for (var i=0; i<dataType.length; i++)\n        _addRule(keyword, dataType[i], definition);\n    } else {\n      _addRule(keyword, dataType, definition);\n    }\n\n    var metaSchema = definition.metaSchema;\n    if (metaSchema) {\n      if (definition.$data && this._opts.$data) {\n        metaSchema = {\n          anyOf: [\n            metaSchema,\n            { '$ref': 'https://raw.githubusercontent.com/ajv-validator/ajv/master/lib/refs/data.json#' }\n          ]\n        };\n      }\n      definition.validateSchema = this.compile(metaSchema, true);\n    }\n  }\n\n  RULES.keywords[keyword] = RULES.all[keyword] = true;\n\n\n  function _addRule(keyword, dataType, definition) {\n    var ruleGroup;\n    for (var i=0; i<RULES.length; i++) {\n      var rg = RULES[i];\n      if (rg.type == dataType) {\n        ruleGroup = rg;\n        break;\n      }\n    }\n\n    if (!ruleGroup) {\n      ruleGroup = { type: dataType, rules: [] };\n      RULES.push(ruleGroup);\n    }\n\n    var rule = {\n      keyword: keyword,\n      definition: definition,\n      custom: true,\n      code: customRuleCode,\n      implements: definition.implements\n    };\n    ruleGroup.rules.push(rule);\n    RULES.custom[keyword] = rule;\n  }\n\n  return this;\n}\n\n\n/**\n * Get keyword\n * @this  Ajv\n * @param {String} keyword pre-defined or custom keyword.\n * @return {Object|Boolean} custom keyword definition, `true` if it is a predefined keyword, `false` otherwise.\n */\nfunction getKeyword(keyword) {\n  /* jshint validthis: true */\n  var rule = this.RULES.custom[keyword];\n  return rule ? rule.definition : this.RULES.keywords[keyword] || false;\n}\n\n\n/**\n * Remove keyword\n * @this  Ajv\n * @param {String} keyword pre-defined or custom keyword.\n * @return {Ajv} this for method chaining\n */\nfunction removeKeyword(keyword) {\n  /* jshint validthis: true */\n  var RULES = this.RULES;\n  delete RULES.keywords[keyword];\n  delete RULES.all[keyword];\n  delete RULES.custom[keyword];\n  for (var i=0; i<RULES.length; i++) {\n    var rules = RULES[i].rules;\n    for (var j=0; j<rules.length; j++) {\n      if (rules[j].keyword == keyword) {\n        rules.splice(j, 1);\n        break;\n      }\n    }\n  }\n  return this;\n}\n\n\n/**\n * Validate keyword definition\n * @this  Ajv\n * @param {Object} definition keyword definition object.\n * @param {Boolean} throwError true to throw exception if definition is invalid\n * @return {boolean} validation result\n */\nfunction validateKeyword(definition, throwError) {\n  validateKeyword.errors = null;\n  var v = this._validateKeyword = this._validateKeyword\n                                  || this.compile(definitionSchema, true);\n\n  if (v(definition)) return true;\n  validateKeyword.errors = v.errors;\n  if (throwError)\n    throw new Error('custom keyword definition is invalid: '  + this.errorsText(v.errors));\n  else\n    return false;\n}\n", "\nmodule.exports = function generate_custom(it, $keyword, $ruleType) {\n  var out = ' ';\n  var $lvl = it.level;\n  var $dataLvl = it.dataLevel;\n  var $schema = it.schema[$keyword];\n  var $schemaPath = it.schemaPath + it.util.getProperty($keyword);\n  var $errSchemaPath = it.errSchemaPath + '/' + $keyword;\n  var $breakOnError = !it.opts.allErrors;\n  var $errorKeyword;\n  var $data = 'data' + ($dataLvl || '');\n  var $valid = 'valid' + $lvl;\n  var $errs = 'errs__' + $lvl;\n  var $isData = it.opts.$data && $schema && $schema.$data,\n    $schemaValue;\n  if ($isData) {\n    out += ' var schema' + ($lvl) + ' = ' + (it.util.getData($schema.$data, $dataLvl, it.dataPathArr)) + '; ';\n    $schemaValue = 'schema' + $lvl;\n  } else {\n    $schemaValue = $schema;\n  }\n  var $rule = this,\n    $definition = 'definition' + $lvl,\n    $rDef = $rule.definition,\n    $closingBraces = '';\n  var $compile, $inline, $macro, $ruleValidate, $validateCode;\n  if ($isData && $rDef.$data) {\n    $validateCode = 'keywordValidate' + $lvl;\n    var $validateSchema = $rDef.validateSchema;\n    out += ' var ' + ($definition) + ' = RULES.custom[\\'' + ($keyword) + '\\'].definition; var ' + ($validateCode) + ' = ' + ($definition) + '.validate;';\n  } else {\n    $ruleValidate = it.useCustomRule($rule, $schema, it.schema, it);\n    if (!$ruleValidate) return;\n    $schemaValue = 'validate.schema' + $schemaPath;\n    $validateCode = $ruleValidate.code;\n    $compile = $rDef.compile;\n    $inline = $rDef.inline;\n    $macro = $rDef.macro;\n  }\n  var $ruleErrs = $validateCode + '.errors',\n    $i = 'i' + $lvl,\n    $ruleErr = 'ruleErr' + $lvl,\n    $asyncKeyword = $rDef.async;\n  if ($asyncKeyword && !it.async) throw new Error('async keyword in sync schema');\n  if (!($inline || $macro)) {\n    out += '' + ($ruleErrs) + ' = null;';\n  }\n  out += 'var ' + ($errs) + ' = errors;var ' + ($valid) + ';';\n  if ($isData && $rDef.$data) {\n    $closingBraces += '}';\n    out += ' if (' + ($schemaValue) + ' === undefined) { ' + ($valid) + ' = true; } else { ';\n    if ($validateSchema) {\n      $closingBraces += '}';\n      out += ' ' + ($valid) + ' = ' + ($definition) + '.validateSchema(' + ($schemaValue) + '); if (' + ($valid) + ') { ';\n    }\n  }\n  if ($inline) {\n    if ($rDef.statements) {\n      out += ' ' + ($ruleValidate.validate) + ' ';\n    } else {\n      out += ' ' + ($valid) + ' = ' + ($ruleValidate.validate) + '; ';\n    }\n  } else if ($macro) {\n    var $it = it.util.copy(it);\n    var $closingBraces = '';\n    $it.level++;\n    var $nextValid = 'valid' + $it.level;\n    $it.schema = $ruleValidate.validate;\n    $it.schemaPath = '';\n    var $wasComposite = it.compositeRule;\n    it.compositeRule = $it.compositeRule = true;\n    var $code = it.validate($it).replace(/validate\\.schema/g, $validateCode);\n    it.compositeRule = $it.compositeRule = $wasComposite;\n    out += ' ' + ($code);\n  } else {\n    var $$outStack = $$outStack || [];\n    $$outStack.push(out);\n    out = '';\n    out += '  ' + ($validateCode) + '.call( ';\n    if (it.opts.passContext) {\n      out += 'this';\n    } else {\n      out += 'self';\n    }\n    if ($compile || $rDef.schema === false) {\n      out += ' , ' + ($data) + ' ';\n    } else {\n      out += ' , ' + ($schemaValue) + ' , ' + ($data) + ' , validate.schema' + (it.schemaPath) + ' ';\n    }\n    out += ' , (dataPath || \\'\\')';\n    if (it.errorPath != '\"\"') {\n      out += ' + ' + (it.errorPath);\n    }\n    var $parentData = $dataLvl ? 'data' + (($dataLvl - 1) || '') : 'parentData',\n      $parentDataProperty = $dataLvl ? it.dataPathArr[$dataLvl] : 'parentDataProperty';\n    out += ' , ' + ($parentData) + ' , ' + ($parentDataProperty) + ' , rootData )  ';\n    var def_callRuleValidate = out;\n    out = $$outStack.pop();\n    if ($rDef.errors === false) {\n      out += ' ' + ($valid) + ' = ';\n      if ($asyncKeyword) {\n        out += 'await ';\n      }\n      out += '' + (def_callRuleValidate) + '; ';\n    } else {\n      if ($asyncKeyword) {\n        $ruleErrs = 'customErrors' + $lvl;\n        out += ' var ' + ($ruleErrs) + ' = null; try { ' + ($valid) + ' = await ' + (def_callRuleValidate) + '; } catch (e) { ' + ($valid) + ' = false; if (e instanceof ValidationError) ' + ($ruleErrs) + ' = e.errors; else throw e; } ';\n      } else {\n        out += ' ' + ($ruleErrs) + ' = null; ' + ($valid) + ' = ' + (def_callRuleValidate) + '; ';\n      }\n    }\n  }\n  if ($rDef.modifying) {\n    out += ' if (' + ($parentData) + ') ' + ($data) + ' = ' + ($parentData) + '[' + ($parentDataProperty) + '];';\n  }\n  out += '' + ($closingBraces);\n  if ($rDef.valid) {\n    if ($breakOnError) {\n      out += ' if (true) { ';\n    }\n  } else {\n    out += ' if ( ';\n    if ($rDef.valid === undefined) {\n      out += ' !';\n      if ($macro) {\n        out += '' + ($nextValid);\n      } else {\n        out += '' + ($valid);\n      }\n    } else {\n      out += ' ' + (!$rDef.valid) + ' ';\n    }\n    out += ') { ';\n    $errorKeyword = $rule.keyword;\n    var $$outStack = $$outStack || [];\n    $$outStack.push(out);\n    out = '';\n    var $$outStack = $$outStack || [];\n    $$outStack.push(out);\n    out = ''; /* istanbul ignore else */\n    if (it.createErrors !== false) {\n      out += ' { keyword: \\'' + ($errorKeyword || 'custom') + '\\' , dataPath: (dataPath || \\'\\') + ' + (it.errorPath) + ' , schemaPath: ' + (it.util.toQuotedString($errSchemaPath)) + ' , params: { keyword: \\'' + ($rule.keyword) + '\\' } ';\n      if (it.opts.messages !== false) {\n        out += ' , message: \\'should pass \"' + ($rule.keyword) + '\" keyword validation\\' ';\n      }\n      if (it.opts.verbose) {\n        out += ' , schema: validate.schema' + ($schemaPath) + ' , parentSchema: validate.schema' + (it.schemaPath) + ' , data: ' + ($data) + ' ';\n      }\n      out += ' } ';\n    } else {\n      out += ' {} ';\n    }\n    var __err = out;\n    out = $$outStack.pop();\n    if (!it.compositeRule && $breakOnError) {\n      /* istanbul ignore if */\n      if (it.async) {\n        out += ' throw new ValidationError([' + (__err) + ']); ';\n      } else {\n        out += ' validate.errors = [' + (__err) + ']; return false; ';\n      }\n    } else {\n      out += ' var err = ' + (__err) + ';  if (vErrors === null) vErrors = [err]; else vErrors.push(err); errors++; ';\n    }\n    var def_customError = out;\n    out = $$outStack.pop();\n    if ($inline) {\n      if ($rDef.errors) {\n        if ($rDef.errors != 'full') {\n          out += '  for (var ' + ($i) + '=' + ($errs) + '; ' + ($i) + '<errors; ' + ($i) + '++) { var ' + ($ruleErr) + ' = vErrors[' + ($i) + ']; if (' + ($ruleErr) + '.dataPath === undefined) ' + ($ruleErr) + '.dataPath = (dataPath || \\'\\') + ' + (it.errorPath) + '; if (' + ($ruleErr) + '.schemaPath === undefined) { ' + ($ruleErr) + '.schemaPath = \"' + ($errSchemaPath) + '\"; } ';\n          if (it.opts.verbose) {\n            out += ' ' + ($ruleErr) + '.schema = ' + ($schemaValue) + '; ' + ($ruleErr) + '.data = ' + ($data) + '; ';\n          }\n          out += ' } ';\n        }\n      } else {\n        if ($rDef.errors === false) {\n          out += ' ' + (def_customError) + ' ';\n        } else {\n          out += ' if (' + ($errs) + ' == errors) { ' + (def_customError) + ' } else {  for (var ' + ($i) + '=' + ($errs) + '; ' + ($i) + '<errors; ' + ($i) + '++) { var ' + ($ruleErr) + ' = vErrors[' + ($i) + ']; if (' + ($ruleErr) + '.dataPath === undefined) ' + ($ruleErr) + '.dataPath = (dataPath || \\'\\') + ' + (it.errorPath) + '; if (' + ($ruleErr) + '.schemaPath === undefined) { ' + ($ruleErr) + '.schemaPath = \"' + ($errSchemaPath) + '\"; } ';\n          if (it.opts.verbose) {\n            out += ' ' + ($ruleErr) + '.schema = ' + ($schemaValue) + '; ' + ($ruleErr) + '.data = ' + ($data) + '; ';\n          }\n          out += ' } } ';\n        }\n      }\n    } else if ($macro) {\n      out += '   var err =   '; /* istanbul ignore else */\n      if (it.createErrors !== false) {\n        out += ' { keyword: \\'' + ($errorKeyword || 'custom') + '\\' , dataPath: (dataPath || \\'\\') + ' + (it.errorPath) + ' , schemaPath: ' + (it.util.toQuotedString($errSchemaPath)) + ' , params: { keyword: \\'' + ($rule.keyword) + '\\' } ';\n        if (it.opts.messages !== false) {\n          out += ' , message: \\'should pass \"' + ($rule.keyword) + '\" keyword validation\\' ';\n        }\n        if (it.opts.verbose) {\n          out += ' , schema: validate.schema' + ($schemaPath) + ' , parentSchema: validate.schema' + (it.schemaPath) + ' , data: ' + ($data) + ' ';\n        }\n        out += ' } ';\n      } else {\n        out += ' {} ';\n      }\n      out += ';  if (vErrors === null) vErrors = [err]; else vErrors.push(err); errors++; ';\n      if (!it.compositeRule && $breakOnError) {\n        /* istanbul ignore if */\n        if (it.async) {\n          out += ' throw new ValidationError(vErrors); ';\n        } else {\n          out += ' validate.errors = vErrors; return false; ';\n        }\n      }\n    } else {\n      if ($rDef.errors === false) {\n        out += ' ' + (def_customError) + ' ';\n      } else {\n        out += ' if (Array.isArray(' + ($ruleErrs) + ')) { if (vErrors === null) vErrors = ' + ($ruleErrs) + '; else vErrors = vErrors.concat(' + ($ruleErrs) + '); errors = vErrors.length;  for (var ' + ($i) + '=' + ($errs) + '; ' + ($i) + '<errors; ' + ($i) + '++) { var ' + ($ruleErr) + ' = vErrors[' + ($i) + ']; if (' + ($ruleErr) + '.dataPath === undefined) ' + ($ruleErr) + '.dataPath = (dataPath || \\'\\') + ' + (it.errorPath) + ';  ' + ($ruleErr) + '.schemaPath = \"' + ($errSchemaPath) + '\";  ';\n        if (it.opts.verbose) {\n          out += ' ' + ($ruleErr) + '.schema = ' + ($schemaValue) + '; ' + ($ruleErr) + '.data = ' + ($data) + '; ';\n        }\n        out += ' } } else { ' + (def_customError) + ' } ';\n      }\n    }\n    out += ' } ';\n    if ($breakOnError) {\n      out += ' else { ';\n    }\n  }\n  return out;\n}\n", "\n\nvar metaSchema = require('./refs/json-schema-draft-07.json');\n\nmodule.exports = {\n  $id: 'https://github.com/ajv-validator/ajv/blob/master/lib/definition_schema.js',\n  definitions: {\n    simpleTypes: metaSchema.definitions.simpleTypes\n  },\n  type: 'object',\n  dependencies: {\n    schema: ['validate'],\n    $data: ['validate'],\n    statements: ['inline'],\n    valid: {not: {required: ['macro']}}\n  },\n  properties: {\n    type: metaSchema.properties.type,\n    schema: {type: 'boolean'},\n    statements: {type: 'boolean'},\n    dependencies: {\n      type: 'array',\n      items: {type: 'string'}\n    },\n    metaSchema: {type: 'object'},\n    modifying: {type: 'boolean'},\n    valid: {type: 'boolean'},\n    $data: {type: 'boolean'},\n    async: {type: 'boolean'},\n    errors: {\n      anyOf: [\n        {type: 'boolean'},\n        {const: 'full'}\n      ]\n    }\n  }\n};\n", "module.exports = {\n    \"$schema\": \"http://json-schema.org/draft-07/schema#\",\n    \"$id\": \"http://json-schema.org/draft-07/schema#\",\n    \"title\": \"Core schema meta-schema\",\n    \"definitions\": {\n        \"schemaArray\": {\n            \"type\": \"array\",\n            \"minItems\": 1,\n            \"items\": { \"$ref\": \"#\" }\n        },\n        \"nonNegativeInteger\": {\n            \"type\": \"integer\",\n            \"minimum\": 0\n        },\n        \"nonNegativeIntegerDefault0\": {\n            \"allOf\": [\n                { \"$ref\": \"#/definitions/nonNegativeInteger\" },\n                { \"default\": 0 }\n            ]\n        },\n        \"simpleTypes\": {\n            \"enum\": [\n                \"array\",\n                \"boolean\",\n                \"integer\",\n                \"null\",\n                \"number\",\n                \"object\",\n                \"string\"\n            ]\n        },\n        \"stringArray\": {\n            \"type\": \"array\",\n            \"items\": { \"type\": \"string\" },\n            \"uniqueItems\": true,\n            \"default\": []\n        }\n    },\n    \"type\": [\"object\", \"boolean\"],\n    \"properties\": {\n        \"$id\": {\n            \"type\": \"string\",\n            \"format\": \"uri-reference\"\n        },\n        \"$schema\": {\n            \"type\": \"string\",\n            \"format\": \"uri\"\n        },\n        \"$ref\": {\n            \"type\": \"string\",\n            \"format\": \"uri-reference\"\n        },\n        \"$comment\": {\n            \"type\": \"string\"\n        },\n        \"title\": {\n            \"type\": \"string\"\n        },\n        \"description\": {\n            \"type\": \"string\"\n        },\n        \"default\": true,\n        \"readOnly\": {\n            \"type\": \"boolean\",\n            \"default\": false\n        },\n        \"examples\": {\n            \"type\": \"array\",\n            \"items\": true\n        },\n        \"multipleOf\": {\n            \"type\": \"number\",\n            \"exclusiveMinimum\": 0\n        },\n        \"maximum\": {\n            \"type\": \"number\"\n        },\n        \"exclusiveMaximum\": {\n            \"type\": \"number\"\n        },\n        \"minimum\": {\n            \"type\": \"number\"\n        },\n        \"exclusiveMinimum\": {\n            \"type\": \"number\"\n        },\n        \"maxLength\": { \"$ref\": \"#/definitions/nonNegativeInteger\" },\n        \"minLength\": { \"$ref\": \"#/definitions/nonNegativeIntegerDefault0\" },\n        \"pattern\": {\n            \"type\": \"string\",\n            \"format\": \"regex\"\n        },\n        \"additionalItems\": { \"$ref\": \"#\" },\n        \"items\": {\n            \"anyOf\": [\n                { \"$ref\": \"#\" },\n                { \"$ref\": \"#/definitions/schemaArray\" }\n            ],\n            \"default\": true\n        },\n        \"maxItems\": { \"$ref\": \"#/definitions/nonNegativeInteger\" },\n        \"minItems\": { \"$ref\": \"#/definitions/nonNegativeIntegerDefault0\" },\n        \"uniqueItems\": {\n            \"type\": \"boolean\",\n            \"default\": false\n        },\n        \"contains\": { \"$ref\": \"#\" },\n        \"maxProperties\": { \"$ref\": \"#/definitions/nonNegativeInteger\" },\n        \"minProperties\": { \"$ref\": \"#/definitions/nonNegativeIntegerDefault0\" },\n        \"required\": { \"$ref\": \"#/definitions/stringArray\" },\n        \"additionalProperties\": { \"$ref\": \"#\" },\n        \"definitions\": {\n            \"type\": \"object\",\n            \"additionalProperties\": { \"$ref\": \"#\" },\n            \"default\": {}\n        },\n        \"properties\": {\n            \"type\": \"object\",\n            \"additionalProperties\": { \"$ref\": \"#\" },\n            \"default\": {}\n        },\n        \"patternProperties\": {\n            \"type\": \"object\",\n            \"additionalProperties\": { \"$ref\": \"#\" },\n            \"propertyNames\": { \"format\": \"regex\" },\n            \"default\": {}\n        },\n        \"dependencies\": {\n            \"type\": \"object\",\n            \"additionalProperties\": {\n                \"anyOf\": [\n                    { \"$ref\": \"#\" },\n                    { \"$ref\": \"#/definitions/stringArray\" }\n                ]\n            }\n        },\n        \"propertyNames\": { \"$ref\": \"#\" },\n        \"const\": true,\n        \"enum\": {\n            \"type\": \"array\",\n            \"items\": true,\n            \"minItems\": 1,\n            \"uniqueItems\": true\n        },\n        \"type\": {\n            \"anyOf\": [\n                { \"$ref\": \"#/definitions/simpleTypes\" },\n                {\n                    \"type\": \"array\",\n                    \"items\": { \"$ref\": \"#/definitions/simpleTypes\" },\n                    \"minItems\": 1,\n                    \"uniqueItems\": true\n                }\n            ]\n        },\n        \"format\": { \"type\": \"string\" },\n        \"contentMediaType\": { \"type\": \"string\" },\n        \"contentEncoding\": { \"type\": \"string\" },\n        \"if\": {\"$ref\": \"#\"},\n        \"then\": {\"$ref\": \"#\"},\n        \"else\": {\"$ref\": \"#\"},\n        \"allOf\": { \"$ref\": \"#/definitions/schemaArray\" },\n        \"anyOf\": { \"$ref\": \"#/definitions/schemaArray\" },\n        \"oneOf\": { \"$ref\": \"#/definitions/schemaArray\" },\n        \"not\": { \"$ref\": \"#\" }\n    },\n    \"default\": true\n}\n", "module.exports = {\n    \"$schema\": \"http://json-schema.org/draft-07/schema#\",\n    \"$id\": \"https://raw.githubusercontent.com/ajv-validator/ajv/master/lib/refs/data.json#\",\n    \"description\": \"Meta-schema for $data reference (JSON Schema extension proposal)\",\n    \"type\": \"object\",\n    \"required\": [ \"$data\" ],\n    \"properties\": {\n        \"$data\": {\n            \"type\": \"string\",\n            \"anyOf\": [\n                { \"format\": \"relative-json-pointer\" }, \n                { \"format\": \"json-pointer\" }\n            ]\n        }\n    },\n    \"additionalProperties\": false\n}\n"]}