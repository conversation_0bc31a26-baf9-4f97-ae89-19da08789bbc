<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ECL检测系统流程图</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', <PERSON><PERSON>, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .container {
            background: white;
            border-radius: 20px;
            padding: 40px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            width: 1500px;
            height: 850px;
            position: relative;
            overflow: hidden;
        }

        .container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            border: 3px solid #2196F3;
            border-radius: 20px;
            pointer-events: none;
        }

        .flow-container {
            position: relative;
            width: 100%;
            height: 100%;
        }

        .flow-line {
            position: absolute;
            top: 50%;
            left: 50px;
            right: 50px;
            height: 4px;
            background: linear-gradient(90deg, #333 0%, #2196F3 100%);
            border-radius: 2px;
            z-index: 1;
        }

        .flow-point {
            position: absolute;
            width: 24px;
            height: 24px;
            background: #2196F3;
            border-radius: 50%;
            border: 4px solid white;
            box-shadow: 0 4px 12px rgba(33, 150, 243, 0.4);
            z-index: 3;
            cursor: pointer;
            transition: transform 0.3s ease;
        }

        .flow-point:hover {
            transform: scale(1.2);
        }

        .flow-point.start {
            left: 50px;
            top: calc(50% - 12px);
            background: #333;
        }

        .flow-point.step1 {
            left: 280px;
            top: calc(50% - 12px);
        }

        .flow-point.step2 {
            left: 510px;
            top: calc(50% - 12px);
        }

        .flow-point.step3 {
            left: 740px;
            top: calc(50% - 12px);
        }

        .flow-point.end {
            right: 50px;
            top: calc(50% - 12px);
        }

        .step-description {
            position: absolute;
            background: white;
            padding: 20px;
            border-radius: 12px;
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
            border-left: 4px solid #2196F3;
            max-width: 200px;
            font-size: 14px;
            line-height: 1.5;
            color: #333;
            z-index: 2;
        }

        .step-description.top {
            bottom: calc(50% + 40px);
        }

        .step-description.bottom {
            top: calc(50% + 40px);
        }

        .step-description h4 {
            margin: 0 0 10px 0;
            color: #2196F3;
            font-size: 16px;
            font-weight: bold;
        }

        .step-description p {
            margin: 0;
            font-size: 13px;
            line-height: 1.4;
        }

        .desc1 {
            left: 50px;
            top: calc(50% + 40px);
        }

        .desc2 {
            left: 180px;
            bottom: calc(50% + 40px);
        }

        .desc3 {
            left: 410px;
            top: calc(50% + 40px);
        }

        .desc4 {
            left: 640px;
            bottom: calc(50% + 40px);
        }

        .desc5 {
            right: 50px;
            top: calc(50% + 40px);
        }

        .main-title {
            position: absolute;
            top: 80px;
            right: 150px;
            background: linear-gradient(135deg, #2196F3, #1976D2);
            color: white;
            padding: 30px 40px;
            border-radius: 50px;
            font-size: 24px;
            font-weight: bold;
            text-align: center;
            box-shadow: 0 10px 30px rgba(33, 150, 243, 0.3);
            z-index: 4;
        }

        .connection-line {
            position: absolute;
            background: #2196F3;
            z-index: 1;
        }

        .line1 {
            left: 74px;
            top: calc(50% + 12px);
            width: 2px;
            height: 40px;
        }

        .line2 {
            left: 304px;
            bottom: calc(50% + 12px);
            width: 2px;
            height: 40px;
        }

        .line3 {
            left: 534px;
            top: calc(50% + 12px);
            width: 2px;
            height: 40px;
        }

        .line4 {
            left: 764px;
            bottom: calc(50% + 12px);
            width: 2px;
            height: 40px;
        }

        .line5 {
            right: 74px;
            top: calc(50% + 12px);
            width: 2px;
            height: 40px;
        }

        .tech-stack {
            position: absolute;
            bottom: 30px;
            left: 40px;
            right: 40px;
            background: rgba(248, 249, 250, 0.95);
            padding: 20px;
            border-radius: 12px;
            border-top: 3px solid #2196F3;
            backdrop-filter: blur(10px);
        }

        .tech-title {
            font-size: 18px;
            font-weight: bold;
            color: #2196F3;
            margin-bottom: 15px;
            text-align: center;
        }

        .tech-items {
            display: flex;
            justify-content: space-between;
            gap: 15px;
        }

        .tech-item {
            flex: 1;
            background: white;
            padding: 15px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            text-align: center;
        }

        .tech-item .file-name {
            color: #2196F3;
            font-weight: bold;
            font-size: 14px;
            margin-bottom: 8px;
        }

        .tech-item .file-desc {
            color: #666;
            font-size: 12px;
            line-height: 1.3;
        }

        @keyframes pulse {
            0% { box-shadow: 0 4px 12px rgba(33, 150, 243, 0.4); }
            50% { box-shadow: 0 4px 20px rgba(33, 150, 243, 0.8); }
            100% { box-shadow: 0 4px 12px rgba(33, 150, 243, 0.4); }
        }

        .flow-point.active {
            animation: pulse 2s infinite;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="flow-container">
            <!-- 主标题 -->
            <div class="main-title">
                ECL检测流程的<br>全自动化
            </div>

            <!-- 流程主线 -->
            <div class="flow-line"></div>

            <!-- 流程节点 -->
            <div class="flow-point start" data-step="0"></div>
            <div class="flow-point step1" data-step="1"></div>
            <div class="flow-point step2" data-step="2"></div>
            <div class="flow-point step3" data-step="3"></div>
            <div class="flow-point end" data-step="4"></div>

            <!-- 连接线 -->
            <div class="connection-line line1"></div>
            <div class="connection-line line2"></div>
            <div class="connection-line line3"></div>
            <div class="connection-line line4"></div>
            <div class="connection-line line5"></div>

            <!-- 步骤描述 -->
            <div class="step-description desc1">
                <h4>用户启动"开始自动检测"按钮</h4>
                <p>将自动开启视频录制，通过pages/index/index.js中的startRecording()方法启动录制流程</p>
            </div>

            <div class="step-description desc2">
                <h4>自动发送视频加载励磁电压的命令</h4>
                <p>通过updateParameter()和connectToDevice()方法自动配置设备参数，建立稳定的视频流连接</p>
            </div>

            <div class="step-description desc3">
                <h4>待电化学发光结束后，系统将自动停止录制</h4>
                <p>调用stopRecording()方法结束录制，并将录制的视频上传到云服务器系统分析处理模块进行分析处理</p>
            </div>

            <div class="step-description desc4">
                <h4>云服务器系统分析处理模块分析结束之后</h4>
                <p>通过analyzeVideo云函数使用FFmpeg提取帧，Jimp进行图像分析，计算C/T区域灰度值，将分析结果返回给数据显示模块</p>
            </div>

            <div class="step-description desc5">
                <h4>全自动化学分析结束</h4>
                <p>通过getUserData云函数获取分析结果，在pages/me/me.js中展示数据，支持本地保存和分享功能</p>
            </div>
        </div>

        <!-- 技术栈说明 -->
        <div class="tech-stack">
            <div class="tech-title">核心技术模块实现</div>
            <div class="tech-items">
                <div class="tech-item">
                    <div class="file-name">pages/index</div>
                    <div class="file-desc">前端交互控制<br>视频录制管理<br>设备参数配置</div>
                </div>
                <div class="tech-item">
                    <div class="file-name">analyzeVideo</div>
                    <div class="file-desc">云端视频分析<br>FFmpeg帧提取<br>Jimp图像处理</div>
                </div>
                <div class="tech-item">
                    <div class="file-name">getUserData</div>
                    <div class="file-desc">数据获取服务<br>文件下载管理<br>临时URL生成</div>
                </div>
                <div class="tech-item">
                    <div class="file-name">pages/me</div>
                    <div class="file-desc">用户数据展示<br>本地保存功能<br>分享导出管理</div>
                </div>
                <div class="tech-item">
                    <div class="file-name">deleteUserData</div>
                    <div class="file-desc">数据清理服务<br>存储空间优化<br>批量文件删除</div>
                </div>
            </div>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const flowPoints = document.querySelectorAll('.flow-point');
            const descriptions = document.querySelectorAll('.step-description');
            
            // 初始化第一个点为激活状态
            flowPoints[0].classList.add('active');
            
            // 自动播放流程动画
            let currentStep = 0;
            const totalSteps = flowPoints.length;
            
            function animateFlow() {
                // 移除所有激活状态
                flowPoints.forEach(point => point.classList.remove('active'));
                
                // 激活当前步骤
                flowPoints[currentStep].classList.add('active');
                
                // 高亮对应描述
                descriptions.forEach((desc, index) => {
                    if (index === currentStep) {
                        desc.style.background = '#e3f2fd';
                        desc.style.transform = 'scale(1.05)';
                    } else {
                        desc.style.background = 'white';
                        desc.style.transform = 'scale(1)';
                    }
                });
                
                // 移动到下一步
                currentStep = (currentStep + 1) % totalSteps;
            }
            
            // 每3秒切换一次
            setInterval(animateFlow, 3000);
            
            // 点击节点手动切换
            flowPoints.forEach((point, index) => {
                point.addEventListener('click', function() {
                    currentStep = index;
                    animateFlow();
                });
            });
            
            // 悬停效果
            descriptions.forEach(desc => {
                desc.addEventListener('mouseenter', function() {
                    this.style.transform = 'scale(1.05)';
                    this.style.zIndex = '10';
                });
                
                desc.addEventListener('mouseleave', function() {
                    if (!this.style.background.includes('e3f2fd')) {
                        this.style.transform = 'scale(1)';
                    }
                    this.style.zIndex = '2';
                });
            });
        });
    </script>
</body>
</html>
