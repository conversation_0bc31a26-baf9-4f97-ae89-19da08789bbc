<!-- 个人页面 -->
<!-- 自定义导航栏 - Skyline渲染引擎，动态适配状态栏高度 -->
<view class="custom-navbar" style="height: {{totalNavHeight}}px; padding-top: {{safeAreaTop}}px;">
  <view class="navbar-title">我</view>
</view>

<view class="container" style="padding-top: {{totalNavHeight}}px;">
  <!-- 用户信息部分 -->
  <view class="user-section">
    <view class="userinfo">
      <button class="avatar-wrapper" open-type="chooseAvatar" bindchooseavatar="onChooseAvatar">
        <image class="userinfo-avatar" src="{{isLoggedIn ? userInfo.avatarUrl : '../../assets/img/icon_normal.png'}}" mode="cover"></image>
      </button>
      <input type="nickname" 
             class="userinfo-nickname" 
             value="{{userInfo.nickName}}" 
             bindchange="onNicknameChange" 
             placeholder="微信用户"
             placeholder-style="color: rgba(255, 255, 255, 0.8);"/>
    </view>

    <!-- 登录按钮 -->
    <button class="login-btn" 
            wx:if="{{!isLoggedIn}}" 
            bindtap="getUserProfile">点击登录</button>
            
    <!-- 头像昵称填写按钮 -->
    <button class="avatar-btn"
            wx:if="{{isLoggedIn && !hasUserInfo}}"
            open-type="chooseAvatar"
            bindchooseavatar="onChooseAvatar">设置头像和昵称</button>
            
    <!-- 退出登录按钮 -->
    <button class="logout-btn" wx:if="{{isLoggedIn}}" bindtap="logout">退出登录</button>
  </view>

  <view class="space-line"></view>

  <!-- 功能列表部分 -->
  <view class="menu-list">
    <view class="weui-cells weui-cells_after-title">
      <navigator url="{{item.url}}" class="weui-cell weui-cell_access" hover-class="weui-cell_active" wx:for="{{meList}}" wx:key="text" bindtap="handleMeListItemClick" data-item="{{item}}">
        <view class="weui-cell__hd">
          <image src="{{item.icon}}" class="cell-image" style="margin-right: 5px;vertical-align: middle;width:20px; height: 20px;"></image>
        </view>
        <view class="weui-cell__bd" style="padding-top:10rpx;">{{item.text}}</view>
        <view class="badge" wx:if="{{showBadge}}">1</view>
        <view class="weui-cell__ft weui-cell__ft_in-access"></view>
      </navigator>
      
      <!-- 数据按钮 -->
      <view class="weui-cell weui-cell_access add-device-button" bindtap="showMyData">
        <view class="weui-cell__hd">
          <image src="../../assets/img/iconfont-tuihuo.png" class="cell-image" style="margin-right: 5px;vertical-align: middle;width:20px; height: 20px;"></image>
        </view>
        <view class="weui-cell__bd" style="padding-top:10rpx;">数据</view>
        <view class="weui-cell__ft weui-cell__ft_in-access"></view>
      </view>

      <!-- 手机号绑定/更换按钮 -->
      <!--
      <view class="weui-cell weui-cell_access phone-bind-cell" wx:if="{{isLoggedIn && showBindPhoneButton}}">
        <button wx:if="{{!isPhoneBound}}"
                class="phone-bind-btn bind-phone"
                bindtap="handlePhoneBinding">绑定手机号</button>
        <button wx:else
                class="phone-bind-btn change-phone"
                bindtap="handlePhoneBinding">更换手机号</button>
      </view>
      -->
    </view>
  </view>
</view>

<!-- 显示数据模式 -->
<view wx:if="{{isShowingData}}" class="data-display" style="top: {{totalNavHeight}}px; height: calc(100vh - {{totalNavHeight}}px);">
  <view class="data-header">
    <text class="header-title">{{isMultiSelectMode ? '选择数据' : '数据'}}</text>

    <!-- 非多选模式：显示搜索框 -->
    <view class="search-container" wx:if="{{!isMultiSelectMode}}">
      <!-- <image class="search-icon" src="../../assets/img/search.png" mode="aspectFit"></image> --> 
      <input class="search-input {{isSearchFocused ? 'search-input-focused' : ''}}" 
             placeholder="搜索名称/ID/T/C值/数据组..." 
             placeholder-style="color:#b0bec5;" 
             value="{{searchQuery}}" 
             bindinput="handleSearchInput"
             bindfocus="handleFocus" 
             bindblur="handleBlur" />
    </view>

    <!-- 多选模式：显示图表按钮容器 -->
    <view class="cancel-btn-container" wx:if="{{isMultiSelectMode}}">
      <view class="generate-btn"
            bindtap="generateChart"
            hover-class="none">
        生成图表
      </view>
    </view>

    <!-- 原来的取消按钮逻辑注释掉或移除 -->
    <!-- 
    <view class="cancel-btn"
          wx:if="{{isMultiSelectMode}}"
          bindtap="exitMultiSelectMode"
          hover-class="none">
      取消
    </view>
     -->
  </view>
  
  <!-- 数据列表 -->
  <scroll-view scroll-y="true" class="data-list {{modeTransition ? 'mode-transition' : ''}} {{animateItems ? 'animateItems' : ''}}">
    <view wx:if="{{filteredStoredData.length > 0}}">
      <view wx:for="{{filteredStoredData}}" 
            wx:key="_id"
            class="data-item {{isMultiSelectMode ? 'multi-select-mode' : ''}} {{item.selected ? 'selected-item' : ''}}"
            bindtap="{{!isMultiSelectMode ? 'showParamDetail' : ''}}"
            data-index="{{index}}"
            data-item="{{item}}"
            data-title="参数详情"
            data-time="{{item.displayTime || (item.updateTime || item.analysisTime)}}"
            data-group="{{item.originalIndex + 1}}">
        <!-- 选择框 -->
        <view class="my-checkbox {{item.selected ? 'checked' : ''}}" 
              wx:if="{{isMultiSelectMode}}"
              catchtap="selectData"
              data-index="{{index}}"></view>
        
        <!-- 数据内容 -->
        <view class="data-content" 
              catchtap="{{isMultiSelectMode ? 'selectData' : ''}}"
              data-index="{{index}}">
          <!-- 主要信息 -->
          <view class="data-header">
            <view class="data-main">
              <text class="tc-value">T/C: {{item.tcValue}}</text>
              <view class="{{isMultiSelectMode ? 'analysis-type-container-multi' : 'analysis-type-container-single'}}" catchtap="toggleAnalysisType" data-index="{{index}}">
                <view class="analysis-type {{item.isTemp ? 'temp' : 'formal'}} {{item.isFlipping ? 'flipping' : ''}}">
                  <view class="type-front">正式</view>
                  <view class="type-back">临时</view>
                </view>
              </view>
              <view style="clear:both;"></view>
              <!-- 将时间移到新行，100%宽度 -->
              <text class="time-row">{{item.displayTime || (item.updateTime || item.analysisTime)}}</text>
            </view>
          </view>
          
          <!-- 详细信息结构优化 -->
          <view class="data-details">
            <view class="detail-row">
              <view class="detail-left">
                <!-- C区块 -->
                <view class="area-block c-area">
                  <text class="detail-label">C区:</text>
                  <text class="detail-value">{{item.cAreaValue}}</text>
                </view>
                <!-- T区块 -->
                <view class="area-block t-area">
                  <text class="detail-label">T区:</text>
                  <text class="detail-value">{{item.tAreaValue}}</text>
                </view>
              </view>
              
              <!-- 单选模式：视频/图片按钮 -->
              <block wx:if="{{!isMultiSelectMode}}">
                <button class="action-btn primary"
                        catch:tap="viewMedia"
                        data-purpose="viewMedia"
                        data-index="{{index}}">
                  查看图片
                </button>
              </block>
            </view>
            
            <!-- 多选模式：按钮区域移到单独的行 -->
            <view class="btn-container" wx:if="{{isMultiSelectMode}}" catchtap="preventBubble">
              <view class="media-btn video {{item.videoSelected ? 'selected' : ''}}"
                    bindtap="toggleVideoSelect"
                    data-index="{{index}}">
                <view class="btn-content" style="display: flex; align-items: center; justify-content: center; height: 70rpx; line-height: 70rpx;">
                  <image src="../../assets/img/video-icon.png" class="btn-icon" mode="aspectFit" style="margin-right: 8rpx;"></image>
                  <view class="btn-text" style="line-height: 70rpx; height: 70rpx; display: flex; align-items: center; justify-content: center; text-align: center;">视频</view>
                </view>
              </view>
              <view class="media-btn image {{item.imageSelected ? 'selected' : ''}}"
                    bindtap="toggleImageSelect"
                    data-index="{{index}}">
                <view class="btn-content" style="display: flex; align-items: center; justify-content: center; height: 70rpx; line-height: 70rpx;">
                  <image src="../../assets/img/image-icon.png" class="btn-icon" mode="aspectFit" style="margin-right: 8rpx;"></image>
                  <view class="btn-text" style="line-height: 70rpx; height: 70rpx; display: flex; align-items: center; justify-content: center; text-align: center;">图片和数值</view>
                </view>
              </view>
            </view>
            
            <view class="param-row" wx:if="{{item.parameters}}">
              <text class="param-tag">参数</text>
              <text class="param-count">已设置{{item.parameters ? Object.keys(item.parameters).length : 0}}项</text>
            </view>
          </view>
        </view>
      </view>
    </view>
    <view wx:else class="no-data">
      <text>没有找到相关数据</text>
    </view>
    
    <!-- 添加底部空间，始终存在但只在多选模式下占据实际高度 -->
    <view class="bottom-space-holder {{isMultiSelectMode ? 'active' : ''}}"></view>
  </scroll-view>

  <!-- 参数详情卡片 - 优化结构 -->
  <view class="param-detail-mask {{showParamDetail ? 'show' : ''}} {{paramDetailMaskHiding ? 'hiding' : ''}}" bindtap="hideParamDetail">
    <view class="param-detail-card {{paramDetailHiding ? 'hiding' : ''}}" style="animation: {{paramDetailCardAnimation || 'none'}};" catchtap="preventBubble">
      <view class="param-detail-header">
        <view class="param-detail-title">{{currentParamTitle || '参数详情'}}</view>
        <view class="param-detail-time">{{currentParamTime || ''}}</view>
      </view>
      <scroll-view scroll-y class="param-detail-content" scroll-top="{{paramScrollTop}}">
        <!-- 浓度设置 -->
        <view class="param-group">
          <view class="param-group-title">[LgE] 浓度设置</view>
          <view class="param-detail-item">
            <view class="param-detail-label"><text class="icon-dot"></text>浓度</view>
            <view class="param-detail-value">{{currentParamData.concentration || '未设置'}} ng mL⁻¹</view>
          </view>
          <view class="param-detail-item" wx:if="{{currentParamData.tcValue}}">
            <view class="param-detail-label"><text class="icon-dot"></text>T/C值</view>
            <view class="param-detail-value">{{currentParamData.tcValue}}</view>
          </view>
        </view>

        <!-- 基础图像参数 -->
        <view class="param-group">
          <view class="param-group-title">基础图像参数</view>
          <view class="param-detail-item" wx:if="{{currentParamData.brightness || currentParamData.parameters.brightness || (currentParamData.videoParameters && currentParamData.videoParameters.brightness)}}">
            <view class="param-detail-label"><text class="icon-dot"></text>亮度</view>
            <view class="param-detail-value">{{currentParamData.brightness || (currentParamData.videoParameters ? currentParamData.videoParameters.brightness : currentParamData.parameters.brightness)}}</view>
          </view>
          <view class="param-detail-item" wx:if="{{currentParamData.contrast || currentParamData.parameters.contrast || (currentParamData.videoParameters && currentParamData.videoParameters.contrast)}}">
            <view class="param-detail-label"><text class="icon-dot"></text>对比度</view>
            <view class="param-detail-value">{{currentParamData.contrast || (currentParamData.videoParameters ? currentParamData.videoParameters.contrast : currentParamData.parameters.contrast)}}</view>
          </view>
          <view class="param-detail-item" wx:if="{{currentParamData.saturation || currentParamData.parameters.saturation || (currentParamData.videoParameters && currentParamData.videoParameters.saturation)}}">
            <view class="param-detail-label"><text class="icon-dot"></text>饱和度</view>
            <view class="param-detail-value">{{currentParamData.saturation || (currentParamData.videoParameters ? currentParamData.videoParameters.saturation : currentParamData.parameters.saturation)}}</view>
          </view>
          <view class="param-detail-item" wx:if="{{currentParamData.white_balance_temperature_auto !== undefined || currentParamData.parameters.white_balance_temperature_auto !== undefined || (currentParamData.videoParameters && currentParamData.videoParameters.white_balance_temperature_auto !== undefined)}}">
            <view class="param-detail-label"><text class="icon-dot"></text>自动白平衡</view>
            <view class="param-detail-value">{{currentParamData.white_balance_temperature_auto !== undefined ? currentParamData.white_balance_temperature_auto : (currentParamData.videoParameters && currentParamData.videoParameters.white_balance_temperature_auto !== undefined ? currentParamData.videoParameters.white_balance_temperature_auto : currentParamData.parameters.white_balance_temperature_auto)}}</view>
          </view>
          <view class="param-detail-item" wx:if="{{currentParamData.gain || currentParamData.parameters.gain || (currentParamData.videoParameters && currentParamData.videoParameters.gain)}}">
            <view class="param-detail-label"><text class="icon-dot"></text>增益</view>
            <view class="param-detail-value">{{currentParamData.gain || (currentParamData.videoParameters ? currentParamData.videoParameters.gain : currentParamData.parameters.gain)}}</view>
          </view>
          <view class="param-detail-item" wx:if="{{currentParamData.power_line_frequency || currentParamData.parameters.power_line_frequency || (currentParamData.videoParameters && currentParamData.videoParameters.power_line_frequency)}}">
            <view class="param-detail-label"><text class="icon-dot"></text>电力线频率</view>
            <view class="param-detail-value">{{currentParamData.power_line_frequency || (currentParamData.videoParameters ? currentParamData.videoParameters.power_line_frequency : currentParamData.parameters.power_line_frequency)}}</view>
          </view>
          <view class="param-detail-item" wx:if="{{currentParamData.white_balance_temperature || currentParamData.parameters.white_balance_temperature || (currentParamData.videoParameters && currentParamData.videoParameters.white_balance_temperature)}}">
            <view class="param-detail-label"><text class="icon-dot"></text>白平衡</view>
            <view class="param-detail-value">{{currentParamData.white_balance_temperature || (currentParamData.videoParameters ? currentParamData.videoParameters.white_balance_temperature : currentParamData.parameters.white_balance_temperature)}}</view>
          </view>
          <view class="param-detail-item" wx:if="{{currentParamData.sharpness || currentParamData.parameters.sharpness || (currentParamData.videoParameters && currentParamData.videoParameters.sharpness)}}">
             <view class="param-detail-label"><text class="icon-dot"></text>清晰度</view>
             <view class="param-detail-value">{{currentParamData.sharpness || (currentParamData.videoParameters ? currentParamData.videoParameters.sharpness : currentParamData.parameters.sharpness)}}</view>
           </view>
          <view class="param-detail-item" wx:if="{{currentParamData.exposure_auto !== undefined || currentParamData.parameters.exposure_auto !== undefined || (currentParamData.videoParameters && currentParamData.videoParameters.exposure_auto !== undefined)}}">
            <view class="param-detail-label"><text class="icon-dot"></text>自动曝光</view>
            <view class="param-detail-value">{{currentParamData.exposure_auto !== undefined ? currentParamData.exposure_auto : (currentParamData.videoParameters && currentParamData.videoParameters.exposure_auto !== undefined ? currentParamData.videoParameters.exposure_auto : currentParamData.parameters.exposure_auto)}}</view>
          </view>
          <view class="param-detail-item" wx:if="{{currentParamData.zoom_absolute || currentParamData.parameters.zoom_absolute || (currentParamData.videoParameters && currentParamData.videoParameters.zoom_absolute)}}">
            <view class="param-detail-label"><text class="icon-dot"></text>缩放</view>
            <view class="param-detail-value">{{currentParamData.zoom_absolute || (currentParamData.videoParameters ? currentParamData.videoParameters.zoom_absolute : currentParamData.parameters.zoom_absolute)}}</view>
          </view>
          <view class="param-detail-item" wx:if="{{currentParamData.exposure_absolute || currentParamData.parameters.exposure_absolute || (currentParamData.videoParameters && currentParamData.videoParameters.exposure_absolute)}}">
            <view class="param-detail-label"><text class="icon-dot"></text>曝光值</view>
            <view class="param-detail-value">{{currentParamData.exposure_absolute || (currentParamData.videoParameters ? currentParamData.videoParameters.exposure_absolute : currentParamData.parameters.exposure_absolute)}}</view>
          </view>
          <view class="param-detail-item" wx:if="{{currentParamData.pan_absolute !== undefined || (currentParamData.parameters && currentParamData.parameters.pan_absolute !== undefined) || (currentParamData.videoParameters && currentParamData.videoParameters.pan_absolute !== undefined)}}">
            <view class="param-detail-label"><text class="icon-dot"></text>摄像头水平转动</view>
            <view class="param-detail-value">{{currentParamData.pan_absolute !== undefined ? currentParamData.pan_absolute : (currentParamData.videoParameters && currentParamData.videoParameters.pan_absolute !== undefined ? currentParamData.videoParameters.pan_absolute : currentParamData.parameters.pan_absolute)}}</view>
          </view>
          <view class="param-detail-item" wx:if="{{currentParamData.tilt_absolute !== undefined || (currentParamData.parameters && currentParamData.parameters.tilt_absolute !== undefined) || (currentParamData.videoParameters && currentParamData.videoParameters.tilt_absolute !== undefined)}}">
            <view class="param-detail-label"><text class="icon-dot"></text>摄像头垂直转动</view>
            <view class="param-detail-value">{{currentParamData.tilt_absolute !== undefined ? currentParamData.tilt_absolute : (currentParamData.videoParameters && currentParamData.videoParameters.tilt_absolute !== undefined ? currentParamData.videoParameters.tilt_absolute : currentParamData.parameters.tilt_absolute)}}</view>
          </view>
          <view class="param-detail-item" wx:if="{{currentParamData.focus_absolute !== undefined || (currentParamData.parameters && currentParamData.parameters.focus_absolute !== undefined) || (currentParamData.videoParameters && currentParamData.videoParameters.focus_absolute !== undefined)}}">
            <view class="param-detail-label"><text class="icon-dot"></text>摄像头焦点移动</view>
            <view class="param-detail-value">{{currentParamData.focus_absolute !== undefined ? currentParamData.focus_absolute : (currentParamData.videoParameters && currentParamData.videoParameters.focus_absolute !== undefined ? currentParamData.videoParameters.focus_absolute : currentParamData.parameters.focus_absolute)}}</view>
          </view>
          <view class="param-detail-item" wx:if="{{currentParamData.camera_move_speed !== undefined || (currentParamData.parameters && currentParamData.parameters.camera_move_speed !== undefined) || (currentParamData.videoParameters && currentParamData.videoParameters.camera_move_speed !== undefined)}}">
            <view class="param-detail-label"><text class="icon-dot"></text>摄像机移动速度</view>
            <view class="param-detail-value">{{currentParamData.camera_move_speed !== undefined ? currentParamData.camera_move_speed : (currentParamData.videoParameters && currentParamData.videoParameters.camera_move_speed !== undefined ? currentParamData.videoParameters.camera_move_speed : currentParamData.parameters.camera_move_speed)}}</view>
          </view>
          <view class="param-detail-item" wx:if="{{currentParamData.setVoltage || currentParamData.parameters.setVoltage || (currentParamData.videoParameters && currentParamData.videoParameters.setVoltage)}}">
            <view class="param-detail-label"><text class="icon-dot"></text>电压 (5~20 V)</view>
            <view class="param-detail-value">{{currentParamData.setVoltage || (currentParamData.videoParameters ? currentParamData.videoParameters.setVoltage : currentParamData.parameters.setVoltage)}}</view>
          </view>
        </view>
      </scroll-view>
    </view>
  </view>

  <!-- 底部按钮区域 -->
  <view class="bottom-buttons {{isMultiSelectMode ? 'multi-select-mode' : ''}}">
    <block wx:if="{{isMultiSelectMode}}">
      <view class="btn-group">
        <view class="delete-btn equal-width-btn"
              bindtap="deleteSelectedData"
              hover-class="none">
          删除
        </view>
        <view class="export-btn equal-width-btn"
              bindtap="handleExportData"
              hover-class="none"
              style="opacity: {{canExport ? 1 : 0.5}}"
              disabled="{{!canExport}}">
          导出/保存
        </view>
        <view class="cancel-btn equal-width-btn"
              bindtap="exitMultiSelectMode"
              hover-class="none">
          取消
        </view>
      </view>
    </block>
    <view class="single-mode-buttons" wx:else>
      <view class="back-btn"
            bindtap="hideDataDisplay"
            hover-class="none">
        返回
      </view>
      <view class="select-btn"
            bindtap="enterMultiSelectMode"
            hover-class="none">
        选择
      </view>
    </view>
  </view>
</view>

<!-- 图表显示区域 -->
<view wx:if="{{showChart}}" class="chart-overlay" catchtouchmove="preventTouchMove">
  <view class="chart-container">
    <view class="chart-header">
      <text class="chart-title">T/C比例趋势图</text>
      <view class="chart-controls">
        <view class="reset-btn" bindtap="resetChartView">重置</view>
        <view class="export-btn" bindtap="exportChart" data-type="excel">导出Excel</view>
        <text class="close-btn" bindtap="closeChart">×</text>
      </view>
    </view>
    <view class="chart-scroll-container" 
          style="transform: scale({{chartScale}}) translate({{translateX}}px, {{translateY}}px);">
      <canvas type="2d" 
              id="tcChart" 
              class="tc-chart"
              bindtouchstart="touchStart"
              bindtouchmove="touchMove"
              bindtouchend="touchEnd"
              bindwheel="handleMouseWheel"></canvas>
    </view>
  </view>
</view>

<!-- 媒体选择卡片 -->
<view class="media-select-mask {{showMediaSelect ? 'show' : ''}}" catch:tap="hideMediaSelect">
  <view class="media-select-card" catch:tap="preventBubble">
    <view class="media-select-title">选择查看内容</view>
    <view class="media-select-options">
      <view class="media-option" catch:tap="viewCAreaImage">
        <image class="media-icon" src="../../assets/img/image-icon.png" mode="aspectFit"></image>
        <view class="option-text">C区图片</view>
      </view>
      <view class="media-option" catch:tap="viewTAreaImage">
        <image class="media-icon" src="../../assets/img/image-icon.png" mode="aspectFit"></image>
        <view class="option-text">T区图片</view>
      </view>
    </view>
  </view>
</view>

<!-- 导出选项卡片 -->
<view class="media-select-mask {{showExportOptions ? 'show' : ''}}" bindtap="hideExportOptions">
  <view class="media-select-card" catchtap="preventBubble">
    <view class="media-select-title">选择导出方式</view>
    <view class="media-select-options">
      <view class="media-option" catchtap="shareToWechat">
        <image class="media-icon" src="../../assets/img/wechat-icon.png" mode="aspectFit"></image>
        <view class="option-text">分享给好友</view>
      </view>
      <view class="media-option" catchtap="saveLocally">
        <image class="media-icon" src="../../assets/img/save-icon.png" mode="aspectFit"></image>
        <view class="option-text">保存到本地</view>
      </view>
    </view>
  </view>
</view>

<!-- 保存进度提示卡片 -->
<view class="save-progress-mask {{showSaveProgress || showSaveResults ? 'show' : ''}}" wx:if="{{showSaveProgress || showSaveResults}}">
  <!-- 进度显示 -->
  <view class="save-progress-card {{showSaveProgress && !showSaveResults ? 'show' : ''}}" wx:if="{{showSaveProgress && !showSaveResults}}">
    <!-- 卡片头部 -->
    <view class="save-progress-header">
      <view class="save-progress-title">保存到相册</view>
    </view>

    <!-- 卡片内容 -->
    <view class="save-progress-content">
      <!-- 保存进度分组 -->
      <view class="save-progress-group">
        <view class="save-progress-group-title">保存进度</view>

        <!-- 进度百分比项 -->
        <view class="save-progress-item">
          <view class="save-progress-label">
            <view class="icon-dot"></view>
            完成进度
          </view>
          <view class="save-progress-percentage">{{saveProgress.percentage}}%</view>
        </view>

        <!-- 进度条 -->
        <view class="save-progress-bar-container">
          <view class="save-progress-bar" style="width: {{saveProgress.percentage}}%"></view>
        </view>

        <!-- 状态信息项 -->
        <view class="save-progress-item" wx:if="{{saveProgress.text}}">
          <view class="save-progress-label">
            <view class="icon-dot"></view>
            状态
          </view>
          <view class="save-progress-value">{{saveProgress.text}}</view>
        </view>
      </view>

      <!-- 当前任务显示 -->
      <view class="save-progress-current-task" wx:if="{{saveProgress.currentTask}}">
        {{saveProgress.currentTask}}
      </view>
    </view>
  </view>

  <!-- 结果显示 -->
  <view class="save-results-card {{showSaveResults ? 'show' : ''}}" wx:if="{{showSaveResults}}">
    <!-- 卡片头部 -->
    <view class="save-results-header">
      <view class="save-results-title">保存完成</view>
    </view>

    <!-- 卡片内容 -->
    <view class="save-results-content">
      <!-- 成功保存分组 -->
      <view class="save-results-group" wx:if="{{saveResults.videos.length > 0 || saveResults.images.length > 0 || saveResults.dataFiles.length > 0}}">
        <view class="save-results-group-title">成功保存</view>

        <!-- 视频文件项 -->
        <view class="save-results-item" wx:if="{{saveResults.videos.length > 0}}">
          <view class="save-results-label">
            <view class="icon-dot"></view>
            视频文件
          </view>
          <view class="save-results-value">{{saveResults.videos.length}}个</view>
        </view>

        <!-- 图片文件项 -->
        <view class="save-results-item" wx:if="{{saveResults.images.length > 0}}">
          <view class="save-results-label">
            <view class="icon-dot"></view>
            图片文件
          </view>
          <view class="save-results-value">{{saveResults.images.length}}个</view>
        </view>

        <!-- 数据文件项 -->
        <view class="save-results-item" wx:if="{{saveResults.dataFiles.length > 0}}">
          <view class="save-results-label">
            <view class="icon-dot"></view>
            数据文件
          </view>
          <view class="save-results-value">{{saveResults.dataFiles.length}}个</view>
        </view>
      </view>

      <!-- 失败信息分组 -->
      <view class="save-results-group" wx:if="{{saveResults.errors.length > 0}}">
        <view class="save-results-group-title error">保存失败</view>

        <view class="save-results-error-item" wx:for="{{saveResults.errors}}" wx:key="index">
          <view class="save-results-error-label">
            <view class="icon-dot error"></view>
            错误 {{index + 1}}
          </view>
          <view class="save-results-error-value">{{item}}</view>
        </view>
      </view>

      <!-- 确定按钮 -->
      <button class="save-results-close-btn" bindtap="closeSaveResults">确定</button>
    </view>
  </view>
</view>

<!-- 隐藏的获取手机号按钮 -->
<button wx:if="{{showGetPhone}}" 
        class="hidden-btn" 
        open-type="getPhoneNumber" 
        bindgetphonenumber="getPhoneNumber">获取手机号</button>

<!-- 本地图片预览组件 -->
<view class="local-preview-mask {{showLocalPreview ? 'show' : ''}}" bindtap="hideLocalPreview">
  <view class="local-preview-container" catchtap="preventBubble">
    <view class="local-preview-header">
      <view class="local-preview-title">图片预览</view>
      <view class="local-preview-close" bindtap="hideLocalPreview">×</view>
    </view>
    <scroll-view scroll-y class="local-preview-scroll">
      <block wx:for="{{previewImageUrls}}" wx:key="index">
        <image class="local-preview-image" src="{{item}}" mode="widthFix" bindtap="copyImageUrl" data-url="{{item}}" binderror="onImageLoadError" data-index="{{index}}"></image>
      </block>
    </scroll-view>
  </view>
</view>

<!-- 内联视频播放器 -->
<view class="video-player-mask {{showVideoPlayer ? 'show' : ''}}" bindtap="closeVideoPlayer">
  <view class="video-player-container" catchtap="preventBubble">
    <view class="video-player-header">
      <view class="video-player-title">视频播放</view>
      <view class="video-player-close" bindtap="closeVideoPlayer">×</view>
    </view>
    <view class="video-player-content">
      <video 
        id="videoPlayer" 
        class="video-instance"
        src="{{currentVideoUrl}}" 
        controls 
        autoplay
        show-center-play-btn="true"
        show-play-btn="true"
        show-fullscreen-btn="true"
        enable-progress-gesture="true"
        object-fit="contain"
        bindended="closeVideoPlayer"
        binderror="onVideoError"
      ></video>
    </view>
  </view>
</view>

<!-- 隐藏的canvas用于生成数据图片 -->
<canvas type="2d" id="dataCanvas" style="position: fixed; top: -9999px; left: -9999px; width: 800px; height: 1200px;"></canvas>

<!-- 底部自定义导航栏 -->
<view class="custom-tabbar">
  <view class="tab-item {{currentTab === 'index' ? 'active' : ''}}" data-page="index" bindtap="switchTab">
    <view class="tab-icon-home">
      <view class="home-door"></view>
      <view class="home-window"></view>
      <view class="home-chimney"></view>
    </view>
    <text class="tab-text">主界面</text>
  </view>
  <view class="tab-item {{currentTab === 'addDevice' ? 'active' : ''}}" data-page="addDevice" bindtap="switchTab">
    <view class="tab-icon-add">
      <view class="add-connector"></view>
      <view class="add-plus-horizontal"></view>
      <view class="add-plus-vertical"></view>
    </view>
    <text class="tab-text">设备录入</text>
  </view>
  <view class="tab-item {{currentTab === 'me' ? 'active' : ''}}" data-page="me" bindtap="switchTab">
    <view class="tab-icon-me">
      <view class="me-ear-left"></view>
      <view class="me-ear-right"></view>
      <view class="me-arm-left"></view>
      <view class="me-arm-right"></view>
    </view>
    <text class="tab-text">个人</text>
  </view>
</view>