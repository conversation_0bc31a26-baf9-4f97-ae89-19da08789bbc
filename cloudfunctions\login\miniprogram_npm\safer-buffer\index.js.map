{"version": 3, "sources": ["safer.js"], "names": [], "mappings": ";;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.js", "sourcesContent": ["/* eslint-disable node/no-deprecated-api */\n\n\n\nvar buffer = require('buffer')\nvar Buffer = buffer.Buffer\n\nvar safer = {}\n\nvar key\n\nfor (key in buffer) {\n  if (!buffer.hasOwnProperty(key)) continue\n  if (key === 'SlowBuffer' || key === 'Buffer') continue\n  safer[key] = buffer[key]\n}\n\nvar Safer = safer.Buffer = {}\nfor (key in Buffer) {\n  if (!Buffer.hasOwnProperty(key)) continue\n  if (key === 'allocUnsafe' || key === 'allocUnsafeSlow') continue\n  Safer[key] = Buffer[key]\n}\n\nsafer.Buffer.prototype = Buffer.prototype\n\nif (!Safer.from || Safer.from === Uint8Array.from) {\n  Safer.from = function (value, encodingOrOffset, length) {\n    if (typeof value === 'number') {\n      throw new TypeError('The \"value\" argument must not be of type number. Received type ' + typeof value)\n    }\n    if (value && typeof value.length === 'undefined') {\n      throw new TypeError('The first argument must be one of type string, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>rray, or Array-like Object. Received type ' + typeof value)\n    }\n    return Buffer(value, encodingOrOffset, length)\n  }\n}\n\nif (!Safer.alloc) {\n  Safer.alloc = function (size, fill, encoding) {\n    if (typeof size !== 'number') {\n      throw new TypeError('The \"size\" argument must be of type number. Received type ' + typeof size)\n    }\n    if (size < 0 || size >= 2 * (1 << 30)) {\n      throw new RangeError('The value \"' + size + '\" is invalid for option \"size\"')\n    }\n    var buf = Buffer(size)\n    if (!fill || fill.length === 0) {\n      buf.fill(0)\n    } else if (typeof encoding === 'string') {\n      buf.fill(fill, encoding)\n    } else {\n      buf.fill(fill)\n    }\n    return buf\n  }\n}\n\nif (!safer.kStringMaxLength) {\n  try {\n    safer.kStringMaxLength = process.binding('buffer').kStringMaxLength\n  } catch (e) {\n    // we can't determine kStringMaxLength in environments where process.binding\n    // is unsupported, so let's not set it\n  }\n}\n\nif (!safer.constants) {\n  safer.constants = {\n    MAX_LENGTH: safer.kMaxLength\n  }\n  if (safer.kStringMaxLength) {\n    safer.constants.MAX_STRING_LENGTH = safer.kStringMaxLength\n  }\n}\n\nmodule.exports = safer\n"]}