<!-- addDevice.wxml -->
<!-- 自定义导航栏 - Skyline渲染引擎要求，动态适配状态栏高度 -->
<view class="custom-navbar" style="height: {{totalNavHeight}}px; padding-top: {{safeAreaTop}}px;">
  <view class="navbar-title">设备录入</view>
</view>

<view class="page__bd page__bd_spacing" style="padding-top: {{totalNavHeight + 20}}px;">
  <view class="weui-cells__title">录入方式</view>
  <view class="weui-cells weui-cells_after-title">
    <view class="weui-cell weui-cell_switch">
      <view class="weui-cell__bd">扫码录入:</view>
      <view class="weui-cell__ft">
        <switch bindchange="screenInput" disabled="{{!isScanButtonEnabled}}" checked="{{!isShowInputForm}}" />
      </view>
    </view>
    <view class="weui-cell weui-cell_switch">
      <view class="weui-cell__bd">连续录入:</view>
      <view class="weui-cell__ft">
        <switch checked />
      </view>
    </view>
  </view>
  <form wx:if="{{isShowInputForm}}">
    <view class="weui-cells__title">设备基本信息:</view>
    <view class="weui-cells weui-cells_after-title">
      <view class="weui-cell weui-cell_input">
        <view class="weui-cell__hd">
          <view class="weui-label">设备ID:</view>
        </view>
        <view class="weui-cell__bd">
          <input class="weui-input" placeholder="请输入设备ID" value="{{deviceId}}" bindinput="inputDeviceId" />
        </view>
      </view>
      <view class="weui-cell weui-cell_input">
        <view class="weui-cell__hd">
          <view class="weui-label">序列号SN:</view>
        </view>
        <view class="weui-cell__bd">
          <input class="weui-input" placeholder="请输入序列号SN" bindinput="inputSerialNumber" />
        </view>
      </view>
      <view class="weui-cell weui-cell_input">
        <view class="weui-cell__hd">
          <view class="weui-label">MAC地址:</view>
        </view>
        <view class="weui-cell__bd">
          <input class="weui-input" placeholder="请输入MAC地址" bindinput="inputMacAddress" />
        </view>
      </view>
      <view class="weui-cell weui-cell_input">
        <view class="weui-cell__hd">
          <view class="weui-label">价格:</view>
        </view>
        <view class="weui-cell__bd">
          <input class="weui-input" placeholder="请输入价格" bindinput="inputPrice" />
        </view>
      </view>
      <view class="weui-cell weui-cell_input">
        <view class="weui-cell__hd">
          <view class="weui-label">设备型号:</view>
        </view>
        <view class="weui-cell__bd">
          <input class="weui-input" placeholder="请输入设备型号" bindinput="inputDeviceModel" />
        </view>
      </view>
      <view class="weui-cell weui-cell_input">
        <view class="weui-cell__hd">
          <view class="weui-label">设备名称:</view>
        </view>
        <view class="weui-cell__bd">
          <input class="weui-input" placeholder="请输入设备名称" bindinput="inputDeviceName" />
        </view>
      </view>
    </view>
  </form>
  <view class="weui-btn-area">
    <button class="weui-btn" type="primary" bindtap="addDevice">开始录入</button>
  </view>
</view>

<!-- 自定义底部导航栏 -->
<view class="custom-tabbar">
  <view class="tab-item {{ currentTab === 'index' ? 'active' : '' }}" bindtap="switchTab" data-page="index">
    <view class="tab-icon-home">
      <view class="home-door"></view>
      <view class="home-window"></view>
      <view class="home-chimney"></view>
    </view>
    <text class="tab-text">主界面</text>
  </view>
  <view class="tab-item {{ currentTab === 'addDevice' ? 'active' : '' }}" bindtap="switchTab" data-page="addDevice">
    <view class="tab-icon-add">
      <view class="add-connector"></view>
      <view class="add-plus-horizontal"></view>
      <view class="add-plus-vertical"></view>
    </view>
    <text class="tab-text">设备录入</text>
  </view>
  <view class="tab-item {{ currentTab === 'me' ? 'active' : '' }}" bindtap="switchTab" data-page="me">
    <view class="tab-icon-me">
      <view class="me-ear-left"></view>
      <view class="me-ear-right"></view>
      <view class="me-arm-left"></view>
      <view class="me-arm-right"></view>
    </view>
    <text class="tab-text">我的</text>
  </view>
</view>