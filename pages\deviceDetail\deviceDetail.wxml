<!-- 自定义导航栏 - Skyline渲染引擎要求 -->
<view class="custom-navbar">
  <view class="navbar-title">设备管理系统</view>
</view>

<view class="content" wx:if="{{isLoading}}">
    <view class="device-content">
        <view>
            <view class="device-list">
                <view class="device-image">
                    <image src="{{deviceMsg.image}}" mode="aspectFit"></image>
                </view>
                <view class="device-info">
                    <view class="device-info-style">
                        <view>设备名称: {{deviceMsg.name}}</view>
                        <view>设备ID: {{deviceMsg.deviceId}}</view>
                        <view>设备型号: {{deviceMsg.model}}</view>
                        <view>设备状态: {{deviceMsg.status}}</view>
                        <view wx:if="{{!showAddDevice}}">可借数量: {{deviceMsg.qty}}</view>

                        <view class="add-device-container" wx:if="{{showAddDevice}}">
                            <label>录入数量：</label>
                            <input class="add-device-qty" value="{{addDeviceQty}}" bindchange="inputChange"/>
                        </view>
                    </view>
                </view>
            </view>
        </view>
    </view>
    <view class="space-line-30"></view>
    <scroll-view scroll-y="true" class="scroll-content" style="height:{{windowHeight * 0.66}}px">
        <view class="device-intro">
            <text class="device-title">设备介绍</text>
            <text class="device-msg">{{deviceMsg.description}}</text>
        </view>
        <view class="space-line-30"></view>
        <view class="device-intro">
            <text class="device-title">使用说明</text>
            <text class="device-msg">{{deviceMsg.usage}}</text>
        </view>
        <view class="space-line-30"></view>
        <view class="device-intro">
            <text class="device-title">维护记录</text>
            <text class="device-msg">{{deviceMsg.maintenance}}</text>
        </view>
        <view class="btn-area">
            <button type="primary" size="default" wx:if="{{showBorrowBtn}}" bindtap="borrowDevice">借阅</button>
            <button type="primary" size="default" wx:if="{{showBookBtn}}" bindtap="bookDevice">预约</button>
            <button type="primary" size="default" wx:if="{{showAddDevice}}" bindtap="addDevice">录入</button>
        </view>
    </scroll-view>
</view>



