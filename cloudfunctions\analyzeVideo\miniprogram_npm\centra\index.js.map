{"version": 3, "sources": ["createRequest.js", "model/CentraRequest.js", "model/CentraResponse.js"], "names": [], "mappings": ";;;;;;;AAAA;AACA;AACA;AACA,ACHA;ADIA,ACHA;ADIA,ACHA;AACA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.js", "sourcesContent": ["const CentraRequest = require('./model/CentraRequest.js')\n\nmodule.exports = (url, method) => {\n\treturn new CentraRequest(url, method)\n}\n", "const path = require('path')\nconst http = require('http')\nconst https = require('https')\nconst followRedirects = require('follow-redirects')\nconst qs = require('querystring')\nconst zlib = require('zlib')\nconst {URL} = require('url')\n\nconst CentraResponse = require('./CentraResponse.js')\n\nconst supportedCompressions = ['gzip', 'deflate', 'br']\n\nconst useRequest = (protocol, maxRedirects) => {\n\tlet httpr\n\tlet httpsr\n\tif (maxRedirects <= 0) {\n\t\thttpr = http.request\n\t\thttpsr = https.request\n\t}\n\telse {\n\t\thttpr = followRedirects.http.request\n\t\thttpsr = followRedirects.https.request\n\t}\n\n\tif (protocol === 'http:') {\n\t\treturn httpr\n\t}\n\telse if (protocol === 'https:') {\n\t\treturn httpsr\n\t}\n\telse throw new Error('Bad URL protocol: ' + protocol)\n}\n\nmodule.exports = class CentraRequest {\n\tconstructor (url, method = 'GET') {\n\t\tthis.url = typeof url === 'string' ? new URL(url) : url\n\t\tthis.method = method\n\t\tthis.data = null\n\t\tthis.sendDataAs = null\n\t\tthis.reqHeaders = {}\n\t\tthis.streamEnabled = false\n\t\tthis.compressionEnabled = false\n\t\tthis.timeoutTime = null\n\t\tthis.coreOptions = {}\n\t\tthis.maxRedirects = 0\n\n\t\tthis.resOptions = {\n\t\t\t'maxBuffer': 50 * 1000000 // 50 MB\n\t\t}\n\n\t\treturn this\n\t}\n\n\tfollowRedirects(n) {\n\t\tthis.maxRedirects = n\n\n\t\treturn this\n\t}\n\n\tquery (a1, a2) {\n\t\tif (typeof a1 === 'object') {\n\t\t\tObject.keys(a1).forEach((queryKey) => {\n\t\t\t\tthis.url.searchParams.append(queryKey, a1[queryKey])\n\t\t\t})\n\t\t}\n\t\telse this.url.searchParams.append(a1, a2)\n\n\t\treturn this\n\t}\n\n\tpath (relativePath) {\n\t\tthis.url.pathname = path.join(this.url.pathname, relativePath)\n\n\t\treturn this\n\t}\n\n\tbody (data, sendAs) {\n\t\tthis.sendDataAs = typeof data === 'object' && !sendAs && !Buffer.isBuffer(data) ? 'json' : (sendAs ? sendAs.toLowerCase() : 'buffer')\n\t\tthis.data = this.sendDataAs === 'form' ? qs.stringify(data) : (this.sendDataAs === 'json' ? JSON.stringify(data) : data)\n\n\t\treturn this\n\t}\n\n\theader (a1, a2) {\n\t\tif (typeof a1 === 'object') {\n\t\t\tObject.keys(a1).forEach((headerName) => {\n\t\t\t\tthis.reqHeaders[headerName.toLowerCase()] = a1[headerName]\n\t\t\t})\n\t\t}\n\t\telse this.reqHeaders[a1.toLowerCase()] = a2\n\n\t\treturn this\n\t}\n\n\ttimeout (timeout) {\n\t\tthis.timeoutTime = timeout\n\n\t\treturn this\n\t}\n\n\toption (name, value) {\n\t\tthis.coreOptions[name] = value\n\n\t\treturn this\n\t}\n\n\tstream () {\n\t\tthis.streamEnabled = true\n\n\t\treturn this\n\t}\n\n\tcompress () {\n\t\tthis.compressionEnabled = true\n\n\t\tif (!this.reqHeaders['accept-encoding']) this.reqHeaders['accept-encoding'] = supportedCompressions.join(', ')\n\n\t\treturn this\n\t}\n\n\tsend () {\n\t\treturn new Promise((resolve, reject) => {\n\t\t\tif (this.data) {\n\t\t\t\tif (!this.reqHeaders.hasOwnProperty('content-type')) {\n\t\t\t\t\tif (this.sendDataAs === 'json') {\n\t\t\t\t\t\tthis.reqHeaders['content-type'] = 'application/json'\n\t\t\t\t\t}\n\t\t\t\t\telse if (this.sendDataAs === 'form') {\n\t\t\t\t\t\tthis.reqHeaders['content-type'] = 'application/x-www-form-urlencoded'\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\tif (!this.reqHeaders.hasOwnProperty('content-length')) {\n\t\t\t\t\tthis.reqHeaders['content-length'] = Buffer.byteLength(this.data)\n\t\t\t\t}\n\t\t\t}\n\n\t\t\tconst options = Object.assign({\n\t\t\t\t'protocol': this.url.protocol,\n\t\t\t\t'host': this.url.hostname.replace('[', '').replace(']', ''),\n\t\t\t\t'port': this.url.port,\n\t\t\t\t'path': this.url.pathname + (this.url.search === null ? '' : this.url.search),\n\t\t\t\t'method': this.method,\n\t\t\t\t'headers': this.reqHeaders,\n\t\t\t\t'maxRedirects': this.maxRedirects\n\t\t\t}, this.coreOptions)\n\n\t\t\tlet req\n\n\t\t\tconst resHandler = (res) => {\n\t\t\t\tlet stream = res\n\n\t\t\t\tif (this.compressionEnabled) {\n\t\t\t\t\tif (res.headers['content-encoding'] === 'gzip') {\n\t\t\t\t\t\tstream = res.pipe(zlib.createGunzip())\n\t\t\t\t\t}\n\t\t\t\t\telse if (res.headers['content-encoding'] === 'deflate') {\n\t\t\t\t\t\tstream = res.pipe(zlib.createInflate())\n\t\t\t\t\t}\n\t\t\t\t\telse if (res.headers['content-encoding'] === 'br') {\n\t\t\t\t\t\tstream = res.pipe(zlib.createBrotliDecompress())\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\tlet centraRes\n\n\t\t\t\tif (this.streamEnabled) {\n\t\t\t\t\tresolve(stream)\n\t\t\t\t}\n\t\t\t\telse {\n\t\t\t\t\tcentraRes = new CentraResponse(res, this.resOptions)\n\n\t\t\t\t\tstream.on('error', (err) => {\n\t\t\t\t\t\treject(err)\n\t\t\t\t\t})\n\n\t\t\t\t\tstream.on('aborted', () => {\n\t\t\t\t\t\treject(new Error('Server aborted request'))\n\t\t\t\t\t})\n\n\t\t\t\t\tstream.on('data', (chunk) => {\n\t\t\t\t\t\tcentraRes._addChunk(chunk)\n\n\t\t\t\t\t\tif (this.resOptions.maxBuffer !== null && centraRes.body.length > this.resOptions.maxBuffer) {\n\t\t\t\t\t\t\tstream.destroy()\n\n\t\t\t\t\t\t\treject('Received a response which was longer than acceptable when buffering. (' + this.body.length + ' bytes)')\n\t\t\t\t\t\t}\n\t\t\t\t\t})\n\n\t\t\t\t\tstream.on('end', () => {\n\t\t\t\t\t\tresolve(centraRes)\n\t\t\t\t\t})\n\t\t\t\t}\n\t\t\t}\n\n\t\t\tconst request = useRequest(this.url.protocol, this.maxRedirects)\n\n\t\t\treq = request(options, resHandler)\n\n\t\t\tif (this.timeoutTime) {\n\t\t\t\treq.setTimeout(this.timeoutTime, () => {\n\t\t\t\t\treq.abort()\n\n\t\t\t\t\tif (!this.streamEnabled) {\n\t\t\t\t\t\treject(new Error('Timeout reached'))\n\t\t\t\t\t}\n\t\t\t\t})\n\t\t\t}\n\n\t\t\treq.on('error', (err) => {\n\t\t\t\treject(err)\n\t\t\t})\n\n\t\t\tif (this.data) req.write(this.data)\n\n\t\t\treq.end()\n\t\t})\n\t}\n}\n", "module.exports = class CentraResponse {\n\tconstructor (res, resOptions) {\n\t\tthis.coreRes = res\n\t\tthis.resOptions = resOptions\n\n\t\tthis.body = Buffer.alloc(0)\n\n\t\tthis.headers = res.headers\n\t\tthis.statusCode = res.statusCode\n\t}\n\n\t_addChunk (chunk) {\n\t\tthis.body = Buffer.concat([this.body, chunk])\n\t}\n\n\tasync json () {\n\t\treturn this.statusCode === 204 ? null : JSON.parse(this.body)\n\t}\n\n\tasync text () {\n\t\treturn this.body.toString()\n\t}\n}\n"]}