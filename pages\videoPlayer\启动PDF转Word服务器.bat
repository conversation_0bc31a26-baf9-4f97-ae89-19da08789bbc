@echo off
chcp 65001 >nul
title PDF转Word服务器启动器

echo ================================================================
echo 🚀 PDF转Word服务器自动启动程序
echo ================================================================
echo 📋 功能：自动检查依赖 + 启动服务器 + 打开浏览器
echo 🔧 支持：标准转换、精确转换、MinerU等多种模式
echo ================================================================
echo.

echo 🔍 检查Python环境...
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ 错误：未找到Python，请先安装Python 3.7+
    echo 📥 下载地址：https://www.python.org/downloads/
    pause
    exit /b 1
)

echo ✅ Python环境检查通过

echo.
echo 📦 检查并安装基础依赖...
echo 🔄 安装Flask...
pip install flask -i https://pypi.tuna.tsinghua.edu.cn/simple/ --quiet

echo 🔄 安装pdf2docx...
pip install pdf2docx -i https://pypi.tuna.tsinghua.edu.cn/simple/ --quiet

echo 🔄 安装python-docx...
pip install python-docx -i https://pypi.tuna.tsinghua.edu.cn/simple/ --quiet

echo 🔄 安装Pillow...
pip install Pillow -i https://pypi.tuna.tsinghua.edu.cn/simple/ --quiet

echo 🔄 安装PyMuPDF...
pip install PyMuPDF -i https://pypi.tuna.tsinghua.edu.cn/simple/ --quiet

echo ✅ 基础依赖安装完成

echo.
echo 🌐 启动PDF转Word服务器...
echo ⏳ 服务器启动中，请稍候...

start "" python pdf_to_word_server.py

echo ✅ 服务器启动命令已执行

echo.
echo ⏳ 等待服务器就绪...
timeout /t 3 /nobreak >nul

echo 🌍 打开浏览器...
start "" http://localhost:5001

echo.
echo ================================================================
echo 🎉 PDF转Word服务器已启动！
echo 🌐 访问地址：http://localhost:5001
echo 📋 支持格式：PDF → Word
echo 🔧 转换模式：标准、精确、MinerU等
echo ================================================================
echo.
echo 💡 使用提示：
echo    - 浏览器会自动打开服务页面
echo    - 上传PDF文件即可开始转换
echo    - 支持多种转换模式，推荐使用MinerU转换
echo    - 关闭服务器窗口可停止服务
echo.
echo 📋 如果浏览器未自动打开，请手动访问：
echo    http://localhost:5001
echo.
echo ⚠️ 注意：请保持此窗口打开，关闭将停止服务器
echo.
pause
