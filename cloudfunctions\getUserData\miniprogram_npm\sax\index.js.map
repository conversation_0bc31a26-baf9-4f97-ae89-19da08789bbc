{"version": 3, "sources": ["sax.js"], "names": [], "mappings": ";;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.js", "sourcesContent": [";(function (sax) { // wrapper for non-node envs\n  sax.parser = function (strict, opt) { return new SAXParser(strict, opt) }\n  sax.SAXParser = SAXParser\n  sax.SAXStream = SAXStream\n  sax.createStream = createStream\n\n  // When we pass the MAX_BUFFER_LENGTH position, start checking for buffer overruns.\n  // When we check, schedule the next check for MAX_BUFFER_LENGTH - (max(buffer lengths)),\n  // since that's the earliest that a buffer overrun could occur.  This way, checks are\n  // as rare as required, but as often as necessary to ensure never crossing this bound.\n  // Furthermore, buffers are only tested at most once per write(), so passing a very\n  // large string into write() might have undesirable effects, but this is manageable by\n  // the caller, so it is assumed to be safe.  Thus, a call to write() may, in the extreme\n  // edge case, result in creating at most one complete copy of the string passed in.\n  // Set to Infinity to have unlimited buffers.\n  sax.MAX_BUFFER_LENGTH = 64 * 1024\n\n  var buffers = [\n    'comment', 'sgmlDecl', 'textNode', 'tagName', 'doctype',\n    'procInstName', 'procInstBody', 'entity', 'attribName',\n    'attribValue', 'cdata', 'script'\n  ]\n\n  sax.EVENTS = [\n    'text',\n    'processinginstruction',\n    'sgmldeclaration',\n    'doctype',\n    'comment',\n    'opentagstart',\n    'attribute',\n    'opentag',\n    'closetag',\n    'opencdata',\n    'cdata',\n    'closecdata',\n    'error',\n    'end',\n    'ready',\n    'script',\n    'opennamespace',\n    'closenamespace'\n  ]\n\n  function SAXParser (strict, opt) {\n    if (!(this instanceof SAXParser)) {\n      return new SAXParser(strict, opt)\n    }\n\n    var parser = this\n    clearBuffers(parser)\n    parser.q = parser.c = ''\n    parser.bufferCheckPosition = sax.MAX_BUFFER_LENGTH\n    parser.opt = opt || {}\n    parser.opt.lowercase = parser.opt.lowercase || parser.opt.lowercasetags\n    parser.looseCase = parser.opt.lowercase ? 'toLowerCase' : 'toUpperCase'\n    parser.tags = []\n    parser.closed = parser.closedRoot = parser.sawRoot = false\n    parser.tag = parser.error = null\n    parser.strict = !!strict\n    parser.noscript = !!(strict || parser.opt.noscript)\n    parser.state = S.BEGIN\n    parser.strictEntities = parser.opt.strictEntities\n    parser.ENTITIES = parser.strictEntities ? Object.create(sax.XML_ENTITIES) : Object.create(sax.ENTITIES)\n    parser.attribList = []\n\n    // namespaces form a prototype chain.\n    // it always points at the current tag,\n    // which protos to its parent tag.\n    if (parser.opt.xmlns) {\n      parser.ns = Object.create(rootNS)\n    }\n\n    // disallow unquoted attribute values if not otherwise configured\n    // and strict mode is true\n    if (parser.opt.unquotedAttributeValues === undefined) {\n      parser.opt.unquotedAttributeValues = !strict;\n    }\n\n    // mostly just for error reporting\n    parser.trackPosition = parser.opt.position !== false\n    if (parser.trackPosition) {\n      parser.position = parser.line = parser.column = 0\n    }\n    emit(parser, 'onready')\n  }\n\n  if (!Object.create) {\n    Object.create = function (o) {\n      function F () {}\n      F.prototype = o\n      var newf = new F()\n      return newf\n    }\n  }\n\n  if (!Object.keys) {\n    Object.keys = function (o) {\n      var a = []\n      for (var i in o) if (o.hasOwnProperty(i)) a.push(i)\n      return a\n    }\n  }\n\n  function checkBufferLength (parser) {\n    var maxAllowed = Math.max(sax.MAX_BUFFER_LENGTH, 10)\n    var maxActual = 0\n    for (var i = 0, l = buffers.length; i < l; i++) {\n      var len = parser[buffers[i]].length\n      if (len > maxAllowed) {\n        // Text/cdata nodes can get big, and since they're buffered,\n        // we can get here under normal conditions.\n        // Avoid issues by emitting the text node now,\n        // so at least it won't get any bigger.\n        switch (buffers[i]) {\n          case 'textNode':\n            closeText(parser)\n            break\n\n          case 'cdata':\n            emitNode(parser, 'oncdata', parser.cdata)\n            parser.cdata = ''\n            break\n\n          case 'script':\n            emitNode(parser, 'onscript', parser.script)\n            parser.script = ''\n            break\n\n          default:\n            error(parser, 'Max buffer length exceeded: ' + buffers[i])\n        }\n      }\n      maxActual = Math.max(maxActual, len)\n    }\n    // schedule the next check for the earliest possible buffer overrun.\n    var m = sax.MAX_BUFFER_LENGTH - maxActual\n    parser.bufferCheckPosition = m + parser.position\n  }\n\n  function clearBuffers (parser) {\n    for (var i = 0, l = buffers.length; i < l; i++) {\n      parser[buffers[i]] = ''\n    }\n  }\n\n  function flushBuffers (parser) {\n    closeText(parser)\n    if (parser.cdata !== '') {\n      emitNode(parser, 'oncdata', parser.cdata)\n      parser.cdata = ''\n    }\n    if (parser.script !== '') {\n      emitNode(parser, 'onscript', parser.script)\n      parser.script = ''\n    }\n  }\n\n  SAXParser.prototype = {\n    end: function () { end(this) },\n    write: write,\n    resume: function () { this.error = null; return this },\n    close: function () { return this.write(null) },\n    flush: function () { flushBuffers(this) }\n  }\n\n  var Stream\n  try {\n    Stream = require('stream').Stream\n  } catch (ex) {\n    Stream = function () {}\n  }\n  if (!Stream) Stream = function () {}\n\n  var streamWraps = sax.EVENTS.filter(function (ev) {\n    return ev !== 'error' && ev !== 'end'\n  })\n\n  function createStream (strict, opt) {\n    return new SAXStream(strict, opt)\n  }\n\n  function SAXStream (strict, opt) {\n    if (!(this instanceof SAXStream)) {\n      return new SAXStream(strict, opt)\n    }\n\n    Stream.apply(this)\n\n    this._parser = new SAXParser(strict, opt)\n    this.writable = true\n    this.readable = true\n\n    var me = this\n\n    this._parser.onend = function () {\n      me.emit('end')\n    }\n\n    this._parser.onerror = function (er) {\n      me.emit('error', er)\n\n      // if didn't throw, then means error was handled.\n      // go ahead and clear error, so we can write again.\n      me._parser.error = null\n    }\n\n    this._decoder = null\n\n    streamWraps.forEach(function (ev) {\n      Object.defineProperty(me, 'on' + ev, {\n        get: function () {\n          return me._parser['on' + ev]\n        },\n        set: function (h) {\n          if (!h) {\n            me.removeAllListeners(ev)\n            me._parser['on' + ev] = h\n            return h\n          }\n          me.on(ev, h)\n        },\n        enumerable: true,\n        configurable: false\n      })\n    })\n  }\n\n  SAXStream.prototype = Object.create(Stream.prototype, {\n    constructor: {\n      value: SAXStream\n    }\n  })\n\n  SAXStream.prototype.write = function (data) {\n    if (typeof Buffer === 'function' &&\n      typeof Buffer.isBuffer === 'function' &&\n      Buffer.isBuffer(data)) {\n      if (!this._decoder) {\n        var SD = require('string_decoder').StringDecoder\n        this._decoder = new SD('utf8')\n      }\n      data = this._decoder.write(data)\n    }\n\n    this._parser.write(data.toString())\n    this.emit('data', data)\n    return true\n  }\n\n  SAXStream.prototype.end = function (chunk) {\n    if (chunk && chunk.length) {\n      this.write(chunk)\n    }\n    this._parser.end()\n    return true\n  }\n\n  SAXStream.prototype.on = function (ev, handler) {\n    var me = this\n    if (!me._parser['on' + ev] && streamWraps.indexOf(ev) !== -1) {\n      me._parser['on' + ev] = function () {\n        var args = arguments.length === 1 ? [arguments[0]] : Array.apply(null, arguments)\n        args.splice(0, 0, ev)\n        me.emit.apply(me, args)\n      }\n    }\n\n    return Stream.prototype.on.call(me, ev, handler)\n  }\n\n  // this really needs to be replaced with character classes.\n  // XML allows all manner of ridiculous numbers and digits.\n  var CDATA = '[CDATA['\n  var DOCTYPE = 'DOCTYPE'\n  var XML_NAMESPACE = 'http://www.w3.org/XML/1998/namespace'\n  var XMLNS_NAMESPACE = 'http://www.w3.org/2000/xmlns/'\n  var rootNS = { xml: XML_NAMESPACE, xmlns: XMLNS_NAMESPACE }\n\n  // http://www.w3.org/TR/REC-xml/#NT-NameStartChar\n  // This implementation works on strings, a single character at a time\n  // as such, it cannot ever support astral-plane characters (10000-EFFFF)\n  // without a significant breaking change to either this  parser, or the\n  // JavaScript language.  Implementation of an emoji-capable xml parser\n  // is left as an exercise for the reader.\n  var nameStart = /[:_A-Za-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD]/\n\n  var nameBody = /[:_A-Za-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD\\u00B7\\u0300-\\u036F\\u203F-\\u2040.\\d-]/\n\n  var entityStart = /[#:_A-Za-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD]/\n  var entityBody = /[#:_A-Za-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD\\u00B7\\u0300-\\u036F\\u203F-\\u2040.\\d-]/\n\n  function isWhitespace (c) {\n    return c === ' ' || c === '\\n' || c === '\\r' || c === '\\t'\n  }\n\n  function isQuote (c) {\n    return c === '\"' || c === '\\''\n  }\n\n  function isAttribEnd (c) {\n    return c === '>' || isWhitespace(c)\n  }\n\n  function isMatch (regex, c) {\n    return regex.test(c)\n  }\n\n  function notMatch (regex, c) {\n    return !isMatch(regex, c)\n  }\n\n  var S = 0\n  sax.STATE = {\n    BEGIN: S++, // leading byte order mark or whitespace\n    BEGIN_WHITESPACE: S++, // leading whitespace\n    TEXT: S++, // general stuff\n    TEXT_ENTITY: S++, // &amp and such.\n    OPEN_WAKA: S++, // <\n    SGML_DECL: S++, // <!BLARG\n    SGML_DECL_QUOTED: S++, // <!BLARG foo \"bar\n    DOCTYPE: S++, // <!DOCTYPE\n    DOCTYPE_QUOTED: S++, // <!DOCTYPE \"//blah\n    DOCTYPE_DTD: S++, // <!DOCTYPE \"//blah\" [ ...\n    DOCTYPE_DTD_QUOTED: S++, // <!DOCTYPE \"//blah\" [ \"foo\n    COMMENT_STARTING: S++, // <!-\n    COMMENT: S++, // <!--\n    COMMENT_ENDING: S++, // <!-- blah -\n    COMMENT_ENDED: S++, // <!-- blah --\n    CDATA: S++, // <![CDATA[ something\n    CDATA_ENDING: S++, // ]\n    CDATA_ENDING_2: S++, // ]]\n    PROC_INST: S++, // <?hi\n    PROC_INST_BODY: S++, // <?hi there\n    PROC_INST_ENDING: S++, // <?hi \"there\" ?\n    OPEN_TAG: S++, // <strong\n    OPEN_TAG_SLASH: S++, // <strong /\n    ATTRIB: S++, // <a\n    ATTRIB_NAME: S++, // <a foo\n    ATTRIB_NAME_SAW_WHITE: S++, // <a foo _\n    ATTRIB_VALUE: S++, // <a foo=\n    ATTRIB_VALUE_QUOTED: S++, // <a foo=\"bar\n    ATTRIB_VALUE_CLOSED: S++, // <a foo=\"bar\"\n    ATTRIB_VALUE_UNQUOTED: S++, // <a foo=bar\n    ATTRIB_VALUE_ENTITY_Q: S++, // <foo bar=\"&quot;\"\n    ATTRIB_VALUE_ENTITY_U: S++, // <foo bar=&quot\n    CLOSE_TAG: S++, // </a\n    CLOSE_TAG_SAW_WHITE: S++, // </a   >\n    SCRIPT: S++, // <script> ...\n    SCRIPT_ENDING: S++ // <script> ... <\n  }\n\n  sax.XML_ENTITIES = {\n    'amp': '&',\n    'gt': '>',\n    'lt': '<',\n    'quot': '\"',\n    'apos': \"'\"\n  }\n\n  sax.ENTITIES = {\n    'amp': '&',\n    'gt': '>',\n    'lt': '<',\n    'quot': '\"',\n    'apos': \"'\",\n    'AElig': 198,\n    'Aacute': 193,\n    'Acirc': 194,\n    'Agrave': 192,\n    'Aring': 197,\n    'Atilde': 195,\n    'Auml': 196,\n    'Ccedil': 199,\n    'ETH': 208,\n    'Eacute': 201,\n    'Ecirc': 202,\n    'Egrave': 200,\n    'Euml': 203,\n    'Iacute': 205,\n    'Icirc': 206,\n    'Igrave': 204,\n    'Iuml': 207,\n    'Ntilde': 209,\n    'Oacute': 211,\n    'Ocirc': 212,\n    'Ograve': 210,\n    'Oslash': 216,\n    'Otilde': 213,\n    'Ouml': 214,\n    'THORN': 222,\n    'Uacute': 218,\n    'Ucirc': 219,\n    'Ugrave': 217,\n    'Uuml': 220,\n    'Yacute': 221,\n    'aacute': 225,\n    'acirc': 226,\n    'aelig': 230,\n    'agrave': 224,\n    'aring': 229,\n    'atilde': 227,\n    'auml': 228,\n    'ccedil': 231,\n    'eacute': 233,\n    'ecirc': 234,\n    'egrave': 232,\n    'eth': 240,\n    'euml': 235,\n    'iacute': 237,\n    'icirc': 238,\n    'igrave': 236,\n    'iuml': 239,\n    'ntilde': 241,\n    'oacute': 243,\n    'ocirc': 244,\n    'ograve': 242,\n    'oslash': 248,\n    'otilde': 245,\n    'ouml': 246,\n    'szlig': 223,\n    'thorn': 254,\n    'uacute': 250,\n    'ucirc': 251,\n    'ugrave': 249,\n    'uuml': 252,\n    'yacute': 253,\n    'yuml': 255,\n    'copy': 169,\n    'reg': 174,\n    'nbsp': 160,\n    'iexcl': 161,\n    'cent': 162,\n    'pound': 163,\n    'curren': 164,\n    'yen': 165,\n    'brvbar': 166,\n    'sect': 167,\n    'uml': 168,\n    'ordf': 170,\n    'laquo': 171,\n    'not': 172,\n    'shy': 173,\n    'macr': 175,\n    'deg': 176,\n    'plusmn': 177,\n    'sup1': 185,\n    'sup2': 178,\n    'sup3': 179,\n    'acute': 180,\n    'micro': 181,\n    'para': 182,\n    'middot': 183,\n    'cedil': 184,\n    'ordm': 186,\n    'raquo': 187,\n    'frac14': 188,\n    'frac12': 189,\n    'frac34': 190,\n    'iquest': 191,\n    'times': 215,\n    'divide': 247,\n    'OElig': 338,\n    'oelig': 339,\n    'Scaron': 352,\n    'scaron': 353,\n    'Yuml': 376,\n    'fnof': 402,\n    'circ': 710,\n    'tilde': 732,\n    'Alpha': 913,\n    'Beta': 914,\n    'Gamma': 915,\n    'Delta': 916,\n    'Epsilon': 917,\n    'Zeta': 918,\n    'Eta': 919,\n    'Theta': 920,\n    'Iota': 921,\n    'Kappa': 922,\n    'Lambda': 923,\n    'Mu': 924,\n    'Nu': 925,\n    'Xi': 926,\n    'Omicron': 927,\n    'Pi': 928,\n    'Rho': 929,\n    'Sigma': 931,\n    'Tau': 932,\n    'Upsilon': 933,\n    'Phi': 934,\n    'Chi': 935,\n    'Psi': 936,\n    'Omega': 937,\n    'alpha': 945,\n    'beta': 946,\n    'gamma': 947,\n    'delta': 948,\n    'epsilon': 949,\n    'zeta': 950,\n    'eta': 951,\n    'theta': 952,\n    'iota': 953,\n    'kappa': 954,\n    'lambda': 955,\n    'mu': 956,\n    'nu': 957,\n    'xi': 958,\n    'omicron': 959,\n    'pi': 960,\n    'rho': 961,\n    'sigmaf': 962,\n    'sigma': 963,\n    'tau': 964,\n    'upsilon': 965,\n    'phi': 966,\n    'chi': 967,\n    'psi': 968,\n    'omega': 969,\n    'thetasym': 977,\n    'upsih': 978,\n    'piv': 982,\n    'ensp': 8194,\n    'emsp': 8195,\n    'thinsp': 8201,\n    'zwnj': 8204,\n    'zwj': 8205,\n    'lrm': 8206,\n    'rlm': 8207,\n    'ndash': 8211,\n    'mdash': 8212,\n    'lsquo': 8216,\n    'rsquo': 8217,\n    'sbquo': 8218,\n    'ldquo': 8220,\n    'rdquo': 8221,\n    'bdquo': 8222,\n    'dagger': 8224,\n    'Dagger': 8225,\n    'bull': 8226,\n    'hellip': 8230,\n    'permil': 8240,\n    'prime': 8242,\n    'Prime': 8243,\n    'lsaquo': 8249,\n    'rsaquo': 8250,\n    'oline': 8254,\n    'frasl': 8260,\n    'euro': 8364,\n    'image': 8465,\n    'weierp': 8472,\n    'real': 8476,\n    'trade': 8482,\n    'alefsym': 8501,\n    'larr': 8592,\n    'uarr': 8593,\n    'rarr': 8594,\n    'darr': 8595,\n    'harr': 8596,\n    'crarr': 8629,\n    'lArr': 8656,\n    'uArr': 8657,\n    'rArr': 8658,\n    'dArr': 8659,\n    'hArr': 8660,\n    'forall': 8704,\n    'part': 8706,\n    'exist': 8707,\n    'empty': 8709,\n    'nabla': 8711,\n    'isin': 8712,\n    'notin': 8713,\n    'ni': 8715,\n    'prod': 8719,\n    'sum': 8721,\n    'minus': 8722,\n    'lowast': 8727,\n    'radic': 8730,\n    'prop': 8733,\n    'infin': 8734,\n    'ang': 8736,\n    'and': 8743,\n    'or': 8744,\n    'cap': 8745,\n    'cup': 8746,\n    'int': 8747,\n    'there4': 8756,\n    'sim': 8764,\n    'cong': 8773,\n    'asymp': 8776,\n    'ne': 8800,\n    'equiv': 8801,\n    'le': 8804,\n    'ge': 8805,\n    'sub': 8834,\n    'sup': 8835,\n    'nsub': 8836,\n    'sube': 8838,\n    'supe': 8839,\n    'oplus': 8853,\n    'otimes': 8855,\n    'perp': 8869,\n    'sdot': 8901,\n    'lceil': 8968,\n    'rceil': 8969,\n    'lfloor': 8970,\n    'rfloor': 8971,\n    'lang': 9001,\n    'rang': 9002,\n    'loz': 9674,\n    'spades': 9824,\n    'clubs': 9827,\n    'hearts': 9829,\n    'diams': 9830\n  }\n\n  Object.keys(sax.ENTITIES).forEach(function (key) {\n    var e = sax.ENTITIES[key]\n    var s = typeof e === 'number' ? String.fromCharCode(e) : e\n    sax.ENTITIES[key] = s\n  })\n\n  for (var s in sax.STATE) {\n    sax.STATE[sax.STATE[s]] = s\n  }\n\n  // shorthand\n  S = sax.STATE\n\n  function emit (parser, event, data) {\n    parser[event] && parser[event](data)\n  }\n\n  function emitNode (parser, nodeType, data) {\n    if (parser.textNode) closeText(parser)\n    emit(parser, nodeType, data)\n  }\n\n  function closeText (parser) {\n    parser.textNode = textopts(parser.opt, parser.textNode)\n    if (parser.textNode) emit(parser, 'ontext', parser.textNode)\n    parser.textNode = ''\n  }\n\n  function textopts (opt, text) {\n    if (opt.trim) text = text.trim()\n    if (opt.normalize) text = text.replace(/\\s+/g, ' ')\n    return text\n  }\n\n  function error (parser, er) {\n    closeText(parser)\n    if (parser.trackPosition) {\n      er += '\\nLine: ' + parser.line +\n        '\\nColumn: ' + parser.column +\n        '\\nChar: ' + parser.c\n    }\n    er = new Error(er)\n    parser.error = er\n    emit(parser, 'onerror', er)\n    return parser\n  }\n\n  function end (parser) {\n    if (parser.sawRoot && !parser.closedRoot) strictFail(parser, 'Unclosed root tag')\n    if ((parser.state !== S.BEGIN) &&\n      (parser.state !== S.BEGIN_WHITESPACE) &&\n      (parser.state !== S.TEXT)) {\n      error(parser, 'Unexpected end')\n    }\n    closeText(parser)\n    parser.c = ''\n    parser.closed = true\n    emit(parser, 'onend')\n    SAXParser.call(parser, parser.strict, parser.opt)\n    return parser\n  }\n\n  function strictFail (parser, message) {\n    if (typeof parser !== 'object' || !(parser instanceof SAXParser)) {\n      throw new Error('bad call to strictFail')\n    }\n    if (parser.strict) {\n      error(parser, message)\n    }\n  }\n\n  function newTag (parser) {\n    if (!parser.strict) parser.tagName = parser.tagName[parser.looseCase]()\n    var parent = parser.tags[parser.tags.length - 1] || parser\n    var tag = parser.tag = { name: parser.tagName, attributes: {} }\n\n    // will be overridden if tag contails an xmlns=\"foo\" or xmlns:foo=\"bar\"\n    if (parser.opt.xmlns) {\n      tag.ns = parent.ns\n    }\n    parser.attribList.length = 0\n    emitNode(parser, 'onopentagstart', tag)\n  }\n\n  function qname (name, attribute) {\n    var i = name.indexOf(':')\n    var qualName = i < 0 ? [ '', name ] : name.split(':')\n    var prefix = qualName[0]\n    var local = qualName[1]\n\n    // <x \"xmlns\"=\"http://foo\">\n    if (attribute && name === 'xmlns') {\n      prefix = 'xmlns'\n      local = ''\n    }\n\n    return { prefix: prefix, local: local }\n  }\n\n  function attrib (parser) {\n    if (!parser.strict) {\n      parser.attribName = parser.attribName[parser.looseCase]()\n    }\n\n    if (parser.attribList.indexOf(parser.attribName) !== -1 ||\n      parser.tag.attributes.hasOwnProperty(parser.attribName)) {\n      parser.attribName = parser.attribValue = ''\n      return\n    }\n\n    if (parser.opt.xmlns) {\n      var qn = qname(parser.attribName, true)\n      var prefix = qn.prefix\n      var local = qn.local\n\n      if (prefix === 'xmlns') {\n        // namespace binding attribute. push the binding into scope\n        if (local === 'xml' && parser.attribValue !== XML_NAMESPACE) {\n          strictFail(parser,\n            'xml: prefix must be bound to ' + XML_NAMESPACE + '\\n' +\n            'Actual: ' + parser.attribValue)\n        } else if (local === 'xmlns' && parser.attribValue !== XMLNS_NAMESPACE) {\n          strictFail(parser,\n            'xmlns: prefix must be bound to ' + XMLNS_NAMESPACE + '\\n' +\n            'Actual: ' + parser.attribValue)\n        } else {\n          var tag = parser.tag\n          var parent = parser.tags[parser.tags.length - 1] || parser\n          if (tag.ns === parent.ns) {\n            tag.ns = Object.create(parent.ns)\n          }\n          tag.ns[local] = parser.attribValue\n        }\n      }\n\n      // defer onattribute events until all attributes have been seen\n      // so any new bindings can take effect. preserve attribute order\n      // so deferred events can be emitted in document order\n      parser.attribList.push([parser.attribName, parser.attribValue])\n    } else {\n      // in non-xmlns mode, we can emit the event right away\n      parser.tag.attributes[parser.attribName] = parser.attribValue\n      emitNode(parser, 'onattribute', {\n        name: parser.attribName,\n        value: parser.attribValue\n      })\n    }\n\n    parser.attribName = parser.attribValue = ''\n  }\n\n  function openTag (parser, selfClosing) {\n    if (parser.opt.xmlns) {\n      // emit namespace binding events\n      var tag = parser.tag\n\n      // add namespace info to tag\n      var qn = qname(parser.tagName)\n      tag.prefix = qn.prefix\n      tag.local = qn.local\n      tag.uri = tag.ns[qn.prefix] || ''\n\n      if (tag.prefix && !tag.uri) {\n        strictFail(parser, 'Unbound namespace prefix: ' +\n          JSON.stringify(parser.tagName))\n        tag.uri = qn.prefix\n      }\n\n      var parent = parser.tags[parser.tags.length - 1] || parser\n      if (tag.ns && parent.ns !== tag.ns) {\n        Object.keys(tag.ns).forEach(function (p) {\n          emitNode(parser, 'onopennamespace', {\n            prefix: p,\n            uri: tag.ns[p]\n          })\n        })\n      }\n\n      // handle deferred onattribute events\n      // Note: do not apply default ns to attributes:\n      //   http://www.w3.org/TR/REC-xml-names/#defaulting\n      for (var i = 0, l = parser.attribList.length; i < l; i++) {\n        var nv = parser.attribList[i]\n        var name = nv[0]\n        var value = nv[1]\n        var qualName = qname(name, true)\n        var prefix = qualName.prefix\n        var local = qualName.local\n        var uri = prefix === '' ? '' : (tag.ns[prefix] || '')\n        var a = {\n          name: name,\n          value: value,\n          prefix: prefix,\n          local: local,\n          uri: uri\n        }\n\n        // if there's any attributes with an undefined namespace,\n        // then fail on them now.\n        if (prefix && prefix !== 'xmlns' && !uri) {\n          strictFail(parser, 'Unbound namespace prefix: ' +\n            JSON.stringify(prefix))\n          a.uri = prefix\n        }\n        parser.tag.attributes[name] = a\n        emitNode(parser, 'onattribute', a)\n      }\n      parser.attribList.length = 0\n    }\n\n    parser.tag.isSelfClosing = !!selfClosing\n\n    // process the tag\n    parser.sawRoot = true\n    parser.tags.push(parser.tag)\n    emitNode(parser, 'onopentag', parser.tag)\n    if (!selfClosing) {\n      // special case for <script> in non-strict mode.\n      if (!parser.noscript && parser.tagName.toLowerCase() === 'script') {\n        parser.state = S.SCRIPT\n      } else {\n        parser.state = S.TEXT\n      }\n      parser.tag = null\n      parser.tagName = ''\n    }\n    parser.attribName = parser.attribValue = ''\n    parser.attribList.length = 0\n  }\n\n  function closeTag (parser) {\n    if (!parser.tagName) {\n      strictFail(parser, 'Weird empty close tag.')\n      parser.textNode += '</>'\n      parser.state = S.TEXT\n      return\n    }\n\n    if (parser.script) {\n      if (parser.tagName !== 'script') {\n        parser.script += '</' + parser.tagName + '>'\n        parser.tagName = ''\n        parser.state = S.SCRIPT\n        return\n      }\n      emitNode(parser, 'onscript', parser.script)\n      parser.script = ''\n    }\n\n    // first make sure that the closing tag actually exists.\n    // <a><b></c></b></a> will close everything, otherwise.\n    var t = parser.tags.length\n    var tagName = parser.tagName\n    if (!parser.strict) {\n      tagName = tagName[parser.looseCase]()\n    }\n    var closeTo = tagName\n    while (t--) {\n      var close = parser.tags[t]\n      if (close.name !== closeTo) {\n        // fail the first time in strict mode\n        strictFail(parser, 'Unexpected close tag')\n      } else {\n        break\n      }\n    }\n\n    // didn't find it.  we already failed for strict, so just abort.\n    if (t < 0) {\n      strictFail(parser, 'Unmatched closing tag: ' + parser.tagName)\n      parser.textNode += '</' + parser.tagName + '>'\n      parser.state = S.TEXT\n      return\n    }\n    parser.tagName = tagName\n    var s = parser.tags.length\n    while (s-- > t) {\n      var tag = parser.tag = parser.tags.pop()\n      parser.tagName = parser.tag.name\n      emitNode(parser, 'onclosetag', parser.tagName)\n\n      var x = {}\n      for (var i in tag.ns) {\n        x[i] = tag.ns[i]\n      }\n\n      var parent = parser.tags[parser.tags.length - 1] || parser\n      if (parser.opt.xmlns && tag.ns !== parent.ns) {\n        // remove namespace bindings introduced by tag\n        Object.keys(tag.ns).forEach(function (p) {\n          var n = tag.ns[p]\n          emitNode(parser, 'onclosenamespace', { prefix: p, uri: n })\n        })\n      }\n    }\n    if (t === 0) parser.closedRoot = true\n    parser.tagName = parser.attribValue = parser.attribName = ''\n    parser.attribList.length = 0\n    parser.state = S.TEXT\n  }\n\n  function parseEntity (parser) {\n    var entity = parser.entity\n    var entityLC = entity.toLowerCase()\n    var num\n    var numStr = ''\n\n    if (parser.ENTITIES[entity]) {\n      return parser.ENTITIES[entity]\n    }\n    if (parser.ENTITIES[entityLC]) {\n      return parser.ENTITIES[entityLC]\n    }\n    entity = entityLC\n    if (entity.charAt(0) === '#') {\n      if (entity.charAt(1) === 'x') {\n        entity = entity.slice(2)\n        num = parseInt(entity, 16)\n        numStr = num.toString(16)\n      } else {\n        entity = entity.slice(1)\n        num = parseInt(entity, 10)\n        numStr = num.toString(10)\n      }\n    }\n    entity = entity.replace(/^0+/, '')\n    if (isNaN(num) || numStr.toLowerCase() !== entity) {\n      strictFail(parser, 'Invalid character entity')\n      return '&' + parser.entity + ';'\n    }\n\n    return String.fromCodePoint(num)\n  }\n\n  function beginWhiteSpace (parser, c) {\n    if (c === '<') {\n      parser.state = S.OPEN_WAKA\n      parser.startTagPosition = parser.position\n    } else if (!isWhitespace(c)) {\n      // have to process this as a text node.\n      // weird, but happens.\n      strictFail(parser, 'Non-whitespace before first tag.')\n      parser.textNode = c\n      parser.state = S.TEXT\n    }\n  }\n\n  function charAt (chunk, i) {\n    var result = ''\n    if (i < chunk.length) {\n      result = chunk.charAt(i)\n    }\n    return result\n  }\n\n  function write (chunk) {\n    var parser = this\n    if (this.error) {\n      throw this.error\n    }\n    if (parser.closed) {\n      return error(parser,\n        'Cannot write after close. Assign an onready handler.')\n    }\n    if (chunk === null) {\n      return end(parser)\n    }\n    if (typeof chunk === 'object') {\n      chunk = chunk.toString()\n    }\n    var i = 0\n    var c = ''\n    while (true) {\n      c = charAt(chunk, i++)\n      parser.c = c\n\n      if (!c) {\n        break\n      }\n\n      if (parser.trackPosition) {\n        parser.position++\n        if (c === '\\n') {\n          parser.line++\n          parser.column = 0\n        } else {\n          parser.column++\n        }\n      }\n\n      switch (parser.state) {\n        case S.BEGIN:\n          parser.state = S.BEGIN_WHITESPACE\n          if (c === '\\uFEFF') {\n            continue\n          }\n          beginWhiteSpace(parser, c)\n          continue\n\n        case S.BEGIN_WHITESPACE:\n          beginWhiteSpace(parser, c)\n          continue\n\n        case S.TEXT:\n          if (parser.sawRoot && !parser.closedRoot) {\n            var starti = i - 1\n            while (c && c !== '<' && c !== '&') {\n              c = charAt(chunk, i++)\n              if (c && parser.trackPosition) {\n                parser.position++\n                if (c === '\\n') {\n                  parser.line++\n                  parser.column = 0\n                } else {\n                  parser.column++\n                }\n              }\n            }\n            parser.textNode += chunk.substring(starti, i - 1)\n          }\n          if (c === '<' && !(parser.sawRoot && parser.closedRoot && !parser.strict)) {\n            parser.state = S.OPEN_WAKA\n            parser.startTagPosition = parser.position\n          } else {\n            if (!isWhitespace(c) && (!parser.sawRoot || parser.closedRoot)) {\n              strictFail(parser, 'Text data outside of root node.')\n            }\n            if (c === '&') {\n              parser.state = S.TEXT_ENTITY\n            } else {\n              parser.textNode += c\n            }\n          }\n          continue\n\n        case S.SCRIPT:\n          // only non-strict\n          if (c === '<') {\n            parser.state = S.SCRIPT_ENDING\n          } else {\n            parser.script += c\n          }\n          continue\n\n        case S.SCRIPT_ENDING:\n          if (c === '/') {\n            parser.state = S.CLOSE_TAG\n          } else {\n            parser.script += '<' + c\n            parser.state = S.SCRIPT\n          }\n          continue\n\n        case S.OPEN_WAKA:\n          // either a /, ?, !, or text is coming next.\n          if (c === '!') {\n            parser.state = S.SGML_DECL\n            parser.sgmlDecl = ''\n          } else if (isWhitespace(c)) {\n            // wait for it...\n          } else if (isMatch(nameStart, c)) {\n            parser.state = S.OPEN_TAG\n            parser.tagName = c\n          } else if (c === '/') {\n            parser.state = S.CLOSE_TAG\n            parser.tagName = ''\n          } else if (c === '?') {\n            parser.state = S.PROC_INST\n            parser.procInstName = parser.procInstBody = ''\n          } else {\n            strictFail(parser, 'Unencoded <')\n            // if there was some whitespace, then add that in.\n            if (parser.startTagPosition + 1 < parser.position) {\n              var pad = parser.position - parser.startTagPosition\n              c = new Array(pad).join(' ') + c\n            }\n            parser.textNode += '<' + c\n            parser.state = S.TEXT\n          }\n          continue\n\n        case S.SGML_DECL:\n          if (parser.sgmlDecl + c === '--') {\n            parser.state = S.COMMENT\n            parser.comment = ''\n            parser.sgmlDecl = ''\n            continue;\n          }\n\n          if (parser.doctype && parser.doctype !== true && parser.sgmlDecl) {\n            parser.state = S.DOCTYPE_DTD\n            parser.doctype += '<!' + parser.sgmlDecl + c\n            parser.sgmlDecl = ''\n          } else if ((parser.sgmlDecl + c).toUpperCase() === CDATA) {\n            emitNode(parser, 'onopencdata')\n            parser.state = S.CDATA\n            parser.sgmlDecl = ''\n            parser.cdata = ''\n          } else if ((parser.sgmlDecl + c).toUpperCase() === DOCTYPE) {\n            parser.state = S.DOCTYPE\n            if (parser.doctype || parser.sawRoot) {\n              strictFail(parser,\n                'Inappropriately located doctype declaration')\n            }\n            parser.doctype = ''\n            parser.sgmlDecl = ''\n          } else if (c === '>') {\n            emitNode(parser, 'onsgmldeclaration', parser.sgmlDecl)\n            parser.sgmlDecl = ''\n            parser.state = S.TEXT\n          } else if (isQuote(c)) {\n            parser.state = S.SGML_DECL_QUOTED\n            parser.sgmlDecl += c\n          } else {\n            parser.sgmlDecl += c\n          }\n          continue\n\n        case S.SGML_DECL_QUOTED:\n          if (c === parser.q) {\n            parser.state = S.SGML_DECL\n            parser.q = ''\n          }\n          parser.sgmlDecl += c\n          continue\n\n        case S.DOCTYPE:\n          if (c === '>') {\n            parser.state = S.TEXT\n            emitNode(parser, 'ondoctype', parser.doctype)\n            parser.doctype = true // just remember that we saw it.\n          } else {\n            parser.doctype += c\n            if (c === '[') {\n              parser.state = S.DOCTYPE_DTD\n            } else if (isQuote(c)) {\n              parser.state = S.DOCTYPE_QUOTED\n              parser.q = c\n            }\n          }\n          continue\n\n        case S.DOCTYPE_QUOTED:\n          parser.doctype += c\n          if (c === parser.q) {\n            parser.q = ''\n            parser.state = S.DOCTYPE\n          }\n          continue\n\n        case S.DOCTYPE_DTD:\n          if (c === ']') {\n            parser.doctype += c\n            parser.state = S.DOCTYPE\n          } else if (c === '<') {\n            parser.state = S.OPEN_WAKA\n            parser.startTagPosition = parser.position\n          } else if (isQuote(c)) {\n            parser.doctype += c\n            parser.state = S.DOCTYPE_DTD_QUOTED\n            parser.q = c\n          } else {\n            parser.doctype += c\n          }\n          continue\n\n        case S.DOCTYPE_DTD_QUOTED:\n          parser.doctype += c\n          if (c === parser.q) {\n            parser.state = S.DOCTYPE_DTD\n            parser.q = ''\n          }\n          continue\n\n        case S.COMMENT:\n          if (c === '-') {\n            parser.state = S.COMMENT_ENDING\n          } else {\n            parser.comment += c\n          }\n          continue\n\n        case S.COMMENT_ENDING:\n          if (c === '-') {\n            parser.state = S.COMMENT_ENDED\n            parser.comment = textopts(parser.opt, parser.comment)\n            if (parser.comment) {\n              emitNode(parser, 'oncomment', parser.comment)\n            }\n            parser.comment = ''\n          } else {\n            parser.comment += '-' + c\n            parser.state = S.COMMENT\n          }\n          continue\n\n        case S.COMMENT_ENDED:\n          if (c !== '>') {\n            strictFail(parser, 'Malformed comment')\n            // allow <!-- blah -- bloo --> in non-strict mode,\n            // which is a comment of \" blah -- bloo \"\n            parser.comment += '--' + c\n            parser.state = S.COMMENT\n          } else if (parser.doctype && parser.doctype !== true) {\n            parser.state = S.DOCTYPE_DTD\n          } else {\n            parser.state = S.TEXT\n          }\n          continue\n\n        case S.CDATA:\n          if (c === ']') {\n            parser.state = S.CDATA_ENDING\n          } else {\n            parser.cdata += c\n          }\n          continue\n\n        case S.CDATA_ENDING:\n          if (c === ']') {\n            parser.state = S.CDATA_ENDING_2\n          } else {\n            parser.cdata += ']' + c\n            parser.state = S.CDATA\n          }\n          continue\n\n        case S.CDATA_ENDING_2:\n          if (c === '>') {\n            if (parser.cdata) {\n              emitNode(parser, 'oncdata', parser.cdata)\n            }\n            emitNode(parser, 'onclosecdata')\n            parser.cdata = ''\n            parser.state = S.TEXT\n          } else if (c === ']') {\n            parser.cdata += ']'\n          } else {\n            parser.cdata += ']]' + c\n            parser.state = S.CDATA\n          }\n          continue\n\n        case S.PROC_INST:\n          if (c === '?') {\n            parser.state = S.PROC_INST_ENDING\n          } else if (isWhitespace(c)) {\n            parser.state = S.PROC_INST_BODY\n          } else {\n            parser.procInstName += c\n          }\n          continue\n\n        case S.PROC_INST_BODY:\n          if (!parser.procInstBody && isWhitespace(c)) {\n            continue\n          } else if (c === '?') {\n            parser.state = S.PROC_INST_ENDING\n          } else {\n            parser.procInstBody += c\n          }\n          continue\n\n        case S.PROC_INST_ENDING:\n          if (c === '>') {\n            emitNode(parser, 'onprocessinginstruction', {\n              name: parser.procInstName,\n              body: parser.procInstBody\n            })\n            parser.procInstName = parser.procInstBody = ''\n            parser.state = S.TEXT\n          } else {\n            parser.procInstBody += '?' + c\n            parser.state = S.PROC_INST_BODY\n          }\n          continue\n\n        case S.OPEN_TAG:\n          if (isMatch(nameBody, c)) {\n            parser.tagName += c\n          } else {\n            newTag(parser)\n            if (c === '>') {\n              openTag(parser)\n            } else if (c === '/') {\n              parser.state = S.OPEN_TAG_SLASH\n            } else {\n              if (!isWhitespace(c)) {\n                strictFail(parser, 'Invalid character in tag name')\n              }\n              parser.state = S.ATTRIB\n            }\n          }\n          continue\n\n        case S.OPEN_TAG_SLASH:\n          if (c === '>') {\n            openTag(parser, true)\n            closeTag(parser)\n          } else {\n            strictFail(parser, 'Forward-slash in opening tag not followed by >')\n            parser.state = S.ATTRIB\n          }\n          continue\n\n        case S.ATTRIB:\n          // haven't read the attribute name yet.\n          if (isWhitespace(c)) {\n            continue\n          } else if (c === '>') {\n            openTag(parser)\n          } else if (c === '/') {\n            parser.state = S.OPEN_TAG_SLASH\n          } else if (isMatch(nameStart, c)) {\n            parser.attribName = c\n            parser.attribValue = ''\n            parser.state = S.ATTRIB_NAME\n          } else {\n            strictFail(parser, 'Invalid attribute name')\n          }\n          continue\n\n        case S.ATTRIB_NAME:\n          if (c === '=') {\n            parser.state = S.ATTRIB_VALUE\n          } else if (c === '>') {\n            strictFail(parser, 'Attribute without value')\n            parser.attribValue = parser.attribName\n            attrib(parser)\n            openTag(parser)\n          } else if (isWhitespace(c)) {\n            parser.state = S.ATTRIB_NAME_SAW_WHITE\n          } else if (isMatch(nameBody, c)) {\n            parser.attribName += c\n          } else {\n            strictFail(parser, 'Invalid attribute name')\n          }\n          continue\n\n        case S.ATTRIB_NAME_SAW_WHITE:\n          if (c === '=') {\n            parser.state = S.ATTRIB_VALUE\n          } else if (isWhitespace(c)) {\n            continue\n          } else {\n            strictFail(parser, 'Attribute without value')\n            parser.tag.attributes[parser.attribName] = ''\n            parser.attribValue = ''\n            emitNode(parser, 'onattribute', {\n              name: parser.attribName,\n              value: ''\n            })\n            parser.attribName = ''\n            if (c === '>') {\n              openTag(parser)\n            } else if (isMatch(nameStart, c)) {\n              parser.attribName = c\n              parser.state = S.ATTRIB_NAME\n            } else {\n              strictFail(parser, 'Invalid attribute name')\n              parser.state = S.ATTRIB\n            }\n          }\n          continue\n\n        case S.ATTRIB_VALUE:\n          if (isWhitespace(c)) {\n            continue\n          } else if (isQuote(c)) {\n            parser.q = c\n            parser.state = S.ATTRIB_VALUE_QUOTED\n          } else {\n            if (!parser.opt.unquotedAttributeValues) {\n              error(parser, 'Unquoted attribute value')\n            }\n            parser.state = S.ATTRIB_VALUE_UNQUOTED\n            parser.attribValue = c\n          }\n          continue\n\n        case S.ATTRIB_VALUE_QUOTED:\n          if (c !== parser.q) {\n            if (c === '&') {\n              parser.state = S.ATTRIB_VALUE_ENTITY_Q\n            } else {\n              parser.attribValue += c\n            }\n            continue\n          }\n          attrib(parser)\n          parser.q = ''\n          parser.state = S.ATTRIB_VALUE_CLOSED\n          continue\n\n        case S.ATTRIB_VALUE_CLOSED:\n          if (isWhitespace(c)) {\n            parser.state = S.ATTRIB\n          } else if (c === '>') {\n            openTag(parser)\n          } else if (c === '/') {\n            parser.state = S.OPEN_TAG_SLASH\n          } else if (isMatch(nameStart, c)) {\n            strictFail(parser, 'No whitespace between attributes')\n            parser.attribName = c\n            parser.attribValue = ''\n            parser.state = S.ATTRIB_NAME\n          } else {\n            strictFail(parser, 'Invalid attribute name')\n          }\n          continue\n\n        case S.ATTRIB_VALUE_UNQUOTED:\n          if (!isAttribEnd(c)) {\n            if (c === '&') {\n              parser.state = S.ATTRIB_VALUE_ENTITY_U\n            } else {\n              parser.attribValue += c\n            }\n            continue\n          }\n          attrib(parser)\n          if (c === '>') {\n            openTag(parser)\n          } else {\n            parser.state = S.ATTRIB\n          }\n          continue\n\n        case S.CLOSE_TAG:\n          if (!parser.tagName) {\n            if (isWhitespace(c)) {\n              continue\n            } else if (notMatch(nameStart, c)) {\n              if (parser.script) {\n                parser.script += '</' + c\n                parser.state = S.SCRIPT\n              } else {\n                strictFail(parser, 'Invalid tagname in closing tag.')\n              }\n            } else {\n              parser.tagName = c\n            }\n          } else if (c === '>') {\n            closeTag(parser)\n          } else if (isMatch(nameBody, c)) {\n            parser.tagName += c\n          } else if (parser.script) {\n            parser.script += '</' + parser.tagName\n            parser.tagName = ''\n            parser.state = S.SCRIPT\n          } else {\n            if (!isWhitespace(c)) {\n              strictFail(parser, 'Invalid tagname in closing tag')\n            }\n            parser.state = S.CLOSE_TAG_SAW_WHITE\n          }\n          continue\n\n        case S.CLOSE_TAG_SAW_WHITE:\n          if (isWhitespace(c)) {\n            continue\n          }\n          if (c === '>') {\n            closeTag(parser)\n          } else {\n            strictFail(parser, 'Invalid characters in closing tag')\n          }\n          continue\n\n        case S.TEXT_ENTITY:\n        case S.ATTRIB_VALUE_ENTITY_Q:\n        case S.ATTRIB_VALUE_ENTITY_U:\n          var returnState\n          var buffer\n          switch (parser.state) {\n            case S.TEXT_ENTITY:\n              returnState = S.TEXT\n              buffer = 'textNode'\n              break\n\n            case S.ATTRIB_VALUE_ENTITY_Q:\n              returnState = S.ATTRIB_VALUE_QUOTED\n              buffer = 'attribValue'\n              break\n\n            case S.ATTRIB_VALUE_ENTITY_U:\n              returnState = S.ATTRIB_VALUE_UNQUOTED\n              buffer = 'attribValue'\n              break\n          }\n\n          if (c === ';') {\n            var parsedEntity = parseEntity(parser)\n            if (parser.opt.unparsedEntities && !Object.values(sax.XML_ENTITIES).includes(parsedEntity)) {\n              parser.entity = ''\n              parser.state = returnState\n              parser.write(parsedEntity)\n            } else {\n              parser[buffer] += parsedEntity\n              parser.entity = ''\n              parser.state = returnState\n            }\n          } else if (isMatch(parser.entity.length ? entityBody : entityStart, c)) {\n            parser.entity += c\n          } else {\n            strictFail(parser, 'Invalid character in entity name')\n            parser[buffer] += '&' + parser.entity + c\n            parser.entity = ''\n            parser.state = returnState\n          }\n\n          continue\n\n        default: /* istanbul ignore next */ {\n          throw new Error(parser, 'Unknown state: ' + parser.state)\n        }\n      }\n    } // while\n\n    if (parser.position >= parser.bufferCheckPosition) {\n      checkBufferLength(parser)\n    }\n    return parser\n  }\n\n  /*! http://mths.be/fromcodepoint v0.1.0 by @mathias */\n  /* istanbul ignore next */\n  if (!String.fromCodePoint) {\n    (function () {\n      var stringFromCharCode = String.fromCharCode\n      var floor = Math.floor\n      var fromCodePoint = function () {\n        var MAX_SIZE = 0x4000\n        var codeUnits = []\n        var highSurrogate\n        var lowSurrogate\n        var index = -1\n        var length = arguments.length\n        if (!length) {\n          return ''\n        }\n        var result = ''\n        while (++index < length) {\n          var codePoint = Number(arguments[index])\n          if (\n            !isFinite(codePoint) || // `NaN`, `+Infinity`, or `-Infinity`\n            codePoint < 0 || // not a valid Unicode code point\n            codePoint > 0x10FFFF || // not a valid Unicode code point\n            floor(codePoint) !== codePoint // not an integer\n          ) {\n            throw RangeError('Invalid code point: ' + codePoint)\n          }\n          if (codePoint <= 0xFFFF) { // BMP code point\n            codeUnits.push(codePoint)\n          } else { // Astral code point; split in surrogate halves\n            // http://mathiasbynens.be/notes/javascript-encoding#surrogate-formulae\n            codePoint -= 0x10000\n            highSurrogate = (codePoint >> 10) + 0xD800\n            lowSurrogate = (codePoint % 0x400) + 0xDC00\n            codeUnits.push(highSurrogate, lowSurrogate)\n          }\n          if (index + 1 === length || codeUnits.length > MAX_SIZE) {\n            result += stringFromCharCode.apply(null, codeUnits)\n            codeUnits.length = 0\n          }\n        }\n        return result\n      }\n      /* istanbul ignore next */\n      if (Object.defineProperty) {\n        Object.defineProperty(String, 'fromCodePoint', {\n          value: fromCodePoint,\n          configurable: true,\n          writable: true\n        })\n      } else {\n        String.fromCodePoint = fromCodePoint\n      }\n    }())\n  }\n})(typeof exports === 'undefined' ? this.sax = {} : exports)\n"]}