{"version": 3, "sources": ["index.js", "bitmapimage.js", "gif.js", "gifcodec.js", "gifutil.js", "gifframe.js"], "names": [], "mappings": ";;;;;;;AAAA;AACA;AACA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ACHA,AFMA;ACFA,ACHA,AFMA;ACFA,ACHA,AFMA;ACFA,ACHA,ACHA,AHSA;ACFA,ACHA,ACHA,AHSA;ACFA,ACHA,ACHA,AHSA;ACFA,ACHA,ACHA,ACHA,AJYA;ACFA,ACHA,ACHA,ACHA,AJYA;ACFA,ACHA,ACHA,ACHA,AJYA;ACFA,ACHA,ACHA,AENA,ADGA,AJYA;ACFA,ACHA,ACHA,AENA,ADGA,AJYA;ACFA,ACHA,ACHA,AENA,ADGA;AHUA,ACHA,ACHA,AENA,ADGA;AHUA,ACHA,ACHA,AENA,ADGA;AHUA,ACHA,ACHA,AENA,ADGA;AHUA,ACHA,ACHA,AENA,ADGA;AHUA,ACHA,ACHA,AENA,ADGA;AHUA,ACHA,ACHA,AENA,ADGA;AHUA,ACHA,ACHA,AENA,ADGA;AHUA,ACHA,ACHA,AENA,ADGA;AHUA,ACHA,ACHA,AENA,ADGA;AHUA,ACHA,ACHA,AENA,ADGA;AHUA,ACHA,ACHA,AENA,ADGA;AHUA,ACHA,ACHA,AENA,ADGA;AHUA,ACHA,ACHA,AENA,ADGA;AHUA,ACHA,ACHA,AENA,ADGA;AHUA,ACHA,ACHA,AENA,ADGA;AHUA,ACHA,ACHA,AENA,ADGA;AHUA,ACHA,ACHA,AENA,ADGA;AHUA,ACHA,ACHA,AENA,ADGA;AHUA,ACHA,ACHA,AENA,ADGA;AHUA,ACHA,ACHA,AENA,ADGA;AHUA,ACHA,ACHA,AENA,ADGA;AHUA,ACHA,ACHA,AENA,ADGA;AHUA,ACHA,ACHA,AENA,ADGA;AHUA,ACHA,ACHA,AENA,ADGA;AHUA,ACHA,ACHA,AENA,ADGA;AHUA,ACHA,ACHA,AENA,ADGA;AHUA,ACHA,ACHA,AENA,ADGA;AHUA,ACHA,ACHA,AENA,ADGA;AHUA,ACHA,ACHA,AENA,ADGA;AHUA,ACHA,ACHA,AENA,ADGA;AHUA,ACHA,ACHA,AENA,ADGA;AHUA,ACHA,ACHA,AENA,ADGA;AHUA,ACHA,ACHA,AENA,ADGA;AHUA,ACHA,ACHA,AENA,ADGA;AHUA,ACHA,ACHA,AENA,ADGA;AHUA,ACHA,ACHA,AENA,ADGA;AHUA,ACHA,ACHA,AENA,ADGA;AHUA,ACHA,ACHA,AENA,ADGA;AHUA,ACHA,ACHA,AENA,ADGA;AHUA,ACHA,ACHA,AENA,ADGA;AHUA,ACHA,ACHA,AENA,ADGA;AHUA,ACHA,ACHA,AENA,ADGA;AHUA,ACHA,ACHA,AENA,ADGA;AHUA,ACHA,ACHA,AENA,ADGA;AHUA,ACHA,ACHA,AENA,ADGA;AHUA,ACHA,ACHA,AENA,ADGA;AHUA,ACHA,ACHA,AENA,ADGA;AHUA,ACHA,ACHA,AENA,ADGA;AHUA,ACHA,ACHA,AENA,ADGA;AHUA,ACHA,ACHA,AENA,ADGA;AHUA,ACHA,ACHA,AENA,ADGA;AHUA,ACHA,ACHA,AENA,ADGA;AHUA,ACHA,ACHA,AENA,ADGA;AHUA,ACHA,ACHA,AENA,ADGA;AHUA,ACHA,ACHA,AENA,ADGA;AHUA,ACHA,ACHA,AENA,ADGA;AHUA,ACHA,ACHA,AENA,ADGA;AHUA,ACHA,ACHA,AENA,ADGA;AHUA,AENA,AENA,ADGA;AHUA,AENA,AENA,ADGA;AHUA,AENA,AENA,ADGA;AHUA,AENA,AENA,ADGA;AHUA,AENA,AENA,ADGA;AHUA,AENA,AENA,ADGA;AHUA,AENA,AENA,ADGA;AHUA,AENA,AENA,ADGA;AHUA,AENA,AENA,ADGA;AHUA,AENA,AENA,ADGA;AHUA,AENA,AENA,ADGA;AHUA,AENA,AENA,ADGA;AHUA,AENA,AENA,ADGA;AHUA,AENA,AENA,ADGA;AHUA,AENA,AENA,ADGA;AHUA,AENA,AENA,ADGA;AHUA,AENA,AENA,ADGA;AHUA,AENA,AENA,ADGA;AHUA,AENA,AENA,ADGA;AHUA,AENA,AENA,ADGA;AHUA,AENA,AENA,ADGA;AHUA,AENA,AENA,ADGA;AHUA,AENA,AENA,ADGA;AHUA,AENA,AENA,ADGA;AHUA,AENA,AENA,ADGA;AHUA,AENA,AENA,ADGA;AHUA,AENA,AENA,ADGA;AHUA,AENA,AENA,ADGA;AHUA,AENA,AENA,ADGA;AHUA,AENA,AENA,ADGA;AHUA,AENA,AENA,ADGA;AHUA,AENA,AENA,ADGA;AHUA,AENA,AENA,ADGA;AHUA,AENA,AENA,ADGA;AHUA,AENA,AENA,ADGA;AHUA,AENA,AENA,ADGA;AHUA,AENA,AENA,ADGA;AHUA,AENA,AENA,ADGA;AHUA,AENA,AENA,ADGA;AHUA,AENA,AENA,ADGA;AHUA,AENA,AENA,ADGA;AHUA,AENA,AENA,ADGA;AHUA,AENA,AENA,ADGA;AHUA,AENA,AENA,ADGA;AHUA,AENA,AENA,ADGA;AHUA,AENA,AENA,ADGA;AHUA,AENA,AENA,ADGA;AHUA,AENA,AENA,ADGA;AHUA,AENA,AENA,ADGA;AHUA,AENA,AENA,ADGA;AHUA,AENA,AENA,ADGA;AHUA,AENA,AENA,ADGA;AHUA,AENA,AENA,ADGA;AHUA,AENA,AENA,ADGA;AHUA,AENA,ACHA;AHUA,AENA,ACHA;AHUA,AENA,ACHA;AHUA,AENA,ACHA;AHUA,AENA,ACHA;AHUA,AENA,ACHA;AHUA,AENA,ACHA;AHUA,AENA,ACHA;AHUA,AENA,ACHA;AHUA,AENA,ACHA;AHUA,AENA,ACHA;AHUA,AENA,ACHA;AHUA,AENA,ACHA;AHUA,AENA,ACHA;AHUA,AENA,ACHA;AHUA,AENA,ACHA;AHUA,AENA,ACHA;AHUA,AENA,ACHA;AHUA,AENA,ACHA;AHUA,AENA,ACHA;AHUA,AENA,ACHA;AHUA,AENA,ACHA;AHUA,AENA,ACHA;AHUA,AENA,ACHA;AHUA,AENA,ACHA;AHUA,AENA,ACHA;AHUA,AENA,ACHA;AHUA,AENA,ACHA;AHUA,AENA,ACHA;AHUA,AENA,ACHA;AHUA,AENA,ACHA;AHUA,AENA,ACHA;AHUA,AENA,ACHA;AHUA,AENA,ACHA;AHUA,AENA,ACHA;AHUA,AENA,ACHA;AHUA,AENA,ACHA;AHUA,AENA,ACHA;AHUA,AENA,ACHA;AHUA,AENA,ACHA;AHUA,AENA,ACHA;AHUA,AENA,ACHA;AHUA,AENA,ACHA;AHUA,AENA,ACHA;AHUA,AENA,ACHA;AHUA,AENA,ACHA;AHUA,AENA,ACHA;AHUA,AENA,ACHA;AHUA,AENA,ACHA;AHUA,AENA,ACHA;AHUA,AENA,ACHA;AHUA,AENA,ACHA;AHUA,AENA,ACHA;AHUA,AENA,ACHA;AHUA,AENA,ACHA;AHUA,AENA,ACHA;AHUA,AENA,ACHA;AHUA,AENA,ACHA;AHUA,AENA,ACHA;AHUA,AENA,ACHA;AHUA,AENA,ACHA;AHUA,AENA,ACHA;AHUA,AENA,ACHA;AHUA,AENA,ACHA;AHUA,AENA,ACHA;AHUA,AENA,ACHA;AHUA,AENA,ACHA;AHUA,AENA,ACHA;AHUA,AENA,ACHA;AHUA,AENA,ACHA;AHUA,AENA,ACHA;AHUA,AENA,ACHA;AHUA,AENA,ACHA;AHUA,AENA,ACHA;AHUA,AENA,ACHA;AHUA,AENA,ACHA;AHUA,AENA,ACHA;AHUA,AENA,ACHA;AHUA,AENA,ACHA;AHUA,AENA,ACHA;AHUA,AENA,ACHA;AHUA,AENA,ACHA;AHUA,AENA,ACHA;AHUA,AENA,ACHA;AHUA,AENA,ACHA;AHUA,AENA,ACHA;AHUA,AENA,ACHA;AHUA,AENA,ACHA;AHUA,AENA,ACHA;AHUA,AENA,ACHA;AHUA,AENA,ACHA;AHUA,AENA,ACHA;AHUA,AENA,ACHA;AHUA,AENA,ACHA;AHUA,AENA,ACHA;AHUA,AENA,ACHA;AHUA,AENA,ACHA;AHUA,AENA,ACHA;AHUA,AENA,ACHA;AHUA,AENA,ACHA;AHUA,AENA,ACHA;AHUA,AENA,ACHA;AHUA,AENA,ACHA;AHUA,AENA,ACHA;AHUA,AENA,ACHA;AHUA,AENA,ACHA;AHUA,AENA,ACHA;AHUA,AENA,ACHA;AHUA,AENA,ACHA;AHUA,AENA,ACHA;AHUA,AENA,ACHA;AHUA,AENA,ACHA;AHUA,AENA,ACHA;AHUA,AENA,ACHA;AHUA,AENA,ACHA;AHUA,AENA,ACHA;AHUA,AENA,ACHA;AHUA,AENA,ACHA;AHUA,AENA,ACHA;AHUA,AENA,ACHA;AHUA,AENA,ACHA;AHUA,AENA,ACHA;AHUA,AENA,ACHA;AHUA,AENA,ACHA;AHUA,AENA,ACHA;AHUA,AENA,ACHA;AHUA,AENA,ACHA;AHUA,AENA,ACHA;AHUA,AENA,ACHA;AHUA,AENA,ACHA;AHUA,AENA,ACHA;AHUA,AENA,ACHA;AHUA,AENA,ACHA;AHUA,AENA,ACHA;AHUA,AENA,ACHA;AHUA,AENA,ACHA;AHUA,AENA,ACHA;AHUA,AENA,ACHA;AHUA,AENA,ACHA;AHUA,AENA,ACHA;AHUA,AENA,ACHA;AHUA,AENA,ACHA;AHUA,AENA,ACHA;AHUA,AENA,ACHA;AHUA,AENA,ACHA;AHUA,AENA,ACHA;AHUA,AENA,ACHA;AHUA,AENA,ACHA;AHUA,AENA,ACHA;AHUA,AENA,ACHA;AHUA,AENA,ACHA;AHUA,AENA,ACHA;AHUA,AENA,ACHA;AHUA,AENA,ACHA;AHUA,AENA,ACHA;AHUA,AENA,ACHA;AHUA,AENA,ACHA;AHUA,AENA,ACHA;AHUA,AENA,ACHA;AHUA,AENA,ACHA;AHUA,AENA,ACHA;AHUA,AENA,ACHA;AHUA,AENA,ACHA;AHUA,AENA,ACHA;AHUA,AENA,ACHA;AHUA,AENA,ACHA;AHUA,AENA,ACHA;AHUA,AENA,ACHA;AHUA,AENA,ACHA;AHUA,AENA,ACHA;AHUA,AENA,ACHA;AHUA,AENA,ACHA;AHUA,AENA,ACHA;AHUA,AENA,ACHA;AHUA,AENA,ACHA;AHUA,AENA,ACHA;AHUA,AENA,ACHA;AHUA,AENA,ACHA;AHUA,AENA,ACHA;AHUA,AENA,ACHA;AHUA,AENA,ACHA;AHUA,AENA,ACHA;AHUA,AENA,ACHA;AHUA,AENA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.js", "sourcesContent": ["\n\nconst BitmapImage = require('./bitmapimage');\nconst { Gif, GifError } = require('./gif');\nconst { GifCodec } = require('./gifcodec');\nconst { GifFrame } = require('./gifframe');\nconst GifUtil = require('./gifutil');\n\nmodule.exports = {\n    BitmapImage,\n    Gif,\n    GifCodec,\n    GifFrame,\n    GifUtil,\n    GifError\n};\n", "\n\n/** @class BitmapImage */\n\nclass BitmapImage {\n\n    /**\n     * BitmapImage is a class that hold an RGBA (red, green, blue, alpha) representation of an image. It's shape is borrowed from the Jimp package to make it easy to transfer GIF image frames into Jimp and Jimp images into GIF image frames. Each instance has a `bitmap` property having the following properties:\n     * \n     * Property | Description\n     * --- | ---\n     * bitmap.width | width of image in pixels\n     * bitmap.height | height of image in pixels\n     * bitmap.data | a Buffer whose every four bytes represents a pixel, each sequential byte of a pixel corresponding to the red, green, blue, and alpha values of the pixel\n     *\n     * Its constructor supports the following signatures:\n     *\n     * * new BitmapImage(bitmap: { width: number, height: number, data: Buffer })\n     * * new BitmapImage(bitmapImage: BitmapImage)\n     * * new BitmapImage(width: number, height: number, buffer: Buffer)\n     * * new BitmapImage(width: number, height: number, backgroundRGBA?: number)\n     * \n     * When a `BitmapImage` is provided, the constructed `BitmapImage` is a deep clone of the provided one, so that each image's pixel data can subsequently be modified without affecting each other.\n     *\n     * `backgroundRGBA` is an optional parameter representing a pixel as a single number. In hex, the number is as follows: 0xRRGGBBAA, where RR is the red byte, GG the green byte, BB, the blue byte, and AA the alpha value. An AA of 0x00 is considered transparent, and all non-zero AA values are treated as opaque.\n     */\n\n    constructor(...args) {\n        // don't confirm the number of args, because a subclass may have\n        // additional args and pass them all to the superclass\n        if (args.length === 0) {\n            throw new Error(\"constructor requires parameters\");\n        }\n        const firstArg = args[0];\n        if (firstArg !== null && typeof firstArg === 'object') {\n            if (firstArg instanceof BitmapImage) {\n                // copy a provided BitmapImage\n                const sourceBitmap = firstArg.bitmap;\n                this.bitmap = {\n                    width: sourceBitmap.width,\n                    height: sourceBitmap.height,\n                    data: new Buffer(sourceBitmap.width * sourceBitmap.height * 4)\n                };\n                sourceBitmap.data.copy(this.bitmap.data);\n            }\n            else if (firstArg.width && firstArg.height && firstArg.data) {\n                // share a provided bitmap\n                this.bitmap = firstArg;\n            }\n            else {\n                throw new Error(\"unrecognized constructor parameters\");\n            }\n        }\n        else if (typeof firstArg === 'number' && typeof args[1] === 'number')\n        {\n            const width = firstArg;\n            const height = args[1];\n            const thirdArg = args[2];\n            this.bitmap = { width, height };\n\n            if (Buffer.isBuffer(thirdArg)) {\n                this.bitmap.data = thirdArg;\n            }\n            else {\n                this.bitmap.data = new Buffer(width * height * 4);\n                if (typeof thirdArg === 'number') {\n                    this.fillRGBA(thirdArg);\n                }\n            }\n        }\n        else {\n            throw new Error(\"unrecognized constructor parameters\");\n        }\n    }\n\n    /**\n     * Copy a square portion of this image into another image. \n     * \n     * @param {BitmapImage} toImage Image into which to copy the square\n     * @param {number} toX x-coord in toImage of upper-left corner of receiving square\n     * @param {number} toY y-coord in toImage of upper-left corner of receiving square\n     * @param {number} fromX x-coord in this image of upper-left corner of source square\n     * @param {number} fromY y-coord in this image of upper-left corner of source square\n     * @return {BitmapImage} The present image to allow for chaining.\n     */\n\n    blit(toImage, toX, toY, fromX, fromY, fromWidth, fromHeight) {\n        if (fromX + fromWidth > this.bitmap.width) {\n            throw new Error(\"copy exceeds width of source bitmap\");\n        }\n        if (toX + fromWidth > toImage.bitmap.width) {\n            throw new Error(\"copy exceeds width of target bitmap\");\n        }\n        if (fromY + fromHeight > this.bitmap.height) {\n            throw new Error(\"copy exceeds height of source bitmap\");\n        }\n        if (toY + fromHeight > toImage.bitmap.height) {\n            throw new Erro(\"copy exceeds height of target bitmap\");\n        }\n        \n        const sourceBuf = this.bitmap.data;\n        const targetBuf = toImage.bitmap.data;\n        const sourceByteWidth = this.bitmap.width * 4;\n        const targetByteWidth = toImage.bitmap.width * 4;\n        const copyByteWidth = fromWidth * 4;\n        let si = fromY * sourceByteWidth + fromX * 4;\n        let ti = toY * targetByteWidth + toX * 4;\n\n        while (--fromHeight >= 0) {\n            sourceBuf.copy(targetBuf, ti, si, si + copyByteWidth);\n            si += sourceByteWidth;\n            ti += targetByteWidth;\n        }\n        return this;\n    }\n\n    /**\n     * Fills the image with a single color.\n     * \n     * @param {number} rgba Color with which to fill image, expressed as a singlenumber in the form 0xRRGGBBAA, where AA is 0x00 for transparent and any other value for opaque.\n     * @return {BitmapImage} The present image to allow for chaining.\n     */\n\n    fillRGBA(rgba) {\n        const buf = this.bitmap.data;\n        const bufByteWidth = this.bitmap.height * 4;\n        \n        let bi = 0;\n        while (bi < bufByteWidth) {\n            buf.writeUInt32BE(rgba, bi);\n            bi += 4;\n        }\n        while (bi < buf.length) {\n            buf.copy(buf, bi, 0, bufByteWidth);\n            bi += bufByteWidth;\n        }\n        return this;\n    }\n\n    /**\n     * Gets the RGBA number of the pixel at the given coordinate in the form 0xRRGGBBAA, where AA is the alpha value, with alpha 0x00 encoding to transparency in GIFs.\n     * \n     * @param {number} x x-coord of pixel\n     * @param {number} y y-coord of pixel\n     * @return {number} RGBA of pixel in 0xRRGGBBAA form\n     */\n\n    getRGBA(x, y) {\n        const bi = (y * this.bitmap.width + x) * 4;\n        return this.bitmap.data.readUInt32BE(bi);\n    }\n\n    /**\n     * Gets a set of all RGBA colors found within the image.\n     * \n     * @return {Set} Set of all RGBA colors that the image contains.\n     */\n\n    getRGBASet() {\n        const rgbaSet = new Set();\n        const buf = this.bitmap.data;\n        for (let bi = 0; bi < buf.length; bi += 4) {\n            rgbaSet.add(buf.readUInt32BE(bi, true));\n        }\n        return rgbaSet;\n    }\n\n    /**\n     * Converts the image to greyscale using inferred Adobe metrics.\n     * \n     * @return {BitmapImage} The present image to allow for chaining.\n     */\n\n    greyscale() {\n        const buf = this.bitmap.data;\n        this.scan(0, 0, this.bitmap.width, this.bitmap.height, (x, y, idx) => {\n            const grey = Math.round(\n                0.299 * buf[idx] +\n                0.587 * buf[idx + 1] +\n                0.114 * buf[idx + 2]\n            );\n            buf[idx] = grey;\n            buf[idx + 1] = grey;\n            buf[idx + 2] = grey;\n        });\n        return this;\n    }\n\n    /**\n     * Reframes the image as if placing a frame around the original image and replacing the original image with the newly framed image. When the new frame is strictly within the boundaries of the original image, this method crops the image. When any of the new boundaries exceed those of the original image, the `fillRGBA` must be provided to indicate the color with which to fill the extra space added to the image.\n     * \n     * @param {number} xOffset The x-coord offset of the upper-left pixel of the desired image relative to the present image.\n     * @param {number} yOffset The y-coord offset of the upper-left pixel of the desired image relative to the present image.\n     * @param {number} width The width of the new image after reframing\n     * @param {number} height The height of the new image after reframing\n     * @param {number} fillRGBA The color with which to fill space added to the image as a result of the reframing, in 0xRRGGBBAA format, where AA is 0x00 to indicate transparent and a non-zero value to indicate opaque. This parameter is only required when the reframing exceeds the original boundaries (i.e. does not simply perform a crop).\n     * @return {BitmapImage} The present image to allow for chaining.\n     */\n\n    reframe(xOffset, yOffset, width, height, fillRGBA) {\n        const cropX = (xOffset < 0 ? 0 : xOffset);\n        const cropY = (yOffset < 0 ? 0 : yOffset);\n        const cropWidth = (width + cropX > this.bitmap.width ?\n                this.bitmap.width - cropX : width);\n        const cropHeight = (height + cropY > this.bitmap.height ?\n                this.bitmap.height - cropY : height);\n        const newX = (xOffset < 0 ? -xOffset : 0);\n        const newY = (yOffset < 0 ? -yOffset : 0);\n\n        let image;\n        if (fillRGBA === undefined) {\n            if (cropX !== xOffset || cropY != yOffset ||\n                    cropWidth !== width || cropHeight !== height)\n            {\n                throw new GifError(`fillRGBA required for this reframing`);\n            }\n            image = new BitmapImage(width, height);\n        }\n        else {\n            image = new BitmapImage(width, height, fillRGBA);\n        }\n        this.blit(image, newX, newY, cropX, cropY, cropWidth, cropHeight);\n        this.bitmap = image.bitmap;\n        return this;\n    }\n\n    /**\n     * Scales the image size up by an integer factor. Each pixel of the original image becomes a square of the same color in the new image having a size of `factor` x `factor` pixels.\n     * \n     * @param {number} factor The factor by which to scale up the image. Must be an integer >= 1.\n     * @return {BitmapImage} The present image to allow for chaining.\n     */\n\n    scale(factor) {\n        if (factor === 1) {\n            return;\n        }\n        if (!Number.isInteger(factor) || factor < 1) {\n            throw new Error(\"the scale must be an integer >= 1\");\n        }\n        const sourceWidth = this.bitmap.width;\n        const sourceHeight = this.bitmap.height;\n        const destByteWidth = sourceWidth * factor * 4;\n        const sourceBuf = this.bitmap.data;\n        const destBuf = new Buffer(sourceHeight * destByteWidth * factor);\n        let sourceIndex = 0;\n        let priorDestRowIndex;\n        let destIndex = 0;\n        for (let y = 0; y < sourceHeight; ++y) {\n            priorDestRowIndex = destIndex;\n            for (let x = 0; x < sourceWidth; ++x) {\n                const color = sourceBuf.readUInt32BE(sourceIndex, true);\n                for (let cx = 0; cx < factor; ++cx) {\n                    destBuf.writeUInt32BE(color, destIndex);\n                    destIndex += 4;\n                }\n                sourceIndex += 4;\n            }\n            for (let cy = 1; cy < factor; ++cy) {\n                destBuf.copy(destBuf, destIndex, priorDestRowIndex, destIndex);\n                destIndex += destByteWidth;\n                priorDestRowIndex += destByteWidth;\n            }\n        }\n        this.bitmap = {\n            width: sourceWidth * factor,\n            height: sourceHeight * factor,\n            data: destBuf\n        };\n        return this;\n    }\n\n    /**\n     * Scans all coordinates of the image, handing each in turn to the provided handler function.\n     *\n     * @param {function} scanHandler A function(x: number, y: number, bi: number) to be called for each pixel of the image with that pixel's x-coord, y-coord, and index into the `data` buffer. The function accesses the pixel at this coordinate by accessing the `this.data` at index `bi`.\n     * @see scanAllIndexes\n     */\n\n    scanAllCoords(scanHandler) {\n        const width = this.bitmap.width;\n        const bufferLength = this.bitmap.data.length;\n        let x = 0;\n        let y = 0;\n\n        for (let bi = 0; bi < bufferLength; bi += 4) {\n            scanHandler(x, y, bi);\n            if (++x === width) {\n                x = 0;\n                ++y;\n            }\n        }\n    }\n\n    /**\n     * Scans all pixels of the image, handing the index of each in turn to the provided handler function. Runs a bit faster than `scanAllCoords()`, should the handler not need pixel coordinates.\n     *\n     * @param {function} scanHandler A function(bi: number) to be called for each pixel of the image with that pixel's index into the `data` buffer. The pixels is found at index 'bi' within `this.data`.\n     * @see scanAllCoords\n     */\n\n    scanAllIndexes(scanHandler) {\n        const bufferLength = this.bitmap.data.length;\n        for (let bi = 0; bi < bufferLength; bi += 4) {\n            scanHandler(bi);\n        }\n    }\n}\n\nmodule.exports = BitmapImage;\n", "\n\n/** @class Gif */\n\nclass Gif {\n\n    // width - width of GIF in pixels\n    // height - height of GIF in pixels\n    // loops - 0 = unending; (n > 0) = iterate n times\n    // usesTransparency - whether any frames have transparent pixels\n    // colorScope - scope of color tables in GIF\n    // frames - array of frames\n    // buffer - GIF-formatted data\n\n    /**\n     * Gif is a class representing an encoded GIF. It is intended to be a read-only representation of a byte-encoded GIF. Only encoders and decoders should be creating instances of this class.\n     * \n     * Property | Description\n     * --- | ---\n     * width | width of the GIF at its widest\n     * height | height of the GIF at its highest\n     * loops | the number of times the GIF should loop before stopping; 0 => loop indefinately\n     * usesTransparency | boolean indicating whether at least one frame contains at least one transparent pixel\n     * colorScope | the scope of the color tables as encoded within the GIF; either Gif.GlobalColorsOnly (== 1) or Gif.LocalColorsOnly (== 2).\n     * frames | a array of GifFrame instances, one for each frame of the GIF\n     * buffer | a Buffer holding the encoding's byte data\n     * \n     * Its constructor should only ever be called by the GIF encoder or decoder.\n     *\n     * @param {Buffer} buffer A Buffer containing the encoded bytes\n     * @param {GifFrame[]} frames Array of frames found in the encoding\n     * @param {object} spec Properties of the encoding as listed above\n     */\n\n    constructor(buffer, frames, spec) {\n        this.width = spec.width;\n        this.height = spec.height;\n        this.loops = spec.loops;\n        this.usesTransparency = spec.usesTransparency;\n        this.colorScope = spec.colorScope;\n        this.frames = frames;\n        this.buffer = buffer;\n    }\n}\n\nGif.GlobalColorsPreferred = 0;\nGif.GlobalColorsOnly = 1;\nGif.LocalColorsOnly = 2;\n\n/** @class GifError */\n\nclass GifError extends Error {\n\n    /**\n     * GifError is a class representing a GIF-related error\n     * \n     * @param {string|Error} messageOrError\n     */\n\n    constructor(messageOrError) {\n        super(messageOrError);\n        if (messageOrError instanceof Error) {\n            this.stack = 'Gif' + messageOrError.stack;\n        }\n    }\n}\n\nexports.Gif = Gif;\nexports.GifError = GifError;\n", "\n\nconst Omggif = require('omggif');\nconst { Gif, GifError } = require('./gif');\n\n// allow circular dependency with GifUtil\nfunction GifUtil() {\n    const data = require('./gifutil');\n\n    GifUtil = function () {\n      return data;\n    };\n\n  return data;\n}\n\nconst { GifFrame } = require('./gifframe');\n\nconst PER_GIF_OVERHEAD = 200; // these are guesses at upper limits\nconst PER_FRAME_OVERHEAD = 100;\n\n// Note: I experimented with accepting a global color table when encoding and returning the global color table when decoding. Doing this properly greatly increased the complexity of the code and the amount of clock cycles required. The main issue is that each frame can specify any color of the global color table to be transparent within the frame, while this GIF library strives to hide GIF formatting details from its clients. E.g. it's possible to have 256 colors in the global color table and different transparencies in each frame, requiring clients to either provide per-frame transparency indexes, or for arcane reasons that won't be apparent to client developers, encode some GIFs with local color tables that previously decoded with global tables.\n\n/** @class GifCodec */\n\nclass GifCodec\n{\n    // _transparentRGBA - RGB given to transparent pixels (alpha=0) on decode; defaults to null indicating 0x000000, which is fastest\n\n    /**\n     * GifCodec is a class that both encodes and decodes GIFs. It implements both the `encode()` method expected of an encoder and the `decode()` method expected of a decoder, and it wraps the `omggif` GIF encoder/decoder package. GifCodec serves as this library's default encoder and decoder, but it's possible to wrap other GIF encoders and decoders for use by `gifwrap` as well. GifCodec will not encode GIFs with interlacing.\n     * \n     * Instances of this class are stateless and can be shared across multiple encodings and decodings.\n     * \n     * Its constructor takes one option argument:\n     * \n     * @param {object} options Optionally takes an objection whose only possible property is `transparentRGB`. Images are internally represented in RGBA format, where A is the alpha value of a pixel. When `transparentRGB` is provided, this RGB value (excluding alpha) is assigned to transparent pixels, which are also given alpha value 0x00. (All opaque pixels are given alpha value 0xFF). The RGB color of transparent pixels shouldn't matter for most applications. Defaults to 0x000000.\n     */\n\n    constructor(options = {}) {\n        this._transparentRGB = null; // 0x000000\n        if (typeof options.transparentRGB === 'number' &&\n                options.transparentRGB !== 0)\n        {\n            this._transparentRGBA = options.transparentRGB * 256;\n        }\n        this._testInitialBufferSize = 0; // assume no buffer scaling test\n    }\n\n    /**\n     * Decodes a GIF from a Buffer to yield an instance of Gif. Transparent pixels of the GIF are given alpha values of 0x00, and opaque pixels are given alpha values of 0xFF. The RGB values of transparent pixels default to 0x000000 but can be overridden by the constructor's `transparentRGB` option.\n     * \n     * @param {Buffer} buffer Bytes of an encoded GIF to decode.\n     * @return {Promise} A Promise that resolves to an instance of the Gif class, representing the encoded GIF.\n     * @throws {GifError} Error upon encountered an encoding-related problem with a GIF, so that the caller can distinguish between software errors and problems with GIFs.\n     */\n\n    decodeGif(buffer) {\n        try {\n            let reader;\n            try {\n                reader = new Omggif.GifReader(buffer);\n            }\n            catch (err) {\n                throw new GifError(err);\n            }\n            const frameCount = reader.numFrames();\n            const frames = [];\n            const spec = {\n                width: reader.width,\n                height: reader.height,\n                loops: reader.loopCount()\n            };\n\n            spec.usesTransparency = false;\n            for (let i = 0; i < frameCount; ++i) {\n                const frameInfo =\n                        this._decodeFrame(reader, i, spec.usesTransparency);\n                frames.push(frameInfo.frame);\n                if (frameInfo.usesTransparency) {\n                    spec.usesTransparency = true;\n                }\n            }\n            return Promise.resolve(new Gif(buffer, frames, spec));\n        }\n        catch (err) {\n            return Promise.reject(err);\n        }\n    }\n\n    /**\n     * Encodes a GIF from provided frames. Each pixel having an alpha value of 0x00 renders as transparent within the encoding, while all pixels of non-zero alpha value render as opaque.\n     * \n     * @param {GifFrame[]} frames Array of frames to encode\n     * @param {object} spec An optional object that may provide values for `loops` and `colorScope`, as defined for the Gif class. However, `colorSpace` may also take the value Gif.GlobalColorsPreferred (== 0) to indicate that the encoder should attempt to create only a global color table. `loop` defaults to 0, looping indefinitely, and `colorScope` defaults to Gif.GlobalColorsPreferred.\n     * @return {Promise} A Promise that resolves to an instance of the Gif class, representing the encoded GIF.\n     * @throws {GifError} Error upon encountered an encoding-related problem with a GIF, so that the caller can distinguish between software errors and problems with GIFs.\n     */\n\n    encodeGif(frames, spec = {}) {\n        try {\n            if (frames === null || frames.length === 0) {\n                throw new GifError(\"there are no frames\");\n            }\n            const dims = GifUtil().getMaxDimensions(frames);\n\n            spec = Object.assign({}, spec); // don't munge caller's spec\n            spec.width = dims.maxWidth;\n            spec.height = dims.maxHeight;\n            spec.loops = spec.loops || 0;\n            spec.colorScope = spec.colorScope || Gif.GlobalColorsPreferred;\n\n            return Promise.resolve(this._encodeGif(frames, spec));\n        }\n        catch (err) {\n            return Promise.reject(err);\n        }\n    }\n\n    _decodeFrame(reader, frameIndex, alreadyUsedTransparency) {\n        let info, buffer;\n        try {\n            info = reader.frameInfo(frameIndex);\n            buffer = new Buffer(reader.width * reader.height * 4);\n            reader.decodeAndBlitFrameRGBA(frameIndex, buffer);\n            if (info.width !== reader.width || info.height !== reader.height) {\n                if (info.y) {\n                    // skip unused rows\n                    buffer = buffer.slice(info.y * reader.width * 4);\n                }\n                if (reader.width > info.width) {\n                    // skip scanstride\n                    for (let ii = 0; ii < info.height; ++ii) {\n                        buffer.copy(buffer, ii * info.width * 4,\n                            (info.x + ii * reader.width) * 4,\n                            (info.x + ii * reader.width) * 4 + info.width * 4);\n                    }\n                }\n                // trim buffer to size\n                buffer = buffer.slice(0, info.width * info.height * 4);\n            }\n        }\n        catch (err) {\n            throw new GifError(err);\n        }\n\n        let usesTransparency = false;\n        if (this._transparentRGBA === null) {\n            if (!alreadyUsedTransparency) {\n                for (let i = 3; i < buffer.length; i += 4) {\n                    if (buffer[i] === 0) {\n                        usesTransparency = true;\n                        i = buffer.length;\n                    }\n                }\n            }\n        }\n        else {\n            for (let i = 3; i < buffer.length; i += 4) {\n                if (buffer[i] === 0) {\n                    buffer.writeUInt32BE(this._transparentRGBA, i - 3);\n                    usesTransparency = true; // GIF might encode unused index\n                }\n            }\n        }\n\n        const frame = new GifFrame(info.width, info.height, buffer, {\n            xOffset: info.x,\n            yOffset: info.y,\n            disposalMethod: info.disposal,\n            interlaced: info.interlaced,\n            delayCentisecs: info.delay\n        });\n        return { frame, usesTransparency };\n    }\n\n    _encodeGif(frames, spec) {\n        let colorInfo;\n        if (spec.colorScope === Gif.LocalColorsOnly) {\n            colorInfo = GifUtil().getColorInfo(frames, 0);\n        }\n        else {\n            colorInfo = GifUtil().getColorInfo(frames, 256);\n            if (!colorInfo.colors) { // if global palette impossible\n                if (spec.colorScope === Gif.GlobalColorsOnly) {\n                    throw new GifError(\n                            \"Too many color indexes for global color table\");\n                }\n                spec.colorScope = Gif.LocalColorsOnly\n            }\n        }\n        spec.usesTransparency = colorInfo.usesTransparency;\n\n        const localPalettes = colorInfo.palettes;\n        if (spec.colorScope === Gif.LocalColorsOnly) {\n            const localSizeEst = 2000; //this._getSizeEstimateLocal(localPalettes, frames);\n            return _encodeLocal(frames, spec, localSizeEst, localPalettes);\n        }\n\n        const globalSizeEst = 2000; //this._getSizeEstimateGlobal(colorInfo, frames);\n        return _encodeGlobal(frames, spec, globalSizeEst, colorInfo);\n    }\n\n    _getSizeEstimateGlobal(globalPalette, frames) {\n        if (this._testInitialBufferSize > 0) {\n            return this._testInitialBufferSize;\n        }\n        let sizeEst = PER_GIF_OVERHEAD + 3*256 /* max palette size*/;\n        const pixelBitWidth = _getPixelBitWidth(globalPalette);\n        frames.forEach(frame => {\n            sizeEst += _getFrameSizeEst(frame, pixelBitWidth);\n        });\n        return sizeEst; // should be the upper limit\n    }\n\n    _getSizeEstimateLocal(palettes, frames) {\n        if (this._testInitialBufferSize > 0) {\n            return this._testInitialBufferSize;\n        }\n        let sizeEst = PER_GIF_OVERHEAD;\n        for (let i = 0; i < frames.length; ++i ) {\n            const palette = palettes[i];\n            const pixelBitWidth = _getPixelBitWidth(palette);\n            sizeEst += _getFrameSizeEst(frames[i], pixelBitWidth);\n        }\n        return sizeEst; // should be the upper limit\n    }\n}\nexports.GifCodec = GifCodec;\n\nfunction _colorLookupLinear(colors, color) {\n    const index = colors.indexOf(color);\n    return (index === -1 ? null : index);\n}\n\nfunction _colorLookupBinary(colors, color) {\n    // adapted from https://stackoverflow.com/a/10264318/650894\n    var lo = 0, hi = colors.length - 1, mid;\n    while (lo <= hi) {\n        mid = Math.floor((lo + hi)/2);\n        if (colors[mid] > color)\n            hi = mid - 1;\n        else if (colors[mid] < color)\n            lo = mid + 1;\n        else\n            return mid;\n    }\n    return null;\n}\n\nfunction _encodeGlobal(frames, spec, bufferSizeEst, globalPalette) {\n    // would be inefficient for frames to lookup colors in extended palette \n    const extendedGlobalPalette = {\n        colors: globalPalette.colors.slice(),\n        usesTransparency: globalPalette.usesTransparency\n    };\n    _extendPaletteToPowerOf2(extendedGlobalPalette);\n    const options = {\n        palette: extendedGlobalPalette.colors,\n        loop: spec.loops\n    };\n    let buffer = new Buffer(bufferSizeEst);\n    let gifWriter;\n    try {\n        gifWriter = new Omggif.GifWriter(buffer, spec.width, spec.height,\n                            options);\n    }\n    catch (err) {\n        throw new GifError(err);\n    }\n    for (let i = 0; i < frames.length; ++i) {\n        buffer = _writeFrame(gifWriter, i, frames[i], globalPalette, false);\n    }\n    return new Gif(buffer.slice(0, gifWriter.end()), frames, spec);\n}\n\nfunction _encodeLocal(frames, spec, bufferSizeEst, localPalettes) {\n    const options = {\n        loop: spec.loops\n    };\n    let buffer = new Buffer(bufferSizeEst);\n    let gifWriter;\n    try {\n        gifWriter = new Omggif.GifWriter(buffer, spec.width, spec.height,\n                            options);\n    }                            \n    catch (err) {\n        throw new GifError(err);\n    }\n    for (let i = 0; i < frames.length; ++i) {\n        buffer = _writeFrame(gifWriter, i, frames[i], localPalettes[i], true);\n    }\n    return new Gif(buffer.slice(0, gifWriter.end()), frames, spec);\n}\n\nfunction _extendPaletteToPowerOf2(palette) {\n    const colors = palette.colors;\n    if (palette.usesTransparency) {\n        colors.push(0);\n    }\n    const colorCount = colors.length;\n    let powerOf2 = 2;\n    while (colorCount > powerOf2) {\n        powerOf2 <<= 1;\n    }\n    colors.length = powerOf2;\n    colors.fill(0, colorCount);\n}\n\nfunction _getFrameSizeEst(frame, pixelBitWidth) {\n    let byteLength = frame.bitmap.width * frame.bitmap.height;\n    byteLength = Math.ceil(byteLength * pixelBitWidth / 8);\n    byteLength += Math.ceil(byteLength / 255); // add block size bytes\n    // assume maximum palete size because it might get extended for power of 2\n    return (PER_FRAME_OVERHEAD + byteLength + 3 * 256 /* largest palette */);\n}\n\nfunction _getIndexedImage(frameIndex, frame, palette) {\n    const colors = palette.colors;\n    const colorToIndexFunc = (colors.length <= 8 ? // guess at the break-even\n            _colorLookupLinear : _colorLookupBinary);\n    const colorBuffer = frame.bitmap.data;\n    const indexBuffer = new Buffer(colorBuffer.length/4);\n    let transparentIndex = colors.length;\n    let i = 0, j = 0;\n\n    while (i < colorBuffer.length) {\n        if (colorBuffer[i + 3] !== 0) {\n            const color = (colorBuffer.readUInt32BE(i, true) >> 8) & 0xFFFFFF;\n            // caller guarantees that the color will be in the palette\n            indexBuffer[j] = colorToIndexFunc(colors, color);\n        }\n        else {\n            indexBuffer[j] = transparentIndex;\n        }\n        i += 4; // skip alpha\n        ++j;\n    }\n\n    if (palette.usesTransparency) {\n        if (transparentIndex === 256) {\n            throw new GifError(`Frame ${frameIndex} already has 256 colors` +\n                    `and so can't use transparency`);\n        }\n    }\n    else {\n        transparentIndex = null;\n    }\n\n    return { buffer: indexBuffer, transparentIndex };\n}\n\nfunction _getPixelBitWidth(palette) {\n    let indexCount = palette.indexCount;\n    let pixelBitWidth = 0;\n    --indexCount; // start at maximum index\n    while (indexCount) {\n        ++pixelBitWidth;\n        indexCount >>= 1;\n    }\n    return (pixelBitWidth > 0 ? pixelBitWidth : 1);\n}\n\nfunction _writeFrame(gifWriter, frameIndex, frame, palette, isLocalPalette) {\n    if (frame.interlaced) {\n        throw new GifError(\"writing interlaced GIFs is not supported\");\n    }\n    const frameInfo = _getIndexedImage(frameIndex, frame, palette);\n    const options = {\n        delay: frame.delayCentisecs,\n        disposal: frame.disposalMethod,\n        transparent: frameInfo.transparentIndex\n    };\n    if (isLocalPalette) {\n        _extendPaletteToPowerOf2(palette); // ok 'cause palette never used again\n        options.palette = palette.colors;\n    }\n    try {\n        let buffer = gifWriter.getOutputBuffer();\n        let startOfFrame = gifWriter.getOutputBufferPosition();\n        let endOfFrame;\n        let tryAgain = true;\n\n        while (tryAgain) {\n            endOfFrame = gifWriter.addFrame(frame.xOffset, frame.yOffset,\n                    frame.bitmap.width, frame.bitmap.height, frameInfo.buffer, options);\n            tryAgain = false;\n            if (endOfFrame >= buffer.length - 1) {\n                const biggerBuffer = new Buffer(buffer.length * 1.5);\n                buffer.copy(biggerBuffer);\n                gifWriter.setOutputBuffer(biggerBuffer);\n                gifWriter.setOutputBufferPosition(startOfFrame);\n                buffer = biggerBuffer;\n                tryAgain = true;\n            }\n        }\n        return buffer;\n    }\n    catch (err) {\n        throw new GifError(err);\n    }\n}\n", "\n\n/** @namespace GifUtil */\n\nconst fs = require('fs');\nconst ImageQ = require('image-q');\n\nconst BitmapImage = require('./bitmapimage');\nconst { GifFrame } = require('./gifframe');\nconst { GifError } = require('./gif');\nconst { GifCodec } = require('./gifcodec');\n\nconst INVALID_SUFFIXES = ['.jpg', '.jpeg', '.png', '.bmp'];\n\nconst defaultCodec = new GifCodec();\n\n/**\n * cloneFrames() clones provided frames. It's a utility method for cloning an entire array of frames at once.\n * \n * @function cloneFrames\n * @memberof GifUtil\n * @param {GifFrame[]} frames An array of GifFrame instances to clone\n * @return {GifFrame[]} An array of GifFrame clones of the provided frames.\n */\n\nexports.cloneFrames = function (frames) {\n    let clones = [];\n    frames.forEach(frame => {\n\n        clones.push(new GifFrame(frame));\n    });\n    return clones;\n}\n\n/**\n * getColorInfo() gets information about the colors used in the provided frames. The method is able to return an array of all colors found across all frames.\n * \n * `maxGlobalIndex` controls whether the computation short-circuits to avoid doing work that the caller doesn't need. The method only returns `colors` and `indexCount` for the colors across all frames when the number of indexes required to store the colors and transparency in a GIF (which is the value of `indexCount`) is less than or equal to `maxGlobalIndex`. Such short-circuiting is useful when the caller just needs to determine whether any frame includes transparency.\n * \n * @function getColorInfo\n * @memberof GifUtil\n * @param {GifFrame[]} frames Frames to examine for color and transparency.\n * @param {number} maxGlobalIndex Maximum number of color indexes (including one for transparency) allowed among the returned compilation of colors. `colors` and `indexCount` are not returned if the number of color indexes required to accommodate  all frames exceeds this number. Returns `colors` and `indexCount` by default.\n * @returns {object} Object containing at least `palettes` and `usesTransparency`. `palettes` is an array of all the palettes returned by GifFrame#getPalette(). `usesTransparency` indicates whether at least one frame uses transparency. If `maxGlobalIndex` is not exceeded, the object also contains `colors`, an array of all colors (RGB) found across all palettes, sorted by increasing value, and `indexCount` indicating the number of indexes required to store the colors and the transparency in a GIF.\n * @throws {GifError} When any frame requires more than 256 color indexes.\n */\n\nexports.getColorInfo = function (frames, maxGlobalIndex) {\n    let usesTransparency = false;\n    const palettes = [];\n    for (let i = 0; i < frames.length; ++i) {\n        let palette = frames[i].getPalette();\n        if (palette.usesTransparency) {\n            usesTransparency = true;\n        }\n        if (palette.indexCount > 256) {\n            throw new GifError(`Frame ${i} uses more than 256 color indexes`);\n        }\n        palettes.push(palette);\n    }\n    if (maxGlobalIndex === 0) {\n        return { usesTransparency, palettes };\n    }\n\n    const globalColorSet = new Set();\n    palettes.forEach(palette => {\n\n        palette.colors.forEach(color => {\n\n            globalColorSet.add(color);\n        });\n    });\n    let indexCount = globalColorSet.size;\n    if (usesTransparency) {\n        // odd that GIF requires a color table entry at transparent index\n        ++indexCount;\n    }\n    if (maxGlobalIndex && indexCount > maxGlobalIndex) {\n        return { usesTransparency, palettes };\n    }\n    \n    const colors = new Array(globalColorSet.size);\n    const iter = globalColorSet.values();\n    for (let i = 0; i < colors.length; ++i) {\n        colors[i] = iter.next().value;\n    }\n    colors.sort((a, b) => (a - b));\n    return { colors, indexCount, usesTransparency, palettes };\n};\n\n/**\n * copyAsJimp() returns a Jimp that contains a copy of the provided bitmap image (which may be either a BitmapImage or a GifFrame). Modifying the Jimp does not affect the provided bitmap image. This method serves as a macro for simplifying working with Jimp.\n *\n * @function copyAsJimp\n * @memberof GifUtil\n * @param {object} Reference to the Jimp package, keeping this library from being dependent on Jimp.\n * @param {bitmapImageToCopy} Instance of BitmapImage (may be a GifUtil) with which to source the Jimp.\n * @return {object} An new instance of Jimp containing a copy of the image in bitmapImageToCopy.\n */\n \nexports.copyAsJimp = function (jimp, bitmapImageToCopy) {\n    return exports.shareAsJimp(jimp, new BitmapImage(bitmapImageToCopy));\n};\n\n/**\n * getMaxDimensions() returns the pixel width and height required to accommodate all of the provided frames, according to the offsets and dimensions of each frame.\n * \n * @function getMaxDimensions\n * @memberof GifUtil\n * @param {GifFrame[]} frames Frames to measure for their aggregate maximum dimensions.\n * @return {object} An object of the form {maxWidth, maxHeight} indicating the maximum width and height required to accommodate all frames.\n */\n\nexports.getMaxDimensions = function (frames) {\n    let maxWidth = 0, maxHeight = 0;\n    frames.forEach(frame => {\n        const width = frame.xOffset + frame.bitmap.width;\n        if (width > maxWidth) {\n            maxWidth = width;\n        }\n        const height = frame.yOffset + frame.bitmap.height;\n        if (height > maxHeight) {\n            maxHeight = height;\n        }\n    });\n    return { maxWidth, maxHeight };\n};\n\n/**\n * Quantizes colors so that there are at most a given number of color indexes (including transparency) across all provided images. Uses an algorithm by Anthony Dekker.\n * \n * The method treats different RGBA combinations as different colors, so if the frame has multiple alpha values or multiple RGB values for an alpha value, the caller may first want to normalize them by converting all transparent pixels to the same RGBA values.\n * \n * The method may increase the number of colors if there are fewer than the provided maximum.\n * \n * @function quantizeDekker\n * @memberof GifUtil\n * @param {BitmapImage|BitmapImage[]} imageOrImages Image or array of images (such as GifFrame instances) to be color-quantized. Quantizing across multiple images ensures color consistency from frame to frame.\n * @param {number} maxColorIndexes The maximum number of color indexes that will exist in the palette after completing quantization. Defaults to 256.\n * @param {object} dither (optional) An object configuring the dithering to apply. The properties are as followings, imported from the [`image-q` package](https://github.com/ibezkrovnyi/image-quantization) without explanation: { `ditherAlgorithm`: One of 'FloydSteinberg', 'FalseFloydSteinberg', 'Stucki', 'Atkinson', 'Jarvis', 'Burkes', 'Sierra', 'TwoSierra', 'SierraLite'; `minimumColorDistanceToDither`: (optional) A number defaulting to 0; `serpentine`: (optional) A boolean defaulting to true; `calculateErrorLikeGIMP`: (optional) A boolean defaulting to false. }\n */\n\nexports.quantizeDekker = function (imageOrImages, maxColorIndexes, dither) {\n    maxColorIndexes = maxColorIndexes || 256;\n    _quantize(imageOrImages, 'NeuQuantFloat', maxColorIndexes, 0, dither);\n}\n\n/**\n * Quantizes colors so that there are at most a given number of color indexes (including transparency) across all provided images. Uses an algorithm by Leon Sorokin. This quantization method differs from the other two by likely never increasing the number of colors, should there be fewer than the provided maximum.\n * \n * The method treats different RGBA combinations as different colors, so if the frame has multiple alpha values or multiple RGB values for an alpha value, the caller may first want to normalize them by converting all transparent pixels to the same RGBA values.\n * \n * @function quantizeSorokin\n * @memberof GifUtil\n * @param {BitmapImage|BitmapImage[]} imageOrImages Image or array of images (such as GifFrame instances) to be color-quantized. Quantizing across multiple images ensures color consistency from frame to frame.\n * @param {number} maxColorIndexes The maximum number of color indexes that will exist in the palette after completing quantization. Defaults to 256.\n * @param {string} histogram (optional) Histogram method: 'top-pop' for global top-population, 'min-pop' for minimum-population threshhold within subregions. Defaults to 'min-pop'.\n * @param {object} dither (optional) An object configuring the dithering to apply, as explained for `quantizeDekker()`.\n */\n\nexports.quantizeSorokin = function (imageOrImages, maxColorIndexes, histogram, dither) {\n    maxColorIndexes = maxColorIndexes || 256;\n    histogram = histogram || 'min-pop';\n    let histogramID;\n    switch (histogram) {\n        case 'min-pop':\n        histogramID = 2;\n        break;\n\n        case 'top-pop':\n        histogramID = 1;\n        break\n\n        default:\n        throw new Error(`Invalid quantizeSorokin histogram '${histogram}'`);\n    }\n    _quantize(imageOrImages, 'RGBQuant', maxColorIndexes, histogramID, dither);\n}\n\n/**\n * Quantizes colors so that there are at most a given number of color indexes (including transparency) across all provided images. Uses an algorithm by Xiaolin Wu.\n * \n * The method treats different RGBA combinations as different colors, so if the frame has multiple alpha values or multiple RGB values for an alpha value, the caller may first want to normalize them by converting all transparent pixels to the same RGBA values.\n * \n * The method may increase the number of colors if there are fewer than the provided maximum.\n * \n * @function quantizeWu\n * @memberof GifUtil\n * @param {BitmapImage|BitmapImage[]} imageOrImages Image or array of images (such as GifFrame instances) to be color-quantized. Quantizing across multiple images ensures color consistency from frame to frame.\n * @param {number} maxColorIndexes The maximum number of color indexes that will exist in the palette after completing quantization. Defaults to 256.\n * @param {number} significantBits (optional) This is the number of significant high bits in each RGB color channel. Takes integer values from 1 through 8. Higher values correspond to higher quality. Defaults to 5.\n * @param {object} dither (optional) An object configuring the dithering to apply, as explained for `quantizeDekker()`.\n */\n\nexports.quantizeWu = function (imageOrImages, maxColorIndexes, significantBits, dither) {\n    maxColorIndexes = maxColorIndexes || 256;\n    significantBits = significantBits || 5;\n    if (significantBits < 1 || significantBits > 8) {\n        throw new Error(\"Invalid quantization quality\");\n    }\n    _quantize(imageOrImages, 'WuQuant', maxColorIndexes, significantBits, dither);\n}\n\n/**\n * read() decodes an encoded GIF, whether provided as a filename or as a byte buffer.\n * \n * @function read\n * @memberof GifUtil\n * @param {string|Buffer} source Source to decode. When a string, it's the GIF filename to load and parse. When a Buffer, it's an encoded GIF to parse.\n * @param {object} decoder An optional GIF decoder object implementing the `decode` method of class GifCodec. When provided, the method decodes the GIF using this decoder. When not provided, the method uses GifCodec.\n * @return {Promise} A Promise that resolves to an instance of the Gif class, representing the decoded GIF.\n */\n\nexports.read = function (source, decoder) {\n    decoder = decoder || defaultCodec;\n    if (Buffer.isBuffer(source)) {\n        return decoder.decodeGif(source);\n    }\n    return _readBinary(source)\n    .then(buffer => {\n\n        return decoder.decodeGif(buffer);\n    });\n};\n\n/**\n * shareAsJimp() returns a Jimp that shares a bitmap with the provided bitmap image (which may be either a BitmapImage or a GifFrame). Modifying the image in either the Jimp or the BitmapImage affects the other objects. This method serves as a macro for simplifying working with Jimp.\n *\n * @function shareAsJimp\n * @memberof GifUtil\n * @param {object} Reference to the Jimp package, keeping this library from being dependent on Jimp.\n * @param {bitmapImageToShare} Instance of BitmapImage (may be a GifUtil) with which to source the Jimp.\n * @return {object} An new instance of Jimp that shares the image in bitmapImageToShare.\n */\n \nexports.shareAsJimp = function (jimp, bitmapImageToShare) {\n    const jimpImage = new jimp(bitmapImageToShare.bitmap.width,\n            bitmapImageToShare.bitmap.height, 0);\n    jimpImage.bitmap.data = bitmapImageToShare.bitmap.data;\n    return jimpImage;\n};\n\n/**\n * write() encodes a GIF and saves it as a file.\n * \n * @function write\n * @memberof GifUtil\n * @param {string} path Filename to write GIF out as. Will overwrite an existing file.\n * @param {GifFrame[]} frames Array of frames to be written into GIF.\n * @param {object} spec An optional object that may provide values for `loops` and `colorScope`, as defined for the Gif class. However, `colorSpace` may also take the value Gif.GlobalColorsPreferred (== 0) to indicate that the encoder should attempt to create only a global color table. `loop` defaults to 0, looping indefinitely, and `colorScope` defaults to Gif.GlobalColorsPreferred.\n * @param {object} encoder An optional GIF encoder object implementing the `encode` method of class GifCodec. When provided, the method encodes the GIF using this encoder. When not provided, the method uses GifCodec.\n * @return {Promise} A Promise that resolves to an instance of the Gif class, representing the encoded GIF.\n */\n\nexports.write = function (path, frames, spec, encoder) {\n    encoder = encoder || defaultCodec;\n    const matches = path.match(/\\.[a-zA-Z]+$/); // prevent accidents\n    if (matches !== null &&\n            INVALID_SUFFIXES.includes(matches[0].toLowerCase()))\n    {\n        throw new Error(`GIF '${path}' has an unexpected suffix`);\n    }\n\n    return encoder.encodeGif(frames, spec)\n    .then(gif => {\n\n        return _writeBinary(path, gif.buffer)\n        .then(() => {\n\n            return gif;\n        });\n    });\n};\n\nfunction _quantize(imageOrImages, method, maxColorIndexes, modifier, dither) {\n    const images = Array.isArray(imageOrImages) ? imageOrImages : [imageOrImages];\n    const ditherAlgs = [\n        'FloydSteinberg',\n        'FalseFloydSteinberg',\n        'Stucki',\n        'Atkinson',\n        'Jarvis',\n        'Burkes',\n        'Sierra',\n        'TwoSierra',\n        'SierraLite'\n    ];\n\n    if (dither) {\n        if (ditherAlgs.indexOf(dither.ditherAlgorithm) < 0) {\n            throw new Error(`Invalid ditherAlgorithm '${dither.ditherAlgorithm}'`);\n        }\n        if (dither.serpentine === undefined) {\n            dither.serpentine = true;\n        }\n        if (dither.minimumColorDistanceToDither === undefined) {\n            dither.minimumColorDistanceToDither = 0;\n        }\n        if (dither.calculateErrorLikeGIMP === undefined) {\n            dither.calculateErrorLikeGIMP = false;\n        }\n    }\n\n    const distCalculator = new ImageQ.distance.Euclidean();\n    const quantizer = new ImageQ.palette[method](distCalculator, maxColorIndexes, modifier);\n    let imageMaker;\n    if (dither) {\n        imageMaker = new ImageQ.image.ErrorDiffusionArray(\n            distCalculator,\n            ImageQ.image.ErrorDiffusionArrayKernel[dither.ditherAlgorithm],\n            dither.serpentine,\n            dither.minimumColorDistanceToDither,\n            dither.calculateErrorLikeGIMP\n        );\n    }\n    else {\n        imageMaker = new ImageQ.image.NearestColor(distCalculator);\n    }\n\n    const inputContainers = [];\n    images.forEach(image => {\n\n        const imageBuf = image.bitmap.data;\n        const inputBuf = new ArrayBuffer(imageBuf.length);\n        const inputArray = new Uint32Array(inputBuf);\n        for (let bi = 0, ai = 0; bi < imageBuf.length; bi += 4, ++ai) {\n            inputArray[ai] = imageBuf.readUInt32LE(bi, true);\n        }\n        const inputContainer = ImageQ.utils.PointContainer.fromUint32Array(\n                inputArray, image.bitmap.width, image.bitmap.height);\n        quantizer.sample(inputContainer);\n        inputContainers.push(inputContainer);\n    });\n    \n    const limitedPalette = quantizer.quantizeSync();\n\n    for (let i = 0; i < images.length; ++i) {\n        const imageBuf = images[i].bitmap.data;\n        const outputContainer = imageMaker.quantizeSync(inputContainers[i], limitedPalette);\n        const outputArray = outputContainer.toUint32Array();\n        for (let bi = 0, ai = 0; bi < imageBuf.length; bi += 4, ++ai) {\n            imageBuf.writeUInt32LE(outputArray[ai], bi);\n        }\n    }\n}\n\nfunction _readBinary(path) {\n    // TBD: add support for URLs\n    return new Promise((resolve, reject) => {\n\n        fs.readFile(path, (err, buffer) => {\n\n            if (err) {\n                return reject(err);\n            }\n            return resolve(buffer);\n        });\n    });\n}\n\nfunction _writeBinary(path, buffer) {\n    // TBD: add support for URLs\n    return new Promise((resolve, reject) => {\n\n        fs.writeFile(path, buffer, err => {\n            \n            if (err) {\n                return reject(err);\n            }\n            return resolve();\n        });\n    });\n}\n", "\n\nconst BitmapImage = require('./bitmapimage');\nconst { GifError } = require('./gif');\n\n/** @class GifFrame */\n\nclass GifFrame extends BitmapImage {\n\n    // xOffset - x offset of bitmap on GIF (defaults to 0)\n    // yOffset - y offset of bitmap on GIF (defaults to 0)\n    // disposalMethod - pixel disposal method when handling partial images\n    // delayCentisecs - duration of frame in hundredths of a second\n    // interlaced - whether the image is interlaced (defaults to false)\n\n    /**\n     * GifFrame is a class representing an image frame of a GIF. GIFs contain one or more instances of GifFrame.\n     * \n     * Property | Description\n     * --- | ---\n     * xOffset | x-coord of position within GIF at which to render the image (defaults to 0)\n     * yOffset | y-coord of position within GIF at which to render the image (defaults to 0)\n     * disposalMethod | GIF disposal method; only relevant when the frames aren't all the same size (defaults to 2, disposing to background color)\n     * delayCentisecs | duration of the frame in hundreths of a second\n     * interlaced | boolean indicating whether the frame renders interlaced\n     * \n     * Its constructor supports the following signatures:\n     * \n     * * new GifFrame(bitmap: {width: number, height: number, data: Buffer}, options?)\n     * * new GifFrame(bitmapImage: BitmapImage, options?)\n     * * new GifFrame(width: number, height: number, buffer: Buffer, options?)\n     * * new GifFrame(width: number, height: number, backgroundRGBA?: number, options?)\n     * * new GifFrame(frame: GifFrame)\n     * \n     * See the base class BitmapImage for a discussion of all parameters but `options` and `frame`. `options` is an optional argument providing initial values for the above-listed GifFrame properties. Each property within option is itself optional.\n     * \n     * Provide a `frame` to the constructor to create a clone of the provided frame. The new frame includes a copy of the provided frame's pixel data so that each can subsequently be modified without affecting each other.\n     */\n\n    constructor(...args) {\n        super(...args);\n        if (args[0] instanceof GifFrame) {\n            // copy a provided GifFrame\n            const source = args[0];\n            this.xOffset = source.xOffset;\n            this.yOffset = source.yOffset;\n            this.disposalMethod = source.disposalMethod;\n            this.delayCentisecs = source.delayCentisecs;\n            this.interlaced = source.interlaced;\n        }\n        else {\n            const lastArg = args[args.length - 1];\n            let options = {};\n            if (typeof lastArg === 'object' && !(lastArg instanceof BitmapImage)) {\n                options = lastArg;\n            }\n            this.xOffset = options.xOffset || 0;\n            this.yOffset = options.yOffset || 0;\n            this.disposalMethod = (options.disposalMethod !== undefined ?\n                    options.disposalMethod : GifFrame.DisposeToBackgroundColor);\n            this.delayCentisecs = options.delayCentisecs || 8;\n            this.interlaced = options.interlaced || false;\n        }\n    }\n\n    /**\n     * Get a summary of the colors found within the frame. The return value is an object of the following form:\n     * \n     * Property | Description\n     * --- | ---\n     * colors | An array of all the opaque colors found within the frame. Each color is given as an RGB number of the form 0xRRGGBB. The array is sorted by increasing number. Will be an empty array when the image is completely transparent.\n     * usesTransparency | boolean indicating whether there are any transparent pixels within the frame. A pixel is considered transparent if its alpha value is 0x00.\n     * indexCount | The number of color indexes required to represent this palette of colors. It is equal to the number of opaque colors plus one if the image includes transparency.\n     * \n     * @return {object} An object representing a color palette as described above.\n     */\n\n    getPalette() {\n        // returns with colors sorted low to high\n        const colorSet = new Set();\n        const buf = this.bitmap.data;\n        let i = 0;\n        let usesTransparency = false;\n        while (i < buf.length) {\n            if (buf[i + 3] === 0) {\n                usesTransparency = true;\n            }\n            else {\n                // can eliminate the bitshift by starting one byte prior\n                const color = (buf.readUInt32BE(i, true) >> 8) & 0xFFFFFF;\n                colorSet.add(color);\n            }\n            i += 4; // skip alpha\n        }\n        const colors = new Array(colorSet.size);\n        const iter = colorSet.values();\n        for (i = 0; i < colors.length; ++i) {\n            colors[i] = iter.next().value;\n        }\n        colors.sort((a, b) => (a - b));\n        let indexCount = colors.length;\n        if (usesTransparency) {\n            ++indexCount;\n        }\n        return { colors, usesTransparency, indexCount };\n    }\n}\n\nGifFrame.DisposeToAnything = 0;\nGifFrame.DisposeNothing = 1;\nGifFrame.DisposeToBackgroundColor = 2;\nGifFrame.DisposeToPrevious = 3;\n\nexports.GifFrame = GifFrame;\n"]}