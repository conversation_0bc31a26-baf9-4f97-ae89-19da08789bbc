{"version": 3, "sources": ["index.js", "geo/index.js", "geo/point.js", "validate.js", "constant.js", "util.js", "const/code.js", "utils/utils.js", "utils/type.js", "utils/symbol.js", "helper/symbol.js", "geo/lineString.js", "geo/polygon.js", "geo/multiPoint.js", "geo/multiLineString.js", "geo/multiPolygon.js", "collection.js", "document.js", "serializer/update.js", "commands/update.js", "operator-map.js", "commands/query.js", "commands/logic.js", "serializer/common.js", "serializer/datatype.js", "serverDate/index.js", "realtime/websocket-client.js", "realtime/virtual-websocket-client.js", "realtime/message.js", "utils/error.js", "config/error.config.js", "realtime/listener.js", "realtime/snapshot.js", "realtime/error.js", "realtime/ws-event.js", "query.js", "serializer/query.js", "aggregate.js", "command.js", "regexp/index.js", "transaction/index.js", "ObjectId/index.js"], "names": [], "mappings": ";;;;;;;AAAA;AACA;AACA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ACHA,AFMA;ACFA,ACHA,AFMA;ACFA,ACHA,AFMA;ACFA,ACHA,AFMA,AGTA;AFOA,ACHA,AFMA,AGTA;AFOA,ACHA,AFMA,AGTA;ACFA,AHSA,ACHA,AFMA,AGTA;ACFA,AHSA,ACHA,AFMA,AGTA;ACFA,AHSA,ACHA,AFMA,AGTA;ACFA,AFMA,AFMA,AKfA,AFMA;ACFA,AFMA,AFMA,AKfA,AFMA;ACFA,AFMA,AFMA,AKfA,AFMA;AGRA,AFMA,AFMA,AFMA,AKfA,AFMA;AGRA,AFMA,AFMA,AFMA,AKfA,AFMA;AGRA,AFMA,AFMA,AFMA,AKfA,AFMA;AGRA,AFMA,AFMA,AFMA,AKfA,AENA,AJYA;AGRA,AFMA,AFMA,AFMA,AKfA,AENA,AJYA;AGRA,AFMA,AFMA,AFMA,AKfA,AENA,AJYA;AGRA,AFMA,AFMA,AFMA,AKfA,AGTA,ADGA,AJYA;AGRA,AFMA,AFMA,AFMA,AKfA,AGTA,ADGA,AJYA;AGRA,AFMA,AFMA,AFMA,AKfA,AGTA,ADGA,AJYA;AGRA,AFMA,AFMA,AFMA,AKfA,AIZA,ADGA,ADGA,AJYA;AGRA,AFMA,AFMA,AFMA,AKfA,AIZA,ADGA,ADGA,AJYA;AGRA,AFMA,AFMA,AFMA,AKfA,AIZA,ADGA,ADGA,AJYA;AGRA,AFMA,AFMA,AQxBA,AV8BA,AKfA,AIZA,ADGA,ADGA,AJYA;AGRA,AFMA,AFMA,AQxBA,AV8BA,AKfA,AIZA,ADGA,ADGA,AJYA;AGRA,AFMA,AFMA,AQxBA,AV8BA,AKfA,AIZA,ADGA,ADGA,AJYA;AGRA,AFMA,AOrBA,AT2BA,AQxBA,AV8BA,AKfA,AIZA,ADGA,ADGA,AJYA;AGRA,AFMA,AOrBA,AT2BA,AQxBA,AV8BA,AKfA,AIZA,ADGA,ADGA,AJYA;AGRA,AFMA,AOrBA,AT2BA,AQxBA,AV8BA,AKfA,AIZA,ADGA,ADGA,AJYA;AGRA,AFMA,AOrBA,AT2BA,AU9BA,AFMA,AV8BA,AKfA,AIZA,ADGA,ADGA,AJYA;AGRA,AFMA,AOrBA,AT2BA,AU9BA,AFMA,AV8BA,AKfA,AIZA,ADGA,ADGA,AJYA;AGRA,AFMA,AOrBA,AT2BA,AU9BA,AFMA,AV8BA,AKfA,AIZA,ADGA,ADGA,AJYA;AGRA,AFMA,AOrBA,AENA,AXiCA,AU9BA,AFMA,AV8BA,AKfA,AIZA,ADGA,ADGA,AJYA;AGRA,AFMA,AOrBA,AENA,AXiCA,AU9BA,AFMA,AV8BA,AKfA,AIZA,ADGA,ADGA,AJYA;AGRA,AFMA,AOrBA,AENA,AXiCA,AU9BA,AFMA,AV8BA,AKfA,AIZA,ADGA,ADGA,AJYA;AGRA,AFMA,AOrBA,AGTA,ADGA,AXiCA,AU9BA,AFMA,ALeA,AIZA,ADGA,ADGA,AJYA;AGRA,AFMA,AOrBA,AGTA,ADGA,AXiCA,AU9BA,AFMA,ALeA,AIZA,ADGA,ADGA,AJYA;AGRA,AFMA,AOrBA,AGTA,ADGA,AXiCA,AU9BA,AFMA,ALeA,AIZA,ADGA,ADGA,AJYA;AGRA,AFMA,AOrBA,AGTA,ADGA,AENA,AbuCA,AU9BA,AFMA,ALeA,AIZA,ADGA,ADGA,AJYA;AGRA,AFMA,AOrBA,AGTA,ADGA,AENA,AbuCA,AU9BA,AFMA,ALeA,AIZA,ADGA,ADGA,AJYA;AGRA,AFMA,AOrBA,AGTA,ADGA,AENA,AbuCA,AU9BA,AFMA,ALeA,AIZA,AFMA,AJYA;AatCA,AV8BA,AFMA,AOrBA,AGTA,ADGA,AENA,AbuCA,AU9BA,AFMA,ALeA,AIZA,AFMA,AJYA;AatCA,AV8BA,AFMA,AOrBA,AGTA,ADGA,AENA,AbuCA,AU9BA,AFMA,ALeA,AIZA,AFMA,AJYA;AatCA,AV8BA,AFMA,AOrBA,AGTA,ADGA,AENA,AHSA,AFMA,ALeA,AIZA,AFMA,AJYA;AatCA,AV8BA,AFMA,AavCA,ANkBA,AGTA,ADGA,AENA,AHSA,APqBA,AIZA,AFMA,AJYA;AatCA,AV8BA,AFMA,AavCA,ANkBA,AGTA,ADGA,AENA,AHSA,APqBA,AIZA,AFMA,AJYA;AatCA,AV8BA,AFMA,AavCA,ANkBA,AGTA,ADGA,AENA,AHSA,APqBA,AIZA,AFMA,AJYA;AatCA,AV8BA,AFMA,AavCA,ANkBA,AGTA,ADGA,AENA,AHSA,AMlBA,AbuCA,AIZA,AFMA,AJYA;AatCA,AZoCA,AavCA,ANkBA,AGTA,ADGA,AENA,AHSA,AMlBA,AbuCA,AIZA,AFMA,AJYA;AatCA,AZoCA,AavCA,ANkBA,AGTA,ADGA,AENA,AHSA,AMlBA,AbuCA,AIZA,AFMA,AJYA;AatCA,AGTA,Af6CA,AavCA,ANkBA,AGTA,ADGA,AENA,AHSA,AMlBA,AbuCA,AIZA,AFMA,AJYA;AatCA,AGTA,Af6CA,AavCA,ANkBA,AGTA,ADGA,AENA,AHSA,AMlBA,AbuCA,AIZA,AFMA,AJYA;AatCA,AGTA,Af6CA,AavCA,ANkBA,AGTA,ADGA,AENA,AHSA,AMlBA,AbuCA,AIZA,AFMA,AJYA;AatCA,AGTA,Af6CA,AavCA,ANkBA,AGTA,ADGA,AENA,AHSA,AQxBA,AFMA,AbuCA,AIZA,AFMA,AJYA;AatCA,AGTA,Af6CA,AavCA,ANkBA,AGTA,ADGA,AENA,AHSA,AQxBA,AFMA,AbuCA,AIZA,AFMA,AJYA;AatCA,AGTA,Af6CA,AavCA,ANkBA,AGTA,ADGA,AENA,AHSA,AQxBA,AFMA,AbuCA,AIZA,AFMA,AJYA;AatCA,AKfA,AFMA,Af6CA,AavCA,ANkBA,AGTA,ADGA,AENA,AHSA,AQxBA,AFMA,AbuCA,AIZA,AFMA,AJYA;AatCA,AKfA,AFMA,Af6CA,AavCA,ANkBA,AGTA,ADGA,AENA,AHSA,AQxBA,AFMA,AbuCA,AIZA,AFMA,AJYA;AatCA,AKfA,AFMA,Af6CA,AavCA,ANkBA,AGTA,ADGA,AENA,AHSA,AQxBA,AFMA,AbuCA,AIZA,AFMA,AJYA;AatCA,AMlBA,ADGA,AFMA,Af6CA,AavCA,ANkBA,AGTA,ADGA,AENA,AHSA,AQxBA,AFMA,AbuCA,AIZA,AFMA,AJYA;AatCA,AMlBA,ADGA,AFMA,Af6CA,AavCA,ANkBA,AGTA,ADGA,AENA,AHSA,AQxBA,AFMA,AbuCA,AENA,AJYA;AatCA,AMlBA,ADGA,AFMA,Af6CA,AavCA,ANkBA,AGTA,ADGA,AENA,AHSA,AQxBA,AFMA,AbuCA,AENA,AJYA;AatCA,AMlBA,ADGA,AFMA,Af6CA,AavCA,ANkBA,AGTA,ADGA,AENA,AHSA,AQxBA,AGTA,ALeA,AbuCA,AENA,AJYA;AatCA,AMlBA,ADGA,AFMA,Af6CA,AavCA,ANkBA,AGTA,ADGA,AENA,AHSA,AQxBA,AGTA,ALeA,AbuCA,AENA,AJYA;AatCA,AMlBA,ADGA,AFMA,Af6CA,AavCA,ANkBA,AGTA,ADGA,AENA,AHSA,AQxBA,AGTA,ALeA,AbuCA,AENA,AJYA;AatCA,AMlBA,ADGA,AFMA,Af6CA,AavCA,ANkBA,AGTA,ADGA,AENA,AHSA,AQxBA,AGTA,ACHA,ANkBA,AbuCA,AENA,AJYA;AatCA,AMlBA,ADGA,AFMA,Af6CA,AavCA,ANkBA,AGTA,ADGA,AENA,AHSA,AQxBA,AGTA,ACHA,ANkBA,AbuCA,AENA,AJYA;AatCA,AMlBA,ADGA,AFMA,Af6CA,AavCA,ANkBA,AGTA,ADGA,AENA,AHSA,AQxBA,AGTA,ACHA,ANkBA,AbuCA,AENA,AJYA;AatCA,AMlBA,ADGA,AFMA,Af6CA,AavCA,ANkBA,AGTA,ADGA,AENA,AHSA,AQxBA,AGTA,ACHA,ANkBA,AOrBA,ApB4DA,AENA,AJYA;AatCA,AMlBA,ADGA,AFMA,Af6CA,AavCA,ANkBA,AGTA,ADGA,AENA,AHSA,AQxBA,AGTA,ACHA,ANkBA,AOrBA,ApB4DA,AENA,AJYA;AatCA,AMlBA,ADGA,AFMA,Af6CA,AavCA,ANkBA,AGTA,ADGA,AENA,AHSA,AQxBA,AGTA,ACHA,ANkBA,AOrBA,ApB4DA,AENA,AJYA;AatCA,AMlBA,ADGA,AFMA,Af6CA,AavCA,ANkBA,AGTA,ADGA,AENA,AHSA,AQxBA,AMlBA,AHSA,ACHA,ANkBA,AOrBA,ApB4DA,AENA,AJYA;AatCA,AMlBA,ADGA,AFMA,Af6CA,AavCA,ANkBA,AGTA,ADGA,AENA,AHSA,AQxBA,AMlBA,AHSA,ACHA,ANkBA,AOrBA,ApB4DA,AENA,AJYA;AatCA,AMlBA,ADGA,AFMA,Af6CA,AavCA,ANkBA,AGTA,ADGA,AENA,AHSA,AQxBA,AMlBA,AHSA,ACHA,ANkBA,AOrBA,ApB4DA,AENA,AJYA;AatCA,AMlBA,ADGA,AFMA,Af6CA,AavCA,ANkBA,AGTA,ADGA,AENA,AHSA,AQxBA,AOrBA,ADGA,AHSA,ACHA,ANkBA,AOrBA,ApB4DA,AFMA;AatCA,AMlBA,ADGA,AFMA,Af6CA,AavCA,ANkBA,AGTA,ADGA,AENA,AHSA,AQxBA,AOrBA,ADGA,AHSA,ACHA,ANkBA,AOrBA,ApB4DA,AFMA;AatCA,AMlBA,ADGA,AFMA,Af6CA,AavCA,ANkBA,AGTA,ADGA,AENA,AHSA,AQxBA,AOrBA,ADGA,AHSA,ACHA,ANkBA,AOrBA,ApB4DA,AFMA;AatCA,AMlBA,ADGA,AFMA,Af6CA,AavCA,ANkBA,AGTA,ADGA,AENA,AHSA,AgBhDA,ADGA,ADGA,AHSA,ACHA,ANkBA,AOrBA,ApB4DA,AFMA;AatCA,AMlBA,ADGA,AFMA,Af6CA,AavCA,ANkBA,AGTA,ADGA,AENA,AHSA,AgBhDA,ADGA,ADGA,AHSA,ACHA,ANkBA,AOrBA,ApB4DA,AFMA;AatCA,AMlBA,ADGA,AFMA,Af6CA,AavCA,ANkBA,AGTA,ADGA,AENA,AHSA,AgBhDA,ADGA,ADGA,AHSA,ACHA,ANkBA,AOrBA,ApB4DA,AFMA;AatCA,AMlBA,ADGA,AFMA,Af6CA,AavCA,ANkBA,AGTA,ADGA,AENA,AHSA,AgBhDA,ADGA,ADGA,AHSA,ACHA,ANkBA,AOrBA,ApB4DA,AwBxEA,A1B8EA;AatCA,AMlBA,ADGA,AFMA,Af6CA,AavCA,ANkBA,AGTA,ADGA,AENA,AHSA,AgBhDA,ADGA,ADGA,AHSA,ACHA,ANkBA,AOrBA,ApB4DA,AwBxEA,A1B8EA;AatCA,AMlBA,ADGA,AFMA,AFMA,ANkBA,AGTA,ADGA,AENA,AHSA,AgBhDA,ADGA,ADGA,AHSA,ACHA,ANkBA,AOrBA,ApB4DA,AwBxEA,A1B8EA;AatCA,AMlBA,ADGA,AFMA,AWjCA,AbuCA,ANkBA,AGTA,ACHA,AHSA,AgBhDA,ADGA,ADGA,AHSA,ACHA,ANkBA,AOrBA,ApB4DA,AwBxEA,A1B8EA;AatCA,AMlBA,ADGA,AFMA,AWjCA,AbuCA,AHSA,ACHA,AHSA,AgBhDA,ADGA,ADGA,AHSA,ACHA,ANkBA,AOrBA,ApB4DA,AwBxEA,A1B8EA;AatCA,AMlBA,ADGA,AFMA,AWjCA,AbuCA,AHSA,ACHA,AHSA,AgBhDA,ADGA,ADGA,AHSA,ACHA,ANkBA,AOrBA,ApB4DA,AwBxEA,A1B8EA;AatCA,AMlBA,ADGA,AFMA,AWjCA,AbuCA,AHSA,ACHA,AHSA,AmBzDA,AHSA,ADGA,ADGA,AHSA,ACHA,ANkBA,AOrBA,ApB4DA,AwBxEA,A1B8EA;AatCA,AMlBA,ADGA,AFMA,AWjCA,AbuCA,AHSA,ACHA,AHSA,AmBzDA,AHSA,ADGA,ADGA,AHSA,ACHA,ANkBA,AOrBA,ApB4DA,AwBxEA,A1B8EA;AatCA,AMlBA,ADGA,AFMA,AWjCA,AbuCA,AHSA,ACHA,AHSA,AmBzDA,AJYA,ADGA,AHSA,ACHA,ANkBA,AOrBA,ApB4DA,AwBxEA,A1B8EA;AatCA,AMlBA,ADGA,AFMA,AWjCA,AbuCA,AHSA,ACHA,AHSA,AmBzDA,ACHA,ALeA,ADGA,AHSA,ACHA,ANkBA,AOrBA,ApB4DA,AwBxEA,A1B8EA;AatCA,AMlBA,ADGA,AFMA,AWjCA,AbuCA,AHSA,ACHA,AHSA,AmBzDA,ACHA,ALeA,ADGA,AHSA,ACHA,ANkBA,AOrBA,ApB4DA,AwBxEA,A1B8EA;AatCA,AMlBA,ADGA,AFMA,AWjCA,AbuCA,AHSA,ACHA,AHSA,AmBzDA,ACHA,ALeA,ADGA,AHSA,ACHA,ANkBA,AOrBA,ApB4DA,AwBxEA,A1B8EA;AatCA,AMlBA,ADGA,AFMA,AWjCA,AbuCA,AFMA,AHSA,AqB/DA,AFMA,ACHA,ALeA,ADGA,AHSA,ACHA,ANkBA,AbuCA,AwBxEA,A1B8EA;AatCA,AMlBA,ADGA,AFMA,AWjCA,AbuCA,AFMA,AHSA,AqB/DA,AFMA,ACHA,ALeA,ADGA,AHSA,ACHA,ANkBA,AbuCA,AwBxEA,A1B8EA;AatCA,AMlBA,ADGA,AFMA,AWjCA,AbuCA,AFMA,AHSA,AqB/DA,AFMA,ACHA,ALeA,ADGA,AHSA,ACHA,ANkBA,AbuCA,AwBxEA,A1B8EA;AatCA,AMlBA,ADGA,AFMA,AWjCA,AbuCA,AFMA,AHSA,AqB/DA,AFMA,ACHA,ALeA,ADGA,AQxBA,AXiCA,ACHA,ANkBA,AbuCA,AwBxEA,A1B8EA;AatCA,AMlBA,ADGA,AFMA,AWjCA,AbuCA,AFMA,AHSA,AqB/DA,AFMA,ACHA,ALeA,ADGA,AQxBA,AXiCA,ACHA,ANkBA,AbuCA,AwBxEA,A1B8EA;AatCA,AMlBA,ADGA,AFMA,AWjCA,AbuCA,AFMA,AHSA,AqB/DA,AFMA,ACHA,ALeA,ADGA,AQxBA,AXiCA,ACHA,ANkBA,AbuCA,AwBxEA,A1B8EA;AatCA,AMlBA,ADGA,AFMA,AWjCA,AbuCA,AFMA,AHSA,AuBrEA,AFMA,AFMA,ACHA,ALeA,ADGA,AQxBA,AXiCA,ACHA,ANkBA,AbuCA,AwBxEA,A1B8EA;AatCA,AMlBA,ADGA,AFMA,AFMA,AFMA,AHSA,AuBrEA,AFMA,AFMA,ACHA,ALeA,ADGA,AQxBA,AXiCA,ACHA,ANkBA,AbuCA,AwBxEA,A1B8EA;AatCA,AMlBA,ADGA,AJYA,AFMA,AoB5DA,AFMA,AFMA,ACHA,ALeA,ADGA,AQxBA,AXiCA,ACHA,ANkBA,AbuCA,AwBxEA,A1B8EA;AatCA,AMlBA,ADGA,AJYA,AkBtDA,AFMA,AFMA,ACHA,ALeA,ADGA,AQxBA,AXiCA,ACHA,AYpCA,AlBsDA,AbuCA,AwBxEA,A1B8EA;AatCA,AMlBA,ADGA,AJYA,AkBtDA,AFMA,AFMA,ACHA,ALeA,ADGA,AQxBA,AXiCA,ACHA,AYpCA,AlBsDA,AbuCA,AwBxEA,A1B8EA;AatCA,AMlBA,ADGA,AJYA,AkBtDA,AFMA,AFMA,ACHA,ALeA,ADGA,AQxBA,AXiCA,ACHA,AYpCA,AlBsDA,AbuCA,AwBxEA,A1B8EA;AkCrGA,ArB+DA,AMlBA,ADGA,AJYA,AkBtDA,AHSA,ALeA,ADGA,AQxBA,AXiCA,ACHA,AYpCA,AlBsDA,AbuCA,AwBxEA,A1B8EA;AkCrGA,ArB+DA,AMlBA,ADGA,AJYA,AkBtDA,AHSA,ALeA,ADGA,AQxBA,AXiCA,ACHA,AYpCA,AlBsDA,AbuCA,AwBxEA,A1B8EA;AkCrGA,ArB+DA,AMlBA,ADGA,AJYA,AkBtDA,AHSA,ALeA,ADGA,AQxBA,AXiCA,ACHA,AYpCA,AlBsDA,AbuCA,AwBxEA,A1B8EA;AkCrGA,ArB+DA,AsBlEA,AhBgDA,ADGA,AJYA,AkBtDA,AHSA,ALeA,ADGA,AQxBA,AXiCA,ACHA,AYpCA,AlBsDA,AbuCA,AwBxEA,A1B8EA;AkCrGA,ArB+DA,AsBlEA,AhBgDA,ADGA,AJYA,AkBtDA,AHSA,ALeA,ADGA,AQxBA,AXiCA,ACHA,AYpCA,AlBsDA,AbuCA,AwBxEA,A1B8EA;AkCrGA,ArB+DA,AsBlEA,AhBgDA,ADGA,AJYA,AkBtDA,AHSA,ALeA,ADGA,AQxBA,AXiCA,ACHA,AYpCA,AlBsDA,AbuCA,AwBxEA,A1B8EA;AkCrGA,ArB+DA,AsBlEA,AhBgDA,ADGA,AJYA,AkBtDA,AHSA,ALeA,ADGA,AQxBA,AKfA,AhBgDA,ACHA,AYpCA,AlBsDA,AWjCA,A1B8EA;AkCrGA,ArB+DA,AsBlEA,AhBgDA,ADGA,AJYA,AkBtDA,AHSA,ALeA,ADGA,AQxBA,AKfA,AhBgDA,ACHA,AYpCA,AlBsDA,AWjCA,A1B8EA;AkCrGA,ArB+DA,AsBlEA,AhBgDA,ADGA,AJYA,AkBtDA,AHSA,ALeA,ADGA,AQxBA,AKfA,AhBgDA,ACHA,AYpCA,AlBsDA,AWjCA,A1B8EA;AkCrGA,ArB+DA,AsBlEA,AhBgDA,ADGA,AJYA,AkBtDA,AHSA,ALeA,ADGA,AQxBA,AKfA,AhBgDA,ACHA,AYpCA,AlBsDA,AsBlEA,AXiCA,A1B8EA;AkCrGA,ACHA,AhBgDA,ADGA,AJYA,AkBtDA,AHSA,ALeA,ADGA,AQxBA,AKfA,AhBgDA,ACHA,AYpCA,AlBsDA,AsBlEA,AXiCA,A1B8EA;AkCrGA,ACHA,AhBgDA,ADGA,AJYA,AkBtDA,AHSA,ALeA,ADGA,AQxBA,AKfA,AhBgDA,ACHA,AYpCA,AlBsDA,AsBlEA,AXiCA,A1B8EA;AsCjHA,AJYA,ACHA,AhBgDA,ADGA,AJYA,AkBtDA,AHSA,ALeA,ADGA,AQxBA,AKfA,AhBgDA,ACHA,AYpCA,AlBsDA,AsBlEA,AXiCA,A1B8EA;AsCjHA,AJYA,ACHA,AhBgDA,ADGA,AJYA,AkBtDA,AHSA,ALeA,ADGA,AQxBA,AKfA,AhBgDA,ACHA,AYpCA,AlBsDA,AsBlEA,AXiCA,A1B8EA;AsCjHA,AJYA,ACHA,AhBgDA,ADGA,AJYA,AkBtDA,AHSA,ALeA,ADGA,AQxBA,AKfA,AhBgDA,ACHA,AYpCA,AlBsDA,AsBlEA,AXiCA,A1B8EA;AsCjHA,AJYA,ACHA,AhBgDA,ADGA,AJYA,AkBtDA,AHSA,ALeA,ADGA,AQxBA,AKfA,AhBgDA,ACHA,AYpCA,AlBsDA,AsBlEA,AXiCA,A1B8EA;AsCjHA,AJYA,ACHA,AhBgDA,ADGA,AJYA,AkBtDA,AHSA,ALeA,ADGA,AQxBA,AKfA,AhBgDA,ACHA,AYpCA,AlBsDA,AsBlEA,AXiCA,A1B8EA;AsCjHA,AJYA,ACHA,AhBgDA,ADGA,AJYA,AkBtDA,AHSA,ALeA,ADGA,AQxBA,AKfA,AhBgDA,ACHA,AYpCA,AlBsDA,AsBlEA,AXiCA,A1B8EA;AsCjHA,AJYA,ACHA,AhBgDA,ADGA,AJYA,AkBtDA,AHSA,ALeA,ADGA,AQxBA,AKfA,AhBgDA,ACHA,AYpCA,AlBsDA,AsBlEA,AXiCA,A1B8EA;AsCjHA,AJYA,ACHA,AhBgDA,ADGA,AJYA,AkBtDA,AHSA,ALeA,ADGA,AQxBA,AKfA,AhBgDA,ACHA,AYpCA,AlBsDA,AsBlEA,AXiCA,A1B8EA;AsCjHA,AJYA,ACHA,AhBgDA,ADGA,AJYA,AkBtDA,AHSA,ALeA,ADGA,AQxBA,AKfA,AhBgDA,ACHA,AYpCA,AlBsDA,AsBlEA,AXiCA,A1B8EA;AsCjHA,AJYA,ACHA,AhBgDA,ADGA,AJYA,AkBtDA,AHSA,ALeA,ADGA,AQxBA,AKfA,AhBgDA,ACHA,AYpCA,AlBsDA,AsBlEA,AXiCA,A1B8EA;AsCjHA,AJYA,ACHA,AhBgDA,ADGA,AJYA,AkBtDA,AHSA,ALeA,ADGA,AQxBA,AKfA,AhBgDA,ACHA,AYpCA,AlBsDA,AsBlEA,AXiCA,A1B8EA;AsCjHA,AJYA,ACHA,AhBgDA,ADGA,AJYA,AkBtDA,AHSA,ALeA,ADGA,AQxBA,AKfA,AhBgDA,ACHA,AYpCA,AlBsDA,AsBlEA,AXiCA,A1B8EA;AsCjHA,AJYA,ACHA,AhBgDA,ADGA,AJYA,AkBtDA,AHSA,ALeA,ADGA,AQxBA,AKfA,AhBgDA,ACHA,AYpCA,AlBsDA,AsBlEA,AXiCA,A1B8EA;AsCjHA,AJYA,ACHA,AhBgDA,ADGA,AJYA,AkBtDA,AHSA,ALeA,ADGA,AQxBA,AKfA,AhBgDA,ACHA,AYpCA,AlBsDA,AsBlEA,AXiCA,A1B8EA;AsCjHA,AJYA,ACHA,AhBgDA,ADGA,AJYA,AkBtDA,AHSA,ALeA,ADGA,AQxBA,AKfA,AhBgDA,ACHA,AYpCA,AlBsDA,AsBlEA,AXiCA,A1B8EA;AsCjHA,AJYA,ACHA,AhBgDA,ADGA,AJYA,AkBtDA,AHSA,ALeA,ADGA,AQxBA,AKfA,AhBgDA,ACHA,AYpCA,AlBsDA,AsBlEA,AXiCA,A1B8EA;AsCjHA,AJYA,ACHA,AhBgDA,ADGA,AJYA,AkBtDA,ARwBA,ADGA,AQxBA,AKfA,AhBgDA,ACHA,AYpCA,AlBsDA,AsBlEA,AXiCA,A1B8EA;AsCjHA,AJYA,ACHA,AhBgDA,ADGA,AJYA,AkBtDA,ARwBA,ADGA,AQxBA,AKfA,AhBgDA,ACHA,AYpCA,AlBsDA,AsBlEA,AXiCA,A1B8EA;AsCjHA,AJYA,ACHA,AhBgDA,ADGA,AJYA,AkBtDA,ARwBA,ADGA,AQxBA,AKfA,AhBgDA,ACHA,AYpCA,AlBsDA,AsBlEA,AXiCA,A1B8EA;AsCjHA,AJYA,ACHA,AhBgDA,ADGA,AJYA,AkBtDA,ARwBA,ADGA,AQxBA,AKfA,AhBgDA,ACHA,AYpCA,AlBsDA,AsBlEA,AXiCA,A1B8EA;AsCjHA,AJYA,ACHA,AhBgDA,ADGA,AJYA,AkBtDA,ARwBA,ADGA,AQxBA,AKfA,AhBgDA,ACHA,AYpCA,AlBsDA,AsBlEA,ArC+GA;AsCjHA,AJYA,ACHA,AhBgDA,ADGA,AJYA,AkBtDA,ARwBA,ADGA,AQxBA,AKfA,AhBgDA,ACHA,AYpCA,AlBsDA,AsBlEA,ArC+GA;AkCrGA,ACHA,AhBgDA,ADGA,AJYA,AkBtDA,ARwBA,ADGA,AQxBA,AKfA,AhBgDA,ACHA,AYpCA,AlBsDA,AsBlEA,ArC+GA;AkCrGA,ACHA,AjBmDA,AJYA,AkBtDA,ARwBA,ADGA,AQxBA,AKfA,AhBgDA,ACHA,AYpCA,AlBsDA,AsBlEA,ArC+GA;AkCrGA,ACHA,AjBmDA,AJYA,AkBtDA,ARwBA,ADGA,AQxBA,AKfA,AhBgDA,ACHA,AYpCA,AlBsDA,AsBlEA,ArC+GA;AkCrGA,ACHA,AjBmDA,AJYA,AkBtDA,ARwBA,ADGA,AQxBA,AXiCA,ACHA,AYpCA,AlBsDA,AsBlEA,ArC+GA;AkCrGA,ACHA,AjBmDA,AJYA,AkBtDA,ARwBA,ADGA,AQxBA,AXiCA,ACHA,AYpCA,AlBsDA,AsBlEA,ArC+GA;AkCrGA,ACHA,AjBmDA,AJYA,AkBtDA,ARwBA,ADGA,AQxBA,AXiCA,ACHA,AYpCA,AlBsDA,AsBlEA,ArC+GA;AkCrGA,ACHA,AjBmDA,AJYA,AkBtDA,ARwBA,ADGA,AQxBA,AXiCA,ACHA,AYpCA,AlBsDA,AsBlEA,ArC+GA;AkCrGA,ACHA,AjBmDA,AJYA,AkBtDA,ARwBA,ADGA,AQxBA,AXiCA,ACHA,AYpCA,AlBsDA,AsBlEA,ArC+GA;AkCrGA,ACHA,AjBmDA,AJYA,AkBtDA,ARwBA,ADGA,AQxBA,AXiCA,ACHA,AYpCA,AlBsDA,AsBlEA,ArC+GA;AkCrGA,ACHA,AjBmDA,AJYA,AkBtDA,ARwBA,ADGA,AQxBA,AXiCA,ACHA,AYpCA,AlBsDA,AsBlEA,ArC+GA;AkCrGA,ACHA,AjBmDA,AJYA,AkBtDA,ARwBA,ADGA,AQxBA,AXiCA,ACHA,AYpCA,AlBsDA,AsBlEA,ArC+GA;AkCrGA,ACHA,AjBmDA,AJYA,AkBtDA,ARwBA,ADGA,AQxBA,AXiCA,ACHA,AYpCA,AlBsDA,AsBlEA,ArC+GA;AkCrGA,ACHA,AjBmDA,AJYA,AkBtDA,ARwBA,ADGA,AQxBA,AXiCA,ACHA,AYpCA,AlBsDA,AsBlEA,ArC+GA;AkCrGA,ACHA,AjBmDA,AJYA,AkBtDA,ARwBA,ADGA,AQxBA,AXiCA,ACHA,AYpCA,AlBsDA,AsBlEA,ArC+GA;AkCrGA,ACHA,AjBmDA,AJYA,AkBtDA,ARwBA,ADGA,AQxBA,AXiCA,ACHA,AYpCA,AlBsDA,AsBlEA,ArC+GA;AkCrGA,ACHA,AjBmDA,AJYA,AkBtDA,ARwBA,ADGA,AQxBA,AV8BA,AYpCA,AlBsDA,AsBlEA,ArC+GA;AkCrGA,ACHA,AjBmDA,AJYA,AkBtDA,ARwBA,ADGA,AQxBA,AV8BA,AYpCA,AlBsDA,AsBlEA,ArC+GA;AkCrGA,ACHA,AjBmDA,AJYA,AkBtDA,ARwBA,ADGA,AQxBA,AV8BA,AYpCA,AlBsDA,AsBlEA,ArC+GA;AkCrGA,ACHA,AjBmDA,AJYA,AkBtDA,ARwBA,ADGA,AQxBA,AV8BA,AYpCA,AlBsDA,AsBlEA,ArC+GA;AkCrGA,ACHA,AjBmDA,AJYA,AkBtDA,ARwBA,ADGA,AQxBA,AV8BA,AYpCA,AlBsDA,AsBlEA,ArC+GA;AkCrGA,ACHA,AjBmDA,AJYA,AkBtDA,ARwBA,ADGA,AQxBA,AV8BA,AYpCA,AlBsDA,AsBlEA;AHUA,ACHA,AjBmDA,AJYA,AkBtDA,ARwBA,ADGA,AQxBA,AV8BA,AYpCA,AlBsDA,AsBlEA;AHUA,ACHA,AjBmDA,AJYA,AkBtDA,ARwBA,ADGA,AQxBA,AV8BA,AYpCA,AlBsDA,AsBlEA;AHUA,ACHA,AjBmDA,AJYA,AkBtDA,ARwBA,ADGA,AQxBA,AV8BA,AYpCA,AlBsDA,AsBlEA;AHUA,ACHA,AjBmDA,AJYA,AkBtDA,ARwBA,ADGA,AQxBA,AV8BA,AYpCA,AlBsDA,AsBlEA;AHUA,ACHA,AjBmDA,AJYA,AkBtDA,ARwBA,ADGA,AQxBA,AV8BA,AYpCA,AlBsDA,AsBlEA;AHUA,ACHA,AjBmDA,AJYA,AkBtDA,ARwBA,ADGA,AQxBA,AV8BA,AYpCA,AlBsDA,AsBlEA;AHUA,ACHA,AjBmDA,AJYA,AkBtDA,ARwBA,ADGA,AQxBA,AENA,AlBsDA,AsBlEA;AHUA,ACHA,AjBmDA,AJYA,AkBtDA,ARwBA,ADGA,AQxBA,AENA,AlBsDA,AsBlEA;AHUA,ACHA,AjBmDA,AJYA,AkBtDA,ARwBA,ADGA,AQxBA,AENA,AlBsDA,AsBlEA;AHUA,ACHA,AjBmDA,AJYA,AkBtDA,ARwBA,ADGA,AQxBA,AENA,AlBsDA,AsBlEA;AHUA,ACHA,AjBmDA,AJYA,AkBtDA,ARwBA,ADGA,AQxBA,AENA,AlBsDA,AsBlEA;AHUA,ACHA,AjBmDA,AJYA,AkBtDA,ARwBA,ADGA,AQxBA,AENA,AlBsDA,AsBlEA;AHUA,ACHA,AjBmDA,AJYA,AkBtDA,ARwBA,ADGA,AQxBA,AENA,AlBsDA,AsBlEA;AHUA,ACHA,AjBmDA,AJYA,AkBtDA,ARwBA,ADGA,AQxBA,AENA,AlBsDA,AsBlEA;AHUA,ACHA,AjBmDA,AJYA,AkBtDA,ARwBA,ADGA,AQxBA,AENA,AlBsDA,AsBlEA;AHUA,ACHA,AjBmDA,AJYA,AkBtDA,ARwBA,ADGA,AQxBA,AENA,AlBsDA,AsBlEA;AHUA,ACHA,AjBmDA,AJYA,AkBtDA,ARwBA,ADGA,AQxBA,AENA,AlBsDA,AsBlEA;AHUA,ACHA,AjBmDA,AJYA,AkBtDA,ARwBA,ADGA,AQxBA,AENA,AlBsDA,AsBlEA;AHUA,ACHA,AjBmDA,AJYA,AkBtDA,ARwBA,ADGA,AQxBA,AENA,AlBsDA,AsBlEA;AHUA,ACHA,AjBmDA,AJYA,AkBtDA,ARwBA,ADGA,AQxBA,AENA,AlBsDA,AsBlEA;AHUA,ACHA,AjBmDA,AJYA,AkBtDA,ARwBA,ADGA,AQxBA,AENA,AlBsDA,AsBlEA;AHUA,ACHA,AjBmDA,AJYA,AkBtDA,ARwBA,ADGA,AQxBA,AENA,AlBsDA,AsBlEA;AHUA,ACHA,AjBmDA,AJYA,AkBtDA,ARwBA,ADGA,AQxBA,AENA,AlBsDA,AsBlEA;AHUA,ACHA,AjBmDA,AJYA,AkBtDA,ARwBA,ADGA,AQxBA,AENA,AlBsDA,AsBlEA;AHUA,ACHA,AjBmDA,AJYA,AkBtDA,ARwBA,ADGA,AQxBA,AENA,AlBsDA,AsBlEA;AHUA,ACHA,AjBmDA,AJYA,AkBtDA,ARwBA,ADGA,AQxBA,AENA,AlBsDA,AsBlEA;AHUA,ACHA,AjBmDA,AJYA,AkBtDA,ARwBA,ADGA,AQxBA,AENA,AlBsDA,AsBlEA;AHUA,ACHA,AjBmDA,AJYA,AkBtDA,ARwBA,ADGA,AQxBA,AENA,AlBsDA,AsBlEA;AHUA,ACHA,AjBmDA,AJYA,AkBtDA,ARwBA,ADGA,AQxBA,AENA,AlBsDA,AsBlEA;AHUA,ACHA,AjBmDA,AJYA,AkBtDA,ARwBA,ADGA,AQxBA,AENA,AlBsDA,AsBlEA;AHUA,ACHA,AjBmDA,AJYA,AkBtDA,ARwBA,ADGA,AQxBA,AENA,AlBsDA,AsBlEA;AHUA,ACHA,AjBmDA,AJYA,AkBtDA,ARwBA,ADGA,AQxBA,AENA,AIZA;AHUA,ACHA,AjBmDA,AJYA,AkBtDA,ARwBA,ADGA,AQxBA,AENA,AIZA;AHUA,ACHA,AjBmDA,AJYA,AkBtDA,ARwBA,ADGA,AQxBA,AENA,AIZA;AHUA,ACHA,AjBmDA,AJYA,AkBtDA,ARwBA,ADGA,AQxBA,AENA,AIZA;AHUA,ACHA,AjBmDA,AJYA,AkBtDA,ARwBA,ADGA,AQxBA,AENA,AIZA;AHUA,ACHA,AjBmDA,AJYA,AkBtDA,ARwBA,ADGA,AQxBA,AENA,AIZA;AHUA,ACHA,AjBmDA,AJYA,AkBtDA,ARwBA,ADGA,AQxBA,AENA,AIZA;AHUA,ACHA,AjBmDA,AJYA,AkBtDA,ARwBA,ADGA,AQxBA,AENA,AIZA;AHUA,ACHA,AjBmDA,AJYA,AkBtDA,ARwBA,ADGA,AQxBA,AENA,AIZA;AHUA,ACHA,AjBmDA,AJYA,AkBtDA,ARwBA,ADGA,AQxBA,AENA,AIZA;AHUA,ACHA,AjBmDA,AJYA,AkBtDA,ARwBA,ADGA,AQxBA,AENA,AIZA;AHUA,ACHA,AjBmDA,AJYA,AkBtDA,ARwBA,ADGA,AQxBA,AENA,AIZA;AHUA,ACHA,AjBmDA,AJYA,AkBtDA,ARwBA,ADGA,AQxBA,AENA,AIZA;AHUA,ACHA,AjBmDA,AJYA,AkBtDA,ARwBA,ADGA,AQxBA,AENA,AIZA;AHUA,ACHA,ArB+DA,AkBtDA,ARwBA,ADGA,AQxBA,AENA,AIZA;AHUA,ACHA,ArB+DA,AkBtDA,ARwBA,ADGA,AQxBA,AENA,AIZA;AHUA,ACHA,ArB+DA,AkBtDA,ARwBA,ADGA,AQxBA,AENA,AIZA;AHUA,ACHA,ArB+DA,AkBtDA,ARwBA,ADGA,AQxBA,AENA,AIZA;AHUA,ACHA,ArB+DA,AkBtDA,ARwBA,ADGA,AQxBA,AENA,AIZA;AHUA,ACHA,ArB+DA,AkBtDA,ARwBA,ADGA,AQxBA,AENA,AIZA;AHUA,ACHA,ArB+DA,AkBtDA,ARwBA,ADGA,AQxBA,AENA,AIZA;AHUA,ACHA,ArB+DA,AkBtDA,ARwBA,ADGA,AQxBA,AENA,AIZA;AHUA,ACHA,ArB+DA,AkBtDA,ARwBA,ADGA,AQxBA,AENA,AIZA;AHUA,ACHA,ArB+DA,AkBtDA,ARwBA,ADGA,AQxBA,AENA,AIZA;AHUA,ACHA,ArB+DA,AkBtDA,ARwBA,ADGA,AQxBA,AENA,AIZA;AHUA,ACHA,ArB+DA,AkBtDA,ARwBA,ADGA,AQxBA,AENA,AIZA;AHUA,ACHA,ArB+DA,AkBtDA,ARwBA,ADGA,AQxBA,AENA,AIZA;AHUA,ACHA,ArB+DA,AkBtDA,ARwBA,ADGA,AQxBA,AENA,AIZA;AHUA,ACHA,ArB+DA,AkBtDA,ARwBA,ADGA,AQxBA,AENA,AIZA;AHUA,ACHA,ArB+DA,AkBtDA,ARwBA,ADGA,AQxBA,AENA;ACFA,ACHA,ArB+DA,AkBtDA,ARwBA,ADGA,AQxBA,AENA;ACFA,ACHA,ArB+DA,AkBtDA,ARwBA,ADGA,AQxBA,AENA;ACFA,ACHA,ArB+DA,AkBtDA,ARwBA,ADGA,AQxBA,AENA;ACFA,ACHA,ArB+DA,AkBtDA,ARwBA,ADGA,AQxBA,AENA;ACFA,ACHA,ArB+DA,AkBtDA,ARwBA,ADGA,AQxBA,AENA;ACFA,ACHA,ArB+DA,AkBtDA,ARwBA,ADGA,AQxBA,AENA;ACFA,ACHA,ArB+DA,AkBtDA,ARwBA,ADGA,AQxBA,AENA;ACFA,ACHA,ArB+DA,AkBtDA,ARwBA,ADGA,AQxBA,AENA;ACFA,ACHA,ArB+DA,AkBtDA,ARwBA,ADGA,AQxBA,AENA;AELA,ArB+DA,AkBtDA,ARwBA,ADGA,AQxBA,AENA;AELA,ArB+DA,AkBtDA,ARwBA,ADGA,AQxBA,AENA;AELA,ArB+DA,AkBtDA,ARwBA,ADGA,AQxBA,AENA;AELA,ArB+DA,AkBtDA,ARwBA,ADGA,AQxBA,AENA;AELA,ArB+DA,AkBtDA,ARwBA,ADGA,AQxBA,AENA;AELA,ArB+DA,AkBtDA,ARwBA,ADGA,AQxBA,AENA;AELA,ArB+DA,AkBtDA,ARwBA,ADGA,AU9BA;AELA,ArB+DA,AkBtDA,ARwBA,ADGA,AU9BA;AELA,ArB+DA,AkBtDA,ARwBA,ADGA,AU9BA;AELA,ArB+DA,AkBtDA,ARwBA,ADGA,AU9BA;AELA,ArB+DA,AkBtDA,ARwBA,ADGA,AU9BA;AELA,ArB+DA,AkBtDA,ARwBA,ADGA,AU9BA;AELA,ArB+DA,AkBtDA,ARwBA,ADGA,AU9BA;AELA,ArB+DA,AkBtDA,ARwBA,ADGA,AU9BA;AELA,ArB+DA,AkBtDA,ARwBA,ADGA,AU9BA;AELA,ArB+DA,AkBtDA,ARwBA,ADGA,AU9BA;AELA,ArB+DA,AkBtDA,ARwBA,ADGA,AU9BA;AELA,ArB+DA,AkBtDA,ARwBA,ADGA,AU9BA;AELA,ArB+DA,AkBtDA,ARwBA,ADGA,AU9BA;AELA,ArB+DA,AkBtDA,ARwBA,ADGA,AU9BA;AELA,ArB+DA,AkBtDA,ARwBA,ADGA,AU9BA;AELA,ArB+DA,AkBtDA,ARwBA,ADGA,AU9BA;AELA,ArB+DA,AkBtDA,ARwBA,ADGA,AU9BA;AELA,ArB+DA,AkBtDA,ARwBA,ADGA,AU9BA;AELA,ArB+DA,AkBtDA,ARwBA,ADGA,AU9BA;AELA,ArB+DA,AkBtDA,ARwBA,ADGA,AU9BA;AELA,ArB+DA,AkBtDA,ARwBA,ADGA,AU9BA;AELA,ArB+DA,AkBtDA,ARwBA,ADGA,AU9BA;AELA,ArB+DA,AkBtDA,ARwBA,ADGA,AU9BA;AELA,ArB+DA,AkBtDA,ARwBA,ADGA,AU9BA;AELA,ArB+DA,AkBtDA,ARwBA,ADGA,AU9BA;AELA,ArB+DA,AkBtDA,ARwBA,ADGA,AU9BA;AELA,ArB+DA,AkBtDA,ARwBA,ADGA,AU9BA;AELA,ArB+DA,AkBtDA,ARwBA,ADGA,AU9BA;AELA,ArB+DA,AkBtDA,ARwBA,ADGA,AU9BA;AELA,AHSA,ARwBA,ADGA,AU9BA;AELA,AHSA,ARwBA,ADGA,AU9BA;AELA,AHSA,ARwBA,ADGA,AU9BA;AELA,AHSA,ARwBA,ADGA,AU9BA;AELA,AHSA,ARwBA,ADGA,AU9BA;AELA,AHSA,ARwBA,ADGA,AU9BA;AELA,AHSA,ARwBA,ADGA,AU9BA;AELA,AHSA,ARwBA,ADGA,AU9BA;AELA,AHSA,ARwBA,ADGA,AU9BA;AELA,AHSA,ARwBA,ADGA,AU9BA;AELA,AHSA,ARwBA,ADGA,AU9BA;AELA,AHSA,ARwBA,ADGA,AU9BA;AELA,AHSA,ARwBA,ADGA,AU9BA;AELA,AHSA,ARwBA,ADGA,AU9BA;AELA,AHSA,ARwBA,ADGA,AU9BA;AELA,AHSA,ARwBA,ADGA,AU9BA;AELA,AHSA,ARwBA,ADGA,AU9BA;AELA,AHSA,ARwBA,ADGA,AU9BA;AELA,AHSA,ARwBA,ADGA,AU9BA;AELA,AHSA,ARwBA,ADGA,AU9BA;AELA,AHSA,ARwBA,ADGA,AU9BA;AELA,AHSA,ARwBA,ADGA,AU9BA;AELA,AHSA,ARwBA,ADGA,AU9BA;AELA,AHSA,ARwBA,ADGA,AU9BA;AELA,AHSA,ARwBA,ADGA,AU9BA;AELA,AHSA,ARwBA,ADGA,AU9BA;AELA,AHSA,ARwBA,ADGA,AU9BA;AELA,AHSA,ARwBA,ADGA,AU9BA;AELA,AHSA,ARwBA,ADGA,AU9BA;AELA,AHSA,ARwBA,ADGA,AU9BA;AELA,AHSA,ARwBA,ADGA,AU9BA;AELA,AHSA,ARwBA,ADGA,AU9BA;AELA,AHSA,ARwBA,ADGA,AU9BA;AELA,AHSA,ARwBA,ADGA,AU9BA;AELA,AHSA,ARwBA,ADGA,AU9BA;AELA,AHSA,ARwBA,ADGA,AU9BA;AELA,AHSA,ARwBA,ADGA,AU9BA;AELA,AHSA,ARwBA,ADGA,AU9BA;AELA,AHSA,ARwBA,ADGA,AU9BA;AELA,AHSA,ARwBA,ADGA,AU9BA;AELA,AHSA,ARwBA,ADGA,AU9BA;AELA,AHSA,ARwBA,ADGA,AU9BA;AELA,AHSA,ARwBA,ADGA,AU9BA;AELA,AHSA,ARwBA,ADGA,AU9BA;AELA,AHSA,ARwBA,ADGA,AU9BA;AELA,AHSA,ARwBA,ADGA,AU9BA;AELA,AHSA,ARwBA,ADGA,AU9BA;AELA,AHSA,ARwBA,ADGA,AU9BA;AELA,AHSA,ARwBA,ADGA,AU9BA;AELA,AHSA,ARwBA,ADGA,AU9BA;AELA,AHSA,ARwBA,ADGA,AU9BA;AELA,AHSA,ARwBA,ADGA,AU9BA;AELA,AHSA,ARwBA,ADGA,AU9BA;AELA,AHSA,ARwBA,ADGA,AU9BA;AELA,AHSA,ARwBA,ADGA,AU9BA;AELA,AHSA,ARwBA,ADGA,AU9BA;AELA,AHSA,ARwBA,ADGA,AU9BA;AELA,AHSA,ARwBA,ADGA,AU9BA;AELA,AHSA,ARwBA,ADGA,AU9BA;AELA,AHSA,ARwBA,ADGA,AU9BA;AELA,AHSA,ARwBA,ADGA,AU9BA;AELA,AHSA,ARwBA,ADGA,AU9BA;AELA,AHSA,ARwBA,ADGA,AU9BA;AELA,AHSA,ARwBA,ADGA,AU9BA;AELA,AHSA,ARwBA,ADGA,AU9BA;AELA,AHSA,ARwBA,ADGA,AU9BA;AELA,AHSA,ARwBA,ADGA,AU9BA;AELA,AHSA,ARwBA,ADGA,AU9BA;AELA,AHSA,ARwBA,ADGA,AU9BA;AELA,AHSA,ARwBA,ADGA,AU9BA;AELA,AHSA,ARwBA,ADGA,AU9BA;AELA,AHSA,ARwBA,ADGA,AU9BA;AELA,AHSA,ARwBA,ADGA,AU9BA;AELA,AHSA,ARwBA,ADGA,AU9BA;AELA,AHSA,ARwBA,ADGA,AU9BA;AELA,AHSA,ARwBA,ADGA,AU9BA;AELA,AHSA,ARwBA,ADGA,AU9BA;AELA,AHSA,ARwBA,ADGA,AU9BA;AELA,AHSA,ARwBA,ADGA,AU9BA;AELA,AHSA,ARwBA,ADGA,AU9BA;AELA,AHSA,ARwBA,ADGA,AU9BA;AELA,AHSA,ARwBA,ADGA,AU9BA;AELA,AXiCA,ADGA,AU9BA;AELA,AXiCA,ADGA,AU9BA;AELA,AXiCA,ADGA,AU9BA;AELA,AXiCA,ADGA,AU9BA;AELA,AXiCA,ADGA,AU9BA;AELA,AXiCA,ADGA,AU9BA;AELA,AXiCA,ADGA,AU9BA;AELA,AXiCA,ADGA,AU9BA;AELA,AXiCA,ADGA,AU9BA;AELA,AXiCA,ADGA,AU9BA;AELA,AXiCA,ADGA;AYnCA,AXiCA,ADGA;AYnCA,AXiCA,ADGA;AYnCA,AXiCA,ADGA;AYnCA,AXiCA,ADGA;AYnCA,AXiCA,ADGA;AYnCA,AXiCA,ADGA;AYnCA,AXiCA,ADGA;AYnCA,AXiCA,ADGA;AYnCA,AXiCA,ADGA;AYnCA,AXiCA,ADGA;AYnCA,AXiCA,ADGA;AYnCA,AXiCA,ADGA;AYnCA,AXiCA,ADGA;AYnCA,AXiCA,ADGA;AYnCA,AXiCA,ADGA;AYnCA,AXiCA,ADGA;AYnCA,AXiCA,ADGA;AYnCA,AXiCA,ADGA;AYnCA,AXiCA,ADGA;AYnCA,AXiCA,ADGA;AYnCA,AXiCA,ADGA;AYnCA,AXiCA,ADGA;AYnCA,AXiCA,ADGA;AYnCA,AXiCA,ADGA;AYnCA,AXiCA,ADGA;AYnCA,AXiCA,ADGA;AYnCA,AXiCA,ADGA;AYnCA,AXiCA,ADGA;AYnCA,AXiCA,ADGA;AYnCA,AXiCA,ADGA;AYnCA,AXiCA,ADGA;AYnCA,AXiCA,ADGA;AYnCA,AXiCA,ADGA;AYnCA,AXiCA,ADGA;AYnCA,AXiCA,ADGA;AYnCA,AXiCA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.js", "sourcesContent": ["\r\nObject.defineProperty(exports, \"__esModule\", { value: true });\r\nconst Geo = require(\"./geo/index\");\r\nconst collection_1 = require(\"./collection\");\r\nconst command_1 = require(\"./command\");\r\nconst index_1 = require(\"./serverDate/index\");\r\nconst index_2 = require(\"./regexp/index\");\r\nconst index_3 = require(\"./transaction/index\");\r\nconst index_4 = require(\"./ObjectId/index\");\r\nvar query_1 = require(\"./query\");\r\nexports.Query = query_1.Query;\r\nvar collection_2 = require(\"./collection\");\r\nexports.CollectionReference = collection_2.CollectionReference;\r\nvar document_1 = require(\"./document\");\r\nexports.DocumentReference = document_1.DocumentReference;\r\nclass Db {\r\n    constructor(config) {\r\n        this.config = config;\r\n        this.Geo = Geo;\r\n        this.serverDate = index_1.ServerDateConstructor;\r\n        this.command = command_1.Command;\r\n        this.RegExp = index_2.RegExpConstructor;\r\n        this.ObjectId = index_4.ObjectIdConstructor;\r\n        this.startTransaction = index_3.startTransaction;\r\n        this.runTransaction = index_3.runTransaction;\r\n    }\r\n    collection(collName) {\r\n        if (!collName) {\r\n            throw new Error('Collection name is required');\r\n        }\r\n        return new collection_1.CollectionReference(this, collName);\r\n    }\r\n    createCollection(collName) {\r\n        let request = new Db.reqClass(this.config);\r\n        const params = {\r\n            collectionName: collName\r\n        };\r\n        return request.send('database.addCollection', params);\r\n    }\r\n}\r\nexports.Db = Db;\r\n", "\r\nfunction __export(m) {\r\n    for (var p in m) if (!exports.hasOwnProperty(p)) exports[p] = m[p];\r\n}\r\nObject.defineProperty(exports, \"__esModule\", { value: true });\r\n__export(require(\"./point\"));\r\n__export(require(\"./lineString\"));\r\n__export(require(\"./polygon\"));\r\n__export(require(\"./multiPoint\"));\r\n__export(require(\"./multiLineString\"));\r\n__export(require(\"./multiPolygon\"));\r\n", "\r\nObject.defineProperty(exports, \"__esModule\", { value: true });\r\nconst validate_1 = require(\"../validate\");\r\nconst symbol_1 = require(\"../helper/symbol\");\r\nconst type_1 = require(\"../utils/type\");\r\nclass Point {\r\n    constructor(longitude, latitude) {\r\n        validate_1.Validate.isGeopoint('longitude', longitude);\r\n        validate_1.Validate.isGeopoint('latitude', latitude);\r\n        this.longitude = longitude;\r\n        this.latitude = latitude;\r\n    }\r\n    parse(key) {\r\n        return {\r\n            [key]: {\r\n                type: 'Point',\r\n                coordinates: [this.longitude, this.latitude]\r\n            }\r\n        };\r\n    }\r\n    toJSON() {\r\n        return {\r\n            type: 'Point',\r\n            coordinates: [\r\n                this.longitude,\r\n                this.latitude,\r\n            ],\r\n        };\r\n    }\r\n    toReadableString() {\r\n        return `[${this.longitude},${this.latitude}]`;\r\n    }\r\n    static validate(point) {\r\n        return point.type === 'Point' &&\r\n            type_1.isArray(point.coordinates) &&\r\n            validate_1.Validate.isGeopoint('longitude', point.coordinates[0]) &&\r\n            validate_1.Validate.isGeopoint('latitude', point.coordinates[1]);\r\n    }\r\n    get _internalType() {\r\n        return symbol_1.SYMBOL_GEO_POINT;\r\n    }\r\n}\r\nexports.Point = Point;\r\n", "\r\nObject.defineProperty(exports, \"__esModule\", { value: true });\r\nconst constant_1 = require(\"./constant\");\r\nconst util_1 = require(\"./util\");\r\nconst code_1 = require(\"./const/code\");\r\nconst utils_1 = require(\"./utils/utils\");\r\nconst type_1 = require(\"./utils/type\");\r\nconst symbol_1 = require(\"./helper/symbol\");\r\nconst validOptionsKeys = ['limit', 'offset', 'projection', 'order', 'multiple', 'timeout', 'raw'];\r\nclass Validate {\r\n    static isGeopoint(point, degree) {\r\n        if (util_1.Util.whichType(degree) !== constant_1.FieldType.Number) {\r\n            throw new Error('Geo Point must be number type');\r\n        }\r\n        const degreeAbs = Math.abs(degree);\r\n        if (point === 'latitude' && degreeAbs > 90) {\r\n            throw new Error('latitude should be a number ranges from -90 to 90');\r\n        }\r\n        else if (point === 'longitude' && degreeAbs > 180) {\r\n            throw new Error('longitude should be a number ranges from -180 to 180');\r\n        }\r\n        return true;\r\n    }\r\n    static isInteger(param, num) {\r\n        if (!Number.isInteger(num)) {\r\n            throw new Error(param + constant_1.ErrorCode.IntergerError);\r\n        }\r\n        return true;\r\n    }\r\n    static mustBeBoolean(param, bool) {\r\n        if (typeof bool !== 'boolean') {\r\n            throw new Error(param + constant_1.ErrorCode.BooleanError);\r\n        }\r\n        return true;\r\n    }\r\n    static isProjection(param, value) {\r\n        if (type_1.getType(value) !== 'object') {\r\n            throw utils_1.E(Object.assign(Object.assign({}, code_1.ERRORS.INVALID_PARAM), { message: `${param} projection must be an object` }));\r\n        }\r\n        for (const key in value) {\r\n            const subValue = value[key];\r\n            if (type_1.getType(subValue) === 'number') {\r\n                if (subValue !== 0 && subValue !== 1) {\r\n                    throw utils_1.E(Object.assign(Object.assign({}, code_1.ERRORS.INVALID_PARAM), { message: `if the value in projection is of number, it must be 0 or 1` }));\r\n                }\r\n            }\r\n            else if (type_1.getType(subValue) === 'object') {\r\n            }\r\n            else {\r\n                throw utils_1.E(Object.assign(Object.assign({}, code_1.ERRORS.INVALID_PARAM), { message: 'invalid projection' }));\r\n            }\r\n        }\r\n        return true;\r\n    }\r\n    static isOrder(param, value) {\r\n        if (type_1.getType(value) !== 'object') {\r\n            throw utils_1.E(Object.assign(Object.assign({}, code_1.ERRORS.INVALID_PARAM), { message: `${param} order must be an object` }));\r\n        }\r\n        for (let key in value) {\r\n            const subValue = value[key];\r\n            if (subValue !== 1 && subValue !== -1) {\r\n                throw utils_1.E(Object.assign(Object.assign({}, code_1.ERRORS.INVALID_PARAM), { message: `order value must be 1 or -1` }));\r\n            }\r\n        }\r\n        return true;\r\n    }\r\n    static isFieldOrder(direction) {\r\n        if (constant_1.OrderDirectionList.indexOf(direction) === -1) {\r\n            throw new Error(constant_1.ErrorCode.DirectionError);\r\n        }\r\n        return true;\r\n    }\r\n    static isFieldPath(path) {\r\n        if (!/^[a-zA-Z0-9-_\\.]/.test(path)) {\r\n            throw new Error();\r\n        }\r\n        return true;\r\n    }\r\n    static isOperator(op) {\r\n        if (constant_1.WhereFilterOpList.indexOf(op) === -1) {\r\n            throw new Error(constant_1.ErrorCode.OpStrError);\r\n        }\r\n        return true;\r\n    }\r\n    static isCollName(name) {\r\n        if (!/^[a-zA-Z0-9]([a-zA-Z0-9-_]){1,32}$/.test(name)) {\r\n            throw new Error(constant_1.ErrorCode.CollNameError);\r\n        }\r\n        return true;\r\n    }\r\n    static isDocID(docId) {\r\n        if (!/^([a-fA-F0-9]){24}$/.test(docId)) {\r\n            throw new Error(constant_1.ErrorCode.DocIDError);\r\n        }\r\n        return true;\r\n    }\r\n    static isValidOptions(options = {}) {\r\n        if (type_1.getType(options) !== 'object') {\r\n            throw utils_1.E(Object.assign(Object.assign({}, code_1.ERRORS.INVALID_PARAM), { message: `options must be an object` }));\r\n        }\r\n        const keys = Object.keys(options);\r\n        for (const index in keys) {\r\n            if (validOptionsKeys.indexOf(keys[index]) < 0) {\r\n                throw utils_1.E(Object.assign(Object.assign({}, code_1.ERRORS.INVALID_PARAM), { message: `${keys[index]} is invalid options key` }));\r\n            }\r\n        }\r\n        const { limit, offset, projection, order } = options;\r\n        const { multiple } = options;\r\n        if (limit !== undefined) {\r\n            Validate.isInteger('limit', limit);\r\n        }\r\n        if (offset !== undefined) {\r\n            Validate.isInteger('offset', offset);\r\n        }\r\n        if (projection !== undefined) {\r\n            Validate.isProjection('projection', projection);\r\n        }\r\n        if (order !== undefined) {\r\n            Validate.isOrder('order', order);\r\n        }\r\n        if (multiple !== undefined) {\r\n            Validate.mustBeBoolean('multiple', multiple);\r\n        }\r\n        if (options.timeout !== undefined) {\r\n            Validate.isInteger('timeout', options.timeout);\r\n        }\r\n        return true;\r\n    }\r\n    static isValidAggregation(stage) {\r\n        if (Object.keys(stage).length !== 1) {\r\n            throw utils_1.E(Object.assign(Object.assign({}, code_1.ERRORS.INVALID_PARAM), { message: `aggregation stage must have one key` }));\r\n        }\r\n        return true;\r\n    }\r\n    static isCentersPhere(param) {\r\n        if (Array.isArray(param) && param.length === 2) {\r\n            if (type_1.getType(param[0]) === 'object' &&\r\n                param[0]._internalType === symbol_1.SYMBOL_GEO_POINT &&\r\n                typeof param[1] === 'number') {\r\n                return true;\r\n            }\r\n            if (Array.isArray(param[0]) && param[0].length === 2) {\r\n                const longitude = param[0][0];\r\n                const latitude = param[0][1];\r\n                Validate.isGeopoint('longitude', longitude);\r\n                Validate.isGeopoint('latitude', latitude);\r\n                if (typeof param[1] === 'number') {\r\n                    return true;\r\n                }\r\n            }\r\n        }\r\n        throw new Error(`${constant_1.ErrorCode.CentersPhereError}`);\r\n    }\r\n}\r\nexports.Validate = Validate;\r\n", "\r\nObject.defineProperty(exports, \"__esModule\", { value: true });\r\nvar ErrorCode;\r\n(function (ErrorCode) {\r\n    ErrorCode[\"DocIDError\"] = \"\\u6587\\u6863ID\\u4E0D\\u5408\\u6CD5\";\r\n    ErrorCode[\"CollNameError\"] = \"\\u96C6\\u5408\\u540D\\u79F0\\u4E0D\\u5408\\u6CD5\";\r\n    ErrorCode[\"OpStrError\"] = \"\\u64CD\\u4F5C\\u7B26\\u4E0D\\u5408\\u6CD5\";\r\n    ErrorCode[\"DirectionError\"] = \"\\u6392\\u5E8F\\u5B57\\u7B26\\u4E0D\\u5408\\u6CD5\";\r\n    ErrorCode[\"IntergerError\"] = \"must be integer\";\r\n    ErrorCode[\"BooleanError\"] = \"must be boolean\";\r\n    ErrorCode[\"ArrayError\"] = \"must be array\";\r\n    ErrorCode[\"QueryParamTypeError\"] = \"\\u67E5\\u8BE2\\u53C2\\u6570\\u5FC5\\u987B\\u4E3A\\u5BF9\\u8C61\";\r\n    ErrorCode[\"QueryParamValueError\"] = \"\\u67E5\\u8BE2\\u53C2\\u6570\\u5BF9\\u8C61\\u503C\\u4E0D\\u80FD\\u5747\\u4E3Aundefined\";\r\n    ErrorCode[\"CentersPhereError\"] = \"centersPhere\\u7ED3\\u6784\\u4E0D\\u5408\\u6CD5\";\r\n})(ErrorCode || (ErrorCode = {}));\r\nexports.ErrorCode = ErrorCode;\r\nconst FieldType = {\r\n    String: 'String',\r\n    Number: 'Number',\r\n    Object: 'Object',\r\n    Array: 'Array',\r\n    Boolean: 'Boolean',\r\n    Null: 'Null',\r\n    GeoPoint: 'GeoPoint',\r\n    GeoLineString: 'GeoLineString',\r\n    GeoPolygon: 'GeoPolygon',\r\n    GeoMultiPoint: 'GeoMultiPoint',\r\n    GeoMultiLineString: 'GeoMultiLineString',\r\n    GeoMultiPolygon: 'GeoMultiPolygon',\r\n    Date: 'Date',\r\n    Command: 'Command',\r\n    ServerDate: 'ServerDate',\r\n    BsonDate: 'BsonDate'\r\n};\r\nexports.FieldType = FieldType;\r\nconst OrderDirectionList = ['desc', 'asc'];\r\nexports.OrderDirectionList = OrderDirectionList;\r\nconst WhereFilterOpList = ['<', '<=', '==', '>=', '>'];\r\nexports.WhereFilterOpList = WhereFilterOpList;\r\nvar Opeartor;\r\n(function (Opeartor) {\r\n    Opeartor[\"lt\"] = \"<\";\r\n    Opeartor[\"gt\"] = \">\";\r\n    Opeartor[\"lte\"] = \"<=\";\r\n    Opeartor[\"gte\"] = \">=\";\r\n    Opeartor[\"eq\"] = \"==\";\r\n})(Opeartor || (Opeartor = {}));\r\nexports.Opeartor = Opeartor;\r\nconst OperatorMap = {\r\n    [Opeartor.eq]: '$eq',\r\n    [Opeartor.lt]: '$lt',\r\n    [Opeartor.lte]: '$lte',\r\n    [Opeartor.gt]: '$gt',\r\n    [Opeartor.gte]: '$gte'\r\n};\r\nexports.OperatorMap = OperatorMap;\r\nconst UpdateOperatorList = [\r\n    '$set',\r\n    '$inc',\r\n    '$mul',\r\n    '$unset',\r\n    '$push',\r\n    '$pop',\r\n    '$unshift',\r\n    '$shift',\r\n    '$currentDate',\r\n    '$each',\r\n    '$position'\r\n];\r\nexports.UpdateOperatorList = UpdateOperatorList;\r\nvar QueryType;\r\n(function (QueryType) {\r\n    QueryType[\"WHERE\"] = \"WHERE\";\r\n    QueryType[\"DOC\"] = \"DOC\";\r\n})(QueryType || (QueryType = {}));\r\nexports.QueryType = QueryType;\r\n", "\r\nObject.defineProperty(exports, \"__esModule\", { value: true });\r\nconst constant_1 = require(\"./constant\");\r\nconst index_1 = require(\"./geo/index\");\r\nclass Util {\r\n}\r\nexports.Util = Util;\r\nUtil.formatResDocumentData = (documents) => {\r\n    return documents.map(document => {\r\n        return Util.formatField(document);\r\n    });\r\n};\r\nUtil.formatField = document => {\r\n    const keys = Object.keys(document);\r\n    let protoField = {};\r\n    if (Array.isArray(document)) {\r\n        protoField = [];\r\n    }\r\n    keys.forEach(key => {\r\n        const item = document[key];\r\n        const type = Util.whichType(item);\r\n        let realValue;\r\n        switch (type) {\r\n            case constant_1.FieldType.GeoPoint:\r\n                realValue = new index_1.Point(item.coordinates[0], item.coordinates[1]);\r\n                break;\r\n            case constant_1.FieldType.GeoLineString:\r\n                realValue = new index_1.LineString(item.coordinates.map(point => new index_1.Point(point[0], point[1])));\r\n                break;\r\n            case constant_1.FieldType.GeoPolygon:\r\n                realValue = new index_1.Polygon(item.coordinates.map(line => new index_1.LineString(line.map(([lng, lat]) => new index_1.Point(lng, lat)))));\r\n                break;\r\n            case constant_1.FieldType.GeoMultiPoint:\r\n                realValue = new index_1.MultiPoint(item.coordinates.map(point => new index_1.Point(point[0], point[1])));\r\n                break;\r\n            case constant_1.FieldType.GeoMultiLineString:\r\n                realValue = new index_1.MultiLineString(item.coordinates.map(line => new index_1.LineString(line.map(([lng, lat]) => new index_1.Point(lng, lat)))));\r\n                break;\r\n            case constant_1.FieldType.GeoMultiPolygon:\r\n                realValue = new index_1.MultiPolygon(item.coordinates.map(polygon => new index_1.Polygon(polygon.map(line => new index_1.LineString(line.map(([lng, lat]) => new index_1.Point(lng, lat)))))));\r\n                break;\r\n            case constant_1.FieldType.Date:\r\n                realValue = item;\r\n                break;\r\n            case constant_1.FieldType.Object:\r\n            case constant_1.FieldType.Array:\r\n                realValue = Util.formatField(item);\r\n                break;\r\n            case constant_1.FieldType.ServerDate:\r\n                realValue = new Date(item.$date);\r\n                break;\r\n            default:\r\n                realValue = item;\r\n        }\r\n        if (Array.isArray(protoField)) {\r\n            protoField.push(realValue);\r\n        }\r\n        else {\r\n            protoField[key] = realValue;\r\n        }\r\n    });\r\n    return protoField;\r\n};\r\nUtil.whichType = (obj) => {\r\n    let type = Object.prototype.toString.call(obj).slice(8, -1);\r\n    if (type === constant_1.FieldType.Date) {\r\n        return constant_1.FieldType.Date;\r\n    }\r\n    if (type === constant_1.FieldType.Object) {\r\n        if (obj.$date) {\r\n            type = constant_1.FieldType.ServerDate;\r\n        }\r\n        else if (index_1.Point.validate(obj)) {\r\n            type = constant_1.FieldType.GeoPoint;\r\n        }\r\n        else if (index_1.LineString.validate(obj)) {\r\n            type = constant_1.FieldType.GeoLineString;\r\n        }\r\n        else if (index_1.Polygon.validate(obj)) {\r\n            type = constant_1.FieldType.GeoPolygon;\r\n        }\r\n        else if (index_1.MultiPoint.validate(obj)) {\r\n            type = constant_1.FieldType.GeoMultiPoint;\r\n        }\r\n        else if (index_1.MultiLineString.validate(obj)) {\r\n            type = constant_1.FieldType.GeoMultiLineString;\r\n        }\r\n        else if (index_1.MultiPolygon.validate(obj)) {\r\n            type = constant_1.FieldType.GeoMultiPolygon;\r\n        }\r\n    }\r\n    return type;\r\n};\r\nUtil.generateDocId = () => {\r\n    let chars = 'ABCDEFabcdef0123456789';\r\n    let autoId = '';\r\n    for (let i = 0; i < 24; i++) {\r\n        autoId += chars.charAt(Math.floor(Math.random() * chars.length));\r\n    }\r\n    return autoId;\r\n};\r\n", "\r\nObject.defineProperty(exports, \"__esModule\", { value: true });\r\nexports.ERRORS = {\r\n    CREATE_WATCH_NET_ERROR: {\r\n        code: 'CREATE_WATCH_NET_ERROR',\r\n        message: 'create watch network error'\r\n    },\r\n    CREATE_WACTH_EXCEED_ERROR: {\r\n        code: 'CREATE_WACTH_EXCEED_ERROR',\r\n        message: 'maximum connections exceed'\r\n    },\r\n    CREATE_WATCH_SERVER_ERROR: {\r\n        code: 'CREATE_WATCH_SERVER_ERROR',\r\n        message: 'create watch server error'\r\n    },\r\n    CONN_ERROR: {\r\n        code: 'CONN_ERROR',\r\n        message: 'connection error'\r\n    },\r\n    INVALID_PARAM: {\r\n        code: 'INVALID_PARAM',\r\n        message: 'Invalid request param'\r\n    },\r\n    INSERT_DOC_FAIL: {\r\n        code: 'INSERT_DOC_FAIL',\r\n        message: 'insert document failed'\r\n    },\r\n    DATABASE_TRANSACTION_CONFLICT: {\r\n        code: 'DATABASE_TRANSACTION_CONFLICT',\r\n        message: 'database transaction conflict'\r\n    },\r\n    DATABASE_REQUEST_FAILED: {\r\n        code: 'DATABASE_REQUEST_FAILED',\r\n        message: 'database request failed'\r\n    }\r\n};\r\n", "\r\nObject.defineProperty(exports, \"__esModule\", { value: true });\r\nconst bson_1 = require(\"bson\");\r\nconst type_1 = require(\"./type\");\r\nexports.sleep = (ms = 0) => new Promise(r => setTimeout(r, ms));\r\nconst counters = {};\r\nexports.autoCount = (domain = 'any') => {\r\n    if (!counters[domain]) {\r\n        counters[domain] = 0;\r\n    }\r\n    return counters[domain]++;\r\n};\r\nexports.getReqOpts = (apiOptions) => {\r\n    if (apiOptions.timeout !== undefined) {\r\n        return {\r\n            timeout: apiOptions.timeout\r\n        };\r\n    }\r\n    return {};\r\n};\r\nexports.filterUndefined = o => {\r\n    if (!type_1.isObject(o)) {\r\n        return o;\r\n    }\r\n    for (let key in o) {\r\n        if (o[key] === undefined) {\r\n            delete o[key];\r\n        }\r\n        else if (type_1.isObject(o[key])) {\r\n            o[key] = exports.filterUndefined(o[key]);\r\n        }\r\n    }\r\n    return o;\r\n};\r\nexports.stringifyByEJSON = params => {\r\n    params = exports.filterUndefined(params);\r\n    return bson_1.EJSON.stringify(params, { relaxed: false });\r\n};\r\nexports.parseByEJSON = params => {\r\n    return bson_1.EJSON.parse(params);\r\n};\r\nclass TcbError extends Error {\r\n    constructor(error) {\r\n        super(error.message);\r\n        this.code = error.code;\r\n        this.message = error.message;\r\n    }\r\n}\r\nexports.TcbError = TcbError;\r\nexports.E = (errObj) => {\r\n    return new TcbError(errObj);\r\n};\r\nfunction processReturn(throwOnCode, res) {\r\n    if (throwOnCode === false) {\r\n        return res;\r\n    }\r\n    throw exports.E(Object.assign({}, res));\r\n}\r\nexports.processReturn = processReturn;\r\n", "\r\nObject.defineProperty(exports, \"__esModule\", { value: true });\r\nconst symbol_1 = require(\"./symbol\");\r\nexports.getType = (x) => Object.prototype.toString.call(x).slice(8, -1).toLowerCase();\r\nexports.isObject = (x) => exports.getType(x) === 'object';\r\nexports.isString = (x) => exports.getType(x) === 'string';\r\nexports.isNumber = (x) => exports.getType(x) === 'number';\r\nexports.isPromise = (x) => exports.getType(x) === 'promise';\r\nexports.isFunction = (x) => typeof x === 'function';\r\nexports.isArray = (x) => Array.isArray(x);\r\nexports.isDate = (x) => exports.getType(x) === 'date';\r\nexports.isRegExp = (x) => exports.getType(x) === 'regexp';\r\nexports.isInternalObject = (x) => x && (x._internalType instanceof symbol_1.InternalSymbol);\r\nexports.isPlainObject = (obj) => {\r\n    if (typeof obj !== 'object' || obj === null)\r\n        return false;\r\n    let proto = obj;\r\n    while (Object.getPrototypeOf(proto) !== null) {\r\n        proto = Object.getPrototypeOf(proto);\r\n    }\r\n    return Object.getPrototypeOf(obj) === proto;\r\n};\r\n", "\r\nObject.defineProperty(exports, \"__esModule\", { value: true });\r\nconst _symbols = [];\r\nconst __internalMark__ = {};\r\nclass HiddenSymbol {\r\n    constructor(target) {\r\n        Object.defineProperties(this, {\r\n            target: {\r\n                enumerable: false,\r\n                writable: false,\r\n                configurable: false,\r\n                value: target,\r\n            },\r\n        });\r\n    }\r\n}\r\nclass InternalSymbol extends HiddenSymbol {\r\n    constructor(target, __mark__) {\r\n        if (__mark__ !== __internalMark__) {\r\n            throw new TypeError('InternalSymbol cannot be constructed with new operator');\r\n        }\r\n        super(target);\r\n    }\r\n    static for(target) {\r\n        for (let i = 0, len = _symbols.length; i < len; i++) {\r\n            if (_symbols[i].target === target) {\r\n                return _symbols[i].instance;\r\n            }\r\n        }\r\n        const symbol = new InternalSymbol(target, __internalMark__);\r\n        _symbols.push({\r\n            target,\r\n            instance: symbol,\r\n        });\r\n        return symbol;\r\n    }\r\n}\r\nexports.InternalSymbol = InternalSymbol;\r\nexports.default = InternalSymbol;\r\n", "\r\nfunction __export(m) {\r\n    for (var p in m) if (!exports.hasOwnProperty(p)) exports[p] = m[p];\r\n}\r\nObject.defineProperty(exports, \"__esModule\", { value: true });\r\nconst symbol_1 = require(\"../utils/symbol\");\r\n__export(require(\"../utils/symbol\"));\r\nexports.SYMBOL_UNSET_FIELD_NAME = symbol_1.default.for('UNSET_FIELD_NAME');\r\nexports.SYMBOL_UPDATE_COMMAND = symbol_1.default.for('UPDATE_COMMAND');\r\nexports.SYMBOL_QUERY_COMMAND = symbol_1.default.for('QUERY_COMMAND');\r\nexports.SYMBOL_LOGIC_COMMAND = symbol_1.default.for('LOGIC_COMMAND');\r\nexports.SYMBOL_GEO_POINT = symbol_1.default.for('GEO_POINT');\r\nexports.SYMBOL_GEO_LINE_STRING = symbol_1.default.for('SYMBOL_GEO_LINE_STRING');\r\nexports.SYMBOL_GEO_POLYGON = symbol_1.default.for('SYMBOL_GEO_POLYGON');\r\nexports.SYMBOL_GEO_MULTI_POINT = symbol_1.default.for('SYMBOL_GEO_MULTI_POINT');\r\nexports.SYMBOL_GEO_MULTI_LINE_STRING = symbol_1.default.for('SYMBOL_GEO_MULTI_LINE_STRING');\r\nexports.SYMBOL_GEO_MULTI_POLYGON = symbol_1.default.for('SYMBOL_GEO_MULTI_POLYGON');\r\nexports.SYMBOL_SERVER_DATE = symbol_1.default.for('SERVER_DATE');\r\nexports.SYMBOL_REGEXP = symbol_1.default.for('REGEXP');\r\nexports.SYMBOL_OBJECTID = symbol_1.default.for('OBJECTID');\r\n", "\r\nObject.defineProperty(exports, \"__esModule\", { value: true });\r\nconst symbol_1 = require(\"../helper/symbol\");\r\nconst point_1 = require(\"./point\");\r\nconst type_1 = require(\"../utils/type\");\r\nclass LineString {\r\n    constructor(points) {\r\n        if (!type_1.isArray(points)) {\r\n            throw new TypeError(`\"points\" must be of type Point[]. Received type ${typeof points}`);\r\n        }\r\n        if (points.length < 2) {\r\n            throw new Error('\"points\" must contain 2 points at least');\r\n        }\r\n        points.forEach(point => {\r\n            if (!(point instanceof point_1.Point)) {\r\n                throw new TypeError(`\"points\" must be of type Point[]. Received type ${typeof point}[]`);\r\n            }\r\n        });\r\n        this.points = points;\r\n    }\r\n    parse(key) {\r\n        return {\r\n            [key]: {\r\n                type: 'LineString',\r\n                coordinates: this.points.map(point => point.toJSON().coordinates)\r\n            }\r\n        };\r\n    }\r\n    toJSON() {\r\n        return {\r\n            type: 'LineString',\r\n            coordinates: this.points.map(point => point.toJSON().coordinates)\r\n        };\r\n    }\r\n    static validate(lineString) {\r\n        if (lineString.type !== 'LineString' || !type_1.isArray(lineString.coordinates)) {\r\n            return false;\r\n        }\r\n        for (let point of lineString.coordinates) {\r\n            if (!type_1.isNumber(point[0]) || !type_1.isNumber(point[1])) {\r\n                return false;\r\n            }\r\n        }\r\n        return true;\r\n    }\r\n    static isClosed(lineString) {\r\n        const firstPoint = lineString.points[0];\r\n        const lastPoint = lineString.points[lineString.points.length - 1];\r\n        if (firstPoint.latitude === lastPoint.latitude && firstPoint.longitude === lastPoint.longitude) {\r\n            return true;\r\n        }\r\n    }\r\n    get _internalType() {\r\n        return symbol_1.SYMBOL_GEO_LINE_STRING;\r\n    }\r\n}\r\nexports.LineString = LineString;\r\n", "\r\nObject.defineProperty(exports, \"__esModule\", { value: true });\r\nconst symbol_1 = require(\"../helper/symbol\");\r\nconst type_1 = require(\"../utils/type\");\r\nconst lineString_1 = require(\"./lineString\");\r\nclass Polygon {\r\n    constructor(lines) {\r\n        if (!type_1.isArray(lines)) {\r\n            throw new TypeError(`\"lines\" must be of type LineString[]. Received type ${typeof lines}`);\r\n        }\r\n        if (lines.length === 0) {\r\n            throw new Error('Polygon must contain 1 linestring at least');\r\n        }\r\n        lines.forEach(line => {\r\n            if (!(line instanceof lineString_1.LineString)) {\r\n                throw new TypeError(`\"lines\" must be of type LineString[]. Received type ${typeof line}[]`);\r\n            }\r\n            if (!lineString_1.LineString.isClosed(line)) {\r\n                throw new Error(`LineString ${line.points.map(p => p.toReadableString())} is not a closed cycle`);\r\n            }\r\n        });\r\n        this.lines = lines;\r\n    }\r\n    parse(key) {\r\n        return {\r\n            [key]: {\r\n                type: 'Polygon',\r\n                coordinates: this.lines.map(line => {\r\n                    return line.points.map(point => [point.longitude, point.latitude]);\r\n                })\r\n            }\r\n        };\r\n    }\r\n    toJSON() {\r\n        return {\r\n            type: 'Polygon',\r\n            coordinates: this.lines.map(line => {\r\n                return line.points.map(point => [point.longitude, point.latitude]);\r\n            })\r\n        };\r\n    }\r\n    static validate(polygon) {\r\n        if (polygon.type !== 'Polygon' || !type_1.isArray(polygon.coordinates)) {\r\n            return false;\r\n        }\r\n        for (let line of polygon.coordinates) {\r\n            if (!this.isCloseLineString(line)) {\r\n                return false;\r\n            }\r\n            for (let point of line) {\r\n                if (!type_1.isNumber(point[0]) || !type_1.isNumber(point[1])) {\r\n                    return false;\r\n                }\r\n            }\r\n        }\r\n        return true;\r\n    }\r\n    static isCloseLineString(lineString) {\r\n        const firstPoint = lineString[0];\r\n        const lastPoint = lineString[lineString.length - 1];\r\n        if (firstPoint[0] !== lastPoint[0] || firstPoint[1] !== lastPoint[1]) {\r\n            return false;\r\n        }\r\n        return true;\r\n    }\r\n    get _internalType() {\r\n        return symbol_1.SYMBOL_GEO_POLYGON;\r\n    }\r\n}\r\nexports.Polygon = Polygon;\r\n", "\r\nObject.defineProperty(exports, \"__esModule\", { value: true });\r\nconst symbol_1 = require(\"../helper/symbol\");\r\nconst point_1 = require(\"./point\");\r\nconst type_1 = require(\"../utils/type\");\r\nclass MultiPoint {\r\n    constructor(points) {\r\n        if (!type_1.isArray(points)) {\r\n            throw new TypeError(`\"points\" must be of type Point[]. Received type ${typeof points}`);\r\n        }\r\n        if (points.length === 0) {\r\n            throw new Error('\"points\" must contain 1 point at least');\r\n        }\r\n        points.forEach(point => {\r\n            if (!(point instanceof point_1.Point)) {\r\n                throw new TypeError(`\"points\" must be of type Point[]. Received type ${typeof point}[]`);\r\n            }\r\n        });\r\n        this.points = points;\r\n    }\r\n    parse(key) {\r\n        return {\r\n            [key]: {\r\n                type: 'MultiPoint',\r\n                coordinates: this.points.map(point => point.toJSON().coordinates)\r\n            }\r\n        };\r\n    }\r\n    toJSON() {\r\n        return {\r\n            type: 'MultiPoint',\r\n            coordinates: this.points.map(point => point.toJSON().coordinates)\r\n        };\r\n    }\r\n    static validate(multiPoint) {\r\n        if (multiPoint.type !== 'MultiPoint' || !type_1.isArray(multiPoint.coordinates)) {\r\n            return false;\r\n        }\r\n        for (let point of multiPoint.coordinates) {\r\n            if (!type_1.isNumber(point[0]) || !type_1.isNumber(point[1])) {\r\n                return false;\r\n            }\r\n        }\r\n        return true;\r\n    }\r\n    get _internalType() {\r\n        return symbol_1.SYMBOL_GEO_MULTI_POINT;\r\n    }\r\n}\r\nexports.MultiPoint = MultiPoint;\r\n", "\r\nObject.defineProperty(exports, \"__esModule\", { value: true });\r\nconst symbol_1 = require(\"../helper/symbol\");\r\nconst type_1 = require(\"../utils/type\");\r\nconst lineString_1 = require(\"./lineString\");\r\nclass MultiLineString {\r\n    constructor(lines) {\r\n        if (!type_1.isArray(lines)) {\r\n            throw new TypeError(`\"lines\" must be of type LineString[]. Received type ${typeof lines}`);\r\n        }\r\n        if (lines.length === 0) {\r\n            throw new Error('Polygon must contain 1 linestring at least');\r\n        }\r\n        lines.forEach(line => {\r\n            if (!(line instanceof lineString_1.LineString)) {\r\n                throw new TypeError(`\"lines\" must be of type LineString[]. Received type ${typeof line}[]`);\r\n            }\r\n        });\r\n        this.lines = lines;\r\n    }\r\n    parse(key) {\r\n        return {\r\n            [key]: {\r\n                type: 'MultiLineString',\r\n                coordinates: this.lines.map(line => {\r\n                    return line.points.map(point => [point.longitude, point.latitude]);\r\n                })\r\n            }\r\n        };\r\n    }\r\n    toJSON() {\r\n        return {\r\n            type: 'MultiLineString',\r\n            coordinates: this.lines.map(line => {\r\n                return line.points.map(point => [point.longitude, point.latitude]);\r\n            })\r\n        };\r\n    }\r\n    static validate(multiLineString) {\r\n        if (multiLineString.type !== 'MultiLineString' || !type_1.isArray(multiLineString.coordinates)) {\r\n            return false;\r\n        }\r\n        for (let line of multiLineString.coordinates) {\r\n            for (let point of line) {\r\n                if (!type_1.isNumber(point[0]) || !type_1.isNumber(point[1])) {\r\n                    return false;\r\n                }\r\n            }\r\n        }\r\n        return true;\r\n    }\r\n    get _internalType() {\r\n        return symbol_1.SYMBOL_GEO_MULTI_LINE_STRING;\r\n    }\r\n}\r\nexports.MultiLineString = MultiLineString;\r\n", "\r\nObject.defineProperty(exports, \"__esModule\", { value: true });\r\nconst symbol_1 = require(\"../helper/symbol\");\r\nconst type_1 = require(\"../utils/type\");\r\nconst polygon_1 = require(\"./polygon\");\r\nclass MultiPolygon {\r\n    constructor(polygons) {\r\n        if (!type_1.isArray(polygons)) {\r\n            throw new TypeError(`\"polygons\" must be of type Polygon[]. Received type ${typeof polygons}`);\r\n        }\r\n        if (polygons.length === 0) {\r\n            throw new Error('MultiPolygon must contain 1 polygon at least');\r\n        }\r\n        for (let polygon of polygons) {\r\n            if (!(polygon instanceof polygon_1.Polygon)) {\r\n                throw new TypeError(`\"polygon\" must be of type Polygon[]. Received type ${typeof polygon}[]`);\r\n            }\r\n        }\r\n        this.polygons = polygons;\r\n    }\r\n    parse(key) {\r\n        return {\r\n            [key]: {\r\n                type: 'MultiPolygon',\r\n                coordinates: this.polygons.map(polygon => {\r\n                    return polygon.lines.map(line => {\r\n                        return line.points.map(point => [point.longitude, point.latitude]);\r\n                    });\r\n                })\r\n            }\r\n        };\r\n    }\r\n    toJSON() {\r\n        return {\r\n            type: 'MultiPolygon',\r\n            coordinates: this.polygons.map(polygon => {\r\n                return polygon.lines.map(line => {\r\n                    return line.points.map(point => [point.longitude, point.latitude]);\r\n                });\r\n            })\r\n        };\r\n    }\r\n    static validate(multiPolygon) {\r\n        if (multiPolygon.type !== 'MultiPolygon' || !type_1.isArray(multiPolygon.coordinates)) {\r\n            return false;\r\n        }\r\n        for (let polygon of multiPolygon.coordinates) {\r\n            for (let line of polygon) {\r\n                for (let point of line) {\r\n                    if (!type_1.isNumber(point[0]) || !type_1.isNumber(point[1])) {\r\n                        return false;\r\n                    }\r\n                }\r\n            }\r\n        }\r\n        return true;\r\n    }\r\n    get _internalType() {\r\n        return symbol_1.SYMBOL_GEO_MULTI_POLYGON;\r\n    }\r\n}\r\nexports.MultiPolygon = MultiPolygon;\r\n", "\r\nObject.defineProperty(exports, \"__esModule\", { value: true });\r\nconst document_1 = require(\"./document\");\r\nconst query_1 = require(\"./query\");\r\nconst aggregate_1 = require(\"./aggregate\");\r\nconst datatype_1 = require(\"./serializer/datatype\");\r\nconst utils_1 = require(\"./utils/utils\");\r\nconst validate_1 = require(\"./validate\");\r\nconst type_1 = require(\"./utils/type\");\r\nclass CollectionReference extends query_1.Query {\r\n    constructor(db, coll, apiOptions, transactionId) {\r\n        super(db, coll, '', apiOptions, transactionId);\r\n        if (transactionId) {\r\n            this._transactionId = transactionId;\r\n        }\r\n    }\r\n    get name() {\r\n        return this._coll;\r\n    }\r\n    doc(docID) {\r\n        if (typeof docID !== 'string' && typeof docID !== 'number') {\r\n            throw new Error('docId必须为字符串或数字');\r\n        }\r\n        return new document_1.DocumentReference(this._db, this._coll, this._apiOptions, docID, this._transactionId);\r\n    }\r\n    async add(data) {\r\n        let transformData = data;\r\n        if (!type_1.isArray(data)) {\r\n            transformData = [data];\r\n        }\r\n        transformData = transformData.map(item => {\r\n            return utils_1.stringifyByEJSON(datatype_1.serialize(item));\r\n        });\r\n        let params = {\r\n            collectionName: this._coll,\r\n            data: transformData\r\n        };\r\n        if (this._transactionId) {\r\n            params.transactionId = this._transactionId;\r\n        }\r\n        const res = await this._request.send('database.insertDocument', params, utils_1.getReqOpts(this._apiOptions));\r\n        if (res.code) {\r\n            return res;\r\n        }\r\n        if (!type_1.isArray(data)) {\r\n            if (this._transactionId) {\r\n                return {\r\n                    inserted: 1,\r\n                    ok: 1,\r\n                    id: res.data.insertedIds[0],\r\n                    requestId: res.requestId\r\n                };\r\n            }\r\n            return {\r\n                id: res.data.insertedIds[0],\r\n                requestId: res.requestId\r\n            };\r\n        }\r\n        return {\r\n            ids: res.data.insertedIds,\r\n            requestId: res.requestId\r\n        };\r\n    }\r\n    aggregate(rawPipeline = []) {\r\n        return new aggregate_1.default(this._db, this._coll, (this._apiOptions.raw || false) ? rawPipeline : []);\r\n    }\r\n    options(apiOptions) {\r\n        validate_1.Validate.isValidOptions(apiOptions);\r\n        return new CollectionReference(this._db, this._coll, apiOptions, this._transactionId);\r\n    }\r\n}\r\nexports.CollectionReference = CollectionReference;\r\n", "\r\nObject.defineProperty(exports, \"__esModule\", { value: true });\r\nconst index_1 = require(\"./index\");\r\nconst util_1 = require(\"./util\");\r\nconst update_1 = require(\"./serializer/update\");\r\nconst datatype_1 = require(\"./serializer/datatype\");\r\nconst update_2 = require(\"./commands/update\");\r\nconst websocket_client_1 = require(\"./realtime/websocket-client\");\r\nconst constant_1 = require(\"./constant\");\r\nconst utils_1 = require(\"./utils/utils\");\r\nconst code_1 = require(\"./const/code\");\r\nconst bson_1 = require(\"bson\");\r\nclass DocumentReference {\r\n    constructor(db, coll, apiOptions, docID, transactionId) {\r\n        this.watch = (options) => {\r\n            if (!index_1.Db.ws) {\r\n                index_1.Db.ws = new websocket_client_1.RealtimeWebSocketClient({\r\n                    context: {\r\n                        appConfig: {\r\n                            docSizeLimit: 1000,\r\n                            realtimePingInterval: 10000,\r\n                            realtimePongWaitTimeout: 5000,\r\n                            request: this.request\r\n                        }\r\n                    }\r\n                });\r\n            }\r\n            return index_1.Db.ws.watch(Object.assign(Object.assign({}, options), { envId: this._db.config.env, collectionName: this._coll, query: JSON.stringify({\r\n                    _id: this.id\r\n                }) }));\r\n        };\r\n        this._db = db;\r\n        this._coll = coll;\r\n        this.id = docID;\r\n        this._transactionId = transactionId;\r\n        this.request = new index_1.Db.reqClass(this._db.config);\r\n        this._apiOptions = apiOptions;\r\n    }\r\n    async create(data) {\r\n        if (this.id) {\r\n            data['_id'] = this.id;\r\n        }\r\n        let params = {\r\n            collectionName: this._coll,\r\n            data: [utils_1.stringifyByEJSON(datatype_1.serialize(data))],\r\n            transactionId: this._transactionId\r\n        };\r\n        const res = await this.request.send('database.insertDocument', params, utils_1.getReqOpts(this._apiOptions));\r\n        if (res.code) {\r\n            return res;\r\n        }\r\n        if (this._transactionId) {\r\n            return {\r\n                inserted: 1,\r\n                ok: 1,\r\n                id: res.data.insertedIds[0],\r\n                requestId: res.requestId\r\n            };\r\n        }\r\n        return {\r\n            id: res.data.insertedIds[0],\r\n            requestId: res.requestId\r\n        };\r\n    }\r\n    async set(data) {\r\n        if (!this.id) {\r\n            return utils_1.processReturn(this._db.config.throwOnCode, Object.assign(Object.assign({}, code_1.ERRORS.INVALID_PARAM), { message: 'docId不能为空' }));\r\n        }\r\n        if (!data || typeof data !== 'object') {\r\n            return utils_1.processReturn(this._db.config.throwOnCode, Object.assign(Object.assign({}, code_1.ERRORS.INVALID_PARAM), { message: '参数必需是非空对象' }));\r\n        }\r\n        if (data.hasOwnProperty('_id')) {\r\n            return utils_1.processReturn(this._db.config.throwOnCode, Object.assign(Object.assign({}, code_1.ERRORS.INVALID_PARAM), { message: '不能更新_id的值' }));\r\n        }\r\n        let hasOperator = false;\r\n        const checkMixed = objs => {\r\n            if (typeof objs === 'object') {\r\n                for (let key in objs) {\r\n                    if (objs[key] instanceof update_2.UpdateCommand) {\r\n                        hasOperator = true;\r\n                    }\r\n                    else if (typeof objs[key] === 'object') {\r\n                        checkMixed(objs[key]);\r\n                    }\r\n                }\r\n            }\r\n        };\r\n        checkMixed(data);\r\n        if (hasOperator) {\r\n            return utils_1.processReturn(this._db.config.throwOnCode, Object.assign(Object.assign({}, code_1.ERRORS.DATABASE_REQUEST_FAILED), { message: 'update operator complicit' }));\r\n        }\r\n        let param = {\r\n            collectionName: this._coll,\r\n            queryType: constant_1.QueryType.DOC,\r\n            data: utils_1.stringifyByEJSON(datatype_1.serialize(data)),\r\n            transactionId: this._transactionId,\r\n            multi: false,\r\n            merge: false,\r\n            upsert: true\r\n        };\r\n        if (this.id) {\r\n            param['query'] = utils_1.stringifyByEJSON({ _id: this.id });\r\n        }\r\n        const res = await this.request.send('database.modifyDocument', param, utils_1.getReqOpts(this._apiOptions));\r\n        if (res.code) {\r\n            return res;\r\n        }\r\n        if (this._transactionId) {\r\n            return {\r\n                updated: res.data.updated,\r\n                upserted: [{ _id: res.data.upsert_id }],\r\n                requestId: res.requestId\r\n            };\r\n        }\r\n        return {\r\n            updated: res.data.updated,\r\n            upsertedId: res.data.upsert_id,\r\n            requestId: res.requestId\r\n        };\r\n    }\r\n    async update(data) {\r\n        if (!data || typeof data !== 'object') {\r\n            return utils_1.processReturn(this._db.config.throwOnCode, Object.assign(Object.assign({}, code_1.ERRORS.INVALID_PARAM), { message: '参数必需是非空对象' }));\r\n        }\r\n        if (data.hasOwnProperty('_id')) {\r\n            return utils_1.processReturn(this._db.config.throwOnCode, Object.assign(Object.assign({}, code_1.ERRORS.INVALID_PARAM), { message: '不能更新_id的值' }));\r\n        }\r\n        const query = utils_1.stringifyByEJSON({ _id: this.id });\r\n        const param = {\r\n            collectionName: this._coll,\r\n            transactionId: this._transactionId,\r\n            data: update_1.UpdateSerializer.encodeEJSON(data, this._apiOptions.raw || false),\r\n            query,\r\n            queryType: constant_1.QueryType.DOC,\r\n            multi: false,\r\n            merge: true,\r\n            upsert: false\r\n        };\r\n        const res = await this.request.send('database.modifyDocument', param, utils_1.getReqOpts(this._apiOptions));\r\n        if (res.code) {\r\n            return res;\r\n        }\r\n        return {\r\n            updated: res.data.updated,\r\n            requestId: res.requestId\r\n        };\r\n    }\r\n    async delete() {\r\n        return this.remove();\r\n    }\r\n    async remove() {\r\n        const query = utils_1.stringifyByEJSON({ _id: this.id });\r\n        const param = {\r\n            collectionName: this._coll,\r\n            transactionId: this._transactionId,\r\n            query: query,\r\n            queryType: constant_1.QueryType.DOC,\r\n            multi: false\r\n        };\r\n        const res = await this.request.send('database.removeDocument', param, utils_1.getReqOpts(this._apiOptions));\r\n        if (res.code) {\r\n            return res;\r\n        }\r\n        return {\r\n            deleted: res.data.deleted,\r\n            requestId: res.requestId\r\n        };\r\n    }\r\n    async get() {\r\n        const query = utils_1.stringifyByEJSON({ _id: this.id });\r\n        const { projection } = this._apiOptions;\r\n        const param = {\r\n            collectionName: this._coll,\r\n            query,\r\n            transactionId: this._transactionId,\r\n            queryType: constant_1.QueryType.DOC,\r\n            multi: false\r\n        };\r\n        if (projection) {\r\n            param.projection = utils_1.stringifyByEJSON(projection);\r\n        }\r\n        const res = await this.request.send('database.getDocument', param, utils_1.getReqOpts(this._apiOptions));\r\n        if (res.code) {\r\n            return res;\r\n        }\r\n        const list = res.data.list.map(item => bson_1.EJSON.parse(item));\r\n        const documents = util_1.Util.formatResDocumentData(list);\r\n        if (this._transactionId) {\r\n            return {\r\n                data: documents[0] || null,\r\n                requestId: res.requestId\r\n            };\r\n        }\r\n        return {\r\n            data: documents,\r\n            requestId: res.requestId,\r\n            offset: res.data.offset,\r\n            limit: res.data.limit\r\n        };\r\n    }\r\n    field(projection) {\r\n        let transformProjection = {};\r\n        for (let k in projection) {\r\n            if (typeof projection[k] === 'boolean') {\r\n                transformProjection[k] = projection[k] === true ? 1 : 0;\r\n            }\r\n            if (typeof projection[k] === 'number') {\r\n                transformProjection[k] = projection[k] > 0 ? 1 : 0;\r\n            }\r\n            if (typeof projection[k] === 'object') {\r\n                transformProjection[k] = projection[k];\r\n            }\r\n        }\r\n        let newApiOption = Object.assign({}, this._apiOptions);\r\n        newApiOption.projection = transformProjection;\r\n        return new DocumentReference(this._db, this._coll, newApiOption, this.id, this._transactionId);\r\n    }\r\n}\r\nexports.DocumentReference = DocumentReference;\r\n", "\r\nObject.defineProperty(exports, \"__esModule\", { value: true });\r\nconst update_1 = require(\"../commands/update\");\r\nconst symbol_1 = require(\"../helper/symbol\");\r\nconst type_1 = require(\"../utils/type\");\r\nconst operator_map_1 = require(\"../operator-map\");\r\nconst common_1 = require(\"./common\");\r\nconst utils_1 = require(\"../utils/utils\");\r\nclass UpdateSerializer {\r\n    constructor() { }\r\n    static encode(query) {\r\n        const stringifier = new UpdateSerializer();\r\n        return stringifier.encodeUpdate(query);\r\n    }\r\n    static encodeEJSON(query, raw) {\r\n        const stringifier = new UpdateSerializer();\r\n        return utils_1.stringifyByEJSON(raw ? query : stringifier.encodeUpdate(query));\r\n    }\r\n    encodeUpdate(query) {\r\n        if (update_1.isUpdateCommand(query)) {\r\n            return this.encodeUpdateCommand(query);\r\n        }\r\n        else if (type_1.getType(query) === 'object') {\r\n            return this.encodeUpdateObject(query);\r\n        }\r\n        else {\r\n            return query;\r\n        }\r\n    }\r\n    encodeUpdateCommand(query) {\r\n        if (query.fieldName === symbol_1.SYMBOL_UNSET_FIELD_NAME) {\r\n            throw new Error('Cannot encode a comparison command with unset field name');\r\n        }\r\n        switch (query.operator) {\r\n            case update_1.UPDATE_COMMANDS_LITERAL.PUSH:\r\n            case update_1.UPDATE_COMMANDS_LITERAL.PULL:\r\n            case update_1.UPDATE_COMMANDS_LITERAL.PULL_ALL:\r\n            case update_1.UPDATE_COMMANDS_LITERAL.POP:\r\n            case update_1.UPDATE_COMMANDS_LITERAL.SHIFT:\r\n            case update_1.UPDATE_COMMANDS_LITERAL.UNSHIFT:\r\n            case update_1.UPDATE_COMMANDS_LITERAL.ADD_TO_SET: {\r\n                return this.encodeArrayUpdateCommand(query);\r\n            }\r\n            default: {\r\n                return this.encodeFieldUpdateCommand(query);\r\n            }\r\n        }\r\n    }\r\n    encodeFieldUpdateCommand(query) {\r\n        const $op = operator_map_1.operatorToString(query.operator);\r\n        switch (query.operator) {\r\n            case update_1.UPDATE_COMMANDS_LITERAL.REMOVE: {\r\n                return {\r\n                    [$op]: {\r\n                        [query.fieldName]: ''\r\n                    }\r\n                };\r\n            }\r\n            default: {\r\n                return {\r\n                    [$op]: {\r\n                        [query.fieldName]: query.operands[0]\r\n                    }\r\n                };\r\n            }\r\n        }\r\n    }\r\n    encodeArrayUpdateCommand(query) {\r\n        const $op = operator_map_1.operatorToString(query.operator);\r\n        switch (query.operator) {\r\n            case update_1.UPDATE_COMMANDS_LITERAL.PUSH: {\r\n                let modifiers;\r\n                if (type_1.isArray(query.operands)) {\r\n                    modifiers = {\r\n                        $each: query.operands.map(common_1.encodeInternalDataType)\r\n                    };\r\n                }\r\n                else {\r\n                    modifiers = query.operands;\r\n                }\r\n                return {\r\n                    [$op]: {\r\n                        [query.fieldName]: modifiers\r\n                    }\r\n                };\r\n            }\r\n            case update_1.UPDATE_COMMANDS_LITERAL.UNSHIFT: {\r\n                const modifiers = {\r\n                    $each: query.operands.map(common_1.encodeInternalDataType),\r\n                    $position: 0\r\n                };\r\n                return {\r\n                    [$op]: {\r\n                        [query.fieldName]: modifiers\r\n                    }\r\n                };\r\n            }\r\n            case update_1.UPDATE_COMMANDS_LITERAL.POP: {\r\n                return {\r\n                    [$op]: {\r\n                        [query.fieldName]: 1\r\n                    }\r\n                };\r\n            }\r\n            case update_1.UPDATE_COMMANDS_LITERAL.SHIFT: {\r\n                return {\r\n                    [$op]: {\r\n                        [query.fieldName]: -1\r\n                    }\r\n                };\r\n            }\r\n            default: {\r\n                return {\r\n                    [$op]: {\r\n                        [query.fieldName]: common_1.encodeInternalDataType(query.operands)\r\n                    }\r\n                };\r\n            }\r\n        }\r\n    }\r\n    encodeUpdateObject(query) {\r\n        const flattened = common_1.flattenQueryObject(query);\r\n        for (const key in flattened) {\r\n            if (/^\\$/.test(key))\r\n                continue;\r\n            let val = flattened[key];\r\n            if (update_1.isUpdateCommand(val)) {\r\n                flattened[key] = val._setFieldName(key);\r\n                const condition = this.encodeUpdateCommand(flattened[key]);\r\n                common_1.mergeConditionAfterEncode(flattened, condition, key);\r\n            }\r\n            else {\r\n                flattened[key] = val = common_1.encodeInternalDataType(val);\r\n                const $setCommand = new update_1.UpdateCommand(update_1.UPDATE_COMMANDS_LITERAL.SET, [val], key);\r\n                const condition = this.encodeUpdateCommand($setCommand);\r\n                common_1.mergeConditionAfterEncode(flattened, condition, key);\r\n            }\r\n        }\r\n        return flattened;\r\n    }\r\n}\r\nexports.UpdateSerializer = UpdateSerializer;\r\n", "\r\nObject.defineProperty(exports, \"__esModule\", { value: true });\r\nconst symbol_1 = require(\"../helper/symbol\");\r\nvar UPDATE_COMMANDS_LITERAL;\r\n(function (UPDATE_COMMANDS_LITERAL) {\r\n    UPDATE_COMMANDS_LITERAL[\"SET\"] = \"set\";\r\n    UPDATE_COMMANDS_LITERAL[\"REMOVE\"] = \"remove\";\r\n    UPDATE_COMMANDS_LITERAL[\"INC\"] = \"inc\";\r\n    UPDATE_COMMANDS_LITERAL[\"MUL\"] = \"mul\";\r\n    UPDATE_COMMANDS_LITERAL[\"PUSH\"] = \"push\";\r\n    UPDATE_COMMANDS_LITERAL[\"PULL\"] = \"pull\";\r\n    UPDATE_COMMANDS_LITERAL[\"PULL_ALL\"] = \"pullAll\";\r\n    UPDATE_COMMANDS_LITERAL[\"POP\"] = \"pop\";\r\n    UPDATE_COMMANDS_LITERAL[\"SHIFT\"] = \"shift\";\r\n    UPDATE_COMMANDS_LITERAL[\"UNSHIFT\"] = \"unshift\";\r\n    UPDATE_COMMANDS_LITERAL[\"ADD_TO_SET\"] = \"addToSet\";\r\n    UPDATE_COMMANDS_LITERAL[\"BIT\"] = \"bit\";\r\n    UPDATE_COMMANDS_LITERAL[\"RENAME\"] = \"rename\";\r\n    UPDATE_COMMANDS_LITERAL[\"MAX\"] = \"max\";\r\n    UPDATE_COMMANDS_LITERAL[\"MIN\"] = \"min\";\r\n})(UPDATE_COMMANDS_LITERAL = exports.UPDATE_COMMANDS_LITERAL || (exports.UPDATE_COMMANDS_LITERAL = {}));\r\nclass UpdateCommand {\r\n    constructor(operator, operands, fieldName) {\r\n        this._internalType = symbol_1.SYMBOL_UPDATE_COMMAND;\r\n        Object.defineProperties(this, {\r\n            _internalType: {\r\n                enumerable: false,\r\n                configurable: false,\r\n            },\r\n        });\r\n        this.operator = operator;\r\n        this.operands = operands;\r\n        this.fieldName = fieldName || symbol_1.SYMBOL_UNSET_FIELD_NAME;\r\n    }\r\n    _setFieldName(fieldName) {\r\n        const command = new UpdateCommand(this.operator, this.operands, fieldName);\r\n        return command;\r\n    }\r\n}\r\nexports.UpdateCommand = UpdateCommand;\r\nfunction isUpdateCommand(object) {\r\n    return object && (object instanceof UpdateCommand) && (object._internalType === symbol_1.SYMBOL_UPDATE_COMMAND);\r\n}\r\nexports.isUpdateCommand = isUpdateCommand;\r\nfunction isKnownUpdateCommand(object) {\r\n    return isUpdateCommand(object) && (object.operator.toUpperCase() in UPDATE_COMMANDS_LITERAL);\r\n}\r\nexports.isKnownUpdateCommand = isKnownUpdateCommand;\r\nexports.default = UpdateCommand;\r\n", "\r\nObject.defineProperty(exports, \"__esModule\", { value: true });\r\nconst query_1 = require(\"./commands/query\");\r\nconst logic_1 = require(\"./commands/logic\");\r\nconst update_1 = require(\"./commands/update\");\r\nexports.OperatorMap = {};\r\nfor (const key in query_1.QUERY_COMMANDS_LITERAL) {\r\n    exports.OperatorMap[key] = '$' + key;\r\n}\r\nfor (const key in logic_1.LOGIC_COMMANDS_LITERAL) {\r\n    exports.OperatorMap[key] = '$' + key;\r\n}\r\nfor (const key in update_1.UPDATE_COMMANDS_LITERAL) {\r\n    exports.OperatorMap[key] = '$' + key;\r\n}\r\nexports.OperatorMap[query_1.QUERY_COMMANDS_LITERAL.NEQ] = '$ne';\r\nexports.OperatorMap[update_1.UPDATE_COMMANDS_LITERAL.REMOVE] = '$unset';\r\nexports.OperatorMap[update_1.UPDATE_COMMANDS_LITERAL.SHIFT] = '$pop';\r\nexports.OperatorMap[update_1.UPDATE_COMMANDS_LITERAL.UNSHIFT] = '$push';\r\nfunction operatorToString(operator) {\r\n    return exports.OperatorMap[operator] || '$' + operator;\r\n}\r\nexports.operatorToString = operatorToString;\r\n", "\r\nObject.defineProperty(exports, \"__esModule\", { value: true });\r\nconst logic_1 = require(\"./logic\");\r\nconst symbol_1 = require(\"../helper/symbol\");\r\nconst index_1 = require(\"../geo/index\");\r\nconst type_1 = require(\"../utils/type\");\r\nconst validate_1 = require(\"../validate\");\r\nexports.EQ = 'eq';\r\nexports.NEQ = 'neq';\r\nexports.GT = 'gt';\r\nexports.GTE = 'gte';\r\nexports.LT = 'lt';\r\nexports.LTE = 'lte';\r\nexports.IN = 'in';\r\nexports.NIN = 'nin';\r\nexports.ALL = 'all';\r\nexports.ELEM_MATCH = 'elemMatch';\r\nexports.EXISTS = 'exists';\r\nexports.SIZE = 'size';\r\nexports.MOD = 'mod';\r\nvar QUERY_COMMANDS_LITERAL;\r\n(function (QUERY_COMMANDS_LITERAL) {\r\n    QUERY_COMMANDS_LITERAL[\"EQ\"] = \"eq\";\r\n    QUERY_COMMANDS_LITERAL[\"NEQ\"] = \"neq\";\r\n    QUERY_COMMANDS_LITERAL[\"GT\"] = \"gt\";\r\n    QUERY_COMMANDS_LITERAL[\"GTE\"] = \"gte\";\r\n    QUERY_COMMANDS_LITERAL[\"LT\"] = \"lt\";\r\n    QUERY_COMMANDS_LITERAL[\"LTE\"] = \"lte\";\r\n    QUERY_COMMANDS_LITERAL[\"IN\"] = \"in\";\r\n    QUERY_COMMANDS_LITERAL[\"NIN\"] = \"nin\";\r\n    QUERY_COMMANDS_LITERAL[\"ALL\"] = \"all\";\r\n    QUERY_COMMANDS_LITERAL[\"ELEM_MATCH\"] = \"elemMatch\";\r\n    QUERY_COMMANDS_LITERAL[\"EXISTS\"] = \"exists\";\r\n    QUERY_COMMANDS_LITERAL[\"SIZE\"] = \"size\";\r\n    QUERY_COMMANDS_LITERAL[\"MOD\"] = \"mod\";\r\n    QUERY_COMMANDS_LITERAL[\"GEO_NEAR\"] = \"geoNear\";\r\n    QUERY_COMMANDS_LITERAL[\"GEO_WITHIN\"] = \"geoWithin\";\r\n    QUERY_COMMANDS_LITERAL[\"GEO_INTERSECTS\"] = \"geoIntersects\";\r\n})(QUERY_COMMANDS_LITERAL = exports.QUERY_COMMANDS_LITERAL || (exports.QUERY_COMMANDS_LITERAL = {}));\r\nclass QueryCommand extends logic_1.LogicCommand {\r\n    constructor(operator, operands, fieldName) {\r\n        super(operator, operands, fieldName);\r\n        this.operator = operator;\r\n        this._internalType = symbol_1.SYMBOL_QUERY_COMMAND;\r\n    }\r\n    toJSON() {\r\n        switch (this.operator) {\r\n            case QUERY_COMMANDS_LITERAL.IN:\r\n            case QUERY_COMMANDS_LITERAL.NIN:\r\n                return {\r\n                    ['$' + this.operator]: this.operands\r\n                };\r\n            case QUERY_COMMANDS_LITERAL.NEQ:\r\n                return {\r\n                    ['$ne']: this.operands[0]\r\n                };\r\n            default:\r\n                return {\r\n                    ['$' + this.operator]: this.operands[0]\r\n                };\r\n        }\r\n    }\r\n    _setFieldName(fieldName) {\r\n        const command = new QueryCommand(this.operator, this.operands, fieldName);\r\n        return command;\r\n    }\r\n    eq(val) {\r\n        const command = new QueryCommand(QUERY_COMMANDS_LITERAL.EQ, [val], this.fieldName);\r\n        return this.and(command);\r\n    }\r\n    neq(val) {\r\n        const command = new QueryCommand(QUERY_COMMANDS_LITERAL.NEQ, [val], this.fieldName);\r\n        return this.and(command);\r\n    }\r\n    gt(val) {\r\n        const command = new QueryCommand(QUERY_COMMANDS_LITERAL.GT, [val], this.fieldName);\r\n        return this.and(command);\r\n    }\r\n    gte(val) {\r\n        const command = new QueryCommand(QUERY_COMMANDS_LITERAL.GTE, [val], this.fieldName);\r\n        return this.and(command);\r\n    }\r\n    lt(val) {\r\n        const command = new QueryCommand(QUERY_COMMANDS_LITERAL.LT, [val], this.fieldName);\r\n        return this.and(command);\r\n    }\r\n    lte(val) {\r\n        const command = new QueryCommand(QUERY_COMMANDS_LITERAL.LTE, [val], this.fieldName);\r\n        return this.and(command);\r\n    }\r\n    in(list) {\r\n        const command = new QueryCommand(QUERY_COMMANDS_LITERAL.IN, list, this.fieldName);\r\n        return this.and(command);\r\n    }\r\n    nin(list) {\r\n        const command = new QueryCommand(QUERY_COMMANDS_LITERAL.NIN, list, this.fieldName);\r\n        return this.and(command);\r\n    }\r\n    geoNear(val) {\r\n        if (!(val.geometry instanceof index_1.Point)) {\r\n            throw new TypeError(`\"geometry\" must be of type Point. Received type ${typeof val.geometry}`);\r\n        }\r\n        if (val.maxDistance !== undefined && !type_1.isNumber(val.maxDistance)) {\r\n            throw new TypeError(`\"maxDistance\" must be of type Number. Received type ${typeof val.maxDistance}`);\r\n        }\r\n        if (val.minDistance !== undefined && !type_1.isNumber(val.minDistance)) {\r\n            throw new TypeError(`\"minDistance\" must be of type Number. Received type ${typeof val.minDistance}`);\r\n        }\r\n        const command = new QueryCommand(QUERY_COMMANDS_LITERAL.GEO_NEAR, [val], this.fieldName);\r\n        return this.and(command);\r\n    }\r\n    geoWithin(val) {\r\n        if (!(val.geometry instanceof index_1.MultiPolygon) &&\r\n            !(val.geometry instanceof index_1.Polygon) &&\r\n            !validate_1.Validate.isCentersPhere(val.centerSphere)) {\r\n            throw new TypeError(`\"geometry\" must be of type Polygon or MultiPolygon. Received type ${typeof val.geometry}`);\r\n        }\r\n        const command = new QueryCommand(QUERY_COMMANDS_LITERAL.GEO_WITHIN, [val], this.fieldName);\r\n        return this.and(command);\r\n    }\r\n    geoIntersects(val) {\r\n        if (!(val.geometry instanceof index_1.Point) &&\r\n            !(val.geometry instanceof index_1.LineString) &&\r\n            !(val.geometry instanceof index_1.Polygon) &&\r\n            !(val.geometry instanceof index_1.MultiPoint) &&\r\n            !(val.geometry instanceof index_1.MultiLineString) &&\r\n            !(val.geometry instanceof index_1.MultiPolygon)) {\r\n            throw new TypeError(`\"geometry\" must be of type Point, LineString, Polygon, MultiPoint, MultiLineString or MultiPolygon. Received type ${typeof val.geometry}`);\r\n        }\r\n        const command = new QueryCommand(QUERY_COMMANDS_LITERAL.GEO_INTERSECTS, [val], this.fieldName);\r\n        return this.and(command);\r\n    }\r\n}\r\nexports.QueryCommand = QueryCommand;\r\nfunction isQueryCommand(object) {\r\n    return object && object instanceof QueryCommand && object._internalType === symbol_1.SYMBOL_QUERY_COMMAND;\r\n}\r\nexports.isQueryCommand = isQueryCommand;\r\nfunction isKnownQueryCommand(object) {\r\n    return isQueryCommand(object) && object.operator.toUpperCase() in QUERY_COMMANDS_LITERAL;\r\n}\r\nexports.isKnownQueryCommand = isKnownQueryCommand;\r\nfunction isComparisonCommand(object) {\r\n    return isQueryCommand(object);\r\n}\r\nexports.isComparisonCommand = isComparisonCommand;\r\nexports.default = QueryCommand;\r\n", "\r\nObject.defineProperty(exports, \"__esModule\", { value: true });\r\nconst symbol_1 = require(\"../helper/symbol\");\r\nconst query_1 = require(\"./query\");\r\nexports.AND = 'and';\r\nexports.OR = 'or';\r\nexports.NOT = 'not';\r\nexports.NOR = 'nor';\r\nvar LOGIC_COMMANDS_LITERAL;\r\n(function (LOGIC_COMMANDS_LITERAL) {\r\n    LOGIC_COMMANDS_LITERAL[\"AND\"] = \"and\";\r\n    LOGIC_COMMANDS_LITERAL[\"OR\"] = \"or\";\r\n    LOGIC_COMMANDS_LITERAL[\"NOT\"] = \"not\";\r\n    LOGIC_COMMANDS_LITERAL[\"NOR\"] = \"nor\";\r\n})(LOGIC_COMMANDS_LITERAL = exports.LOGIC_COMMANDS_LITERAL || (exports.LOGIC_COMMANDS_LITERAL = {}));\r\nclass LogicCommand {\r\n    constructor(operator, operands, fieldName) {\r\n        this._internalType = symbol_1.SYMBOL_LOGIC_COMMAND;\r\n        Object.defineProperties(this, {\r\n            _internalType: {\r\n                enumerable: false,\r\n                configurable: false,\r\n            },\r\n        });\r\n        this.operator = operator;\r\n        this.operands = operands;\r\n        this.fieldName = fieldName || symbol_1.SYMBOL_UNSET_FIELD_NAME;\r\n        if (this.fieldName !== symbol_1.SYMBOL_UNSET_FIELD_NAME) {\r\n            if (Array.isArray(operands)) {\r\n                operands = operands.slice();\r\n                this.operands = operands;\r\n                for (let i = 0, len = operands.length; i < len; i++) {\r\n                    const query = operands[i];\r\n                    if (isLogicCommand(query) || query_1.isQueryCommand(query)) {\r\n                        operands[i] = query._setFieldName(this.fieldName);\r\n                    }\r\n                }\r\n            }\r\n            else {\r\n                const query = operands;\r\n                if (isLogicCommand(query) || query_1.isQueryCommand(query)) {\r\n                    operands = query._setFieldName(this.fieldName);\r\n                }\r\n            }\r\n        }\r\n    }\r\n    _setFieldName(fieldName) {\r\n        const operands = this.operands.map(operand => {\r\n            if (operand instanceof LogicCommand) {\r\n                return operand._setFieldName(fieldName);\r\n            }\r\n            else {\r\n                return operand;\r\n            }\r\n        });\r\n        const command = new LogicCommand(this.operator, operands, fieldName);\r\n        return command;\r\n    }\r\n    and(...__expressions__) {\r\n        const expressions = Array.isArray(arguments[0]) ? arguments[0] : Array.from(arguments);\r\n        expressions.unshift(this);\r\n        return new LogicCommand(LOGIC_COMMANDS_LITERAL.AND, expressions, this.fieldName);\r\n    }\r\n    or(...__expressions__) {\r\n        const expressions = Array.isArray(arguments[0]) ? arguments[0] : Array.from(arguments);\r\n        expressions.unshift(this);\r\n        return new LogicCommand(LOGIC_COMMANDS_LITERAL.OR, expressions, this.fieldName);\r\n    }\r\n}\r\nexports.LogicCommand = LogicCommand;\r\nfunction isLogicCommand(object) {\r\n    return object && (object instanceof LogicCommand) && (object._internalType === symbol_1.SYMBOL_LOGIC_COMMAND);\r\n}\r\nexports.isLogicCommand = isLogicCommand;\r\nfunction isKnownLogicCommand(object) {\r\n    return isLogicCommand && (object.operator.toUpperCase() in LOGIC_COMMANDS_LITERAL);\r\n}\r\nexports.isKnownLogicCommand = isKnownLogicCommand;\r\nexports.default = LogicCommand;\r\n", "\r\nObject.defineProperty(exports, \"__esModule\", { value: true });\r\nconst type_1 = require(\"../utils/type\");\r\nconst datatype_1 = require(\"./datatype\");\r\nfunction flatten(query, shouldPreserverObject, parents, visited) {\r\n    const cloned = Object.assign({}, query);\r\n    for (const key in query) {\r\n        if (/^\\$/.test(key))\r\n            continue;\r\n        const value = query[key];\r\n        if (value === undefined) {\r\n            delete cloned[key];\r\n            continue;\r\n        }\r\n        if (!value)\r\n            continue;\r\n        if (type_1.isObject(value) && !shouldPreserverObject(value)) {\r\n            if (visited.indexOf(value) > -1) {\r\n                throw new Error('Cannot convert circular structure to JSON');\r\n            }\r\n            const newParents = [...parents, key];\r\n            const newVisited = [...visited, value];\r\n            const flattenedChild = flatten(value, shouldPreserverObject, newParents, newVisited);\r\n            cloned[key] = flattenedChild;\r\n            let hasKeyNotCombined = false;\r\n            for (const childKey in flattenedChild) {\r\n                if (!/^\\$/.test(childKey)) {\r\n                    cloned[`${key}.${childKey}`] = flattenedChild[childKey];\r\n                    delete cloned[key][childKey];\r\n                }\r\n                else {\r\n                    hasKeyNotCombined = true;\r\n                }\r\n            }\r\n            if (!hasKeyNotCombined) {\r\n                delete cloned[key];\r\n            }\r\n        }\r\n    }\r\n    return cloned;\r\n}\r\nfunction flattenQueryObject(query) {\r\n    return flatten(query, isConversionRequired, [], [query]);\r\n}\r\nexports.flattenQueryObject = flattenQueryObject;\r\nfunction flattenObject(object) {\r\n    return flatten(object, (_) => false, [], [object]);\r\n}\r\nexports.flattenObject = flattenObject;\r\nfunction mergeConditionAfterEncode(query, condition, key) {\r\n    if (!condition[key]) {\r\n        delete query[key];\r\n    }\r\n    for (const conditionKey in condition) {\r\n        if (query[conditionKey]) {\r\n            if (type_1.isArray(query[conditionKey])) {\r\n                query[conditionKey].push(condition[conditionKey]);\r\n            }\r\n            else if (type_1.isObject(query[conditionKey])) {\r\n                if (type_1.isObject(condition[conditionKey])) {\r\n                    Object.assign(query[conditionKey], condition[conditionKey]);\r\n                }\r\n                else {\r\n                    console.warn(`unmergable condition, query is object but condition is ${type_1.getType(condition)}, can only overwrite`, condition, key);\r\n                    query[conditionKey] = condition[conditionKey];\r\n                }\r\n            }\r\n            else {\r\n                console.warn(`to-merge query is of type ${type_1.getType(query)}, can only overwrite`, query, condition, key);\r\n                query[conditionKey] = condition[conditionKey];\r\n            }\r\n        }\r\n        else {\r\n            query[conditionKey] = condition[conditionKey];\r\n        }\r\n    }\r\n}\r\nexports.mergeConditionAfterEncode = mergeConditionAfterEncode;\r\nfunction isConversionRequired(val) {\r\n    return type_1.isInternalObject(val) || type_1.isDate(val) || type_1.isRegExp(val);\r\n}\r\nexports.isConversionRequired = isConversionRequired;\r\nfunction encodeInternalDataType(val) {\r\n    return datatype_1.serialize(val);\r\n}\r\nexports.encodeInternalDataType = encodeInternalDataType;\r\nfunction decodeInternalDataType(object) {\r\n    return datatype_1.deserialize(object);\r\n}\r\nexports.decodeInternalDataType = decodeInternalDataType;\r\n", "\r\nObject.defineProperty(exports, \"__esModule\", { value: true });\r\nconst symbol_1 = require(\"../helper/symbol\");\r\nconst type_1 = require(\"../utils/type\");\r\nconst index_1 = require(\"../geo/index\");\r\nconst index_2 = require(\"../serverDate/index\");\r\nfunction serialize(val) {\r\n    return serializeHelper(val, [val]);\r\n}\r\nexports.serialize = serialize;\r\nfunction serializeHelper(val, visited) {\r\n    if (type_1.isInternalObject(val)) {\r\n        switch (val._internalType) {\r\n            case symbol_1.SYMBOL_GEO_POINT: {\r\n                return val.toJSON();\r\n            }\r\n            case symbol_1.SYMBOL_SERVER_DATE: {\r\n                return val.parse();\r\n            }\r\n            case symbol_1.SYMBOL_REGEXP: {\r\n                return val.parse();\r\n            }\r\n            case symbol_1.SYMBOL_OBJECTID: {\r\n                return val.parse();\r\n            }\r\n            default: {\r\n                return val.toJSON ? val.toJSON() : val;\r\n            }\r\n        }\r\n    }\r\n    else if (type_1.isDate(val)) {\r\n        return val;\r\n    }\r\n    else if (type_1.isRegExp(val)) {\r\n        return {\r\n            $regularExpression: {\r\n                pattern: val.source,\r\n                options: val.flags\r\n            }\r\n        };\r\n    }\r\n    else if (type_1.isArray(val)) {\r\n        return val.map(item => {\r\n            if (visited.indexOf(item) > -1) {\r\n                throw new Error('Cannot convert circular structure to JSON');\r\n            }\r\n            return serializeHelper(item, [...visited, item]);\r\n        });\r\n    }\r\n    else if (type_1.isObject(val)) {\r\n        const rawRet = Object.assign({}, val);\r\n        const finalRet = {};\r\n        for (const key in rawRet) {\r\n            if (visited.indexOf(rawRet[key]) > -1) {\r\n                throw new Error('Cannot convert circular structure to JSON');\r\n            }\r\n            if (rawRet[key] !== undefined) {\r\n                finalRet[key] = serializeHelper(rawRet[key], [...visited, rawRet[key]]);\r\n            }\r\n        }\r\n        return finalRet;\r\n    }\r\n    else {\r\n        return val;\r\n    }\r\n}\r\nfunction deserialize(object) {\r\n    const ret = Object.assign({}, object);\r\n    for (const key in ret) {\r\n        switch (key) {\r\n            case '$date': {\r\n                switch (type_1.getType(ret[key])) {\r\n                    case 'number': {\r\n                        return new Date(ret[key]);\r\n                    }\r\n                    case 'object': {\r\n                        return new index_2.ServerDate(ret[key]);\r\n                    }\r\n                }\r\n                break;\r\n            }\r\n            case 'type': {\r\n                switch (ret.type) {\r\n                    case 'Point': {\r\n                        if (type_1.isArray(ret.coordinates) &&\r\n                            type_1.isNumber(ret.coordinates[0]) &&\r\n                            type_1.isNumber(ret.coordinates[1])) {\r\n                            return new index_1.Point(ret.coordinates[0], ret.coordinates[1]);\r\n                        }\r\n                        break;\r\n                    }\r\n                }\r\n                break;\r\n            }\r\n        }\r\n    }\r\n    return object;\r\n}\r\nexports.deserialize = deserialize;\r\n", "\r\nObject.defineProperty(exports, \"__esModule\", { value: true });\r\nconst symbol_1 = require(\"../helper/symbol\");\r\nclass ServerDate {\r\n    constructor({ offset = 0 } = {}) {\r\n        this.offset = offset;\r\n    }\r\n    get _internalType() {\r\n        return symbol_1.SYMBOL_SERVER_DATE;\r\n    }\r\n    parse() {\r\n        return {\r\n            $tcb_server_date: {\r\n                offset: this.offset\r\n            }\r\n        };\r\n    }\r\n}\r\nexports.ServerDate = ServerDate;\r\nfunction ServerDateConstructor(opt) {\r\n    return new ServerDate(opt);\r\n}\r\nexports.ServerDateConstructor = ServerDateConstructor;\r\n", "\r\nObject.defineProperty(exports, \"__esModule\", { value: true });\r\nconst virtual_websocket_client_1 = require(\"./virtual-websocket-client\");\r\nconst utils_1 = require(\"../utils/utils\");\r\nconst message_1 = require(\"./message\");\r\nconst ws_event_1 = require(\"./ws-event\");\r\nconst error_1 = require(\"../utils/error\");\r\nconst error_2 = require(\"./error\");\r\nconst error_config_1 = require(\"../config/error.config\");\r\nconst __1 = require(\"../\");\r\nconst WS_READY_STATE = {\r\n    CONNECTING: 0,\r\n    OPEN: 1,\r\n    CLOSING: 2,\r\n    CLOSED: 3\r\n};\r\nconst MAX_RTT_OBSERVED = 3;\r\nconst DEFAULT_EXPECTED_EVENT_WAIT_TIME = 5000;\r\nconst DEFAULT_UNTRUSTED_RTT_THRESHOLD = 10000;\r\nconst DEFAULT_MAX_RECONNECT = 5;\r\nconst DEFAULT_WS_RECONNECT_INTERVAL = 10000;\r\nconst DEFAULT_PING_FAIL_TOLERANCE = 2;\r\nconst DEFAULT_PONG_MISS_TOLERANCE = 2;\r\nconst DEFAULT_LOGIN_TIMEOUT = 5000;\r\nclass RealtimeWebSocketClient {\r\n    constructor(options) {\r\n        this._virtualWSClient = new Set();\r\n        this._queryIdClientMap = new Map();\r\n        this._watchIdClientMap = new Map();\r\n        this._pingFailed = 0;\r\n        this._pongMissed = 0;\r\n        this._logins = new Map();\r\n        this._wsReadySubsribers = [];\r\n        this._wsResponseWait = new Map();\r\n        this._rttObserved = [];\r\n        this.initWebSocketConnection = async (reconnect, availableRetries = this._maxReconnect) => {\r\n            if (reconnect && this._reconnectState) {\r\n                return;\r\n            }\r\n            if (reconnect) {\r\n                this._reconnectState = true;\r\n            }\r\n            if (this._wsInitPromise) {\r\n                return this._wsInitPromise;\r\n            }\r\n            if (reconnect) {\r\n                this.pauseClients();\r\n            }\r\n            this.close(ws_event_1.CLOSE_EVENT_CODE.ReconnectWebSocket);\r\n            this._wsInitPromise = new Promise(async (resolve, reject) => {\r\n                try {\r\n                    const wsSign = await this.getWsSign();\r\n                    await new Promise(success => {\r\n                        const url = wsSign.wsUrl || 'wss://tcb-ws.tencentcloudapi.com';\r\n                        this._ws = __1.Db.wsClass ? new __1.Db.wsClass(url) : new WebSocket(url);\r\n                        success();\r\n                    });\r\n                    if (this._ws.connect) {\r\n                        await this._ws.connect();\r\n                    }\r\n                    await this.initWebSocketEvent();\r\n                    resolve();\r\n                    if (reconnect) {\r\n                        this.resumeClients();\r\n                        this._reconnectState = false;\r\n                    }\r\n                }\r\n                catch (e) {\r\n                    console.error('[realtime] initWebSocketConnection connect fail', e);\r\n                    if (availableRetries > 0) {\r\n                        const isConnected = true;\r\n                        this._wsInitPromise = undefined;\r\n                        if (isConnected) {\r\n                            await utils_1.sleep(this._reconnectInterval);\r\n                            if (reconnect) {\r\n                                this._reconnectState = false;\r\n                            }\r\n                        }\r\n                        resolve(this.initWebSocketConnection(reconnect, availableRetries - 1));\r\n                    }\r\n                    else {\r\n                        reject(e);\r\n                        if (reconnect) {\r\n                            this.closeAllClients(new error_1.CloudSDKError({\r\n                                errCode: error_config_1.ERR_CODE.SDK_DATABASE_REALTIME_LISTENER_RECONNECT_WATCH_FAIL,\r\n                                errMsg: e\r\n                            }));\r\n                        }\r\n                    }\r\n                }\r\n            });\r\n            try {\r\n                await this._wsInitPromise;\r\n                this._wsReadySubsribers.forEach(({ resolve }) => resolve());\r\n            }\r\n            catch (e) {\r\n                this._wsReadySubsribers.forEach(({ reject }) => reject());\r\n            }\r\n            finally {\r\n                this._wsInitPromise = undefined;\r\n                this._wsReadySubsribers = [];\r\n            }\r\n        };\r\n        this.initWebSocketEvent = () => new Promise((resolve, reject) => {\r\n            if (!this._ws) {\r\n                throw new Error('can not initWebSocketEvent, ws not exists');\r\n            }\r\n            let wsOpened = false;\r\n            this._ws.onopen = event => {\r\n                console.warn('[realtime] ws event: open', event);\r\n                wsOpened = true;\r\n                resolve();\r\n            };\r\n            this._ws.onerror = event => {\r\n                this._logins = new Map();\r\n                if (!wsOpened) {\r\n                    console.error('[realtime] ws open failed with ws event: error', event);\r\n                    reject(event);\r\n                }\r\n                else {\r\n                    console.error('[realtime] ws event: error', event);\r\n                    this.clearHeartbeat();\r\n                    this._virtualWSClient.forEach(client => client.closeWithError(new error_1.CloudSDKError({\r\n                        errCode: error_config_1.ERR_CODE.SDK_DATABASE_REALTIME_LISTENER_WEBSOCKET_CONNECTION_ERROR,\r\n                        errMsg: event\r\n                    })));\r\n                }\r\n            };\r\n            this._ws.onclose = closeEvent => {\r\n                console.warn('[realtime] ws event: close', closeEvent);\r\n                this._logins = new Map();\r\n                this.clearHeartbeat();\r\n                switch (closeEvent.code) {\r\n                    case ws_event_1.CLOSE_EVENT_CODE.ReconnectWebSocket: {\r\n                        break;\r\n                    }\r\n                    case ws_event_1.CLOSE_EVENT_CODE.NoRealtimeListeners: {\r\n                        break;\r\n                    }\r\n                    case ws_event_1.CLOSE_EVENT_CODE.HeartbeatPingError:\r\n                    case ws_event_1.CLOSE_EVENT_CODE.HeartbeatPongTimeoutError:\r\n                    case ws_event_1.CLOSE_EVENT_CODE.NormalClosure:\r\n                    case ws_event_1.CLOSE_EVENT_CODE.AbnormalClosure: {\r\n                        if (this._maxReconnect > 0) {\r\n                            this.initWebSocketConnection(true, this._maxReconnect);\r\n                        }\r\n                        else {\r\n                            this.closeAllClients(ws_event_1.getWSCloseError(closeEvent.code));\r\n                        }\r\n                        break;\r\n                    }\r\n                    case ws_event_1.CLOSE_EVENT_CODE.NoAuthentication: {\r\n                        this.closeAllClients(ws_event_1.getWSCloseError(closeEvent.code, closeEvent.reason));\r\n                        break;\r\n                    }\r\n                    default: {\r\n                        if (this._maxReconnect > 0) {\r\n                            this.initWebSocketConnection(true, this._maxReconnect);\r\n                        }\r\n                        else {\r\n                            this.closeAllClients(ws_event_1.getWSCloseError(closeEvent.code));\r\n                        }\r\n                    }\r\n                }\r\n            };\r\n            this._ws.onmessage = res => {\r\n                const rawMsg = res.data;\r\n                this.heartbeat();\r\n                let msg;\r\n                try {\r\n                    msg = JSON.parse(rawMsg);\r\n                }\r\n                catch (e) {\r\n                    throw new Error(`[realtime] onMessage parse res.data error: ${e}`);\r\n                }\r\n                if (msg.msgType === 'ERROR') {\r\n                    let virtualWatch = null;\r\n                    this._virtualWSClient.forEach(item => {\r\n                        if (item.watchId === msg.watchId) {\r\n                            virtualWatch = item;\r\n                        }\r\n                    });\r\n                    if (virtualWatch) {\r\n                        virtualWatch.listener.onError(msg);\r\n                    }\r\n                }\r\n                const responseWaitSpec = this._wsResponseWait.get(msg.requestId);\r\n                if (responseWaitSpec) {\r\n                    try {\r\n                        if (msg.msgType === 'ERROR') {\r\n                            responseWaitSpec.reject(new error_2.RealtimeErrorMessageError(msg));\r\n                        }\r\n                        else {\r\n                            responseWaitSpec.resolve(msg);\r\n                        }\r\n                    }\r\n                    catch (e) {\r\n                        console.error('ws onMessage responseWaitSpec.resolve(msg) errored:', e);\r\n                    }\r\n                    finally {\r\n                        this._wsResponseWait.delete(msg.requestId);\r\n                    }\r\n                    if (responseWaitSpec.skipOnMessage) {\r\n                        return;\r\n                    }\r\n                }\r\n                if (msg.msgType === 'PONG') {\r\n                    if (this._lastPingSendTS) {\r\n                        const rtt = Date.now() - this._lastPingSendTS;\r\n                        if (rtt > DEFAULT_UNTRUSTED_RTT_THRESHOLD) {\r\n                            console.warn(`[realtime] untrusted rtt observed: ${rtt}`);\r\n                            return;\r\n                        }\r\n                        if (this._rttObserved.length >= MAX_RTT_OBSERVED) {\r\n                            this._rttObserved.splice(0, this._rttObserved.length - MAX_RTT_OBSERVED + 1);\r\n                        }\r\n                        this._rttObserved.push(rtt);\r\n                    }\r\n                    return;\r\n                }\r\n                let client = msg.watchId && this._watchIdClientMap.get(msg.watchId);\r\n                if (client) {\r\n                    client.onMessage(msg);\r\n                }\r\n                else {\r\n                    console.error(`[realtime] no realtime listener found responsible for watchId ${msg.watchId}: `, msg);\r\n                    switch (msg.msgType) {\r\n                        case 'INIT_EVENT':\r\n                        case 'NEXT_EVENT':\r\n                        case 'CHECK_EVENT': {\r\n                            client = this._queryIdClientMap.get(msg.msgData.queryID);\r\n                            if (client) {\r\n                                client.onMessage(msg);\r\n                            }\r\n                            break;\r\n                        }\r\n                        default: {\r\n                            for (const [, client] of this._watchIdClientMap) {\r\n                                client.onMessage(msg);\r\n                                break;\r\n                            }\r\n                        }\r\n                    }\r\n                }\r\n            };\r\n            this.heartbeat();\r\n        });\r\n        this.isWSConnected = () => {\r\n            return Boolean(this._ws && this._ws.readyState === WS_READY_STATE.OPEN);\r\n        };\r\n        this.onceWSConnected = async () => {\r\n            if (this.isWSConnected()) {\r\n                return;\r\n            }\r\n            if (this._wsInitPromise) {\r\n                return this._wsInitPromise;\r\n            }\r\n            return new Promise((resolve, reject) => {\r\n                this._wsReadySubsribers.push({\r\n                    resolve,\r\n                    reject\r\n                });\r\n            });\r\n        };\r\n        this.webLogin = async (envId, refresh) => {\r\n            if (!refresh) {\r\n                if (envId) {\r\n                    const loginInfo = this._logins.get(envId);\r\n                    if (loginInfo) {\r\n                        if (loginInfo.loggedIn && loginInfo.loginResult) {\r\n                            return loginInfo.loginResult;\r\n                        }\r\n                        else if (loginInfo.loggingInPromise) {\r\n                            return loginInfo.loggingInPromise;\r\n                        }\r\n                    }\r\n                }\r\n                else {\r\n                    const emptyEnvLoginInfo = this._logins.get('');\r\n                    if (emptyEnvLoginInfo && emptyEnvLoginInfo.loggingInPromise) {\r\n                        return emptyEnvLoginInfo.loggingInPromise;\r\n                    }\r\n                }\r\n            }\r\n            const promise = new Promise(async (resolve, reject) => {\r\n                try {\r\n                    const wsSign = await this.getWsSign();\r\n                    const msgData = {\r\n                        envId: wsSign.envId || '',\r\n                        accessToken: '',\r\n                        referrer: 'web',\r\n                        sdkVersion: '',\r\n                        dataVersion: __1.Db.dataVersion || ''\r\n                    };\r\n                    const loginMsg = {\r\n                        watchId: undefined,\r\n                        requestId: message_1.genRequestId(),\r\n                        msgType: 'LOGIN',\r\n                        msgData,\r\n                        exMsgData: {\r\n                            runtime: __1.Db.runtime,\r\n                            signStr: wsSign.signStr,\r\n                            secretVersion: wsSign.secretVersion\r\n                        }\r\n                    };\r\n                    const loginResMsg = await this.send({\r\n                        msg: loginMsg,\r\n                        waitResponse: true,\r\n                        skipOnMessage: true,\r\n                        timeout: DEFAULT_LOGIN_TIMEOUT\r\n                    });\r\n                    if (!loginResMsg.msgData.code) {\r\n                        resolve({\r\n                            envId: wsSign.envId\r\n                        });\r\n                    }\r\n                    else {\r\n                        reject(new Error(`${loginResMsg.msgData.code} ${loginResMsg.msgData.message}`));\r\n                    }\r\n                }\r\n                catch (e) {\r\n                    reject(e);\r\n                }\r\n            });\r\n            let loginInfo = envId && this._logins.get(envId);\r\n            const loginStartTS = Date.now();\r\n            if (loginInfo) {\r\n                loginInfo.loggedIn = false;\r\n                loginInfo.loggingInPromise = promise;\r\n                loginInfo.loginStartTS = loginStartTS;\r\n            }\r\n            else {\r\n                loginInfo = {\r\n                    loggedIn: false,\r\n                    loggingInPromise: promise,\r\n                    loginStartTS\r\n                };\r\n                this._logins.set(envId || '', loginInfo);\r\n            }\r\n            try {\r\n                const loginResult = await promise;\r\n                const curLoginInfo = envId && this._logins.get(envId);\r\n                if (curLoginInfo &&\r\n                    curLoginInfo === loginInfo &&\r\n                    curLoginInfo.loginStartTS === loginStartTS) {\r\n                    loginInfo.loggedIn = true;\r\n                    loginInfo.loggingInPromise = undefined;\r\n                    loginInfo.loginStartTS = undefined;\r\n                    loginInfo.loginResult = loginResult;\r\n                    return loginResult;\r\n                }\r\n                else if (curLoginInfo) {\r\n                    if (curLoginInfo.loggedIn && curLoginInfo.loginResult) {\r\n                        return curLoginInfo.loginResult;\r\n                    }\r\n                    else if (curLoginInfo.loggingInPromise) {\r\n                        return curLoginInfo.loggingInPromise;\r\n                    }\r\n                    else {\r\n                        throw new Error('ws unexpected login info');\r\n                    }\r\n                }\r\n                else {\r\n                    throw new Error('ws login info reset');\r\n                }\r\n            }\r\n            catch (e) {\r\n                loginInfo.loggedIn = false;\r\n                loginInfo.loggingInPromise = undefined;\r\n                loginInfo.loginStartTS = undefined;\r\n                loginInfo.loginResult = undefined;\r\n                throw e;\r\n            }\r\n        };\r\n        this.getWsSign = async () => {\r\n            if (this._wsSign && this._wsSign.expiredTs > Date.now()) {\r\n                return this._wsSign;\r\n            }\r\n            const expiredTs = Date.now() + 60000;\r\n            const res = await this._context.appConfig.request.send('auth.wsWebSign', { runtime: __1.Db.runtime });\r\n            if (res.code) {\r\n                throw new Error(`[tcb-js-sdk] 获取实时数据推送登录票据失败: ${res.code}`);\r\n            }\r\n            if (res.data) {\r\n                const { signStr, wsUrl, secretVersion, envId } = res.data;\r\n                return {\r\n                    signStr,\r\n                    wsUrl,\r\n                    secretVersion,\r\n                    envId,\r\n                    expiredTs\r\n                };\r\n            }\r\n            else {\r\n                throw new Error('[tcb-js-sdk] 获取实时数据推送登录票据失败');\r\n            }\r\n        };\r\n        this.getWaitExpectedTimeoutLength = () => {\r\n            if (!this._rttObserved.length) {\r\n                return DEFAULT_EXPECTED_EVENT_WAIT_TIME;\r\n            }\r\n            return ((this._rttObserved.reduce((acc, cur) => acc + cur) /\r\n                this._rttObserved.length) *\r\n                1.5);\r\n        };\r\n        this.ping = async () => {\r\n            const msg = {\r\n                watchId: undefined,\r\n                requestId: message_1.genRequestId(),\r\n                msgType: 'PING',\r\n                msgData: null\r\n            };\r\n            await this.send({\r\n                msg\r\n            });\r\n        };\r\n        this.send = async (opts) => new Promise(async (_resolve, _reject) => {\r\n            let timeoutId;\r\n            let _hasResolved = false;\r\n            let _hasRejected = false;\r\n            const resolve = (value) => {\r\n                _hasResolved = true;\r\n                timeoutId && clearTimeout(timeoutId);\r\n                _resolve(value);\r\n            };\r\n            const reject = (error) => {\r\n                _hasRejected = true;\r\n                timeoutId && clearTimeout(timeoutId);\r\n                _reject(error);\r\n            };\r\n            if (opts.timeout) {\r\n                timeoutId = setTimeout(async () => {\r\n                    if (!_hasResolved || !_hasRejected) {\r\n                        await utils_1.sleep(0);\r\n                        if (!_hasResolved || !_hasRejected) {\r\n                            reject(new error_1.TimeoutError('wsclient.send timedout'));\r\n                        }\r\n                    }\r\n                }, opts.timeout);\r\n            }\r\n            try {\r\n                if (this._wsInitPromise) {\r\n                    await this._wsInitPromise;\r\n                }\r\n                if (!this._ws) {\r\n                    reject(new Error('invalid state: ws connection not exists, can not send message'));\r\n                    return;\r\n                }\r\n                if (this._ws.readyState !== WS_READY_STATE.OPEN) {\r\n                    reject(new Error(`ws readyState invalid: ${this._ws.readyState}, can not send message`));\r\n                    return;\r\n                }\r\n                if (opts.waitResponse) {\r\n                    this._wsResponseWait.set(opts.msg.requestId, {\r\n                        resolve,\r\n                        reject,\r\n                        skipOnMessage: opts.skipOnMessage\r\n                    });\r\n                }\r\n                try {\r\n                    await this._ws.send(JSON.stringify(opts.msg));\r\n                    if (!opts.waitResponse) {\r\n                        resolve();\r\n                    }\r\n                }\r\n                catch (err) {\r\n                    if (err) {\r\n                        reject(err);\r\n                        if (opts.waitResponse) {\r\n                            this._wsResponseWait.delete(opts.msg.requestId);\r\n                        }\r\n                    }\r\n                }\r\n            }\r\n            catch (e) {\r\n                reject(e);\r\n            }\r\n        });\r\n        this.closeAllClients = (error) => {\r\n            this._virtualWSClient.forEach(client => {\r\n                client.closeWithError(error);\r\n            });\r\n        };\r\n        this.pauseClients = (clients) => {\r\n            ;\r\n            (clients || this._virtualWSClient).forEach(client => {\r\n                client.pause();\r\n            });\r\n        };\r\n        this.resumeClients = (clients) => {\r\n            ;\r\n            (clients || this._virtualWSClient).forEach(client => {\r\n                client.resume();\r\n            });\r\n        };\r\n        this.onWatchStart = (client, queryID) => {\r\n            this._queryIdClientMap.set(queryID, client);\r\n        };\r\n        this.onWatchClose = (client, queryID) => {\r\n            if (queryID) {\r\n                this._queryIdClientMap.delete(queryID);\r\n            }\r\n            this._watchIdClientMap.delete(client.watchId);\r\n            this._virtualWSClient.delete(client);\r\n            if (!this._virtualWSClient.size) {\r\n                this.close(ws_event_1.CLOSE_EVENT_CODE.NoRealtimeListeners);\r\n            }\r\n        };\r\n        this._maxReconnect = options.maxReconnect || DEFAULT_MAX_RECONNECT;\r\n        this._reconnectInterval =\r\n            options.reconnectInterval || DEFAULT_WS_RECONNECT_INTERVAL;\r\n        this._context = options.context;\r\n    }\r\n    heartbeat(immediate) {\r\n        this.clearHeartbeat();\r\n        this._pingTimeoutId = setTimeout(async () => {\r\n            try {\r\n                if (!this._ws || this._ws.readyState !== WS_READY_STATE.OPEN) {\r\n                    return;\r\n                }\r\n                this._lastPingSendTS = Date.now();\r\n                await this.ping();\r\n                this._pingFailed = 0;\r\n                this._pongTimeoutId = setTimeout(() => {\r\n                    console.error('pong timed out');\r\n                    if (this._pongMissed < DEFAULT_PONG_MISS_TOLERANCE) {\r\n                        this._pongMissed++;\r\n                        this.heartbeat(true);\r\n                    }\r\n                    else {\r\n                        this.initWebSocketConnection(true);\r\n                    }\r\n                }, this._context.appConfig.realtimePongWaitTimeout);\r\n            }\r\n            catch (e) {\r\n                if (this._pingFailed < DEFAULT_PING_FAIL_TOLERANCE) {\r\n                    this._pingFailed++;\r\n                    this.heartbeat();\r\n                }\r\n                else {\r\n                    this.close(ws_event_1.CLOSE_EVENT_CODE.HeartbeatPingError);\r\n                }\r\n            }\r\n        }, immediate ? 0 : this._context.appConfig.realtimePingInterval);\r\n    }\r\n    clearHeartbeat() {\r\n        this._pingTimeoutId && clearTimeout(this._pingTimeoutId);\r\n        this._pongTimeoutId && clearTimeout(this._pongTimeoutId);\r\n    }\r\n    close(code) {\r\n        this.clearHeartbeat();\r\n        if (this._ws) {\r\n            this._ws.close(code, ws_event_1.CLOSE_EVENT_CODE_INFO[code].name);\r\n            this._ws = undefined;\r\n        }\r\n    }\r\n    watch(options) {\r\n        if (!this._ws && !this._wsInitPromise) {\r\n            this.initWebSocketConnection(false);\r\n        }\r\n        const virtualClient = new virtual_websocket_client_1.VirtualWebSocketClient(Object.assign(Object.assign({}, options), { send: this.send, login: this.webLogin, isWSConnected: this.isWSConnected, onceWSConnected: this.onceWSConnected, getWaitExpectedTimeoutLength: this.getWaitExpectedTimeoutLength, onWatchStart: this.onWatchStart, onWatchClose: this.onWatchClose, debug: true }));\r\n        this._virtualWSClient.add(virtualClient);\r\n        this._watchIdClientMap.set(virtualClient.watchId, virtualClient);\r\n        return virtualClient.listener;\r\n    }\r\n}\r\nexports.RealtimeWebSocketClient = RealtimeWebSocketClient;\r\n", "\r\nObject.defineProperty(exports, \"__esModule\", { value: true });\r\nconst lodash_set_1 = require(\"lodash.set\");\r\nconst lodash_unset_1 = require(\"lodash.unset\");\r\nconst lodash_clonedeep_1 = require(\"lodash.clonedeep\");\r\nconst message_1 = require(\"./message\");\r\nconst error_1 = require(\"../utils/error\");\r\nconst error_config_1 = require(\"../config/error.config\");\r\nconst utils_1 = require(\"../utils/utils\");\r\nconst listener_1 = require(\"./listener\");\r\nconst snapshot_1 = require(\"./snapshot\");\r\nconst error_2 = require(\"./error\");\r\nvar WATCH_STATUS;\r\n(function (WATCH_STATUS) {\r\n    WATCH_STATUS[\"LOGGINGIN\"] = \"LOGGINGIN\";\r\n    WATCH_STATUS[\"INITING\"] = \"INITING\";\r\n    WATCH_STATUS[\"REBUILDING\"] = \"REBUILDING\";\r\n    WATCH_STATUS[\"ACTIVE\"] = \"ACTIVE\";\r\n    WATCH_STATUS[\"ERRORED\"] = \"ERRORED\";\r\n    WATCH_STATUS[\"CLOSING\"] = \"CLOSING\";\r\n    WATCH_STATUS[\"CLOSED\"] = \"CLOSED\";\r\n    WATCH_STATUS[\"PAUSED\"] = \"PAUSED\";\r\n    WATCH_STATUS[\"RESUMING\"] = \"RESUMING\";\r\n})(WATCH_STATUS || (WATCH_STATUS = {}));\r\nconst DEFAULT_WAIT_TIME_ON_UNKNOWN_ERROR = 100;\r\nconst DEFAULT_MAX_AUTO_RETRY_ON_ERROR = 2;\r\nconst DEFAULT_MAX_SEND_ACK_AUTO_RETRY_ON_ERROR = 2;\r\nconst DEFAULT_SEND_ACK_DEBOUNCE_TIMEOUT = 10 * 1000;\r\nconst DEFAULT_INIT_WATCH_TIMEOUT = 10 * 1000;\r\nconst DEFAULT_REBUILD_WATCH_TIMEOUT = 10 * 1000;\r\nclass VirtualWebSocketClient {\r\n    constructor(options) {\r\n        this.watchStatus = WATCH_STATUS.INITING;\r\n        this._login = async (envId, refresh) => {\r\n            this.watchStatus = WATCH_STATUS.LOGGINGIN;\r\n            const loginResult = await this.login(envId, refresh);\r\n            if (!this.envId) {\r\n                this.envId = loginResult.envId;\r\n            }\r\n            return loginResult;\r\n        };\r\n        this.initWatch = async (forceRefreshLogin) => {\r\n            if (this._initWatchPromise) {\r\n                return this._initWatchPromise;\r\n            }\r\n            this._initWatchPromise = new Promise(async (resolve, reject) => {\r\n                try {\r\n                    if (this.watchStatus === WATCH_STATUS.PAUSED) {\r\n                        console.log('[realtime] initWatch cancelled on pause');\r\n                        return resolve();\r\n                    }\r\n                    const { envId } = await this._login(this.envId, forceRefreshLogin);\r\n                    if (this.watchStatus === WATCH_STATUS.PAUSED) {\r\n                        console.log('[realtime] initWatch cancelled on pause');\r\n                        return resolve();\r\n                    }\r\n                    this.watchStatus = WATCH_STATUS.INITING;\r\n                    const initWatchMsg = {\r\n                        watchId: this.watchId,\r\n                        requestId: message_1.genRequestId(),\r\n                        msgType: 'INIT_WATCH',\r\n                        msgData: {\r\n                            envId,\r\n                            collName: this.collectionName,\r\n                            query: this.query,\r\n                            limit: this.limit,\r\n                            orderBy: this.orderBy\r\n                        }\r\n                    };\r\n                    const initEventMsg = await this.send({\r\n                        msg: initWatchMsg,\r\n                        waitResponse: true,\r\n                        skipOnMessage: true,\r\n                        timeout: DEFAULT_INIT_WATCH_TIMEOUT\r\n                    });\r\n                    const { events, currEvent } = initEventMsg.msgData;\r\n                    this.sessionInfo = {\r\n                        queryID: initEventMsg.msgData.queryID,\r\n                        currentEventId: currEvent - 1,\r\n                        currentDocs: []\r\n                    };\r\n                    if (events.length > 0) {\r\n                        for (const e of events) {\r\n                            e.ID = currEvent;\r\n                        }\r\n                        this.handleServerEvents(initEventMsg);\r\n                    }\r\n                    else {\r\n                        this.sessionInfo.currentEventId = currEvent;\r\n                        const snapshot = new snapshot_1.Snapshot({\r\n                            id: currEvent,\r\n                            docChanges: [],\r\n                            docs: [],\r\n                            type: 'init'\r\n                        });\r\n                        this.listener.onChange(snapshot);\r\n                        this.scheduleSendACK();\r\n                    }\r\n                    this.onWatchStart(this, this.sessionInfo.queryID);\r\n                    this.watchStatus = WATCH_STATUS.ACTIVE;\r\n                    this._availableRetries.INIT_WATCH = DEFAULT_MAX_AUTO_RETRY_ON_ERROR;\r\n                    resolve();\r\n                }\r\n                catch (e) {\r\n                    this.handleWatchEstablishmentError(e, {\r\n                        operationName: 'INIT_WATCH',\r\n                        resolve,\r\n                        reject\r\n                    });\r\n                }\r\n            });\r\n            let success = false;\r\n            try {\r\n                await this._initWatchPromise;\r\n                success = true;\r\n            }\r\n            finally {\r\n                this._initWatchPromise = undefined;\r\n            }\r\n            console.log(`[realtime] initWatch ${success ? 'success' : 'fail'}`);\r\n        };\r\n        this.rebuildWatch = async (forceRefreshLogin) => {\r\n            if (this._rebuildWatchPromise) {\r\n                return this._rebuildWatchPromise;\r\n            }\r\n            this._rebuildWatchPromise = new Promise(async (resolve, reject) => {\r\n                try {\r\n                    if (this.watchStatus === WATCH_STATUS.PAUSED) {\r\n                        console.log('[realtime] rebuildWatch cancelled on pause');\r\n                        return resolve();\r\n                    }\r\n                    const { envId } = await this._login(this.envId, forceRefreshLogin);\r\n                    if (!this.sessionInfo) {\r\n                        throw new Error('can not rebuildWatch without a successful initWatch (lack of sessionInfo)');\r\n                    }\r\n                    if (this.watchStatus === WATCH_STATUS.PAUSED) {\r\n                        console.log('[realtime] rebuildWatch cancelled on pause');\r\n                        return resolve();\r\n                    }\r\n                    this.watchStatus = WATCH_STATUS.REBUILDING;\r\n                    const rebuildWatchMsg = {\r\n                        watchId: this.watchId,\r\n                        requestId: message_1.genRequestId(),\r\n                        msgType: 'REBUILD_WATCH',\r\n                        msgData: {\r\n                            envId,\r\n                            collName: this.collectionName,\r\n                            queryID: this.sessionInfo.queryID,\r\n                            eventID: this.sessionInfo.currentEventId\r\n                        }\r\n                    };\r\n                    const nextEventMsg = await this.send({\r\n                        msg: rebuildWatchMsg,\r\n                        waitResponse: true,\r\n                        skipOnMessage: false,\r\n                        timeout: DEFAULT_REBUILD_WATCH_TIMEOUT\r\n                    });\r\n                    this.handleServerEvents(nextEventMsg);\r\n                    this.watchStatus = WATCH_STATUS.ACTIVE;\r\n                    this._availableRetries.REBUILD_WATCH = DEFAULT_MAX_AUTO_RETRY_ON_ERROR;\r\n                    resolve();\r\n                }\r\n                catch (e) {\r\n                    this.handleWatchEstablishmentError(e, {\r\n                        operationName: 'REBUILD_WATCH',\r\n                        resolve,\r\n                        reject\r\n                    });\r\n                }\r\n            });\r\n            let success = false;\r\n            try {\r\n                await this._rebuildWatchPromise;\r\n                success = true;\r\n            }\r\n            finally {\r\n                this._rebuildWatchPromise = undefined;\r\n            }\r\n            console.log(`[realtime] rebuildWatch ${success ? 'success' : 'fail'}`);\r\n        };\r\n        this.handleWatchEstablishmentError = async (e, options) => {\r\n            const isInitWatch = options.operationName === 'INIT_WATCH';\r\n            const abortWatch = () => {\r\n                this.closeWithError(new error_1.CloudSDKError({\r\n                    errCode: isInitWatch\r\n                        ? error_config_1.ERR_CODE.SDK_DATABASE_REALTIME_LISTENER_INIT_WATCH_FAIL\r\n                        : error_config_1.ERR_CODE.SDK_DATABASE_REALTIME_LISTENER_REBUILD_WATCH_FAIL,\r\n                    errMsg: e\r\n                }));\r\n                options.reject(e);\r\n            };\r\n            const retry = (refreshLogin) => {\r\n                if (this.useRetryTicket(options.operationName)) {\r\n                    if (isInitWatch) {\r\n                        this._initWatchPromise = undefined;\r\n                        options.resolve(this.initWatch(refreshLogin));\r\n                    }\r\n                    else {\r\n                        this._rebuildWatchPromise = undefined;\r\n                        options.resolve(this.rebuildWatch(refreshLogin));\r\n                    }\r\n                }\r\n                else {\r\n                    abortWatch();\r\n                }\r\n            };\r\n            this.handleCommonError(e, {\r\n                onSignError: () => retry(true),\r\n                onTimeoutError: () => retry(false),\r\n                onNotRetryableError: abortWatch,\r\n                onCancelledError: options.reject,\r\n                onUnknownError: async () => {\r\n                    try {\r\n                        const onWSDisconnected = async () => {\r\n                            this.pause();\r\n                            await this.onceWSConnected();\r\n                            retry(true);\r\n                        };\r\n                        if (!this.isWSConnected()) {\r\n                            await onWSDisconnected();\r\n                        }\r\n                        else {\r\n                            await utils_1.sleep(DEFAULT_WAIT_TIME_ON_UNKNOWN_ERROR);\r\n                            if (this.watchStatus === WATCH_STATUS.PAUSED) {\r\n                                options.reject(new error_1.CancelledError(`${options.operationName} cancelled due to pause after unknownError`));\r\n                            }\r\n                            else if (!this.isWSConnected()) {\r\n                                await onWSDisconnected();\r\n                            }\r\n                            else {\r\n                                retry(false);\r\n                            }\r\n                        }\r\n                    }\r\n                    catch (e) {\r\n                        retry(true);\r\n                    }\r\n                }\r\n            });\r\n        };\r\n        this.closeWatch = async () => {\r\n            const queryId = this.sessionInfo ? this.sessionInfo.queryID : '';\r\n            if (this.watchStatus !== WATCH_STATUS.ACTIVE) {\r\n                this.watchStatus = WATCH_STATUS.CLOSED;\r\n                this.onWatchClose(this, queryId);\r\n                return;\r\n            }\r\n            try {\r\n                this.watchStatus = WATCH_STATUS.CLOSING;\r\n                const closeWatchMsg = {\r\n                    watchId: this.watchId,\r\n                    requestId: message_1.genRequestId(),\r\n                    msgType: 'CLOSE_WATCH',\r\n                    msgData: null\r\n                };\r\n                await this.send({\r\n                    msg: closeWatchMsg\r\n                });\r\n                this.sessionInfo = undefined;\r\n                this.watchStatus = WATCH_STATUS.CLOSED;\r\n            }\r\n            catch (e) {\r\n                this.closeWithError(new error_1.CloudSDKError({\r\n                    errCode: error_config_1.ERR_CODE.SDK_DATABASE_REALTIME_LISTENER_CLOSE_WATCH_FAIL,\r\n                    errMsg: e\r\n                }));\r\n            }\r\n            finally {\r\n                this.onWatchClose(this, queryId);\r\n            }\r\n        };\r\n        this.scheduleSendACK = () => {\r\n            this.clearACKSchedule();\r\n            this._ackTimeoutId = setTimeout(() => {\r\n                if (this._waitExpectedTimeoutId) {\r\n                    this.scheduleSendACK();\r\n                }\r\n                else {\r\n                    this.sendACK();\r\n                }\r\n            }, DEFAULT_SEND_ACK_DEBOUNCE_TIMEOUT);\r\n        };\r\n        this.clearACKSchedule = () => {\r\n            if (this._ackTimeoutId) {\r\n                clearTimeout(this._ackTimeoutId);\r\n            }\r\n        };\r\n        this.sendACK = async () => {\r\n            try {\r\n                if (this.watchStatus !== WATCH_STATUS.ACTIVE) {\r\n                    this.scheduleSendACK();\r\n                    return;\r\n                }\r\n                if (!this.sessionInfo) {\r\n                    console.warn('[realtime listener] can not send ack without a successful initWatch (lack of sessionInfo)');\r\n                    return;\r\n                }\r\n                const ackMsg = {\r\n                    watchId: this.watchId,\r\n                    requestId: message_1.genRequestId(),\r\n                    msgType: 'CHECK_LAST',\r\n                    msgData: {\r\n                        queryID: this.sessionInfo.queryID,\r\n                        eventID: this.sessionInfo.currentEventId\r\n                    }\r\n                };\r\n                await this.send({\r\n                    msg: ackMsg\r\n                });\r\n                this.scheduleSendACK();\r\n            }\r\n            catch (e) {\r\n                if (error_2.isRealtimeErrorMessageError(e)) {\r\n                    const msg = e.payload;\r\n                    switch (msg.msgData.code) {\r\n                        case 'CHECK_LOGIN_FAILED':\r\n                        case 'SIGN_EXPIRED_ERROR':\r\n                        case 'SIGN_INVALID_ERROR':\r\n                        case 'SIGN_PARAM_INVALID': {\r\n                            this.rebuildWatch();\r\n                            return;\r\n                        }\r\n                        case 'QUERYID_INVALID_ERROR':\r\n                        case 'SYS_ERR':\r\n                        case 'INVALIID_ENV':\r\n                        case 'COLLECTION_PERMISSION_DENIED': {\r\n                            this.closeWithError(new error_1.CloudSDKError({\r\n                                errCode: error_config_1.ERR_CODE.SDK_DATABASE_REALTIME_LISTENER_CHECK_LAST_FAIL,\r\n                                errMsg: msg.msgData.code\r\n                            }));\r\n                            return;\r\n                        }\r\n                        default: {\r\n                            break;\r\n                        }\r\n                    }\r\n                }\r\n                if (this._availableRetries.CHECK_LAST &&\r\n                    this._availableRetries.CHECK_LAST > 0) {\r\n                    this._availableRetries.CHECK_LAST--;\r\n                    this.scheduleSendACK();\r\n                }\r\n                else {\r\n                    this.closeWithError(new error_1.CloudSDKError({\r\n                        errCode: error_config_1.ERR_CODE.SDK_DATABASE_REALTIME_LISTENER_CHECK_LAST_FAIL,\r\n                        errMsg: e\r\n                    }));\r\n                }\r\n            }\r\n        };\r\n        this.handleCommonError = (e, options) => {\r\n            if (error_2.isRealtimeErrorMessageError(e)) {\r\n                const msg = e.payload;\r\n                switch (msg.msgData.code) {\r\n                    case 'CHECK_LOGIN_FAILED':\r\n                    case 'SIGN_EXPIRED_ERROR':\r\n                    case 'SIGN_INVALID_ERROR':\r\n                    case 'SIGN_PARAM_INVALID': {\r\n                        options.onSignError(e);\r\n                        return;\r\n                    }\r\n                    case 'QUERYID_INVALID_ERROR':\r\n                    case 'SYS_ERR':\r\n                    case 'INVALIID_ENV':\r\n                    case 'COLLECTION_PERMISSION_DENIED': {\r\n                        options.onNotRetryableError(e);\r\n                        return;\r\n                    }\r\n                    default: {\r\n                        options.onNotRetryableError(e);\r\n                        return;\r\n                    }\r\n                }\r\n            }\r\n            else if (error_1.isTimeoutError(e)) {\r\n                options.onTimeoutError(e);\r\n                return;\r\n            }\r\n            else if (error_1.isCancelledError(e)) {\r\n                options.onCancelledError(e);\r\n                return;\r\n            }\r\n            options.onUnknownError(e);\r\n        };\r\n        this.watchId = `watchid_${+new Date()}_${Math.random()}`;\r\n        this.envId = options.envId;\r\n        this.collectionName = options.collectionName;\r\n        this.query = options.query;\r\n        this.limit = options.limit;\r\n        this.orderBy = options.orderBy;\r\n        this.send = options.send;\r\n        this.login = options.login;\r\n        this.isWSConnected = options.isWSConnected;\r\n        this.onceWSConnected = options.onceWSConnected;\r\n        this.getWaitExpectedTimeoutLength = options.getWaitExpectedTimeoutLength;\r\n        this.onWatchStart = options.onWatchStart;\r\n        this.onWatchClose = options.onWatchClose;\r\n        this.debug = options.debug;\r\n        this._availableRetries = {\r\n            INIT_WATCH: DEFAULT_MAX_AUTO_RETRY_ON_ERROR,\r\n            REBUILD_WATCH: DEFAULT_MAX_AUTO_RETRY_ON_ERROR,\r\n            CHECK_LAST: DEFAULT_MAX_SEND_ACK_AUTO_RETRY_ON_ERROR\r\n        };\r\n        this.listener = new listener_1.RealtimeListener({\r\n            close: this.closeWatch,\r\n            onChange: options.onChange,\r\n            onError: options.onError,\r\n            debug: this.debug,\r\n            virtualClient: this\r\n        });\r\n        this.initWatch();\r\n    }\r\n    useRetryTicket(operationName) {\r\n        if (this._availableRetries[operationName] &&\r\n            this._availableRetries[operationName] > 0) {\r\n            this._availableRetries[operationName]--;\r\n            console.log(`[realtime] ${operationName} use a retry ticket, now only ${this._availableRetries[operationName]} retry left`);\r\n            return true;\r\n        }\r\n        return false;\r\n    }\r\n    async handleServerEvents(msg) {\r\n        try {\r\n            this.scheduleSendACK();\r\n            await this._handleServerEvents(msg);\r\n            this._postHandleServerEventsValidityCheck(msg);\r\n        }\r\n        catch (e) {\r\n            console.error('[realtime listener] internal non-fatal error: handle server events failed with error: ', e);\r\n            throw e;\r\n        }\r\n    }\r\n    async _handleServerEvents(msg) {\r\n        const { requestId } = msg;\r\n        const { events } = msg.msgData;\r\n        const { msgType } = msg;\r\n        if (!events.length || !this.sessionInfo) {\r\n            return;\r\n        }\r\n        const sessionInfo = this.sessionInfo;\r\n        let allChangeEvents;\r\n        try {\r\n            allChangeEvents = events.map(getPublicEvent);\r\n        }\r\n        catch (e) {\r\n            this.closeWithError(new error_1.CloudSDKError({\r\n                errCode: error_config_1.ERR_CODE.SDK_DATABASE_REALTIME_LISTENER_RECEIVE_INVALID_SERVER_DATA,\r\n                errMsg: e\r\n            }));\r\n            return;\r\n        }\r\n        let docs = [...sessionInfo.currentDocs];\r\n        let initEncountered = false;\r\n        for (let i = 0, len = allChangeEvents.length; i < len; i++) {\r\n            const change = allChangeEvents[i];\r\n            if (sessionInfo.currentEventId >= change.id) {\r\n                if (!allChangeEvents[i - 1] || change.id > allChangeEvents[i - 1].id) {\r\n                    console.warn(`[realtime] duplicate event received, cur ${sessionInfo.currentEventId} but got ${change.id}`);\r\n                }\r\n                else {\r\n                    console.error(`[realtime listener] server non-fatal error: events out of order (the latter event's id is smaller than that of the former) (requestId ${requestId})`);\r\n                }\r\n                continue;\r\n            }\r\n            else if (sessionInfo.currentEventId === change.id - 1) {\r\n                switch (change.dataType) {\r\n                    case 'update': {\r\n                        if (!change.doc) {\r\n                            switch (change.queueType) {\r\n                                case 'update':\r\n                                case 'dequeue': {\r\n                                    const localDoc = docs.find(doc => doc._id === change.docId);\r\n                                    if (localDoc) {\r\n                                        const doc = lodash_clonedeep_1.default(localDoc);\r\n                                        if (change.updatedFields) {\r\n                                            for (const fieldPath in change.updatedFields) {\r\n                                                lodash_set_1.default(doc, fieldPath, change.updatedFields[fieldPath]);\r\n                                            }\r\n                                        }\r\n                                        if (change.removedFields) {\r\n                                            for (const fieldPath of change.removedFields) {\r\n                                                lodash_unset_1.default(doc, fieldPath);\r\n                                            }\r\n                                        }\r\n                                        change.doc = doc;\r\n                                    }\r\n                                    else {\r\n                                        console.error('[realtime listener] internal non-fatal server error: unexpected update dataType event where no doc is associated.');\r\n                                    }\r\n                                    break;\r\n                                }\r\n                                case 'enqueue': {\r\n                                    const err = new error_1.CloudSDKError({\r\n                                        errCode: error_config_1.ERR_CODE.SDK_DATABASE_REALTIME_LISTENER_UNEXPECTED_FATAL_ERROR,\r\n                                        errMsg: `HandleServerEvents: full doc is not provided with dataType=\"update\" and queueType=\"enqueue\" (requestId ${msg.requestId})`\r\n                                    });\r\n                                    this.closeWithError(err);\r\n                                    throw err;\r\n                                }\r\n                                default: {\r\n                                    break;\r\n                                }\r\n                            }\r\n                        }\r\n                        break;\r\n                    }\r\n                    case 'replace': {\r\n                        if (!change.doc) {\r\n                            const err = new error_1.CloudSDKError({\r\n                                errCode: error_config_1.ERR_CODE.SDK_DATABASE_REALTIME_LISTENER_UNEXPECTED_FATAL_ERROR,\r\n                                errMsg: `HandleServerEvents: full doc is not provided with dataType=\"replace\" (requestId ${msg.requestId})`\r\n                            });\r\n                            this.closeWithError(err);\r\n                            throw err;\r\n                        }\r\n                        break;\r\n                    }\r\n                    case 'remove': {\r\n                        const doc = docs.find(doc => doc._id === change.docId);\r\n                        if (doc) {\r\n                            change.doc = doc;\r\n                        }\r\n                        else {\r\n                            console.error('[realtime listener] internal non-fatal server error: unexpected remove event where no doc is associated.');\r\n                        }\r\n                        break;\r\n                    }\r\n                    case 'limit': {\r\n                        if (!change.doc) {\r\n                            switch (change.queueType) {\r\n                                case 'dequeue': {\r\n                                    const doc = docs.find(doc => doc._id === change.docId);\r\n                                    if (doc) {\r\n                                        change.doc = doc;\r\n                                    }\r\n                                    else {\r\n                                        console.error('[realtime listener] internal non-fatal server error: unexpected limit dataType event where no doc is associated.');\r\n                                    }\r\n                                    break;\r\n                                }\r\n                                case 'enqueue': {\r\n                                    const err = new error_1.CloudSDKError({\r\n                                        errCode: error_config_1.ERR_CODE.SDK_DATABASE_REALTIME_LISTENER_UNEXPECTED_FATAL_ERROR,\r\n                                        errMsg: `HandleServerEvents: full doc is not provided with dataType=\"limit\" and queueType=\"enqueue\" (requestId ${msg.requestId})`\r\n                                    });\r\n                                    this.closeWithError(err);\r\n                                    throw err;\r\n                                }\r\n                                default: {\r\n                                    break;\r\n                                }\r\n                            }\r\n                        }\r\n                        break;\r\n                    }\r\n                }\r\n                switch (change.queueType) {\r\n                    case 'init': {\r\n                        if (!initEncountered) {\r\n                            initEncountered = true;\r\n                            docs = [change.doc];\r\n                        }\r\n                        else {\r\n                            docs.push(change.doc);\r\n                        }\r\n                        break;\r\n                    }\r\n                    case 'enqueue': {\r\n                        docs.push(change.doc);\r\n                        break;\r\n                    }\r\n                    case 'dequeue': {\r\n                        const ind = docs.findIndex(doc => doc._id === change.docId);\r\n                        if (ind > -1) {\r\n                            docs.splice(ind, 1);\r\n                        }\r\n                        else {\r\n                            console.error('[realtime listener] internal non-fatal server error: unexpected dequeue event where no doc is associated.');\r\n                        }\r\n                        break;\r\n                    }\r\n                    case 'update': {\r\n                        const ind = docs.findIndex(doc => doc._id === change.docId);\r\n                        if (ind > -1) {\r\n                            docs[ind] = change.doc;\r\n                        }\r\n                        else {\r\n                            console.error('[realtime listener] internal non-fatal server error: unexpected queueType update event where no doc is associated.');\r\n                        }\r\n                        break;\r\n                    }\r\n                }\r\n                if (i === len - 1 ||\r\n                    (allChangeEvents[i + 1] && allChangeEvents[i + 1].id !== change.id)) {\r\n                    const docsSnapshot = [...docs];\r\n                    const docChanges = allChangeEvents\r\n                        .slice(0, i + 1)\r\n                        .filter(c => c.id === change.id);\r\n                    this.sessionInfo.currentEventId = change.id;\r\n                    this.sessionInfo.currentDocs = docs;\r\n                    const snapshot = new snapshot_1.Snapshot({\r\n                        id: change.id,\r\n                        docChanges,\r\n                        docs: docsSnapshot,\r\n                        msgType\r\n                    });\r\n                    this.listener.onChange(snapshot);\r\n                }\r\n            }\r\n            else {\r\n                console.warn(`[realtime listener] event received is out of order, cur ${this.sessionInfo.currentEventId} but got ${change.id}`);\r\n                await this.rebuildWatch();\r\n                return;\r\n            }\r\n        }\r\n    }\r\n    _postHandleServerEventsValidityCheck(msg) {\r\n        if (!this.sessionInfo) {\r\n            console.error('[realtime listener] internal non-fatal error: sessionInfo lost after server event handling, this should never occur');\r\n            return;\r\n        }\r\n        if (this.sessionInfo.expectEventId &&\r\n            this.sessionInfo.currentEventId >= this.sessionInfo.expectEventId) {\r\n            this.clearWaitExpectedEvent();\r\n        }\r\n        if (this.sessionInfo.currentEventId < msg.msgData.currEvent) {\r\n            console.warn('[realtime listener] internal non-fatal error: client eventId does not match with server event id after server event handling');\r\n            return;\r\n        }\r\n    }\r\n    clearWaitExpectedEvent() {\r\n        if (this._waitExpectedTimeoutId) {\r\n            clearTimeout(this._waitExpectedTimeoutId);\r\n            this._waitExpectedTimeoutId = undefined;\r\n        }\r\n    }\r\n    onMessage(msg) {\r\n        switch (this.watchStatus) {\r\n            case WATCH_STATUS.PAUSED: {\r\n                if (msg.msgType !== 'ERROR') {\r\n                    return;\r\n                }\r\n                break;\r\n            }\r\n            case WATCH_STATUS.LOGGINGIN:\r\n            case WATCH_STATUS.INITING:\r\n            case WATCH_STATUS.REBUILDING: {\r\n                console.warn(`[realtime listener] internal non-fatal error: unexpected message received while ${this.watchStatus}`);\r\n                return;\r\n            }\r\n            case WATCH_STATUS.CLOSED: {\r\n                console.warn('[realtime listener] internal non-fatal error: unexpected message received when the watch has closed');\r\n                return;\r\n            }\r\n            case WATCH_STATUS.ERRORED: {\r\n                console.warn('[realtime listener] internal non-fatal error: unexpected message received when the watch has ended with error');\r\n                return;\r\n            }\r\n        }\r\n        if (!this.sessionInfo) {\r\n            console.warn('[realtime listener] internal non-fatal error: sessionInfo not found while message is received.');\r\n            return;\r\n        }\r\n        this.scheduleSendACK();\r\n        switch (msg.msgType) {\r\n            case 'NEXT_EVENT': {\r\n                console.warn(`nextevent ${msg.msgData.currEvent} ignored`, msg);\r\n                this.handleServerEvents(msg);\r\n                break;\r\n            }\r\n            case 'CHECK_EVENT': {\r\n                if (this.sessionInfo.currentEventId < msg.msgData.currEvent) {\r\n                    this.sessionInfo.expectEventId = msg.msgData.currEvent;\r\n                    this.clearWaitExpectedEvent();\r\n                    this._waitExpectedTimeoutId = setTimeout(() => {\r\n                        this.rebuildWatch();\r\n                    }, this.getWaitExpectedTimeoutLength());\r\n                    console.log(`[realtime] waitExpectedTimeoutLength ${this.getWaitExpectedTimeoutLength()}`);\r\n                }\r\n                break;\r\n            }\r\n            case 'ERROR': {\r\n                this.closeWithError(new error_1.CloudSDKError({\r\n                    errCode: error_config_1.ERR_CODE.SDK_DATABASE_REALTIME_LISTENER_SERVER_ERROR_MSG,\r\n                    errMsg: `${msg.msgData.code} - ${msg.msgData.message}`\r\n                }));\r\n                break;\r\n            }\r\n            default: {\r\n                console.warn(`[realtime listener] virtual client receive unexpected msg ${msg.msgType}: `, msg);\r\n                break;\r\n            }\r\n        }\r\n    }\r\n    closeWithError(error) {\r\n        this.watchStatus = WATCH_STATUS.ERRORED;\r\n        this.clearACKSchedule();\r\n        this.listener.onError(error);\r\n        this.onWatchClose(this, (this.sessionInfo && this.sessionInfo.queryID) || '');\r\n        console.log(`[realtime] client closed (${this.collectionName} ${this.query}) (watchId ${this.watchId})`);\r\n    }\r\n    pause() {\r\n        this.watchStatus = WATCH_STATUS.PAUSED;\r\n        console.log(`[realtime] client paused (${this.collectionName} ${this.query}) (watchId ${this.watchId})`);\r\n    }\r\n    async resume() {\r\n        this.watchStatus = WATCH_STATUS.RESUMING;\r\n        console.log(`[realtime] client resuming with ${this.sessionInfo ? 'REBUILD_WATCH' : 'INIT_WATCH'} (${this.collectionName} ${this.query}) (${this.watchId})`);\r\n        try {\r\n            await (this.sessionInfo ? this.rebuildWatch() : this.initWatch());\r\n            console.log(`[realtime] client successfully resumed (${this.collectionName} ${this.query}) (${this.watchId})`);\r\n        }\r\n        catch (e) {\r\n            console.error(`[realtime] client resume failed (${this.collectionName} ${this.query}) (${this.watchId})`, e);\r\n        }\r\n    }\r\n}\r\nexports.VirtualWebSocketClient = VirtualWebSocketClient;\r\nfunction getPublicEvent(event) {\r\n    const e = {\r\n        id: event.ID,\r\n        dataType: event.DataType,\r\n        queueType: event.QueueType,\r\n        docId: event.DocID,\r\n        doc: event.Doc && event.Doc !== '{}' ? JSON.parse(event.Doc) : undefined\r\n    };\r\n    if (event.DataType === 'update') {\r\n        if (event.UpdatedFields) {\r\n            e.updatedFields = JSON.parse(event.UpdatedFields);\r\n        }\r\n        if (event.removedFields || event.RemovedFields) {\r\n            e.removedFields = JSON.parse(event.removedFields);\r\n        }\r\n    }\r\n    return e;\r\n}\r\n", "\r\nObject.defineProperty(exports, \"__esModule\", { value: true });\r\nfunction genRequestId(prefix = '') {\r\n    return `${prefix ? `${prefix}_` : ''}${+new Date()}_${Math.random()}`;\r\n}\r\nexports.genRequestId = genRequestId;\r\nfunction isInitEventMessage(msg) {\r\n    return msg.msgType === 'INIT_EVENT';\r\n}\r\nexports.isInitEventMessage = isInitEventMessage;\r\n", "\r\nObject.defineProperty(exports, \"__esModule\", { value: true });\r\nconst type_1 = require(\"./type\");\r\nconst error_config_1 = require(\"../config/error.config\");\r\nclass CloudSDKError extends Error {\r\n    constructor(options) {\r\n        super(options.errMsg);\r\n        this.errCode = 'UNKNOWN_ERROR';\r\n        Object.defineProperties(this, {\r\n            message: {\r\n                get() {\r\n                    return (`errCode: ${this.errCode} ${error_config_1.ERR_CODE[this.errCode] ||\r\n                        ''} | errMsg: ` + this.errMsg);\r\n                },\r\n                set(msg) {\r\n                    this.errMsg = msg;\r\n                }\r\n            }\r\n        });\r\n        this.errCode = options.errCode || 'UNKNOWN_ERROR';\r\n        this.errMsg = options.errMsg;\r\n    }\r\n    get message() {\r\n        return `errCode: ${this.errCode} | errMsg: ` + this.errMsg;\r\n    }\r\n    set message(msg) {\r\n        this.errMsg = msg;\r\n    }\r\n}\r\nexports.CloudSDKError = CloudSDKError;\r\nfunction isSDKError(error) {\r\n    return (error && error instanceof Error && type_1.isString(error.errMsg));\r\n}\r\nexports.isSDKError = isSDKError;\r\nexports.isGenericError = (e) => e.generic;\r\nclass TimeoutError extends Error {\r\n    constructor(message) {\r\n        super(message);\r\n        this.type = 'timeout';\r\n        this.payload = null;\r\n        this.generic = true;\r\n    }\r\n}\r\nexports.TimeoutError = TimeoutError;\r\nexports.isTimeoutError = (e) => e.type === 'timeout';\r\nclass CancelledError extends Error {\r\n    constructor(message) {\r\n        super(message);\r\n        this.type = 'cancelled';\r\n        this.payload = null;\r\n        this.generic = true;\r\n    }\r\n}\r\nexports.CancelledError = CancelledError;\r\nexports.isCancelledError = (e) => e.type === 'cancelled';\r\n", "\r\nObject.defineProperty(exports, \"__esModule\", { value: true });\r\nexports.ERR_CODE = {\r\n    UNKNOWN_ERROR: 'UNKNOWN_ERROR',\r\n    SDK_DATABASE_REALTIME_LISTENER_INIT_WATCH_FAIL: 'SDK_DATABASE_REALTIME_LISTENER_INIT_WATCH_FAIL',\r\n    SDK_DATABASE_REALTIME_LISTENER_RECONNECT_WATCH_FAIL: 'SDK_DATABASE_REALTIME_LISTENER_RECONNECT_WATCH_FAIL',\r\n    SDK_DATABASE_REALTIME_LISTENER_REBUILD_WATCH_FAIL: 'SDK_DATABASE_REALTIME_LISTENER_REBUILD_WATCH_FAIL',\r\n    SDK_DATABASE_REALTIME_LISTENER_CLOSE_WATCH_FAIL: 'SDK_DATABASE_REALTIME_LISTENER_CLOSE_WATCH_FAIL',\r\n    SDK_DATABASE_REALTIME_LISTENER_SERVER_ERROR_MSG: 'SDK_DATABASE_REALTIME_LISTENER_SERVER_ERROR_MSG',\r\n    SDK_DATABASE_REALTIME_LISTENER_RECEIVE_INVALID_SERVER_DATA: 'SDK_DATABASE_REALTIME_LISTENER_RECEIVE_INVALID_SERVER_DATA',\r\n    SDK_DATABASE_REALTIME_LISTENER_WEBSOCKET_CONNECTION_ERROR: 'SDK_DATABASE_REALTIME_LISTENER_WEBSOCKET_CONNECTION_ERROR',\r\n    SDK_DATABASE_REALTIME_LISTENER_WEBSOCKET_CONNECTION_CLOSED: 'SDK_DATABASE_REALTIME_LISTENER_WEBSOCKET_CONNECTION_CLOSED',\r\n    SDK_DATABASE_REALTIME_LISTENER_CHECK_LAST_FAIL: 'SDK_DATABASE_REALTIME_LISTENER_CHECK_LAST_FAIL',\r\n    SDK_DATABASE_REALTIME_LISTENER_UNEXPECTED_FATAL_ERROR: 'SDK_DATABASE_REALTIME_LISTENER_UNEXPECTED_FATAL_ERROR'\r\n};\r\n", "\r\nObject.defineProperty(exports, \"__esModule\", { value: true });\r\nclass RealtimeListener {\r\n    constructor(options) {\r\n        this.close = options.close;\r\n        this.onChange = options.onChange;\r\n        this.onError = options.onError;\r\n        if (options.debug) {\r\n            Object.defineProperty(this, 'virtualClient', {\r\n                get: () => {\r\n                    return options.virtualClient;\r\n                }\r\n            });\r\n        }\r\n    }\r\n}\r\nexports.RealtimeListener = RealtimeListener;\r\n", "\r\nObject.defineProperty(exports, \"__esModule\", { value: true });\r\nclass Snapshot {\r\n    constructor(options) {\r\n        const { id, docChanges, docs, msgType, type } = options;\r\n        let cachedDocChanges;\r\n        let cachedDocs;\r\n        Object.defineProperties(this, {\r\n            id: {\r\n                get: () => id,\r\n                enumerable: true\r\n            },\r\n            docChanges: {\r\n                get: () => {\r\n                    if (!cachedDocChanges) {\r\n                        cachedDocChanges = JSON.parse(JSON.stringify(docChanges));\r\n                    }\r\n                    return cachedDocChanges;\r\n                },\r\n                enumerable: true\r\n            },\r\n            docs: {\r\n                get: () => {\r\n                    if (!cachedDocs) {\r\n                        cachedDocs = JSON.parse(JSON.stringify(docs));\r\n                    }\r\n                    return cachedDocs;\r\n                },\r\n                enumerable: true\r\n            },\r\n            msgType: {\r\n                get: () => msgType,\r\n                enumerable: true\r\n            },\r\n            type: {\r\n                get: () => type,\r\n                enumerable: true\r\n            }\r\n        });\r\n    }\r\n}\r\nexports.Snapshot = Snapshot;\r\n", "\r\nObject.defineProperty(exports, \"__esModule\", { value: true });\r\nclass RealtimeErrorMessageError extends Error {\r\n    constructor(serverErrorMsg) {\r\n        super(`Watch Error ${JSON.stringify(serverErrorMsg.msgData)} (requestid: ${serverErrorMsg.requestId})`);\r\n        this.isRealtimeErrorMessageError = true;\r\n        this.payload = serverErrorMsg;\r\n    }\r\n}\r\nexports.RealtimeErrorMessageError = RealtimeErrorMessageError;\r\nexports.isRealtimeErrorMessageError = (e) => e && e.isRealtimeErrorMessageError;\r\n", "\r\nObject.defineProperty(exports, \"__esModule\", { value: true });\r\nconst error_1 = require(\"../utils/error\");\r\nconst error_config_1 = require(\"../config/error.config\");\r\nexports.CLOSE_EVENT_CODE_INFO = {\r\n    1000: {\r\n        code: 1000,\r\n        name: 'Normal Closure',\r\n        description: 'Normal closure; the connection successfully completed whatever purpose for which it was created.'\r\n    },\r\n    1001: {\r\n        code: 1001,\r\n        name: 'Going Away',\r\n        description: 'The endpoint is going away, either because of a server failure or because the browser is navigating away from the page that opened the connection.'\r\n    },\r\n    1002: {\r\n        code: 1002,\r\n        name: 'Protocol Error',\r\n        description: 'The endpoint is terminating the connection due to a protocol error.'\r\n    },\r\n    1003: {\r\n        code: 1003,\r\n        name: 'Unsupported Data',\r\n        description: 'The connection is being terminated because the endpoint received data of a type it cannot accept (for example, a text-only endpoint received binary data).'\r\n    },\r\n    1005: {\r\n        code: 1005,\r\n        name: 'No Status Received',\r\n        description: 'Indicates that no status code was provided even though one was expected.'\r\n    },\r\n    1006: {\r\n        code: 1006,\r\n        name: 'Abnormal Closure',\r\n        description: 'Used to indicate that a connection was closed abnormally (that is, with no close frame being sent) when a status code is expected.'\r\n    },\r\n    1007: {\r\n        code: 1007,\r\n        name: 'Invalid frame payload data',\r\n        description: 'The endpoint is terminating the connection because a message was received that contained inconsistent data (e.g., non-UTF-8 data within a text message).'\r\n    },\r\n    1008: {\r\n        code: 1008,\r\n        name: 'Policy Violation',\r\n        description: 'The endpoint is terminating the connection because it received a message that violates its policy. This is a generic status code, used when codes 1003 and 1009 are not suitable.'\r\n    },\r\n    1009: {\r\n        code: 1009,\r\n        name: 'Message too big',\r\n        description: 'The endpoint is terminating the connection because a data frame was received that is too large.'\r\n    },\r\n    1010: {\r\n        code: 1010,\r\n        name: 'Missing Extension',\r\n        description: \"The client is terminating the connection because it expected the server to negotiate one or more extension, but the server didn't.\"\r\n    },\r\n    1011: {\r\n        code: 1011,\r\n        name: 'Internal Error',\r\n        description: 'The server is terminating the connection because it encountered an unexpected condition that prevented it from fulfilling the request.'\r\n    },\r\n    1012: {\r\n        code: 1012,\r\n        name: 'Service Restart',\r\n        description: 'The server is terminating the connection because it is restarting.'\r\n    },\r\n    1013: {\r\n        code: 1013,\r\n        name: 'Try Again Later',\r\n        description: 'The server is terminating the connection due to a temporary condition, e.g. it is overloaded and is casting off some of its clients.'\r\n    },\r\n    1014: {\r\n        code: 1014,\r\n        name: 'Bad Gateway',\r\n        description: 'The server was acting as a gateway or proxy and received an invalid response from the upstream server. This is similar to 502 HTTP Status Code.'\r\n    },\r\n    1015: {\r\n        code: 1015,\r\n        name: 'TLS Handshake',\r\n        description: \"Indicates that the connection was closed due to a failure to perform a TLS handshake (e.g., the server certificate can't be verified).\"\r\n    },\r\n    3000: {\r\n        code: 3000,\r\n        name: 'Reconnect WebSocket',\r\n        description: 'The client is terminating the connection because it wants to reconnect'\r\n    },\r\n    3001: {\r\n        code: 3001,\r\n        name: 'No Realtime Listeners',\r\n        description: 'The client is terminating the connection because no more realtime listeners exist'\r\n    },\r\n    3002: {\r\n        code: 3002,\r\n        name: 'Heartbeat Ping Error',\r\n        description: 'The client is terminating the connection due to its failure in sending heartbeat messages'\r\n    },\r\n    3003: {\r\n        code: 3003,\r\n        name: 'Heartbeat Pong Timeout Error',\r\n        description: 'The client is terminating the connection because no heartbeat response is received from the server'\r\n    },\r\n    3050: {\r\n        code: 3050,\r\n        name: 'Server Close',\r\n        description: 'The client is terminating the connection because no heartbeat response is received from the server'\r\n    }\r\n};\r\nvar CLOSE_EVENT_CODE;\r\n(function (CLOSE_EVENT_CODE) {\r\n    CLOSE_EVENT_CODE[CLOSE_EVENT_CODE[\"NormalClosure\"] = 1000] = \"NormalClosure\";\r\n    CLOSE_EVENT_CODE[CLOSE_EVENT_CODE[\"GoingAway\"] = 1001] = \"GoingAway\";\r\n    CLOSE_EVENT_CODE[CLOSE_EVENT_CODE[\"ProtocolError\"] = 1002] = \"ProtocolError\";\r\n    CLOSE_EVENT_CODE[CLOSE_EVENT_CODE[\"UnsupportedData\"] = 1003] = \"UnsupportedData\";\r\n    CLOSE_EVENT_CODE[CLOSE_EVENT_CODE[\"NoStatusReceived\"] = 1005] = \"NoStatusReceived\";\r\n    CLOSE_EVENT_CODE[CLOSE_EVENT_CODE[\"AbnormalClosure\"] = 1006] = \"AbnormalClosure\";\r\n    CLOSE_EVENT_CODE[CLOSE_EVENT_CODE[\"InvalidFramePayloadData\"] = 1007] = \"InvalidFramePayloadData\";\r\n    CLOSE_EVENT_CODE[CLOSE_EVENT_CODE[\"PolicyViolation\"] = 1008] = \"PolicyViolation\";\r\n    CLOSE_EVENT_CODE[CLOSE_EVENT_CODE[\"MessageTooBig\"] = 1009] = \"MessageTooBig\";\r\n    CLOSE_EVENT_CODE[CLOSE_EVENT_CODE[\"MissingExtension\"] = 1010] = \"MissingExtension\";\r\n    CLOSE_EVENT_CODE[CLOSE_EVENT_CODE[\"InternalError\"] = 1011] = \"InternalError\";\r\n    CLOSE_EVENT_CODE[CLOSE_EVENT_CODE[\"ServiceRestart\"] = 1012] = \"ServiceRestart\";\r\n    CLOSE_EVENT_CODE[CLOSE_EVENT_CODE[\"TryAgainLater\"] = 1013] = \"TryAgainLater\";\r\n    CLOSE_EVENT_CODE[CLOSE_EVENT_CODE[\"BadGateway\"] = 1014] = \"BadGateway\";\r\n    CLOSE_EVENT_CODE[CLOSE_EVENT_CODE[\"TLSHandshake\"] = 1015] = \"TLSHandshake\";\r\n    CLOSE_EVENT_CODE[CLOSE_EVENT_CODE[\"ReconnectWebSocket\"] = 3000] = \"ReconnectWebSocket\";\r\n    CLOSE_EVENT_CODE[CLOSE_EVENT_CODE[\"NoRealtimeListeners\"] = 3001] = \"NoRealtimeListeners\";\r\n    CLOSE_EVENT_CODE[CLOSE_EVENT_CODE[\"HeartbeatPingError\"] = 3002] = \"HeartbeatPingError\";\r\n    CLOSE_EVENT_CODE[CLOSE_EVENT_CODE[\"HeartbeatPongTimeoutError\"] = 3003] = \"HeartbeatPongTimeoutError\";\r\n    CLOSE_EVENT_CODE[CLOSE_EVENT_CODE[\"NoAuthentication\"] = 3050] = \"NoAuthentication\";\r\n})(CLOSE_EVENT_CODE = exports.CLOSE_EVENT_CODE || (exports.CLOSE_EVENT_CODE = {}));\r\nexports.getWSCloseError = (code, reason) => {\r\n    const info = exports.CLOSE_EVENT_CODE_INFO[code];\r\n    const errMsg = !info\r\n        ? `code ${code}`\r\n        : `${info.name}, code ${code}, reason ${reason || info.description}`;\r\n    return new error_1.CloudSDKError({\r\n        errCode: error_config_1.ERR_CODE.SDK_DATABASE_REALTIME_LISTENER_WEBSOCKET_CONNECTION_CLOSED,\r\n        errMsg\r\n    });\r\n};\r\n", "\r\nObject.defineProperty(exports, \"__esModule\", { value: true });\r\nconst constant_1 = require(\"./constant\");\r\nconst index_1 = require(\"./index\");\r\nconst validate_1 = require(\"./validate\");\r\nconst util_1 = require(\"./util\");\r\nconst query_1 = require(\"./serializer/query\");\r\nconst update_1 = require(\"./serializer/update\");\r\nconst websocket_client_1 = require(\"./realtime/websocket-client\");\r\nconst constant_2 = require(\"./constant\");\r\nconst utils_1 = require(\"./utils/utils\");\r\nconst code_1 = require(\"./const/code\");\r\nconst bson_1 = require(\"bson\");\r\nclass Query {\r\n    constructor(db, coll, fieldFilters, apiOptions, transactionId) {\r\n        this.watch = (options) => {\r\n            if (!index_1.Db.ws) {\r\n                index_1.Db.ws = new websocket_client_1.RealtimeWebSocketClient({\r\n                    context: {\r\n                        appConfig: {\r\n                            docSizeLimit: 1000,\r\n                            realtimePingInterval: 10000,\r\n                            realtimePongWaitTimeout: 5000,\r\n                            request: this._request\r\n                        }\r\n                    }\r\n                });\r\n            }\r\n            const { limit, order } = this._apiOptions;\r\n            return index_1.Db.ws.watch(Object.assign(Object.assign({}, options), { envId: this._db.config.env, collectionName: this._coll, query: JSON.stringify(this._fieldFilters), limit, orderBy: order\r\n                    ? order.reduce((acc, cur) => {\r\n                        acc[cur.field] = cur.direction;\r\n                        return acc;\r\n                    }, {})\r\n                    : undefined }));\r\n        };\r\n        this._db = db;\r\n        this._coll = coll;\r\n        this._fieldFilters = fieldFilters;\r\n        this._apiOptions = apiOptions || {};\r\n        this._request = new index_1.Db.reqClass(this._db.config);\r\n        this._transactionId = transactionId;\r\n    }\r\n    async get() {\r\n        const order = this._apiOptions.order;\r\n        let param = {\r\n            collectionName: this._coll,\r\n            queryType: constant_1.QueryType.WHERE,\r\n            transactionId: this._transactionId\r\n        };\r\n        if (this._fieldFilters) {\r\n            param.query = this._fieldFilters;\r\n        }\r\n        if (order) {\r\n            param.order = utils_1.stringifyByEJSON(order);\r\n        }\r\n        const offset = this._apiOptions.offset;\r\n        if (offset) {\r\n            param.offset = offset;\r\n        }\r\n        const limit = this._apiOptions.limit;\r\n        if (limit) {\r\n            param.limit = limit < 1000 ? limit : 1000;\r\n        }\r\n        else {\r\n            param.limit = 100;\r\n        }\r\n        const projection = this._apiOptions.projection;\r\n        if (projection) {\r\n            param.projection = utils_1.stringifyByEJSON(projection);\r\n        }\r\n        const res = await this._request.send('database.getDocument', param, utils_1.getReqOpts(this._apiOptions));\r\n        if (res.code) {\r\n            return res;\r\n        }\r\n        const list = res.data.list.map(item => bson_1.EJSON.parse(item));\r\n        const documents = util_1.Util.formatResDocumentData(list);\r\n        const result = {\r\n            data: documents,\r\n            requestId: res.requestId\r\n        };\r\n        if (res.limit)\r\n            result.limit = res.limit;\r\n        if (res.offset)\r\n            result.offset = res.offset;\r\n        return result;\r\n    }\r\n    async count() {\r\n        let param = {\r\n            collectionName: this._coll,\r\n            queryType: constant_1.QueryType.WHERE\r\n        };\r\n        if (this._fieldFilters) {\r\n            param.query = this._fieldFilters;\r\n        }\r\n        const res = await this._request.send('database.calculateDocument', param, utils_1.getReqOpts(this._apiOptions));\r\n        if (res.code) {\r\n            return res;\r\n        }\r\n        return {\r\n            requestId: res.requestId,\r\n            total: res.data.total\r\n        };\r\n    }\r\n    where(query) {\r\n        if (Object.prototype.toString.call(query).slice(8, -1) !== 'Object') {\r\n            throw Error(constant_2.ErrorCode.QueryParamTypeError);\r\n        }\r\n        const keys = Object.keys(query);\r\n        const checkFlag = keys.some(item => {\r\n            return query[item] !== undefined;\r\n        });\r\n        if (keys.length && !checkFlag) {\r\n            throw Error(constant_2.ErrorCode.QueryParamValueError);\r\n        }\r\n        return new Query(this._db, this._coll, query_1.QuerySerializer.encodeEJSON(query, this._apiOptions.raw || false), this._apiOptions, this._transactionId);\r\n    }\r\n    options(apiOptions) {\r\n        validate_1.Validate.isValidOptions(apiOptions);\r\n        return new Query(this._db, this._coll, this._fieldFilters, apiOptions, this._transactionId);\r\n    }\r\n    orderBy(fieldPath, directionStr) {\r\n        validate_1.Validate.isFieldPath(fieldPath);\r\n        validate_1.Validate.isFieldOrder(directionStr);\r\n        const newOrder = {\r\n            [fieldPath]: directionStr === 'desc' ? -1 : 1\r\n        };\r\n        const order = this._apiOptions.order || {};\r\n        const newApiOption = Object.assign({}, this._apiOptions, {\r\n            order: Object.assign({}, order, newOrder)\r\n        });\r\n        return new Query(this._db, this._coll, this._fieldFilters, newApiOption, this._transactionId);\r\n    }\r\n    limit(limit) {\r\n        validate_1.Validate.isInteger('limit', limit);\r\n        let newApiOption = Object.assign({}, this._apiOptions);\r\n        newApiOption.limit = limit;\r\n        return new Query(this._db, this._coll, this._fieldFilters, newApiOption, this._transactionId);\r\n    }\r\n    skip(offset) {\r\n        validate_1.Validate.isInteger('offset', offset);\r\n        let newApiOption = Object.assign({}, this._apiOptions);\r\n        newApiOption.offset = offset;\r\n        return new Query(this._db, this._coll, this._fieldFilters, newApiOption, this._transactionId);\r\n    }\r\n    async update(data) {\r\n        if (!data || typeof data !== 'object') {\r\n            return utils_1.processReturn(this._db.config.throwOnCode, Object.assign(Object.assign({}, code_1.ERRORS.INVALID_PARAM), { message: '参数必需是非空对象' }));\r\n        }\r\n        if (data.hasOwnProperty('_id')) {\r\n            return utils_1.processReturn(this._db.config.throwOnCode, Object.assign(Object.assign({}, code_1.ERRORS.INVALID_PARAM), { message: '不能更新_id的值' }));\r\n        }\r\n        let { multiple } = this._apiOptions;\r\n        const multi = multiple === undefined ? true : multiple;\r\n        let param = {\r\n            collectionName: this._coll,\r\n            queryType: constant_1.QueryType.WHERE,\r\n            multi,\r\n            merge: true,\r\n            upsert: false,\r\n            data: update_1.UpdateSerializer.encodeEJSON(data, this._apiOptions.raw || false)\r\n        };\r\n        if (this._fieldFilters) {\r\n            param.query = this._fieldFilters;\r\n        }\r\n        const res = await this._request.send('database.modifyDocument', param, utils_1.getReqOpts(this._apiOptions));\r\n        if (res.code) {\r\n            return res;\r\n        }\r\n        return {\r\n            requestId: res.requestId,\r\n            updated: res.data.updated,\r\n            upsertId: res.data.upsert_id\r\n        };\r\n    }\r\n    field(projection) {\r\n        let transformProjection = {};\r\n        for (let k in projection) {\r\n            if (typeof projection[k] === 'boolean') {\r\n                transformProjection[k] = projection[k] === true ? 1 : 0;\r\n            }\r\n            if (typeof projection[k] === 'number') {\r\n                transformProjection[k] = projection[k] > 0 ? 1 : 0;\r\n            }\r\n            if (typeof projection[k] === 'object') {\r\n                transformProjection[k] = projection[k];\r\n            }\r\n        }\r\n        let newApiOption = Object.assign({}, this._apiOptions);\r\n        newApiOption.projection = transformProjection;\r\n        return new Query(this._db, this._coll, this._fieldFilters, newApiOption, this._transactionId);\r\n    }\r\n    async remove() {\r\n        const { offset, limit, projection, order } = this._apiOptions;\r\n        if (offset !== undefined ||\r\n            limit !== undefined ||\r\n            projection !== undefined ||\r\n            order !== undefined) {\r\n            console.warn('`offset`, `limit`, `projection`, `orderBy` are not supported in remove() operation');\r\n        }\r\n        let { multiple } = this._apiOptions;\r\n        const multi = multiple === undefined ? true : multiple;\r\n        const param = {\r\n            collectionName: this._coll,\r\n            query: this._fieldFilters,\r\n            queryType: constant_1.QueryType.WHERE,\r\n            multi\r\n        };\r\n        const res = await this._request.send('database.removeDocument', param, utils_1.getReqOpts(this._apiOptions));\r\n        if (res.code) {\r\n            return res;\r\n        }\r\n        return {\r\n            requestId: res.requestId,\r\n            deleted: res.data.deleted\r\n        };\r\n    }\r\n    async updateAndReturn(data) {\r\n        if (!data || typeof data !== 'object') {\r\n            return utils_1.processReturn(this._db.config.throwOnCode, Object.assign(Object.assign({}, code_1.ERRORS.INVALID_PARAM), { message: '参数必需是非空对象' }));\r\n        }\r\n        if (data.hasOwnProperty('_id')) {\r\n            return utils_1.processReturn(this._db.config.throwOnCode, Object.assign(Object.assign({}, code_1.ERRORS.INVALID_PARAM), { message: '不能更新_id的值' }));\r\n        }\r\n        let param = {\r\n            collectionName: this._coll,\r\n            queryType: constant_1.QueryType.WHERE,\r\n            data: update_1.UpdateSerializer.encodeEJSON(data, false)\r\n        };\r\n        if (this._transactionId) {\r\n            param.transactionId = this._transactionId;\r\n        }\r\n        if (this._fieldFilters) {\r\n            param.query = this._fieldFilters;\r\n        }\r\n        const res = await this._request.send('database.modifyAndReturnDoc', param, utils_1.getReqOpts(this._apiOptions));\r\n        if (res.code) {\r\n            return res;\r\n        }\r\n        return {\r\n            requestId: res.requestId,\r\n            updated: res.data.updated,\r\n            doc: res.data.doc && bson_1.EJSON.parse(res.data.doc)\r\n        };\r\n    }\r\n}\r\nexports.Query = Query;\r\n", "\r\nObject.defineProperty(exports, \"__esModule\", { value: true });\r\nconst query_1 = require(\"../commands/query\");\r\nconst logic_1 = require(\"../commands/logic\");\r\nconst symbol_1 = require(\"../helper/symbol\");\r\nconst type_1 = require(\"../utils/type\");\r\nconst operator_map_1 = require(\"../operator-map\");\r\nconst common_1 = require(\"./common\");\r\nconst utils_1 = require(\"../utils/utils\");\r\nconst validate_1 = require(\"../validate\");\r\nclass QuerySerializer {\r\n    constructor() { }\r\n    static encode(query) {\r\n        const encoder = new QueryEncoder();\r\n        return encoder.encodeQuery(query);\r\n    }\r\n    static encodeEJSON(query, raw) {\r\n        const encoder = new QueryEncoder();\r\n        return utils_1.stringifyByEJSON(raw ? query : encoder.encodeQuery(query));\r\n    }\r\n}\r\nexports.QuerySerializer = QuerySerializer;\r\nclass QueryEncoder {\r\n    encodeQuery(query, key) {\r\n        if (common_1.isConversionRequired(query)) {\r\n            if (logic_1.isLogicCommand(query)) {\r\n                return this.encodeLogicCommand(query);\r\n            }\r\n            else if (query_1.isQueryCommand(query)) {\r\n                return this.encodeQueryCommand(query);\r\n            }\r\n            else if (type_1.isRegExp(query)) {\r\n                return { [key]: this.encodeRegExp(query) };\r\n            }\r\n            else if (type_1.isDate(query)) {\r\n                return { [key]: query };\r\n            }\r\n            else {\r\n                return { [key]: this.encodeQueryObject(query) };\r\n            }\r\n        }\r\n        else {\r\n            if (type_1.isObject(query)) {\r\n                return this.encodeQueryObject(query);\r\n            }\r\n            else {\r\n                return query;\r\n            }\r\n        }\r\n    }\r\n    encodeRegExp(query) {\r\n        return {\r\n            $regularExpression: {\r\n                pattern: query.source,\r\n                options: query.flags\r\n            }\r\n        };\r\n    }\r\n    encodeLogicCommand(query) {\r\n        switch (query.operator) {\r\n            case logic_1.LOGIC_COMMANDS_LITERAL.NOR:\r\n            case logic_1.LOGIC_COMMANDS_LITERAL.AND:\r\n            case logic_1.LOGIC_COMMANDS_LITERAL.OR: {\r\n                const $op = operator_map_1.operatorToString(query.operator);\r\n                const subqueries = query.operands.map((oprand) => this.encodeQuery(oprand, query.fieldName));\r\n                return {\r\n                    [$op]: subqueries\r\n                };\r\n            }\r\n            case logic_1.LOGIC_COMMANDS_LITERAL.NOT: {\r\n                const $op = operator_map_1.operatorToString(query.operator);\r\n                const operatorExpression = query.operands[0];\r\n                if (type_1.isRegExp(operatorExpression)) {\r\n                    return {\r\n                        [query.fieldName]: {\r\n                            [$op]: this.encodeRegExp(operatorExpression)\r\n                        }\r\n                    };\r\n                }\r\n                else {\r\n                    const subqueries = this.encodeQuery(operatorExpression)[query.fieldName];\r\n                    return {\r\n                        [query.fieldName]: {\r\n                            [$op]: subqueries\r\n                        }\r\n                    };\r\n                }\r\n            }\r\n            default: {\r\n                const $op = operator_map_1.operatorToString(query.operator);\r\n                if (query.operands.length === 1) {\r\n                    const subquery = this.encodeQuery(query.operands[0]);\r\n                    return {\r\n                        [$op]: subquery\r\n                    };\r\n                }\r\n                else {\r\n                    const subqueries = query.operands.map(this.encodeQuery.bind(this));\r\n                    return {\r\n                        [$op]: subqueries\r\n                    };\r\n                }\r\n            }\r\n        }\r\n    }\r\n    encodeQueryCommand(query) {\r\n        if (query_1.isComparisonCommand(query)) {\r\n            return this.encodeComparisonCommand(query);\r\n        }\r\n        else {\r\n            return this.encodeComparisonCommand(query);\r\n        }\r\n    }\r\n    encodeComparisonCommand(query) {\r\n        if (query.fieldName === symbol_1.SYMBOL_UNSET_FIELD_NAME) {\r\n            throw new Error('Cannot encode a comparison command with unset field name');\r\n        }\r\n        const $op = operator_map_1.operatorToString(query.operator);\r\n        switch (query.operator) {\r\n            case query_1.QUERY_COMMANDS_LITERAL.EQ:\r\n            case query_1.QUERY_COMMANDS_LITERAL.NEQ:\r\n            case query_1.QUERY_COMMANDS_LITERAL.LT:\r\n            case query_1.QUERY_COMMANDS_LITERAL.LTE:\r\n            case query_1.QUERY_COMMANDS_LITERAL.GT:\r\n            case query_1.QUERY_COMMANDS_LITERAL.GTE:\r\n            case query_1.QUERY_COMMANDS_LITERAL.ELEM_MATCH:\r\n            case query_1.QUERY_COMMANDS_LITERAL.EXISTS:\r\n            case query_1.QUERY_COMMANDS_LITERAL.SIZE:\r\n            case query_1.QUERY_COMMANDS_LITERAL.MOD: {\r\n                return {\r\n                    [query.fieldName]: {\r\n                        [$op]: common_1.encodeInternalDataType(query.operands[0])\r\n                    }\r\n                };\r\n            }\r\n            case query_1.QUERY_COMMANDS_LITERAL.IN:\r\n            case query_1.QUERY_COMMANDS_LITERAL.NIN:\r\n            case query_1.QUERY_COMMANDS_LITERAL.ALL: {\r\n                return {\r\n                    [query.fieldName]: {\r\n                        [$op]: common_1.encodeInternalDataType(query.operands)\r\n                    }\r\n                };\r\n            }\r\n            case query_1.QUERY_COMMANDS_LITERAL.GEO_NEAR: {\r\n                const options = query.operands[0];\r\n                return {\r\n                    [query.fieldName]: {\r\n                        $nearSphere: {\r\n                            $geometry: options.geometry.toJSON(),\r\n                            $maxDistance: options.maxDistance,\r\n                            $minDistance: options.minDistance\r\n                        }\r\n                    }\r\n                };\r\n            }\r\n            case query_1.QUERY_COMMANDS_LITERAL.GEO_WITHIN: {\r\n                const options = query.operands[0];\r\n                if (options.centerSphere) {\r\n                    validate_1.Validate.isCentersPhere(options.centerSphere);\r\n                    const centerSphere = options.centerSphere;\r\n                    if (centerSphere[0]._internalType === symbol_1.SYMBOL_GEO_POINT) {\r\n                        return {\r\n                            [query.fieldName]: {\r\n                                $geoWithin: {\r\n                                    $centerSphere: [centerSphere[0].toJSON().coordinates, centerSphere[1]]\r\n                                }\r\n                            }\r\n                        };\r\n                    }\r\n                    return {\r\n                        [query.fieldName]: {\r\n                            $geoWithin: {\r\n                                $centerSphere: options.centerSphere\r\n                            }\r\n                        }\r\n                    };\r\n                }\r\n                return {\r\n                    [query.fieldName]: {\r\n                        $geoWithin: {\r\n                            $geometry: options.geometry.toJSON()\r\n                        }\r\n                    }\r\n                };\r\n            }\r\n            case query_1.QUERY_COMMANDS_LITERAL.GEO_INTERSECTS: {\r\n                const options = query.operands[0];\r\n                return {\r\n                    [query.fieldName]: {\r\n                        $geoIntersects: {\r\n                            $geometry: options.geometry.toJSON()\r\n                        }\r\n                    }\r\n                };\r\n            }\r\n            default: {\r\n                return {\r\n                    [query.fieldName]: {\r\n                        [$op]: common_1.encodeInternalDataType(query.operands[0])\r\n                    }\r\n                };\r\n            }\r\n        }\r\n    }\r\n    encodeQueryObject(query) {\r\n        const flattened = common_1.flattenQueryObject(query);\r\n        for (const key in flattened) {\r\n            const val = flattened[key];\r\n            if (logic_1.isLogicCommand(val)) {\r\n                flattened[key] = val._setFieldName(key);\r\n                const condition = this.encodeLogicCommand(flattened[key]);\r\n                this.mergeConditionAfterEncode(flattened, condition, key);\r\n            }\r\n            else if (query_1.isComparisonCommand(val)) {\r\n                flattened[key] = val._setFieldName(key);\r\n                const condition = this.encodeComparisonCommand(flattened[key]);\r\n                this.mergeConditionAfterEncode(flattened, condition, key);\r\n            }\r\n            else if (common_1.isConversionRequired(val)) {\r\n                flattened[key] = common_1.encodeInternalDataType(val);\r\n            }\r\n        }\r\n        return flattened;\r\n    }\r\n    mergeConditionAfterEncode(query, condition, key) {\r\n        if (!condition[key]) {\r\n            delete query[key];\r\n        }\r\n        for (const conditionKey in condition) {\r\n            if (query[conditionKey]) {\r\n                if (type_1.isArray(query[conditionKey])) {\r\n                    query[conditionKey] = query[conditionKey].concat(condition[conditionKey]);\r\n                }\r\n                else if (type_1.isObject(query[conditionKey])) {\r\n                    if (type_1.isObject(condition[conditionKey])) {\r\n                        Object.assign(query, condition);\r\n                    }\r\n                    else {\r\n                        console.warn(`unmergable condition, query is object but condition is ${type_1.getType(condition)}, can only overwrite`, condition, key);\r\n                        query[conditionKey] = condition[conditionKey];\r\n                    }\r\n                }\r\n                else {\r\n                    console.warn(`to-merge query is of type ${type_1.getType(query)}, can only overwrite`, query, condition, key);\r\n                    query[conditionKey] = condition[conditionKey];\r\n                }\r\n            }\r\n            else {\r\n                query[conditionKey] = condition[conditionKey];\r\n            }\r\n        }\r\n    }\r\n}\r\n", "\r\nObject.defineProperty(exports, \"__esModule\", { value: true });\r\nconst index_1 = require(\"./index\");\r\nconst bson_1 = require(\"bson\");\r\nconst query_1 = require(\"./serializer/query\");\r\nconst utils_1 = require(\"./utils/utils\");\r\nconst type_1 = require(\"./utils/type\");\r\nconst validate_1 = require(\"./validate\");\r\nconst point_1 = require(\"./geo/point\");\r\nclass Aggregation {\r\n    constructor(db, collectionName, rawPipeline) {\r\n        this._stages = [];\r\n        if (db && collectionName) {\r\n            this._db = db;\r\n            this._request = new index_1.Db.reqClass(this._db.config);\r\n            this._collectionName = collectionName;\r\n            if (rawPipeline && rawPipeline.length > 0) {\r\n                rawPipeline.forEach((stage) => {\r\n                    validate_1.Validate.isValidAggregation(stage);\r\n                    const stageName = Object.keys(stage)[0];\r\n                    this._pipe(stageName, stage[stageName], true);\r\n                });\r\n            }\r\n        }\r\n    }\r\n    async end() {\r\n        if (!this._collectionName || !this._db) {\r\n            throw new Error('Aggregation pipeline cannot send request');\r\n        }\r\n        const result = await this._request.send('database.aggregateDocuments', {\r\n            collectionName: this._collectionName,\r\n            stages: this._stages\r\n        });\r\n        if (result && result.data && result.data.list) {\r\n            return {\r\n                requestId: result.requestId,\r\n                data: result.data.list.map(bson_1.EJSON.parse)\r\n            };\r\n        }\r\n        return result;\r\n    }\r\n    unwrap() {\r\n        return this._stages;\r\n    }\r\n    done() {\r\n        return this._stages.map(({ stageKey, stageValue }) => {\r\n            return {\r\n                [stageKey]: JSON.parse(stageValue)\r\n            };\r\n        });\r\n    }\r\n    _pipe(stage, param, raw = false) {\r\n        let transformParam = '';\r\n        if (type_1.getType(param) === 'object') {\r\n            transformParam = utils_1.stringifyByEJSON(param);\r\n        }\r\n        else {\r\n            transformParam = JSON.stringify(param);\r\n        }\r\n        this._stages.push({\r\n            stageKey: raw ? stage : `$${stage}`,\r\n            stageValue: transformParam\r\n        });\r\n        return this;\r\n    }\r\n    addFields(param) {\r\n        return this._pipe('addFields', param);\r\n    }\r\n    bucket(param) {\r\n        return this._pipe('bucket', param);\r\n    }\r\n    bucketAuto(param) {\r\n        return this._pipe('bucketAuto', param);\r\n    }\r\n    count(param) {\r\n        return this._pipe('count', param);\r\n    }\r\n    geoNear(param) {\r\n        if (param.query) {\r\n            param.query = query_1.QuerySerializer.encode(param.query);\r\n        }\r\n        if (param.distanceMultiplier && typeof (param.distanceMultiplier) === 'number') {\r\n            param.distanceMultiplier = param.distanceMultiplier;\r\n        }\r\n        if (param.near) {\r\n            param.near = new point_1.Point(param.near.longitude, param.near.latitude).toJSON();\r\n        }\r\n        return this._pipe('geoNear', param);\r\n    }\r\n    group(param) {\r\n        return this._pipe('group', param);\r\n    }\r\n    limit(param) {\r\n        return this._pipe('limit', param);\r\n    }\r\n    match(param) {\r\n        return this._pipe('match', query_1.QuerySerializer.encode(param));\r\n    }\r\n    project(param) {\r\n        return this._pipe('project', param);\r\n    }\r\n    lookup(param) {\r\n        return this._pipe('lookup', param);\r\n    }\r\n    replaceRoot(param) {\r\n        return this._pipe('replaceRoot', param);\r\n    }\r\n    sample(param) {\r\n        return this._pipe('sample', param);\r\n    }\r\n    skip(param) {\r\n        return this._pipe('skip', param);\r\n    }\r\n    sort(param) {\r\n        return this._pipe('sort', param);\r\n    }\r\n    sortByCount(param) {\r\n        return this._pipe('sortByCount', param);\r\n    }\r\n    unwind(param) {\r\n        return this._pipe('unwind', param);\r\n    }\r\n}\r\nexports.default = Aggregation;\r\n", "\r\nObject.defineProperty(exports, \"__esModule\", { value: true });\r\nconst query_1 = require(\"./commands/query\");\r\nconst logic_1 = require(\"./commands/logic\");\r\nconst update_1 = require(\"./commands/update\");\r\nconst type_1 = require(\"./utils/type\");\r\nconst aggregate_1 = require(\"./aggregate\");\r\nexports.Command = {\r\n    eq(val) {\r\n        return new query_1.QueryCommand(query_1.QUERY_COMMANDS_LITERAL.EQ, [val]);\r\n    },\r\n    neq(val) {\r\n        return new query_1.QueryCommand(query_1.QUERY_COMMANDS_LITERAL.NEQ, [val]);\r\n    },\r\n    lt(val) {\r\n        return new query_1.QueryCommand(query_1.QUERY_COMMANDS_LITERAL.LT, [val]);\r\n    },\r\n    lte(val) {\r\n        return new query_1.QueryCommand(query_1.QUERY_COMMANDS_LITERAL.LTE, [val]);\r\n    },\r\n    gt(val) {\r\n        return new query_1.QueryCommand(query_1.QUERY_COMMANDS_LITERAL.GT, [val]);\r\n    },\r\n    gte(val) {\r\n        return new query_1.QueryCommand(query_1.QUERY_COMMANDS_LITERAL.GTE, [val]);\r\n    },\r\n    in(val) {\r\n        return new query_1.QueryCommand(query_1.QUERY_COMMANDS_LITERAL.IN, val);\r\n    },\r\n    nin(val) {\r\n        return new query_1.QueryCommand(query_1.QUERY_COMMANDS_LITERAL.NIN, val);\r\n    },\r\n    all(val) {\r\n        return new query_1.QueryCommand(query_1.QUERY_COMMANDS_LITERAL.ALL, val);\r\n    },\r\n    elemMatch(val) {\r\n        return new query_1.QueryCommand(query_1.QUERY_COMMANDS_LITERAL.ELEM_MATCH, [val]);\r\n    },\r\n    exists(val) {\r\n        return new query_1.QueryCommand(query_1.QUERY_COMMANDS_LITERAL.EXISTS, [val]);\r\n    },\r\n    size(val) {\r\n        return new query_1.QueryCommand(query_1.QUERY_COMMANDS_LITERAL.SIZE, [val]);\r\n    },\r\n    mod() {\r\n        if (arguments.length == 1)\r\n            return new query_1.QueryCommand(query_1.QUERY_COMMANDS_LITERAL.MOD, [arguments[0]]);\r\n        if (arguments.length == 2)\r\n            return new query_1.QueryCommand(query_1.QUERY_COMMANDS_LITERAL.MOD, [[arguments[0], arguments[1]]]);\r\n        return new query_1.QueryCommand(query_1.QUERY_COMMANDS_LITERAL.MOD, arguments);\r\n    },\r\n    geoNear(val) {\r\n        return new query_1.QueryCommand(query_1.QUERY_COMMANDS_LITERAL.GEO_NEAR, [val]);\r\n    },\r\n    geoWithin(val) {\r\n        return new query_1.QueryCommand(query_1.QUERY_COMMANDS_LITERAL.GEO_WITHIN, [val]);\r\n    },\r\n    geoIntersects(val) {\r\n        return new query_1.QueryCommand(query_1.QUERY_COMMANDS_LITERAL.GEO_INTERSECTS, [val]);\r\n    },\r\n    and(...__expressions__) {\r\n        const expressions = type_1.isArray(arguments[0]) ? arguments[0] : Array.from(arguments);\r\n        return new logic_1.LogicCommand(logic_1.LOGIC_COMMANDS_LITERAL.AND, expressions);\r\n    },\r\n    nor(...__expressions__) {\r\n        const expressions = type_1.isArray(arguments[0]) ? arguments[0] : Array.from(arguments);\r\n        return new logic_1.LogicCommand(logic_1.LOGIC_COMMANDS_LITERAL.NOR, expressions);\r\n    },\r\n    or(...__expressions__) {\r\n        const expressions = type_1.isArray(arguments[0]) ? arguments[0] : Array.from(arguments);\r\n        return new logic_1.LogicCommand(logic_1.LOGIC_COMMANDS_LITERAL.OR, expressions);\r\n    },\r\n    not(...__expressions__) {\r\n        const expressions = type_1.isArray(arguments[0]) ? arguments[0] : Array.from(arguments);\r\n        return new logic_1.LogicCommand(logic_1.LOGIC_COMMANDS_LITERAL.NOT, expressions);\r\n    },\r\n    set(val) {\r\n        return new update_1.UpdateCommand(update_1.UPDATE_COMMANDS_LITERAL.SET, [val]);\r\n    },\r\n    remove() {\r\n        return new update_1.UpdateCommand(update_1.UPDATE_COMMANDS_LITERAL.REMOVE, []);\r\n    },\r\n    inc(val) {\r\n        return new update_1.UpdateCommand(update_1.UPDATE_COMMANDS_LITERAL.INC, [val]);\r\n    },\r\n    mul(val) {\r\n        return new update_1.UpdateCommand(update_1.UPDATE_COMMANDS_LITERAL.MUL, [val]);\r\n    },\r\n    push(...args) {\r\n        let values;\r\n        if (type_1.isObject(args[0]) && args[0].hasOwnProperty('each')) {\r\n            values = {};\r\n            const options = args[0];\r\n            if (options.each !== undefined) {\r\n                values['$each'] = options.each;\r\n            }\r\n            if (options.position !== undefined) {\r\n                values['$position'] = options.position;\r\n            }\r\n            if (options.sort !== undefined) {\r\n                values['$sort'] = options.sort;\r\n            }\r\n            if (options.slice !== undefined) {\r\n                values['$slice'] = options.slice;\r\n            }\r\n        }\r\n        else if (type_1.isArray(args[0])) {\r\n            values = args[0];\r\n        }\r\n        else {\r\n            values = Array.from(args);\r\n        }\r\n        return new update_1.UpdateCommand(update_1.UPDATE_COMMANDS_LITERAL.PUSH, values);\r\n    },\r\n    pull(values) {\r\n        return new update_1.UpdateCommand(update_1.UPDATE_COMMANDS_LITERAL.PULL, values);\r\n    },\r\n    pullAll(values) {\r\n        return new update_1.UpdateCommand(update_1.UPDATE_COMMANDS_LITERAL.PULL_ALL, values);\r\n    },\r\n    pop() {\r\n        return new update_1.UpdateCommand(update_1.UPDATE_COMMANDS_LITERAL.POP, []);\r\n    },\r\n    shift() {\r\n        return new update_1.UpdateCommand(update_1.UPDATE_COMMANDS_LITERAL.SHIFT, []);\r\n    },\r\n    unshift(...__values__) {\r\n        const values = type_1.isArray(arguments[0]) ? arguments[0] : Array.from(arguments);\r\n        return new update_1.UpdateCommand(update_1.UPDATE_COMMANDS_LITERAL.UNSHIFT, values);\r\n    },\r\n    addToSet(values) {\r\n        return new update_1.UpdateCommand(update_1.UPDATE_COMMANDS_LITERAL.ADD_TO_SET, values);\r\n    },\r\n    rename(values) {\r\n        return new update_1.UpdateCommand(update_1.UPDATE_COMMANDS_LITERAL.RENAME, [values]);\r\n    },\r\n    bit(values) {\r\n        return new update_1.UpdateCommand(update_1.UPDATE_COMMANDS_LITERAL.BIT, [values]);\r\n    },\r\n    max(values) {\r\n        return new update_1.UpdateCommand(update_1.UPDATE_COMMANDS_LITERAL.MAX, [values]);\r\n    },\r\n    min(values) {\r\n        return new update_1.UpdateCommand(update_1.UPDATE_COMMANDS_LITERAL.MIN, [values]);\r\n    },\r\n    expr(values) {\r\n        return {\r\n            $expr: values\r\n        };\r\n    },\r\n    jsonSchema(schema) {\r\n        return {\r\n            $jsonSchema: schema\r\n        };\r\n    },\r\n    text(values) {\r\n        if (type_1.isString(values)) {\r\n            return {\r\n                $search: values.search\r\n            };\r\n        }\r\n        else {\r\n            return {\r\n                $search: values.search,\r\n                $language: values.language,\r\n                $caseSensitive: values.caseSensitive,\r\n                $diacriticSensitive: values.diacriticSensitive\r\n            };\r\n        }\r\n    },\r\n    aggregate: {\r\n        pipeline() {\r\n            return new aggregate_1.default();\r\n        },\r\n        abs: param => new AggregationOperator('abs', param),\r\n        add: param => new AggregationOperator('add', param),\r\n        ceil: param => new AggregationOperator('ceil', param),\r\n        divide: param => new AggregationOperator('divide', param),\r\n        exp: param => new AggregationOperator('exp', param),\r\n        floor: param => new AggregationOperator('floor', param),\r\n        ln: param => new AggregationOperator('ln', param),\r\n        log: param => new AggregationOperator('log', param),\r\n        log10: param => new AggregationOperator('log10', param),\r\n        mod: param => new AggregationOperator('mod', param),\r\n        multiply: param => new AggregationOperator('multiply', param),\r\n        pow: param => new AggregationOperator('pow', param),\r\n        sqrt: param => new AggregationOperator('sqrt', param),\r\n        subtract: param => new AggregationOperator('subtract', param),\r\n        trunc: param => new AggregationOperator('trunc', param),\r\n        arrayElemAt: param => new AggregationOperator('arrayElemAt', param),\r\n        arrayToObject: param => new AggregationOperator('arrayToObject', param),\r\n        concatArrays: param => new AggregationOperator('concatArrays', param),\r\n        filter: param => new AggregationOperator('filter', param),\r\n        in: param => new AggregationOperator('in', param),\r\n        indexOfArray: param => new AggregationOperator('indexOfArray', param),\r\n        isArray: param => new AggregationOperator('isArray', param),\r\n        map: param => new AggregationOperator('map', param),\r\n        range: param => new AggregationOperator('range', param),\r\n        reduce: param => new AggregationOperator('reduce', param),\r\n        reverseArray: param => new AggregationOperator('reverseArray', param),\r\n        size: param => new AggregationOperator('size', param),\r\n        slice: param => new AggregationOperator('slice', param),\r\n        zip: param => new AggregationOperator('zip', param),\r\n        and: param => new AggregationOperator('and', param),\r\n        not: param => new AggregationOperator('not', param),\r\n        or: param => new AggregationOperator('or', param),\r\n        cmp: param => new AggregationOperator('cmp', param),\r\n        eq: param => new AggregationOperator('eq', param),\r\n        gt: param => new AggregationOperator('gt', param),\r\n        gte: param => new AggregationOperator('gte', param),\r\n        lt: param => new AggregationOperator('lt', param),\r\n        lte: param => new AggregationOperator('lte', param),\r\n        neq: param => new AggregationOperator('ne', param),\r\n        cond: param => new AggregationOperator('cond', param),\r\n        ifNull: param => new AggregationOperator('ifNull', param),\r\n        switch: param => new AggregationOperator('switch', param),\r\n        dateFromParts: param => new AggregationOperator('dateFromParts', param),\r\n        dateFromString: param => new AggregationOperator('dateFromString', param),\r\n        dayOfMonth: param => new AggregationOperator('dayOfMonth', param),\r\n        dayOfWeek: param => new AggregationOperator('dayOfWeek', param),\r\n        dayOfYear: param => new AggregationOperator('dayOfYear', param),\r\n        isoDayOfWeek: param => new AggregationOperator('isoDayOfWeek', param),\r\n        isoWeek: param => new AggregationOperator('isoWeek', param),\r\n        isoWeekYear: param => new AggregationOperator('isoWeekYear', param),\r\n        millisecond: param => new AggregationOperator('millisecond', param),\r\n        minute: param => new AggregationOperator('minute', param),\r\n        month: param => new AggregationOperator('month', param),\r\n        second: param => new AggregationOperator('second', param),\r\n        hour: param => new AggregationOperator('hour', param),\r\n        week: param => new AggregationOperator('week', param),\r\n        year: param => new AggregationOperator('year', param),\r\n        literal: param => new AggregationOperator('literal', param),\r\n        mergeObjects: param => new AggregationOperator('mergeObjects', param),\r\n        objectToArray: param => new AggregationOperator('objectToArray', param),\r\n        allElementsTrue: param => new AggregationOperator('allElementsTrue', param),\r\n        anyElementTrue: param => new AggregationOperator('anyElementTrue', param),\r\n        setDifference: param => new AggregationOperator('setDifference', param),\r\n        setEquals: param => new AggregationOperator('setEquals', param),\r\n        setIntersection: param => new AggregationOperator('setIntersection', param),\r\n        setIsSubset: param => new AggregationOperator('setIsSubset', param),\r\n        setUnion: param => new AggregationOperator('setUnion', param),\r\n        concat: param => new AggregationOperator('concat', param),\r\n        dateToString: param => new AggregationOperator('dateToString', param),\r\n        indexOfBytes: param => new AggregationOperator('indexOfBytes', param),\r\n        indexOfCP: param => new AggregationOperator('indexOfCP', param),\r\n        split: param => new AggregationOperator('split', param),\r\n        strLenBytes: param => new AggregationOperator('strLenBytes', param),\r\n        strLenCP: param => new AggregationOperator('strLenCP', param),\r\n        strcasecmp: param => new AggregationOperator('strcasecmp', param),\r\n        substr: param => new AggregationOperator('substr', param),\r\n        substrBytes: param => new AggregationOperator('substrBytes', param),\r\n        substrCP: param => new AggregationOperator('substrCP', param),\r\n        toLower: param => new AggregationOperator('toLower', param),\r\n        toUpper: param => new AggregationOperator('toUpper', param),\r\n        meta: param => new AggregationOperator('meta', param),\r\n        addToSet: param => new AggregationOperator('addToSet', param),\r\n        avg: param => new AggregationOperator('avg', param),\r\n        first: param => new AggregationOperator('first', param),\r\n        last: param => new AggregationOperator('last', param),\r\n        max: param => new AggregationOperator('max', param),\r\n        min: param => new AggregationOperator('min', param),\r\n        push: param => new AggregationOperator('push', param),\r\n        stdDevPop: param => new AggregationOperator('stdDevPop', param),\r\n        stdDevSamp: param => new AggregationOperator('stdDevSamp', param),\r\n        sum: param => new AggregationOperator('sum', param),\r\n        let: param => new AggregationOperator('let', param)\r\n    },\r\n    project: {\r\n        slice: param => new ProjectionOperator('slice', param),\r\n        elemMatch: param => new ProjectionOperator('elemMatch', param)\r\n    }\r\n};\r\nclass AggregationOperator {\r\n    constructor(name, param) {\r\n        this['$' + name] = param;\r\n    }\r\n}\r\nexports.AggregationOperator = AggregationOperator;\r\nclass ProjectionOperator {\r\n    constructor(name, param) {\r\n        this['$' + name] = param;\r\n    }\r\n}\r\nexports.ProjectionOperator = ProjectionOperator;\r\nexports.default = exports.Command;\r\n", "\r\nObject.defineProperty(exports, \"__esModule\", { value: true });\r\nconst symbol_1 = require(\"../helper/symbol\");\r\nclass RegExp {\r\n    constructor({ regexp, options }) {\r\n        if (!regexp) {\r\n            throw new TypeError('regexp must be a string');\r\n        }\r\n        this.$regularExpression = {\r\n            pattern: regexp || '',\r\n            options: options || ''\r\n        };\r\n    }\r\n    parse() {\r\n        return {\r\n            $regularExpression: {\r\n                pattern: this.$regularExpression.pattern,\r\n                options: this.$regularExpression.options\r\n            }\r\n        };\r\n    }\r\n    get _internalType() {\r\n        return symbol_1.SYMBOL_REGEXP;\r\n    }\r\n}\r\nexports.RegExp = RegExp;\r\nfunction RegExpConstructor(param) {\r\n    return new RegExp(param);\r\n}\r\nexports.RegExpConstructor = RegExpConstructor;\r\n", "\r\nObject.defineProperty(exports, \"__esModule\", { value: true });\r\nconst index_1 = require(\"../index\");\r\nconst collection_1 = require(\"../collection\");\r\nconst code_1 = require(\"../const/code\");\r\nconst START = 'database.startTransaction';\r\nconst COMMIT = 'database.commitTransaction';\r\nconst ABORT = 'database.abortTransaction';\r\nclass Transaction {\r\n    constructor(db) {\r\n        this._db = db;\r\n        this._request = new index_1.Db.reqClass(this._db.config);\r\n        this.aborted = false;\r\n        this.commited = false;\r\n        this.inited = false;\r\n    }\r\n    async init() {\r\n        const res = await this._request.send(START);\r\n        if (res.code) {\r\n            throw res;\r\n        }\r\n        this.inited = true;\r\n        this._id = res.transactionId;\r\n    }\r\n    collection(collName) {\r\n        if (!collName) {\r\n            throw new Error('Collection name is required');\r\n        }\r\n        return new collection_1.CollectionReference(this._db, collName, {}, this._id);\r\n    }\r\n    getTransactionId() {\r\n        return this._id;\r\n    }\r\n    getRequestMethod() {\r\n        return this._request;\r\n    }\r\n    async commit() {\r\n        const param = {\r\n            transactionId: this._id\r\n        };\r\n        const res = await this._request.send(COMMIT, param);\r\n        if (res.code)\r\n            throw res;\r\n        this.commited = true;\r\n        return res;\r\n    }\r\n    async rollback(customRollbackRes) {\r\n        const param = {\r\n            transactionId: this._id\r\n        };\r\n        const res = await this._request.send(ABORT, param);\r\n        if (res.code)\r\n            throw res;\r\n        this.aborted = true;\r\n        this.abortReason = customRollbackRes;\r\n        return res;\r\n    }\r\n}\r\nexports.Transaction = Transaction;\r\nasync function startTransaction() {\r\n    const transaction = new Transaction(this);\r\n    await transaction.init();\r\n    return transaction;\r\n}\r\nexports.startTransaction = startTransaction;\r\nasync function runTransaction(callback, times = 3) {\r\n    let transaction;\r\n    try {\r\n        transaction = new Transaction(this);\r\n        await transaction.init();\r\n        const callbackRes = await callback(transaction);\r\n        if (transaction.aborted === true) {\r\n            throw transaction.abortReason;\r\n        }\r\n        await transaction.commit();\r\n        return callbackRes;\r\n    }\r\n    catch (error) {\r\n        if (transaction.inited === false) {\r\n            throw error;\r\n        }\r\n        const throwWithRollback = async (error) => {\r\n            if (!transaction.aborted && !transaction.commited) {\r\n                try {\r\n                    await transaction.rollback();\r\n                }\r\n                catch (err) {\r\n                }\r\n                throw error;\r\n            }\r\n            if (transaction.aborted === true) {\r\n                throw transaction.abortReason;\r\n            }\r\n            throw error;\r\n        };\r\n        if (times <= 0) {\r\n            await throwWithRollback(error);\r\n        }\r\n        if (error && error.code === code_1.ERRORS.DATABASE_TRANSACTION_CONFLICT.code) {\r\n            return await runTransaction.bind(this)(callback, --times);\r\n        }\r\n        await throwWithRollback(error);\r\n    }\r\n}\r\nexports.runTransaction = runTransaction;\r\n", "\r\nObject.defineProperty(exports, \"__esModule\", { value: true });\r\nconst symbol_1 = require(\"../helper/symbol\");\r\nclass ObjectId {\r\n    constructor({ id = '' } = {}) {\r\n        this.id = id;\r\n    }\r\n    get _internalType() {\r\n        return symbol_1.SYMBOL_OBJECTID;\r\n    }\r\n    parse() {\r\n        return {\r\n            $oid: this.id\r\n        };\r\n    }\r\n}\r\nexports.ObjectId = ObjectId;\r\nfunction ObjectIdConstructor(opt) {\r\n    return new ObjectId(opt);\r\n}\r\nexports.ObjectIdConstructor = ObjectIdConstructor;\r\n"]}