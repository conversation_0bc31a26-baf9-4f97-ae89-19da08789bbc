# 视频流接收、录制与分析功能说明

本文档介绍了如何使用视频流接收、录制和分析功能，包括实时视频录制和自动分析的完整流程。

## 功能概述

系统支持两种视频获取和分析方式：
1. **本地视频上传**：上传本地设备中已有的视频文件进行分析
2. **视频流接收与录制**：接收实时视频流，录制所需片段，并进行分析

## 使用流程

### 1. 视频流接收

点击主界面上的【开始接收视频】按钮，系统将连接默认设备的视频流。视频流连接成功后，屏幕上会显示实时视频内容。

视频流支持以下格式：
- HLS（HTTP Live Streaming）
- MP4（视频文件流）

### 2. 视频录制

当成功接收视频流后，可以进行视频录制操作：

1. 点击【开始录制】按钮开始录制视频
2. 录制过程中，按钮会变为【停止录制】状态
3. 需要停止录制时，再次点击该按钮

录制完成后，系统会提供两个选项：
- **立即分析**：上传录制的视频并立即进行分析
- **仅保存**：将录制的视频保存到相册中，稍后可以通过本地上传功能分析

### 3. 视频分析

无论是从视频流录制还是本地上传的视频，分析步骤基本相同：

1. 视频上传到云端
2. 系统创建分析任务
3. 后台进行视频处理和分析
4. 前端显示分析进度
5. 分析完成后显示结果

分析过程中可以调整的参数包括：
- 亮度（Brightness）
- 对比度（Contrast）
- 饱和度（Saturation）
- 锐度（Sharpness）
- 伽马值（Gamma）
- 白平衡（White Balance）
- 背光（Backlight）
- 曝光（Exposure）

### 4. 查看分析结果

分析完成后，系统会自动显示分析结果，包括：
- C区图像
- T区图像
- TC值（T/C比值）
- 浓度值（Concentration）

## 注意事项

1. 视频流接收需要稳定的网络连接
2. 视频录制时间不宜过长，建议控制在10秒到60秒之间
3. 录制的视频会临时保存在设备上，分析完成后可以选择是否删除
4. 如果分析过程中断，系统会提供继续分析的选项

## 常见问题

**Q: 为什么视频流无法连接？**  
A: 请确保设备已正确连接到网络，并且设备ID正确。默认设备是"default_device"。

**Q: 录制的视频如何与已上传的视频区分？**  
A: 录制的视频会保存在"recorded_videos"目录下，文件名包含录制时间戳。

**Q: 分析过程需要多长时间？**  
A: 分析时间取决于视频长度和复杂度，通常10秒的视频需要20-60秒完成分析。

**Q: 如何取消正在进行的分析？**  
A: 在分析进度页面点击"取消分析"按钮，或者在超时提示中选择"停止分析"。

## 技术说明

本功能通过以下云函数实现：
- `startVideoStream`：启动并接收视频流
- `analyzeVideo`：处理和分析视频内容

前端使用微信小程序的媒体录制API（`wx.createMediaRecorder`）实现视频录制功能。 