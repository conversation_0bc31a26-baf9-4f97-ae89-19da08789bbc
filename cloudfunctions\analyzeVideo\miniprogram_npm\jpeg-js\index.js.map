{"version": 3, "sources": ["index.js", "lib/encoder.js", "lib/decoder.js"], "names": [], "mappings": ";;;;;;;AAAA;AACA;AACA;AACA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,AENA,ADGA;ADIA,AENA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.js", "sourcesContent": ["var encode = require('./lib/encoder'),\n    decode = require('./lib/decoder');\n\nmodule.exports = {\n  encode: encode,\n  decode: decode\n};\n", "/*\n  Copyright (c) 2008, Adobe Systems Incorporated\n  All rights reserved.\n\n  Redistribution and use in source and binary forms, with or without \n  modification, are permitted provided that the following conditions are\n  met:\n\n  * Redistributions of source code must retain the above copyright notice, \n    this list of conditions and the following disclaimer.\n  \n  * Redistributions in binary form must reproduce the above copyright\n    notice, this list of conditions and the following disclaimer in the \n    documentation and/or other materials provided with the distribution.\n  \n  * Neither the name of Adobe Systems Incorporated nor the names of its \n    contributors may be used to endorse or promote products derived from \n    this software without specific prior written permission.\n\n  THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS \"AS\n  IS\" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO,\n  THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR\n  PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT OWNER OR \n  CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL,\n  EXEMPLARY, OR <PERSON><PERSON>EQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO,\n  PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR\n  PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF\n  LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING\n  NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS\n  SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.\n*/\n/*\nJPEG encoder ported to JavaScript and optimized by Andreas Ritter, www.bytestrom.eu, 11/2009\n\nBasic GUI blocking jpeg encoder\n*/\n\nvar btoa = btoa || function(buf) {\n  return Buffer.from(buf).toString('base64');\n};\n\nfunction JPEGEncoder(quality) {\n  var self = this;\n\tvar fround = Math.round;\n\tvar ffloor = Math.floor;\n\tvar YTable = new Array(64);\n\tvar UVTable = new Array(64);\n\tvar fdtbl_Y = new Array(64);\n\tvar fdtbl_UV = new Array(64);\n\tvar YDC_HT;\n\tvar UVDC_HT;\n\tvar YAC_HT;\n\tvar UVAC_HT;\n\t\n\tvar bitcode = new Array(65535);\n\tvar category = new Array(65535);\n\tvar outputfDCTQuant = new Array(64);\n\tvar DU = new Array(64);\n\tvar byteout = [];\n\tvar bytenew = 0;\n\tvar bytepos = 7;\n\t\n\tvar YDU = new Array(64);\n\tvar UDU = new Array(64);\n\tvar VDU = new Array(64);\n\tvar clt = new Array(256);\n\tvar RGB_YUV_TABLE = new Array(2048);\n\tvar currentQuality;\n\t\n\tvar ZigZag = [\n\t\t\t 0, 1, 5, 6,14,15,27,28,\n\t\t\t 2, 4, 7,13,16,26,29,42,\n\t\t\t 3, 8,12,17,25,30,41,43,\n\t\t\t 9,11,18,24,31,40,44,53,\n\t\t\t10,19,23,32,39,45,52,54,\n\t\t\t20,22,33,38,46,51,55,60,\n\t\t\t21,34,37,47,50,56,59,61,\n\t\t\t35,36,48,49,57,58,62,63\n\t\t];\n\t\n\tvar std_dc_luminance_nrcodes = [0,0,1,5,1,1,1,1,1,1,0,0,0,0,0,0,0];\n\tvar std_dc_luminance_values = [0,1,2,3,4,5,6,7,8,9,10,11];\n\tvar std_ac_luminance_nrcodes = [0,0,2,1,3,3,2,4,3,5,5,4,4,0,0,1,0x7d];\n\tvar std_ac_luminance_values = [\n\t\t\t0x01,0x02,0x03,0x00,0x04,0x11,0x05,0x12,\n\t\t\t0x21,0x31,0x41,0x06,0x13,0x51,0x61,0x07,\n\t\t\t0x22,0x71,0x14,0x32,0x81,0x91,0xa1,0x08,\n\t\t\t0x23,0x42,0xb1,0xc1,0x15,0x52,0xd1,0xf0,\n\t\t\t0x24,0x33,0x62,0x72,0x82,0x09,0x0a,0x16,\n\t\t\t0x17,0x18,0x19,0x1a,0x25,0x26,0x27,0x28,\n\t\t\t0x29,0x2a,0x34,0x35,0x36,0x37,0x38,0x39,\n\t\t\t0x3a,0x43,0x44,0x45,0x46,0x47,0x48,0x49,\n\t\t\t0x4a,0x53,0x54,0x55,0x56,0x57,0x58,0x59,\n\t\t\t0x5a,0x63,0x64,0x65,0x66,0x67,0x68,0x69,\n\t\t\t0x6a,0x73,0x74,0x75,0x76,0x77,0x78,0x79,\n\t\t\t0x7a,0x83,0x84,0x85,0x86,0x87,0x88,0x89,\n\t\t\t0x8a,0x92,0x93,0x94,0x95,0x96,0x97,0x98,\n\t\t\t0x99,0x9a,0xa2,0xa3,0xa4,0xa5,0xa6,0xa7,\n\t\t\t0xa8,0xa9,0xaa,0xb2,0xb3,0xb4,0xb5,0xb6,\n\t\t\t0xb7,0xb8,0xb9,0xba,0xc2,0xc3,0xc4,0xc5,\n\t\t\t0xc6,0xc7,0xc8,0xc9,0xca,0xd2,0xd3,0xd4,\n\t\t\t0xd5,0xd6,0xd7,0xd8,0xd9,0xda,0xe1,0xe2,\n\t\t\t0xe3,0xe4,0xe5,0xe6,0xe7,0xe8,0xe9,0xea,\n\t\t\t0xf1,0xf2,0xf3,0xf4,0xf5,0xf6,0xf7,0xf8,\n\t\t\t0xf9,0xfa\n\t\t];\n\t\n\tvar std_dc_chrominance_nrcodes = [0,0,3,1,1,1,1,1,1,1,1,1,0,0,0,0,0];\n\tvar std_dc_chrominance_values = [0,1,2,3,4,5,6,7,8,9,10,11];\n\tvar std_ac_chrominance_nrcodes = [0,0,2,1,2,4,4,3,4,7,5,4,4,0,1,2,0x77];\n\tvar std_ac_chrominance_values = [\n\t\t\t0x00,0x01,0x02,0x03,0x11,0x04,0x05,0x21,\n\t\t\t0x31,0x06,0x12,0x41,0x51,0x07,0x61,0x71,\n\t\t\t0x13,0x22,0x32,0x81,0x08,0x14,0x42,0x91,\n\t\t\t0xa1,0xb1,0xc1,0x09,0x23,0x33,0x52,0xf0,\n\t\t\t0x15,0x62,0x72,0xd1,0x0a,0x16,0x24,0x34,\n\t\t\t0xe1,0x25,0xf1,0x17,0x18,0x19,0x1a,0x26,\n\t\t\t0x27,0x28,0x29,0x2a,0x35,0x36,0x37,0x38,\n\t\t\t0x39,0x3a,0x43,0x44,0x45,0x46,0x47,0x48,\n\t\t\t0x49,0x4a,0x53,0x54,0x55,0x56,0x57,0x58,\n\t\t\t0x59,0x5a,0x63,0x64,0x65,0x66,0x67,0x68,\n\t\t\t0x69,0x6a,0x73,0x74,0x75,0x76,0x77,0x78,\n\t\t\t0x79,0x7a,0x82,0x83,0x84,0x85,0x86,0x87,\n\t\t\t0x88,0x89,0x8a,0x92,0x93,0x94,0x95,0x96,\n\t\t\t0x97,0x98,0x99,0x9a,0xa2,0xa3,0xa4,0xa5,\n\t\t\t0xa6,0xa7,0xa8,0xa9,0xaa,0xb2,0xb3,0xb4,\n\t\t\t0xb5,0xb6,0xb7,0xb8,0xb9,0xba,0xc2,0xc3,\n\t\t\t0xc4,0xc5,0xc6,0xc7,0xc8,0xc9,0xca,0xd2,\n\t\t\t0xd3,0xd4,0xd5,0xd6,0xd7,0xd8,0xd9,0xda,\n\t\t\t0xe2,0xe3,0xe4,0xe5,0xe6,0xe7,0xe8,0xe9,\n\t\t\t0xea,0xf2,0xf3,0xf4,0xf5,0xf6,0xf7,0xf8,\n\t\t\t0xf9,0xfa\n\t\t];\n\t\n\tfunction initQuantTables(sf){\n\t\t\tvar YQT = [\n\t\t\t\t16, 11, 10, 16, 24, 40, 51, 61,\n\t\t\t\t12, 12, 14, 19, 26, 58, 60, 55,\n\t\t\t\t14, 13, 16, 24, 40, 57, 69, 56,\n\t\t\t\t14, 17, 22, 29, 51, 87, 80, 62,\n\t\t\t\t18, 22, 37, 56, 68,109,103, 77,\n\t\t\t\t24, 35, 55, 64, 81,104,113, 92,\n\t\t\t\t49, 64, 78, 87,103,121,120,101,\n\t\t\t\t72, 92, 95, 98,112,100,103, 99\n\t\t\t];\n\t\t\t\n\t\t\tfor (var i = 0; i < 64; i++) {\n\t\t\t\tvar t = ffloor((YQT[i]*sf+50)/100);\n\t\t\t\tif (t < 1) {\n\t\t\t\t\tt = 1;\n\t\t\t\t} else if (t > 255) {\n\t\t\t\t\tt = 255;\n\t\t\t\t}\n\t\t\t\tYTable[ZigZag[i]] = t;\n\t\t\t}\n\t\t\tvar UVQT = [\n\t\t\t\t17, 18, 24, 47, 99, 99, 99, 99,\n\t\t\t\t18, 21, 26, 66, 99, 99, 99, 99,\n\t\t\t\t24, 26, 56, 99, 99, 99, 99, 99,\n\t\t\t\t47, 66, 99, 99, 99, 99, 99, 99,\n\t\t\t\t99, 99, 99, 99, 99, 99, 99, 99,\n\t\t\t\t99, 99, 99, 99, 99, 99, 99, 99,\n\t\t\t\t99, 99, 99, 99, 99, 99, 99, 99,\n\t\t\t\t99, 99, 99, 99, 99, 99, 99, 99\n\t\t\t];\n\t\t\tfor (var j = 0; j < 64; j++) {\n\t\t\t\tvar u = ffloor((UVQT[j]*sf+50)/100);\n\t\t\t\tif (u < 1) {\n\t\t\t\t\tu = 1;\n\t\t\t\t} else if (u > 255) {\n\t\t\t\t\tu = 255;\n\t\t\t\t}\n\t\t\t\tUVTable[ZigZag[j]] = u;\n\t\t\t}\n\t\t\tvar aasf = [\n\t\t\t\t1.0, 1.387039845, 1.306562965, 1.175875602,\n\t\t\t\t1.0, 0.785694958, 0.541196100, 0.275899379\n\t\t\t];\n\t\t\tvar k = 0;\n\t\t\tfor (var row = 0; row < 8; row++)\n\t\t\t{\n\t\t\t\tfor (var col = 0; col < 8; col++)\n\t\t\t\t{\n\t\t\t\t\tfdtbl_Y[k]  = (1.0 / (YTable [ZigZag[k]] * aasf[row] * aasf[col] * 8.0));\n\t\t\t\t\tfdtbl_UV[k] = (1.0 / (UVTable[ZigZag[k]] * aasf[row] * aasf[col] * 8.0));\n\t\t\t\t\tk++;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t\t\n\t\tfunction computeHuffmanTbl(nrcodes, std_table){\n\t\t\tvar codevalue = 0;\n\t\t\tvar pos_in_table = 0;\n\t\t\tvar HT = new Array();\n\t\t\tfor (var k = 1; k <= 16; k++) {\n\t\t\t\tfor (var j = 1; j <= nrcodes[k]; j++) {\n\t\t\t\t\tHT[std_table[pos_in_table]] = [];\n\t\t\t\t\tHT[std_table[pos_in_table]][0] = codevalue;\n\t\t\t\t\tHT[std_table[pos_in_table]][1] = k;\n\t\t\t\t\tpos_in_table++;\n\t\t\t\t\tcodevalue++;\n\t\t\t\t}\n\t\t\t\tcodevalue*=2;\n\t\t\t}\n\t\t\treturn HT;\n\t\t}\n\t\t\n\t\tfunction initHuffmanTbl()\n\t\t{\n\t\t\tYDC_HT = computeHuffmanTbl(std_dc_luminance_nrcodes,std_dc_luminance_values);\n\t\t\tUVDC_HT = computeHuffmanTbl(std_dc_chrominance_nrcodes,std_dc_chrominance_values);\n\t\t\tYAC_HT = computeHuffmanTbl(std_ac_luminance_nrcodes,std_ac_luminance_values);\n\t\t\tUVAC_HT = computeHuffmanTbl(std_ac_chrominance_nrcodes,std_ac_chrominance_values);\n\t\t}\n\t\n\t\tfunction initCategoryNumber()\n\t\t{\n\t\t\tvar nrlower = 1;\n\t\t\tvar nrupper = 2;\n\t\t\tfor (var cat = 1; cat <= 15; cat++) {\n\t\t\t\t//Positive numbers\n\t\t\t\tfor (var nr = nrlower; nr<nrupper; nr++) {\n\t\t\t\t\tcategory[32767+nr] = cat;\n\t\t\t\t\tbitcode[32767+nr] = [];\n\t\t\t\t\tbitcode[32767+nr][1] = cat;\n\t\t\t\t\tbitcode[32767+nr][0] = nr;\n\t\t\t\t}\n\t\t\t\t//Negative numbers\n\t\t\t\tfor (var nrneg =-(nrupper-1); nrneg<=-nrlower; nrneg++) {\n\t\t\t\t\tcategory[32767+nrneg] = cat;\n\t\t\t\t\tbitcode[32767+nrneg] = [];\n\t\t\t\t\tbitcode[32767+nrneg][1] = cat;\n\t\t\t\t\tbitcode[32767+nrneg][0] = nrupper-1+nrneg;\n\t\t\t\t}\n\t\t\t\tnrlower <<= 1;\n\t\t\t\tnrupper <<= 1;\n\t\t\t}\n\t\t}\n\t\t\n\t\tfunction initRGBYUVTable() {\n\t\t\tfor(var i = 0; i < 256;i++) {\n\t\t\t\tRGB_YUV_TABLE[i]      \t\t=  19595 * i;\n\t\t\t\tRGB_YUV_TABLE[(i+ 256)>>0] \t=  38470 * i;\n\t\t\t\tRGB_YUV_TABLE[(i+ 512)>>0] \t=   7471 * i + 0x8000;\n\t\t\t\tRGB_YUV_TABLE[(i+ 768)>>0] \t= -11059 * i;\n\t\t\t\tRGB_YUV_TABLE[(i+1024)>>0] \t= -21709 * i;\n\t\t\t\tRGB_YUV_TABLE[(i+1280)>>0] \t=  32768 * i + 0x807FFF;\n\t\t\t\tRGB_YUV_TABLE[(i+1536)>>0] \t= -27439 * i;\n\t\t\t\tRGB_YUV_TABLE[(i+1792)>>0] \t= - 5329 * i;\n\t\t\t}\n\t\t}\n\t\t\n\t\t// IO functions\n\t\tfunction writeBits(bs)\n\t\t{\n\t\t\tvar value = bs[0];\n\t\t\tvar posval = bs[1]-1;\n\t\t\twhile ( posval >= 0 ) {\n\t\t\t\tif (value & (1 << posval) ) {\n\t\t\t\t\tbytenew |= (1 << bytepos);\n\t\t\t\t}\n\t\t\t\tposval--;\n\t\t\t\tbytepos--;\n\t\t\t\tif (bytepos < 0) {\n\t\t\t\t\tif (bytenew == 0xFF) {\n\t\t\t\t\t\twriteByte(0xFF);\n\t\t\t\t\t\twriteByte(0);\n\t\t\t\t\t}\n\t\t\t\t\telse {\n\t\t\t\t\t\twriteByte(bytenew);\n\t\t\t\t\t}\n\t\t\t\t\tbytepos=7;\n\t\t\t\t\tbytenew=0;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t\n\t\tfunction writeByte(value)\n\t\t{\n\t\t\t//byteout.push(clt[value]); // write char directly instead of converting later\n      byteout.push(value);\n\t\t}\n\t\n\t\tfunction writeWord(value)\n\t\t{\n\t\t\twriteByte((value>>8)&0xFF);\n\t\t\twriteByte((value   )&0xFF);\n\t\t}\n\t\t\n\t\t// DCT & quantization core\n\t\tfunction fDCTQuant(data, fdtbl)\n\t\t{\n\t\t\tvar d0, d1, d2, d3, d4, d5, d6, d7;\n\t\t\t/* Pass 1: process rows. */\n\t\t\tvar dataOff=0;\n\t\t\tvar i;\n\t\t\tvar I8 = 8;\n\t\t\tvar I64 = 64;\n\t\t\tfor (i=0; i<I8; ++i)\n\t\t\t{\n\t\t\t\td0 = data[dataOff];\n\t\t\t\td1 = data[dataOff+1];\n\t\t\t\td2 = data[dataOff+2];\n\t\t\t\td3 = data[dataOff+3];\n\t\t\t\td4 = data[dataOff+4];\n\t\t\t\td5 = data[dataOff+5];\n\t\t\t\td6 = data[dataOff+6];\n\t\t\t\td7 = data[dataOff+7];\n\t\t\t\t\n\t\t\t\tvar tmp0 = d0 + d7;\n\t\t\t\tvar tmp7 = d0 - d7;\n\t\t\t\tvar tmp1 = d1 + d6;\n\t\t\t\tvar tmp6 = d1 - d6;\n\t\t\t\tvar tmp2 = d2 + d5;\n\t\t\t\tvar tmp5 = d2 - d5;\n\t\t\t\tvar tmp3 = d3 + d4;\n\t\t\t\tvar tmp4 = d3 - d4;\n\t\n\t\t\t\t/* Even part */\n\t\t\t\tvar tmp10 = tmp0 + tmp3;\t/* phase 2 */\n\t\t\t\tvar tmp13 = tmp0 - tmp3;\n\t\t\t\tvar tmp11 = tmp1 + tmp2;\n\t\t\t\tvar tmp12 = tmp1 - tmp2;\n\t\n\t\t\t\tdata[dataOff] = tmp10 + tmp11; /* phase 3 */\n\t\t\t\tdata[dataOff+4] = tmp10 - tmp11;\n\t\n\t\t\t\tvar z1 = (tmp12 + tmp13) * 0.707106781; /* c4 */\n\t\t\t\tdata[dataOff+2] = tmp13 + z1; /* phase 5 */\n\t\t\t\tdata[dataOff+6] = tmp13 - z1;\n\t\n\t\t\t\t/* Odd part */\n\t\t\t\ttmp10 = tmp4 + tmp5; /* phase 2 */\n\t\t\t\ttmp11 = tmp5 + tmp6;\n\t\t\t\ttmp12 = tmp6 + tmp7;\n\t\n\t\t\t\t/* The rotator is modified from fig 4-8 to avoid extra negations. */\n\t\t\t\tvar z5 = (tmp10 - tmp12) * 0.382683433; /* c6 */\n\t\t\t\tvar z2 = 0.541196100 * tmp10 + z5; /* c2-c6 */\n\t\t\t\tvar z4 = 1.306562965 * tmp12 + z5; /* c2+c6 */\n\t\t\t\tvar z3 = tmp11 * 0.707106781; /* c4 */\n\t\n\t\t\t\tvar z11 = tmp7 + z3;\t/* phase 5 */\n\t\t\t\tvar z13 = tmp7 - z3;\n\t\n\t\t\t\tdata[dataOff+5] = z13 + z2;\t/* phase 6 */\n\t\t\t\tdata[dataOff+3] = z13 - z2;\n\t\t\t\tdata[dataOff+1] = z11 + z4;\n\t\t\t\tdata[dataOff+7] = z11 - z4;\n\t\n\t\t\t\tdataOff += 8; /* advance pointer to next row */\n\t\t\t}\n\t\n\t\t\t/* Pass 2: process columns. */\n\t\t\tdataOff = 0;\n\t\t\tfor (i=0; i<I8; ++i)\n\t\t\t{\n\t\t\t\td0 = data[dataOff];\n\t\t\t\td1 = data[dataOff + 8];\n\t\t\t\td2 = data[dataOff + 16];\n\t\t\t\td3 = data[dataOff + 24];\n\t\t\t\td4 = data[dataOff + 32];\n\t\t\t\td5 = data[dataOff + 40];\n\t\t\t\td6 = data[dataOff + 48];\n\t\t\t\td7 = data[dataOff + 56];\n\t\t\t\t\n\t\t\t\tvar tmp0p2 = d0 + d7;\n\t\t\t\tvar tmp7p2 = d0 - d7;\n\t\t\t\tvar tmp1p2 = d1 + d6;\n\t\t\t\tvar tmp6p2 = d1 - d6;\n\t\t\t\tvar tmp2p2 = d2 + d5;\n\t\t\t\tvar tmp5p2 = d2 - d5;\n\t\t\t\tvar tmp3p2 = d3 + d4;\n\t\t\t\tvar tmp4p2 = d3 - d4;\n\t\n\t\t\t\t/* Even part */\n\t\t\t\tvar tmp10p2 = tmp0p2 + tmp3p2;\t/* phase 2 */\n\t\t\t\tvar tmp13p2 = tmp0p2 - tmp3p2;\n\t\t\t\tvar tmp11p2 = tmp1p2 + tmp2p2;\n\t\t\t\tvar tmp12p2 = tmp1p2 - tmp2p2;\n\t\n\t\t\t\tdata[dataOff] = tmp10p2 + tmp11p2; /* phase 3 */\n\t\t\t\tdata[dataOff+32] = tmp10p2 - tmp11p2;\n\t\n\t\t\t\tvar z1p2 = (tmp12p2 + tmp13p2) * 0.707106781; /* c4 */\n\t\t\t\tdata[dataOff+16] = tmp13p2 + z1p2; /* phase 5 */\n\t\t\t\tdata[dataOff+48] = tmp13p2 - z1p2;\n\t\n\t\t\t\t/* Odd part */\n\t\t\t\ttmp10p2 = tmp4p2 + tmp5p2; /* phase 2 */\n\t\t\t\ttmp11p2 = tmp5p2 + tmp6p2;\n\t\t\t\ttmp12p2 = tmp6p2 + tmp7p2;\n\t\n\t\t\t\t/* The rotator is modified from fig 4-8 to avoid extra negations. */\n\t\t\t\tvar z5p2 = (tmp10p2 - tmp12p2) * 0.382683433; /* c6 */\n\t\t\t\tvar z2p2 = 0.541196100 * tmp10p2 + z5p2; /* c2-c6 */\n\t\t\t\tvar z4p2 = 1.306562965 * tmp12p2 + z5p2; /* c2+c6 */\n\t\t\t\tvar z3p2 = tmp11p2 * 0.707106781; /* c4 */\n\t\n\t\t\t\tvar z11p2 = tmp7p2 + z3p2;\t/* phase 5 */\n\t\t\t\tvar z13p2 = tmp7p2 - z3p2;\n\t\n\t\t\t\tdata[dataOff+40] = z13p2 + z2p2; /* phase 6 */\n\t\t\t\tdata[dataOff+24] = z13p2 - z2p2;\n\t\t\t\tdata[dataOff+ 8] = z11p2 + z4p2;\n\t\t\t\tdata[dataOff+56] = z11p2 - z4p2;\n\t\n\t\t\t\tdataOff++; /* advance pointer to next column */\n\t\t\t}\n\t\n\t\t\t// Quantize/descale the coefficients\n\t\t\tvar fDCTQuant;\n\t\t\tfor (i=0; i<I64; ++i)\n\t\t\t{\n\t\t\t\t// Apply the quantization and scaling factor & Round to nearest integer\n\t\t\t\tfDCTQuant = data[i]*fdtbl[i];\n\t\t\t\toutputfDCTQuant[i] = (fDCTQuant > 0.0) ? ((fDCTQuant + 0.5)|0) : ((fDCTQuant - 0.5)|0);\n\t\t\t\t//outputfDCTQuant[i] = fround(fDCTQuant);\n\n\t\t\t}\n\t\t\treturn outputfDCTQuant;\n\t\t}\n\t\t\n\t\tfunction writeAPP0()\n\t\t{\n\t\t\twriteWord(0xFFE0); // marker\n\t\t\twriteWord(16); // length\n\t\t\twriteByte(0x4A); // J\n\t\t\twriteByte(0x46); // F\n\t\t\twriteByte(0x49); // I\n\t\t\twriteByte(0x46); // F\n\t\t\twriteByte(0); // = \"JFIF\",'\\0'\n\t\t\twriteByte(1); // versionhi\n\t\t\twriteByte(1); // versionlo\n\t\t\twriteByte(0); // xyunits\n\t\t\twriteWord(1); // xdensity\n\t\t\twriteWord(1); // ydensity\n\t\t\twriteByte(0); // thumbnwidth\n\t\t\twriteByte(0); // thumbnheight\n\t\t}\n\n\t\tfunction writeAPP1(exifBuffer) {\n\t\t\tif (!exifBuffer) return;\n\n\t\t\twriteWord(0xFFE1); // APP1 marker\n\n\t\t\tif (exifBuffer[0] === 0x45 &&\n\t\t\t\t\texifBuffer[1] === 0x78 &&\n\t\t\t\t\texifBuffer[2] === 0x69 &&\n\t\t\t\t\texifBuffer[3] === 0x66) {\n\t\t\t\t// Buffer already starts with EXIF, just use it directly\n\t\t\t\twriteWord(exifBuffer.length + 2); // length is buffer + length itself!\n\t\t\t} else {\n\t\t\t\t// Buffer doesn't start with EXIF, write it for them\n\t\t\t\twriteWord(exifBuffer.length + 5 + 2); // length is buffer + EXIF\\0 + length itself!\n\t\t\t\twriteByte(0x45); // E\n\t\t\t\twriteByte(0x78); // X\n\t\t\t\twriteByte(0x69); // I\n\t\t\t\twriteByte(0x66); // F\n\t\t\t\twriteByte(0); // = \"EXIF\",'\\0'\n\t\t\t}\n\n\t\t\tfor (var i = 0; i < exifBuffer.length; i++) {\n\t\t\t\twriteByte(exifBuffer[i]);\n\t\t\t}\n\t\t}\n\n\t\tfunction writeSOF0(width, height)\n\t\t{\n\t\t\twriteWord(0xFFC0); // marker\n\t\t\twriteWord(17);   // length, truecolor YUV JPG\n\t\t\twriteByte(8);    // precision\n\t\t\twriteWord(height);\n\t\t\twriteWord(width);\n\t\t\twriteByte(3);    // nrofcomponents\n\t\t\twriteByte(1);    // IdY\n\t\t\twriteByte(0x11); // HVY\n\t\t\twriteByte(0);    // QTY\n\t\t\twriteByte(2);    // IdU\n\t\t\twriteByte(0x11); // HVU\n\t\t\twriteByte(1);    // QTU\n\t\t\twriteByte(3);    // IdV\n\t\t\twriteByte(0x11); // HVV\n\t\t\twriteByte(1);    // QTV\n\t\t}\n\t\n\t\tfunction writeDQT()\n\t\t{\n\t\t\twriteWord(0xFFDB); // marker\n\t\t\twriteWord(132);\t   // length\n\t\t\twriteByte(0);\n\t\t\tfor (var i=0; i<64; i++) {\n\t\t\t\twriteByte(YTable[i]);\n\t\t\t}\n\t\t\twriteByte(1);\n\t\t\tfor (var j=0; j<64; j++) {\n\t\t\t\twriteByte(UVTable[j]);\n\t\t\t}\n\t\t}\n\t\n\t\tfunction writeDHT()\n\t\t{\n\t\t\twriteWord(0xFFC4); // marker\n\t\t\twriteWord(0x01A2); // length\n\t\n\t\t\twriteByte(0); // HTYDCinfo\n\t\t\tfor (var i=0; i<16; i++) {\n\t\t\t\twriteByte(std_dc_luminance_nrcodes[i+1]);\n\t\t\t}\n\t\t\tfor (var j=0; j<=11; j++) {\n\t\t\t\twriteByte(std_dc_luminance_values[j]);\n\t\t\t}\n\t\n\t\t\twriteByte(0x10); // HTYACinfo\n\t\t\tfor (var k=0; k<16; k++) {\n\t\t\t\twriteByte(std_ac_luminance_nrcodes[k+1]);\n\t\t\t}\n\t\t\tfor (var l=0; l<=161; l++) {\n\t\t\t\twriteByte(std_ac_luminance_values[l]);\n\t\t\t}\n\t\n\t\t\twriteByte(1); // HTUDCinfo\n\t\t\tfor (var m=0; m<16; m++) {\n\t\t\t\twriteByte(std_dc_chrominance_nrcodes[m+1]);\n\t\t\t}\n\t\t\tfor (var n=0; n<=11; n++) {\n\t\t\t\twriteByte(std_dc_chrominance_values[n]);\n\t\t\t}\n\t\n\t\t\twriteByte(0x11); // HTUACinfo\n\t\t\tfor (var o=0; o<16; o++) {\n\t\t\t\twriteByte(std_ac_chrominance_nrcodes[o+1]);\n\t\t\t}\n\t\t\tfor (var p=0; p<=161; p++) {\n\t\t\t\twriteByte(std_ac_chrominance_values[p]);\n\t\t\t}\n\t\t}\n\t\t\n\t\tfunction writeCOM(comments)\n\t\t{\n\t\t\tif (typeof comments === \"undefined\" || comments.constructor !== Array) return;\n\t\t\tcomments.forEach(e => {\n\t\t\t\tif (typeof e !== \"string\") return;\n\t\t\t\twriteWord(0xFFFE); // marker\n\t\t\t\tvar l = e.length;\n\t\t\t\twriteWord(l + 2); // length itself as well\n\t\t\t\tvar i;\n\t\t\t\tfor (i = 0; i < l; i++)\n\t\t\t\t\twriteByte(e.charCodeAt(i));\n\t\t\t});\n\t\t}\n\t\n\t\tfunction writeSOS()\n\t\t{\n\t\t\twriteWord(0xFFDA); // marker\n\t\t\twriteWord(12); // length\n\t\t\twriteByte(3); // nrofcomponents\n\t\t\twriteByte(1); // IdY\n\t\t\twriteByte(0); // HTY\n\t\t\twriteByte(2); // IdU\n\t\t\twriteByte(0x11); // HTU\n\t\t\twriteByte(3); // IdV\n\t\t\twriteByte(0x11); // HTV\n\t\t\twriteByte(0); // Ss\n\t\t\twriteByte(0x3f); // Se\n\t\t\twriteByte(0); // Bf\n\t\t}\n\t\t\n\t\tfunction processDU(CDU, fdtbl, DC, HTDC, HTAC){\n\t\t\tvar EOB = HTAC[0x00];\n\t\t\tvar M16zeroes = HTAC[0xF0];\n\t\t\tvar pos;\n\t\t\tvar I16 = 16;\n\t\t\tvar I63 = 63;\n\t\t\tvar I64 = 64;\n\t\t\tvar DU_DCT = fDCTQuant(CDU, fdtbl);\n\t\t\t//ZigZag reorder\n\t\t\tfor (var j=0;j<I64;++j) {\n\t\t\t\tDU[ZigZag[j]]=DU_DCT[j];\n\t\t\t}\n\t\t\tvar Diff = DU[0] - DC; DC = DU[0];\n\t\t\t//Encode DC\n\t\t\tif (Diff==0) {\n\t\t\t\twriteBits(HTDC[0]); // Diff might be 0\n\t\t\t} else {\n\t\t\t\tpos = 32767+Diff;\n\t\t\t\twriteBits(HTDC[category[pos]]);\n\t\t\t\twriteBits(bitcode[pos]);\n\t\t\t}\n\t\t\t//Encode ACs\n\t\t\tvar end0pos = 63; // was const... which is crazy\n\t\t\tfor (; (end0pos>0)&&(DU[end0pos]==0); end0pos--) {};\n\t\t\t//end0pos = first element in reverse order !=0\n\t\t\tif ( end0pos == 0) {\n\t\t\t\twriteBits(EOB);\n\t\t\t\treturn DC;\n\t\t\t}\n\t\t\tvar i = 1;\n\t\t\tvar lng;\n\t\t\twhile ( i <= end0pos ) {\n\t\t\t\tvar startpos = i;\n\t\t\t\tfor (; (DU[i]==0) && (i<=end0pos); ++i) {}\n\t\t\t\tvar nrzeroes = i-startpos;\n\t\t\t\tif ( nrzeroes >= I16 ) {\n\t\t\t\t\tlng = nrzeroes>>4;\n\t\t\t\t\tfor (var nrmarker=1; nrmarker <= lng; ++nrmarker)\n\t\t\t\t\t\twriteBits(M16zeroes);\n\t\t\t\t\tnrzeroes = nrzeroes&0xF;\n\t\t\t\t}\n\t\t\t\tpos = 32767+DU[i];\n\t\t\t\twriteBits(HTAC[(nrzeroes<<4)+category[pos]]);\n\t\t\t\twriteBits(bitcode[pos]);\n\t\t\t\ti++;\n\t\t\t}\n\t\t\tif ( end0pos != I63 ) {\n\t\t\t\twriteBits(EOB);\n\t\t\t}\n\t\t\treturn DC;\n\t\t}\n\n\t\tfunction initCharLookupTable(){\n\t\t\tvar sfcc = String.fromCharCode;\n\t\t\tfor(var i=0; i < 256; i++){ ///// ACHTUNG // 255\n\t\t\t\tclt[i] = sfcc(i);\n\t\t\t}\n\t\t}\n\t\t\n\t\tthis.encode = function(image,quality) // image data object\n\t\t{\n\t\t\tvar time_start = new Date().getTime();\n\t\t\t\n\t\t\tif(quality) setQuality(quality);\n\t\t\t\n\t\t\t// Initialize bit writer\n\t\t\tbyteout = new Array();\n\t\t\tbytenew=0;\n\t\t\tbytepos=7;\n\t\n\t\t\t// Add JPEG headers\n\t\t\twriteWord(0xFFD8); // SOI\n\t\t\twriteAPP0();\n\t\t\twriteCOM(image.comments);\n\t\t\twriteAPP1(image.exifBuffer);\n\t\t\twriteDQT();\n\t\t\twriteSOF0(image.width,image.height);\n\t\t\twriteDHT();\n\t\t\twriteSOS();\n\n\t\n\t\t\t// Encode 8x8 macroblocks\n\t\t\tvar DCY=0;\n\t\t\tvar DCU=0;\n\t\t\tvar DCV=0;\n\t\t\t\n\t\t\tbytenew=0;\n\t\t\tbytepos=7;\n\t\t\t\n\t\t\t\n\t\t\tthis.encode.displayName = \"_encode_\";\n\n\t\t\tvar imageData = image.data;\n\t\t\tvar width = image.width;\n\t\t\tvar height = image.height;\n\n\t\t\tvar quadWidth = width*4;\n\t\t\tvar tripleWidth = width*3;\n\t\t\t\n\t\t\tvar x, y = 0;\n\t\t\tvar r, g, b;\n\t\t\tvar start,p, col,row,pos;\n\t\t\twhile(y < height){\n\t\t\t\tx = 0;\n\t\t\t\twhile(x < quadWidth){\n\t\t\t\tstart = quadWidth * y + x;\n\t\t\t\tp = start;\n\t\t\t\tcol = -1;\n\t\t\t\trow = 0;\n\t\t\t\t\n\t\t\t\tfor(pos=0; pos < 64; pos++){\n\t\t\t\t\trow = pos >> 3;// /8\n\t\t\t\t\tcol = ( pos & 7 ) * 4; // %8\n\t\t\t\t\tp = start + ( row * quadWidth ) + col;\t\t\n\t\t\t\t\t\n\t\t\t\t\tif(y+row >= height){ // padding bottom\n\t\t\t\t\t\tp-= (quadWidth*(y+1+row-height));\n\t\t\t\t\t}\n\n\t\t\t\t\tif(x+col >= quadWidth){ // padding right\t\n\t\t\t\t\t\tp-= ((x+col) - quadWidth +4)\n\t\t\t\t\t}\n\t\t\t\t\t\n\t\t\t\t\tr = imageData[ p++ ];\n\t\t\t\t\tg = imageData[ p++ ];\n\t\t\t\t\tb = imageData[ p++ ];\n\t\t\t\t\t\n\t\t\t\t\t\n\t\t\t\t\t/* // calculate YUV values dynamically\n\t\t\t\t\tYDU[pos]=((( 0.29900)*r+( 0.58700)*g+( 0.11400)*b))-128; //-0x80\n\t\t\t\t\tUDU[pos]=(((-0.16874)*r+(-0.33126)*g+( 0.50000)*b));\n\t\t\t\t\tVDU[pos]=((( 0.50000)*r+(-0.41869)*g+(-0.08131)*b));\n\t\t\t\t\t*/\n\t\t\t\t\t\n\t\t\t\t\t// use lookup table (slightly faster)\n\t\t\t\t\tYDU[pos] = ((RGB_YUV_TABLE[r]             + RGB_YUV_TABLE[(g +  256)>>0] + RGB_YUV_TABLE[(b +  512)>>0]) >> 16)-128;\n\t\t\t\t\tUDU[pos] = ((RGB_YUV_TABLE[(r +  768)>>0] + RGB_YUV_TABLE[(g + 1024)>>0] + RGB_YUV_TABLE[(b + 1280)>>0]) >> 16)-128;\n\t\t\t\t\tVDU[pos] = ((RGB_YUV_TABLE[(r + 1280)>>0] + RGB_YUV_TABLE[(g + 1536)>>0] + RGB_YUV_TABLE[(b + 1792)>>0]) >> 16)-128;\n\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\tDCY = processDU(YDU, fdtbl_Y, DCY, YDC_HT, YAC_HT);\n\t\t\t\tDCU = processDU(UDU, fdtbl_UV, DCU, UVDC_HT, UVAC_HT);\n\t\t\t\tDCV = processDU(VDU, fdtbl_UV, DCV, UVDC_HT, UVAC_HT);\n\t\t\t\tx+=32;\n\t\t\t\t}\n\t\t\t\ty+=8;\n\t\t\t}\n\t\t\t\n\t\t\t\n\t\t\t////////////////////////////////////////////////////////////////\n\t\n\t\t\t// Do the bit alignment of the EOI marker\n\t\t\tif ( bytepos >= 0 ) {\n\t\t\t\tvar fillbits = [];\n\t\t\t\tfillbits[1] = bytepos+1;\n\t\t\t\tfillbits[0] = (1<<(bytepos+1))-1;\n\t\t\t\twriteBits(fillbits);\n\t\t\t}\n\t\n\t\t\twriteWord(0xFFD9); //EOI\n\n\t\t\tif (typeof module === 'undefined') return new Uint8Array(byteout);\n      return Buffer.from(byteout);\n\n\t\t\tvar jpegDataUri = 'data:image/jpeg;base64,' + btoa(byteout.join(''));\n\t\t\t\n\t\t\tbyteout = [];\n\t\t\t\n\t\t\t// benchmarking\n\t\t\tvar duration = new Date().getTime() - time_start;\n    \t\t//console.log('Encoding time: '+ duration + 'ms');\n    \t\t//\n\t\t\t\n\t\t\treturn jpegDataUri\t\t\t\n\t}\n\t\n\tfunction setQuality(quality){\n\t\tif (quality <= 0) {\n\t\t\tquality = 1;\n\t\t}\n\t\tif (quality > 100) {\n\t\t\tquality = 100;\n\t\t}\n\t\t\n\t\tif(currentQuality == quality) return // don't recalc if unchanged\n\t\t\n\t\tvar sf = 0;\n\t\tif (quality < 50) {\n\t\t\tsf = Math.floor(5000 / quality);\n\t\t} else {\n\t\t\tsf = Math.floor(200 - quality*2);\n\t\t}\n\t\t\n\t\tinitQuantTables(sf);\n\t\tcurrentQuality = quality;\n\t\t//console.log('Quality set to: '+quality +'%');\n\t}\n\t\n\tfunction init(){\n\t\tvar time_start = new Date().getTime();\n\t\tif(!quality) quality = 50;\n\t\t// Create tables\n\t\tinitCharLookupTable()\n\t\tinitHuffmanTbl();\n\t\tinitCategoryNumber();\n\t\tinitRGBYUVTable();\n\t\t\n\t\tsetQuality(quality);\n\t\tvar duration = new Date().getTime() - time_start;\n    \t//console.log('Initialization '+ duration + 'ms');\n\t}\n\t\n\tinit();\n\t\n};\n\nif (typeof module !== 'undefined') {\n\tmodule.exports = encode;\n} else if (typeof window !== 'undefined') {\n\twindow['jpeg-js'] = window['jpeg-js'] || {};\n\twindow['jpeg-js'].encode = encode;\n}\n\nfunction encode(imgData, qu) {\n  if (typeof qu === 'undefined') qu = 50;\n  var encoder = new JPEGEncoder(qu);\n\tvar data = encoder.encode(imgData, qu);\n  return {\n    data: data,\n    width: imgData.width,\n    height: imgData.height,\n  };\n}\n\n// helper function to get the imageData of an existing image on the current page.\nfunction getImageDataFromImage(idOrElement){\n\tvar theImg = (typeof(idOrElement)=='string')? document.getElementById(idOrElement):idOrElement;\n\tvar cvs = document.createElement('canvas');\n\tcvs.width = theImg.width;\n\tcvs.height = theImg.height;\n\tvar ctx = cvs.getContext(\"2d\");\n\tctx.drawImage(theImg,0,0);\n\t\n\treturn (ctx.getImageData(0, 0, cvs.width, cvs.height));\n}\n", "/* -*- tab-width: 2; indent-tabs-mode: nil; c-basic-offset: 2 -*- /\n/* vim: set shiftwidth=2 tabstop=2 autoindent cindent expandtab: */\n/*\n   Copyright 2011 notmasteryet\n\n   Licensed under the Apache License, Version 2.0 (the \"License\");\n   you may not use this file except in compliance with the License.\n   You may obtain a copy of the License at\n\n       http://www.apache.org/licenses/LICENSE-2.0\n\n   Unless required by applicable law or agreed to in writing, software\n   distributed under the License is distributed on an \"AS IS\" BASIS,\n   WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n   See the License for the specific language governing permissions and\n   limitations under the License.\n*/\n\n// - The JPEG specification can be found in the ITU CCITT Recommendation T.81\n//   (www.w3.org/Graphics/JPEG/itu-t81.pdf)\n// - The JFIF specification can be found in the JPEG File Interchange Format\n//   (www.w3.org/Graphics/JPEG/jfif3.pdf)\n// - The Adobe Application-Specific JPEG markers in the Supporting the DCT Filters\n//   in PostScript Level 2, Technical Note #5116\n//   (partners.adobe.com/public/developer/en/ps/sdk/5116.DCT_Filter.pdf)\n\nvar JpegImage = (function jpegImage() {\n  \n  var dct<PERSON>igZag = new Int32Array([\n     0,\n     1,  8,\n    16,  9,  2,\n     3, 10, 17, 24,\n    32, 25, 18, 11, 4,\n     5, 12, 19, 26, 33, 40,\n    48, 41, 34, 27, 20, 13,  6,\n     7, 14, 21, 28, 35, 42, 49, 56,\n    57, 50, 43, 36, 29, 22, 15,\n    23, 30, 37, 44, 51, 58,\n    59, 52, 45, 38, 31,\n    39, 46, 53, 60,\n    61, 54, 47,\n    55, 62,\n    63\n  ]);\n\n  var dctCos1  =  4017   // cos(pi/16)\n  var dctSin1  =   799   // sin(pi/16)\n  var dctCos3  =  3406   // cos(3*pi/16)\n  var dctSin3  =  2276   // sin(3*pi/16)\n  var dctCos6  =  1567   // cos(6*pi/16)\n  var dctSin6  =  3784   // sin(6*pi/16)\n  var dctSqrt2 =  5793   // sqrt(2)\n  var dctSqrt1d2 = 2896  // sqrt(2) / 2\n\n  function constructor() {\n  }\n\n  function buildHuffmanTable(codeLengths, values) {\n    var k = 0, code = [], i, j, length = 16;\n    while (length > 0 && !codeLengths[length - 1])\n      length--;\n    code.push({children: [], index: 0});\n    var p = code[0], q;\n    for (i = 0; i < length; i++) {\n      for (j = 0; j < codeLengths[i]; j++) {\n        p = code.pop();\n        p.children[p.index] = values[k];\n        while (p.index > 0) {\n          if (code.length === 0)\n            throw new Error('Could not recreate Huffman Table');\n          p = code.pop();\n        }\n        p.index++;\n        code.push(p);\n        while (code.length <= i) {\n          code.push(q = {children: [], index: 0});\n          p.children[p.index] = q.children;\n          p = q;\n        }\n        k++;\n      }\n      if (i + 1 < length) {\n        // p here points to last code\n        code.push(q = {children: [], index: 0});\n        p.children[p.index] = q.children;\n        p = q;\n      }\n    }\n    return code[0].children;\n  }\n\n  function decodeScan(data, offset,\n                      frame, components, resetInterval,\n                      spectralStart, spectralEnd,\n                      successivePrev, successive, opts) {\n    var precision = frame.precision;\n    var samplesPerLine = frame.samplesPerLine;\n    var scanLines = frame.scanLines;\n    var mcusPerLine = frame.mcusPerLine;\n    var progressive = frame.progressive;\n    var maxH = frame.maxH, maxV = frame.maxV;\n\n    var startOffset = offset, bitsData = 0, bitsCount = 0;\n    function readBit() {\n      if (bitsCount > 0) {\n        bitsCount--;\n        return (bitsData >> bitsCount) & 1;\n      }\n      bitsData = data[offset++];\n      if (bitsData == 0xFF) {\n        var nextByte = data[offset++];\n        if (nextByte) {\n          throw new Error(\"unexpected marker: \" + ((bitsData << 8) | nextByte).toString(16));\n        }\n        // unstuff 0\n      }\n      bitsCount = 7;\n      return bitsData >>> 7;\n    }\n    function decodeHuffman(tree) {\n      var node = tree, bit;\n      while ((bit = readBit()) !== null) {\n        node = node[bit];\n        if (typeof node === 'number')\n          return node;\n        if (typeof node !== 'object')\n          throw new Error(\"invalid huffman sequence\");\n      }\n      return null;\n    }\n    function receive(length) {\n      var n = 0;\n      while (length > 0) {\n        var bit = readBit();\n        if (bit === null) return;\n        n = (n << 1) | bit;\n        length--;\n      }\n      return n;\n    }\n    function receiveAndExtend(length) {\n      var n = receive(length);\n      if (n >= 1 << (length - 1))\n        return n;\n      return n + (-1 << length) + 1;\n    }\n    function decodeBaseline(component, zz) {\n      var t = decodeHuffman(component.huffmanTableDC);\n      var diff = t === 0 ? 0 : receiveAndExtend(t);\n      zz[0]= (component.pred += diff);\n      var k = 1;\n      while (k < 64) {\n        var rs = decodeHuffman(component.huffmanTableAC);\n        var s = rs & 15, r = rs >> 4;\n        if (s === 0) {\n          if (r < 15)\n            break;\n          k += 16;\n          continue;\n        }\n        k += r;\n        var z = dctZigZag[k];\n        zz[z] = receiveAndExtend(s);\n        k++;\n      }\n    }\n    function decodeDCFirst(component, zz) {\n      var t = decodeHuffman(component.huffmanTableDC);\n      var diff = t === 0 ? 0 : (receiveAndExtend(t) << successive);\n      zz[0] = (component.pred += diff);\n    }\n    function decodeDCSuccessive(component, zz) {\n      zz[0] |= readBit() << successive;\n    }\n    var eobrun = 0;\n    function decodeACFirst(component, zz) {\n      if (eobrun > 0) {\n        eobrun--;\n        return;\n      }\n      var k = spectralStart, e = spectralEnd;\n      while (k <= e) {\n        var rs = decodeHuffman(component.huffmanTableAC);\n        var s = rs & 15, r = rs >> 4;\n        if (s === 0) {\n          if (r < 15) {\n            eobrun = receive(r) + (1 << r) - 1;\n            break;\n          }\n          k += 16;\n          continue;\n        }\n        k += r;\n        var z = dctZigZag[k];\n        zz[z] = receiveAndExtend(s) * (1 << successive);\n        k++;\n      }\n    }\n    var successiveACState = 0, successiveACNextValue;\n    function decodeACSuccessive(component, zz) {\n      var k = spectralStart, e = spectralEnd, r = 0;\n      while (k <= e) {\n        var z = dctZigZag[k];\n        var direction = zz[z] < 0 ? -1 : 1;\n        switch (successiveACState) {\n        case 0: // initial state\n          var rs = decodeHuffman(component.huffmanTableAC);\n          var s = rs & 15, r = rs >> 4;\n          if (s === 0) {\n            if (r < 15) {\n              eobrun = receive(r) + (1 << r);\n              successiveACState = 4;\n            } else {\n              r = 16;\n              successiveACState = 1;\n            }\n          } else {\n            if (s !== 1)\n              throw new Error(\"invalid ACn encoding\");\n            successiveACNextValue = receiveAndExtend(s);\n            successiveACState = r ? 2 : 3;\n          }\n          continue;\n        case 1: // skipping r zero items\n        case 2:\n          if (zz[z])\n            zz[z] += (readBit() << successive) * direction;\n          else {\n            r--;\n            if (r === 0)\n              successiveACState = successiveACState == 2 ? 3 : 0;\n          }\n          break;\n        case 3: // set value for a zero item\n          if (zz[z])\n            zz[z] += (readBit() << successive) * direction;\n          else {\n            zz[z] = successiveACNextValue << successive;\n            successiveACState = 0;\n          }\n          break;\n        case 4: // eob\n          if (zz[z])\n            zz[z] += (readBit() << successive) * direction;\n          break;\n        }\n        k++;\n      }\n      if (successiveACState === 4) {\n        eobrun--;\n        if (eobrun === 0)\n          successiveACState = 0;\n      }\n    }\n    function decodeMcu(component, decode, mcu, row, col) {\n      var mcuRow = (mcu / mcusPerLine) | 0;\n      var mcuCol = mcu % mcusPerLine;\n      var blockRow = mcuRow * component.v + row;\n      var blockCol = mcuCol * component.h + col;\n      // If the block is missing and we're in tolerant mode, just skip it.\n      if (component.blocks[blockRow] === undefined && opts.tolerantDecoding)\n        return;\n      decode(component, component.blocks[blockRow][blockCol]);\n    }\n    function decodeBlock(component, decode, mcu) {\n      var blockRow = (mcu / component.blocksPerLine) | 0;\n      var blockCol = mcu % component.blocksPerLine;\n      // If the block is missing and we're in tolerant mode, just skip it.\n      if (component.blocks[blockRow] === undefined && opts.tolerantDecoding)\n        return;\n      decode(component, component.blocks[blockRow][blockCol]);\n    }\n\n    var componentsLength = components.length;\n    var component, i, j, k, n;\n    var decodeFn;\n    if (progressive) {\n      if (spectralStart === 0)\n        decodeFn = successivePrev === 0 ? decodeDCFirst : decodeDCSuccessive;\n      else\n        decodeFn = successivePrev === 0 ? decodeACFirst : decodeACSuccessive;\n    } else {\n      decodeFn = decodeBaseline;\n    }\n\n    var mcu = 0, marker;\n    var mcuExpected;\n    if (componentsLength == 1) {\n      mcuExpected = components[0].blocksPerLine * components[0].blocksPerColumn;\n    } else {\n      mcuExpected = mcusPerLine * frame.mcusPerColumn;\n    }\n    if (!resetInterval) resetInterval = mcuExpected;\n\n    var h, v;\n    while (mcu < mcuExpected) {\n      // reset interval stuff\n      for (i = 0; i < componentsLength; i++)\n        components[i].pred = 0;\n      eobrun = 0;\n\n      if (componentsLength == 1) {\n        component = components[0];\n        for (n = 0; n < resetInterval; n++) {\n          decodeBlock(component, decodeFn, mcu);\n          mcu++;\n        }\n      } else {\n        for (n = 0; n < resetInterval; n++) {\n          for (i = 0; i < componentsLength; i++) {\n            component = components[i];\n            h = component.h;\n            v = component.v;\n            for (j = 0; j < v; j++) {\n              for (k = 0; k < h; k++) {\n                decodeMcu(component, decodeFn, mcu, j, k);\n              }\n            }\n          }\n          mcu++;\n\n          // If we've reached our expected MCU's, stop decoding\n          if (mcu === mcuExpected) break;\n        }\n      }\n\n      if (mcu === mcuExpected) {\n        // Skip trailing bytes at the end of the scan - until we reach the next marker\n        do {\n          if (data[offset] === 0xFF) {\n            if (data[offset + 1] !== 0x00) {\n              break;\n            }\n          }\n          offset += 1;\n        } while (offset < data.length - 2);\n      }\n\n      // find marker\n      bitsCount = 0;\n      marker = (data[offset] << 8) | data[offset + 1];\n      if (marker < 0xFF00) {\n        throw new Error(\"marker was not found\");\n      }\n\n      if (marker >= 0xFFD0 && marker <= 0xFFD7) { // RSTx\n        offset += 2;\n      }\n      else\n        break;\n    }\n\n    return offset - startOffset;\n  }\n\n  function buildComponentData(frame, component) {\n    var lines = [];\n    var blocksPerLine = component.blocksPerLine;\n    var blocksPerColumn = component.blocksPerColumn;\n    var samplesPerLine = blocksPerLine << 3;\n    // Only 1 used per invocation of this function and garbage collected after invocation, so no need to account for its memory footprint.\n    var R = new Int32Array(64), r = new Uint8Array(64);\n\n    // A port of poppler's IDCT method which in turn is taken from:\n    //   Christoph Loeffler, Adriaan Ligtenberg, George S. Moschytz,\n    //   \"Practical Fast 1-D DCT Algorithms with 11 Multiplications\",\n    //   IEEE Intl. Conf. on Acoustics, Speech & Signal Processing, 1989,\n    //   988-991.\n    function quantizeAndInverse(zz, dataOut, dataIn) {\n      var qt = component.quantizationTable;\n      var v0, v1, v2, v3, v4, v5, v6, v7, t;\n      var p = dataIn;\n      var i;\n\n      // dequant\n      for (i = 0; i < 64; i++)\n        p[i] = zz[i] * qt[i];\n\n      // inverse DCT on rows\n      for (i = 0; i < 8; ++i) {\n        var row = 8 * i;\n\n        // check for all-zero AC coefficients\n        if (p[1 + row] == 0 && p[2 + row] == 0 && p[3 + row] == 0 &&\n            p[4 + row] == 0 && p[5 + row] == 0 && p[6 + row] == 0 &&\n            p[7 + row] == 0) {\n          t = (dctSqrt2 * p[0 + row] + 512) >> 10;\n          p[0 + row] = t;\n          p[1 + row] = t;\n          p[2 + row] = t;\n          p[3 + row] = t;\n          p[4 + row] = t;\n          p[5 + row] = t;\n          p[6 + row] = t;\n          p[7 + row] = t;\n          continue;\n        }\n\n        // stage 4\n        v0 = (dctSqrt2 * p[0 + row] + 128) >> 8;\n        v1 = (dctSqrt2 * p[4 + row] + 128) >> 8;\n        v2 = p[2 + row];\n        v3 = p[6 + row];\n        v4 = (dctSqrt1d2 * (p[1 + row] - p[7 + row]) + 128) >> 8;\n        v7 = (dctSqrt1d2 * (p[1 + row] + p[7 + row]) + 128) >> 8;\n        v5 = p[3 + row] << 4;\n        v6 = p[5 + row] << 4;\n\n        // stage 3\n        t = (v0 - v1+ 1) >> 1;\n        v0 = (v0 + v1 + 1) >> 1;\n        v1 = t;\n        t = (v2 * dctSin6 + v3 * dctCos6 + 128) >> 8;\n        v2 = (v2 * dctCos6 - v3 * dctSin6 + 128) >> 8;\n        v3 = t;\n        t = (v4 - v6 + 1) >> 1;\n        v4 = (v4 + v6 + 1) >> 1;\n        v6 = t;\n        t = (v7 + v5 + 1) >> 1;\n        v5 = (v7 - v5 + 1) >> 1;\n        v7 = t;\n\n        // stage 2\n        t = (v0 - v3 + 1) >> 1;\n        v0 = (v0 + v3 + 1) >> 1;\n        v3 = t;\n        t = (v1 - v2 + 1) >> 1;\n        v1 = (v1 + v2 + 1) >> 1;\n        v2 = t;\n        t = (v4 * dctSin3 + v7 * dctCos3 + 2048) >> 12;\n        v4 = (v4 * dctCos3 - v7 * dctSin3 + 2048) >> 12;\n        v7 = t;\n        t = (v5 * dctSin1 + v6 * dctCos1 + 2048) >> 12;\n        v5 = (v5 * dctCos1 - v6 * dctSin1 + 2048) >> 12;\n        v6 = t;\n\n        // stage 1\n        p[0 + row] = v0 + v7;\n        p[7 + row] = v0 - v7;\n        p[1 + row] = v1 + v6;\n        p[6 + row] = v1 - v6;\n        p[2 + row] = v2 + v5;\n        p[5 + row] = v2 - v5;\n        p[3 + row] = v3 + v4;\n        p[4 + row] = v3 - v4;\n      }\n\n      // inverse DCT on columns\n      for (i = 0; i < 8; ++i) {\n        var col = i;\n\n        // check for all-zero AC coefficients\n        if (p[1*8 + col] == 0 && p[2*8 + col] == 0 && p[3*8 + col] == 0 &&\n            p[4*8 + col] == 0 && p[5*8 + col] == 0 && p[6*8 + col] == 0 &&\n            p[7*8 + col] == 0) {\n          t = (dctSqrt2 * dataIn[i+0] + 8192) >> 14;\n          p[0*8 + col] = t;\n          p[1*8 + col] = t;\n          p[2*8 + col] = t;\n          p[3*8 + col] = t;\n          p[4*8 + col] = t;\n          p[5*8 + col] = t;\n          p[6*8 + col] = t;\n          p[7*8 + col] = t;\n          continue;\n        }\n\n        // stage 4\n        v0 = (dctSqrt2 * p[0*8 + col] + 2048) >> 12;\n        v1 = (dctSqrt2 * p[4*8 + col] + 2048) >> 12;\n        v2 = p[2*8 + col];\n        v3 = p[6*8 + col];\n        v4 = (dctSqrt1d2 * (p[1*8 + col] - p[7*8 + col]) + 2048) >> 12;\n        v7 = (dctSqrt1d2 * (p[1*8 + col] + p[7*8 + col]) + 2048) >> 12;\n        v5 = p[3*8 + col];\n        v6 = p[5*8 + col];\n\n        // stage 3\n        t = (v0 - v1 + 1) >> 1;\n        v0 = (v0 + v1 + 1) >> 1;\n        v1 = t;\n        t = (v2 * dctSin6 + v3 * dctCos6 + 2048) >> 12;\n        v2 = (v2 * dctCos6 - v3 * dctSin6 + 2048) >> 12;\n        v3 = t;\n        t = (v4 - v6 + 1) >> 1;\n        v4 = (v4 + v6 + 1) >> 1;\n        v6 = t;\n        t = (v7 + v5 + 1) >> 1;\n        v5 = (v7 - v5 + 1) >> 1;\n        v7 = t;\n\n        // stage 2\n        t = (v0 - v3 + 1) >> 1;\n        v0 = (v0 + v3 + 1) >> 1;\n        v3 = t;\n        t = (v1 - v2 + 1) >> 1;\n        v1 = (v1 + v2 + 1) >> 1;\n        v2 = t;\n        t = (v4 * dctSin3 + v7 * dctCos3 + 2048) >> 12;\n        v4 = (v4 * dctCos3 - v7 * dctSin3 + 2048) >> 12;\n        v7 = t;\n        t = (v5 * dctSin1 + v6 * dctCos1 + 2048) >> 12;\n        v5 = (v5 * dctCos1 - v6 * dctSin1 + 2048) >> 12;\n        v6 = t;\n\n        // stage 1\n        p[0*8 + col] = v0 + v7;\n        p[7*8 + col] = v0 - v7;\n        p[1*8 + col] = v1 + v6;\n        p[6*8 + col] = v1 - v6;\n        p[2*8 + col] = v2 + v5;\n        p[5*8 + col] = v2 - v5;\n        p[3*8 + col] = v3 + v4;\n        p[4*8 + col] = v3 - v4;\n      }\n\n      // convert to 8-bit integers\n      for (i = 0; i < 64; ++i) {\n        var sample = 128 + ((p[i] + 8) >> 4);\n        dataOut[i] = sample < 0 ? 0 : sample > 0xFF ? 0xFF : sample;\n      }\n    }\n\n    requestMemoryAllocation(samplesPerLine * blocksPerColumn * 8);\n\n    var i, j;\n    for (var blockRow = 0; blockRow < blocksPerColumn; blockRow++) {\n      var scanLine = blockRow << 3;\n      for (i = 0; i < 8; i++)\n        lines.push(new Uint8Array(samplesPerLine));\n      for (var blockCol = 0; blockCol < blocksPerLine; blockCol++) {\n        quantizeAndInverse(component.blocks[blockRow][blockCol], r, R);\n\n        var offset = 0, sample = blockCol << 3;\n        for (j = 0; j < 8; j++) {\n          var line = lines[scanLine + j];\n          for (i = 0; i < 8; i++)\n            line[sample + i] = r[offset++];\n        }\n      }\n    }\n    return lines;\n  }\n\n  function clampTo8bit(a) {\n    return a < 0 ? 0 : a > 255 ? 255 : a;\n  }\n\n  constructor.prototype = {\n    load: function load(path) {\n      var xhr = new XMLHttpRequest();\n      xhr.open(\"GET\", path, true);\n      xhr.responseType = \"arraybuffer\";\n      xhr.onload = (function() {\n        // TODO catch parse error\n        var data = new Uint8Array(xhr.response || xhr.mozResponseArrayBuffer);\n        this.parse(data);\n        if (this.onload)\n          this.onload();\n      }).bind(this);\n      xhr.send(null);\n    },\n    parse: function parse(data) {\n      var maxResolutionInPixels = this.opts.maxResolutionInMP * 1000 * 1000;\n      var offset = 0, length = data.length;\n      function readUint16() {\n        var value = (data[offset] << 8) | data[offset + 1];\n        offset += 2;\n        return value;\n      }\n      function readDataBlock() {\n        var length = readUint16();\n        var array = data.subarray(offset, offset + length - 2);\n        offset += array.length;\n        return array;\n      }\n      function prepareComponents(frame) {\n        // According to the JPEG standard, the sampling factor must be between 1 and 4\n        // See https://github.com/libjpeg-turbo/libjpeg-turbo/blob/9abeff46d87bd201a952e276f3e4339556a403a3/libjpeg.txt#L1138-L1146\n        var maxH = 1, maxV = 1;\n        var component, componentId;\n        for (componentId in frame.components) {\n          if (frame.components.hasOwnProperty(componentId)) {\n            component = frame.components[componentId];\n            if (maxH < component.h) maxH = component.h;\n            if (maxV < component.v) maxV = component.v;\n          }\n        }\n        var mcusPerLine = Math.ceil(frame.samplesPerLine / 8 / maxH);\n        var mcusPerColumn = Math.ceil(frame.scanLines / 8 / maxV);\n        for (componentId in frame.components) {\n          if (frame.components.hasOwnProperty(componentId)) {\n            component = frame.components[componentId];\n            var blocksPerLine = Math.ceil(Math.ceil(frame.samplesPerLine / 8) * component.h / maxH);\n            var blocksPerColumn = Math.ceil(Math.ceil(frame.scanLines  / 8) * component.v / maxV);\n            var blocksPerLineForMcu = mcusPerLine * component.h;\n            var blocksPerColumnForMcu = mcusPerColumn * component.v;\n            var blocksToAllocate = blocksPerColumnForMcu * blocksPerLineForMcu;\n            var blocks = [];\n\n            // Each block is a Int32Array of length 64 (4 x 64 = 256 bytes)\n            requestMemoryAllocation(blocksToAllocate * 256);\n\n            for (var i = 0; i < blocksPerColumnForMcu; i++) {\n              var row = [];\n              for (var j = 0; j < blocksPerLineForMcu; j++)\n                row.push(new Int32Array(64));\n              blocks.push(row);\n            }\n            component.blocksPerLine = blocksPerLine;\n            component.blocksPerColumn = blocksPerColumn;\n            component.blocks = blocks;\n          }\n        }\n        frame.maxH = maxH;\n        frame.maxV = maxV;\n        frame.mcusPerLine = mcusPerLine;\n        frame.mcusPerColumn = mcusPerColumn;\n      }\n      var jfif = null;\n      var adobe = null;\n      var pixels = null;\n      var frame, resetInterval;\n      var quantizationTables = [], frames = [];\n      var huffmanTablesAC = [], huffmanTablesDC = [];\n      var fileMarker = readUint16();\n      var malformedDataOffset = -1;\n      this.comments = [];\n      if (fileMarker != 0xFFD8) { // SOI (Start of Image)\n        throw new Error(\"SOI not found\");\n      }\n\n      fileMarker = readUint16();\n      while (fileMarker != 0xFFD9) { // EOI (End of image)\n        var i, j, l;\n        switch(fileMarker) {\n          case 0xFF00: break;\n          case 0xFFE0: // APP0 (Application Specific)\n          case 0xFFE1: // APP1\n          case 0xFFE2: // APP2\n          case 0xFFE3: // APP3\n          case 0xFFE4: // APP4\n          case 0xFFE5: // APP5\n          case 0xFFE6: // APP6\n          case 0xFFE7: // APP7\n          case 0xFFE8: // APP8\n          case 0xFFE9: // APP9\n          case 0xFFEA: // APP10\n          case 0xFFEB: // APP11\n          case 0xFFEC: // APP12\n          case 0xFFED: // APP13\n          case 0xFFEE: // APP14\n          case 0xFFEF: // APP15\n          case 0xFFFE: // COM (Comment)\n            var appData = readDataBlock();\n\n            if (fileMarker === 0xFFFE) {\n              var comment = String.fromCharCode.apply(null, appData);\n              this.comments.push(comment);\n            }\n\n            if (fileMarker === 0xFFE0) {\n              if (appData[0] === 0x4A && appData[1] === 0x46 && appData[2] === 0x49 &&\n                appData[3] === 0x46 && appData[4] === 0) { // 'JFIF\\x00'\n                jfif = {\n                  version: { major: appData[5], minor: appData[6] },\n                  densityUnits: appData[7],\n                  xDensity: (appData[8] << 8) | appData[9],\n                  yDensity: (appData[10] << 8) | appData[11],\n                  thumbWidth: appData[12],\n                  thumbHeight: appData[13],\n                  thumbData: appData.subarray(14, 14 + 3 * appData[12] * appData[13])\n                };\n              }\n            }\n            // TODO APP1 - Exif\n            if (fileMarker === 0xFFE1) {\n              if (appData[0] === 0x45 &&\n                appData[1] === 0x78 &&\n                appData[2] === 0x69 &&\n                appData[3] === 0x66 &&\n                appData[4] === 0) { // 'EXIF\\x00'\n                this.exifBuffer = appData.subarray(5, appData.length);\n              }\n            }\n\n            if (fileMarker === 0xFFEE) {\n              if (appData[0] === 0x41 && appData[1] === 0x64 && appData[2] === 0x6F &&\n                appData[3] === 0x62 && appData[4] === 0x65 && appData[5] === 0) { // 'Adobe\\x00'\n                adobe = {\n                  version: appData[6],\n                  flags0: (appData[7] << 8) | appData[8],\n                  flags1: (appData[9] << 8) | appData[10],\n                  transformCode: appData[11]\n                };\n              }\n            }\n            break;\n\n          case 0xFFDB: // DQT (Define Quantization Tables)\n            var quantizationTablesLength = readUint16();\n            var quantizationTablesEnd = quantizationTablesLength + offset - 2;\n            while (offset < quantizationTablesEnd) {\n              var quantizationTableSpec = data[offset++];\n              requestMemoryAllocation(64 * 4);\n              var tableData = new Int32Array(64);\n              if ((quantizationTableSpec >> 4) === 0) { // 8 bit values\n                for (j = 0; j < 64; j++) {\n                  var z = dctZigZag[j];\n                  tableData[z] = data[offset++];\n                }\n              } else if ((quantizationTableSpec >> 4) === 1) { //16 bit\n                for (j = 0; j < 64; j++) {\n                  var z = dctZigZag[j];\n                  tableData[z] = readUint16();\n                }\n              } else\n                throw new Error(\"DQT: invalid table spec\");\n              quantizationTables[quantizationTableSpec & 15] = tableData;\n            }\n            break;\n\n          case 0xFFC0: // SOF0 (Start of Frame, Baseline DCT)\n          case 0xFFC1: // SOF1 (Start of Frame, Extended DCT)\n          case 0xFFC2: // SOF2 (Start of Frame, Progressive DCT)\n            readUint16(); // skip data length\n            frame = {};\n            frame.extended = (fileMarker === 0xFFC1);\n            frame.progressive = (fileMarker === 0xFFC2);\n            frame.precision = data[offset++];\n            frame.scanLines = readUint16();\n            frame.samplesPerLine = readUint16();\n            frame.components = {};\n            frame.componentsOrder = [];\n\n            var pixelsInFrame = frame.scanLines * frame.samplesPerLine;\n            if (pixelsInFrame > maxResolutionInPixels) {\n              var exceededAmount = Math.ceil((pixelsInFrame - maxResolutionInPixels) / 1e6);\n              throw new Error(`maxResolutionInMP limit exceeded by ${exceededAmount}MP`);\n            }\n\n            var componentsCount = data[offset++], componentId;\n            var maxH = 0, maxV = 0;\n            for (i = 0; i < componentsCount; i++) {\n              componentId = data[offset];\n              var h = data[offset + 1] >> 4;\n              var v = data[offset + 1] & 15;\n              var qId = data[offset + 2];\n\n              if ( h <= 0 || v <= 0 ) {\n                throw new Error('Invalid sampling factor, expected values above 0');\n              }\n\n              frame.componentsOrder.push(componentId);\n              frame.components[componentId] = {\n                h: h,\n                v: v,\n                quantizationIdx: qId\n              };\n              offset += 3;\n            }\n            prepareComponents(frame);\n            frames.push(frame);\n            break;\n\n          case 0xFFC4: // DHT (Define Huffman Tables)\n            var huffmanLength = readUint16();\n            for (i = 2; i < huffmanLength;) {\n              var huffmanTableSpec = data[offset++];\n              var codeLengths = new Uint8Array(16);\n              var codeLengthSum = 0;\n              for (j = 0; j < 16; j++, offset++) {\n                codeLengthSum += (codeLengths[j] = data[offset]);\n              }\n              requestMemoryAllocation(16 + codeLengthSum);\n              var huffmanValues = new Uint8Array(codeLengthSum);\n              for (j = 0; j < codeLengthSum; j++, offset++)\n                huffmanValues[j] = data[offset];\n              i += 17 + codeLengthSum;\n\n              ((huffmanTableSpec >> 4) === 0 ?\n                huffmanTablesDC : huffmanTablesAC)[huffmanTableSpec & 15] =\n                buildHuffmanTable(codeLengths, huffmanValues);\n            }\n            break;\n\n          case 0xFFDD: // DRI (Define Restart Interval)\n            readUint16(); // skip data length\n            resetInterval = readUint16();\n            break;\n\n          case 0xFFDC: // Number of Lines marker\n            readUint16() // skip data length\n            readUint16() // Ignore this data since it represents the image height\n            break;\n            \n          case 0xFFDA: // SOS (Start of Scan)\n            var scanLength = readUint16();\n            var selectorsCount = data[offset++];\n            var components = [], component;\n            for (i = 0; i < selectorsCount; i++) {\n              component = frame.components[data[offset++]];\n              var tableSpec = data[offset++];\n              component.huffmanTableDC = huffmanTablesDC[tableSpec >> 4];\n              component.huffmanTableAC = huffmanTablesAC[tableSpec & 15];\n              components.push(component);\n            }\n            var spectralStart = data[offset++];\n            var spectralEnd = data[offset++];\n            var successiveApproximation = data[offset++];\n            var processed = decodeScan(data, offset,\n              frame, components, resetInterval,\n              spectralStart, spectralEnd,\n              successiveApproximation >> 4, successiveApproximation & 15, this.opts);\n            offset += processed;\n            break;\n\n          case 0xFFFF: // Fill bytes\n            if (data[offset] !== 0xFF) { // Avoid skipping a valid marker.\n              offset--;\n            }\n            break;\n          default:\n            if (data[offset - 3] == 0xFF &&\n                data[offset - 2] >= 0xC0 && data[offset - 2] <= 0xFE) {\n              // could be incorrect encoding -- last 0xFF byte of the previous\n              // block was eaten by the encoder\n              offset -= 3;\n              break;\n            }\n            else if (fileMarker === 0xE0 || fileMarker == 0xE1) {\n              // Recover from malformed APP1 markers popular in some phone models.\n              // See https://github.com/eugeneware/jpeg-js/issues/82\n              if (malformedDataOffset !== -1) {\n                throw new Error(`first unknown JPEG marker at offset ${malformedDataOffset.toString(16)}, second unknown JPEG marker ${fileMarker.toString(16)} at offset ${(offset - 1).toString(16)}`);\n              }\n              malformedDataOffset = offset - 1;\n              const nextOffset = readUint16();\n              if (data[offset + nextOffset - 2] === 0xFF) {\n                offset += nextOffset - 2;\n                break;\n              }\n            }\n            throw new Error(\"unknown JPEG marker \" + fileMarker.toString(16));\n        }\n        fileMarker = readUint16();\n      }\n      if (frames.length != 1)\n        throw new Error(\"only single frame JPEGs supported\");\n\n      // set each frame's components quantization table\n      for (var i = 0; i < frames.length; i++) {\n        var cp = frames[i].components;\n        for (var j in cp) {\n          cp[j].quantizationTable = quantizationTables[cp[j].quantizationIdx];\n          delete cp[j].quantizationIdx;\n        }\n      }\n\n      this.width = frame.samplesPerLine;\n      this.height = frame.scanLines;\n      this.jfif = jfif;\n      this.adobe = adobe;\n      this.components = [];\n      for (var i = 0; i < frame.componentsOrder.length; i++) {\n        var component = frame.components[frame.componentsOrder[i]];\n        this.components.push({\n          lines: buildComponentData(frame, component),\n          scaleX: component.h / frame.maxH,\n          scaleY: component.v / frame.maxV\n        });\n      }\n    },\n    getData: function getData(width, height) {\n      var scaleX = this.width / width, scaleY = this.height / height;\n\n      var component1, component2, component3, component4;\n      var component1Line, component2Line, component3Line, component4Line;\n      var x, y;\n      var offset = 0;\n      var Y, Cb, Cr, K, C, M, Ye, R, G, B;\n      var colorTransform;\n      var dataLength = width * height * this.components.length;\n      requestMemoryAllocation(dataLength);\n      var data = new Uint8Array(dataLength);\n      switch (this.components.length) {\n        case 1:\n          component1 = this.components[0];\n          for (y = 0; y < height; y++) {\n            component1Line = component1.lines[0 | (y * component1.scaleY * scaleY)];\n            for (x = 0; x < width; x++) {\n              Y = component1Line[0 | (x * component1.scaleX * scaleX)];\n\n              data[offset++] = Y;\n            }\n          }\n          break;\n        case 2:\n          // PDF might compress two component data in custom colorspace\n          component1 = this.components[0];\n          component2 = this.components[1];\n          for (y = 0; y < height; y++) {\n            component1Line = component1.lines[0 | (y * component1.scaleY * scaleY)];\n            component2Line = component2.lines[0 | (y * component2.scaleY * scaleY)];\n            for (x = 0; x < width; x++) {\n              Y = component1Line[0 | (x * component1.scaleX * scaleX)];\n              data[offset++] = Y;\n              Y = component2Line[0 | (x * component2.scaleX * scaleX)];\n              data[offset++] = Y;\n            }\n          }\n          break;\n        case 3:\n          // The default transform for three components is true\n          colorTransform = true;\n          // The adobe transform marker overrides any previous setting\n          if (this.adobe && this.adobe.transformCode)\n            colorTransform = true;\n          else if (typeof this.opts.colorTransform !== 'undefined')\n            colorTransform = !!this.opts.colorTransform;\n\n          component1 = this.components[0];\n          component2 = this.components[1];\n          component3 = this.components[2];\n          for (y = 0; y < height; y++) {\n            component1Line = component1.lines[0 | (y * component1.scaleY * scaleY)];\n            component2Line = component2.lines[0 | (y * component2.scaleY * scaleY)];\n            component3Line = component3.lines[0 | (y * component3.scaleY * scaleY)];\n            for (x = 0; x < width; x++) {\n              if (!colorTransform) {\n                R = component1Line[0 | (x * component1.scaleX * scaleX)];\n                G = component2Line[0 | (x * component2.scaleX * scaleX)];\n                B = component3Line[0 | (x * component3.scaleX * scaleX)];\n              } else {\n                Y = component1Line[0 | (x * component1.scaleX * scaleX)];\n                Cb = component2Line[0 | (x * component2.scaleX * scaleX)];\n                Cr = component3Line[0 | (x * component3.scaleX * scaleX)];\n\n                R = clampTo8bit(Y + 1.402 * (Cr - 128));\n                G = clampTo8bit(Y - 0.3441363 * (Cb - 128) - 0.71413636 * (Cr - 128));\n                B = clampTo8bit(Y + 1.772 * (Cb - 128));\n              }\n\n              data[offset++] = R;\n              data[offset++] = G;\n              data[offset++] = B;\n            }\n          }\n          break;\n        case 4:\n          if (!this.adobe)\n            throw new Error('Unsupported color mode (4 components)');\n          // The default transform for four components is false\n          colorTransform = false;\n          // The adobe transform marker overrides any previous setting\n          if (this.adobe && this.adobe.transformCode)\n            colorTransform = true;\n          else if (typeof this.opts.colorTransform !== 'undefined')\n            colorTransform = !!this.opts.colorTransform;\n\n          component1 = this.components[0];\n          component2 = this.components[1];\n          component3 = this.components[2];\n          component4 = this.components[3];\n          for (y = 0; y < height; y++) {\n            component1Line = component1.lines[0 | (y * component1.scaleY * scaleY)];\n            component2Line = component2.lines[0 | (y * component2.scaleY * scaleY)];\n            component3Line = component3.lines[0 | (y * component3.scaleY * scaleY)];\n            component4Line = component4.lines[0 | (y * component4.scaleY * scaleY)];\n            for (x = 0; x < width; x++) {\n              if (!colorTransform) {\n                C = component1Line[0 | (x * component1.scaleX * scaleX)];\n                M = component2Line[0 | (x * component2.scaleX * scaleX)];\n                Ye = component3Line[0 | (x * component3.scaleX * scaleX)];\n                K = component4Line[0 | (x * component4.scaleX * scaleX)];\n              } else {\n                Y = component1Line[0 | (x * component1.scaleX * scaleX)];\n                Cb = component2Line[0 | (x * component2.scaleX * scaleX)];\n                Cr = component3Line[0 | (x * component3.scaleX * scaleX)];\n                K = component4Line[0 | (x * component4.scaleX * scaleX)];\n\n                C = 255 - clampTo8bit(Y + 1.402 * (Cr - 128));\n                M = 255 - clampTo8bit(Y - 0.3441363 * (Cb - 128) - 0.71413636 * (Cr - 128));\n                Ye = 255 - clampTo8bit(Y + 1.772 * (Cb - 128));\n              }\n              data[offset++] = 255-C;\n              data[offset++] = 255-M;\n              data[offset++] = 255-Ye;\n              data[offset++] = 255-K;\n            }\n          }\n          break;\n        default:\n          throw new Error('Unsupported color mode');\n      }\n      return data;\n    },\n    copyToImageData: function copyToImageData(imageData, formatAsRGBA) {\n      var width = imageData.width, height = imageData.height;\n      var imageDataArray = imageData.data;\n      var data = this.getData(width, height);\n      var i = 0, j = 0, x, y;\n      var Y, K, C, M, R, G, B;\n      switch (this.components.length) {\n        case 1:\n          for (y = 0; y < height; y++) {\n            for (x = 0; x < width; x++) {\n              Y = data[i++];\n\n              imageDataArray[j++] = Y;\n              imageDataArray[j++] = Y;\n              imageDataArray[j++] = Y;\n              if (formatAsRGBA) {\n                imageDataArray[j++] = 255;\n              }\n            }\n          }\n          break;\n        case 3:\n          for (y = 0; y < height; y++) {\n            for (x = 0; x < width; x++) {\n              R = data[i++];\n              G = data[i++];\n              B = data[i++];\n\n              imageDataArray[j++] = R;\n              imageDataArray[j++] = G;\n              imageDataArray[j++] = B;\n              if (formatAsRGBA) {\n                imageDataArray[j++] = 255;\n              }\n            }\n          }\n          break;\n        case 4:\n          for (y = 0; y < height; y++) {\n            for (x = 0; x < width; x++) {\n              C = data[i++];\n              M = data[i++];\n              Y = data[i++];\n              K = data[i++];\n\n              R = 255 - clampTo8bit(C * (1 - K / 255) + K);\n              G = 255 - clampTo8bit(M * (1 - K / 255) + K);\n              B = 255 - clampTo8bit(Y * (1 - K / 255) + K);\n\n              imageDataArray[j++] = R;\n              imageDataArray[j++] = G;\n              imageDataArray[j++] = B;\n              if (formatAsRGBA) {\n                imageDataArray[j++] = 255;\n              }\n            }\n          }\n          break;\n        default:\n          throw new Error('Unsupported color mode');\n      }\n    }\n  };\n\n\n  // We cap the amount of memory used by jpeg-js to avoid unexpected OOMs from untrusted content.\n  var totalBytesAllocated = 0;\n  var maxMemoryUsageBytes = 0;\n  function requestMemoryAllocation(increaseAmount = 0) {\n    var totalMemoryImpactBytes = totalBytesAllocated + increaseAmount;\n    if (totalMemoryImpactBytes > maxMemoryUsageBytes) {\n      var exceededAmount = Math.ceil((totalMemoryImpactBytes - maxMemoryUsageBytes) / 1024 / 1024);\n      throw new Error(`maxMemoryUsageInMB limit exceeded by at least ${exceededAmount}MB`);\n    }\n\n    totalBytesAllocated = totalMemoryImpactBytes;\n  }\n\n  constructor.resetMaxMemoryUsage = function (maxMemoryUsageBytes_) {\n    totalBytesAllocated = 0;\n    maxMemoryUsageBytes = maxMemoryUsageBytes_;\n  };\n\n  constructor.getBytesAllocated = function () {\n    return totalBytesAllocated;\n  };\n\n  constructor.requestMemoryAllocation = requestMemoryAllocation;\n\n  return constructor;\n})();\n\nif (typeof module !== 'undefined') {\n\tmodule.exports = decode;\n} else if (typeof window !== 'undefined') {\n\twindow['jpeg-js'] = window['jpeg-js'] || {};\n\twindow['jpeg-js'].decode = decode;\n}\n\nfunction decode(jpegData, userOpts = {}) {\n  var defaultOpts = {\n    // \"undefined\" means \"Choose whether to transform colors based on the image’s color model.\"\n    colorTransform: undefined,\n    useTArray: false,\n    formatAsRGBA: true,\n    tolerantDecoding: true,\n    maxResolutionInMP: 100, // Don't decode more than 100 megapixels\n    maxMemoryUsageInMB: 512, // Don't decode if memory footprint is more than 512MB\n  };\n\n  var opts = {...defaultOpts, ...userOpts};\n  var arr = new Uint8Array(jpegData);\n  var decoder = new JpegImage();\n  decoder.opts = opts;\n  // If this constructor ever supports async decoding this will need to be done differently.\n  // Until then, treating as singleton limit is fine.\n  JpegImage.resetMaxMemoryUsage(opts.maxMemoryUsageInMB * 1024 * 1024);\n  decoder.parse(arr);\n\n  var channels = (opts.formatAsRGBA) ? 4 : 3;\n  var bytesNeeded = decoder.width * decoder.height * channels;\n  try {\n    JpegImage.requestMemoryAllocation(bytesNeeded);\n    var image = {\n      width: decoder.width,\n      height: decoder.height,\n      exifBuffer: decoder.exifBuffer,\n      data: opts.useTArray ?\n        new Uint8Array(bytesNeeded) :\n        Buffer.alloc(bytesNeeded)\n    };\n    if(decoder.comments.length > 0) {\n      image[\"comments\"] = decoder.comments;\n    }\n  } catch (err) {\n    if (err instanceof RangeError) {\n      throw new Error(\"Could not allocate enough memory for the image. \" +\n                      \"Required: \" + bytesNeeded);\n    } \n    \n    if (err instanceof ReferenceError) {\n      if (err.message === \"Buffer is not defined\") {\n        throw new Error(\"Buffer is not globally defined in this environment. \" +\n                        \"Consider setting useTArray to true\");\n      }\n    }\n    throw err;\n  }\n\n  decoder.copyToImageData(image, opts.formatAsRGBA);\n\n  return image;\n}\n"]}