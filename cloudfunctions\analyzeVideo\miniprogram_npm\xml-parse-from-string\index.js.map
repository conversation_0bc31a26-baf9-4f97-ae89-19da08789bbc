{"version": 3, "sources": ["index.js"], "names": [], "mappings": ";;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.js", "sourcesContent": ["module.exports = (function xmlparser() {\n  //common browsers\n  if (typeof self.DOMParser !== 'undefined') {\n    return function(str) {\n      var parser = new self.DOMParser()\n      return parser.parseFromString(str, 'application/xml')\n    }\n  } \n\n  //IE8 fallback\n  if (typeof self.ActiveXObject !== 'undefined'\n      && new self.ActiveXObject('Microsoft.XMLDOM')) {\n    return function(str) {\n      var xmlDoc = new self.ActiveXObject(\"Microsoft.XMLDOM\")\n      xmlDoc.async = \"false\"\n      xmlDoc.loadXML(str)\n      return xmlDoc\n    }\n  }\n\n  //last resort fallback\n  return function(str) {\n    var div = document.createElement('div')\n    div.innerHTML = str\n    return div\n  }\n})()\n"]}