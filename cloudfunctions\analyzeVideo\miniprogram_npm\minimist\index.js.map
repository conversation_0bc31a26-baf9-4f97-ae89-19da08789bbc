{"version": 3, "sources": ["index.js"], "names": [], "mappings": ";;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.js", "sourcesContent": ["\n\nfunction hasKey(obj, keys) {\n\tvar o = obj;\n\tkeys.slice(0, -1).forEach(function (key) {\n\t\to = o[key] || {};\n\t});\n\n\tvar key = keys[keys.length - 1];\n\treturn key in o;\n}\n\nfunction isNumber(x) {\n\tif (typeof x === 'number') { return true; }\n\tif ((/^0x[0-9a-f]+$/i).test(x)) { return true; }\n\treturn (/^[-+]?(?:\\d+(?:\\.\\d*)?|\\.\\d+)(e[-+]?\\d+)?$/).test(x);\n}\n\nfunction isConstructorOrProto(obj, key) {\n\treturn (key === 'constructor' && typeof obj[key] === 'function') || key === '__proto__';\n}\n\nmodule.exports = function (args, opts) {\n\tif (!opts) { opts = {}; }\n\n\tvar flags = {\n\t\tbools: {},\n\t\tstrings: {},\n\t\tunknownFn: null,\n\t};\n\n\tif (typeof opts.unknown === 'function') {\n\t\tflags.unknownFn = opts.unknown;\n\t}\n\n\tif (typeof opts.boolean === 'boolean' && opts.boolean) {\n\t\tflags.allBools = true;\n\t} else {\n\t\t[].concat(opts.boolean).filter(Boolean).forEach(function (key) {\n\t\t\tflags.bools[key] = true;\n\t\t});\n\t}\n\n\tvar aliases = {};\n\n\tfunction aliasIsBoolean(key) {\n\t\treturn aliases[key].some(function (x) {\n\t\t\treturn flags.bools[x];\n\t\t});\n\t}\n\n\tObject.keys(opts.alias || {}).forEach(function (key) {\n\t\taliases[key] = [].concat(opts.alias[key]);\n\t\taliases[key].forEach(function (x) {\n\t\t\taliases[x] = [key].concat(aliases[key].filter(function (y) {\n\t\t\t\treturn x !== y;\n\t\t\t}));\n\t\t});\n\t});\n\n\t[].concat(opts.string).filter(Boolean).forEach(function (key) {\n\t\tflags.strings[key] = true;\n\t\tif (aliases[key]) {\n\t\t\t[].concat(aliases[key]).forEach(function (k) {\n\t\t\t\tflags.strings[k] = true;\n\t\t\t});\n\t\t}\n\t});\n\n\tvar defaults = opts.default || {};\n\n\tvar argv = { _: [] };\n\n\tfunction argDefined(key, arg) {\n\t\treturn (flags.allBools && (/^--[^=]+$/).test(arg))\n\t\t\t|| flags.strings[key]\n\t\t\t|| flags.bools[key]\n\t\t\t|| aliases[key];\n\t}\n\n\tfunction setKey(obj, keys, value) {\n\t\tvar o = obj;\n\t\tfor (var i = 0; i < keys.length - 1; i++) {\n\t\t\tvar key = keys[i];\n\t\t\tif (isConstructorOrProto(o, key)) { return; }\n\t\t\tif (o[key] === undefined) { o[key] = {}; }\n\t\t\tif (\n\t\t\t\to[key] === Object.prototype\n\t\t\t\t|| o[key] === Number.prototype\n\t\t\t\t|| o[key] === String.prototype\n\t\t\t) {\n\t\t\t\to[key] = {};\n\t\t\t}\n\t\t\tif (o[key] === Array.prototype) { o[key] = []; }\n\t\t\to = o[key];\n\t\t}\n\n\t\tvar lastKey = keys[keys.length - 1];\n\t\tif (isConstructorOrProto(o, lastKey)) { return; }\n\t\tif (\n\t\t\to === Object.prototype\n\t\t\t|| o === Number.prototype\n\t\t\t|| o === String.prototype\n\t\t) {\n\t\t\to = {};\n\t\t}\n\t\tif (o === Array.prototype) { o = []; }\n\t\tif (o[lastKey] === undefined || flags.bools[lastKey] || typeof o[lastKey] === 'boolean') {\n\t\t\to[lastKey] = value;\n\t\t} else if (Array.isArray(o[lastKey])) {\n\t\t\to[lastKey].push(value);\n\t\t} else {\n\t\t\to[lastKey] = [o[lastKey], value];\n\t\t}\n\t}\n\n\tfunction setArg(key, val, arg) {\n\t\tif (arg && flags.unknownFn && !argDefined(key, arg)) {\n\t\t\tif (flags.unknownFn(arg) === false) { return; }\n\t\t}\n\n\t\tvar value = !flags.strings[key] && isNumber(val)\n\t\t\t? Number(val)\n\t\t\t: val;\n\t\tsetKey(argv, key.split('.'), value);\n\n\t\t(aliases[key] || []).forEach(function (x) {\n\t\t\tsetKey(argv, x.split('.'), value);\n\t\t});\n\t}\n\n\tObject.keys(flags.bools).forEach(function (key) {\n\t\tsetArg(key, defaults[key] === undefined ? false : defaults[key]);\n\t});\n\n\tvar notFlags = [];\n\n\tif (args.indexOf('--') !== -1) {\n\t\tnotFlags = args.slice(args.indexOf('--') + 1);\n\t\targs = args.slice(0, args.indexOf('--'));\n\t}\n\n\tfor (var i = 0; i < args.length; i++) {\n\t\tvar arg = args[i];\n\t\tvar key;\n\t\tvar next;\n\n\t\tif ((/^--.+=/).test(arg)) {\n\t\t\t// Using [\\s\\S] instead of . because js doesn't support the\n\t\t\t// 'dotall' regex modifier. See:\n\t\t\t// http://stackoverflow.com/a/1068308/13216\n\t\t\tvar m = arg.match(/^--([^=]+)=([\\s\\S]*)$/);\n\t\t\tkey = m[1];\n\t\t\tvar value = m[2];\n\t\t\tif (flags.bools[key]) {\n\t\t\t\tvalue = value !== 'false';\n\t\t\t}\n\t\t\tsetArg(key, value, arg);\n\t\t} else if ((/^--no-.+/).test(arg)) {\n\t\t\tkey = arg.match(/^--no-(.+)/)[1];\n\t\t\tsetArg(key, false, arg);\n\t\t} else if ((/^--.+/).test(arg)) {\n\t\t\tkey = arg.match(/^--(.+)/)[1];\n\t\t\tnext = args[i + 1];\n\t\t\tif (\n\t\t\t\tnext !== undefined\n\t\t\t\t&& !(/^(-|--)[^-]/).test(next)\n\t\t\t\t&& !flags.bools[key]\n\t\t\t\t&& !flags.allBools\n\t\t\t\t&& (aliases[key] ? !aliasIsBoolean(key) : true)\n\t\t\t) {\n\t\t\t\tsetArg(key, next, arg);\n\t\t\t\ti += 1;\n\t\t\t} else if ((/^(true|false)$/).test(next)) {\n\t\t\t\tsetArg(key, next === 'true', arg);\n\t\t\t\ti += 1;\n\t\t\t} else {\n\t\t\t\tsetArg(key, flags.strings[key] ? '' : true, arg);\n\t\t\t}\n\t\t} else if ((/^-[^-]+/).test(arg)) {\n\t\t\tvar letters = arg.slice(1, -1).split('');\n\n\t\t\tvar broken = false;\n\t\t\tfor (var j = 0; j < letters.length; j++) {\n\t\t\t\tnext = arg.slice(j + 2);\n\n\t\t\t\tif (next === '-') {\n\t\t\t\t\tsetArg(letters[j], next, arg);\n\t\t\t\t\tcontinue;\n\t\t\t\t}\n\n\t\t\t\tif ((/[A-Za-z]/).test(letters[j]) && next[0] === '=') {\n\t\t\t\t\tsetArg(letters[j], next.slice(1), arg);\n\t\t\t\t\tbroken = true;\n\t\t\t\t\tbreak;\n\t\t\t\t}\n\n\t\t\t\tif (\n\t\t\t\t\t(/[A-Za-z]/).test(letters[j])\n\t\t\t\t\t&& (/-?\\d+(\\.\\d*)?(e-?\\d+)?$/).test(next)\n\t\t\t\t) {\n\t\t\t\t\tsetArg(letters[j], next, arg);\n\t\t\t\t\tbroken = true;\n\t\t\t\t\tbreak;\n\t\t\t\t}\n\n\t\t\t\tif (letters[j + 1] && letters[j + 1].match(/\\W/)) {\n\t\t\t\t\tsetArg(letters[j], arg.slice(j + 2), arg);\n\t\t\t\t\tbroken = true;\n\t\t\t\t\tbreak;\n\t\t\t\t} else {\n\t\t\t\t\tsetArg(letters[j], flags.strings[letters[j]] ? '' : true, arg);\n\t\t\t\t}\n\t\t\t}\n\n\t\t\tkey = arg.slice(-1)[0];\n\t\t\tif (!broken && key !== '-') {\n\t\t\t\tif (\n\t\t\t\t\targs[i + 1]\n\t\t\t\t\t&& !(/^(-|--)[^-]/).test(args[i + 1])\n\t\t\t\t\t&& !flags.bools[key]\n\t\t\t\t\t&& (aliases[key] ? !aliasIsBoolean(key) : true)\n\t\t\t\t) {\n\t\t\t\t\tsetArg(key, args[i + 1], arg);\n\t\t\t\t\ti += 1;\n\t\t\t\t} else if (args[i + 1] && (/^(true|false)$/).test(args[i + 1])) {\n\t\t\t\t\tsetArg(key, args[i + 1] === 'true', arg);\n\t\t\t\t\ti += 1;\n\t\t\t\t} else {\n\t\t\t\t\tsetArg(key, flags.strings[key] ? '' : true, arg);\n\t\t\t\t}\n\t\t\t}\n\t\t} else {\n\t\t\tif (!flags.unknownFn || flags.unknownFn(arg) !== false) {\n\t\t\t\targv._.push(flags.strings._ || !isNumber(arg) ? arg : Number(arg));\n\t\t\t}\n\t\t\tif (opts.stopEarly) {\n\t\t\t\targv._.push.apply(argv._, args.slice(i + 1));\n\t\t\t\tbreak;\n\t\t\t}\n\t\t}\n\t}\n\n\tObject.keys(defaults).forEach(function (k) {\n\t\tif (!hasKey(argv, k.split('.'))) {\n\t\t\tsetKey(argv, k.split('.'), defaults[k]);\n\n\t\t\t(aliases[k] || []).forEach(function (x) {\n\t\t\t\tsetKey(argv, x.split('.'), defaults[k]);\n\t\t\t});\n\t\t}\n\t});\n\n\tif (opts['--']) {\n\t\targv['--'] = notFlags.slice();\n\t} else {\n\t\tnotFlags.forEach(function (k) {\n\t\t\targv._.push(k);\n\t\t});\n\t}\n\n\treturn argv;\n};\n"]}