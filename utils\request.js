const config = require('./config');

// 请求重试次数
const MAX_RETRY_COUNT = 3;

class Request {
  constructor() {
    this.baseUrl = config.url;
  }

  // 处理请求错误
  handleError(error, reject) {
    console.error('请求错误：', error);
    wx.showToast({
      title: '网络错误，请稍后重试',
      icon: 'none',
      duration: 2000
    });
    reject(error);
  }

  // 发起网络请求的核心方法
  request(options, retryCount = 0) {
    return new Promise((resolve, reject) => {
      const token = wx.getStorageSync(config.storageKeys.token);
      
      wx.request({
        url: options.url,
        method: options.method || 'GET',
        data: options.data,
        header: {
          'content-type': 'application/json',
          'Authorization': token ? `Bearer ${token}` : '',
          ...options.header
        },
        timeout: config.defaults.requestTimeout,
        success: (res) => {
          if (res.statusCode >= 200 && res.statusCode < 300) {
            resolve(res.data);
          } else if (res.statusCode === 401) {
            // token过期，需要重新登录
            wx.removeStorageSync(config.storageKeys.token);
            wx.redirectTo({ url: '/pages/login/login' });
            reject(new Error('未授权，请重新登录'));
          } else {
            if (retryCount < MAX_RETRY_COUNT) {
              // 请求失败后重试
              setTimeout(() => {
                this.request(options, retryCount + 1)
                  .then(resolve)
                  .catch(reject);
              }, 1000 * (retryCount + 1));
            } else {
              this.handleError(res, reject);
            }
          }
        },
        fail: (error) => {
          if (retryCount < MAX_RETRY_COUNT) {
            setTimeout(() => {
              this.request(options, retryCount + 1)
                .then(resolve)
                .catch(reject);
            }, 1000 * (retryCount + 1));
          } else {
            this.handleError(error, reject);
          }
        }
      });
    });
  }

  // GET请求
  get(url, data = {}, options = {}) {
    return this.request({
      url,
      method: 'GET',
      data,
      ...options
    });
  }

  // POST请求
  post(url, data = {}, options = {}) {
    return this.request({
      url,
      method: 'POST',
      data,
      ...options
    });
  }

  // PUT请求
  put(url, data = {}, options = {}) {
    return this.request({
      url,
      method: 'PUT',
      data,
      ...options
    });
  }

  // DELETE请求
  delete(url, data = {}, options = {}) {
    return this.request({
      url,
      method: 'DELETE',
      data,
      ...options
    });
  }
}

module.exports = new Request(); 