// 云函数入口文件
const cloud = require('wx-server-sdk')

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})

// 用户数据和验证码存储路径
const USERS_PATH = 'users'
const VERIFY_CODES_PATH = 'verify_codes'

// 生成6位随机验证码
function generateVerifyCode() {
  return Math.floor(100000 + Math.random() * 900000).toString()
}

// 云函数入口函数
exports.main = async (event, context) => {
  console.log('接收到的参数:', event)
  const wxContext = cloud.getWXContext()
  const openid = wxContext.OPENID
  const { action, phoneNumber, verifyCode } = event

  try {
    // 根据action执行不同操作
    switch (action) {
      case 'getWxPhone': {
        try {
          // 解密手机号
          const result = await cloud.openapi.cloudbase.getOpenData({
            openData: [event.cloudID]
          });
          
          if (result && result.dataList && result.dataList[0].data) {
            const phoneInfo = result.dataList[0].data;
            return {
              success: true,
              phoneNumber: phoneInfo.phoneNumber,
              message: '获取成功'
            };
          } else {
            return {
              success: false,
              message: '获取手机号失败'
            };
          }
        } catch (err) {
          console.error('获取手机号失败:', err);
          return {
            success: false,
            message: '获取手机号失败'
          };
        }
      }

      case 'sendCode': {
        // 验证手机号格式
        if (!/^1[3-9]\d{9}$/.test(phoneNumber)) {
          return {
            success: false,
            message: '手机号格式不正确'
          }
        }

        // 生成验证码
        const code = generateVerifyCode()
        console.log('生成的验证码:', code)

        // 由于没有短信功能，直接返回验证码
        return {
          success: true,
          message: '验证码生成成功',
          verifyCode: code
        }
      }

      case 'verifyAndBind': {
        // 验证手机号格式
        if (!/^1[3-9]\d{9}$/.test(phoneNumber)) {
          return {
            success: false,
            message: '手机号格式不正确'
          }
        }

        // 验证验证码格式
        if (!/^\d{6}$/.test(verifyCode)) {
          return {
            success: false,
            message: '验证码格式不正确'
          }
        }

        try {
          // 更新用户信息
          const userDataPath = `${USERS_PATH}/${openid}/profile.json`
          const userData = {
            phoneNumber: phoneNumber,
            updateTime: new Date().toISOString()
          }

          await cloud.uploadFile({
            cloudPath: userDataPath,
            fileContent: Buffer.from(JSON.stringify(userData))
          })

          return {
            success: true,
            message: '手机号绑定成功'
          }
        } catch (err) {
          console.error('绑定手机号失败:', err)
          return {
            success: false,
            message: '绑定失败'
          }
        }
      }

      case 'unbind': {
        try {
          // 更新用户信息，移除手机号
          const userDataPath = `${USERS_PATH}/${openid}/profile.json`
          const userData = {
            phoneNumber: null,
            updateTime: new Date().toISOString()
          }

          await cloud.uploadFile({
            cloudPath: userDataPath,
            fileContent: Buffer.from(JSON.stringify(userData))
          })

          return {
            success: true,
            message: '手机号解绑成功'
          }
        } catch (err) {
          console.error('解绑手机号失败:', err)
          return {
            success: false,
            message: '解绑失败'
          }
        }
      }

      default:
        return {
          success: false,
          message: '未知的操作类型'
        }
    }
  } catch (err) {
    console.error('操作失败：', err)
    return {
      success: false,
      message: err.message || '操作失败'
    }
  }
} 