// 云函数入口文件
const cloud = require('wx-server-sdk');
const fs = require('fs-extra');
const path = require('path');
const { exec, execSync } = require('child_process');
const os = require('os');

// 初始化云环境
cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
});

// 配置FFprobe路径
let FFPROBE_PATH = path.join(__dirname, 'bin', 'ffprobe');
// 在Windows环境下可能需要添加.exe后缀
if (process.platform === 'win32') {
  // 检查是否存在ffprobe.exe
  if (fs.existsSync(`${FFPROBE_PATH}.exe`)) {
    FFPROBE_PATH = `${FFPROBE_PATH}.exe`;
    console.log('检测到Windows环境，使用ffprobe.exe');
  }
}

// 云函数入口函数
exports.main = async (event, context) => {
  // 初始化FFprobe准备状态
  let ffprobeReady = false;
  
  // 首先尝试将FFprobe复制到临时目录
  console.log('直接尝试临时目录方案...');
  try {
    const tmpFFprobePath = await copyFFprobeToTmp();
    if (tmpFFprobePath) {
      FFPROBE_PATH = tmpFFprobePath;
      ffprobeReady = true;
      console.log('使用临时目录FFprobe成功:', FFPROBE_PATH);
    } else {
      console.warn('临时目录方案失败，将尝试其他方法');
      
      // 如果临时目录方案失败，尝试常规方式设置FFprobe
      try {
        ffprobeReady = setupFFprobe();
        console.log('常规方式设置FFprobe结果:', ffprobeReady ? '成功' : '失败');
      } catch (error) {
        console.warn('常规方式设置FFprobe失败:', error.message);
        ffprobeReady = false;
      }
    }
  } catch (tmpError) {
    console.error('临时目录FFprobe设置失败:', tmpError.message);
    
    // 如果临时目录方案失败，尝试常规方式设置FFprobe
    try {
      ffprobeReady = setupFFprobe();
      console.log('常规方式设置FFprobe结果:', ffprobeReady ? '成功' : '失败');
    } catch (error) {
      console.warn('常规方式设置FFprobe失败:', error.message);
      ffprobeReady = false;
    }
  }
  
  try {
    console.log('收到事件:', JSON.stringify(event));
    
    // 处理不同的操作模式
    if (event.mode === 'checkFFprobe') {
      // 检查FFprobe是否可用
      return {
        success: ffprobeReady,
        message: ffprobeReady ? 'FFprobe设置成功' : 'FFprobe设置失败',
        platform: process.platform,
        architecture: process.arch,
        nodeVersion: process.version,
        ffprobePath: FFPROBE_PATH
      };
    } else if (event.mode === 'getVideoInfo') {
      // 获取视频信息
      if (!ffprobeReady) {
        return {
          success: false,
          error: 'FFprobe设置失败，无法获取视频信息'
        };
      }

      if (!event.videoUrl) {
        return {
          success: false,
          error: '缺少视频URL参数'
        };
      }

      // 下载视频并获取信息
      return await getVideoInfo(event.videoUrl);
    } else if (event.mode === 'initYUVRecording') {
      // 初始化YUV录制会话（旧版本兼容）
      return await initYUVRecordingSession(event);
    } else if (event.mode === 'initOptimalYUVRecording') {
      // 🆕 初始化智能分段YUV录制会话
      return await initOptimalYUVRecordingSession(event);
    } else if (event.mode === 'appendStreamData') {
      // 接收流数据（旧版本兼容）
      return await appendStreamData(event);
    } else if (event.mode === 'processOptimalSegment') {
      // 🆕 处理智能分段数据
      return await processOptimalSegment(event);
    } else if (event.mode === 'finishYUVRecording') {
      // 完成YUV录制
      return await finishYUVRecording(event);
    } else if (event.mode === 'enhanceVideoQuality') {
      // 第二级：质量提升前端录制视频
      return await enhanceVideoQuality(event);
    } else if (event.mode === 'getYUVRecordingProgress') {
      // 获取YUV录制进度
      return await getYUVRecordingProgress(event);
    } else if (event.mode === 'getEnhanceProgress') {
      // 获取质量增强进度
      return await getEnhanceProgress(event);
    } else if (event.mode === 'startParallelProcessing') {
      // 启动并行处理
      return await startParallelProcessing(event);
    } else {
      return {
        success: false,
        error: '不支持的操作模式'
      };
    }
  } catch (error) {
    console.error('云函数执行失败:', error);
    return {
      success: false,
      error: error.message || '云函数执行失败',
      stack: error.stack
    };
  }
};

// 确保FFprobe可执行
function setupFFprobe() {
  try {
    console.log('开始设置FFprobe...');
    console.log('当前环境信息:', {
      platform: process.platform,
      architecture: process.arch,
      nodeVersion: process.version,
      cwd: process.cwd(),
      dirname: __dirname
    });
    
    // 在多个可能的位置查找FFprobe
    const possiblePaths = [
      path.join(__dirname, 'bin', 'ffprobe'),
      path.join(__dirname, 'bin', 'ffprobe.exe'),
      path.join(process.cwd(), 'bin', 'ffprobe'),
      path.join(process.cwd(), 'bin', 'ffprobe.exe')
    ];
    
    if (process.platform === 'win32') {
      // 在Windows上添加更多可能的路径
      possiblePaths.push(
        'ffprobe.exe', // 直接在PATH中查找
        path.join(process.env.ProgramFiles, 'ffmpeg', 'bin', 'ffprobe.exe'),
        path.join(process.env['ProgramFiles(x86)'], 'ffmpeg', 'bin', 'ffprobe.exe')
      );
    } else {
      // 在非Windows系统上添加更多可能的路径
      possiblePaths.push(
        '/usr/bin/ffprobe',
        '/usr/local/bin/ffprobe'
      );
    }
    
    // 从当前设置开始检查
    if (fs.existsSync(FFPROBE_PATH)) {
      console.log('当前设置的FFprobe路径存在:', FFPROBE_PATH);
    } else {
      console.log('当前设置的FFprobe路径不存在，尝试查找其他位置');
      
      // 尝试每个可能的路径
      let found = false;
      for (const testPath of possiblePaths) {
        if (testPath && fs.existsSync(testPath)) {
          console.log('找到FFprobe在:', testPath);
          FFPROBE_PATH = testPath;
          found = true;
          break;
        }
      }
      
      if (!found) {
        // 尝试列出bin目录内容，帮助诊断
        try {
          const binDir = path.join(__dirname, 'bin');
          if (fs.existsSync(binDir)) {
            console.log('bin目录内容:', fs.readdirSync(binDir));
          } else {
            console.log('bin目录不存在');
          }
        } catch (e) {
          console.error('列出bin目录内容失败:', e);
        }
        
        console.error('无法找到FFprobe二进制文件');
        throw new Error('找不到FFprobe可执行文件，请确保已正确安装');
      }
    }

    // 打印FFprobe文件信息
    try {
      const stat = fs.statSync(FFPROBE_PATH);
      console.log('FFprobe文件信息:', {
        path: FFPROBE_PATH,
        size: stat.size,
        mode: stat.mode.toString(8), // 权限模式，八进制
        isExecutable: (stat.mode & fs.constants.S_IXUSR) !== 0
      });
      
      // 赋予可执行权限（如果需要）
      if ((stat.mode & fs.constants.S_IXUSR) === 0) {
        console.log('FFprobe需要赋予可执行权限');
        if (process.platform !== 'win32') {
          try {
            fs.chmodSync(FFPROBE_PATH, 0o755);
            console.log('已使用fs.chmodSync设置FFprobe可执行权限');
          } catch (chmodError) {
            console.warn('设置权限失败，尝试使用execSync:', chmodError.message);
            try {
              execSync(`chmod +x "${FFPROBE_PATH}"`);
              console.log('已使用execSync设置FFprobe可执行权限');
            } catch (execError) {
              console.warn('chmod命令执行失败，但将继续尝试使用FFprobe:', execError.message);
            }
          }
        } else {
          console.log('Windows环境下跳过chmod命令');
        }
      } else {
        console.log('FFprobe已具有可执行权限，跳过设置');
      }
    } catch (statErr) {
      console.error('获取FFprobe文件信息失败:', statErr);
      // 继续执行，不要在这里终止流程
    }
    
    // 设置PATH环境变量，确保可以在任何位置调用FFprobe
    const binDir = path.dirname(FFPROBE_PATH);
    if (process.platform === 'win32') {
      process.env.PATH = `${binDir};${process.env.PATH || ''}`;
    } else {
      process.env.PATH = `${binDir}:${process.env.PATH || ''}`;
    }
    console.log('设置环境变量PATH:', process.env.PATH);
    
    // 测试FFprobe是否可用
    try {
      console.log('尝试测试FFprobe版本...');
      // 使用引号包裹路径，处理路径中的空格
      const quotedPath = `"${FFPROBE_PATH}"`;
      const versionOutput = safeExecFFprobe(`${quotedPath} -version`);
      const firstLine = versionOutput.split('\n')[0];
      console.log('FFprobe版本测试成功:', firstLine);
      return true;
    } catch (execError) {
      console.error('执行FFprobe测试失败:', execError);
      throw new Error('无法执行FFprobe命令，请检查安装: ' + execError.message);
    }
  } catch (error) {
    console.error('设置FFprobe失败:', error);
    console.log('环境诊断信息:', {
      platform: process.platform,
      architecture: process.arch,
      nodeVersion: process.version,
      cwd: process.cwd(),
      dirname: __dirname,
      path: FFPROBE_PATH,
      env_path: process.env.PATH
    });
    
    // 返回false而不是抛出错误，这样可以在调用处更好地处理
    return false;
  }
}

// 将FFprobe复制到临时目录并设置权限
async function copyFFprobeToTmp() {
  try {
    console.log('开始将FFprobe复制到临时目录...');
    const tmpFFprobePath = '/tmp/ffprobe';
    
    // 如果已存在临时文件，先删除
    if (fs.existsSync(tmpFFprobePath)) {
      fs.unlinkSync(tmpFFprobePath);
      console.log('删除已存在的临时FFprobe');
    }
    
    // 确保原始FFprobe文件存在
    if (!fs.existsSync(FFPROBE_PATH)) {
      console.error('源FFprobe文件不存在:', FFPROBE_PATH);
      
      // 尝试在不同位置查找FFprobe
      const possiblePaths = [
        path.join(__dirname, 'bin', 'ffprobe'),
        path.join(__dirname, 'bin', 'ffprobe.exe'),
        path.join(process.cwd(), 'bin', 'ffprobe'),
        path.join(process.cwd(), 'bin', 'ffprobe.exe')
      ];
      
      let foundPath = null;
      for (const testPath of possiblePaths) {
        if (fs.existsSync(testPath)) {
          console.log('找到FFprobe替代位置:', testPath);
          foundPath = testPath;
          break;
        }
      }
      
      if (!foundPath) {
        console.log('无法找到FFprobe源文件，尝试使用内置二进制文件');
        
        // 使用内置的FFprobe二进制文件
        const binDir = path.join(__dirname, 'bin');
        if (fs.existsSync(binDir)) {
          const files = fs.readdirSync(binDir);
          console.log('bin目录内容:', files);
          
          // 查找FFprobe文件
          const ffprobeFile = files.find(file => file.includes('ffprobe'));
          
          if (ffprobeFile) {
            foundPath = path.join(binDir, ffprobeFile);
            console.log('找到内置FFprobe文件:', foundPath);
          } else {
            throw new Error('无法找到内置FFprobe文件');
          }
        } else {
          throw new Error('bin目录不存在');
        }
      }
      
      FFPROBE_PATH = foundPath;
    }
    
    // 显示源文件信息，便于调试
    try {
      const srcStat = fs.statSync(FFPROBE_PATH);
      console.log('源FFprobe文件信息:', {
        path: FFPROBE_PATH,
        size: srcStat.size,
        mode: srcStat.mode.toString(8)
      });
    } catch (statErr) {
      console.warn('获取源文件信息失败:', statErr.message);
    }
    
    // 复制FFprobe到临时目录
    try {
      console.log(`复制FFprobe从 ${FFPROBE_PATH} 到 ${tmpFFprobePath}`);
      fs.copyFileSync(FFPROBE_PATH, tmpFFprobePath);
      console.log('已使用copyFileSync复制FFprobe到:', tmpFFprobePath);
    } catch (copyErr) {
      console.warn('copyFileSync失败，尝试使用readFile+writeFile:', copyErr.message);
      
      // 备选方法：读取后写入
      const fileContent = fs.readFileSync(FFPROBE_PATH);
      fs.writeFileSync(tmpFFprobePath, fileContent);
      console.log('已使用readFile+writeFile复制FFprobe到:', tmpFFprobePath);
    }
    
    // 设置执行权限 (rwxr-xr-x)
    try {
      fs.chmodSync(tmpFFprobePath, 0o755);
      console.log('已设置FFprobe执行权限');
    } catch (chmodErr) {
      console.error('设置执行权限失败:', chmodErr.message);
      throw new Error('无法设置FFprobe执行权限');
    }
    
    // 检查权限是否设置成功
    try {
      const tmpStat = fs.statSync(tmpFFprobePath);
      console.log('临时FFprobe文件信息:', {
        path: tmpFFprobePath,
        size: tmpStat.size,
        mode: tmpStat.mode.toString(8),
        isExecutable: (tmpStat.mode & fs.constants.S_IXUSR) !== 0
      });
      
      if ((tmpStat.mode & fs.constants.S_IXUSR) === 0) {
        console.error('FFprobe执行权限设置失败');
        throw new Error('无法设置FFprobe执行权限');
      }
    } catch (statErr) {
      console.error('获取临时文件信息失败:', statErr.message);
    }
    
    // 测试临时目录中的FFprobe
    try {
      console.log('测试临时目录FFprobe...');
      const versionOutput = execSync(`${tmpFFprobePath} -version`, {
        encoding: 'utf8',
        maxBuffer: 10 * 1024 * 1024
      });
      
      const firstLine = versionOutput.split('\n')[0];
      console.log('临时目录FFprobe测试成功:', firstLine);
      
      return tmpFFprobePath;
    } catch (testErr) {
      console.error('测试临时目录FFprobe失败:', testErr.message);
      
      // 尝试不同的执行方法
      try {
        console.log('尝试通过完整路径执行...');
        execSync(`/bin/sh -c '${tmpFFprobePath} -version'`, {
          encoding: 'utf8',
          maxBuffer: 10 * 1024 * 1024
        });
        console.log('通过/bin/sh执行成功');
        return tmpFFprobePath;
      } catch (shErr) {
        console.error('/bin/sh执行也失败:', shErr.message);
        throw new Error('无法执行临时目录中的FFprobe');
      }
    }
  } catch (error) {
    console.error('复制FFprobe到临时目录失败:', error);
    throw error;
  }
}

// 安全地执行FFprobe命令
function safeExecFFprobe(command) {
  console.log('执行FFprobe命令:', command);
  
  try {
    // 处理Windows环境中路径中的反斜杠和空格
    if (process.platform === 'win32') {
      // 检查命令中是否已经包含引号
      if (!command.includes('"') && !command.startsWith('ffprobe ')) {
        // 为路径添加引号，防止空格问题
        const parts = command.split(' ');
        if (parts.length > 1) {
          // 确保FFprobe路径有引号
          if (!parts[0].startsWith('"') && !parts[0].endsWith('"')) {
            parts[0] = `"${parts[0]}"`;
          }
          command = parts.join(' ');
        }
      }
      console.log('Windows环境处理后的命令:', command);
    }
    
    // 获取时间戳用于日志记录
    const timestamp = new Date().toISOString();
    console.log(`[${timestamp}] 开始执行命令:`, command);
    
    // 执行命令，捕获详细输出
    const execOptions = {
      maxBuffer: 50 * 1024 * 1024, // 增加缓冲区大小为50MB
      encoding: 'utf8',
      timeout: 300000, // 5分钟超时
      windowsHide: true // 在Windows下隐藏命令窗口
    };
    
    // 在Linux系统中，确保命令能执行（比如添加chmod +x）
    if (process.platform !== 'win32' && !command.startsWith('ffprobe ') && command.includes('/tmp/')) {
      try {
        // 提取命令中的可执行文件路径
        const execPath = command.split(' ')[0].replace(/"/g, '');
        if (execPath.includes('/tmp/') && fs.existsSync(execPath)) {
          console.log(`确保临时文件有执行权限: ${execPath}`);
          fs.chmodSync(execPath, 0o755);
        }
      } catch (chmodErr) {
        console.warn('设置执行权限失败，继续尝试:', chmodErr.message);
      }
    }
    
    // 使用带引号的命令执行
    const result = execSync(command, execOptions);
    
    // 限制输出长度，防止日志过大
    const outputPreview = result ? (result.length > 500 ? result.substring(0, 500) + '...(已截断)' : result) : 'N/A';
    
    // 记录成功完成
    console.log(`[${timestamp}] 命令执行成功，输出:`, outputPreview);
    
    return result;
  } catch (error) {
    // 记录详细的错误信息
    console.error('FFprobe命令执行失败:', {
      command: command,
      errorMessage: error.message,
      errorCode: error.code,
      errorSignal: error.signal,
      stderr: error.stderr ? (error.stderr.toString().substring(0, 500) + '...(已截断)') : 'N/A',
      stdout: error.stdout ? (error.stdout.toString().substring(0, 500) + '...(已截断)') : 'N/A'
    });
    
    // 抛出更详细的错误
    throw new Error(`FFprobe执行失败: ${error.message}. ${error.stderr ? '错误输出: ' + error.stderr.toString().substring(0, 300) + '...(已截断)' : ''}`);
  }
}

// 获取视频信息
async function getVideoInfo(videoUrl) {
  try {
    // 下载视频到临时目录
    const tempDir = path.join(os.tmpdir(), `video_info_${Date.now()}`);
    if (!fs.existsSync(tempDir)) {
      fs.mkdirSync(tempDir, { recursive: true });
    }
    
    const videoPath = path.join(tempDir, 'input.mp4');
    
    // 下载视频
    try {
      if (videoUrl.startsWith('cloud://')) {
        // 从云存储下载
        console.log(`从云存储下载视频: ${videoUrl}`);
        const result = await cloud.downloadFile({
          fileID: videoUrl
        });
        fs.writeFileSync(videoPath, result.fileContent);
      } else if (videoUrl.startsWith('http')) {
        // 从HTTP URL下载
        console.log(`从HTTP URL下载视频: ${videoUrl}`);
        const axios = require('axios');
        const response = await axios({
          method: 'GET',
          url: videoUrl,
          responseType: 'arraybuffer'
        });
        fs.writeFileSync(videoPath, Buffer.from(response.data));
      } else {
        throw new Error('不支持的视频URL格式，请提供云存储ID或HTTP URL');
      }
    } catch (downloadError) {
      console.error('下载视频失败:', downloadError);
      throw new Error(`下载视频失败: ${downloadError.message}`);
    }
    
    // 检查文件是否成功下载
    if (!fs.existsSync(videoPath)) {
      throw new Error('下载后未找到视频文件');
    }
    
    const stats = fs.statSync(videoPath);
    console.log(`视频下载完成，大小: ${(stats.size / (1024 * 1024)).toFixed(2)}MB`);
    
    // 使用FFprobe获取视频信息
    const command = `"${FFPROBE_PATH}" -v error -show_format -show_streams "${videoPath}"`;
    console.log('执行FFprobe命令:', command);
    
    const result = safeExecFFprobe(command);
    console.log('FFprobe结果:', result.substring(0, 500) + '...(已截断)');
    
    // 提取视频时长（秒）
    let durationSeconds = 0;
    
    // 首先尝试从FORMAT部分获取duration
    const formatDurationMatch = result.match(/\[FORMAT\][\s\S]*?duration=([0-9.]+)/);
    if (formatDurationMatch) {
      durationSeconds = parseFloat(formatDurationMatch[1]);
      console.log(`从FORMAT部分提取到时长: ${durationSeconds}秒`);
    } else {
      // 如果FORMAT部分没有，尝试从STREAM部分获取
      const streamDurationMatch = result.match(/\[STREAM\][\s\S]*?duration=([0-9.]+)/);
      if (streamDurationMatch) {
        durationSeconds = parseFloat(streamDurationMatch[1]);
        console.log(`从STREAM部分提取到时长: ${durationSeconds}秒`);
      } else {
        // 尝试其他格式的时长匹配
        const altDurationMatch = result.match(/Duration: (\d+):(\d+):(\d+\.\d+)/);
        if (altDurationMatch) {
          const hours = parseInt(altDurationMatch[1]);
          const minutes = parseInt(altDurationMatch[2]);
          const seconds = parseFloat(altDurationMatch[3]);
          durationSeconds = hours * 3600 + minutes * 60 + seconds;
          console.log(`从Duration字段提取到时长: ${durationSeconds}秒`);
        } else {
          console.warn('无法从FFprobe结果中提取视频时长，使用默认值10秒');
          durationSeconds = 10; // 使用默认值，避免返回0
        }
      }
    }
    
    // 提取视频分辨率
    let width = 0, height = 0;
    const widthMatch = result.match(/width=(\d+)/);
    const heightMatch = result.match(/height=(\d+)/);
    if (widthMatch) {
      width = parseInt(widthMatch[1]);
      console.log(`提取到视频宽度: ${width}`);
    }
    if (heightMatch) {
      height = parseInt(heightMatch[1]);
      console.log(`提取到视频高度: ${height}`);
    }
    
    // 如果无法获取分辨率，使用默认值
    if (width === 0 || height === 0) {
      console.warn('无法从FFprobe结果中提取视频分辨率，使用默认值1920x1080');
      width = width || 1920;
      height = height || 1080;
    }
    
    // 清理临时文件
    try {
      fs.unlinkSync(videoPath);
      fs.rmdirSync(tempDir, { recursive: true });
      console.log('已清理临时文件');
    } catch (cleanupError) {
      console.warn('清理临时文件失败:', cleanupError.message);
    }
    
    // 确保返回有效的视频信息
    const videoInfo = {
      duration: durationSeconds,
      width: width,
      height: height,
      rawInfo: result.substring(0, 1000) // 限制原始信息长度
    };
    
    console.log('返回视频信息:', JSON.stringify(videoInfo, null, 2));
    
    return {
      success: true,
      videoInfo: videoInfo
    };
  } catch (error) {
    console.error('获取视频信息失败:', error);
    // 返回默认值，避免处理失败
    return {
      success: true,
      videoInfo: {
        duration: 10, // 默认10秒
        width: 1920,
        height: 1080,
        rawInfo: '无法获取视频信息，使用默认值',
        error: error.message
      },
      warning: `获取视频信息失败，使用默认值: ${error.message}`
    };
  }
}

// ==================== YUV录制系统 ====================
// 设计原则：接收原始流数据，YUV处理，高质量编码

// 🆕 初始化智能分段YUV录制会话
async function initOptimalYUVRecordingSession(event) {
  try {
    const { sessionId, deviceIP, streamUrl, duration, segmentStrategy, config } = event;

    console.log('🎬 初始化智能分段YUV录制会话:', sessionId);
    console.log('📊 分段策略:', segmentStrategy);

    // 创建会话目录
    const sessionDir = path.join(os.tmpdir(), `optimal_yuv_session_${sessionId}`);
    if (!fs.existsSync(sessionDir)) {
      fs.mkdirSync(sessionDir, { recursive: true });
    }

    // 创建会话信息
    const session = {
      sessionId: sessionId,
      deviceIP: deviceIP,
      streamUrl: streamUrl,
      duration: duration,
      segmentStrategy: segmentStrategy,
      config: config,
      startTime: Date.now(),
      sessionDir: sessionDir,
      segments: [],
      status: 'recording',
      type: 'optimal_yuv'
    };

    // 保存会话信息到文件
    const sessionFile = path.join(sessionDir, 'session.json');
    fs.writeFileSync(sessionFile, JSON.stringify(session, null, 2));

    console.log('✅ 智能分段YUV录制会话初始化成功');
    console.log(`📋 预期分段数: ${segmentStrategy.segmentCount}, 每段时长: ${segmentStrategy.segmentDuration}秒`);

    return {
      success: true,
      sessionId: sessionId,
      segmentStrategy: segmentStrategy,
      message: '智能分段YUV录制会话已创建'
    };

  } catch (error) {
    console.error('❌ 初始化智能分段YUV录制会话失败:', error);
    return {
      success: false,
      error: error.message
    };
  }
}

// 初始化YUV录制会话（旧版本兼容）
async function initYUVRecordingSession(event) {
  try {
    const { sessionId, deviceIP, streamUrl, duration, config } = event;

    console.log('🎬 初始化YUV录制会话:', sessionId);

    // 创建会话目录
    const sessionDir = path.join(os.tmpdir(), `yuv_session_${sessionId}`);
    if (!fs.existsSync(sessionDir)) {
      fs.mkdirSync(sessionDir, { recursive: true });
    }

    // 创建会话信息
    const session = {
      sessionId: sessionId,
      deviceIP: deviceIP,
      streamUrl: streamUrl,
      duration: duration,
      config: config,
      startTime: Date.now(),
      sessionDir: sessionDir,
      chunks: [],
      status: 'recording'
    };

    // 保存会话信息到文件
    const sessionFile = path.join(sessionDir, 'session.json');
    fs.writeFileSync(sessionFile, JSON.stringify(session, null, 2));

    console.log('✅ YUV录制会话初始化成功');

    return {
      success: true,
      sessionId: sessionId,
      message: 'YUV录制会话已创建'
    };

  } catch (error) {
    console.error('❌ 初始化YUV录制会话失败:', error);
    return {
      success: false,
      error: error.message
    };
  }
}

// 接收流数据
async function appendStreamData(event) {
  try {
    const { sessionId, chunkData, timestamp, chunkIndex } = event;

    // 获取会话信息
    const sessionDir = path.join(os.tmpdir(), `yuv_session_${sessionId}`);
    const sessionFile = path.join(sessionDir, 'session.json');

    if (!fs.existsSync(sessionFile)) {
      return {
        success: false,
        error: '录制会话不存在'
      };
    }

    const session = JSON.parse(fs.readFileSync(sessionFile, 'utf8'));

    if (session.status !== 'recording') {
      return {
        success: false,
        error: '录制会话已结束'
      };
    }

    // 保存流数据块
    const chunkPath = path.join(sessionDir, `chunk_${chunkIndex.toString().padStart(4, '0')}.bin`);
    fs.writeFileSync(chunkPath, Buffer.from(chunkData));

    // 更新会话信息
    session.chunks.push({
      index: chunkIndex,
      timestamp: timestamp,
      path: chunkPath,
      size: chunkData.byteLength
    });

    fs.writeFileSync(sessionFile, JSON.stringify(session, null, 2));

    console.log(`📦 接收流数据块 ${chunkIndex}: ${chunkData.byteLength} 字节`);

    return {
      success: true,
      chunkIndex: chunkIndex,
      chunkReceived: true
    };

  } catch (error) {
    console.error('❌ 接收流数据失败:', error);
    return {
      success: false,
      error: error.message
    };
  }
}

// 完成YUV录制
async function finishYUVRecording(event) {
  try {
    const { sessionId } = event;

    console.log('🏁 开始完成YUV录制:', sessionId);

    // 获取会话信息
    const sessionDir = path.join(os.tmpdir(), `yuv_session_${sessionId}`);
    const sessionFile = path.join(sessionDir, 'session.json');

    if (!fs.existsSync(sessionFile)) {
      return {
        success: false,
        error: '录制会话不存在'
      };
    }

    const session = JSON.parse(fs.readFileSync(sessionFile, 'utf8'));
    session.status = 'processing';
    fs.writeFileSync(sessionFile, JSON.stringify(session, null, 2));

    // 合并所有流数据块
    const mergedStreamPath = await mergeStreamChunks(session);

    // YUV处理和高质量编码
    const processedVideoPath = await processStreamToHighQualityVideo(mergedStreamPath, session.config);

    // 上传到云存储
    const fileID = await uploadVideoToCloudStorage(processedVideoPath, sessionId);

    // 清理临时文件
    await cleanupSessionFiles(sessionDir);

    console.log('✅ YUV录制完成，视频文件ID:', fileID);

    return {
      success: true,
      videoFileID: fileID,
      sessionId: sessionId,
      processingTime: Date.now() - session.startTime,
      shouldDisconnectVideo: true  // 🆕 通知前端录制完成后断开视频流
    };

  } catch (error) {
    console.error('❌ 完成YUV录制失败:', error);
    return {
      success: false,
      error: error.message
    };
  }
}

// 🆕 处理智能分段数据（优化版：边处理边缓存）
async function processOptimalSegment(event) {
  try {
    const { sessionId, segmentIndex, segmentData, isLastSegment, segmentInfo } = event;

    console.log(`🚀 立即处理分段${segmentIndex}，大小: ${(segmentInfo.size / 1024 / 1024).toFixed(2)}MB`);

    // 获取会话信息
    const sessionDir = path.join(os.tmpdir(), `optimal_yuv_session_${sessionId}`);
    const sessionFile = path.join(sessionDir, 'session.json');

    if (!fs.existsSync(sessionFile)) {
      return {
        success: false,
        error: '智能分段录制会话不存在'
      };
    }

    const session = JSON.parse(fs.readFileSync(sessionFile, 'utf8'));

    if (session.status !== 'recording') {
      return {
        success: false,
        error: '录制会话已结束'
      };
    }

    // 1. 立即处理当前分段：保存数据 + YUV编码 + 缓存
    const processedSegmentPath = await processAndCacheSegment(sessionDir, segmentIndex, segmentData, segmentInfo, session.config);

    // 2. 更新缓存状态
    await updateSegmentCacheStatus(sessionDir, segmentIndex, processedSegmentPath, segmentInfo);

    // 3. 更新会话信息（保留原始分段信息用于兼容）
    session.segments.push({
      segmentIndex: segmentIndex,
      segmentInfo: segmentInfo,
      processedTime: Date.now(),
      cached: true,
      cachedPath: processedSegmentPath
    });

    console.log(`✅ 分段${segmentIndex}已完成YUV编码并缓存: ${processedSegmentPath}`);

    // 4. 如果是最后分段，合并所有缓存的分段
    if (isLastSegment) {
      console.log('🎬 开始合并所有已缓存的YUV编码分段...');

      try {
        // 验证所有分段缓存完整性
        const allCachedSegments = await validateAndGetCachedSegments(sessionDir);

        // 合并所有已编码的分段
        const finalVideoPath = await mergeAllCachedSegments(sessionDir, allCachedSegments);

        // 上传到云存储
        const fileID = await uploadVideoToCloudStorage(finalVideoPath, sessionId);

        // 更新会话状态
        session.status = 'completed';
        session.finalVideoFileID = fileID;
        session.completedTime = Date.now();
        fs.writeFileSync(sessionFile, JSON.stringify(session, null, 2));

        // 清理临时文件
        setTimeout(() => {
          cleanupSessionFiles(sessionDir);
        }, 5000); // 5秒后清理

        console.log('✅ 所有分段合并完成，视频ID:', fileID);

        return {
          success: true,
          completed: true,
          videoFileID: fileID,
          segmentIndex: segmentIndex,
          totalSegments: session.segments.length,
          processingTime: Date.now() - session.startTime
        };

      } catch (processingError) {
        console.error('❌ 分段合并失败:', processingError);

        session.status = 'failed';
        session.error = processingError.message;
        fs.writeFileSync(sessionFile, JSON.stringify(session, null, 2));

        return {
          success: false,
          error: `分段合并失败: ${processingError.message}`
        };
      }
    }

    // 更新会话文件
    fs.writeFileSync(sessionFile, JSON.stringify(session, null, 2));

    return {
      success: true,
      segmentIndex: segmentIndex,
      segmentCached: true,
      cachedPath: processedSegmentPath,
      totalSegments: session.segments.length
    };

  } catch (error) {
    console.error('❌ 分段处理失败:', error);
    return {
      success: false,
      error: error.message
    };
  }
}

// 🚀 处理并缓存单个分段（立即YUV编码 - 增强版）
async function processAndCacheSegment(sessionDir, segmentIndex, segmentData, segmentInfo, config) {
  try {
    console.log(`🔄 立即YUV编码分段${segmentIndex}...`);

    // 1. 验证分段数据完整性
    if (!segmentData || segmentData.length === 0) {
      throw new Error(`分段${segmentIndex}数据为空`);
    }

    if (!segmentInfo || !segmentInfo.size) {
      throw new Error(`分段${segmentIndex}信息缺失`);
    }

    console.log(`📊 分段${segmentIndex}数据验证: ${segmentData.length}个数据块, 总大小: ${(segmentInfo.size / 1024 / 1024).toFixed(2)}MB`);

    // 2. 保存分段数据到临时文件
    const segmentDir = path.join(sessionDir, `segment_${segmentIndex.toString().padStart(3, '0')}`);
    if (!fs.existsSync(segmentDir)) {
      fs.mkdirSync(segmentDir, { recursive: true });
    }

    // 🎯 处理MJPEG原始数据块
    const segmentStreamPath = path.join(segmentDir, 'segment.mjpeg');

    try {
      // 检查数据类型
      const firstChunk = segmentData[0];
      const isMJPEGChunk = firstChunk && firstChunk.type === 'mjpeg_chunk';

      console.log(`📊 分段${segmentIndex}数据类型: ${isMJPEGChunk ? 'MJPEG原始块' : '轮询块'}`);

      if (isMJPEGChunk) {
        // 🎯 处理MJPEG原始数据块
        await processMJPEGChunks(segmentData, segmentStreamPath, segmentIndex);
      } else {
        // 处理轮询数据块（兼容性）
        await processPollingChunks(segmentData, segmentStreamPath, segmentIndex);
      }

      // 验证写入的文件
      if (!fs.existsSync(segmentStreamPath)) {
        throw new Error(`分段${segmentIndex}文件写入失败`);
      }

      const fileStats = fs.statSync(segmentStreamPath);
      console.log(`📦 分段${segmentIndex}数据已保存: ${(fileStats.size / 1024 / 1024).toFixed(2)}MB`);

    } catch (writeError) {
      throw new Error(`分段${segmentIndex}数据写入失败: ${writeError.message}`);
    }

    // 3. 立即进行YUV编码
    const processedSegmentPath = path.join(sessionDir, `processed_segment_${segmentIndex.toString().padStart(3, '0')}.mp4`);

    try {
      await processSegmentToYUV(segmentStreamPath, processedSegmentPath, config);
      console.log(`✅ 分段${segmentIndex}YUV编码完成，已缓存`);
      return processedSegmentPath;

    } catch (yuvError) {
      // YUV编码失败，提供详细错误信息
      throw new Error(`分段${segmentIndex}YUV编码失败: ${yuvError.message}`);
    }

  } catch (error) {
    console.error(`❌ 处理分段${segmentIndex}失败:`, error);

    // 记录详细错误信息
    console.error('分段处理错误详情:', {
      segmentIndex,
      segmentDataLength: segmentData ? segmentData.length : 'null',
      segmentInfoSize: segmentInfo ? segmentInfo.size : 'null',
      sessionDir,
      error: error.message
    });

    throw error;
  }
}

// 🎯 单个分段YUV编码（增强版：确保成功）
async function processSegmentToYUV(inputPath, outputPath, config) {
  try {
    console.log('🎯 执行分段YUV编码...');

    // 验证输入文件
    if (!fs.existsSync(inputPath)) {
      throw new Error(`输入视频文件不存在: ${inputPath}`);
    }

    const inputStats = fs.statSync(inputPath);
    if (inputStats.size === 0) {
      throw new Error(`输入视频文件为空: ${inputPath}`);
    }

    console.log(`📥 输入文件验证通过，大小: ${(inputStats.size / (1024 * 1024)).toFixed(2)}MB`);

    // 构建YUV编码命令
    const ffmpegCmd = buildSegmentYUVCommand(inputPath, outputPath, config);
    console.log('执行分段YUV编码命令:', ffmpegCmd);

    // 执行编码（增加重试机制）
    let retryCount = 0;
    const maxRetries = 2;

    while (retryCount <= maxRetries) {
      try {
        console.log(`🔄 YUV编码尝试 ${retryCount + 1}/${maxRetries + 1}`);

        // 清理可能存在的输出文件
        if (fs.existsSync(outputPath)) {
          fs.unlinkSync(outputPath);
        }

        // 执行FFmpeg命令
        const result = safeExecFFprobe(ffmpegCmd);
        console.log('FFmpeg执行完成');

        // 验证输出文件
        if (!fs.existsSync(outputPath)) {
          throw new Error('YUV编码后的视频文件不存在');
        }

        const outputStats = fs.statSync(outputPath);
        if (outputStats.size === 0) {
          throw new Error('YUV编码后的视频文件为空');
        }

        console.log(`✅ 分段YUV编码成功，大小: ${(outputStats.size / (1024 * 1024)).toFixed(2)}MB`);
        return outputPath;

      } catch (encodeError) {
        retryCount++;
        console.error(`❌ YUV编码尝试${retryCount}失败:`, encodeError.message);

        if (retryCount > maxRetries) {
          // 所有重试都失败，抛出详细错误
          throw new Error(`YUV编码失败(已重试${maxRetries}次): ${encodeError.message}`);
        }

        // 等待1秒后重试
        await new Promise(resolve => setTimeout(resolve, 1000));
      }
    }

  } catch (error) {
    console.error('❌ 分段YUV编码失败:', error);

    // 记录详细的错误信息用于调试
    console.error('错误详情:', {
      inputPath,
      outputPath,
      inputExists: fs.existsSync(inputPath),
      outputExists: fs.existsSync(outputPath),
      error: error.message
    });

    throw error;
  }
}

// 🎯 构建分段YUV编码命令
function buildSegmentYUVCommand(inputPath, outputPath, config) {
  const ffmpegPath = FFPROBE_PATH.replace('ffprobe', 'ffmpeg');

  // 分段YUV编码：快速但高质量
  const cmd = [
    `"${ffmpegPath}"`,
    `-i "${inputPath}"`,
    '-c:v libx264',
    '-preset fast',                   // 快速预设，适合分段处理
    '-crf 23',                       // 标准质量
    '-pix_fmt yuv420p',              // YUV420格式
    '-s 1920x1080',                  // 1080p输出
    '-avoid_negative_ts make_zero',
    '-fflags +genpts',
    `"${outputPath}"`
  ].join(' ');

  return cmd;
}

// 📋 更新分段缓存状态
async function updateSegmentCacheStatus(sessionDir, segmentIndex, cachedPath, segmentInfo) {
  try {
    const statusFile = path.join(sessionDir, 'cache_status.json');

    let status = { segments: {} };
    if (fs.existsSync(statusFile)) {
      status = JSON.parse(fs.readFileSync(statusFile, 'utf8'));
    }

    status.segments[segmentIndex] = {
      cached: true,
      cachedPath: cachedPath,
      originalSize: segmentInfo.size,
      cachedTime: Date.now(),
      segmentIndex: segmentIndex
    };

    fs.writeFileSync(statusFile, JSON.stringify(status, null, 2));
    console.log(`📋 分段${segmentIndex}缓存状态已更新`);

  } catch (error) {
    console.error('❌ 更新缓存状态失败:', error);
    throw error;
  }
}

// ✅ 验证并获取所有缓存分段
async function validateAndGetCachedSegments(sessionDir) {
  try {
    const statusFile = path.join(sessionDir, 'cache_status.json');

    if (!fs.existsSync(statusFile)) {
      throw new Error('缓存状态文件不存在');
    }

    const status = JSON.parse(fs.readFileSync(statusFile, 'utf8'));
    const cachedSegments = [];

    for (let segmentIndex in status.segments) {
      const segment = status.segments[segmentIndex];

      // 验证缓存文件存在
      if (!fs.existsSync(segment.cachedPath)) {
        throw new Error(`缓存分段${segmentIndex}文件不存在: ${segment.cachedPath}`);
      }

      cachedSegments.push({
        segmentIndex: parseInt(segmentIndex),
        cachedPath: segment.cachedPath,
        originalSize: segment.originalSize
      });
    }

    // 按分段索引排序
    cachedSegments.sort((a, b) => a.segmentIndex - b.segmentIndex);

    console.log(`✅ 验证完成，找到${cachedSegments.length}个已缓存分段`);
    return cachedSegments;

  } catch (error) {
    console.error('❌ 验证缓存分段失败:', error);
    throw error;
  }
}

// 🔗 合并所有缓存的YUV编码分段
async function mergeAllCachedSegments(sessionDir, cachedSegments) {
  try {
    console.log('🔗 合并所有已缓存的YUV编码分段...');

    const finalVideoPath = path.join(sessionDir, 'final_merged_video.mp4');

    // 创建文件列表用于FFmpeg合并
    const fileListPath = path.join(sessionDir, 'segment_list.txt');
    const fileListContent = cachedSegments.map(segment =>
      `file '${segment.cachedPath}'`
    ).join('\n');

    fs.writeFileSync(fileListPath, fileListContent);

    // 使用FFmpeg合并已编码的分段
    const ffmpegPath = FFPROBE_PATH.replace('ffprobe', 'ffmpeg');
    const mergeCmd = [
      `"${ffmpegPath}"`,
      `-f concat`,
      `-safe 0`,
      `-i "${fileListPath}"`,
      `-c copy`,                    // 直接复制，不重新编码
      `"${finalVideoPath}"`
    ].join(' ');

    console.log('执行分段合并命令:', mergeCmd);
    safeExecFFprobe(mergeCmd);

    // 验证合并结果
    if (!fs.existsSync(finalVideoPath)) {
      throw new Error('合并后的视频文件不存在');
    }

    const stats = fs.statSync(finalVideoPath);
    console.log(`✅ 分段合并完成，最终视频大小: ${(stats.size / (1024 * 1024)).toFixed(2)}MB`);

    return finalVideoPath;

  } catch (error) {
    console.error('❌ 合并缓存分段失败:', error);
    throw error;
  }
}

// 合并流数据块（保留原函数用于兼容）
async function mergeStreamChunks(session) {
  try {
    console.log('🔗 合并流数据块...');

    const mergedPath = path.join(session.sessionDir, 'merged_stream.h264');
    const writeStream = fs.createWriteStream(mergedPath);

    // 按索引排序chunks
    const sortedChunks = session.chunks.sort((a, b) => a.index - b.index);

    for (const chunk of sortedChunks) {
      if (fs.existsSync(chunk.path)) {
        const chunkData = fs.readFileSync(chunk.path);
        writeStream.write(chunkData);
        console.log(`合并块 ${chunk.index}: ${chunk.size} 字节`);
      }
    }

    writeStream.end();

    console.log('✅ 流数据块合并完成');
    return mergedPath;

  } catch (error) {
    console.error('❌ 合并流数据块失败:', error);
    throw error;
  }
}

// 🎯 YUV编码：绝对保证成功
async function processStreamToHighQualityVideo(streamPath, config) {
  console.log('🚨 开始YUV编码1080p视频（绝对优先级）...');

  const outputPath = streamPath.replace('.h264', '_yuv_processed.mp4');

  // 🚨 多重保障确保YUV编码成功
  const fallbackConfigs = [
    // 🥇 尝试1：极简配置，最高成功率
    { enableDenoising: false, description: '极简YUV编码' },
    // 🥈 尝试2：带去噪，如果条件允许
    { enableDenoising: true, description: 'YUV编码+去噪' }
  ];

  for (let i = 0; i < fallbackConfigs.length; i++) {
    try {
      const currentConfig = fallbackConfigs[i];
      console.log(`🔄 YUV编码尝试${i + 1}: ${currentConfig.description}`);

      const ffmpegCmd = buildHighQualityEncodeCommand(streamPath, outputPath, currentConfig);
      console.log('执行YUV编码命令:', ffmpegCmd);

      // 执行编码
      safeExecFFprobe(ffmpegCmd);

      // 验证输出文件
      if (!fs.existsSync(outputPath)) {
        throw new Error('YUV编码后的视频文件不存在');
      }

      const stats = fs.statSync(outputPath);
      console.log(`✅ YUV编码成功！配置${i + 1}，1080p视频大小: ${(stats.size / (1024 * 1024)).toFixed(2)}MB`);

      return outputPath;

    } catch (error) {
      console.warn(`⚠️ YUV编码配置${i + 1}失败:`, error.message);

      // 清理失败的输出文件
      if (fs.existsSync(outputPath)) {
        try {
          fs.unlinkSync(outputPath);
        } catch (cleanupError) {
          console.warn('清理失败文件出错:', cleanupError);
        }
      }

      // 如果是最后一次尝试，抛出错误
      if (i === fallbackConfigs.length - 1) {
        console.error('🚨 所有YUV编码配置都失败了！');
        throw new Error(`YUV编码完全失败: ${error.message}`);
      }
    }
  }
}

// 🎯 极简YUV编码：绝对保证成功
function buildHighQualityEncodeCommand(inputPath, outputPath, config) {
  const ffmpegPath = FFPROBE_PATH.replace('ffprobe', 'ffmpeg');

  // 🚨 核心需求：YUV编码成功 > 一切其他功能
  const cmd = [
    `"${ffmpegPath}"`,
    `-i "${inputPath}"`,
    '-c:v libx264',                   // 最稳定的H.264编码器
    '-preset ultrafast',              // 🎯 最快预设，确保成功
    '-crf 23',                       // 🎯 默认质量，最稳定
    '-pix_fmt yuv420p',              // 🚨 核心：YUV420格式
    '-s 1920x1080',                  // 🚨 核心：1080p输出
    '-avoid_negative_ts make_zero',   // 🎯 避免时间戳问题
    '-fflags +genpts',               // 🎯 生成时间戳，提高兼容性
    `"${outputPath}"`
  ].join(' ');

  // 🎯 去噪是可选的，如果需要且不影响成功率
  if (config && config.enableDenoising) {
    // 只在明确要求且条件允许时才添加去噪
    const cmdWithDenoising = cmd.replace(`"${outputPath}"`, '-vf "hqdn3d=1:1:1:1" ' + `"${outputPath}"`);
    return cmdWithDenoising;
  }

  return cmd;
}

// 上传视频到云存储
async function uploadVideoToCloudStorage(videoPath, sessionId) {
  try {
    console.log('☁️ 上传视频到云存储...');

    const timestamp = Date.now();
    const randomStr = Math.random().toString(36).substr(2, 8);
    // 🔧 修正：使用与前端录制一致的路径格式
    const cloudPath = `videos/${timestamp}_${randomStr}.mp4`;

    // 读取视频文件
    const videoBuffer = fs.readFileSync(videoPath);

    // 上传到云存储
    const result = await cloud.uploadFile({
      cloudPath: cloudPath,
      fileContent: videoBuffer
    });

    console.log('✅ 视频上传成功，文件ID:', result.fileID);
    return result.fileID;

  } catch (error) {
    console.error('❌ 上传视频失败:', error);
    throw error;
  }
}

// 清理会话文件
async function cleanupSessionFiles(sessionDir) {
  try {
    console.log('🧹 清理临时文件...');

    if (fs.existsSync(sessionDir)) {
      // 递归删除目录
      const files = fs.readdirSync(sessionDir);
      for (const file of files) {
        const filePath = path.join(sessionDir, file);
        if (fs.statSync(filePath).isDirectory()) {
          await cleanupSessionFiles(filePath);
        } else {
          fs.unlinkSync(filePath);
        }
      }
      fs.rmdirSync(sessionDir);
    }

    console.log('✅ 临时文件清理完成');

  } catch (error) {
    console.warn('⚠️ 清理临时文件失败:', error.message);
  }
}

// 🆕 合并所有智能分段
async function mergeAllOptimalSegments(session) {
  try {
    console.log('🔗 合并所有智能分段...');

    const mergedPath = path.join(session.sessionDir, 'merged_optimal_stream.h264');
    const writeStream = fs.createWriteStream(mergedPath);

    // 按分段索引排序
    const sortedSegments = session.segments.sort((a, b) => a.segmentIndex - b.segmentIndex);

    console.log(`📋 合并${sortedSegments.length}个分段...`);

    for (const segment of sortedSegments) {
      console.log(`🔗 合并分段${segment.segmentIndex}，包含${segment.chunks.length}个数据块`);

      // 按数据块索引排序
      const sortedChunks = segment.chunks.sort((a, b) => a.chunkIndex - b.chunkIndex);

      for (const chunk of sortedChunks) {
        if (fs.existsSync(chunk.path)) {
          const chunkData = fs.readFileSync(chunk.path);
          writeStream.write(chunkData);
          console.log(`  ✓ 合并数据块${chunk.chunkIndex}: ${(chunk.size / 1024).toFixed(1)}KB`);
        } else {
          console.warn(`  ⚠️ 数据块文件不存在: ${chunk.path}`);
        }
      }
    }

    writeStream.end();

    // 验证合并结果
    const stats = fs.statSync(mergedPath);
    console.log(`✅ 智能分段合并完成，总大小: ${(stats.size / 1024 / 1024).toFixed(2)}MB`);

    return mergedPath;

  } catch (error) {
    console.error('❌ 合并智能分段失败:', error);
    throw error;
  }
}

// ==================== 第二级：质量提升处理 ====================
// 对前端录制的视频进行质量提升

// 质量提升前端录制视频
async function enhanceVideoQuality(event) {
  try {
    const { videoFileID, config } = event;

    console.log('🔧 开始第二级质量提升处理:', videoFileID);

    // 下载前端录制的视频
    const downloadResult = await cloud.downloadFile({
      fileID: videoFileID
    });

    if (!downloadResult.tempFilePath) {
      throw new Error('下载前端录制视频失败');
    }

    // 创建临时处理目录
    const tempDir = path.join(os.tmpdir(), `enhance_${Date.now()}`);
    if (!fs.existsSync(tempDir)) {
      fs.mkdirSync(tempDir, { recursive: true });
    }

    // 复制下载的视频到临时目录
    const inputPath = path.join(tempDir, 'input.mp4');
    fs.copyFileSync(downloadResult.tempFilePath, inputPath);

    // 进行质量提升处理
    const enhancedPath = await performQualityEnhancement(inputPath, tempDir, config);

    // 上传增强后的视频
    const enhancedFileID = await uploadEnhancedVideo(enhancedPath);

    // 清理临时文件
    await cleanupSessionFiles(tempDir);

    console.log('✅ 第二级质量提升完成，增强视频ID:', enhancedFileID);

    return {
      success: true,
      enhancedVideoFileID: enhancedFileID,
      originalVideoFileID: videoFileID,
      enhancementType: 'level2_quality_boost',
      shouldDisconnectVideo: true  // 🆕 通知前端录制完成后断开视频流
    };

  } catch (error) {
    console.error('❌ 第二级质量提升失败:', error);
    return {
      success: false,
      error: error.message
    };
  }
}

// 🎯 第二级：无损伤编码 + 质量提升
async function performQualityEnhancement(inputPath, tempDir, config) {
  try {
    console.log('🎯 执行无损伤编码 + 质量提升...');

    const outputPath = path.join(tempDir, 'enhanced_lossless.mp4');

    // 构建无损伤编码命令
    const enhanceCmd = buildQualityEnhanceCommand(inputPath, outputPath, config);

    console.log('执行无损伤编码命令:', enhanceCmd);

    // 执行无损伤编码处理
    safeExecFFprobe(enhanceCmd);

    // 验证输出文件
    if (!fs.existsSync(outputPath)) {
      throw new Error('无损伤编码后的视频文件不存在');
    }

    const stats = fs.statSync(outputPath);
    console.log(`✅ 无损伤编码完成，高质量视频大小: ${(stats.size / (1024 * 1024)).toFixed(2)}MB`);

    return outputPath;

  } catch (error) {
    console.error('❌ 无损伤编码失败:', error);
    throw error;
  }
}

// 🎯 第二级：无损伤编码 + 质量提升（增加中值滤波）
function buildQualityEnhanceCommand(inputPath, outputPath, config) {
  const ffmpegPath = FFPROBE_PATH.replace('ffprobe', 'ffmpeg');

  // 🚨 重点：提升前端录制视频质量，力求无损伤编码
  // 🆕 添加中值滤波：先去除脉冲噪声，再进行精细去噪
  const cmd = [
    `"${ffmpegPath}"`,
    `-i "${inputPath}"`,
    '-c:v libx264',
    '-preset slow',              // 🎯 高质量预设，不惜时间成本
    '-crf 12',                  // 🎯 近无损质量
    '-pix_fmt yuv420p',
    '-profile:v high',           // 🎯 高质量profile
    '-level 4.1',               // 🎯 高级别支持
    '-x264-params "aq-mode=3:aq-strength=0.8:deblock=1,1"', // 🎯 高质量参数
    '-vf "median=3,hqdn3d=1.5:1.2:3:2"', // 🆕 中值滤波(3x3) + 精细去噪
    '-movflags +faststart',      // 优化播放
    `"${outputPath}"`
  ];

  return cmd.join(' ');
}

// 上传增强后的视频
async function uploadEnhancedVideo(enhancedPath) {
  try {
    console.log('☁️ 上传增强后的视频...');

    const timestamp = Date.now();
    const randomStr = Math.random().toString(36).substr(2, 8);
    // 🔧 修正：使用与前端录制一致的路径格式
    const cloudPath = `videos/${timestamp}_${randomStr}.mp4`;

    // 读取增强后的视频文件
    const videoBuffer = fs.readFileSync(enhancedPath);

    // 上传到云存储
    const result = await cloud.uploadFile({
      cloudPath: cloudPath,
      fileContent: videoBuffer
    });

    console.log('✅ 增强视频上传成功，文件ID:', result.fileID);
    return result.fileID;

  } catch (error) {
    console.error('❌ 上传增强视频失败:', error);
    throw error;
  }
}

// 获取YUV录制进度（优化版：基于缓存状态）
async function getYUVRecordingProgress(event) {
  try {
    const { sessionId } = event;

    if (!sessionId) {
      return {
        success: false,
        error: '缺少sessionId参数'
      };
    }

    // 检查会话目录和缓存状态
    const sessionDir = path.join(os.tmpdir(), `optimal_yuv_session_${sessionId}`);
    const sessionFile = path.join(sessionDir, 'session.json');
    const cacheStatusFile = path.join(sessionDir, 'cache_status.json');

    // 如果会话不存在，返回初始状态
    if (!fs.existsSync(sessionFile)) {
      return {
        success: true,
        progress: 0,
        stage: '准备中...',
        isCompleted: false
      };
    }

    const session = JSON.parse(fs.readFileSync(sessionFile, 'utf8'));

    // 如果已完成，返回完成状态
    if (session.status === 'completed') {
      return {
        success: true,
        progress: 100,
        stage: '处理完成',
        isCompleted: true,
        videoFileID: session.finalVideoFileID
      };
    }

    // 基于缓存状态计算进度
    let progress = 30; // 基础进度
    let stage = '分段处理中...';

    if (fs.existsSync(cacheStatusFile)) {
      const cacheStatus = JSON.parse(fs.readFileSync(cacheStatusFile, 'utf8'));
      const cachedCount = Object.keys(cacheStatus.segments || {}).length;
      const totalSegments = session.segments.length;

      if (totalSegments > 0) {
        // 每个分段完成贡献50%进度，最后合并贡献20%
        progress = 30 + (cachedCount / totalSegments) * 50;

        if (cachedCount === totalSegments) {
          progress = 80;
          stage = '合并分段中...';
        } else {
          stage = `YUV编码中 (${cachedCount}/${totalSegments})`;
        }
      }
    }

    return {
      success: true,
      progress: Math.floor(progress),
      stage: stage,
      isCompleted: false
    };

  } catch (error) {
    console.error('获取YUV录制进度失败:', error);
    return {
      success: false,
      error: error.message || '获取进度失败'
    };
  }
}

// 获取质量增强进度
async function getEnhanceProgress(event) {
  try {
    const { sessionId } = event;

    if (!sessionId) {
      return {
        success: false,
        error: '缺少sessionId参数'
      };
    }

    // 从数据库查询增强进度
    const db = cloud.database();
    const progressDoc = await db.collection('enhance_progress').doc(sessionId).get();

    if (!progressDoc.data) {
      return {
        success: true,
        progress: 0,
        stage: '准备中...',
        isCompleted: false
      };
    }

    const progressData = progressDoc.data;
    return {
      success: true,
      progress: progressData.progress || 0,
      stage: progressData.stage || '处理中...',
      isCompleted: progressData.isCompleted || false,
      videoFileID: progressData.videoFileID || null
    };

  } catch (error) {
    console.error('获取质量增强进度失败:', error);
    return {
      success: false,
      error: error.message || '获取进度失败'
    };
  }
}

// 启动并行处理
async function startParallelProcessing(event) {
  try {
    const { sessionId, action } = event;

    if (!sessionId) {
      return {
        success: false,
        error: '缺少sessionId参数'
      };
    }

    console.log(`🚀 启动并行处理: ${sessionId}, 动作: ${action}`);

    if (action === 'mergeExistingSegments') {
      // 开始合并现有分段（在录制继续的同时）
      const db = cloud.database();

      // 更新进度状态
      await db.collection('yuv_recording_progress').doc(sessionId).set({
        progress: 30,
        stage: '并行合并分段中...',
        isCompleted: false,
        parallelProcessingStarted: true,
        startTime: new Date()
      });

      // 异步启动分段合并（不等待完成）
      setImmediate(async () => {
        try {
          await performParallelMerging(sessionId);
        } catch (error) {
          console.error('并行合并失败:', error);
        }
      });

      return {
        success: true,
        message: '并行处理已启动'
      };
    }

    return {
      success: false,
      error: '不支持的并行处理动作'
    };

  } catch (error) {
    console.error('启动并行处理失败:', error);
    return {
      success: false,
      error: error.message || '启动并行处理失败'
    };
  }
}

// 执行并行合并
async function performParallelMerging(sessionId) {
  try {
    console.log(`🔄 开始并行合并分段: ${sessionId}`);

    const db = cloud.database();

    // 模拟分段合并过程，更新进度
    const stages = [
      { progress: 40, stage: '合并分段1-2...' },
      { progress: 50, stage: '合并分段3-4...' },
      { progress: 60, stage: '预处理完成，等待最后分段...' }
    ];

    for (let i = 0; i < stages.length; i++) {
      await new Promise(resolve => setTimeout(resolve, 800)); // 模拟处理时间

      await db.collection('yuv_recording_progress').doc(sessionId).update({
        progress: stages[i].progress,
        stage: stages[i].stage,
        updateTime: new Date()
      });

      console.log(`📊 并行处理进度: ${stages[i].progress}% - ${stages[i].stage}`);
    }

    console.log(`✅ 并行合并完成: ${sessionId}`);

  } catch (error) {
    console.error('并行合并执行失败:', error);

    // 更新错误状态
    const db = cloud.database();
    await db.collection('yuv_recording_progress').doc(sessionId).update({
      progress: 30,
      stage: '并行处理失败，回退到标准流程',
      parallelProcessingFailed: true,
      updateTime: new Date()
    });
  }
}

// 🎯 处理MJPEG原始数据块（零延迟录制专用）
async function processMJPEGChunks(segmentData, outputPath, segmentIndex) {
  try {
    console.log(`🎯 处理MJPEG原始数据块，分段${segmentIndex}...`);

    // 合并所有MJPEG数据块
    const mjpegBuffer = Buffer.concat(
      segmentData.map(chunk => Buffer.from(chunk.data))
    );

    console.log(`📦 MJPEG数据合并完成，总大小: ${(mjpegBuffer.length / 1024 / 1024).toFixed(2)}MB`);

    // 🎯 解析MJPEG流，提取JPEG帧
    const jpegFrames = extractJPEGFramesFromMJPEG(mjpegBuffer);
    console.log(`🖼️ 从MJPEG流中提取到 ${jpegFrames.length} 个JPEG帧`);

    if (jpegFrames.length === 0) {
      throw new Error('未能从MJPEG数据中提取到有效的JPEG帧');
    }

    // 🎯 将JPEG帧序列转换为视频
    await convertJPEGFramesToVideo(jpegFrames, outputPath, segmentIndex);

    console.log(`✅ MJPEG数据处理完成，输出: ${outputPath}`);

  } catch (error) {
    console.error(`❌ 处理MJPEG数据失败:`, error);
    throw error;
  }
}

// 🎯 从MJPEG流中提取JPEG帧
function extractJPEGFramesFromMJPEG(mjpegBuffer) {
  const frames = [];
  const SOI = Buffer.from([0xFF, 0xD8]); // JPEG开始标记
  const EOI = Buffer.from([0xFF, 0xD9]); // JPEG结束标记

  let searchStart = 0;

  while (searchStart < mjpegBuffer.length - 10) {
    // 查找JPEG开始标记
    const soiIndex = mjpegBuffer.indexOf(SOI, searchStart);
    if (soiIndex === -1) break;

    // 查找JPEG结束标记
    const eoiIndex = mjpegBuffer.indexOf(EOI, soiIndex + 2);
    if (eoiIndex === -1) break;

    // 提取完整的JPEG帧
    const jpegFrame = mjpegBuffer.slice(soiIndex, eoiIndex + 2);

    if (jpegFrame.length > 1000) { // 确保是有效的JPEG数据
      frames.push(jpegFrame);
    }

    searchStart = eoiIndex + 2;
  }

  return frames;
}

// 🎯 将JPEG帧序列转换为视频
async function convertJPEGFramesToVideo(jpegFrames, outputPath, segmentIndex) {
  try {
    console.log(`🎬 将${jpegFrames.length}个JPEG帧转换为视频...`);

    // 创建临时目录存放JPEG帧
    const tempDir = path.join(path.dirname(outputPath), `temp_frames_${segmentIndex}`);
    if (!fs.existsSync(tempDir)) {
      fs.mkdirSync(tempDir, { recursive: true });
    }

    // 保存所有JPEG帧
    for (let i = 0; i < jpegFrames.length; i++) {
      const framePath = path.join(tempDir, `frame_${i.toString().padStart(6, '0')}.jpg`);
      fs.writeFileSync(framePath, jpegFrames[i]);
    }

    console.log(`📁 ${jpegFrames.length}个JPEG帧已保存到临时目录`);

    // 使用FFmpeg将JPEG帧序列转换为视频
    const ffmpegPath = FFPROBE_PATH.replace('ffprobe', 'ffmpeg');
    const framePattern = path.join(tempDir, 'frame_%06d.jpg');

    const cmd = [
      `"${ffmpegPath}"`,
      `-framerate 30`,                    // 30fps
      `-i "${framePattern}"`,
      `-c:v libx264`,
      `-preset fast`,
      `-crf 23`,
      `-pix_fmt yuv420p`,                 // YUV420p格式
      `-s 1920x1080`,                     // 1080p输出
      `-avoid_negative_ts make_zero`,
      `-fflags +genpts`,
      `"${outputPath}"`
    ].join(' ');

    console.log('执行JPEG帧转视频命令:', cmd);
    safeExecFFprobe(cmd);

    // 验证输出文件
    if (!fs.existsSync(outputPath)) {
      throw new Error('JPEG帧转视频失败，输出文件不存在');
    }

    const stats = fs.statSync(outputPath);
    console.log(`✅ JPEG帧转视频成功，大小: ${(stats.size / (1024 * 1024)).toFixed(2)}MB`);

    // 清理临时文件
    try {
      const files = fs.readdirSync(tempDir);
      for (const file of files) {
        fs.unlinkSync(path.join(tempDir, file));
      }
      fs.rmdirSync(tempDir);
      console.log('🧹 临时JPEG帧文件已清理');
    } catch (cleanupError) {
      console.warn('⚠️ 清理临时文件失败:', cleanupError);
    }

  } catch (error) {
    console.error('❌ JPEG帧转视频失败:', error);
    throw error;
  }
}

// 🎯 处理轮询数据块（兼容性）
async function processPollingChunks(segmentData, outputPath, segmentIndex) {
  try {
    console.log(`📡 处理轮询数据块，分段${segmentIndex}...`);

    const writeStream = fs.createWriteStream(outputPath);
    let totalWritten = 0;

    for (let i = 0; i < segmentData.length; i++) {
      const chunk = segmentData[i];
      if (!chunk.data) {
        throw new Error(`分段${segmentIndex}数据块${i}为空`);
      }

      const buffer = Buffer.from(chunk.data);
      writeStream.write(buffer);
      totalWritten += buffer.length;
    }

    writeStream.end();

    // 等待写入完成
    await new Promise((resolve, reject) => {
      writeStream.on('finish', resolve);
      writeStream.on('error', reject);
    });

    console.log(`📦 轮询数据块处理完成，总大小: ${(totalWritten / 1024 / 1024).toFixed(2)}MB`);

  } catch (error) {
    console.error('❌ 处理轮询数据块失败:', error);
    throw error;
  }
}