// addDevice.js
const app = getApp()

Page({
  data: {
    isShowInputForm: true,
    isScanButtonEnabled: true,
    deviceId: '',
    serialNumber: '',
    macAddress: '',
    price: '',
    deviceModel: '',
    deviceName: '',
    userInfo: null,
    isSubmitting: false,
    currentTab: 'addDevice'
  },

  onLoad: function() {
    // 🔧 页面布局适配
    this._adaptPageLayout();

    // 获取用户信息
    const userInfo = wx.getStorageSync('userInfo')
    if (userInfo) {
      this.setData({
        userInfo: userInfo
      })
    }
  },

  // 🔧 页面布局适配方法
  _adaptPageLayout: function() {
    try {
      // 获取系统信息
      const systemInfo = wx.getSystemInfoSync();

      // 获取状态栏高度（单位：px）
      const statusBarHeight = systemInfo.statusBarHeight || 0;

      // 获取安全区域信息
      const safeArea = systemInfo.safeArea || {};
      const safeAreaTop = safeArea.top || statusBarHeight;

      // 计算导航栏实际高度（状态栏 + 导航内容44rpx + 悬浮按钮空间20rpx）
      // 将rpx转换为px：44rpx = 44 * (screenWidth / 750)
      const navContentHeight = 44 * (systemInfo.screenWidth / 750);
      const floatingButtonSpace = 40 * (systemInfo.screenWidth / 750); // 为悬浮按钮留出更多空间
      const totalNavHeight = safeAreaTop + navContentHeight + floatingButtonSpace;

      console.log('📐 addDevice页面布局计算:', {
        statusBarHeight,
        safeAreaTop,
        navContentHeight,
        totalNavHeight,
        screenWidth: systemInfo.screenWidth
      });

      // 设置页面数据，用于动态样式
      this.setData({
        systemInfo: systemInfo,
        statusBarHeight: statusBarHeight,
        safeAreaTop: safeAreaTop,
        navContentHeight: navContentHeight,
        totalNavHeight: totalNavHeight
      });

    } catch (error) {
      console.error('❌ addDevice页面布局适配失败:', error);
      // 设置默认值，确保页面正常显示
      this.setData({
        statusBarHeight: 20,
        safeAreaTop: 20,
        totalNavHeight: 88
      });
    }
  },

  // 输入框事件处理函数
  inputDeviceId: function(e) {
    this.setData({
      deviceId: e.detail.value
    })
  },

  inputSerialNumber: function(e) {
    this.setData({
      serialNumber: e.detail.value
    })
  },

  inputMacAddress: function(e) {
    this.setData({
      macAddress: e.detail.value
    })
  },

  inputPrice: function(e) {
    this.setData({
      price: e.detail.value
    })
  },

  inputDeviceModel: function(e) {
    this.setData({
      deviceModel: e.detail.value
    })
  },

  inputDeviceName: function(e) {
    this.setData({
      deviceName: e.detail.value
    })
  },

  // 扫码录入切换
  screenInput: function(e) {
    this.setData({
      isShowInputForm: !e.detail.value
    })
    
    // 如果切换到扫码模式，自动启动扫码
    if (e.detail.value) {
      this.scanCode()
    }
  },

  // 验证表单数据
  validateForm: function() {
    const { deviceId, serialNumber, macAddress, deviceModel, deviceName } = this.data
    if (!deviceId) {
      wx.showToast({
        title: '请输入设备ID',
        icon: 'none'
      })
      return false
    }
    if (!serialNumber) {
      wx.showToast({
        title: '请输入序列号',
        icon: 'none'
      })
      return false
    }
    if (!macAddress) {
      wx.showToast({
        title: '请输入MAC地址',
        icon: 'none'
      })
      return false
    }
    if (!deviceModel) {
      wx.showToast({
        title: '请输入设备型号',
        icon: 'none'
      })
      return false
    }
    if (!deviceName) {
      wx.showToast({
        title: '请输入设备名称',
        icon: 'none'
      })
      return false
    }
    return true
  },

  // 添加设备
  addDevice: async function() {
    if (this.data.isSubmitting) return
    
    // 表单验证
    if (!this.validateForm()) return

    // 检查用户是否登录
    if (!this.data.userInfo) {
      wx.showModal({
        title: '提示',
        content: '请先登录后再录入设备',
        showCancel: false
      })
      return
    }

    this.setData({ isSubmitting: true })
    wx.showLoading({ title: '正在录入...' })

    try {
      // 准备设备数据
      const deviceData = {
        deviceId: this.data.deviceId,
        serialNumber: this.data.serialNumber,
        macAddress: this.data.macAddress,
        price: this.data.price,
        deviceModel: this.data.deviceModel,
        deviceName: this.data.deviceName,
        status: 'active',
        createTime: new Date().toISOString(),
        updateTime: new Date().toISOString(),
        // 用户相关信息
        userId: this.data.userInfo._openid || '',
        userName: this.data.userInfo.nickName || '',
        userAvatar: this.data.userInfo.avatarUrl || '',
        // 其他元数据
        isDeleted: false,
        lastMaintenanceTime: null,
        maintenanceRecords: []
      }

      // 将数据转换为JSON字符串
      const jsonData = JSON.stringify(deviceData)

      // 生成唯一的文件名（使用时间戳和随机数）
      const fileName = `devices/${this.data.userInfo._openid}/${Date.now()}_${Math.random().toString(36).substr(2, 9)}.json`

      // 上传到云存储
      const uploadResult = await wx.cloud.uploadFile({
        cloudPath: fileName,
        filePath: wx.env.USER_DATA_PATH + '/temp.json',
        // 将JSON字符串转换为二进制数据
        data: new Uint8Array(new TextEncoder().encode(jsonData)).buffer
      })

      if (uploadResult.fileID) {
        // 获取已存储的设备列表
        let deviceList = wx.getStorageSync('userDevices') || []
        
        // 添加新设备到列表
        deviceList.push({
          fileID: uploadResult.fileID,
          deviceId: this.data.deviceId,
          createTime: new Date().toISOString()
        })
        
        // 更新本地存储
        wx.setStorageSync('userDevices', deviceList)

        wx.hideLoading()
        wx.showToast({
          title: '录入成功',
          icon: 'success',
          duration: 2000
        })

        // 延迟返回上一页
        setTimeout(() => {
          wx.navigateBack()
        }, 2000)
      }
    } catch (error) {
      console.error('添加设备失败:', error)
      wx.hideLoading()
      wx.showModal({
        title: '录入失败',
        content: '设备录入失败，请重试',
        showCancel: false
      })
    } finally {
      this.setData({ isSubmitting: false })
    }
  },

  // 扫码功能
  scanCode: function() {
    wx.scanCode({
      success: (res) => {
        // 解析扫码结果，假设返回的是JSON字符串
        try {
          const deviceInfo = JSON.parse(res.result)
          this.setData({
            deviceId: deviceInfo.deviceId || '',
            serialNumber: deviceInfo.serialNumber || '',
            macAddress: deviceInfo.macAddress || '',
            deviceModel: deviceInfo.deviceModel || '',
            deviceName: deviceInfo.deviceName || ''
          })
        } catch (error) {
          wx.showToast({
            title: '无效的设备码',
            icon: 'none'
          })
          // 扫码结果无效时，切换回手动输入模式
          this.setData({
            isShowInputForm: true
          })
        }
      },
      fail: (error) => {
        console.error('扫码失败:', error)
        wx.showToast({
          title: '扫码失败',
          icon: 'none'
        })
        // 扫码失败时，切换回手动输入模式
        this.setData({
          isShowInputForm: true
        })
      },
      complete: () => {
        // 无论成功失败，都确保switch回到关闭状态
        this.setData({
          isShowInputForm: true
        })
      }
    })
  },

  // 添加tab切换函数
  switchTab: function(e) {
    const page = e.currentTarget.dataset.page;
    
    if (page === this.data.currentTab) {
      return; // 如果点击当前页，不做操作
    }
    
    // 根据页面名称进行跳转
    if (page === 'index') {
      wx.reLaunch({
        url: '/pages/index/index'
      });
    } else if (page === 'addDevice') {
      wx.reLaunch({
        url: '/pages/addDevice/addDevice'
      });
    } else if (page === 'me') {
      wx.reLaunch({
        url: '/pages/me/me'
      });
    }
  }
})