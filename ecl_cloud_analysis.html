<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ECL云端数据分析架构</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .container {
            background: white;
            border-radius: 20px;
            padding: 40px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            width: 1600px;
            height: 900px;
            position: relative;
            overflow: hidden;
        }

        .container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            border: 3px solid #2196F3;
            border-radius: 20px;
            pointer-events: none;
        }

        .main-title {
            position: absolute;
            top: 80px;
            left: 80px;
            background: linear-gradient(135deg, #2196F3, #1976D2);
            color: white;
            padding: 40px 50px;
            border-radius: 50%;
            font-size: 28px;
            font-weight: bold;
            text-align: center;
            box-shadow: 0 15px 35px rgba(33, 150, 243, 0.3);
            z-index: 10;
            width: 200px;
            height: 200px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .analysis-container {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            gap: 60px;
            margin-left: 400px;
            margin-top: 50px;
            height: calc(100% - 100px);
        }

        .divide-conquer {
            flex: 1;
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 15px;
        }

        .work-stealing {
            flex: 1;
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 15px;
        }

        .section-title {
            font-size: 20px;
            font-weight: bold;
            color: #2196F3;
            margin-bottom: 10px;
            text-align: center;
        }

        .section-description {
            font-size: 12px;
            color: #666;
            text-align: center;
            margin-bottom: 20px;
            padding: 0 10px;
            line-height: 1.4;
        }

        .process-box {
            background: #f8f9fa;
            border: 2px solid #2196F3;
            border-radius: 6px;
            padding: 12px 20px;
            margin: 6px;
            font-size: 13px;
            color: #333;
            text-align: center;
            min-width: 140px;
            box-shadow: 0 2px 6px rgba(0,0,0,0.1);
        }

        .main-process {
            background: #2196F3;
            color: white;
            font-weight: bold;
            font-size: 14px;
        }

        .sub-process {
            background: #e3f2fd;
            border-color: #1976D2;
            font-size: 12px;
        }

        .leaf-process {
            background: #f0f0f0;
            border-color: #999;
            font-size: 11px;
            min-width: 100px;
        }

        .result-process {
            background: #4caf50;
            border-color: #4caf50;
            color: white;
            font-weight: bold;
        }

        .connection-line {
            width: 2px;
            height: 30px;
            background: #2196F3;
            margin: 5px auto;
        }

        .horizontal-line {
            height: 2px;
            width: 100px;
            background: #2196F3;
            margin: 10px auto;
        }

        .fork-container {
            display: flex;
            justify-content: space-around;
            width: 100%;
            gap: 20px;
        }

        .join-container {
            display: flex;
            justify-content: center;
            width: 100%;
            margin-top: 20px;
        }

        .task-pool {
            position: relative;
            border: 2px dashed #2196F3;
            border-radius: 50%;
            width: 200px;
            height: 200px;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            margin-bottom: 30px;
        }

        .task-item {
            position: absolute;
            background: #fff3e0;
            border: 1px solid #ff9800;
            border-radius: 15px;
            padding: 6px 10px;
            font-size: 10px;
            color: #e65100;
            text-align: center;
            line-height: 1.2;
        }

        .task1 { top: 15px; left: 50%; transform: translateX(-50%); }
        .task2 { top: 45px; right: 25px; }
        .task3 { bottom: 45px; right: 25px; }
        .task4 { bottom: 15px; left: 50%; transform: translateX(-50%); }
        .task5 { bottom: 45px; left: 25px; }
        .task6 { top: 45px; left: 25px; }

        .worker-container {
            display: flex;
            justify-content: space-around;
            width: 100%;
            gap: 30px;
            margin-top: 20px;
        }

        .worker {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 10px;
        }

        .worker-queue {
            width: 80px;
            height: 200px;
            border: 2px solid #2196F3;
            border-radius: 10px;
            background: #f8f9fa;
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 10px 5px;
            gap: 5px;
        }

        .queue-item {
            background: #e8f5e8;
            border: 1px solid #4caf50;
            border-radius: 4px;
            padding: 6px 4px;
            font-size: 9px;
            color: #2e7d32;
            width: 65px;
            text-align: center;
            line-height: 1.1;
            margin-bottom: 3px;
        }

        .worker-label {
            font-size: 11px;
            color: #666;
            font-weight: bold;
            text-align: center;
            line-height: 1.2;
        }

        .steal-explanation {
            position: relative;
            width: 100%;
            height: 60px;
        }

        .steal-arrow {
            position: absolute;
            color: #f44336;
            font-size: 12px;
            font-weight: bold;
            text-align: center;
            line-height: 1.2;
        }

        .tech-implementation {
            position: absolute;
            bottom: 30px;
            left: 40px;
            right: 40px;
            background: rgba(248, 249, 250, 0.95);
            padding: 20px;
            border-radius: 12px;
            border-top: 3px solid #2196F3;
            backdrop-filter: blur(10px);
        }

        .tech-title {
            font-size: 18px;
            font-weight: bold;
            color: #2196F3;
            margin-bottom: 15px;
            text-align: center;
        }

        .tech-grid {
            display: grid;
            grid-template-columns: repeat(5, 1fr);
            gap: 15px;
        }

        .tech-item {
            background: white;
            padding: 12px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            text-align: center;
        }

        .tech-item .module-name {
            color: #2196F3;
            font-weight: bold;
            font-size: 13px;
            margin-bottom: 8px;
        }

        .tech-item .module-desc {
            color: #666;
            font-size: 11px;
            line-height: 1.3;
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 主标题 -->
        <div class="main-title">
            ECL云端<br>数据分析
        </div>

        <div class="analysis-container">
            <!-- 左侧：分治法 (基于analyzeVideo云函数) -->
            <div class="divide-conquer">
                <div class="section-title">分治法</div>
                <div class="section-description">
                    将复杂的视频分析任务分解为多个子任务并行处理，<br>
                    最后合并结果，提高analyzeVideo云函数的处理效率
                </div>

                <div class="process-box main-process">视频分析主任务</div>
                <div class="connection-line"></div>

                <div class="fork-container">
                    <div>
                        <div class="process-box sub-process">子任务1<br>FFmpeg视频处理</div>
                        <div class="connection-line"></div>
                        <div class="fork-container">
                            <div class="process-box leaf-process">子任务1.1<br>视频帧提取</div>
                            <div class="process-box leaf-process">子任务1.2<br>帧质量检测</div>
                        </div>
                        <div class="connection-line"></div>
                        <div class="process-box result-process">结果1<br>处理后帧序列</div>
                    </div>

                    <div>
                        <div class="process-box sub-process">子任务2<br>Jimp图像分析</div>
                        <div class="connection-line"></div>
                        <div class="fork-container">
                            <div class="process-box leaf-process">子任务2.1<br>C区域分析</div>
                            <div class="process-box leaf-process">子任务2.2<br>T区域分析</div>
                        </div>
                        <div class="connection-line"></div>
                        <div class="process-box result-process">结果2<br>灰度值数据</div>
                    </div>
                </div>

                <div class="connection-line"></div>
                <div class="process-box main-process">视频分析结果</div>
            </div>

            <!-- 右侧：工作窃取 (基于数据处理和用户管理) -->
            <div class="work-stealing">
                <div class="section-title">工作窃取</div>
                <div class="section-description">
                    多个工作线程从共享任务池中获取任务，<br>
                    空闲线程可以从繁忙线程的队列中窃取任务，实现负载均衡
                </div>

                <div class="task-pool">
                    <div class="task-item task1">task1<br>用户数据</div>
                    <div class="task-item task2">task2<br>文件下载</div>
                    <div class="task-item task3">task3<br>数据清理</div>
                    <div class="task-item task4">task4<br>结果导出</div>
                    <div class="task-item task5">task5<br>媒体处理</div>
                    <div class="task-item task6">task6<br>存储管理</div>
                </div>

                <div class="worker-container">
                    <div class="worker">
                        <div class="worker-queue">
                            <div class="queue-item">task4<br>结果导出</div>
                            <div class="queue-item">task5<br>媒体处理</div>
                        </div>
                        <div class="worker-label">taskqueue1<br>getUserData线程</div>
                    </div>

                    <div class="worker">
                        <div class="worker-queue">
                            <div class="queue-item">task6<br>存储管理</div>
                        </div>
                        <div class="worker-label">taskqueue2<br>pages/me线程</div>
                    </div>

                    <div class="worker">
                        <div class="worker-queue">
                            <div class="queue-item">task3<br>数据清理</div>
                        </div>
                        <div class="worker-label">taskqueue3<br>deleteUserData线程</div>
                    </div>
                </div>

                <div class="steal-explanation">
                    <div class="steal-arrow" style="top: 480px; left: 42%; color: #f44336;">
                        ← steal work<br>
                        <span style="font-size: 10px;">空闲线程从繁忙线程窃取任务</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- 技术实现说明 -->
        <div class="tech-implementation">
            <div class="tech-title">ECL系统并行处理技术实现</div>
            <div class="tech-grid">
                <div class="tech-item">
                    <div class="module-name">analyzeVideo</div>
                    <div class="module-desc">分治法视频分析<br>FFmpeg帧提取<br>Jimp并行处理</div>
                </div>
                <div class="tech-item">
                    <div class="module-name">getUserData</div>
                    <div class="module-desc">工作窃取模式<br>文件并发下载<br>负载均衡处理</div>
                </div>
                <div class="tech-item">
                    <div class="module-name">pages/index</div>
                    <div class="module-desc">前端任务调度<br>异步录制管理<br>参数并行配置</div>
                </div>
                <div class="tech-item">
                    <div class="module-name">pages/me</div>
                    <div class="module-desc">数据并行展示<br>批量操作处理<br>多任务导出</div>
                </div>
                <div class="tech-item">
                    <div class="module-name">deleteUserData</div>
                    <div class="module-desc">批量清理任务<br>并发文件删除<br>资源回收优化</div>
                </div>
            </div>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 静态界面，只添加基本的悬停效果
            const processBoxes = document.querySelectorAll('.process-box');
            const taskItems = document.querySelectorAll('.task-item');
            const queueItems = document.querySelectorAll('.queue-item');

            // 悬停效果
            processBoxes.forEach(box => {
                box.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateY(-2px)';
                    this.style.boxShadow = '0 4px 12px rgba(0,0,0,0.15)';
                });

                box.addEventListener('mouseleave', function() {
                    this.style.transform = 'translateY(0)';
                    this.style.boxShadow = '0 2px 6px rgba(0,0,0,0.1)';
                });
            });

            taskItems.forEach(item => {
                item.addEventListener('mouseenter', function() {
                    this.style.transform = this.style.transform.replace('translateX(-50%)', 'translateX(-50%) scale(1.05)');
                    if (!this.style.transform.includes('scale')) {
                        this.style.transform += ' scale(1.05)';
                    }
                });

                item.addEventListener('mouseleave', function() {
                    this.style.transform = this.style.transform.replace(' scale(1.05)', '');
                });
            });
        });
    </script>
</body>
</html>
