{"version": 3, "sources": ["dashdash.js"], "names": [], "mappings": ";;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.js", "sourcesContent": ["/**\n * dashdash - A light, featureful and explicit option parsing library for\n * node.js.\n */\n// vim: set ts=4 sts=4 sw=4 et:\n\nvar assert = require('assert-plus');\nvar format = require('util').format;\nvar fs = require('fs');\nvar path = require('path');\n\n\nvar DEBUG = true;\nif (DEBUG) {\n    var debug = console.warn;\n} else {\n    var debug = function () {};\n}\n\n\n\n// ---- internal support stuff\n\n// Replace {{variable}} in `s` with the template data in `d`.\nfunction renderTemplate(s, d) {\n    return s.replace(/{{([a-zA-Z]+)}}/g, function (match, key) {\n        return d.hasOwnProperty(key) ? d[key] : match;\n    });\n}\n\n/**\n * Return a shallow copy of the given object;\n */\nfunction shallowCopy(obj) {\n    if (!obj) {\n        return (obj);\n    }\n    var copy = {};\n    Object.keys(obj).forEach(function (k) {\n        copy[k] = obj[k];\n    });\n    return (copy);\n}\n\n\nfunction space(n) {\n    var s = '';\n    for (var i = 0; i < n; i++) {\n        s += ' ';\n    }\n    return s;\n}\n\n\nfunction makeIndent(arg, deflen, name) {\n    if (arg === null || arg === undefined)\n        return space(deflen);\n    else if (typeof (arg) === 'number')\n        return space(arg);\n    else if (typeof (arg) === 'string')\n        return arg;\n    else\n        assert.fail('invalid \"' + name + '\": not a string or number: ' + arg);\n}\n\n\n/**\n * Return an array of lines wrapping the given text to the given width.\n * This splits on whitespace. Single tokens longer than `width` are not\n * broken up.\n */\nfunction textwrap(s, width) {\n    var words = s.trim().split(/\\s+/);\n    var lines = [];\n    var line = '';\n    words.forEach(function (w) {\n        var newLength = line.length + w.length;\n        if (line.length > 0)\n            newLength += 1;\n        if (newLength > width) {\n            lines.push(line);\n            line = '';\n        }\n        if (line.length > 0)\n            line += ' ';\n        line += w;\n    });\n    lines.push(line);\n    return lines;\n}\n\n\n/**\n * Transform an option name to a \"key\" that is used as the field\n * on the `opts` object returned from `<parser>.parse()`.\n *\n * Transformations:\n * - '-' -> '_': This allow one to use hyphen in option names (common)\n *   but not have to do silly things like `opt[\"dry-run\"]` to access the\n *   parsed results.\n */\nfunction optionKeyFromName(name) {\n    return name.replace(/-/g, '_');\n}\n\n\n\n// ---- Option types\n\nfunction parseBool(option, optstr, arg) {\n    return Boolean(arg);\n}\n\nfunction parseString(option, optstr, arg) {\n    assert.string(arg, 'arg');\n    return arg;\n}\n\nfunction parseNumber(option, optstr, arg) {\n    assert.string(arg, 'arg');\n    var num = Number(arg);\n    if (isNaN(num)) {\n        throw new Error(format('arg for \"%s\" is not a number: \"%s\"',\n            optstr, arg));\n    }\n    return num;\n}\n\nfunction parseInteger(option, optstr, arg) {\n    assert.string(arg, 'arg');\n    var num = Number(arg);\n    if (!/^[0-9-]+$/.test(arg) || isNaN(num)) {\n        throw new Error(format('arg for \"%s\" is not an integer: \"%s\"',\n            optstr, arg));\n    }\n    return num;\n}\n\nfunction parsePositiveInteger(option, optstr, arg) {\n    assert.string(arg, 'arg');\n    var num = Number(arg);\n    if (!/^[0-9]+$/.test(arg) || isNaN(num) || num === 0) {\n        throw new Error(format('arg for \"%s\" is not a positive integer: \"%s\"',\n            optstr, arg));\n    }\n    return num;\n}\n\n/**\n * Supported date args:\n * - epoch second times (e.g. 1396031701)\n * - ISO 8601 format: YYYY-MM-DD[THH:MM:SS[.sss][Z]]\n *      2014-03-28T18:35:01.489Z\n *      2014-03-28T18:35:01.489\n *      2014-03-28T18:35:01Z\n *      2014-03-28T18:35:01\n *      2014-03-28\n */\nfunction parseDate(option, optstr, arg) {\n    assert.string(arg, 'arg');\n    var date;\n    if (/^\\d+$/.test(arg)) {\n        // epoch seconds\n        date = new Date(Number(arg) * 1000);\n    /* JSSTYLED */\n    } else if (/^\\d{4}-\\d{2}-\\d{2}(T\\d{2}:\\d{2}:\\d{2}(\\.\\d+)?Z?)?$/i.test(arg)) {\n        // ISO 8601 format\n        date = new Date(arg);\n    } else {\n        throw new Error(format('arg for \"%s\" is not a valid date format: \"%s\"',\n            optstr, arg));\n    }\n    if (date.toString() === 'Invalid Date') {\n        throw new Error(format('arg for \"%s\" is an invalid date: \"%s\"',\n            optstr, arg));\n    }\n    return date;\n}\n\nvar optionTypes = {\n    bool: {\n        takesArg: false,\n        parseArg: parseBool\n    },\n    string: {\n        takesArg: true,\n        helpArg: 'ARG',\n        parseArg: parseString\n    },\n    number: {\n        takesArg: true,\n        helpArg: 'NUM',\n        parseArg: parseNumber\n    },\n    integer: {\n        takesArg: true,\n        helpArg: 'INT',\n        parseArg: parseInteger\n    },\n    positiveInteger: {\n        takesArg: true,\n        helpArg: 'INT',\n        parseArg: parsePositiveInteger\n    },\n    date: {\n        takesArg: true,\n        helpArg: 'DATE',\n        parseArg: parseDate\n    },\n    arrayOfBool: {\n        takesArg: false,\n        array: true,\n        parseArg: parseBool\n    },\n    arrayOfString: {\n        takesArg: true,\n        helpArg: 'ARG',\n        array: true,\n        parseArg: parseString\n    },\n    arrayOfNumber: {\n        takesArg: true,\n        helpArg: 'NUM',\n        array: true,\n        parseArg: parseNumber\n    },\n    arrayOfInteger: {\n        takesArg: true,\n        helpArg: 'INT',\n        array: true,\n        parseArg: parseInteger\n    },\n    arrayOfPositiveInteger: {\n        takesArg: true,\n        helpArg: 'INT',\n        array: true,\n        parseArg: parsePositiveInteger\n    },\n    arrayOfDate: {\n        takesArg: true,\n        helpArg: 'INT',\n        array: true,\n        parseArg: parseDate\n    },\n};\n\n\n\n// ---- Parser\n\n/**\n * Parser constructor.\n *\n * @param config {Object} The parser configuration\n *      - options {Array} Array of option specs. See the README for how to\n *        specify each option spec.\n *      - allowUnknown {Boolean} Default false. Whether to throw on unknown\n *        options. If false, then unknown args are included in the _args array.\n *      - interspersed {Boolean} Default true. Whether to allow interspersed\n *        arguments (non-options) and options. E.g.:\n *              node tool.js arg1 arg2 -v\n *        '-v' is after some args here. If `interspersed: false` then '-v'\n *        would not be parsed out. Note that regardless of `interspersed`\n *        the presence of '--' will stop option parsing, as all good\n *        option parsers should.\n */\nfunction Parser(config) {\n    assert.object(config, 'config');\n    assert.arrayOfObject(config.options, 'config.options');\n    assert.optionalBool(config.interspersed, 'config.interspersed');\n    var self = this;\n\n    // Allow interspersed arguments (true by default).\n    this.interspersed = (config.interspersed !== undefined\n        ? config.interspersed : true);\n\n    // Don't allow unknown flags (true by default).\n    this.allowUnknown = (config.allowUnknown !== undefined\n        ? config.allowUnknown : false);\n\n    this.options = config.options.map(function (o) { return shallowCopy(o); });\n    this.optionFromName = {};\n    this.optionFromEnv = {};\n    for (var i = 0; i < this.options.length; i++) {\n        var o = this.options[i];\n        if (o.group !== undefined && o.group !== null) {\n            assert.optionalString(o.group,\n                format('config.options.%d.group', i));\n            continue;\n        }\n        assert.ok(optionTypes[o.type],\n            format('invalid config.options.%d.type: \"%s\" in %j',\n                   i, o.type, o));\n        assert.optionalString(o.name, format('config.options.%d.name', i));\n        assert.optionalArrayOfString(o.names,\n            format('config.options.%d.names', i));\n        assert.ok((o.name || o.names) && !(o.name && o.names),\n            format('exactly one of \"name\" or \"names\" required: %j', o));\n        assert.optionalString(o.help, format('config.options.%d.help', i));\n        var env = o.env || [];\n        if (typeof (env) === 'string') {\n            env = [env];\n        }\n        assert.optionalArrayOfString(env, format('config.options.%d.env', i));\n        assert.optionalString(o.helpGroup,\n            format('config.options.%d.helpGroup', i));\n        assert.optionalBool(o.helpWrap,\n            format('config.options.%d.helpWrap', i));\n        assert.optionalBool(o.hidden, format('config.options.%d.hidden', i));\n\n        if (o.name) {\n            o.names = [o.name];\n        } else {\n            assert.string(o.names[0],\n                format('config.options.%d.names is empty', i));\n        }\n        o.key = optionKeyFromName(o.names[0]);\n        o.names.forEach(function (n) {\n            if (self.optionFromName[n]) {\n                throw new Error(format(\n                    'option name collision: \"%s\" used in %j and %j',\n                    n, self.optionFromName[n], o));\n            }\n            self.optionFromName[n] = o;\n        });\n        env.forEach(function (n) {\n            if (self.optionFromEnv[n]) {\n                throw new Error(format(\n                    'option env collision: \"%s\" used in %j and %j',\n                    n, self.optionFromEnv[n], o));\n            }\n            self.optionFromEnv[n] = o;\n        });\n    }\n}\n\nParser.prototype.optionTakesArg = function optionTakesArg(option) {\n    return optionTypes[option.type].takesArg;\n};\n\n/**\n * Parse options from the given argv.\n *\n * @param inputs {Object} Optional.\n *      - argv {Array} Optional. The argv to parse. Defaults to\n *        `process.argv`.\n *      - slice {Number} The index into argv at which options/args begin.\n *        Default is 2, as appropriate for `process.argv`.\n *      - env {Object} Optional. The env to use for 'env' entries in the\n *        option specs. Defaults to `process.env`.\n * @returns {Object} Parsed `opts`. It has special keys `_args` (the\n *      remaining args from `argv`) and `_order` (gives the order that\n *      options were specified).\n */\nParser.prototype.parse = function parse(inputs) {\n    var self = this;\n\n    // Old API was `parse([argv, [slice]])`\n    if (Array.isArray(arguments[0])) {\n        inputs = {argv: arguments[0], slice: arguments[1]};\n    }\n\n    assert.optionalObject(inputs, 'inputs');\n    if (!inputs) {\n        inputs = {};\n    }\n    assert.optionalArrayOfString(inputs.argv, 'inputs.argv');\n    //assert.optionalNumber(slice, 'slice');\n    var argv = inputs.argv || process.argv;\n    var slice = inputs.slice !== undefined ? inputs.slice : 2;\n    var args = argv.slice(slice);\n    var env = inputs.env || process.env;\n    var opts = {};\n    var _order = [];\n\n    function addOpt(option, optstr, key, val, from) {\n        var type = optionTypes[option.type];\n        var parsedVal = type.parseArg(option, optstr, val);\n        if (type.array) {\n            if (!opts[key]) {\n                opts[key] = [];\n            }\n            if (type.arrayFlatten && Array.isArray(parsedVal)) {\n                for (var i = 0; i < parsedVal.length; i++) {\n                    opts[key].push(parsedVal[i]);\n                }\n            } else {\n                opts[key].push(parsedVal);\n            }\n        } else {\n            opts[key] = parsedVal;\n        }\n        var item = { key: key, value: parsedVal, from: from };\n        _order.push(item);\n    }\n\n    // Parse args.\n    var _args = [];\n    var i = 0;\n    outer: while (i < args.length) {\n        var arg = args[i];\n\n        // End of options marker.\n        if (arg === '--') {\n            i++;\n            break;\n\n        // Long option\n        } else if (arg.slice(0, 2) === '--') {\n            var name = arg.slice(2);\n            var val = null;\n            var idx = name.indexOf('=');\n            if (idx !== -1) {\n                val = name.slice(idx + 1);\n                name = name.slice(0, idx);\n            }\n            var option = this.optionFromName[name];\n            if (!option) {\n                if (!this.allowUnknown)\n                    throw new Error(format('unknown option: \"--%s\"', name));\n                else if (this.interspersed)\n                    _args.push(arg);\n                else\n                    break outer;\n            } else {\n                var takesArg = this.optionTakesArg(option);\n                if (val !== null && !takesArg) {\n                    throw new Error(format('argument given to \"--%s\" option '\n                        + 'that does not take one: \"%s\"', name, arg));\n                }\n                if (!takesArg) {\n                    addOpt(option, '--'+name, option.key, true, 'argv');\n                } else if (val !== null) {\n                    addOpt(option, '--'+name, option.key, val, 'argv');\n                } else if (i + 1 >= args.length) {\n                    throw new Error(format('do not have enough args for \"--%s\" '\n                        + 'option', name));\n                } else {\n                    addOpt(option, '--'+name, option.key, args[i + 1], 'argv');\n                    i++;\n                }\n            }\n\n        // Short option\n        } else if (arg[0] === '-' && arg.length > 1) {\n            var j = 1;\n            var allFound = true;\n            while (j < arg.length) {\n                var name = arg[j];\n                var option = this.optionFromName[name];\n                if (!option) {\n                    allFound = false;\n                    if (this.allowUnknown) {\n                        if (this.interspersed) {\n                            _args.push(arg);\n                            break;\n                        } else\n                            break outer;\n                    } else if (arg.length > 2) {\n                        throw new Error(format(\n                            'unknown option: \"-%s\" in \"%s\" group',\n                            name, arg));\n                    } else {\n                        throw new Error(format('unknown option: \"-%s\"', name));\n                    }\n                } else if (this.optionTakesArg(option)) {\n                    break;\n                }\n                j++;\n            }\n\n            j = 1;\n            while (allFound && j < arg.length) {\n                var name = arg[j];\n                var val = arg.slice(j + 1);  // option val if it takes an arg\n                var option = this.optionFromName[name];\n                var takesArg = this.optionTakesArg(option);\n                if (!takesArg) {\n                    addOpt(option, '-'+name, option.key, true, 'argv');\n                } else if (val) {\n                    addOpt(option, '-'+name, option.key, val, 'argv');\n                    break;\n                } else {\n                    if (i + 1 >= args.length) {\n                        throw new Error(format('do not have enough args '\n                            + 'for \"-%s\" option', name));\n                    }\n                    addOpt(option, '-'+name, option.key, args[i + 1], 'argv');\n                    i++;\n                    break;\n                }\n                j++;\n            }\n\n        // An interspersed arg\n        } else if (this.interspersed) {\n            _args.push(arg);\n\n        // An arg and interspersed args are not allowed, so done options.\n        } else {\n            break outer;\n        }\n        i++;\n    }\n    _args = _args.concat(args.slice(i));\n\n    // Parse environment.\n    Object.keys(this.optionFromEnv).forEach(function (envname) {\n        var val = env[envname];\n        if (val === undefined)\n            return;\n        var option = self.optionFromEnv[envname];\n        if (opts[option.key] !== undefined)\n            return;\n        var takesArg = self.optionTakesArg(option);\n        if (takesArg) {\n            addOpt(option, envname, option.key, val, 'env');\n        } else if (val !== '') {\n            // Boolean envvar handling:\n            // - VAR=<empty-string>     not set (as if the VAR was not set)\n            // - VAR=0                  false\n            // - anything else          true\n            addOpt(option, envname, option.key, (val !== '0'), 'env');\n        }\n    });\n\n    // Apply default values.\n    this.options.forEach(function (o) {\n        if (opts[o.key] === undefined) {\n            if (o.default !== undefined) {\n                opts[o.key] = o.default;\n            } else if (o.type && optionTypes[o.type].default !== undefined) {\n                opts[o.key] = optionTypes[o.type].default;\n            }\n        }\n    });\n\n    opts._order = _order;\n    opts._args = _args;\n    return opts;\n};\n\n\n/**\n * Return help output for the current options.\n *\n * E.g.: if the current options are:\n *      [{names: ['help', 'h'], type: 'bool', help: 'Show help and exit.'}]\n * then this would return:\n *      '  -h, --help     Show help and exit.\\n'\n *\n * @param config {Object} Config for controlling the option help output.\n *      - indent {Number|String} Default 4. An indent/prefix to use for\n *        each option line.\n *      - nameSort {String} Default is 'length'. By default the names are\n *        sorted to put the short opts first (i.e. '-h, --help' preferred\n *        to '--help, -h'). Set to 'none' to not do this sorting.\n *      - maxCol {Number} Default 80. Note that long tokens in a help string\n *        can go past this.\n *      - helpCol {Number} Set to specify a specific column at which\n *        option help will be aligned. By default this is determined\n *        automatically.\n *      - minHelpCol {Number} Default 20.\n *      - maxHelpCol {Number} Default 40.\n *      - includeEnv {Boolean} Default false. If true, a note stating the `env`\n *        envvar (if specified for this option) will be appended to the help\n *        output.\n *      - includeDefault {Boolean} Default false. If true, a note stating\n *        the `default` for this option, if any, will be appended to the help\n *        output.\n *      - helpWrap {Boolean} Default true. Wrap help text in helpCol..maxCol\n *        bounds.\n * @returns {String}\n */\nParser.prototype.help = function help(config) {\n    config = config || {};\n    assert.object(config, 'config');\n\n    var indent = makeIndent(config.indent, 4, 'config.indent');\n    var headingIndent = makeIndent(config.headingIndent,\n        Math.round(indent.length / 2), 'config.headingIndent');\n\n    assert.optionalString(config.nameSort, 'config.nameSort');\n    var nameSort = config.nameSort || 'length';\n    assert.ok(~['length', 'none'].indexOf(nameSort),\n        'invalid \"config.nameSort\"');\n    assert.optionalNumber(config.maxCol, 'config.maxCol');\n    assert.optionalNumber(config.maxHelpCol, 'config.maxHelpCol');\n    assert.optionalNumber(config.minHelpCol, 'config.minHelpCol');\n    assert.optionalNumber(config.helpCol, 'config.helpCol');\n    assert.optionalBool(config.includeEnv, 'config.includeEnv');\n    assert.optionalBool(config.includeDefault, 'config.includeDefault');\n    assert.optionalBool(config.helpWrap, 'config.helpWrap');\n    var maxCol = config.maxCol || 80;\n    var minHelpCol = config.minHelpCol || 20;\n    var maxHelpCol = config.maxHelpCol || 40;\n\n    var lines = [];\n    var maxWidth = 0;\n    this.options.forEach(function (o) {\n        if (o.hidden) {\n            return;\n        }\n        if (o.group !== undefined && o.group !== null) {\n            // We deal with groups in the next pass\n            lines.push(null);\n            return;\n        }\n        var type = optionTypes[o.type];\n        var arg = o.helpArg || type.helpArg || 'ARG';\n        var line = '';\n        var names = o.names.slice();\n        if (nameSort === 'length') {\n            names.sort(function (a, b) {\n                if (a.length < b.length)\n                    return -1;\n                else if (b.length < a.length)\n                    return 1;\n                else\n                    return 0;\n            })\n        }\n        names.forEach(function (name, i) {\n            if (i > 0)\n                line += ', ';\n            if (name.length === 1) {\n                line += '-' + name\n                if (type.takesArg)\n                    line += ' ' + arg;\n            } else {\n                line += '--' + name\n                if (type.takesArg)\n                    line += '=' + arg;\n            }\n        });\n        maxWidth = Math.max(maxWidth, line.length);\n        lines.push(line);\n    });\n\n    // Add help strings.\n    var helpCol = config.helpCol;\n    if (!helpCol) {\n        helpCol = maxWidth + indent.length + 2;\n        helpCol = Math.min(Math.max(helpCol, minHelpCol), maxHelpCol);\n    }\n    var i = -1;\n    this.options.forEach(function (o) {\n        if (o.hidden) {\n            return;\n        }\n        i++;\n\n        if (o.group !== undefined && o.group !== null) {\n            if (o.group === '') {\n                // Support a empty string \"group\" to have a blank line between\n                // sets of options.\n                lines[i] = '';\n            } else {\n                // Render the group heading with the heading-specific indent.\n                lines[i] = (i === 0 ? '' : '\\n') + headingIndent +\n                    o.group + ':';\n            }\n            return;\n        }\n\n        var helpDefault;\n        if (config.includeDefault) {\n            if (o.default !== undefined) {\n                helpDefault = format('Default: %j', o.default);\n            } else if (o.type && optionTypes[o.type].default !== undefined) {\n                helpDefault = format('Default: %j',\n                    optionTypes[o.type].default);\n            }\n        }\n\n        var line = lines[i] = indent + lines[i];\n        if (!o.help && !(config.includeEnv && o.env) && !helpDefault) {\n            return;\n        }\n        var n = helpCol - line.length;\n        if (n >= 0) {\n            line += space(n);\n        } else {\n            line += '\\n' + space(helpCol);\n        }\n\n        var helpEnv = '';\n        if (o.env && o.env.length && config.includeEnv) {\n            helpEnv += 'Environment: ';\n            var type = optionTypes[o.type];\n            var arg = o.helpArg || type.helpArg || 'ARG';\n            var envs = (Array.isArray(o.env) ? o.env : [o.env]).map(\n                function (e) {\n                    if (type.takesArg) {\n                        return e + '=' + arg;\n                    } else {\n                        return e + '=1';\n                    }\n                }\n            );\n            helpEnv += envs.join(', ');\n        }\n        var help = (o.help || '').trim();\n        if (o.helpWrap !== false && config.helpWrap !== false) {\n            // Wrap help description normally.\n            if (help.length && !~'.!?\"\\''.indexOf(help.slice(-1))) {\n                help += '.';\n            }\n            if (help.length) {\n                help += ' ';\n            }\n            help += helpEnv;\n            if (helpDefault) {\n                if (helpEnv) {\n                    help += '. ';\n                }\n                help += helpDefault;\n            }\n            line += textwrap(help, maxCol - helpCol).join(\n                '\\n' + space(helpCol));\n        } else {\n            // Do not wrap help description, but indent newlines appropriately.\n            var helpLines = help.split('\\n').filter(\n                    function (ln) { return ln.length });\n            if (helpEnv !== '') {\n                helpLines.push(helpEnv);\n            }\n            if (helpDefault) {\n                helpLines.push(helpDefault);\n            }\n            line += helpLines.join('\\n' + space(helpCol));\n        }\n\n        lines[i] = line;\n    });\n\n    var rv = '';\n    if (lines.length > 0) {\n        rv = lines.join('\\n') + '\\n';\n    }\n    return rv;\n};\n\n\n/**\n * Return a string suitable for a Bash completion file for this tool.\n *\n * @param args.name {String} The tool name.\n * @param args.specExtra {String} Optional. Extra Bash code content to add\n *      to the end of the \"spec\". Typically this is used to append Bash\n *      \"complete_TYPE\" functions for custom option types. See\n *      \"examples/ddcompletion.js\" for an example.\n * @param args.argtypes {Array} Optional. Array of completion types for\n *      positional args (i.e. non-options). E.g.\n *          argtypes = ['fruit', 'veggie', 'file']\n *      will result in completion of fruits for the first arg, veggies for the\n *      second, and filenames for the third and subsequent positional args.\n *      If not given, positional args will use Bash's 'default' completion.\n *      See `specExtra` for providing Bash `complete_TYPE` functions, e.g.\n *      `complete_fruit` and `complete_veggie` in this example.\n */\nParser.prototype.bashCompletion = function bashCompletion(args) {\n    assert.object(args, 'args');\n    assert.string(args.name, 'args.name');\n    assert.optionalString(args.specExtra, 'args.specExtra');\n    assert.optionalArrayOfString(args.argtypes, 'args.argtypes');\n\n    return bashCompletionFromOptions({\n        name: args.name,\n        specExtra: args.specExtra,\n        argtypes: args.argtypes,\n        options: this.options\n    });\n};\n\n\n// ---- Bash completion\n\nconst BASH_COMPLETION_TEMPLATE_PATH = path.join(\n    __dirname, '../etc/dashdash.bash_completion.in');\n\n/**\n * Return the Bash completion \"spec\" (the string value for the \"{{spec}}\"\n * var in the \"dashdash.bash_completion.in\" template) for this tool.\n *\n * The \"spec\" is Bash code that defines the CLI options and subcmds for\n * the template's completion code. It looks something like this:\n *\n *      local cmd_shortopts=\"-J ...\"\n *      local cmd_longopts=\"--help ...\"\n *      local cmd_optargs=\"-p=tritonprofile ...\"\n *\n * @param args.options {Array} The array of dashdash option specs.\n * @param args.context {String} Optional. A context string for the \"local cmd*\"\n *      vars in the spec. By default it is the empty string. When used to\n *      scope for completion on a *sub-command* (e.g. for \"git log\" on a \"git\"\n *      tool), then it would have a value (e.g. \"__log\"). See\n *      <http://github.com/trentm/node-cmdln> Bash completion for details.\n * @param opts.includeHidden {Boolean} Optional. Default false. By default\n *      hidden options and subcmds are \"excluded\". Here excluded means they\n *      won't be offered as a completion, but if used, their argument type\n *      will be completed. \"Hidden\" options and subcmds are ones with the\n *      `hidden: true` attribute to exclude them from default help output.\n * @param args.argtypes {Array} Optional. Array of completion types for\n *      positional args (i.e. non-options). E.g.\n *          argtypes = ['fruit', 'veggie', 'file']\n *      will result in completion of fruits for the first arg, veggies for the\n *      second, and filenames for the third and subsequent positional args.\n *      If not given, positional args will use Bash's 'default' completion.\n *      See `specExtra` for providing Bash `complete_TYPE` functions, e.g.\n *      `complete_fruit` and `complete_veggie` in this example.\n */\nfunction bashCompletionSpecFromOptions(args) {\n    assert.object(args, 'args');\n    assert.object(args.options, 'args.options');\n    assert.optionalString(args.context, 'args.context');\n    assert.optionalBool(args.includeHidden, 'args.includeHidden');\n    assert.optionalArrayOfString(args.argtypes, 'args.argtypes');\n\n    var context = args.context || '';\n    var includeHidden = (args.includeHidden === undefined\n        ? false : args.includeHidden);\n\n    var spec = [];\n    var shortopts = [];\n    var longopts = [];\n    var optargs = [];\n    (args.options || []).forEach(function (o) {\n        if (o.group !== undefined && o.group !== null) {\n            // Skip group headers.\n            return;\n        }\n\n        var optNames = o.names || [o.name];\n        var optType = getOptionType(o.type);\n        if (optType.takesArg) {\n            var completionType = o.completionType ||\n                optType.completionType || o.type;\n            optNames.forEach(function (optName) {\n                if (optName.length === 1) {\n                    if (includeHidden || !o.hidden) {\n                        shortopts.push('-' + optName);\n                    }\n                    // Include even hidden options in `optargs` so that bash\n                    // completion of its arg still works.\n                    optargs.push('-' + optName + '=' + completionType);\n                } else {\n                    if (includeHidden || !o.hidden) {\n                        longopts.push('--' + optName);\n                    }\n                    optargs.push('--' + optName + '=' + completionType);\n                }\n            });\n        } else {\n            optNames.forEach(function (optName) {\n                if (includeHidden || !o.hidden) {\n                    if (optName.length === 1) {\n                        shortopts.push('-' + optName);\n                    } else {\n                        longopts.push('--' + optName);\n                    }\n                }\n            });\n        }\n    });\n\n    spec.push(format('local cmd%s_shortopts=\"%s\"',\n        context, shortopts.sort().join(' ')));\n    spec.push(format('local cmd%s_longopts=\"%s\"',\n        context, longopts.sort().join(' ')));\n    spec.push(format('local cmd%s_optargs=\"%s\"',\n        context, optargs.sort().join(' ')));\n    if (args.argtypes) {\n        spec.push(format('local cmd%s_argtypes=\"%s\"',\n            context, args.argtypes.join(' ')));\n    }\n    return spec.join('\\n');\n}\n\n\n/**\n * Return a string suitable for a Bash completion file for this tool.\n *\n * @param args.name {String} The tool name.\n * @param args.options {Array} The array of dashdash option specs.\n * @param args.specExtra {String} Optional. Extra Bash code content to add\n *      to the end of the \"spec\". Typically this is used to append Bash\n *      \"complete_TYPE\" functions for custom option types. See\n *      \"examples/ddcompletion.js\" for an example.\n * @param args.argtypes {Array} Optional. Array of completion types for\n *      positional args (i.e. non-options). E.g.\n *          argtypes = ['fruit', 'veggie', 'file']\n *      will result in completion of fruits for the first arg, veggies for the\n *      second, and filenames for the third and subsequent positional args.\n *      If not given, positional args will use Bash's 'default' completion.\n *      See `specExtra` for providing Bash `complete_TYPE` functions, e.g.\n *      `complete_fruit` and `complete_veggie` in this example.\n */\nfunction bashCompletionFromOptions(args) {\n    assert.object(args, 'args');\n    assert.object(args.options, 'args.options');\n    assert.string(args.name, 'args.name');\n    assert.optionalString(args.specExtra, 'args.specExtra');\n    assert.optionalArrayOfString(args.argtypes, 'args.argtypes');\n\n    // Gather template data.\n    var data = {\n        name: args.name,\n        date: new Date(),\n        spec: bashCompletionSpecFromOptions({\n            options: args.options,\n            argtypes: args.argtypes\n        }),\n    };\n    if (args.specExtra) {\n        data.spec += '\\n\\n' + args.specExtra;\n    }\n\n    // Render template.\n    var template = fs.readFileSync(BASH_COMPLETION_TEMPLATE_PATH, 'utf8');\n    return renderTemplate(template, data);\n}\n\n\n\n// ---- exports\n\nfunction createParser(config) {\n    return new Parser(config);\n}\n\n/**\n * Parse argv with the given options.\n *\n * @param config {Object} A merge of all the available fields from\n *      `dashdash.Parser` and `dashdash.Parser.parse`: options, interspersed,\n *      argv, env, slice.\n */\nfunction parse(config) {\n    assert.object(config, 'config');\n    assert.optionalArrayOfString(config.argv, 'config.argv');\n    assert.optionalObject(config.env, 'config.env');\n    var config = shallowCopy(config);\n    var argv = config.argv;\n    delete config.argv;\n    var env = config.env;\n    delete config.env;\n\n    var parser = new Parser(config);\n    return parser.parse({argv: argv, env: env});\n}\n\n\n/**\n * Add a new option type.\n *\n * @params optionType {Object}:\n *      - name {String} Required.\n *      - takesArg {Boolean} Required. Whether this type of option takes an\n *        argument on process.argv. Typically this is true for all but the\n *        \"bool\" type.\n *      - helpArg {String} Required iff `takesArg === true`. The string to\n *        show in generated help for options of this type.\n *      - parseArg {Function} Require. `function (option, optstr, arg)` parser\n *        that takes a string argument and returns an instance of the\n *        appropriate type, or throws an error if the arg is invalid.\n *      - array {Boolean} Optional. Set to true if this is an 'arrayOf' type\n *        that collects multiple usages of the option in process.argv and\n *        puts results in an array.\n *      - arrayFlatten {Boolean} Optional. XXX\n *      - default Optional. Default value for options of this type, if no\n *        default is specified in the option type usage.\n */\nfunction addOptionType(optionType) {\n    assert.object(optionType, 'optionType');\n    assert.string(optionType.name, 'optionType.name');\n    assert.bool(optionType.takesArg, 'optionType.takesArg');\n    if (optionType.takesArg) {\n        assert.string(optionType.helpArg, 'optionType.helpArg');\n    }\n    assert.func(optionType.parseArg, 'optionType.parseArg');\n    assert.optionalBool(optionType.array, 'optionType.array');\n    assert.optionalBool(optionType.arrayFlatten, 'optionType.arrayFlatten');\n\n    optionTypes[optionType.name] = {\n        takesArg: optionType.takesArg,\n        helpArg: optionType.helpArg,\n        parseArg: optionType.parseArg,\n        array: optionType.array,\n        arrayFlatten: optionType.arrayFlatten,\n        default: optionType.default\n    }\n}\n\n\nfunction getOptionType(name) {\n    assert.string(name, 'name');\n    return optionTypes[name];\n}\n\n\n/**\n * Return a synopsis string for the given option spec.\n *\n * Examples:\n *      > synopsisFromOpt({names: ['help', 'h'], type: 'bool'});\n *      '[ --help | -h ]'\n *      > synopsisFromOpt({name: 'file', type: 'string', helpArg: 'FILE'});\n *      '[ --file=FILE ]'\n */\nfunction synopsisFromOpt(o) {\n    assert.object(o, 'o');\n\n    if (o.hasOwnProperty('group')) {\n        return null;\n    }\n    var names = o.names || [o.name];\n    // `type` here could be undefined if, for example, the command has a\n    // dashdash option spec with a bogus 'type'.\n    var type = getOptionType(o.type);\n    var helpArg = o.helpArg || (type && type.helpArg) || 'ARG';\n    var parts = [];\n    names.forEach(function (name) {\n        var part = (name.length === 1 ? '-' : '--') + name;\n        if (type && type.takesArg) {\n            part += (name.length === 1 ? ' ' + helpArg : '=' + helpArg);\n        }\n        parts.push(part);\n    });\n    return ('[ ' + parts.join(' | ') + ' ]');\n};\n\n\nmodule.exports = {\n    createParser: createParser,\n    Parser: Parser,\n    parse: parse,\n    addOptionType: addOptionType,\n    getOptionType: getOptionType,\n    synopsisFromOpt: synopsisFromOpt,\n\n    // Bash completion-related exports\n    BASH_COMPLETION_TEMPLATE_PATH: BASH_COMPLETION_TEMPLATE_PATH,\n    bashCompletionFromOptions: bashCompletionFromOptions,\n    bashCompletionSpecFromOptions: bashCompletionSpecFromOptions,\n\n    // Export the parseFoo parsers because they might be useful as primitives\n    // for custom option types.\n    parseBool: parseBool,\n    parseString: parseString,\n    parseNumber: parseNumber,\n    parseInteger: parseInteger,\n    parsePositiveInteger: parsePositiveInteger,\n    parseDate: parseDate\n};\n"]}