{"version": 3, "sources": ["png.js", "parser-async.js", "chunkstream.js", "filter-parse-async.js", "filter-parse.js", "interlace.js", "paeth-predictor.js", "parser.js", "constants.js", "crc.js", "bitmapper.js", "format-normaliser.js", "packer-async.js", "packer.js", "bitpacker.js", "filter-pack.js", "png-sync.js", "parser-sync.js", "sync-inflate.js", "sync-reader.js", "filter-parse-sync.js", "packer-sync.js"], "names": [], "mappings": ";;;;;;;AAAA;AACA;AACA;ACFA,ADGA;AC<PERSON>,ADGA;ACFA,ADGA;AELA,ADGA,ADGA;AELA,ADGA,ADGA;AELA,ADGA,ADGA;AELA,ACHA,AFMA,ADGA;AELA,ACHA,AFMA,ADGA;AELA,ACHA,AFMA,ADGA;AELA,ACHA,ACHA,AHSA,ADGA;AELA,ACHA,ACHA,AHSA,ADGA;AELA,ACHA,ACHA,AHSA,ADGA;AELA,ACHA,ACHA,ACHA,AJYA,ADGA;AELA,ACHA,ACHA,ACHA,AJYA,ADGA;AELA,ACHA,ACHA,ACHA,AJYA,ADGA;AELA,ACHA,ACHA,ACHA,ACHA,ALeA,ADGA;AELA,ACHA,ACHA,ACHA,ACHA,ALeA,ADGA;AELA,ACHA,ACHA,ACHA,ACHA,ALeA,ADGA;AELA,ACHA,ACHA,ACHA,ACHA,ALeA,AMlBA,APqBA;AELA,ACHA,ACHA,ACHA,ACHA,ALeA,AMlBA,APqBA;AELA,ACHA,ACHA,ACHA,ACHA,ALeA,AMlBA,APqBA;AELA,AMlBA,ALeA,ACHA,ACHA,ACHA,ALeA,AMlBA,APqBA;AELA,AMlBA,ALeA,ACHA,ACHA,ACHA,ALeA,AMlBA,APqBA;AELA,AMlBA,ALeA,ACHA,ACHA,ACHA,ALeA,AMlBA,APqBA;AELA,AMlBA,ACHA,ANkBA,ACHA,ACHA,ACHA,ALeA,AMlBA,APqBA;AELA,AMlBA,ACHA,ANkBA,ACHA,ACHA,ACHA,ALeA,AMlBA,APqBA;AELA,AMlBA,ACHA,ANkBA,ACHA,ACHA,ACHA,ALeA,AMlBA,APqBA;AU7BA,ARwBA,AMlBA,ACHA,ANkBA,ACHA,ACHA,ACHA,ALeA,AMlBA,APqBA;AU7BA,ARwBA,AMlBA,ACHA,ANkBA,ACHA,ACHA,ACHA,ALeA,AMlBA,APqBA;AU7BA,ARwBA,AMlBA,ACHA,ANkBA,ACHA,ACHA,ACHA,ALeA,AMlBA,APqBA;AU7BA,ARwBA,AMlBA,ACHA,ANkBA,ACHA,AOrBA,ANkBA,ACHA,ALeA,AMlBA,APqBA;AU7BA,ARwBA,AMlBA,ACHA,ANkBA,ACHA,AOrBA,ANkBA,ACHA,ALeA,AMlBA,APqBA;AU7BA,ARwBA,AMlBA,ACHA,ALeA,AOrBA,ANkBA,AJYA,AMlBA,APqBA;AU7BA,ARwBA,AMlBA,ACHA,ALeA,AOrBA,ANkBA,AOrBA,AXiCA,AMlBA,APqBA;AU7BA,ARwBA,AMlBA,ACHA,ALeA,AOrBA,ANkBA,AOrBA,AXiCA,AMlBA,APqBA;AU7BA,ARwBA,AMlBA,ACHA,ALeA,AOrBA,ANkBA,AOrBA,AXiCA,AMlBA,APqBA;AU7BA,ARwBA,AMlBA,ACHA,ALeA,AOrBA,ANkBA,AOrBA,ACHA,AZoCA,AMlBA,APqBA;AU7BA,ARwBA,AMlBA,ACHA,ALeA,AOrBA,ANkBA,AOrBA,ACHA,AZoCA,AMlBA,APqBA;AU7BA,ARwBA,AMlBA,ACHA,ALeA,AOrBA,ANkBA,AOrBA,ACHA,AZoCA,AMlBA,APqBA;AU7BA,AIZA,AZoCA,AMlBA,ACHA,ALeA,AOrBA,ANkBA,AOrBA,ACHA,AZoCA,AMlBA,APqBA;AU7BA,AIZA,AZoCA,AMlBA,ACHA,ALeA,AOrBA,ANkBA,AOrBA,ACHA,AZoCA,AMlBA,APqBA;AU7BA,AIZA,AZoCA,AMlBA,ACHA,ALeA,AOrBA,ANkBA,AOrBA,ACHA,AZoCA,AMlBA,APqBA;AU7BA,AIZA,AZoCA,AMlBA,ACHA,AMlBA,AXiCA,AOrBA,ANkBA,AOrBA,ACHA,AZoCA,AMlBA,APqBA;AU7BA,AIZA,AZoCA,AMlBA,ACHA,AMlBA,AXiCA,AOrBA,ANkBA,AOrBA,ACHA,AZoCA,AMlBA,APqBA;AU7BA,AIZA,AZoCA,AMlBA,ACHA,AMlBA,AXiCA,AOrBA,ANkBA,AOrBA,ACHA,AZoCA,AMlBA,APqBA;AU7BA,AIZA,AZoCA,AMlBA,ACHA,AMlBA,AXiCA,AOrBA,ANkBA,AOrBA,ACHA,AZoCA,AMlBA,AS3BA,AhBgDA;AU7BA,AIZA,AZoCA,AMlBA,ACHA,AMlBA,AXiCA,AOrBA,ANkBA,AOrBA,ACHA,AZoCA,AMlBA,AS3BA,AhBgDA;AU7BA,AIZA,AZoCA,AMlBA,ACHA,AMlBA,AXiCA,AOrBA,ANkBA,AOrBA,ACHA,AZoCA,AMlBA,AS3BA,AhBgDA;AU7BA,AIZA,AZoCA,AMlBA,ACHA,AMlBA,AXiCA,AOrBA,ANkBA,AOrBA,ACHA,AZoCA,AgBhDA,AV8BA,AS3BA,AhBgDA;AU7BA,AIZA,AZoCA,AMlBA,ACHA,AMlBA,AXiCA,AOrBA,ANkBA,AOrBA,ACHA,AZoCA,AgBhDA,AV8BA,AS3BA,AhBgDA;AU7BA,AIZA,AZoCA,AMlBA,ACHA,AMlBA,AXiCA,AOrBA,ANkBA,AOrBA,ACHA,AZoCA,AgBhDA,AV8BA,AS3BA,AhBgDA;AU7BA,AIZA,AZoCA,AMlBA,ACHA,AMlBA,AXiCA,AOrBA,ANkBA,AOrBA,ACHA,AZoCA,AgBhDA,AV8BA,AS3BA,AhBgDA,AkBtDA;ARyBA,AIZA,AZoCA,AMlBA,ACHA,AMlBA,AXiCA,AOrBA,ANkBA,AOrBA,ACHA,AZoCA,AgBhDA,AV8BA,AS3BA,AhBgDA,AkBtDA;ARyBA,AIZA,AZoCA,AMlBA,ACHA,AMlBA,AXiCA,AOrBA,ANkBA,AOrBA,ACHA,AZoCA,AgBhDA,AV8BA,AS3BA,AhBgDA,AkBtDA;ARyBA,AIZA,AZoCA,AMlBA,ACHA,AMlBA,AXiCA,AOrBA,ANkBA,AOrBA,ACHA,AZoCA,AgBhDA,AV8BA,AS3BA,AhBgDA,AkBtDA,ACHA;AT4BA,AIZA,AZoCA,AMlBA,ACHA,AMlBA,AXiCA,AOrBA,ANkBA,AOrBA,ACHA,AZoCA,AgBhDA,AV8BA,AS3BA,AhBgDA,AkBtDA,ACHA;AT4BA,AIZA,AZoCA,AOrBA,AMlBA,AXiCA,AOrBA,ANkBA,AOrBA,ACHA,AZoCA,AgBhDA,AV8BA,AS3BA,AhBgDA,AkBtDA,ACHA;AT4BA,AIZA,AZoCA,AOrBA,AMlBA,AKfA,AhBgDA,AOrBA,ANkBA,AOrBA,ACHA,AZoCA,AgBhDA,AV8BA,AS3BA,AhBgDA,AkBtDA,ACHA;AT4BA,AIZA,AZoCA,AOrBA,AMlBA,AKfA,AhBgDA,AOrBA,ANkBA,AOrBA,ACHA,AZoCA,AgBhDA,AV8BA,AS3BA,AhBgDA,AkBtDA,ACHA;AT4BA,AIZA,AZoCA,AOrBA,AMlBA,AKfA,AhBgDA,AOrBA,ANkBA,AOrBA,ACHA,AZoCA,AgBhDA,AV8BA,AS3BA,AhBgDA,AkBtDA,ACHA;AT4BA,AIZA,AZoCA,AOrBA,AMlBA,AKfA,AhBgDA,AOrBA,ANkBA,AOrBA,AS3BA,ARwBA,AZoCA,AgBhDA,AV8BA,AS3BA,AhBgDA,AkBtDA,ACHA;AT4BA,AIZA,AZoCA,AOrBA,AMlBA,AKfA,AhBgDA,AOrBA,ANkBA,AOrBA,AS3BA,ARwBA,AZoCA,AgBhDA,AV8BA,AS3BA,AhBgDA,AkBtDA,ACHA;AT4BA,AIZA,AZoCA,AOrBA,AMlBA,AKfA,AhBgDA,AOrBA,ANkBA,AOrBA,AS3BA,ARwBA,AZoCA,AgBhDA,AV8BA,APqBA,AkBtDA,ACHA;AT4BA,AIZA,AZoCA,AOrBA,AMlBA,AKfA,AhBgDA,AOrBA,ANkBA,AOrBA,AS3BA,ARwBA,AZoCA,AgBhDA,AV8BA,APqBA,AkBtDA,ACHA;AT4BA,AIZA,AZoCA,AOrBA,AMlBA,AKfA,AhBgDA,AOrBA,ANkBA,AOrBA,AS3BA,ARwBA,AZoCA,AgBhDA,AV8BA,APqBA,AkBtDA,ACHA;AT4BA,AIZA,AZoCA,AOrBA,AMlBA,AKfA,AhBgDA,AOrBA,ANkBA,AOrBA,AS3BA,ARwBA,AZoCA,AgBhDA,AV8BA,APqBA,AkBtDA,ACHA;AT4BA,AIZA,AZoCA,AOrBA,AMlBA,AKfA,AhBgDA,AOrBA,ANkBA,AOrBA,AS3BA,ARwBA,AZoCA,AgBhDA,AV8BA,APqBA,AkBtDA,ACHA;AT4BA,AIZA,AZoCA,AOrBA,AMlBA,AKfA,AhBgDA,AOrBA,ANkBA,AOrBA,AS3BA,ARwBA,AZoCA,AgBhDA,AV8BA,APqBA,AkBtDA,ACHA;AT4BA,AIZA,AZoCA,AOrBA,AMlBA,AKfA,AhBgDA,AOrBA,ANkBA,AOrBA,AS3BA,ARwBA,AZoCA,AgBhDA,AV8BA,APqBA,AkBtDA,ACHA;AT4BA,AIZA,AZoCA,AavCA,AKfA,AhBgDA,AOrBA,ANkBA,AOrBA,AS3BA,ARwBA,AZoCA,AgBhDA,AV8BA,APqBA,AkBtDA,ACHA;AT4BA,AIZA,AZoCA,AavCA,AKfA,AhBgDA,AOrBA,ANkBA,AOrBA,AS3BA,ARwBA,AZoCA,AgBhDA,AV8BA,APqBA,AkBtDA,ACHA;AT4BA,AIZA,AZoCA,AavCA,AKfA,AhBgDA,AOrBA,ANkBA,AOrBA,AS3BA,ARwBA,AZoCA,AgBhDA,AV8BA,APqBA,AkBtDA,ACHA;AT4BA,AIZA,AZoCA,AavCA,AKfA,AhBgDA,AOrBA,ANkBA,AOrBA,AS3BA,ARwBA,AZoCA,AgBhDA,AV8BA,APqBA,AkBtDA,ACHA;AT4BA,AIZA,AZoCA,AavCA,AKfA,AhBgDA,AOrBA,ANkBA,AOrBA,AS3BA,ARwBA,AZoCA,AgBhDA,AV8BA,APqBA,AkBtDA,ACHA;AT4BA,AIZA,AZoCA,AavCA,AKfA,AhBgDA,AOrBA,ANkBA,AOrBA,AS3BA,ARwBA,AZoCA,AgBhDA,AV8BA,APqBA,AkBtDA,ACHA;AT4BA,AIZA,AZoCA,AavCA,AKfA,AhBgDA,AOrBA,ANkBA,AOrBA,AS3BA,ARwBA,AZoCA,AgBhDA,AV8BA,APqBA,AkBtDA,ACHA;AT4BA,AIZA,AZoCA,AavCA,AKfA,AhBgDA,AOrBA,ANkBA,AOrBA,AS3BA,ARwBA,AZoCA,AgBhDA,AV8BA,APqBA,AkBtDA,ACHA;AT4BA,AIZA,AZoCA,AavCA,AKfA,AhBgDA,AOrBA,ANkBA,AOrBA,AS3BA,ARwBA,AZoCA,AgBhDA,AV8BA,APqBA,AkBtDA,ACHA;AT4BA,AIZA,AZoCA,AavCA,AKfA,AhBgDA,AOrBA,ANkBA,AOrBA,AS3BA,ARwBA,AZoCA,AgBhDA,AV8BA,APqBA,AkBtDA,ACHA;AT4BA,AIZA,AZoCA,AavCA,AKfA,AhBgDA,AOrBA,ANkBA,AgBhDA,ARwBA,AZoCA,AgBhDA,AV8BA,APqBA,AkBtDA,ACHA;AT4BA,AIZA,AZoCA,AavCA,AKfA,AhBgDA,AOrBA,ANkBA,AgBhDA,ARwBA,AZoCA,AgBhDA,AV8BA,APqBA,AkBtDA,ACHA;AT4BA,AIZA,AZoCA,AavCA,AXiCA,AOrBA,ANkBA,AgBhDA,ARwBA,AZoCA,AgBhDA,AV8BA,APqBA,AkBtDA,ACHA;AT4BA,AIZA,AZoCA,AavCA,AXiCA,AOrBA,ANkBA,AgBhDA,ARwBA,AZoCA,AgBhDA,AV8BA,APqBA,AkBtDA,ACHA;AT4BA,AIZA,AZoCA,AavCA,AXiCA,AOrBA,ANkBA,AgBhDA,ARwBA,AZoCA,AgBhDA,AV8BA,APqBA,AkBtDA,ACHA;AT4BA,AIZA,AZoCA,AavCA,AXiCA,AOrBA,ANkBA,AgBhDA,ARwBA,AZoCA,AgBhDA,AV8BA,APqBA,AkBtDA,ACHA;AT4BA,AIZA,AZoCA,AavCA,AXiCA,AOrBA,ANkBA,AgBhDA,ARwBA,AZoCA,AgBhDA,AV8BA,APqBA,AkBtDA,ACHA;AT4BA,AIZA,AZoCA,AavCA,AXiCA,AOrBA,ANkBA,AgBhDA,ARwBA,AZoCA,AgBhDA,AV8BA,APqBA,AkBtDA,ACHA;AT4BA,AIZA,AZoCA,AavCA,AXiCA,AOrBA,ANkBA,AgBhDA,ARwBA,AZoCA,AgBhDA,AV8BA,APqBA,AkBtDA,ACHA;AT4BA,AIZA,AZoCA,AavCA,AXiCA,AOrBA,ANkBA,AgBhDA,ARwBA,AZoCA,AgBhDA,AV8BA,APqBA,AkBtDA,ACHA;AT4BA,AIZA,AZoCA,AavCA,AXiCA,AOrBA,ANkBA,AgBhDA,ARwBA,AZoCA,AgBhDA,AV8BA,APqBA,AkBtDA,ACHA;AT4BA,AIZA,AZoCA,AavCA,AXiCA,AOrBA,ANkBA,AgBhDA,ARwBA,AZoCA,AgBhDA,AV8BA,APqBA,AkBtDA,ACHA;AT4BA,AIZA,AZoCA,AavCA,AXiCA,AOrBA,ANkBA,AgBhDA,ARwBA,AZoCA,AgBhDA,AV8BA,APqBA,AkBtDA,ACHA;AT4BA,AIZA,AZoCA,AavCA,AXiCA,AOrBA,ANkBA,AgBhDA,ARwBA,AZoCA,AgBhDA,AV8BA,APqBA,AkBtDA,ACHA;AT4BA,AIZA,AZoCA,AavCA,AXiCA,AOrBA,ANkBA,AgBhDA,ARwBA,AZoCA,AgBhDA,AV8BA,APqBA,AkBtDA,ACHA;AT4BA,AIZA,AZoCA,AavCA,AXiCA,AOrBA,ANkBA,AgBhDA,ARwBA,AZoCA,AgBhDA,AV8BA,APqBA,AkBtDA,ACHA;AT4BA,AIZA,AZoCA,AavCA,AXiCA,AOrBA,ANkBA,AgBhDA,ARwBA,AZoCA,AgBhDA,AV8BA,APqBA,AkBtDA,ACHA;AT4BA,AIZA,AZoCA,AavCA,AXiCA,AOrBA,ANkBA,AgBhDA,ARwBA,AZoCA,AgBhDA,AV8BA,APqBA,AkBtDA,ACHA;AT4BA,AIZA,AZoCA,AavCA,AXiCA,AOrBA,ANkBA,AgBhDA,ARwBA,AZoCA,AgBhDA,AV8BA,APqBA,AkBtDA,ACHA;AT4BA,AIZA,AZoCA,AavCA,AXiCA,AOrBA,ANkBA,AgBhDA,ARwBA,AZoCA,AgBhDA,AV8BA,APqBA,AkBtDA,ACHA;AT4BA,AIZA,AZoCA,AavCA,AXiCA,AOrBA,AU9BA,ARwBA,AZoCA,AgBhDA,AV8BA,APqBA,AkBtDA,ACHA;AT4BA,AIZA,AZoCA,AavCA,AXiCA,AOrBA,AU9BA,ARwBA,AZoCA,AgBhDA,AV8BA,APqBA,AkBtDA,ACHA;AT4BA,AIZA,AZoCA,AavCA,AXiCA,AOrBA,AU9BA,ARwBA,AZoCA,AgBhDA,AV8BA,APqBA,AkBtDA,ACHA;AT4BA,AIZA,AZoCA,AavCA,AXiCA,AOrBA,AU9BA,ARwBA,AZoCA,AgBhDA,AV8BA,APqBA,AkBtDA,ACHA;AT4BA,AIZA,AZoCA,AavCA,AXiCA,AOrBA,AU9BA,ARwBA,AZoCA,AgBhDA,AV8BA,APqBA,AkBtDA,ACHA;AT4BA,AIZA,AZoCA,AavCA,AXiCA,AOrBA,AU9BA,ARwBA,AZoCA,AgBhDA,AV8BA,APqBA,AkBtDA,ACHA;AT4BA,AIZA,AZoCA,AavCA,AXiCA,AOrBA,AU9BA,ARwBA,AZoCA,AgBhDA,AV8BA,APqBA,AkBtDA,ACHA;AT4BA,AIZA,AZoCA,AavCA,AXiCA,AOrBA,AU9BA,ARwBA,AZoCA,AgBhDA,AV8BA,APqBA,AkBtDA;ARyBA,AIZA,AZoCA,AavCA,AXiCA,AOrBA,AU9BA,ARwBA,AZoCA,AgBhDA,AV8BA,APqBA,AkBtDA;ARyBA,AIZA,AZoCA,AavCA,AXiCA,AOrBA,AU9BA,ARwBA,AZoCA,AgBhDA,AV8BA,APqBA,AkBtDA;ARyBA,AIZA,AZoCA,AavCA,AXiCA,AOrBA,AENA,AZoCA,AgBhDA,AV8BA,APqBA,AkBtDA;ARyBA,AIZA,AZoCA,AavCA,AXiCA,AOrBA,AENA,AZoCA,AgBhDA,AV8BA,APqBA,AkBtDA;ARyBA,AIZA,AZoCA,AavCA,AXiCA,AOrBA,AENA,AZoCA,AgBhDA,AV8BA,APqBA,AkBtDA;ARyBA,AIZA,AZoCA,AavCA,AXiCA,AOrBA,AENA,AZoCA,AgBhDA,AV8BA,APqBA,AkBtDA;ARyBA,AIZA,AZoCA,AavCA,AXiCA,AOrBA,AENA,AZoCA,AgBhDA,AV8BA,APqBA,AkBtDA;ARyBA,AIZA,AZoCA,AavCA,AXiCA,AOrBA,AENA,AZoCA,AgBhDA,AV8BA,APqBA,AkBtDA;ARyBA,AIZA,AZoCA,AavCA,AXiCA,AOrBA,AENA,AZoCA,AgBhDA,AV8BA,APqBA,AkBtDA;ARyBA,AIZA,AZoCA,AavCA,AXiCA,AOrBA,AENA,AZoCA,AgBhDA,AV8BA,APqBA,AkBtDA;ARyBA,AIZA,AZoCA,AavCA,AXiCA,AOrBA,AENA,AZoCA,AgBhDA,AV8BA,APqBA,AkBtDA;ARyBA,AIZA,AZoCA,AavCA,AXiCA,AOrBA,AENA,AZoCA,AgBhDA,AV8BA,APqBA,AkBtDA;ARyBA,AIZA,AZoCA,AavCA,AXiCA,AOrBA,AENA,AZoCA,AgBhDA,AV8BA,APqBA,AkBtDA;ARyBA,AIZA,AZoCA,AavCA,AXiCA,AS3BA,AZoCA,AgBhDA,AV8BA,APqBA,AkBtDA;ARyBA,AIZA,AZoCA,AavCA,AXiCA,AS3BA,AZoCA,AgBhDA,AV8BA,APqBA,AkBtDA;ARyBA,AIZA,AZoCA,AavCA,AXiCA,AS3BA,AZoCA,AgBhDA,AV8BA,APqBA,AkBtDA;ARyBA,AIZA,AZoCA,AavCA,AXiCA,AS3BA,AZoCA,AgBhDA,AV8BA,APqBA,AkBtDA;ARyBA,AIZA,AZoCA,AavCA,AXiCA,AS3BA,AZoCA,AgBhDA,AV8BA,APqBA,AkBtDA;ARyBA,AIZA,AZoCA,AavCA,AXiCA,AS3BA,AZoCA,AgBhDA,AV8BA,APqBA,AkBtDA;ARyBA,AIZA,AZoCA,AavCA,AXiCA,AS3BA,AZoCA,AgBhDA,AV8BA,APqBA,AkBtDA;ARyBA,AIZA,AZoCA,AavCA,AXiCA,AS3BA,AZoCA,AgBhDA,AV8BA,APqBA,AkBtDA;ARyBA,AIZA,AZoCA,AavCA,AXiCA,AS3BA,AZoCA,AgBhDA,AV8BA,APqBA,AkBtDA;ARyBA,AIZA,AZoCA,AavCA,AXiCA,AS3BA,AZoCA,AgBhDA,AV8BA,APqBA,AkBtDA;ARyBA,AIZA,AZoCA,AavCA,AXiCA,AS3BA,AZoCA,AgBhDA,AV8BA,APqBA,AkBtDA;ARyBA,AIZA,AZoCA,AavCA,AXiCA,AS3BA,AZoCA,AgBhDA,AV8BA,APqBA,AkBtDA;ARyBA,AIZA,AZoCA,AavCA,AXiCA,AS3BA,AZoCA,AgBhDA,AV8BA,APqBA,AkBtDA;ARyBA,AIZA,AZoCA,AavCA,AXiCA,AS3BA,AZoCA,AgBhDA,AV8BA,APqBA,AkBtDA;ARyBA,AIZA,AZoCA,AavCA,AXiCA,AS3BA,AZoCA,AgBhDA,AV8BA,APqBA,AkBtDA;ARyBA,AIZA,AZoCA,AavCA,AXiCA,AS3BA,AZoCA,AgBhDA,AV8BA,APqBA,AkBtDA;ARyBA,AIZA,AZoCA,AavCA,AXiCA,AS3BA,AZoCA,AgBhDA,AV8BA,APqBA,AkBtDA;ARyBA,AIZA,AZoCA,AavCA,AXiCA,AS3BA,AZoCA,AgBhDA,AV8BA,APqBA,AkBtDA;ARyBA,AIZA,AZoCA,AavCA,AXiCA,AS3BA,AZoCA,AgBhDA,AV8BA,APqBA,AkBtDA;ARyBA,AIZA,AZoCA,AavCA,AXiCA,AS3BA,AZoCA,AgBhDA,AV8BA,APqBA,AkBtDA;ARyBA,AIZA,AZoCA,AavCA,AXiCA,AS3BA,AZoCA,AgBhDA,AV8BA,APqBA,AkBtDA;ARyBA,AIZA,AZoCA,AavCA,AXiCA,AS3BA,AZoCA,AgBhDA,AV8BA,APqBA,AkBtDA;ARyBA,AIZA,AZoCA,AavCA,AXiCA,AS3BA,AZoCA,AgBhDA,AV8BA,APqBA,AkBtDA;ARyBA,AIZA,AZoCA,AavCA,AXiCA,AS3BA,AZoCA,AgBhDA,AV8BA,APqBA,AkBtDA;ARyBA,AIZA,AZoCA,AavCA,AXiCA,AHSA,AgBhDA,AV8BA,APqBA,AkBtDA;ARyBA,AIZA,AZoCA,AavCA,AXiCA,AHSA,AgBhDA,AV8BA,APqBA,AkBtDA;ARyBA,AIZA,AZoCA,AavCA,AXiCA,AHSA,AgBhDA,AV8BA,APqBA,AkBtDA;ARyBA,AIZA,AZoCA,AavCA,AXiCA,AHSA,AgBhDA,AV8BA,APqBA,AkBtDA;ARyBA,AIZA,AZoCA,AavCA,AXiCA,AHSA,AgBhDA,AV8BA,APqBA,AkBtDA;ARyBA,AIZA,AZoCA,AavCA,AXiCA,AHSA,AgBhDA,AV8BA,APqBA,AkBtDA;ARyBA,AIZA,AZoCA,AavCA,AXiCA,AHSA,AgBhDA,AV8BA,APqBA,AkBtDA;ARyBA,AIZA,AZoCA,AavCA,AXiCA,AHSA,AgBhDA,AV8BA,APqBA,AkBtDA;ARyBA,AIZA,AZoCA,AavCA,AXiCA,AHSA,AgBhDA,AV8BA,APqBA,AkBtDA;ARyBA,AIZA,AZoCA,AavCA,AXiCA,AHSA,AgBhDA,AV8BA,APqBA,AkBtDA;ARyBA,AIZA,AZoCA,AavCA,AXiCA,AHSA,AMlBA,APqBA,AkBtDA;ARyBA,AIZA,AZoCA,AavCA,AXiCA,AHSA,AMlBA,APqBA,AkBtDA;ARyBA,AIZA,AZoCA,AavCA,AXiCA,AHSA,AMlBA,APqBA,AkBtDA;ARyBA,AIZA,AZoCA,AavCA,AXiCA,AHSA,AMlBA,APqBA,AkBtDA;ARyBA,AIZA,AZoCA,AavCA,AXiCA,AHSA,AMlBA,APqBA,AkBtDA;ARyBA,AIZA,AZoCA,AavCA,AXiCA,AHSA,AMlBA,APqBA,AkBtDA;ARyBA,AIZA,AZoCA,AavCA,AXiCA,AHSA,AMlBA,APqBA,AkBtDA;ARyBA,AIZA,AZoCA,AavCA,AXiCA,AHSA,AMlBA,APqBA,AkBtDA;ARyBA,AIZA,AZoCA,AavCA,AXiCA,AHSA,AMlBA,APqBA,AkBtDA;ARyBA,AIZA,AZoCA,AavCA,AXiCA,AHSA,AMlBA,APqBA,AkBtDA;ARyBA,AIZA,AZoCA,AavCA,AXiCA,AHSA,AMlBA,APqBA,AkBtDA;ARyBA,AIZA,AZoCA,AavCA,AXiCA,AGTA,APqBA,AkBtDA;ARyBA,AIZA,AZoCA,AavCA,AXiCA,AGTA,APqBA,AkBtDA;ARyBA,AIZA,AZoCA,AavCA,AXiCA,AGTA,APqBA,AkBtDA;ARyBA,AIZA,AZoCA,AavCA,AXiCA,AGTA,APqBA,AkBtDA;ARyBA,AIZA,AZoCA,AavCA,AXiCA,AGTA,APqBA,AkBtDA;ARyBA,AIZA,AZoCA,AavCA,AXiCA,AGTA,APqBA,AkBtDA;ARyBA,AIZA,AZoCA,AavCA,AXiCA,AGTA,AWjCA;ARyBA,AIZA,AZoCA,AavCA,AXiCA,AGTA,AWjCA;ARyBA,AIZA,AZoCA,AavCA,AXiCA,AGTA,AWjCA;ARyBA,AIZA,AZoCA,AavCA,AXiCA,AGTA,AWjCA;ARyBA,AIZA,AZoCA,AavCA,AXiCA,AGTA,AWjCA;ARyBA,AIZA,AZoCA,AavCA,AXiCA,AGTA,AWjCA;ARyBA,AIZA,AZoCA,AavCA,AXiCA,AGTA,AWjCA;ARyBA,AIZA,AZoCA,AavCA,AXiCA,AGTA,AWjCA;ARyBA,AIZA,AZoCA,AavCA,AXiCA,AGTA,AWjCA;ARyBA,AIZA,AZoCA,AavCA,AXiCA,AGTA,AWjCA;ARyBA,AIZA,AZoCA,AavCA,ARwBA,AWjCA;ARyBA,AIZA,AZoCA,AavCA,ARwBA,AWjCA;ARyBA,ARwBA,AavCA,ARwBA,AWjCA;ARyBA,ARwBA,AavCA,ARwBA,AWjCA;ARyBA,ARwBA,AavCA,ARwBA,AWjCA;ARyBA,ARwBA,AavCA,ARwBA,AWjCA;ARyBA,ARwBA,AavCA,ARwBA,AWjCA;ARyBA,ARwBA,AavCA,ARwBA,AWjCA;ARyBA,ARwBA,AavCA,ARwBA,AWjCA;ARyBA,ARwBA,AavCA,ARwBA,AWjCA;ARyBA,ARwBA,AavCA,ARwBA,AWjCA;ARyBA,ARwBA,AavCA,ARwBA,AWjCA;ARyBA,ARwBA,AavCA,ARwBA,AWjCA;ARyBA,ARwBA,AavCA,ARwBA,AWjCA;ARyBA,ARwBA,AavCA,ARwBA,AWjCA;ARyBA,ARwBA,AavCA,ARwBA,AWjCA;ARyBA,ARwBA,AavCA,ARwBA,AWjCA;ARyBA,ARwBA,AavCA,ARwBA,AWjCA;ARyBA,ARwBA,AavCA,ARwBA,AWjCA;ARyBA,ARwBA,AavCA,ARwBA,AWjCA;ARyBA,ARwBA,AavCA,ARwBA,AWjCA;ARyBA,ARwBA,AavCA,ARwBA,AWjCA;ARyBA,ARwBA,AavCA,ARwBA,AWjCA;ARyBA,ARwBA,AavCA,ARwBA,AWjCA;ARyBA,ARwBA,AavCA,ARwBA,AWjCA;ARyBA,ARwBA,AavCA,ARwBA,AWjCA;ARyBA,ARwBA,AavCA,ARwBA,AWjCA;ARyBA,ARwBA,AavCA,ARwBA,AWjCA;ARyBA,ARwBA,AavCA,ARwBA,AWjCA;ARyBA,ARwBA,AavCA,ARwBA,AWjCA;ARyBA,ARwBA,AavCA,ARwBA,AWjCA;ARyBA,ARwBA,AavCA,ARwBA,AWjCA;ARyBA,AKfA,ARwBA;AGRA,AKfA,ARwBA;AGRA,AKfA,ARwBA;AGRA,AKfA,ARwBA;AGRA,AKfA,ARwBA;AGRA,AKfA,ARwBA;AGRA,AKfA,ARwBA;AGRA,AKfA,ARwBA;AGRA,AKfA,ARwBA;AGRA,AKfA,ARwBA;AGRA,AKfA,ARwBA;AGRA,AKfA,ARwBA;AGRA,AKfA,ARwBA;AGRA,AKfA,ARwBA;AGRA,AKfA,ARwBA;AGRA,AKfA,ARwBA;AGRA,AKfA,ARwBA;AGRA,AKfA,ARwBA;AGRA,AKfA,ARwBA;AGRA,AKfA,ARwBA;AGRA,AHSA;AGRA,AHSA;AGRA,AHSA;AGRA,AHSA;AGRA,AHSA;AGRA,AHSA;AGRA,AHSA;AGRA,AHSA;AGRA,AHSA;AGRA,AHSA;AGRA,AHSA;AGRA,AHSA;AGRA,AHSA;AGRA,AHSA;AGRA,AHSA;AGRA,AHSA;AGRA,AHSA;AGRA,AHSA;AGRA,AHSA;AGRA,AHSA;AGRA,AHSA;AGRA,AHSA;AGRA,AHSA;AGRA,AHSA;AGRA,AHSA;AGRA,AHSA;AGRA,AHSA;AGRA,AHSA;AGRA,AHSA;AGRA,AHSA;AGRA,AHSA;AGRA,AHSA;AGRA,AHSA;AGRA,AHSA;AGRA,AHSA;AGRA,AHSA;AGRA,AHSA;AGRA,AHSA;AGRA,AHSA;AGRA,AHSA;AGRA,AHSA;AGRA,AHSA;AGRA,AHSA;AGRA,AHSA;AGRA,AHSA;AGRA,AHSA;AGRA,AHSA;AGRA,AHSA;AGRA,AHSA;AGRA,AHSA;AGRA,AHSA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.js", "sourcesContent": ["\n\nvar util = require('util');\nvar Stream = require('stream');\nvar Parser = require('./parser-async');\nvar Packer = require('./packer-async');\nvar PNGSync = require('./png-sync');\n\n\nvar PNG = exports.PNG = function(options) {\n  Stream.call(this);\n\n  options = options || {}; // eslint-disable-line no-param-reassign\n\n  // coerce pixel dimensions to integers (also coerces undefined -> 0):\n  this.width = options.width | 0;\n  this.height = options.height | 0;\n\n  this.data = this.width > 0 && this.height > 0 ?\n    new Buffer(4 * this.width * this.height) : null;\n\n  if (options.fill && this.data) {\n    this.data.fill(0);\n  }\n\n  this.gamma = 0;\n  this.readable = this.writable = true;\n\n  this._parser = new Parser(options);\n\n  this._parser.on('error', this.emit.bind(this, 'error'));\n  this._parser.on('close', this._handleClose.bind(this));\n  this._parser.on('metadata', this._metadata.bind(this));\n  this._parser.on('gamma', this._gamma.bind(this));\n  this._parser.on('parsed', function(data) {\n    this.data = data;\n    this.emit('parsed', data);\n  }.bind(this));\n\n  this._packer = new Packer(options);\n  this._packer.on('data', this.emit.bind(this, 'data'));\n  this._packer.on('end', this.emit.bind(this, 'end'));\n  this._parser.on('close', this._handleClose.bind(this));\n  this._packer.on('error', this.emit.bind(this, 'error'));\n\n};\nutil.inherits(PNG, Stream);\n\nPNG.sync = PNGSync;\n\nPNG.prototype.pack = function() {\n\n  if (!this.data || !this.data.length) {\n    this.emit('error', 'No data provided');\n    return this;\n  }\n\n  process.nextTick(function() {\n    this._packer.pack(this.data, this.width, this.height, this.gamma);\n  }.bind(this));\n\n  return this;\n};\n\n\nPNG.prototype.parse = function(data, callback) {\n\n  if (callback) {\n    var onParsed, onError;\n\n    onParsed = function(parsedData) {\n      this.removeListener('error', onError);\n\n      this.data = parsedData;\n      callback(null, this);\n    }.bind(this);\n\n    onError = function(err) {\n      this.removeListener('parsed', onParsed);\n\n      callback(err, null);\n    }.bind(this);\n\n    this.once('parsed', onParsed);\n    this.once('error', onError);\n  }\n\n  this.end(data);\n  return this;\n};\n\nPNG.prototype.write = function(data) {\n  this._parser.write(data);\n  return true;\n};\n\nPNG.prototype.end = function(data) {\n  this._parser.end(data);\n};\n\nPNG.prototype._metadata = function(metadata) {\n  this.width = metadata.width;\n  this.height = metadata.height;\n\n  this.emit('metadata', metadata);\n};\n\nPNG.prototype._gamma = function(gamma) {\n  this.gamma = gamma;\n};\n\nPNG.prototype._handleClose = function() {\n  if (!this._parser.writable && !this._packer.readable) {\n    this.emit('close');\n  }\n};\n\n\nPNG.bitblt = function(src, dst, srcX, srcY, width, height, deltaX, deltaY) { // eslint-disable-line max-params\n  // coerce pixel dimensions to integers (also coerces undefined -> 0):\n  /* eslint-disable no-param-reassign */\n  srcX |= 0;\n  srcY |= 0;\n  width |= 0;\n  height |= 0;\n  deltaX |= 0;\n  deltaY |= 0;\n  /* eslint-enable no-param-reassign */\n\n  if (srcX > src.width || srcY > src.height || srcX + width > src.width || srcY + height > src.height) {\n    throw new Error('bitblt reading outside image');\n  }\n\n  if (deltaX > dst.width || deltaY > dst.height || deltaX + width > dst.width || deltaY + height > dst.height) {\n    throw new Error('bitblt writing outside image');\n  }\n\n  for (var y = 0; y < height; y++) {\n    src.data.copy(dst.data,\n      ((deltaY + y) * dst.width + deltaX) << 2,\n      ((srcY + y) * src.width + srcX) << 2,\n      ((srcY + y) * src.width + srcX + width) << 2\n    );\n  }\n};\n\n\nPNG.prototype.bitblt = function(dst, srcX, srcY, width, height, deltaX, deltaY) { // eslint-disable-line max-params\n\n  PNG.bitblt(this, dst, srcX, srcY, width, height, deltaX, deltaY);\n  return this;\n};\n\nPNG.adjustGamma = function(src) {\n  if (src.gamma) {\n    for (var y = 0; y < src.height; y++) {\n      for (var x = 0; x < src.width; x++) {\n        var idx = (src.width * y + x) << 2;\n\n        for (var i = 0; i < 3; i++) {\n          var sample = src.data[idx + i] / 255;\n          sample = Math.pow(sample, 1 / 2.2 / src.gamma);\n          src.data[idx + i] = Math.round(sample * 255);\n        }\n      }\n    }\n    src.gamma = 0;\n  }\n};\n\nPNG.prototype.adjustGamma = function() {\n  PNG.adjustGamma(this);\n};\n", "\n\nvar util = require('util');\nvar zlib = require('zlib');\nvar ChunkStream = require('./chunkstream');\nvar FilterAsync = require('./filter-parse-async');\nvar Parser = require('./parser');\nvar bitmapper = require('./bitmapper');\nvar formatNormaliser = require('./format-normaliser');\n\nvar ParserAsync = module.exports = function(options) {\n  ChunkStream.call(this);\n\n  this._parser = new Parser(options, {\n    read: this.read.bind(this),\n    error: this._handleError.bind(this),\n    metadata: this._handleMetaData.bind(this),\n    gamma: this.emit.bind(this, 'gamma'),\n    palette: this._handlePalette.bind(this),\n    transColor: this._handleTransColor.bind(this),\n    finished: this._finished.bind(this),\n    inflateData: this._inflateData.bind(this),\n    simpleTransparency: this._simpleTransparency.bind(this),\n    headersFinished: this._headersFinished.bind(this)\n  });\n  this._options = options;\n  this.writable = true;\n\n  this._parser.start();\n};\nutil.inherits(ParserAsync, ChunkStream);\n\n\nParserAsync.prototype._handleError = function(err) {\n\n  this.emit('error', err);\n\n  this.writable = false;\n\n  this.destroy();\n\n  if (this._inflate && this._inflate.destroy) {\n    this._inflate.destroy();\n  }\n\n  if (this._filter) {\n    this._filter.destroy();\n    // For backward compatibility with Node 7 and below.\n    // Suppress errors due to _inflate calling write() even after\n    // it's destroy()'ed.\n    this._filter.on('error', function() {});\n  }\n\n  this.errord = true;\n};\n\nParserAsync.prototype._inflateData = function(data) {\n  if (!this._inflate) {\n    if (this._bitmapInfo.interlace) {\n      this._inflate = zlib.createInflate();\n\n      this._inflate.on('error', this.emit.bind(this, 'error'));\n      this._filter.on('complete', this._complete.bind(this));\n\n      this._inflate.pipe(this._filter);\n    }\n    else {\n      var rowSize = ((this._bitmapInfo.width * this._bitmapInfo.bpp * this._bitmapInfo.depth + 7) >> 3) + 1;\n      var imageSize = rowSize * this._bitmapInfo.height;\n      var chunkSize = Math.max(imageSize, zlib.Z_MIN_CHUNK);\n\n      this._inflate = zlib.createInflate({ chunkSize: chunkSize });\n      var leftToInflate = imageSize;\n\n      var emitError = this.emit.bind(this, 'error');\n      this._inflate.on('error', function(err) {\n        if (!leftToInflate) {\n          return;\n        }\n\n        emitError(err);\n      });\n      this._filter.on('complete', this._complete.bind(this));\n\n      var filterWrite = this._filter.write.bind(this._filter);\n      this._inflate.on('data', function(chunk) {\n        if (!leftToInflate) {\n          return;\n        }\n\n        if (chunk.length > leftToInflate) {\n          chunk = chunk.slice(0, leftToInflate);\n        }\n\n        leftToInflate -= chunk.length;\n\n        filterWrite(chunk);\n      });\n\n      this._inflate.on('end', this._filter.end.bind(this._filter));\n    }\n  }\n  this._inflate.write(data);\n};\n\nParserAsync.prototype._handleMetaData = function(metaData) {\n  this._metaData = metaData;\n  this._bitmapInfo = Object.create(metaData);\n\n  this._filter = new FilterAsync(this._bitmapInfo);\n};\n\nParserAsync.prototype._handleTransColor = function(transColor) {\n  this._bitmapInfo.transColor = transColor;\n};\n\nParserAsync.prototype._handlePalette = function(palette) {\n  this._bitmapInfo.palette = palette;\n};\n\nParserAsync.prototype._simpleTransparency = function() {\n  this._metaData.alpha = true;\n};\n\nParserAsync.prototype._headersFinished = function() {\n  // Up until this point, we don't know if we have a tRNS chunk (alpha)\n  // so we can't emit metadata any earlier\n  this.emit('metadata', this._metaData);\n};\n\nParserAsync.prototype._finished = function() {\n  if (this.errord) {\n    return;\n  }\n\n  if (!this._inflate) {\n    this.emit('error', 'No Inflate block');\n  }\n  else {\n    // no more data to inflate\n    this._inflate.end();\n  }\n  this.destroySoon();\n};\n\nParserAsync.prototype._complete = function(filteredData) {\n\n  if (this.errord) {\n    return;\n  }\n\n  try {\n    var bitmapData = bitmapper.dataToBitMap(filteredData, this._bitmapInfo);\n\n    var normalisedBitmapData = formatNormaliser(bitmapData, this._bitmapInfo);\n    bitmapData = null;\n  }\n  catch (ex) {\n    this._handleError(ex);\n    return;\n  }\n\n  this.emit('parsed', normalisedBitmapData);\n};\n", "\n\n\nvar util = require('util');\nvar Stream = require('stream');\n\n\nvar ChunkStream = module.exports = function() {\n  Stream.call(this);\n\n  this._buffers = [];\n  this._buffered = 0;\n\n  this._reads = [];\n  this._paused = false;\n\n  this._encoding = 'utf8';\n  this.writable = true;\n};\nutil.inherits(ChunkStream, Stream);\n\n\nChunkStream.prototype.read = function(length, callback) {\n\n  this._reads.push({\n    length: Math.abs(length), // if length < 0 then at most this length\n    allowLess: length < 0,\n    func: callback\n  });\n\n  process.nextTick(function() {\n    this._process();\n\n    // its paused and there is not enought data then ask for more\n    if (this._paused && this._reads.length > 0) {\n      this._paused = false;\n\n      this.emit('drain');\n    }\n  }.bind(this));\n};\n\nChunkStream.prototype.write = function(data, encoding) {\n\n  if (!this.writable) {\n    this.emit('error', new Error('Stream not writable'));\n    return false;\n  }\n\n  var dataBuffer;\n  if (Buffer.isBuffer(data)) {\n    dataBuffer = data;\n  }\n  else {\n    dataBuffer = new Buffer(data, encoding || this._encoding);\n  }\n\n  this._buffers.push(dataBuffer);\n  this._buffered += dataBuffer.length;\n\n  this._process();\n\n  // ok if there are no more read requests\n  if (this._reads && this._reads.length === 0) {\n    this._paused = true;\n  }\n\n  return this.writable && !this._paused;\n};\n\nChunkStream.prototype.end = function(data, encoding) {\n\n  if (data) {\n    this.write(data, encoding);\n  }\n\n  this.writable = false;\n\n  // already destroyed\n  if (!this._buffers) {\n    return;\n  }\n\n  // enqueue or handle end\n  if (this._buffers.length === 0) {\n    this._end();\n  }\n  else {\n    this._buffers.push(null);\n    this._process();\n  }\n};\n\nChunkStream.prototype.destroySoon = ChunkStream.prototype.end;\n\nChunkStream.prototype._end = function() {\n\n  if (this._reads.length > 0) {\n    this.emit('error',\n      new Error('Unexpected end of input')\n    );\n  }\n\n  this.destroy();\n};\n\nChunkStream.prototype.destroy = function() {\n\n  if (!this._buffers) {\n    return;\n  }\n\n  this.writable = false;\n  this._reads = null;\n  this._buffers = null;\n\n  this.emit('close');\n};\n\nChunkStream.prototype._processReadAllowingLess = function(read) {\n  // ok there is any data so that we can satisfy this request\n  this._reads.shift(); // == read\n\n  // first we need to peek into first buffer\n  var smallerBuf = this._buffers[0];\n\n  // ok there is more data than we need\n  if (smallerBuf.length > read.length) {\n\n    this._buffered -= read.length;\n    this._buffers[0] = smallerBuf.slice(read.length);\n\n    read.func.call(this, smallerBuf.slice(0, read.length));\n\n  }\n  else {\n    // ok this is less than maximum length so use it all\n    this._buffered -= smallerBuf.length;\n    this._buffers.shift(); // == smallerBuf\n\n    read.func.call(this, smallerBuf);\n  }\n};\n\nChunkStream.prototype._processRead = function(read) {\n  this._reads.shift(); // == read\n\n  var pos = 0;\n  var count = 0;\n  var data = new Buffer(read.length);\n\n  // create buffer for all data\n  while (pos < read.length) {\n\n    var buf = this._buffers[count++];\n    var len = Math.min(buf.length, read.length - pos);\n\n    buf.copy(data, pos, 0, len);\n    pos += len;\n\n    // last buffer wasn't used all so just slice it and leave\n    if (len !== buf.length) {\n      this._buffers[--count] = buf.slice(len);\n    }\n  }\n\n  // remove all used buffers\n  if (count > 0) {\n    this._buffers.splice(0, count);\n  }\n\n  this._buffered -= read.length;\n\n  read.func.call(this, data);\n};\n\nChunkStream.prototype._process = function() {\n\n  try {\n    // as long as there is any data and read requests\n    while (this._buffered > 0 && this._reads && this._reads.length > 0) {\n\n      var read = this._reads[0];\n\n      // read any data (but no more than length)\n      if (read.allowLess) {\n        this._processReadAllowingLess(read);\n\n      }\n      else if (this._buffered >= read.length) {\n        // ok we can meet some expectations\n\n        this._processRead(read);\n      }\n      else {\n        // not enought data to satisfy first request in queue\n        // so we need to wait for more\n        break;\n      }\n    }\n\n    if (this._buffers && !this.writable) {\n      this._end();\n    }\n  }\n  catch (ex) {\n    this.emit('error', ex);\n  }\n};\n", "\n\nvar util = require('util');\nvar ChunkStream = require('./chunkstream');\nvar Filter = require('./filter-parse');\n\n\nvar FilterAsync = module.exports = function(bitmapInfo) {\n  ChunkStream.call(this);\n\n  var buffers = [];\n  var that = this;\n  this._filter = new Filter(bitmapInfo, {\n    read: this.read.bind(this),\n    write: function(buffer) {\n      buffers.push(buffer);\n    },\n    complete: function() {\n      that.emit('complete', Buffer.concat(buffers));\n    }\n  });\n\n  this._filter.start();\n};\nutil.inherits(FilterAsync, ChunkStream);\n", "\n\nvar interlaceUtils = require('./interlace');\nvar paethPredictor = require('./paeth-predictor');\n\nfunction getByteWidth(width, bpp, depth) {\n  var byteWidth = width * bpp;\n  if (depth !== 8) {\n    byteWidth = Math.ceil(byteWidth / (8 / depth));\n  }\n  return byteWidth;\n}\n\nvar Filter = module.exports = function(bitmapInfo, dependencies) {\n\n  var width = bitmapInfo.width;\n  var height = bitmapInfo.height;\n  var interlace = bitmapInfo.interlace;\n  var bpp = bitmapInfo.bpp;\n  var depth = bitmapInfo.depth;\n\n  this.read = dependencies.read;\n  this.write = dependencies.write;\n  this.complete = dependencies.complete;\n\n  this._imageIndex = 0;\n  this._images = [];\n  if (interlace) {\n    var passes = interlaceUtils.getImagePasses(width, height);\n    for (var i = 0; i < passes.length; i++) {\n      this._images.push({\n        byteWidth: getByteWidth(passes[i].width, bpp, depth),\n        height: passes[i].height,\n        lineIndex: 0\n      });\n    }\n  }\n  else {\n    this._images.push({\n      byteWidth: getByteWidth(width, bpp, depth),\n      height: height,\n      lineIndex: 0\n    });\n  }\n\n  // when filtering the line we look at the pixel to the left\n  // the spec also says it is done on a byte level regardless of the number of pixels\n  // so if the depth is byte compatible (8 or 16) we subtract the bpp in order to compare back\n  // a pixel rather than just a different byte part. However if we are sub byte, we ignore.\n  if (depth === 8) {\n    this._xComparison = bpp;\n  }\n  else if (depth === 16) {\n    this._xComparison = bpp * 2;\n  }\n  else {\n    this._xComparison = 1;\n  }\n};\n\nFilter.prototype.start = function() {\n  this.read(this._images[this._imageIndex].byteWidth + 1, this._reverseFilterLine.bind(this));\n};\n\nFilter.prototype._unFilterType1 = function(rawData, unfilteredLine, byteWidth) {\n\n  var xComparison = this._xComparison;\n  var xBiggerThan = xComparison - 1;\n\n  for (var x = 0; x < byteWidth; x++) {\n    var rawByte = rawData[1 + x];\n    var f1Left = x > xBiggerThan ? unfilteredLine[x - xComparison] : 0;\n    unfilteredLine[x] = rawByte + f1Left;\n  }\n};\n\nFilter.prototype._unFilterType2 = function(rawData, unfilteredLine, byteWidth) {\n\n  var lastLine = this._lastLine;\n\n  for (var x = 0; x < byteWidth; x++) {\n    var rawByte = rawData[1 + x];\n    var f2Up = lastLine ? lastLine[x] : 0;\n    unfilteredLine[x] = rawByte + f2Up;\n  }\n};\n\nFilter.prototype._unFilterType3 = function(rawData, unfilteredLine, byteWidth) {\n\n  var xComparison = this._xComparison;\n  var xBiggerThan = xComparison - 1;\n  var lastLine = this._lastLine;\n\n  for (var x = 0; x < byteWidth; x++) {\n    var rawByte = rawData[1 + x];\n    var f3Up = lastLine ? lastLine[x] : 0;\n    var f3Left = x > xBiggerThan ? unfilteredLine[x - xComparison] : 0;\n    var f3Add = Math.floor((f3Left + f3Up) / 2);\n    unfilteredLine[x] = rawByte + f3Add;\n  }\n};\n\nFilter.prototype._unFilterType4 = function(rawData, unfilteredLine, byteWidth) {\n\n  var xComparison = this._xComparison;\n  var xBiggerThan = xComparison - 1;\n  var lastLine = this._lastLine;\n\n  for (var x = 0; x < byteWidth; x++) {\n    var rawByte = rawData[1 + x];\n    var f4Up = lastLine ? lastLine[x] : 0;\n    var f4Left = x > xBiggerThan ? unfilteredLine[x - xComparison] : 0;\n    var f4UpLeft = x > xBiggerThan && lastLine ? lastLine[x - xComparison] : 0;\n    var f4Add = paethPredictor(f4Left, f4Up, f4UpLeft);\n    unfilteredLine[x] = rawByte + f4Add;\n  }\n};\n\nFilter.prototype._reverseFilterLine = function(rawData) {\n\n  var filter = rawData[0];\n  var unfilteredLine;\n  var currentImage = this._images[this._imageIndex];\n  var byteWidth = currentImage.byteWidth;\n\n  if (filter === 0) {\n    unfilteredLine = rawData.slice(1, byteWidth + 1);\n  }\n  else {\n\n    unfilteredLine = new Buffer(byteWidth);\n\n    switch (filter) {\n      case 1:\n        this._unFilterType1(rawData, unfilteredLine, byteWidth);\n        break;\n      case 2:\n        this._unFilterType2(rawData, unfilteredLine, byteWidth);\n        break;\n      case 3:\n        this._unFilterType3(rawData, unfilteredLine, byteWidth);\n        break;\n      case 4:\n        this._unFilterType4(rawData, unfilteredLine, byteWidth);\n        break;\n      default:\n        throw new Error('Unrecognised filter type - ' + filter);\n    }\n  }\n\n  this.write(unfilteredLine);\n\n  currentImage.lineIndex++;\n  if (currentImage.lineIndex >= currentImage.height) {\n    this._lastLine = null;\n    this._imageIndex++;\n    currentImage = this._images[this._imageIndex];\n  }\n  else {\n    this._lastLine = unfilteredLine;\n  }\n\n  if (currentImage) {\n    // read, using the byte width that may be from the new current image\n    this.read(currentImage.byteWidth + 1, this._reverseFilterLine.bind(this));\n  }\n  else {\n    this._lastLine = null;\n    this.complete();\n  }\n};\n", "\n\n// Adam 7\n//   0 1 2 3 4 5 6 7\n// 0 x 6 4 6 x 6 4 6\n// 1 7 7 7 7 7 7 7 7\n// 2 5 6 5 6 5 6 5 6\n// 3 7 7 7 7 7 7 7 7\n// 4 3 6 4 6 3 6 4 6\n// 5 7 7 7 7 7 7 7 7\n// 6 5 6 5 6 5 6 5 6\n// 7 7 7 7 7 7 7 7 7\n\n\nvar imagePasses = [\n  { // pass 1 - 1px\n    x: [0],\n    y: [0]\n  },\n  { // pass 2 - 1px\n    x: [4],\n    y: [0]\n  },\n  { // pass 3 - 2px\n    x: [0, 4],\n    y: [4]\n  },\n  { // pass 4 - 4px\n    x: [2, 6],\n    y: [0, 4]\n  },\n  { // pass 5 - 8px\n    x: [0, 2, 4, 6],\n    y: [2, 6]\n  },\n  { // pass 6 - 16px\n    x: [1, 3, 5, 7],\n    y: [0, 2, 4, 6]\n  },\n  { // pass 7 - 32px\n    x: [0, 1, 2, 3, 4, 5, 6, 7],\n    y: [1, 3, 5, 7]\n  }\n];\n\nexports.getImagePasses = function(width, height) {\n  var images = [];\n  var xLeftOver = width % 8;\n  var yLeftOver = height % 8;\n  var xRepeats = (width - xLeftOver) / 8;\n  var yRepeats = (height - yLeftOver) / 8;\n  for (var i = 0; i < imagePasses.length; i++) {\n    var pass = imagePasses[i];\n    var passWidth = xRepeats * pass.x.length;\n    var passHeight = yRepeats * pass.y.length;\n    for (var j = 0; j < pass.x.length; j++) {\n      if (pass.x[j] < xLeftOver) {\n        passWidth++;\n      }\n      else {\n        break;\n      }\n    }\n    for (j = 0; j < pass.y.length; j++) {\n      if (pass.y[j] < yLeftOver) {\n        passHeight++;\n      }\n      else {\n        break;\n      }\n    }\n    if (passWidth > 0 && passHeight > 0) {\n      images.push({ width: passWidth, height: passHeight, index: i });\n    }\n  }\n  return images;\n};\n\nexports.getInterlaceIterator = function(width) {\n  return function(x, y, pass) {\n    var outerXLeftOver = x % imagePasses[pass].x.length;\n    var outerX = (((x - outerXLeftOver) / imagePasses[pass].x.length) * 8) + imagePasses[pass].x[outerXLeftOver];\n    var outerYLeftOver = y % imagePasses[pass].y.length;\n    var outerY = (((y - outerYLeftOver) / imagePasses[pass].y.length) * 8) + imagePasses[pass].y[outerYLeftOver];\n    return (outerX * 4) + (outerY * width * 4);\n  };\n};", "\r\n\r\nmodule.exports = function paethPredictor(left, above, upLeft) {\r\n\r\n  var paeth = left + above - upLeft;\r\n  var pLeft = Math.abs(paeth - left);\r\n  var pAbove = Math.abs(paeth - above);\r\n  var pUpLeft = Math.abs(paeth - upLeft);\r\n\r\n  if (pLeft <= pAbove && pLeft <= pUpLeft) {\r\n    return left;\r\n  }\r\n  if (pAbove <= pUpLeft) {\r\n    return above;\r\n  }\r\n  return upLeft;\r\n};", "\n\nvar constants = require('./constants');\nvar CrcCalculator = require('./crc');\n\n\nvar Parser = module.exports = function(options, dependencies) {\n\n  this._options = options;\n  options.checkCRC = options.checkCRC !== false;\n\n  this._hasIHDR = false;\n  this._hasIEND = false;\n  this._emittedHeadersFinished = false;\n\n  // input flags/metadata\n  this._palette = [];\n  this._colorType = 0;\n\n  this._chunks = {};\n  this._chunks[constants.TYPE_IHDR] = this._handleIHDR.bind(this);\n  this._chunks[constants.TYPE_IEND] = this._handleIEND.bind(this);\n  this._chunks[constants.TYPE_IDAT] = this._handleIDAT.bind(this);\n  this._chunks[constants.TYPE_PLTE] = this._handlePLTE.bind(this);\n  this._chunks[constants.TYPE_tRNS] = this._handleTRNS.bind(this);\n  this._chunks[constants.TYPE_gAMA] = this._handleGAMA.bind(this);\n\n  this.read = dependencies.read;\n  this.error = dependencies.error;\n  this.metadata = dependencies.metadata;\n  this.gamma = dependencies.gamma;\n  this.transColor = dependencies.transColor;\n  this.palette = dependencies.palette;\n  this.parsed = dependencies.parsed;\n  this.inflateData = dependencies.inflateData;\n  this.finished = dependencies.finished;\n  this.simpleTransparency = dependencies.simpleTransparency;\n  this.headersFinished = dependencies.headersFinished || function() {};\n};\n\nParser.prototype.start = function() {\n  this.read(constants.PNG_SIGNATURE.length,\n    this._parseSignature.bind(this)\n  );\n};\n\nParser.prototype._parseSignature = function(data) {\n\n  var signature = constants.PNG_SIGNATURE;\n\n  for (var i = 0; i < signature.length; i++) {\n    if (data[i] !== signature[i]) {\n      this.error(new Error('Invalid file signature'));\n      return;\n    }\n  }\n  this.read(8, this._parseChunkBegin.bind(this));\n};\n\nParser.prototype._parseChunkBegin = function(data) {\n\n  // chunk content length\n  var length = data.readUInt32BE(0);\n\n  // chunk type\n  var type = data.readUInt32BE(4);\n  var name = '';\n  for (var i = 4; i < 8; i++) {\n    name += String.fromCharCode(data[i]);\n  }\n\n  //console.log('chunk ', name, length);\n\n  // chunk flags\n  var ancillary = Boolean(data[4] & 0x20); // or critical\n  //    priv = Boolean(data[5] & 0x20), // or public\n  //    safeToCopy = Boolean(data[7] & 0x20); // or unsafe\n\n  if (!this._hasIHDR && type !== constants.TYPE_IHDR) {\n    this.error(new Error('Expected IHDR on beggining'));\n    return;\n  }\n\n  this._crc = new CrcCalculator();\n  this._crc.write(new Buffer(name));\n\n  if (this._chunks[type]) {\n    return this._chunks[type](length);\n  }\n\n  if (!ancillary) {\n    this.error(new Error('Unsupported critical chunk type ' + name));\n    return;\n  }\n\n  this.read(length + 4, this._skipChunk.bind(this));\n};\n\nParser.prototype._skipChunk = function(/*data*/) {\n  this.read(8, this._parseChunkBegin.bind(this));\n};\n\nParser.prototype._handleChunkEnd = function() {\n  this.read(4, this._parseChunkEnd.bind(this));\n};\n\nParser.prototype._parseChunkEnd = function(data) {\n\n  var fileCrc = data.readInt32BE(0);\n  var calcCrc = this._crc.crc32();\n\n  // check CRC\n  if (this._options.checkCRC && calcCrc !== fileCrc) {\n    this.error(new Error('Crc error - ' + fileCrc + ' - ' + calcCrc));\n    return;\n  }\n\n  if (!this._hasIEND) {\n    this.read(8, this._parseChunkBegin.bind(this));\n  }\n};\n\nParser.prototype._handleIHDR = function(length) {\n  this.read(length, this._parseIHDR.bind(this));\n};\nParser.prototype._parseIHDR = function(data) {\n\n  this._crc.write(data);\n\n  var width = data.readUInt32BE(0);\n  var height = data.readUInt32BE(4);\n  var depth = data[8];\n  var colorType = data[9]; // bits: 1 palette, 2 color, 4 alpha\n  var compr = data[10];\n  var filter = data[11];\n  var interlace = data[12];\n\n  // console.log('    width', width, 'height', height,\n  //     'depth', depth, 'colorType', colorType,\n  //     'compr', compr, 'filter', filter, 'interlace', interlace\n  // );\n\n  if (depth !== 8 && depth !== 4 && depth !== 2 && depth !== 1 && depth !== 16) {\n    this.error(new Error('Unsupported bit depth ' + depth));\n    return;\n  }\n  if (!(colorType in constants.COLORTYPE_TO_BPP_MAP)) {\n    this.error(new Error('Unsupported color type'));\n    return;\n  }\n  if (compr !== 0) {\n    this.error(new Error('Unsupported compression method'));\n    return;\n  }\n  if (filter !== 0) {\n    this.error(new Error('Unsupported filter method'));\n    return;\n  }\n  if (interlace !== 0 && interlace !== 1) {\n    this.error(new Error('Unsupported interlace method'));\n    return;\n  }\n\n  this._colorType = colorType;\n\n  var bpp = constants.COLORTYPE_TO_BPP_MAP[this._colorType];\n\n  this._hasIHDR = true;\n\n  this.metadata({\n    width: width,\n    height: height,\n    depth: depth,\n    interlace: Boolean(interlace),\n    palette: Boolean(colorType & constants.COLORTYPE_PALETTE),\n    color: Boolean(colorType & constants.COLORTYPE_COLOR),\n    alpha: Boolean(colorType & constants.COLORTYPE_ALPHA),\n    bpp: bpp,\n    colorType: colorType\n  });\n\n  this._handleChunkEnd();\n};\n\n\nParser.prototype._handlePLTE = function(length) {\n  this.read(length, this._parsePLTE.bind(this));\n};\nParser.prototype._parsePLTE = function(data) {\n\n  this._crc.write(data);\n\n  var entries = Math.floor(data.length / 3);\n  // console.log('Palette:', entries);\n\n  for (var i = 0; i < entries; i++) {\n    this._palette.push([\n      data[i * 3],\n      data[i * 3 + 1],\n      data[i * 3 + 2],\n      0xff\n    ]);\n  }\n\n  this.palette(this._palette);\n\n  this._handleChunkEnd();\n};\n\nParser.prototype._handleTRNS = function(length) {\n  this.simpleTransparency();\n  this.read(length, this._parseTRNS.bind(this));\n};\nParser.prototype._parseTRNS = function(data) {\n\n  this._crc.write(data);\n\n  // palette\n  if (this._colorType === constants.COLORTYPE_PALETTE_COLOR) {\n    if (this._palette.length === 0) {\n      this.error(new Error('Transparency chunk must be after palette'));\n      return;\n    }\n    if (data.length > this._palette.length) {\n      this.error(new Error('More transparent colors than palette size'));\n      return;\n    }\n    for (var i = 0; i < data.length; i++) {\n      this._palette[i][3] = data[i];\n    }\n    this.palette(this._palette);\n  }\n\n  // for colorType 0 (grayscale) and 2 (rgb)\n  // there might be one gray/color defined as transparent\n  if (this._colorType === constants.COLORTYPE_GRAYSCALE) {\n    // grey, 2 bytes\n    this.transColor([data.readUInt16BE(0)]);\n  }\n  if (this._colorType === constants.COLORTYPE_COLOR) {\n    this.transColor([data.readUInt16BE(0), data.readUInt16BE(2), data.readUInt16BE(4)]);\n  }\n\n  this._handleChunkEnd();\n};\n\nParser.prototype._handleGAMA = function(length) {\n  this.read(length, this._parseGAMA.bind(this));\n};\nParser.prototype._parseGAMA = function(data) {\n\n  this._crc.write(data);\n  this.gamma(data.readUInt32BE(0) / constants.GAMMA_DIVISION);\n\n  this._handleChunkEnd();\n};\n\nParser.prototype._handleIDAT = function(length) {\n  if (!this._emittedHeadersFinished) {\n    this._emittedHeadersFinished = true;\n    this.headersFinished();\n  }\n  this.read(-length, this._parseIDAT.bind(this, length));\n};\nParser.prototype._parseIDAT = function(length, data) {\n\n  this._crc.write(data);\n\n  if (this._colorType === constants.COLORTYPE_PALETTE_COLOR && this._palette.length === 0) {\n    throw new Error('Expected palette not found');\n  }\n\n  this.inflateData(data);\n  var leftOverLength = length - data.length;\n\n  if (leftOverLength > 0) {\n    this._handleIDAT(leftOverLength);\n  }\n  else {\n    this._handleChunkEnd();\n  }\n};\n\nParser.prototype._handleIEND = function(length) {\n  this.read(length, this._parseIEND.bind(this));\n};\nParser.prototype._parseIEND = function(data) {\n\n  this._crc.write(data);\n\n  this._hasIEND = true;\n  this._handleChunkEnd();\n\n  if (this.finished) {\n    this.finished();\n  }\n};\n", "\n\n\nmodule.exports = {\n\n  PNG_SIGNATURE: [0x89, 0x50, 0x4e, 0x47, 0x0d, 0x0a, 0x1a, 0x0a],\n\n  TYPE_IHDR: 0x49484452,\n  TYPE_IEND: 0x49454e44,\n  TYPE_IDAT: 0x49444154,\n  TYPE_PLTE: 0x504c5445,\n  TYPE_tRNS: 0x74524e53, // eslint-disable-line camelcase\n  TYPE_gAMA: 0x67414d41, // eslint-disable-line camelcase\n\n  // color-type bits\n  COLORTYPE_GRAYSCALE: 0,\n  COLORTYPE_PALETTE: 1,\n  COLORTYPE_COLOR: 2,\n  COLORTYPE_ALPHA: 4, // e.g. grayscale and alpha\n\n  // color-type combinations\n  COLORTYPE_PALETTE_COLOR: 3,\n  COLORTYPE_COLOR_ALPHA: 6,\n\n  COLORTYPE_TO_BPP_MAP: {\n    0: 1,\n    2: 3,\n    3: 1,\n    4: 2,\n    6: 4\n  },\n\n  GAMMA_DIVISION: 100000\n};\n", "\n\nvar crcTable = [];\n\n(function() {\n  for (var i = 0; i < 256; i++) {\n    var currentCrc = i;\n    for (var j = 0; j < 8; j++) {\n      if (currentCrc & 1) {\n        currentCrc = 0xedb88320 ^ (currentCrc >>> 1);\n      }\n      else {\n        currentCrc = currentCrc >>> 1;\n      }\n    }\n    crcTable[i] = currentCrc;\n  }\n}());\n\nvar CrcCalculator = module.exports = function() {\n  this._crc = -1;\n};\n\nCrcCalculator.prototype.write = function(data) {\n\n  for (var i = 0; i < data.length; i++) {\n    this._crc = crcTable[(this._crc ^ data[i]) & 0xff] ^ (this._crc >>> 8);\n  }\n  return true;\n};\n\nCrcCalculator.prototype.crc32 = function() {\n  return this._crc ^ -1;\n};\n\n\nCrcCalculator.crc32 = function(buf) {\n\n  var crc = -1;\n  for (var i = 0; i < buf.length; i++) {\n    crc = crcTable[(crc ^ buf[i]) & 0xff] ^ (crc >>> 8);\n  }\n  return crc ^ -1;\n};\n", "\n\nvar interlaceUtils = require('./interlace');\n\nvar pixelBppMapper = [\n  // 0 - dummy entry\n  function() {},\n\n  // 1 - L\n  // 0: 0, 1: 0, 2: 0, 3: 0xff\n  function(pxData, data, pxPos, rawPos) {\n    if (rawPos === data.length) {\n      throw new Error('Ran out of data');\n    }\n\n    var pixel = data[rawPos];\n    pxData[pxPos] = pixel;\n    pxData[pxPos + 1] = pixel;\n    pxData[pxPos + 2] = pixel;\n    pxData[pxPos + 3] = 0xff;\n  },\n\n  // 2 - LA\n  // 0: 0, 1: 0, 2: 0, 3: 1\n  function(pxData, data, pxPos, rawPos) {\n    if (rawPos + 1 >= data.length) {\n      throw new Error('Ran out of data');\n    }\n\n    var pixel = data[rawPos];\n    pxData[pxPos] = pixel;\n    pxData[pxPos + 1] = pixel;\n    pxData[pxPos + 2] = pixel;\n    pxData[pxPos + 3] = data[rawPos + 1];\n  },\n\n  // 3 - RGB\n  // 0: 0, 1: 1, 2: 2, 3: 0xff\n  function(pxData, data, pxPos, rawPos) {\n    if (rawPos + 2 >= data.length) {\n      throw new Error('Ran out of data');\n    }\n\n    pxData[pxPos] = data[rawPos];\n    pxData[pxPos + 1] = data[rawPos + 1];\n    pxData[pxPos + 2] = data[rawPos + 2];\n    pxData[pxPos + 3] = 0xff;\n  },\n\n  // 4 - RGBA\n  // 0: 0, 1: 1, 2: 2, 3: 3\n  function(pxData, data, pxPos, rawPos) {\n    if (rawPos + 3 >= data.length) {\n      throw new Error('Ran out of data');\n    }\n\n    pxData[pxPos] = data[rawPos];\n    pxData[pxPos + 1] = data[rawPos + 1];\n    pxData[pxPos + 2] = data[rawPos + 2];\n    pxData[pxPos + 3] = data[rawPos + 3];\n  }\n];\n\nvar pixelBppCustomMapper = [\n  // 0 - dummy entry\n  function() {},\n\n  // 1 - L\n  // 0: 0, 1: 0, 2: 0, 3: 0xff\n  function(pxData, pixelData, pxPos, maxBit) {\n    var pixel = pixelData[0];\n    pxData[pxPos] = pixel;\n    pxData[pxPos + 1] = pixel;\n    pxData[pxPos + 2] = pixel;\n    pxData[pxPos + 3] = maxBit;\n  },\n\n  // 2 - LA\n  // 0: 0, 1: 0, 2: 0, 3: 1\n  function(pxData, pixelData, pxPos) {\n    var pixel = pixelData[0];\n    pxData[pxPos] = pixel;\n    pxData[pxPos + 1] = pixel;\n    pxData[pxPos + 2] = pixel;\n    pxData[pxPos + 3] = pixelData[1];\n  },\n\n  // 3 - RGB\n  // 0: 0, 1: 1, 2: 2, 3: 0xff\n  function(pxData, pixelData, pxPos, maxBit) {\n    pxData[pxPos] = pixelData[0];\n    pxData[pxPos + 1] = pixelData[1];\n    pxData[pxPos + 2] = pixelData[2];\n    pxData[pxPos + 3] = maxBit;\n  },\n\n  // 4 - RGBA\n  // 0: 0, 1: 1, 2: 2, 3: 3\n  function(pxData, pixelData, pxPos) {\n    pxData[pxPos] = pixelData[0];\n    pxData[pxPos + 1] = pixelData[1];\n    pxData[pxPos + 2] = pixelData[2];\n    pxData[pxPos + 3] = pixelData[3];\n  }\n];\n\nfunction bitRetriever(data, depth) {\n\n  var leftOver = [];\n  var i = 0;\n\n  function split() {\n    if (i === data.length) {\n      throw new Error('Ran out of data');\n    }\n    var byte = data[i];\n    i++;\n    var byte8, byte7, byte6, byte5, byte4, byte3, byte2, byte1;\n    switch (depth) {\n      default:\n        throw new Error('unrecognised depth');\n      case 16:\n        byte2 = data[i];\n        i++;\n        leftOver.push(((byte << 8) + byte2));\n        break;\n      case 4:\n        byte2 = byte & 0x0f;\n        byte1 = byte >> 4;\n        leftOver.push(byte1, byte2);\n        break;\n      case 2:\n        byte4 = byte & 3;\n        byte3 = byte >> 2 & 3;\n        byte2 = byte >> 4 & 3;\n        byte1 = byte >> 6 & 3;\n        leftOver.push(byte1, byte2, byte3, byte4);\n        break;\n      case 1:\n        byte8 = byte & 1;\n        byte7 = byte >> 1 & 1;\n        byte6 = byte >> 2 & 1;\n        byte5 = byte >> 3 & 1;\n        byte4 = byte >> 4 & 1;\n        byte3 = byte >> 5 & 1;\n        byte2 = byte >> 6 & 1;\n        byte1 = byte >> 7 & 1;\n        leftOver.push(byte1, byte2, byte3, byte4, byte5, byte6, byte7, byte8);\n        break;\n    }\n  }\n\n  return {\n    get: function(count) {\n      while (leftOver.length < count) {\n        split();\n      }\n      var returner = leftOver.slice(0, count);\n      leftOver = leftOver.slice(count);\n      return returner;\n    },\n    resetAfterLine: function() {\n      leftOver.length = 0;\n    },\n    end: function() {\n      if (i !== data.length) {\n        throw new Error('extra data found');\n      }\n    }\n  };\n}\n\nfunction mapImage8Bit(image, pxData, getPxPos, bpp, data, rawPos) { // eslint-disable-line max-params\n  var imageWidth = image.width;\n  var imageHeight = image.height;\n  var imagePass = image.index;\n  for (var y = 0; y < imageHeight; y++) {\n    for (var x = 0; x < imageWidth; x++) {\n      var pxPos = getPxPos(x, y, imagePass);\n      pixelBppMapper[bpp](pxData, data, pxPos, rawPos);\n      rawPos += bpp; //eslint-disable-line no-param-reassign\n    }\n  }\n  return rawPos;\n}\n\nfunction mapImageCustomBit(image, pxData, getPxPos, bpp, bits, maxBit) { // eslint-disable-line max-params\n  var imageWidth = image.width;\n  var imageHeight = image.height;\n  var imagePass = image.index;\n  for (var y = 0; y < imageHeight; y++) {\n    for (var x = 0; x < imageWidth; x++) {\n      var pixelData = bits.get(bpp);\n      var pxPos = getPxPos(x, y, imagePass);\n      pixelBppCustomMapper[bpp](pxData, pixelData, pxPos, maxBit);\n    }\n    bits.resetAfterLine();\n  }\n}\n\nexports.dataToBitMap = function(data, bitmapInfo) {\n\n  var width = bitmapInfo.width;\n  var height = bitmapInfo.height;\n  var depth = bitmapInfo.depth;\n  var bpp = bitmapInfo.bpp;\n  var interlace = bitmapInfo.interlace;\n\n  if (depth !== 8) {\n    var bits = bitRetriever(data, depth);\n  }\n  var pxData;\n  if (depth <= 8) {\n    pxData = new Buffer(width * height * 4);\n  }\n  else {\n    pxData = new Uint16Array(width * height * 4);\n  }\n  var maxBit = Math.pow(2, depth) - 1;\n  var rawPos = 0;\n  var images;\n  var getPxPos;\n\n  if (interlace) {\n    images = interlaceUtils.getImagePasses(width, height);\n    getPxPos = interlaceUtils.getInterlaceIterator(width, height);\n  }\n  else {\n    var nonInterlacedPxPos = 0;\n    getPxPos = function() {\n      var returner = nonInterlacedPxPos;\n      nonInterlacedPxPos += 4;\n      return returner;\n    };\n    images = [{ width: width, height: height }];\n  }\n\n  for (var imageIndex = 0; imageIndex < images.length; imageIndex++) {\n    if (depth === 8) {\n      rawPos = mapImage8Bit(images[imageIndex], pxData, getPxPos, bpp, data, rawPos);\n    }\n    else {\n      mapImageCustomBit(images[imageIndex], pxData, getPxPos, bpp, bits, maxBit);\n    }\n  }\n  if (depth === 8) {\n    if (rawPos !== data.length) {\n      throw new Error('extra data found');\n    }\n  }\n  else {\n    bits.end();\n  }\n\n  return pxData;\n};\n", "\n\nfunction dePalette(indata, outdata, width, height, palette) {\n  var pxPos = 0;\n  // use values from palette\n  for (var y = 0; y < height; y++) {\n    for (var x = 0; x < width; x++) {\n      var color = palette[indata[pxPos]];\n\n      if (!color) {\n        throw new Error('index ' + indata[pxPos] + ' not in palette');\n      }\n\n      for (var i = 0; i < 4; i++) {\n        outdata[pxPos + i] = color[i];\n      }\n      pxPos += 4;\n    }\n  }\n}\n\nfunction replaceTransparentColor(indata, outdata, width, height, transColor) {\n  var pxPos = 0;\n  for (var y = 0; y < height; y++) {\n    for (var x = 0; x < width; x++) {\n      var makeTrans = false;\n\n      if (transColor.length === 1) {\n        if (transColor[0] === indata[pxPos]) {\n          makeTrans = true;\n        }\n      }\n      else if (transColor[0] === indata[pxPos] && transColor[1] === indata[pxPos + 1] && transColor[2] === indata[pxPos + 2]) {\n        makeTrans = true;\n      }\n      if (makeTrans) {\n        for (var i = 0; i < 4; i++) {\n          outdata[pxPos + i] = 0;\n        }\n      }\n      pxPos += 4;\n    }\n  }\n}\n\nfunction scaleDepth(indata, outdata, width, height, depth) {\n  var maxOutSample = 255;\n  var maxInSample = Math.pow(2, depth) - 1;\n  var pxPos = 0;\n\n  for (var y = 0; y < height; y++) {\n    for (var x = 0; x < width; x++) {\n      for (var i = 0; i < 4; i++) {\n        outdata[pxPos + i] = Math.floor((indata[pxPos + i] * maxOutSample) / maxInSample + 0.5);\n      }\n      pxPos += 4;\n    }\n  }\n}\n\nmodule.exports = function(indata, imageData) {\n\n  var depth = imageData.depth;\n  var width = imageData.width;\n  var height = imageData.height;\n  var colorType = imageData.colorType;\n  var transColor = imageData.transColor;\n  var palette = imageData.palette;\n\n  var outdata = indata; // only different for 16 bits\n\n  if (colorType === 3) { // paletted\n    dePalette(indata, outdata, width, height, palette);\n  }\n  else {\n    if (transColor) {\n      replaceTransparentColor(indata, outdata, width, height, transColor);\n    }\n    // if it needs scaling\n    if (depth !== 8) {\n      // if we need to change the buffer size\n      if (depth === 16) {\n        outdata = new Buffer(width * height * 4);\n      }\n      scaleDepth(indata, outdata, width, height, depth);\n    }\n  }\n  return outdata;\n};\n", "\n\nvar util = require('util');\nvar Stream = require('stream');\nvar constants = require('./constants');\nvar Packer = require('./packer');\n\nvar PackerAsync = module.exports = function(opt) {\n  Stream.call(this);\n\n  var options = opt || {};\n\n  this._packer = new Packer(options);\n  this._deflate = this._packer.createDeflate();\n\n  this.readable = true;\n};\nutil.inherits(PackerAsync, Stream);\n\n\nPackerAsync.prototype.pack = function(data, width, height, gamma) {\n  // Signature\n  this.emit('data', new Buffer(constants.PNG_SIGNATURE));\n  this.emit('data', this._packer.packIHDR(width, height));\n\n  if (gamma) {\n    this.emit('data', this._packer.packGAMA(gamma));\n  }\n\n  var filteredData = this._packer.filterData(data, width, height);\n\n  // compress it\n  this._deflate.on('error', this.emit.bind(this, 'error'));\n\n  this._deflate.on('data', function(compressedData) {\n    this.emit('data', this._packer.packIDAT(compressedData));\n  }.bind(this));\n\n  this._deflate.on('end', function() {\n    this.emit('data', this._packer.packIEND());\n    this.emit('end');\n  }.bind(this));\n\n  this._deflate.end(filteredData);\n};\n", "\n\nvar constants = require('./constants');\nvar CrcStream = require('./crc');\nvar bitPacker = require('./bitpacker');\nvar filter = require('./filter-pack');\nvar zlib = require('zlib');\n\nvar Packer = module.exports = function(options) {\n  this._options = options;\n\n  options.deflateChunkSize = options.deflateChunkSize || 32 * 1024;\n  options.deflateLevel = options.deflateLevel != null ? options.deflateLevel : 9;\n  options.deflateStrategy = options.deflateStrategy != null ? options.deflateStrategy : 3;\n  options.inputHasAlpha = options.inputHasAlpha != null ? options.inputHasAlpha : true;\n  options.deflateFactory = options.deflateFactory || zlib.createDeflate;\n  options.bitDepth = options.bitDepth || 8;\n  // This is outputColorType\n  options.colorType = (typeof options.colorType === 'number') ? options.colorType : constants.COLORTYPE_COLOR_ALPHA;\n  options.inputColorType = (typeof options.inputColorType === 'number') ? options.inputColorType : constants.COLORTYPE_COLOR_ALPHA;\n\n  if ([\n    constants.COLORTYPE_GRAYSCALE,\n    constants.COLORTYPE_COLOR,\n    constants.COLORTYPE_COLOR_ALPHA,\n    constants.COLORTYPE_ALPHA\n  ].indexOf(options.colorType) === -1) {\n    throw new Error('option color type:' + options.colorType + ' is not supported at present');\n  }\n  if ([\n    constants.COLORTYPE_GRAYSCALE,\n    constants.COLORTYPE_COLOR,\n    constants.COLORTYPE_COLOR_ALPHA,\n    constants.COLORTYPE_ALPHA\n  ].indexOf(options.inputColorType) === -1) {\n    throw new Error('option input color type:' + options.inputColorType + ' is not supported at present');\n  }\n  if (options.bitDepth !== 8 && options.bitDepth !== 16) {\n    throw new Error('option bit depth:' + options.bitDepth + ' is not supported at present');\n  }\n};\n\nPacker.prototype.getDeflateOptions = function() {\n  return {\n    chunkSize: this._options.deflateChunkSize,\n    level: this._options.deflateLevel,\n    strategy: this._options.deflateStrategy\n  };\n};\n\nPacker.prototype.createDeflate = function() {\n  return this._options.deflateFactory(this.getDeflateOptions());\n};\n\nPacker.prototype.filterData = function(data, width, height) {\n  // convert to correct format for filtering (e.g. right bpp and bit depth)\n  var packedData = bitPacker(data, width, height, this._options);\n\n  // filter pixel data\n  var bpp = constants.COLORTYPE_TO_BPP_MAP[this._options.colorType];\n  var filteredData = filter(packedData, width, height, this._options, bpp);\n  return filteredData;\n};\n\nPacker.prototype._packChunk = function(type, data) {\n\n  var len = (data ? data.length : 0);\n  var buf = new Buffer(len + 12);\n\n  buf.writeUInt32BE(len, 0);\n  buf.writeUInt32BE(type, 4);\n\n  if (data) {\n    data.copy(buf, 8);\n  }\n\n  buf.writeInt32BE(CrcStream.crc32(buf.slice(4, buf.length - 4)), buf.length - 4);\n  return buf;\n};\n\nPacker.prototype.packGAMA = function(gamma) {\n  var buf = new Buffer(4);\n  buf.writeUInt32BE(Math.floor(gamma * constants.GAMMA_DIVISION), 0);\n  return this._packChunk(constants.TYPE_gAMA, buf);\n};\n\nPacker.prototype.packIHDR = function(width, height) {\n\n  var buf = new Buffer(13);\n  buf.writeUInt32BE(width, 0);\n  buf.writeUInt32BE(height, 4);\n  buf[8] = this._options.bitDepth; // Bit depth\n  buf[9] = this._options.colorType; // colorType\n  buf[10] = 0; // compression\n  buf[11] = 0; // filter\n  buf[12] = 0; // interlace\n\n  return this._packChunk(constants.TYPE_IHDR, buf);\n};\n\nPacker.prototype.packIDAT = function(data) {\n  return this._packChunk(constants.TYPE_IDAT, data);\n};\n\nPacker.prototype.packIEND = function() {\n  return this._packChunk(constants.TYPE_IEND, null);\n};\n", "\n\nvar constants = require('./constants');\n\nmodule.exports = function(dataIn, width, height, options) {\n  var outHasAlpha = [constants.COLORTYPE_COLOR_ALPHA, constants.COLORTYPE_ALPHA].indexOf(options.colorType) !== -1;\n  if (options.colorType === options.inputColorType) {\n    var bigEndian = (function() {\n      var buffer = new ArrayBuffer(2);\n      new DataView(buffer).setInt16(0, 256, true /* littleEndian */);\n      // Int16Array uses the platform's endianness.\n      return new Int16Array(buffer)[0] !== 256;\n    })();\n    // If no need to convert to grayscale and alpha is present/absent in both, take a fast route\n    if (options.bitDepth === 8 || (options.bitDepth === 16 && bigEndian)) {\n      return dataIn;\n    }\n  }\n\n  // map to a UInt16 array if data is 16bit, fix endianness below\n  var data = options.bitDepth !== 16 ? dataIn : new Uint16Array(dataIn.buffer);\n\n  var maxValue = 255;\n  var inBpp = constants.COLORTYPE_TO_BPP_MAP[options.inputColorType];\n  if (inBpp === 4 && !options.inputHasAlpha) {\n    inBpp = 3;\n  }\n  var outBpp = constants.COLORTYPE_TO_BPP_MAP[options.colorType];\n  if (options.bitDepth === 16) {\n    maxValue = 65535;\n    outBpp *= 2;\n  }\n  var outData = new Buffer(width * height * outBpp);\n\n  var inIndex = 0;\n  var outIndex = 0;\n\n  var bgColor = options.bgColor || {};\n  if (bgColor.red === undefined) {\n    bgColor.red = maxValue;\n  }\n  if (bgColor.green === undefined) {\n    bgColor.green = maxValue;\n  }\n  if (bgColor.blue === undefined) {\n    bgColor.blue = maxValue;\n  }\n\n  function getRGBA() {\n    var red;\n    var green;\n    var blue;\n    var alpha = maxValue;\n    switch (options.inputColorType) {\n      case constants.COLORTYPE_COLOR_ALPHA:\n        alpha = data[inIndex + 3];\n        red = data[inIndex];\n        green = data[inIndex + 1];\n        blue = data[inIndex + 2];\n        break;\n      case constants.COLORTYPE_COLOR:\n        red = data[inIndex];\n        green = data[inIndex + 1];\n        blue = data[inIndex + 2];\n        break;\n      case constants.COLORTYPE_ALPHA:\n        alpha = data[inIndex + 1];\n        red = data[inIndex];\n        green = red;\n        blue = red;\n        break;\n      case constants.COLORTYPE_GRAYSCALE:\n        red = data[inIndex];\n        green = red;\n        blue = red;\n        break;\n      default:\n        throw new Error('input color type:' + options.inputColorType + ' is not supported at present');\n    }\n\n    if (options.inputHasAlpha) {\n      if (!outHasAlpha) {\n        alpha /= maxValue;\n        red = Math.min(Math.max(Math.round((1 - alpha) * bgColor.red + alpha * red), 0), maxValue);\n        green = Math.min(Math.max(Math.round((1 - alpha) * bgColor.green + alpha * green), 0), maxValue);\n        blue = Math.min(Math.max(Math.round((1 - alpha) * bgColor.blue + alpha * blue), 0), maxValue);\n      }\n    }\n    return { red: red, green: green, blue: blue, alpha: alpha };\n  }\n\n  for (var y = 0; y < height; y++) {\n    for (var x = 0; x < width; x++) {\n      var rgba = getRGBA(data, inIndex);\n\n      switch (options.colorType) {\n        case constants.COLORTYPE_COLOR_ALPHA:\n        case constants.COLORTYPE_COLOR:\n          if (options.bitDepth === 8) {\n            outData[outIndex] = rgba.red;\n            outData[outIndex + 1] = rgba.green;\n            outData[outIndex + 2] = rgba.blue;\n            if (outHasAlpha) {\n              outData[outIndex + 3] = rgba.alpha;\n            }\n          }\n          else {\n            outData.writeUInt16BE(rgba.red, outIndex);\n            outData.writeUInt16BE(rgba.green, outIndex + 2);\n            outData.writeUInt16BE(rgba.blue, outIndex + 4);\n            if (outHasAlpha) {\n              outData.writeUInt16BE(rgba.alpha, outIndex + 6);\n            }\n          }\n          break;\n        case constants.COLORTYPE_ALPHA:\n        case constants.COLORTYPE_GRAYSCALE:\n          // Convert to grayscale and alpha\n          var grayscale = (rgba.red + rgba.green + rgba.blue) / 3;\n          if (options.bitDepth === 8) {\n            outData[outIndex] = grayscale;\n            if (outHasAlpha) {\n              outData[outIndex + 1] = rgba.alpha;\n            }\n          }\n          else {\n            outData.writeUInt16BE(grayscale, outIndex);\n            if (outHasAlpha) {\n              outData.writeUInt16BE(rgba.alpha, outIndex + 2);\n            }\n          }\n          break;\n        default:\n          throw new Error('unrecognised color Type ' + options.colorType);\n      }\n\n      inIndex += inBpp;\n      outIndex += outBpp;\n    }\n  }\n\n  return outData;\n};\n", "\n\nvar paethPredictor = require('./paeth-predictor');\n\nfunction filterNone(pxData, pxPos, byteWidth, rawData, rawPos) {\n\n  for (var x = 0; x < byteWidth; x++) {\n    rawData[rawPos + x] = pxData[pxPos + x];\n  }\n}\n\nfunction filterSumNone(pxData, pxPos, byteWidth) {\n\n  var sum = 0;\n  var length = pxPos + byteWidth;\n\n  for (var i = pxPos; i < length; i++) {\n    sum += Math.abs(pxData[i]);\n  }\n  return sum;\n}\n\nfunction filterSub(pxData, pxPos, byteWidth, rawData, rawPos, bpp) {\n\n  for (var x = 0; x < byteWidth; x++) {\n\n    var left = x >= bpp ? pxData[pxPos + x - bpp] : 0;\n    var val = pxData[pxPos + x] - left;\n\n    rawData[rawPos + x] = val;\n  }\n}\n\nfunction filterSumSub(pxData, pxPos, byteWidth, bpp) {\n\n  var sum = 0;\n  for (var x = 0; x < byteWidth; x++) {\n\n    var left = x >= bpp ? pxData[pxPos + x - bpp] : 0;\n    var val = pxData[pxPos + x] - left;\n\n    sum += Math.abs(val);\n  }\n\n  return sum;\n}\n\nfunction filterUp(pxData, pxPos, byteWidth, rawData, rawPos) {\n\n  for (var x = 0; x < byteWidth; x++) {\n\n    var up = pxPos > 0 ? pxData[pxPos + x - byteWidth] : 0;\n    var val = pxData[pxPos + x] - up;\n\n    rawData[rawPos + x] = val;\n  }\n}\n\nfunction filterSumUp(pxData, pxPos, byteWidth) {\n\n  var sum = 0;\n  var length = pxPos + byteWidth;\n  for (var x = pxPos; x < length; x++) {\n\n    var up = pxPos > 0 ? pxData[x - byteWidth] : 0;\n    var val = pxData[x] - up;\n\n    sum += Math.abs(val);\n  }\n\n  return sum;\n}\n\nfunction filterAvg(pxData, pxPos, byteWidth, rawData, rawPos, bpp) {\n\n  for (var x = 0; x < byteWidth; x++) {\n\n    var left = x >= bpp ? pxData[pxPos + x - bpp] : 0;\n    var up = pxPos > 0 ? pxData[pxPos + x - byteWidth] : 0;\n    var val = pxData[pxPos + x] - ((left + up) >> 1);\n\n    rawData[rawPos + x] = val;\n  }\n}\n\nfunction filterSumAvg(pxData, pxPos, byteWidth, bpp) {\n\n  var sum = 0;\n  for (var x = 0; x < byteWidth; x++) {\n\n    var left = x >= bpp ? pxData[pxPos + x - bpp] : 0;\n    var up = pxPos > 0 ? pxData[pxPos + x - byteWidth] : 0;\n    var val = pxData[pxPos + x] - ((left + up) >> 1);\n\n    sum += Math.abs(val);\n  }\n\n  return sum;\n}\n\nfunction filterPaeth(pxData, pxPos, byteWidth, rawData, rawPos, bpp) {\n\n  for (var x = 0; x < byteWidth; x++) {\n\n    var left = x >= bpp ? pxData[pxPos + x - bpp] : 0;\n    var up = pxPos > 0 ? pxData[pxPos + x - byteWidth] : 0;\n    var upleft = pxPos > 0 && x >= bpp ? pxData[pxPos + x - (byteWidth + bpp)] : 0;\n    var val = pxData[pxPos + x] - paethPredictor(left, up, upleft);\n\n    rawData[rawPos + x] = val;\n  }\n}\n\nfunction filterSumPaeth(pxData, pxPos, byteWidth, bpp) {\n  var sum = 0;\n  for (var x = 0; x < byteWidth; x++) {\n\n    var left = x >= bpp ? pxData[pxPos + x - bpp] : 0;\n    var up = pxPos > 0 ? pxData[pxPos + x - byteWidth] : 0;\n    var upleft = pxPos > 0 && x >= bpp ? pxData[pxPos + x - (byteWidth + bpp)] : 0;\n    var val = pxData[pxPos + x] - paethPredictor(left, up, upleft);\n\n    sum += Math.abs(val);\n  }\n\n  return sum;\n}\n\nvar filters = {\n  0: filterNone,\n  1: filterSub,\n  2: filterUp,\n  3: filterAvg,\n  4: filterPaeth\n};\n\nvar filterSums = {\n  0: filterSumNone,\n  1: filterSumSub,\n  2: filterSumUp,\n  3: filterSumAvg,\n  4: filterSumPaeth\n};\n\nmodule.exports = function(pxData, width, height, options, bpp) {\n\n  var filterTypes;\n  if (!('filterType' in options) || options.filterType === -1) {\n    filterTypes = [0, 1, 2, 3, 4];\n  }\n  else if (typeof options.filterType === 'number') {\n    filterTypes = [options.filterType];\n  }\n  else {\n    throw new Error('unrecognised filter types');\n  }\n\n  if (options.bitDepth === 16) {\n    bpp *= 2;\n  }\n  var byteWidth = width * bpp;\n  var rawPos = 0;\n  var pxPos = 0;\n  var rawData = new Buffer((byteWidth + 1) * height);\n\n  var sel = filterTypes[0];\n\n  for (var y = 0; y < height; y++) {\n\n    if (filterTypes.length > 1) {\n      // find best filter for this line (with lowest sum of values)\n      var min = Infinity;\n\n      for (var i = 0; i < filterTypes.length; i++) {\n        var sum = filterSums[filterTypes[i]](pxData, pxPos, byteWidth, bpp);\n        if (sum < min) {\n          sel = filterTypes[i];\n          min = sum;\n        }\n      }\n    }\n\n    rawData[rawPos] = sel;\n    rawPos++;\n    filters[sel](pxData, pxPos, byteWidth, rawData, rawPos, bpp);\n    rawPos += byteWidth;\n    pxPos += byteWidth;\n  }\n  return rawData;\n};\n", "\n\n\nvar parse = require('./parser-sync');\nvar pack = require('./packer-sync');\n\n\nexports.read = function(buffer, options) {\n\n  return parse(buffer, options || {});\n};\n\nexports.write = function(png, options) {\n\n  return pack(png, options);\n};\n", "\n\nvar hasSyncZlib = true;\nvar zlib = require('zlib');\nvar inflateSync = require('./sync-inflate');\nif (!zlib.deflateSync) {\n  hasSyncZlib = false;\n}\nvar SyncReader = require('./sync-reader');\nvar FilterSync = require('./filter-parse-sync');\nvar Parser = require('./parser');\nvar bitmapper = require('./bitmapper');\nvar formatNormaliser = require('./format-normaliser');\n\n\nmodule.exports = function(buffer, options) {\n\n  if (!hasSyncZlib) {\n    throw new Error('To use the sync capability of this library in old node versions, please pin pngjs to v2.3.0');\n  }\n\n  var err;\n  function handleError(_err_) {\n    err = _err_;\n  }\n\n  var metaData;\n  function handleMetaData(_metaData_) {\n    metaData = _metaData_;\n  }\n\n  function handleTransColor(transColor) {\n    metaData.transColor = transColor;\n  }\n\n  function handlePalette(palette) {\n    metaData.palette = palette;\n  }\n\n  function handleSimpleTransparency() {\n    metaData.alpha = true;\n  }\n\n  var gamma;\n  function handleGamma(_gamma_) {\n    gamma = _gamma_;\n  }\n\n  var inflateDataList = [];\n  function handleInflateData(inflatedData) {\n    inflateDataList.push(inflatedData);\n  }\n\n  var reader = new SyncReader(buffer);\n\n  var parser = new Parser(options, {\n    read: reader.read.bind(reader),\n    error: handleError,\n    metadata: handleMetaData,\n    gamma: handleGamma,\n    palette: handlePalette,\n    transColor: handleTransColor,\n    inflateData: handleInflateData,\n    simpleTransparency: handleSimpleTransparency\n  });\n\n  parser.start();\n  reader.process();\n\n  if (err) {\n    throw err;\n  }\n\n  //join together the inflate datas\n  var inflateData = Buffer.concat(inflateDataList);\n  inflateDataList.length = 0;\n\n  var inflatedData;\n  if (metaData.interlace) {\n    inflatedData = zlib.inflateSync(inflateData);\n  }\n  else {\n    var rowSize = ((metaData.width * metaData.bpp * metaData.depth + 7) >> 3) + 1;\n    var imageSize = rowSize * metaData.height;\n    inflatedData = inflateSync(inflateData, { chunkSize: imageSize, maxLength: imageSize });\n  }\n  inflateData = null;\n\n  if (!inflatedData || !inflatedData.length) {\n    throw new Error('bad png - invalid inflate data response');\n  }\n\n  var unfilteredData = FilterSync.process(inflatedData, metaData);\n  inflateData = null;\n\n  var bitmapData = bitmapper.dataToBitMap(unfilteredData, metaData);\n  unfilteredData = null;\n\n  var normalisedBitmapData = formatNormaliser(bitmapData, metaData);\n\n  metaData.data = normalisedBitmapData;\n  metaData.gamma = gamma || 0;\n\n  return metaData;\n};\n", "\n\nvar assert = require('assert').ok;\nvar zlib = require('zlib');\nvar util = require('util');\n\nvar kMaxLength = require('buffer').kMaxLength;\n\nfunction Inflate(opts) {\n  if (!(this instanceof Inflate)) {\n    return new Inflate(opts);\n  }\n\n  if (opts && opts.chunkSize < zlib.Z_MIN_CHUNK) {\n    opts.chunkSize = zlib.Z_MIN_CHUNK;\n  }\n\n  zlib.Inflate.call(this, opts);\n\n  // Node 8 --> 9 compatibility check\n  this._offset = this._offset === undefined ? this._outOffset : this._offset;\n  this._buffer = this._buffer || this._outBuffer;\n\n  if (opts && opts.maxLength != null) {\n    this._maxLength = opts.maxLength;\n  }\n}\n\nfunction createInflate(opts) {\n  return new Inflate(opts);\n}\n\nfunction _close(engine, callback) {\n  if (callback) {\n    process.nextTick(callback);\n  }\n\n  // Caller may invoke .close after a zlib error (which will null _handle).\n  if (!engine._handle) {\n    return;\n  }\n\n  engine._handle.close();\n  engine._handle = null;\n}\n\nInflate.prototype._processChunk = function(chunk, flushFlag, asyncCb) {\n  if (typeof asyncCb === 'function') {\n    return zlib.Inflate._processChunk.call(this, chunk, flushFlag, asyncCb);\n  }\n\n  var self = this;\n\n  var availInBefore = chunk && chunk.length;\n  var availOutBefore = this._chunkSize - this._offset;\n  var leftToInflate = this._maxLength;\n  var inOff = 0;\n\n  var buffers = [];\n  var nread = 0;\n\n  var error;\n  this.on('error', function(err) {\n    error = err;\n  });\n\n  function handleChunk(availInAfter, availOutAfter) {\n    if (self._hadError) {\n      return;\n    }\n\n    var have = availOutBefore - availOutAfter;\n    assert(have >= 0, 'have should not go down');\n\n    if (have > 0) {\n      var out = self._buffer.slice(self._offset, self._offset + have);\n      self._offset += have;\n\n      if (out.length > leftToInflate) {\n        out = out.slice(0, leftToInflate);\n      }\n\n      buffers.push(out);\n      nread += out.length;\n      leftToInflate -= out.length;\n\n      if (leftToInflate === 0) {\n        return false;\n      }\n    }\n\n    if (availOutAfter === 0 || self._offset >= self._chunkSize) {\n      availOutBefore = self._chunkSize;\n      self._offset = 0;\n      self._buffer = Buffer.allocUnsafe(self._chunkSize);\n    }\n\n    if (availOutAfter === 0) {\n      inOff += (availInBefore - availInAfter);\n      availInBefore = availInAfter;\n\n      return true;\n    }\n\n    return false;\n  }\n\n  assert(this._handle, 'zlib binding closed');\n  do {\n    var res = this._handle.writeSync(flushFlag,\n      chunk, // in\n      inOff, // in_off\n      availInBefore, // in_len\n      this._buffer, // out\n      this._offset, //out_off\n      availOutBefore); // out_len\n    // Node 8 --> 9 compatibility check\n    res = res || this._writeState;\n  } while (!this._hadError && handleChunk(res[0], res[1]));\n\n  if (this._hadError) {\n    throw error;\n  }\n\n  if (nread >= kMaxLength) {\n    _close(this);\n    throw new RangeError('Cannot create final Buffer. It would be larger than 0x' + kMaxLength.toString(16) + ' bytes');\n  }\n\n  var buf = Buffer.concat(buffers, nread);\n  _close(this);\n\n  return buf;\n};\n\nutil.inherits(Inflate, zlib.Inflate);\n\nfunction zlibBufferSync(engine, buffer) {\n  if (typeof buffer === 'string') {\n    buffer = Buffer.from(buffer);\n  }\n  if (!(buffer instanceof Buffer)) {\n    throw new TypeError('Not a string or buffer');\n  }\n\n  var flushFlag = engine._finishFlushFlag;\n  if (flushFlag == null) {\n    flushFlag = zlib.Z_FINISH;\n  }\n\n  return engine._processChunk(buffer, flushFlag);\n}\n\nfunction inflateSync(buffer, opts) {\n  return zlibBufferSync(new Inflate(opts), buffer);\n}\n\nmodule.exports = exports = inflateSync;\nexports.Inflate = Inflate;\nexports.createInflate = createInflate;\nexports.inflateSync = inflateSync;\n", "\n\nvar SyncReader = module.exports = function(buffer) {\n\n  this._buffer = buffer;\n  this._reads = [];\n};\n\nSyncReader.prototype.read = function(length, callback) {\n\n  this._reads.push({\n    length: Math.abs(length), // if length < 0 then at most this length\n    allowLess: length < 0,\n    func: callback\n  });\n};\n\nSyncReader.prototype.process = function() {\n\n  // as long as there is any data and read requests\n  while (this._reads.length > 0 && this._buffer.length) {\n\n    var read = this._reads[0];\n\n    if (this._buffer.length && (this._buffer.length >= read.length || read.allowLess)) {\n\n      // ok there is any data so that we can satisfy this request\n      this._reads.shift(); // == read\n\n      var buf = this._buffer;\n\n      this._buffer = buf.slice(read.length);\n\n      read.func.call(this, buf.slice(0, read.length));\n\n    }\n    else {\n      break;\n    }\n\n  }\n\n  if (this._reads.length > 0) {\n    return new Error('There are some read requests waitng on finished stream');\n  }\n\n  if (this._buffer.length > 0) {\n    return new Error('unrecognised content at end of stream');\n  }\n\n};\n", "\n\nvar SyncReader = require('./sync-reader');\nvar Filter = require('./filter-parse');\n\n\nexports.process = function(inBuffer, bitmapInfo) {\n\n  var outBuffers = [];\n  var reader = new SyncReader(inBuffer);\n  var filter = new Filter(bitmapInfo, {\n    read: reader.read.bind(reader),\n    write: function(bufferPart) {\n      outBuffers.push(bufferPart);\n    },\n    complete: function() {\n    }\n  });\n\n  filter.start();\n  reader.process();\n\n  return Buffer.concat(outBuffers);\n};", "\n\nvar hasSyncZlib = true;\nvar zlib = require('zlib');\nif (!zlib.deflateSync) {\n  hasSyncZlib = false;\n}\nvar constants = require('./constants');\nvar Packer = require('./packer');\n\nmodule.exports = function(metaData, opt) {\n\n  if (!hasSyncZlib) {\n    throw new Error('To use the sync capability of this library in old node versions, please pin pngjs to v2.3.0');\n  }\n\n  var options = opt || {};\n\n  var packer = new Packer(options);\n\n  var chunks = [];\n\n  // Signature\n  chunks.push(new Buffer(constants.PNG_SIGNATURE));\n\n  // Header\n  chunks.push(packer.packIHDR(metaData.width, metaData.height));\n\n  if (metaData.gamma) {\n    chunks.push(packer.packGAMA(metaData.gamma));\n  }\n\n  var filteredData = packer.filterData(metaData.data, metaData.width, metaData.height);\n\n  // compress it\n  var compressedData = zlib.deflateSync(filteredData, packer.getDeflateOptions());\n  filteredData = null;\n\n  if (!compressedData || !compressedData.length) {\n    throw new Error('bad png - invalid compressed data response');\n  }\n  chunks.push(packer.packIDAT(compressedData));\n\n  // End\n  chunks.push(packer.packIEND());\n\n  return Buffer.concat(chunks);\n};\n"]}