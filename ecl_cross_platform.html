<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ECL跨平台交互系统</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', <PERSON><PERSON>, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .container {
            background: white;
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            width: 1600px;
            height: 900px;
            position: relative;
            overflow: hidden;
        }

        .container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            border: 3px solid #2196F3;
            border-radius: 20px;
            pointer-events: none;
        }

        .main-title {
            background: linear-gradient(135deg, #2196F3, #1976D2);
            color: white;
            padding: 20px;
            border-radius: 15px;
            font-size: 24px;
            font-weight: bold;
            text-align: center;
            box-shadow: 0 8px 25px rgba(33, 150, 243, 0.3);
            margin-bottom: 30px;
            line-height: 1.3;
        }

        .platform-container {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 25px;
            margin-bottom: 30px;
            padding: 0 20px;
        }

        .platform-section {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 15px;
            background: #f8f9fa;
            border-radius: 15px;
            padding: 20px;
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
            border: 2px solid #e9ecef;
        }

        .platform-logo {
            width: 280px;
            height: 140px;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 16px;
            font-weight: bold;
            color: white;
            box-shadow: 0 6px 20px rgba(0,0,0,0.15);
            position: relative;
            overflow: hidden;
        }

        .ios-logo {
            background: linear-gradient(135deg, #000000, #333333);
        }

        .android-logo {
            background: linear-gradient(135deg, #3DDC84, #1976D2);
        }

        .harmony-logo {
            background: linear-gradient(135deg, #FF6B35, #F7931E);
        }

        .windows-logo {
            background: linear-gradient(135deg, #0078D4, #106EBE);
        }

        .platform-logo::before {
            content: '';
            position: absolute;
            top: 15px;
            left: 15px;
            right: 15px;
            bottom: 15px;
            border: 2px dashed rgba(255,255,255,0.3);
            border-radius: 8px;
        }

        .platform-features {
            background: white;
            padding: 15px;
            border-radius: 10px;
            border-left: 4px solid #2196F3;
            width: 100%;
            box-shadow: 0 3px 10px rgba(0,0,0,0.08);
        }

        .platform-features h3 {
            margin: 0 0 12px 0;
            color: #2196F3;
            font-size: 14px;
            text-align: center;
        }

        .feature-list {
            list-style: none;
            padding: 0;
            margin: 0;
        }

        .feature-list li {
            padding: 6px 0;
            font-size: 12px;
            color: #333;
            border-bottom: 1px solid #f0f0f0;
            position: relative;
            padding-left: 18px;
            line-height: 1.3;
        }

        .feature-list li:before {
            content: '✓';
            position: absolute;
            left: 0;
            color: #4caf50;
            font-weight: bold;
            font-size: 11px;
        }

        .feature-list li:last-child {
            border-bottom: none;
        }

        .wechat-section {
            text-align: center;
            margin-bottom: 25px;
        }

        .wechat-bridge {
            display: inline-block;
            background: #07C160;
            color: white;
            padding: 15px 30px;
            border-radius: 25px;
            font-size: 18px;
            font-weight: bold;
            box-shadow: 0 8px 25px rgba(7, 193, 96, 0.3);
            margin-bottom: 15px;
        }

        .cross-platform-note {
            background: rgba(255, 193, 7, 0.1);
            border: 2px solid #FFC107;
            border-radius: 10px;
            padding: 15px 25px;
            font-size: 14px;
            color: #F57F17;
            font-weight: bold;
            text-align: center;
            max-width: 600px;
            margin: 0 auto;
            line-height: 1.4;
        }

        .tech-implementation {
            background: rgba(248, 249, 250, 0.95);
            padding: 25px;
            border-radius: 12px;
            border-top: 3px solid #2196F3;
            backdrop-filter: blur(10px);
            margin: 0 20px;
        }

        .tech-title {
            font-size: 20px;
            font-weight: bold;
            color: #2196F3;
            margin-bottom: 20px;
            text-align: center;
        }

        .tech-grid {
            display: grid;
            grid-template-columns: repeat(5, 1fr);
            gap: 20px;
        }

        .tech-item {
            background: white;
            padding: 18px;
            border-radius: 10px;
            box-shadow: 0 3px 10px rgba(0,0,0,0.1);
            text-align: center;
        }

        .tech-item .module-name {
            color: #2196F3;
            font-weight: bold;
            font-size: 14px;
            margin-bottom: 10px;
        }

        .tech-item .module-desc {
            color: #666;
            font-size: 12px;
            line-height: 1.4;
        }


    </style>
</head>
<body>
    <div class="container">
        <!-- 主标题 -->
        <div class="main-title">
            ECL跨平台交互系统
        </div>

        <!-- 平台展示 -->
        <div class="platform-container">
            <!-- iOS平台 -->
            <div class="platform-section">
                <div class="platform-logo ios-logo">
                    🍎 iOS
                </div>
                <div class="platform-features">
                    <h3>iOS平台支持</h3>
                    <ul class="feature-list">
                        <li>iPhone微信小程序</li>
                        <li>iPad微信小程序</li>
                        <li>视频录制功能完整</li>
                        <li>文件保存到相册</li>
                        <li>数据分享导出</li>
                        <li>云端分析处理</li>
                    </ul>
                </div>
            </div>

            <!-- Android平台 -->
            <div class="platform-section">
                <div class="platform-logo android-logo">
                    🤖 Android
                </div>
                <div class="platform-features">
                    <h3>Android平台支持</h3>
                    <ul class="feature-list">
                        <li>Android微信小程序</li>
                        <li>平板设备支持</li>
                        <li>视频录制功能完整</li>
                        <li>本地文件保存</li>
                        <li>数据分享导出</li>
                        <li>云端分析处理</li>
                    </ul>
                </div>
            </div>

            <!-- 鸿蒙平台 -->
            <div class="platform-section">
                <div class="platform-logo harmony-logo">
                    🌸 HarmonyOS
                </div>
                <div class="platform-features">
                    <h3>鸿蒙平台支持</h3>
                    <ul class="feature-list">
                        <li>华为手机微信小程序</li>
                        <li>华为平板支持</li>
                        <li>视频录制功能完整</li>
                        <li>本地文件保存</li>
                        <li>数据分享导出</li>
                        <li>云端分析处理</li>
                    </ul>
                </div>
            </div>

            <!-- Windows平台 -->
            <div class="platform-section">
                <div class="platform-logo windows-logo">
                    🪟 Windows
                </div>
                <div class="platform-features">
                    <h3>Windows平台支持</h3>
                    <ul class="feature-list">
                        <li>PC微信小程序</li>
                        <li>Surface设备支持</li>
                        <li>视频录制功能完整</li>
                        <li>本地文件保存</li>
                        <li>数据分享导出</li>
                        <li>云端分析处理</li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- 微信小程序说明 -->
        <div class="wechat-section">
            <div class="wechat-bridge">
                微信小程序
            </div>
            <div class="cross-platform-note">
                基于微信小程序框架，实现一次开发，多平台运行<br>
                只要能安装微信的设备都可以使用ECL检测系统
            </div>
        </div>

        <!-- 技术实现说明 -->
        <div class="tech-implementation">
            <div class="tech-title">ECL小程序跨平台技术实现</div>
            <div class="tech-grid">
                <div class="tech-item">
                    <div class="module-name">pages/index</div>
                    <div class="module-desc">统一的用户界面<br>跨平台录制控制<br>设备参数配置</div>
                </div>
                <div class="tech-item">
                    <div class="module-name">analyzeVideo</div>
                    <div class="module-desc">云端统一处理<br>平台无关分析<br>标准化输出</div>
                </div>
                <div class="tech-item">
                    <div class="module-name">getUserData</div>
                    <div class="module-desc">跨平台数据获取<br>统一文件格式<br>兼容性处理</div>
                </div>
                <div class="tech-item">
                    <div class="module-name">pages/me</div>
                    <div class="module-desc">适配不同屏幕<br>统一交互体验<br>平台特性优化</div>
                </div>
                <div class="tech-item">
                    <div class="module-name">deleteUserData</div>
                    <div class="module-desc">跨平台数据清理<br>统一存储管理<br>安全删除机制</div>
                </div>
            </div>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 静态界面，只添加基本的悬停效果
            const platformLogos = document.querySelectorAll('.platform-logo');
            const techItems = document.querySelectorAll('.tech-item');
            
            // 平台logo悬停效果
            platformLogos.forEach(logo => {
                logo.addEventListener('mouseenter', function() {
                    this.style.transform = 'scale(1.05)';
                    this.style.boxShadow = '0 15px 40px rgba(0,0,0,0.3)';
                });
                
                logo.addEventListener('mouseleave', function() {
                    this.style.transform = 'scale(1)';
                    this.style.boxShadow = '0 10px 30px rgba(0,0,0,0.2)';
                });
            });
            
            // 技术模块悬停效果
            techItems.forEach(item => {
                item.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateY(-3px)';
                    this.style.boxShadow = '0 6px 20px rgba(0,0,0,0.15)';
                });
                
                item.addEventListener('mouseleave', function() {
                    this.style.transform = 'translateY(0)';
                    this.style.boxShadow = '0 3px 10px rgba(0,0,0,0.1)';
                });
            });
        });
    </script>
</body>
</html>
