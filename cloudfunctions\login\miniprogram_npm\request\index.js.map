{"version": 3, "sources": ["index.js", "lib/cookies.js", "lib/helpers.js", "request.js", "lib/getProxyFromURI.js", "lib/querystring.js", "lib/har.js", "lib/auth.js", "lib/oauth.js", "lib/hawk.js", "lib/multipart.js", "lib/redirect.js", "lib/tunnel.js"], "names": [], "mappings": ";;;;;;;AAAA;AACA;AACA;AACA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA,ACHA;AFOA,ACHA,ACHA;AFOA,ACHA,ACHA;AFOA,ACHA,ACHA,ACHA;AHUA,ACHA,ACHA,ACHA;AHUA,ACHA,ACHA,ACHA;AHUA,ACHA,AGTA,AFMA,ACHA;AHUA,ACHA,AGTA,AFMA,ACHA;AHUA,ACHA,AGTA,AFMA,ACHA;AHUA,ACHA,AGTA,AFMA,AGTA,AFMA;AHUA,ACHA,AGTA,AFMA,AGTA,AFMA;AHUA,ACHA,AGTA,AFMA,AGTA,AFMA;AHUA,ACHA,AGTA,AENA,AJYA,AGTA,AFMA;AHUA,ACHA,AGTA,AENA,AJYA,AGTA,AFMA;AHUA,ACHA,AGTA,AENA,AJYA,AGTA,AFMA;AHUA,AOrBA,ANkBA,AGTA,AENA,AJYA,AGTA,AFMA;AHUA,AOrBA,ANkBA,AGTA,AENA,AJYA,AGTA,AFMA;AHUA,AOrBA,ANkBA,AGTA,AENA,AJYA,AGTA,AFMA;AHUA,AOrBA,ANkBA,AGTA,AENA,AJYA,AMlBA,AHSA,AFMA;AHUA,AOrBA,ANkBA,AGTA,AENA,AJYA,AMlBA,AHSA,AFMA;AHUA,AOrBA,ANkBA,AGTA,AENA,AJYA,AMlBA,AHSA,AFMA;AHUA,AOrBA,ANkBA,AGTA,AENA,AGTA,APqBA,AMlBA,AHSA,AFMA;AHUA,AOrBA,ANkBA,AGTA,AENA,AGTA,APqBA,AMlBA,AHSA,AFMA;AHUA,AOrBA,ANkBA,AGTA,AENA,AGTA,APqBA,AMlBA,AHSA,AFMA;AHUA,AOrBA,ANkBA,AGTA,AENA,AGTA,APqBA,AQxBA,AFMA,AHSA,AFMA;AHUA,AOrBA,ANkBA,AGTA,AENA,AGTA,APqBA,AQxBA,AFMA,AHSA,AFMA;AHUA,AOrBA,ANkBA,AGTA,AENA,AGTA,APqBA,AQxBA,AFMA,AHSA,AFMA;AHUA,AOrBA,ANkBA,AGTA,AENA,AGTA,APqBA,AQxBA,AFMA,AHSA,AMlBA,ARwBA;AHUA,AOrBA,ANkBA,AGTA,AENA,AGTA,APqBA,AQxBA,AFMA,AHSA,AMlBA,ARwBA;AHUA,AOrBA,ANkBA,AGTA,AENA,AGTA,APqBA,AQxBA,AFMA,AHSA,AMlBA,ARwBA;AHUA,AOrBA,ANkBA,AGTA,AENA,AGTA,APqBA,AQxBA,AFMA,AHSA,AMlBA,ACHA,AT2BA;AHUA,AOrBA,ANkBA,AGTA,AENA,AGTA,APqBA,AQxBA,AFMA,AHSA,AMlBA,ACHA,AT2BA;AHUA,AOrBA,ANkBA,AGTA,AENA,AGTA,APqBA,AQxBA,AFMA,AHSA,AMlBA,ACHA,AT2BA;AHUA,AOrBA,ANkBA,AGTA,AENA,AGTA,APqBA,AQxBA,AFMA,AHSA,AMlBA,ACHA,AT2BA;AHUA,AOrBA,ANkBA,AGTA,AENA,AGTA,APqBA,AQxBA,AFMA,AHSA,AMlBA,ACHA,AT2BA;AHUA,AOrBA,ANkBA,AGTA,AENA,AGTA,APqBA,AQxBA,AFMA,AHSA,AMlBA,ACHA,AT2BA;AHUA,AOrBA,AHSA,AENA,AGTA,APqBA,AQxBA,AFMA,AHSA,AMlBA,ACHA,AT2BA;AHUA,AOrBA,AHSA,AENA,AGTA,APqBA,AQxBA,AFMA,AHSA,AMlBA,ACHA,AT2BA;AHUA,AOrBA,AHSA,AENA,AGTA,APqBA,AQxBA,AFMA,AHSA,AMlBA,ACHA,AT2BA;AHUA,AOrBA,AHSA,AENA,AGTA,APqBA,AQxBA,AFMA,AHSA,AMlBA,ACHA,AT2BA;AHUA,AOrBA,AHSA,AENA,AGTA,APqBA,AQxBA,AFMA,AHSA,AMlBA,ACHA,AT2BA;AHUA,AOrBA,AHSA,AENA,AGTA,APqBA,AQxBA,AFMA,AHSA,AMlBA,ACHA,AT2BA;AHUA,AOrBA,AHSA,AENA,AGTA,APqBA,AQxBA,AFMA,AHSA,AMlBA,ACHA,AT2BA;AHUA,AOrBA,AHSA,AENA,AGTA,APqBA,AQxBA,AFMA,AHSA,AMlBA,ACHA,AT2BA;AHUA,AOrBA,AHSA,AENA,AGTA,APqBA,AQxBA,AFMA,AHSA,AMlBA,ACHA,AT2BA;AHUA,AOrBA,AHSA,AENA,AGTA,APqBA,AQxBA,AFMA,AHSA,AMlBA,ACHA,AT2BA;AHUA,AOrBA,AHSA,AENA,AGTA,APqBA,AQxBA,AFMA,AHSA,AMlBA,ACHA,AT2BA;AHUA,AOrBA,AHSA,AENA,AGTA,APqBA,AQxBA,AFMA,AHSA,AMlBA,ACHA,AT2BA;AHUA,AOrBA,AHSA,AENA,AGTA,APqBA,AQxBA,AFMA,AHSA,AMlBA,ACHA,AT2BA;AHUA,AOrBA,AHSA,AENA,AGTA,APqBA,AQxBA,AFMA,AHSA,AMlBA,ACHA,AT2BA;AHUA,AOrBA,AHSA,AENA,AGTA,APqBA,AQxBA,AFMA,AHSA,AMlBA,ACHA,AT2BA;AHUA,AOrBA,AHSA,AENA,AGTA,APqBA,AQxBA,AFMA,AHSA,AMlBA,ACHA,AT2BA;AHUA,AOrBA,AHSA,AENA,AGTA,APqBA,AQxBA,AFMA,AHSA,AMlBA,ACHA,AT2BA;AHUA,AOrBA,AHSA,AENA,AGTA,APqBA,AQxBA,AFMA,AHSA,AMlBA,ACHA,AT2BA;AHUA,AOrBA,AHSA,AENA,AGTA,APqBA,AQxBA,AFMA,AHSA,AMlBA,ACHA,AT2BA;AHUA,AOrBA,AHSA,AENA,AGTA,APqBA,AQxBA,AFMA,AHSA,AMlBA,ACHA,AT2BA;AHUA,AOrBA,AHSA,AENA,AGTA,APqBA,AQxBA,AFMA,AHSA,AMlBA,ACHA,AT2BA;AHUA,AOrBA,AHSA,AENA,AGTA,APqBA,AQxBA,AFMA,AHSA,AMlBA,ACHA,AT2BA;AHUA,AOrBA,AHSA,AENA,AGTA,APqBA,AQxBA,AFMA,AHSA,AMlBA,ACHA,AT2BA;AHUA,AOrBA,AHSA,AENA,AGTA,APqBA,AQxBA,AFMA,AHSA,AMlBA,ACHA,AT2BA;AHUA,AOrBA,AHSA,AENA,AGTA,APqBA,AQxBA,AFMA,AGTA,ACHA,AT2BA;AHUA,AOrBA,AHSA,AENA,AGTA,APqBA,AQxBA,AFMA,AGTA,ACHA,AT2BA;AHUA,AOrBA,AHSA,AENA,AGTA,APqBA,AQxBA,AFMA,AGTA,ACHA,AT2BA;AHUA,AOrBA,AHSA,AENA,AGTA,APqBA,AQxBA,AFMA,AGTA,ACHA,AT2BA;AHUA,AOrBA,AHSA,AENA,AGTA,APqBA,AQxBA,AFMA,AGTA,ACHA,AT2BA;AHUA,AOrBA,AHSA,AENA,AGTA,APqBA,AQxBA,AFMA,AGTA,ACHA,AT2BA;AHUA,AOrBA,AHSA,AENA,AGTA,APqBA,AQxBA,AFMA,AGTA,ACHA,AT2BA;AHUA,AOrBA,AHSA,AENA,AGTA,ACHA,AFMA,AGTA,ACHA,AT2BA;AHUA,AOrBA,AHSA,AENA,AGTA,ACHA,AFMA,AGTA,ACHA,AT2BA;AHUA,AOrBA,AHSA,AENA,AGTA,ACHA,AFMA,AGTA,ACHA,AT2BA;AHUA,AOrBA,AHSA,AENA,AGTA,ACHA,AFMA,AGTA,ACHA,AT2BA;AHUA,AOrBA,AHSA,AENA,AGTA,ACHA,AFMA,AGTA,ACHA,AT2BA;AHUA,AOrBA,AHSA,AENA,AGTA,ACHA,AFMA,AGTA,ACHA,AT2BA;AHUA,AOrBA,AHSA,AENA,AGTA,ACHA,AFMA,AGTA,ACHA,AT2BA;AHUA,AOrBA,AHSA,AENA,AGTA,ACHA,AFMA,AGTA,ACHA,AT2BA;AHUA,AOrBA,AHSA,AENA,AGTA,ACHA,AFMA,AGTA,ACHA,AT2BA;AHUA,AOrBA,AHSA,AENA,AGTA,ACHA,AFMA,AGTA,ACHA,AT2BA;AHUA,AOrBA,AHSA,AENA,AGTA,ACHA,AFMA,AGTA,ACHA,AT2BA;AHUA,AOrBA,AHSA,AENA,AGTA,ACHA,AFMA,AGTA,ACHA,AT2BA;AHUA,AOrBA,AHSA,AENA,AGTA,ACHA,AFMA,AGTA,ACHA,AT2BA;AHUA,AOrBA,AHSA,AENA,AGTA,ACHA,AFMA,AGTA,ACHA,AT2BA;AHUA,AOrBA,AHSA,AENA,AGTA,ACHA,AFMA,AGTA,ACHA,AT2BA;AHUA,AOrBA,AHSA,AENA,AGTA,ACHA,AFMA,AGTA,ACHA,AT2BA;AHUA,AOrBA,AHSA,AENA,AGTA,ACHA,AFMA,AGTA,ACHA,AT2BA;AHUA,AOrBA,AHSA,AENA,AGTA,ACHA,AFMA,AGTA,ACHA,AT2BA;AHUA,AOrBA,AHSA,AENA,AGTA,ACHA,AFMA,AGTA,ACHA,AT2BA;AHUA,AOrBA,ADGA,AGTA,ACHA,AFMA,AGTA,ACHA,AT2BA;AHUA,AOrBA,ADGA,AGTA,ACHA,AFMA,AGTA,ACHA,AT2BA;AHUA,AOrBA,ADGA,AGTA,ACHA,AFMA,AGTA,ACHA,AT2BA;AHUA,AOrBA,ADGA,AGTA,ACHA,AFMA,AGTA,ACHA,AT2BA;AHUA,AOrBA,ADGA,AGTA,ACHA,AFMA,AGTA,ACHA,AT2BA;AHUA,AOrBA,ADGA,AGTA,ACHA,AFMA,AGTA,ACHA,AT2BA;AHUA,AOrBA,ADGA,AGTA,ACHA,AFMA,AGTA,ACHA,AT2BA;AHUA,AOrBA,ADGA,AGTA,ACHA,AFMA,AGTA,ACHA,AT2BA;AHUA,AOrBA,ADGA,AGTA,ACHA,AFMA,AGTA,ACHA,AT2BA;AHUA,AOrBA,ADGA,AGTA,ACHA,AFMA,AGTA,ACHA,AT2BA;AHUA,AOrBA,ADGA,AGTA,ACHA,AFMA,AGTA,ACHA,AT2BA;AHUA,AOrBA,ADGA,AGTA,ACHA,AFMA,AGTA,ACHA,AT2BA;AHUA,AOrBA,ADGA,AGTA,ACHA,AFMA,AGTA,ACHA,AT2BA;AHUA,AOrBA,ADGA,AGTA,ACHA,AFMA,AGTA,ACHA,AT2BA;AHUA,AOrBA,ADGA,AGTA,ACHA,AFMA,AGTA,ACHA,AT2BA;AHUA,AOrBA,ADGA,AGTA,ACHA,AFMA,AGTA,ACHA,AT2BA;AHUA,AOrBA,ADGA,AGTA,ACHA,AFMA,AGTA,ACHA,AT2BA;AHUA,AOrBA,ADGA,AGTA,ACHA,AFMA,AGTA,ACHA,AT2BA;AHUA,AOrBA,ADGA,AGTA,ACHA,AFMA,AGTA,ACHA,AT2BA;AHUA,AOrBA,ADGA,AGTA,ACHA,AFMA,AGTA,ACHA,AT2BA;AHUA,AOrBA,ADGA,AGTA,ACHA,AFMA,AGTA,ACHA,AT2BA;AHUA,AOrBA,ADGA,AGTA,ACHA,AFMA,AGTA,ACHA,AT2BA;AHUA,AOrBA,ADGA,AGTA,ACHA,AFMA,AGTA,ACHA,AT2BA;AHUA,AOrBA,ADGA,AGTA,ACHA,AFMA,AGTA,ACHA,AT2BA;AHUA,AOrBA,ADGA,AGTA,ACHA,AFMA,AGTA,ACHA,AT2BA;AHUA,AOrBA,ADGA,AIZA,AFMA,AGTA,ACHA,AT2BA;AHUA,AOrBA,ADGA,AIZA,AFMA,AGTA,ACHA,AT2BA;AHUA,AOrBA,ADGA,AIZA,AFMA,AGTA,ACHA,AT2BA;AHUA,AOrBA,ADGA,AIZA,AFMA,AGTA,ACHA,AT2BA;AHUA,AOrBA,ADGA,AIZA,AFMA,AGTA,ACHA,AT2BA;AHUA,AOrBA,ADGA,AIZA,AFMA,AGTA,ACHA,AT2BA;AHUA,AOrBA,ADGA,AIZA,AFMA,AGTA,ACHA,AT2BA;AHUA,AOrBA,ADGA,AIZA,AFMA,AGTA,ACHA,AT2BA;AHUA,AOrBA,ADGA,AIZA,AFMA,AGTA,ACHA,AT2BA;AHUA,AOrBA,ADGA,AIZA,AFMA,AGTA,ACHA,AT2BA;AHUA,AOrBA,ADGA,AIZA,AFMA,AGTA,ACHA,AT2BA;AHUA,AOrBA,ADGA,AIZA,AFMA,AGTA,ACHA,AT2BA;AHUA,AOrBA,ADGA,AIZA,AFMA,AGTA,ACHA,AT2BA;AHUA,AOrBA,ADGA,AIZA,AFMA,AGTA,ACHA,AT2BA;AHUA,AOrBA,ADGA,AIZA,AFMA,AGTA,ACHA,AT2BA;AHUA,AOrBA,ADGA,AIZA,AFMA,AGTA,ACHA,AT2BA;AHUA,AOrBA,ADGA,AIZA,AFMA,AGTA,ACHA,AT2BA;AHUA,AOrBA,ADGA,AIZA,AFMA,AGTA,ACHA,AT2BA;AHUA,AOrBA,ADGA,AIZA,AFMA,AGTA,ACHA,AT2BA;AHUA,AOrBA,ADGA,AIZA,AFMA,AGTA,ACHA,AT2BA;AHUA,AOrBA,ADGA,AIZA,AFMA,AGTA,ACHA,AT2BA;AHUA,AOrBA,ADGA,AIZA,AFMA,AGTA,ACHA,AT2BA;AHUA,AOrBA,ADGA,AIZA,AFMA,AGTA,ACHA,AT2BA;AHUA,AOrBA,ADGA,AIZA,AFMA,AGTA,ACHA,AT2BA;AHUA,AOrBA,ADGA,AIZA,AFMA,AGTA,ACHA,AT2BA;AHUA,AOrBA,ADGA,AIZA,AFMA,AGTA,ACHA,AT2BA;AHUA,AOrBA,ADGA,AENA,AGTA,ACHA,AT2BA;AHUA,AOrBA,ADGA,AENA,AGTA,ACHA,AT2BA;AHUA,AOrBA,ADGA,AENA,AGTA,ACHA,AT2BA;AHUA,AOrBA,ADGA,AENA,AGTA,ACHA,AT2BA;AHUA,AOrBA,ADGA,AENA,AGTA,ACHA,AT2BA;AHUA,AOrBA,ADGA,AENA,AGTA,ACHA,AT2BA;AHUA,AOrBA,ADGA,AENA,AGTA,ACHA,AT2BA;AHUA,AOrBA,ADGA,AENA,AGTA,ACHA,AT2BA;AHUA,AOrBA,ADGA,AENA,AGTA,ACHA,AT2BA;AHUA,AOrBA,ADGA,AENA,AGTA,ACHA,AT2BA;AHUA,AOrBA,ADGA,AENA,AGTA,ACHA,AT2BA;AHUA,AOrBA,ADGA,AENA,AGTA,ACHA,AT2BA;AHUA,AOrBA,ADGA,AENA,AGTA,ACHA,AT2BA;AIXA,ADGA,AENA,AGTA,ACHA,AT2BA;AIXA,ADGA,AENA,AGTA,ACHA,AT2BA;AIXA,ADGA,AENA,AGTA,ACHA,AT2BA;AIXA,ADGA,AENA,AGTA,ACHA,AT2BA;AIXA,ADGA,AENA,AGTA,ACHA,AT2BA;AIXA,ADGA,AENA,AGTA,ACHA,AT2BA;AIXA,ADGA,AENA,AGTA,ACHA,AT2BA;AIXA,ADGA,AENA,AGTA,ACHA,AT2BA;AIXA,ADGA,AENA,AGTA,ACHA,AT2BA;AIXA,ADGA,AENA,AGTA,ACHA,AT2BA;AIXA,ADGA,AENA,AGTA,ACHA,AT2BA;AIXA,ADGA,AENA,AGTA,ACHA,AT2BA;AIXA,ADGA,AENA,AGTA,ACHA,AT2BA;AIXA,ADGA,AENA,AGTA,ACHA,AT2BA;AIXA,ADGA,AENA,AGTA,ACHA,AT2BA;AIXA,ADGA,AENA,AGTA,ACHA,AT2BA;AIXA,ADGA,AENA,AGTA,ACHA,AT2BA;AIXA,ADGA,AKfA,ACHA,AT2BA;AIXA,ADGA,AKfA,ACHA,AT2BA;AIXA,ADGA,AKfA,ACHA,AT2BA;AIXA,ADGA,AKfA,ACHA,AT2BA;AIXA,ADGA,AKfA,ACHA,AT2BA;AIXA,ADGA,AKfA,ACHA,AT2BA;AIXA,ADGA,AKfA,ACHA,AT2BA;AIXA,ADGA,AKfA,ACHA,AT2BA;AIXA,ADGA,AKfA,ACHA,AT2BA;AIXA,ADGA,AKfA,ACHA,AT2BA;AIXA,ADGA,AKfA,ACHA,AT2BA;AIXA,ADGA,AKfA,ACHA,AT2BA;AIXA,ADGA,AKfA,ACHA,AT2BA;AIXA,ADGA,AKfA,ACHA,AT2BA;AIXA,ADGA,AKfA,ACHA,AT2BA;AIXA,ADGA,AMlBA,AT2BA;AGRA,AMlBA,AT2BA;AGRA,AMlBA,AT2BA;AGRA,AMlBA,AT2BA;AGRA,AMlBA,AT2BA;AGRA,AMlBA,AT2BA;AGRA,AMlBA,AT2BA;AGRA,AMlBA,AT2BA;AGRA,AMlBA,AT2BA;AGRA,AMlBA,AT2BA;AGRA,AMlBA,AT2BA;AGRA,AMlBA,AT2BA;AGRA,AMlBA,AT2BA;AGRA,AMlBA,AT2BA;AGRA,AMlBA,AT2BA;AGRA,AMlBA,AT2BA;AGRA,AMlBA,AT2BA;AGRA,AMlBA,AT2BA;AGRA,AMlBA,AT2BA;AGRA,AMlBA,AT2BA;AGRA,AMlBA,AT2BA;AGRA,AMlBA,AT2BA;AGRA,AMlBA,AT2BA;AGRA,AMlBA,AT2BA;AGRA,AHSA;AGRA,AHSA;AGRA,AHSA;AGRA,AHSA;AGRA,AHSA;AGRA,AHSA;AGRA,AHSA;AGRA,AHSA;AGRA,AHSA;AGRA,AHSA;AGRA,AHSA;AGRA,AHSA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.js", "sourcesContent": ["// Copyright 2010-2012 <PERSON><PERSON>\n//\n//    Licensed under the Apache License, Version 2.0 (the \"License\");\n//    you may not use this file except in compliance with the License.\n//    You may obtain a copy of the License at\n//\n//        http://www.apache.org/licenses/LICENSE-2.0\n//\n//    Unless required by applicable law or agreed to in writing, software\n//    distributed under the License is distributed on an \"AS IS\" BASIS,\n//    WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n//    See the License for the specific language governing permissions and\n//    limitations under the License.\n\n\n\nvar extend = require('extend')\nvar cookies = require('./lib/cookies')\nvar helpers = require('./lib/helpers')\n\nvar paramsHaveRequestBody = helpers.paramsHaveRequestBody\n\n// organize params for patch, post, put, head, del\nfunction initParams (uri, options, callback) {\n  if (typeof options === 'function') {\n    callback = options\n  }\n\n  var params = {}\n  if (options !== null && typeof options === 'object') {\n    extend(params, options, {uri: uri})\n  } else if (typeof uri === 'string') {\n    extend(params, {uri: uri})\n  } else {\n    extend(params, uri)\n  }\n\n  params.callback = callback || params.callback\n  return params\n}\n\nfunction request (uri, options, callback) {\n  if (typeof uri === 'undefined') {\n    throw new Error('undefined is not a valid uri or options object.')\n  }\n\n  var params = initParams(uri, options, callback)\n\n  if (params.method === 'HEAD' && paramsHaveRequestBody(params)) {\n    throw new Error('HTTP HEAD requests MUST NOT include a request body.')\n  }\n\n  return new request.Request(params)\n}\n\nfunction verbFunc (verb) {\n  var method = verb.toUpperCase()\n  return function (uri, options, callback) {\n    var params = initParams(uri, options, callback)\n    params.method = method\n    return request(params, params.callback)\n  }\n}\n\n// define like this to please codeintel/intellisense IDEs\nrequest.get = verbFunc('get')\nrequest.head = verbFunc('head')\nrequest.options = verbFunc('options')\nrequest.post = verbFunc('post')\nrequest.put = verbFunc('put')\nrequest.patch = verbFunc('patch')\nrequest.del = verbFunc('delete')\nrequest['delete'] = verbFunc('delete')\n\nrequest.jar = function (store) {\n  return cookies.jar(store)\n}\n\nrequest.cookie = function (str) {\n  return cookies.parse(str)\n}\n\nfunction wrapRequestMethod (method, options, requester, verb) {\n  return function (uri, opts, callback) {\n    var params = initParams(uri, opts, callback)\n\n    var target = {}\n    extend(true, target, options, params)\n\n    target.pool = params.pool || options.pool\n\n    if (verb) {\n      target.method = verb.toUpperCase()\n    }\n\n    if (typeof requester === 'function') {\n      method = requester\n    }\n\n    return method(target, target.callback)\n  }\n}\n\nrequest.defaults = function (options, requester) {\n  var self = this\n\n  options = options || {}\n\n  if (typeof options === 'function') {\n    requester = options\n    options = {}\n  }\n\n  var defaults = wrapRequestMethod(self, options, requester)\n\n  var verbs = ['get', 'head', 'post', 'put', 'patch', 'del', 'delete']\n  verbs.forEach(function (verb) {\n    defaults[verb] = wrapRequestMethod(self[verb], options, requester, verb)\n  })\n\n  defaults.cookie = wrapRequestMethod(self.cookie, options, requester)\n  defaults.jar = self.jar\n  defaults.defaults = self.defaults\n  return defaults\n}\n\nrequest.forever = function (agentOptions, optionsArg) {\n  var options = {}\n  if (optionsArg) {\n    extend(options, optionsArg)\n  }\n  if (agentOptions) {\n    options.agentOptions = agentOptions\n  }\n\n  options.forever = true\n  return request.defaults(options)\n}\n\n// Exports\n\nmodule.exports = request\nrequest.Request = require('./request')\nrequest.initParams = initParams\n\n// Backwards compatibility for request.debug\nObject.defineProperty(request, 'debug', {\n  enumerable: true,\n  get: function () {\n    return request.Request.debug\n  },\n  set: function (debug) {\n    request.Request.debug = debug\n  }\n})\n", "\n\nvar tough = require('tough-cookie')\n\nvar Cookie = tough.Cookie\nvar CookieJar = tough.CookieJar\n\nexports.parse = function (str) {\n  if (str && str.uri) {\n    str = str.uri\n  }\n  if (typeof str !== 'string') {\n    throw new Error('The cookie function only accepts STRING as param')\n  }\n  return Cookie.parse(str, {loose: true})\n}\n\n// Adapt the sometimes-Async api of tough.CookieJar to our requirements\nfunction RequestJar (store) {\n  var self = this\n  self._jar = new CookieJar(store, {looseMode: true})\n}\nRequestJar.prototype.setCookie = function (cookieOrStr, uri, options) {\n  var self = this\n  return self._jar.setCookieSync(cookieOrStr, uri, options || {})\n}\nRequestJar.prototype.getCookieString = function (uri) {\n  var self = this\n  return self._jar.getCookieStringSync(uri)\n}\nRequestJar.prototype.getCookies = function (uri) {\n  var self = this\n  return self._jar.getCookiesSync(uri)\n}\n\nexports.jar = function (store) {\n  return new RequestJar(store)\n}\n", "\n\nvar jsonSafeStringify = require('json-stringify-safe')\nvar crypto = require('crypto')\nvar Buffer = require('safe-buffer').Buffer\n\nvar defer = typeof setImmediate === 'undefined'\n  ? process.nextTick\n  : setImmediate\n\nfunction paramsHaveRequestBody (params) {\n  return (\n    params.body ||\n    params.requestBodyStream ||\n    (params.json && typeof params.json !== 'boolean') ||\n    params.multipart\n  )\n}\n\nfunction safeStringify (obj, replacer) {\n  var ret\n  try {\n    ret = JSON.stringify(obj, replacer)\n  } catch (e) {\n    ret = jsonSafeStringify(obj, replacer)\n  }\n  return ret\n}\n\nfunction md5 (str) {\n  return crypto.createHash('md5').update(str).digest('hex')\n}\n\nfunction isReadStream (rs) {\n  return rs.readable && rs.path && rs.mode\n}\n\nfunction toBase64 (str) {\n  return Buffer.from(str || '', 'utf8').toString('base64')\n}\n\nfunction copy (obj) {\n  var o = {}\n  Object.keys(obj).forEach(function (i) {\n    o[i] = obj[i]\n  })\n  return o\n}\n\nfunction version () {\n  var numbers = process.version.replace('v', '').split('.')\n  return {\n    major: parseInt(numbers[0], 10),\n    minor: parseInt(numbers[1], 10),\n    patch: parseInt(numbers[2], 10)\n  }\n}\n\nexports.paramsHaveRequestBody = paramsHaveRequestBody\nexports.safeStringify = safeStringify\nexports.md5 = md5\nexports.isReadStream = isReadStream\nexports.toBase64 = toBase64\nexports.copy = copy\nexports.version = version\nexports.defer = defer\n", "\n\nvar http = require('http')\nvar https = require('https')\nvar url = require('url')\nvar util = require('util')\nvar stream = require('stream')\nvar zlib = require('zlib')\nvar aws2 = require('aws-sign2')\nvar aws4 = require('aws4')\nvar httpSignature = require('http-signature')\nvar mime = require('mime-types')\nvar caseless = require('caseless')\nvar ForeverAgent = require('forever-agent')\nvar FormData = require('form-data')\nvar extend = require('extend')\nvar isstream = require('isstream')\nvar isTypedArray = require('is-typedarray').strict\nvar helpers = require('./lib/helpers')\nvar cookies = require('./lib/cookies')\nvar getProxyFromURI = require('./lib/getProxyFromURI')\nvar Querystring = require('./lib/querystring').Querystring\nvar Har = require('./lib/har').Har\nvar Auth = require('./lib/auth').Auth\nvar OAuth = require('./lib/oauth').OAuth\nvar hawk = require('./lib/hawk')\nvar Multipart = require('./lib/multipart').Multipart\nvar Redirect = require('./lib/redirect').Redirect\nvar Tunnel = require('./lib/tunnel').Tunnel\nvar now = require('performance-now')\nvar Buffer = require('safe-buffer').Buffer\n\nvar safeStringify = helpers.safeStringify\nvar isReadStream = helpers.isReadStream\nvar toBase64 = helpers.toBase64\nvar defer = helpers.defer\nvar copy = helpers.copy\nvar version = helpers.version\nvar globalCookieJar = cookies.jar()\n\nvar globalPool = {}\n\nfunction filterForNonReserved (reserved, options) {\n  // Filter out properties that are not reserved.\n  // Reserved values are passed in at call site.\n\n  var object = {}\n  for (var i in options) {\n    var notReserved = (reserved.indexOf(i) === -1)\n    if (notReserved) {\n      object[i] = options[i]\n    }\n  }\n  return object\n}\n\nfunction filterOutReservedFunctions (reserved, options) {\n  // Filter out properties that are functions and are reserved.\n  // Reserved values are passed in at call site.\n\n  var object = {}\n  for (var i in options) {\n    var isReserved = !(reserved.indexOf(i) === -1)\n    var isFunction = (typeof options[i] === 'function')\n    if (!(isReserved && isFunction)) {\n      object[i] = options[i]\n    }\n  }\n  return object\n}\n\n// Return a simpler request object to allow serialization\nfunction requestToJSON () {\n  var self = this\n  return {\n    uri: self.uri,\n    method: self.method,\n    headers: self.headers\n  }\n}\n\n// Return a simpler response object to allow serialization\nfunction responseToJSON () {\n  var self = this\n  return {\n    statusCode: self.statusCode,\n    body: self.body,\n    headers: self.headers,\n    request: requestToJSON.call(self.request)\n  }\n}\n\nfunction Request (options) {\n  // if given the method property in options, set property explicitMethod to true\n\n  // extend the Request instance with any non-reserved properties\n  // remove any reserved functions from the options object\n  // set Request instance to be readable and writable\n  // call init\n\n  var self = this\n\n  // start with HAR, then override with additional options\n  if (options.har) {\n    self._har = new Har(self)\n    options = self._har.options(options)\n  }\n\n  stream.Stream.call(self)\n  var reserved = Object.keys(Request.prototype)\n  var nonReserved = filterForNonReserved(reserved, options)\n\n  extend(self, nonReserved)\n  options = filterOutReservedFunctions(reserved, options)\n\n  self.readable = true\n  self.writable = true\n  if (options.method) {\n    self.explicitMethod = true\n  }\n  self._qs = new Querystring(self)\n  self._auth = new Auth(self)\n  self._oauth = new OAuth(self)\n  self._multipart = new Multipart(self)\n  self._redirect = new Redirect(self)\n  self._tunnel = new Tunnel(self)\n  self.init(options)\n}\n\nutil.inherits(Request, stream.Stream)\n\n// Debugging\nRequest.debug = process.env.NODE_DEBUG && /\\brequest\\b/.test(process.env.NODE_DEBUG)\nfunction debug () {\n  if (Request.debug) {\n    console.error('REQUEST %s', util.format.apply(util, arguments))\n  }\n}\nRequest.prototype.debug = debug\n\nRequest.prototype.init = function (options) {\n  // init() contains all the code to setup the request object.\n  // the actual outgoing request is not started until start() is called\n  // this function is called from both the constructor and on redirect.\n  var self = this\n  if (!options) {\n    options = {}\n  }\n  self.headers = self.headers ? copy(self.headers) : {}\n\n  // Delete headers with value undefined since they break\n  // ClientRequest.OutgoingMessage.setHeader in node 0.12\n  for (var headerName in self.headers) {\n    if (typeof self.headers[headerName] === 'undefined') {\n      delete self.headers[headerName]\n    }\n  }\n\n  caseless.httpify(self, self.headers)\n\n  if (!self.method) {\n    self.method = options.method || 'GET'\n  }\n  if (!self.localAddress) {\n    self.localAddress = options.localAddress\n  }\n\n  self._qs.init(options)\n\n  debug(options)\n  if (!self.pool && self.pool !== false) {\n    self.pool = globalPool\n  }\n  self.dests = self.dests || []\n  self.__isRequestRequest = true\n\n  // Protect against double callback\n  if (!self._callback && self.callback) {\n    self._callback = self.callback\n    self.callback = function () {\n      if (self._callbackCalled) {\n        return // Print a warning maybe?\n      }\n      self._callbackCalled = true\n      self._callback.apply(self, arguments)\n    }\n    self.on('error', self.callback.bind())\n    self.on('complete', self.callback.bind(self, null))\n  }\n\n  // People use this property instead all the time, so support it\n  if (!self.uri && self.url) {\n    self.uri = self.url\n    delete self.url\n  }\n\n  // If there's a baseUrl, then use it as the base URL (i.e. uri must be\n  // specified as a relative path and is appended to baseUrl).\n  if (self.baseUrl) {\n    if (typeof self.baseUrl !== 'string') {\n      return self.emit('error', new Error('options.baseUrl must be a string'))\n    }\n\n    if (typeof self.uri !== 'string') {\n      return self.emit('error', new Error('options.uri must be a string when using options.baseUrl'))\n    }\n\n    if (self.uri.indexOf('//') === 0 || self.uri.indexOf('://') !== -1) {\n      return self.emit('error', new Error('options.uri must be a path when using options.baseUrl'))\n    }\n\n    // Handle all cases to make sure that there's only one slash between\n    // baseUrl and uri.\n    var baseUrlEndsWithSlash = self.baseUrl.lastIndexOf('/') === self.baseUrl.length - 1\n    var uriStartsWithSlash = self.uri.indexOf('/') === 0\n\n    if (baseUrlEndsWithSlash && uriStartsWithSlash) {\n      self.uri = self.baseUrl + self.uri.slice(1)\n    } else if (baseUrlEndsWithSlash || uriStartsWithSlash) {\n      self.uri = self.baseUrl + self.uri\n    } else if (self.uri === '') {\n      self.uri = self.baseUrl\n    } else {\n      self.uri = self.baseUrl + '/' + self.uri\n    }\n    delete self.baseUrl\n  }\n\n  // A URI is needed by this point, emit error if we haven't been able to get one\n  if (!self.uri) {\n    return self.emit('error', new Error('options.uri is a required argument'))\n  }\n\n  // If a string URI/URL was given, parse it into a URL object\n  if (typeof self.uri === 'string') {\n    self.uri = url.parse(self.uri)\n  }\n\n  // Some URL objects are not from a URL parsed string and need href added\n  if (!self.uri.href) {\n    self.uri.href = url.format(self.uri)\n  }\n\n  // DEPRECATED: Warning for users of the old Unix Sockets URL Scheme\n  if (self.uri.protocol === 'unix:') {\n    return self.emit('error', new Error('`unix://` URL scheme is no longer supported. Please use the format `http://unix:SOCKET:PATH`'))\n  }\n\n  // Support Unix Sockets\n  if (self.uri.host === 'unix') {\n    self.enableUnixSocket()\n  }\n\n  if (self.strictSSL === false) {\n    self.rejectUnauthorized = false\n  }\n\n  if (!self.uri.pathname) { self.uri.pathname = '/' }\n\n  if (!(self.uri.host || (self.uri.hostname && self.uri.port)) && !self.uri.isUnix) {\n    // Invalid URI: it may generate lot of bad errors, like 'TypeError: Cannot call method `indexOf` of undefined' in CookieJar\n    // Detect and reject it as soon as possible\n    var faultyUri = url.format(self.uri)\n    var message = 'Invalid URI \"' + faultyUri + '\"'\n    if (Object.keys(options).length === 0) {\n      // No option ? This can be the sign of a redirect\n      // As this is a case where the user cannot do anything (they didn't call request directly with this URL)\n      // they should be warned that it can be caused by a redirection (can save some hair)\n      message += '. This can be caused by a crappy redirection.'\n    }\n    // This error was fatal\n    self.abort()\n    return self.emit('error', new Error(message))\n  }\n\n  if (!self.hasOwnProperty('proxy')) {\n    self.proxy = getProxyFromURI(self.uri)\n  }\n\n  self.tunnel = self._tunnel.isEnabled()\n  if (self.proxy) {\n    self._tunnel.setup(options)\n  }\n\n  self._redirect.onRequest(options)\n\n  self.setHost = false\n  if (!self.hasHeader('host')) {\n    var hostHeaderName = self.originalHostHeaderName || 'host'\n    self.setHeader(hostHeaderName, self.uri.host)\n    // Drop :port suffix from Host header if known protocol.\n    if (self.uri.port) {\n      if ((self.uri.port === '80' && self.uri.protocol === 'http:') ||\n          (self.uri.port === '443' && self.uri.protocol === 'https:')) {\n        self.setHeader(hostHeaderName, self.uri.hostname)\n      }\n    }\n    self.setHost = true\n  }\n\n  self.jar(self._jar || options.jar)\n\n  if (!self.uri.port) {\n    if (self.uri.protocol === 'http:') { self.uri.port = 80 } else if (self.uri.protocol === 'https:') { self.uri.port = 443 }\n  }\n\n  if (self.proxy && !self.tunnel) {\n    self.port = self.proxy.port\n    self.host = self.proxy.hostname\n  } else {\n    self.port = self.uri.port\n    self.host = self.uri.hostname\n  }\n\n  if (options.form) {\n    self.form(options.form)\n  }\n\n  if (options.formData) {\n    var formData = options.formData\n    var requestForm = self.form()\n    var appendFormValue = function (key, value) {\n      if (value && value.hasOwnProperty('value') && value.hasOwnProperty('options')) {\n        requestForm.append(key, value.value, value.options)\n      } else {\n        requestForm.append(key, value)\n      }\n    }\n    for (var formKey in formData) {\n      if (formData.hasOwnProperty(formKey)) {\n        var formValue = formData[formKey]\n        if (formValue instanceof Array) {\n          for (var j = 0; j < formValue.length; j++) {\n            appendFormValue(formKey, formValue[j])\n          }\n        } else {\n          appendFormValue(formKey, formValue)\n        }\n      }\n    }\n  }\n\n  if (options.qs) {\n    self.qs(options.qs)\n  }\n\n  if (self.uri.path) {\n    self.path = self.uri.path\n  } else {\n    self.path = self.uri.pathname + (self.uri.search || '')\n  }\n\n  if (self.path.length === 0) {\n    self.path = '/'\n  }\n\n  // Auth must happen last in case signing is dependent on other headers\n  if (options.aws) {\n    self.aws(options.aws)\n  }\n\n  if (options.hawk) {\n    self.hawk(options.hawk)\n  }\n\n  if (options.httpSignature) {\n    self.httpSignature(options.httpSignature)\n  }\n\n  if (options.auth) {\n    if (Object.prototype.hasOwnProperty.call(options.auth, 'username')) {\n      options.auth.user = options.auth.username\n    }\n    if (Object.prototype.hasOwnProperty.call(options.auth, 'password')) {\n      options.auth.pass = options.auth.password\n    }\n\n    self.auth(\n      options.auth.user,\n      options.auth.pass,\n      options.auth.sendImmediately,\n      options.auth.bearer\n    )\n  }\n\n  if (self.gzip && !self.hasHeader('accept-encoding')) {\n    self.setHeader('accept-encoding', 'gzip, deflate')\n  }\n\n  if (self.uri.auth && !self.hasHeader('authorization')) {\n    var uriAuthPieces = self.uri.auth.split(':').map(function (item) { return self._qs.unescape(item) })\n    self.auth(uriAuthPieces[0], uriAuthPieces.slice(1).join(':'), true)\n  }\n\n  if (!self.tunnel && self.proxy && self.proxy.auth && !self.hasHeader('proxy-authorization')) {\n    var proxyAuthPieces = self.proxy.auth.split(':').map(function (item) { return self._qs.unescape(item) })\n    var authHeader = 'Basic ' + toBase64(proxyAuthPieces.join(':'))\n    self.setHeader('proxy-authorization', authHeader)\n  }\n\n  if (self.proxy && !self.tunnel) {\n    self.path = (self.uri.protocol + '//' + self.uri.host + self.path)\n  }\n\n  if (options.json) {\n    self.json(options.json)\n  }\n  if (options.multipart) {\n    self.multipart(options.multipart)\n  }\n\n  if (options.time) {\n    self.timing = true\n\n    // NOTE: elapsedTime is deprecated in favor of .timings\n    self.elapsedTime = self.elapsedTime || 0\n  }\n\n  function setContentLength () {\n    if (isTypedArray(self.body)) {\n      self.body = Buffer.from(self.body)\n    }\n\n    if (!self.hasHeader('content-length')) {\n      var length\n      if (typeof self.body === 'string') {\n        length = Buffer.byteLength(self.body)\n      } else if (Array.isArray(self.body)) {\n        length = self.body.reduce(function (a, b) { return a + b.length }, 0)\n      } else {\n        length = self.body.length\n      }\n\n      if (length) {\n        self.setHeader('content-length', length)\n      } else {\n        self.emit('error', new Error('Argument error, options.body.'))\n      }\n    }\n  }\n  if (self.body && !isstream(self.body)) {\n    setContentLength()\n  }\n\n  if (options.oauth) {\n    self.oauth(options.oauth)\n  } else if (self._oauth.params && self.hasHeader('authorization')) {\n    self.oauth(self._oauth.params)\n  }\n\n  var protocol = self.proxy && !self.tunnel ? self.proxy.protocol : self.uri.protocol\n  var defaultModules = {'http:': http, 'https:': https}\n  var httpModules = self.httpModules || {}\n\n  self.httpModule = httpModules[protocol] || defaultModules[protocol]\n\n  if (!self.httpModule) {\n    return self.emit('error', new Error('Invalid protocol: ' + protocol))\n  }\n\n  if (options.ca) {\n    self.ca = options.ca\n  }\n\n  if (!self.agent) {\n    if (options.agentOptions) {\n      self.agentOptions = options.agentOptions\n    }\n\n    if (options.agentClass) {\n      self.agentClass = options.agentClass\n    } else if (options.forever) {\n      var v = version()\n      // use ForeverAgent in node 0.10- only\n      if (v.major === 0 && v.minor <= 10) {\n        self.agentClass = protocol === 'http:' ? ForeverAgent : ForeverAgent.SSL\n      } else {\n        self.agentClass = self.httpModule.Agent\n        self.agentOptions = self.agentOptions || {}\n        self.agentOptions.keepAlive = true\n      }\n    } else {\n      self.agentClass = self.httpModule.Agent\n    }\n  }\n\n  if (self.pool === false) {\n    self.agent = false\n  } else {\n    self.agent = self.agent || self.getNewAgent()\n  }\n\n  self.on('pipe', function (src) {\n    if (self.ntick && self._started) {\n      self.emit('error', new Error('You cannot pipe to this stream after the outbound request has started.'))\n    }\n    self.src = src\n    if (isReadStream(src)) {\n      if (!self.hasHeader('content-type')) {\n        self.setHeader('content-type', mime.lookup(src.path))\n      }\n    } else {\n      if (src.headers) {\n        for (var i in src.headers) {\n          if (!self.hasHeader(i)) {\n            self.setHeader(i, src.headers[i])\n          }\n        }\n      }\n      if (self._json && !self.hasHeader('content-type')) {\n        self.setHeader('content-type', 'application/json')\n      }\n      if (src.method && !self.explicitMethod) {\n        self.method = src.method\n      }\n    }\n\n  // self.on('pipe', function () {\n  //   console.error('You have already piped to this stream. Pipeing twice is likely to break the request.')\n  // })\n  })\n\n  defer(function () {\n    if (self._aborted) {\n      return\n    }\n\n    var end = function () {\n      if (self._form) {\n        if (!self._auth.hasAuth) {\n          self._form.pipe(self)\n        } else if (self._auth.hasAuth && self._auth.sentAuth) {\n          self._form.pipe(self)\n        }\n      }\n      if (self._multipart && self._multipart.chunked) {\n        self._multipart.body.pipe(self)\n      }\n      if (self.body) {\n        if (isstream(self.body)) {\n          self.body.pipe(self)\n        } else {\n          setContentLength()\n          if (Array.isArray(self.body)) {\n            self.body.forEach(function (part) {\n              self.write(part)\n            })\n          } else {\n            self.write(self.body)\n          }\n          self.end()\n        }\n      } else if (self.requestBodyStream) {\n        console.warn('options.requestBodyStream is deprecated, please pass the request object to stream.pipe.')\n        self.requestBodyStream.pipe(self)\n      } else if (!self.src) {\n        if (self._auth.hasAuth && !self._auth.sentAuth) {\n          self.end()\n          return\n        }\n        if (self.method !== 'GET' && typeof self.method !== 'undefined') {\n          self.setHeader('content-length', 0)\n        }\n        self.end()\n      }\n    }\n\n    if (self._form && !self.hasHeader('content-length')) {\n      // Before ending the request, we had to compute the length of the whole form, asyncly\n      self.setHeader(self._form.getHeaders(), true)\n      self._form.getLength(function (err, length) {\n        if (!err && !isNaN(length)) {\n          self.setHeader('content-length', length)\n        }\n        end()\n      })\n    } else {\n      end()\n    }\n\n    self.ntick = true\n  })\n}\n\nRequest.prototype.getNewAgent = function () {\n  var self = this\n  var Agent = self.agentClass\n  var options = {}\n  if (self.agentOptions) {\n    for (var i in self.agentOptions) {\n      options[i] = self.agentOptions[i]\n    }\n  }\n  if (self.ca) {\n    options.ca = self.ca\n  }\n  if (self.ciphers) {\n    options.ciphers = self.ciphers\n  }\n  if (self.secureProtocol) {\n    options.secureProtocol = self.secureProtocol\n  }\n  if (self.secureOptions) {\n    options.secureOptions = self.secureOptions\n  }\n  if (typeof self.rejectUnauthorized !== 'undefined') {\n    options.rejectUnauthorized = self.rejectUnauthorized\n  }\n\n  if (self.cert && self.key) {\n    options.key = self.key\n    options.cert = self.cert\n  }\n\n  if (self.pfx) {\n    options.pfx = self.pfx\n  }\n\n  if (self.passphrase) {\n    options.passphrase = self.passphrase\n  }\n\n  var poolKey = ''\n\n  // different types of agents are in different pools\n  if (Agent !== self.httpModule.Agent) {\n    poolKey += Agent.name\n  }\n\n  // ca option is only relevant if proxy or destination are https\n  var proxy = self.proxy\n  if (typeof proxy === 'string') {\n    proxy = url.parse(proxy)\n  }\n  var isHttps = (proxy && proxy.protocol === 'https:') || this.uri.protocol === 'https:'\n\n  if (isHttps) {\n    if (options.ca) {\n      if (poolKey) {\n        poolKey += ':'\n      }\n      poolKey += options.ca\n    }\n\n    if (typeof options.rejectUnauthorized !== 'undefined') {\n      if (poolKey) {\n        poolKey += ':'\n      }\n      poolKey += options.rejectUnauthorized\n    }\n\n    if (options.cert) {\n      if (poolKey) {\n        poolKey += ':'\n      }\n      poolKey += options.cert.toString('ascii') + options.key.toString('ascii')\n    }\n\n    if (options.pfx) {\n      if (poolKey) {\n        poolKey += ':'\n      }\n      poolKey += options.pfx.toString('ascii')\n    }\n\n    if (options.ciphers) {\n      if (poolKey) {\n        poolKey += ':'\n      }\n      poolKey += options.ciphers\n    }\n\n    if (options.secureProtocol) {\n      if (poolKey) {\n        poolKey += ':'\n      }\n      poolKey += options.secureProtocol\n    }\n\n    if (options.secureOptions) {\n      if (poolKey) {\n        poolKey += ':'\n      }\n      poolKey += options.secureOptions\n    }\n  }\n\n  if (self.pool === globalPool && !poolKey && Object.keys(options).length === 0 && self.httpModule.globalAgent) {\n    // not doing anything special.  Use the globalAgent\n    return self.httpModule.globalAgent\n  }\n\n  // we're using a stored agent.  Make sure it's protocol-specific\n  poolKey = self.uri.protocol + poolKey\n\n  // generate a new agent for this setting if none yet exists\n  if (!self.pool[poolKey]) {\n    self.pool[poolKey] = new Agent(options)\n    // properly set maxSockets on new agents\n    if (self.pool.maxSockets) {\n      self.pool[poolKey].maxSockets = self.pool.maxSockets\n    }\n  }\n\n  return self.pool[poolKey]\n}\n\nRequest.prototype.start = function () {\n  // start() is called once we are ready to send the outgoing HTTP request.\n  // this is usually called on the first write(), end() or on nextTick()\n  var self = this\n\n  if (self.timing) {\n    // All timings will be relative to this request's startTime.  In order to do this,\n    // we need to capture the wall-clock start time (via Date), immediately followed\n    // by the high-resolution timer (via now()).  While these two won't be set\n    // at the _exact_ same time, they should be close enough to be able to calculate\n    // high-resolution, monotonically non-decreasing timestamps relative to startTime.\n    var startTime = new Date().getTime()\n    var startTimeNow = now()\n  }\n\n  if (self._aborted) {\n    return\n  }\n\n  self._started = true\n  self.method = self.method || 'GET'\n  self.href = self.uri.href\n\n  if (self.src && self.src.stat && self.src.stat.size && !self.hasHeader('content-length')) {\n    self.setHeader('content-length', self.src.stat.size)\n  }\n  if (self._aws) {\n    self.aws(self._aws, true)\n  }\n\n  // We have a method named auth, which is completely different from the http.request\n  // auth option.  If we don't remove it, we're gonna have a bad time.\n  var reqOptions = copy(self)\n  delete reqOptions.auth\n\n  debug('make request', self.uri.href)\n\n  // node v6.8.0 now supports a `timeout` value in `http.request()`, but we\n  // should delete it for now since we handle timeouts manually for better\n  // consistency with node versions before v6.8.0\n  delete reqOptions.timeout\n\n  try {\n    self.req = self.httpModule.request(reqOptions)\n  } catch (err) {\n    self.emit('error', err)\n    return\n  }\n\n  if (self.timing) {\n    self.startTime = startTime\n    self.startTimeNow = startTimeNow\n\n    // Timing values will all be relative to startTime (by comparing to startTimeNow\n    // so we have an accurate clock)\n    self.timings = {}\n  }\n\n  var timeout\n  if (self.timeout && !self.timeoutTimer) {\n    if (self.timeout < 0) {\n      timeout = 0\n    } else if (typeof self.timeout === 'number' && isFinite(self.timeout)) {\n      timeout = self.timeout\n    }\n  }\n\n  self.req.on('response', self.onRequestResponse.bind(self))\n  self.req.on('error', self.onRequestError.bind(self))\n  self.req.on('drain', function () {\n    self.emit('drain')\n  })\n\n  self.req.on('socket', function (socket) {\n    // `._connecting` was the old property which was made public in node v6.1.0\n    var isConnecting = socket._connecting || socket.connecting\n    if (self.timing) {\n      self.timings.socket = now() - self.startTimeNow\n\n      if (isConnecting) {\n        var onLookupTiming = function () {\n          self.timings.lookup = now() - self.startTimeNow\n        }\n\n        var onConnectTiming = function () {\n          self.timings.connect = now() - self.startTimeNow\n        }\n\n        socket.once('lookup', onLookupTiming)\n        socket.once('connect', onConnectTiming)\n\n        // clean up timing event listeners if needed on error\n        self.req.once('error', function () {\n          socket.removeListener('lookup', onLookupTiming)\n          socket.removeListener('connect', onConnectTiming)\n        })\n      }\n    }\n\n    var setReqTimeout = function () {\n      // This timeout sets the amount of time to wait *between* bytes sent\n      // from the server once connected.\n      //\n      // In particular, it's useful for erroring if the server fails to send\n      // data halfway through streaming a response.\n      self.req.setTimeout(timeout, function () {\n        if (self.req) {\n          self.abort()\n          var e = new Error('ESOCKETTIMEDOUT')\n          e.code = 'ESOCKETTIMEDOUT'\n          e.connect = false\n          self.emit('error', e)\n        }\n      })\n    }\n    if (timeout !== undefined) {\n      // Only start the connection timer if we're actually connecting a new\n      // socket, otherwise if we're already connected (because this is a\n      // keep-alive connection) do not bother. This is important since we won't\n      // get a 'connect' event for an already connected socket.\n      if (isConnecting) {\n        var onReqSockConnect = function () {\n          socket.removeListener('connect', onReqSockConnect)\n          self.clearTimeout()\n          setReqTimeout()\n        }\n\n        socket.on('connect', onReqSockConnect)\n\n        self.req.on('error', function (err) { // eslint-disable-line handle-callback-err\n          socket.removeListener('connect', onReqSockConnect)\n        })\n\n        // Set a timeout in memory - this block will throw if the server takes more\n        // than `timeout` to write the HTTP status and headers (corresponding to\n        // the on('response') event on the client). NB: this measures wall-clock\n        // time, not the time between bytes sent by the server.\n        self.timeoutTimer = setTimeout(function () {\n          socket.removeListener('connect', onReqSockConnect)\n          self.abort()\n          var e = new Error('ETIMEDOUT')\n          e.code = 'ETIMEDOUT'\n          e.connect = true\n          self.emit('error', e)\n        }, timeout)\n      } else {\n        // We're already connected\n        setReqTimeout()\n      }\n    }\n    self.emit('socket', socket)\n  })\n\n  self.emit('request', self.req)\n}\n\nRequest.prototype.onRequestError = function (error) {\n  var self = this\n  if (self._aborted) {\n    return\n  }\n  if (self.req && self.req._reusedSocket && error.code === 'ECONNRESET' &&\n    self.agent.addRequestNoreuse) {\n    self.agent = { addRequest: self.agent.addRequestNoreuse.bind(self.agent) }\n    self.start()\n    self.req.end()\n    return\n  }\n  self.clearTimeout()\n  self.emit('error', error)\n}\n\nRequest.prototype.onRequestResponse = function (response) {\n  var self = this\n\n  if (self.timing) {\n    self.timings.response = now() - self.startTimeNow\n  }\n\n  debug('onRequestResponse', self.uri.href, response.statusCode, response.headers)\n  response.on('end', function () {\n    if (self.timing) {\n      self.timings.end = now() - self.startTimeNow\n      response.timingStart = self.startTime\n\n      // fill in the blanks for any periods that didn't trigger, such as\n      // no lookup or connect due to keep alive\n      if (!self.timings.socket) {\n        self.timings.socket = 0\n      }\n      if (!self.timings.lookup) {\n        self.timings.lookup = self.timings.socket\n      }\n      if (!self.timings.connect) {\n        self.timings.connect = self.timings.lookup\n      }\n      if (!self.timings.response) {\n        self.timings.response = self.timings.connect\n      }\n\n      debug('elapsed time', self.timings.end)\n\n      // elapsedTime includes all redirects\n      self.elapsedTime += Math.round(self.timings.end)\n\n      // NOTE: elapsedTime is deprecated in favor of .timings\n      response.elapsedTime = self.elapsedTime\n\n      // timings is just for the final fetch\n      response.timings = self.timings\n\n      // pre-calculate phase timings as well\n      response.timingPhases = {\n        wait: self.timings.socket,\n        dns: self.timings.lookup - self.timings.socket,\n        tcp: self.timings.connect - self.timings.lookup,\n        firstByte: self.timings.response - self.timings.connect,\n        download: self.timings.end - self.timings.response,\n        total: self.timings.end\n      }\n    }\n    debug('response end', self.uri.href, response.statusCode, response.headers)\n  })\n\n  if (self._aborted) {\n    debug('aborted', self.uri.href)\n    response.resume()\n    return\n  }\n\n  self.response = response\n  response.request = self\n  response.toJSON = responseToJSON\n\n  // XXX This is different on 0.10, because SSL is strict by default\n  if (self.httpModule === https &&\n    self.strictSSL && (!response.hasOwnProperty('socket') ||\n    !response.socket.authorized)) {\n    debug('strict ssl error', self.uri.href)\n    var sslErr = response.hasOwnProperty('socket') ? response.socket.authorizationError : self.uri.href + ' does not support SSL'\n    self.emit('error', new Error('SSL Error: ' + sslErr))\n    return\n  }\n\n  // Save the original host before any redirect (if it changes, we need to\n  // remove any authorization headers).  Also remember the case of the header\n  // name because lots of broken servers expect Host instead of host and we\n  // want the caller to be able to specify this.\n  self.originalHost = self.getHeader('host')\n  if (!self.originalHostHeaderName) {\n    self.originalHostHeaderName = self.hasHeader('host')\n  }\n  if (self.setHost) {\n    self.removeHeader('host')\n  }\n  self.clearTimeout()\n\n  var targetCookieJar = (self._jar && self._jar.setCookie) ? self._jar : globalCookieJar\n  var addCookie = function (cookie) {\n    // set the cookie if it's domain in the href's domain.\n    try {\n      targetCookieJar.setCookie(cookie, self.uri.href, {ignoreError: true})\n    } catch (e) {\n      self.emit('error', e)\n    }\n  }\n\n  response.caseless = caseless(response.headers)\n\n  if (response.caseless.has('set-cookie') && (!self._disableCookies)) {\n    var headerName = response.caseless.has('set-cookie')\n    if (Array.isArray(response.headers[headerName])) {\n      response.headers[headerName].forEach(addCookie)\n    } else {\n      addCookie(response.headers[headerName])\n    }\n  }\n\n  if (self._redirect.onResponse(response)) {\n    return // Ignore the rest of the response\n  } else {\n    // Be a good stream and emit end when the response is finished.\n    // Hack to emit end on close because of a core bug that never fires end\n    response.on('close', function () {\n      if (!self._ended) {\n        self.response.emit('end')\n      }\n    })\n\n    response.once('end', function () {\n      self._ended = true\n    })\n\n    var noBody = function (code) {\n      return (\n        self.method === 'HEAD' ||\n        // Informational\n        (code >= 100 && code < 200) ||\n        // No Content\n        code === 204 ||\n        // Not Modified\n        code === 304\n      )\n    }\n\n    var responseContent\n    if (self.gzip && !noBody(response.statusCode)) {\n      var contentEncoding = response.headers['content-encoding'] || 'identity'\n      contentEncoding = contentEncoding.trim().toLowerCase()\n\n      // Be more lenient with decoding compressed responses, since (very rarely)\n      // servers send slightly invalid gzip responses that are still accepted\n      // by common browsers.\n      // Always using Z_SYNC_FLUSH is what cURL does.\n      var zlibOptions = {\n        flush: zlib.Z_SYNC_FLUSH,\n        finishFlush: zlib.Z_SYNC_FLUSH\n      }\n\n      if (contentEncoding === 'gzip') {\n        responseContent = zlib.createGunzip(zlibOptions)\n        response.pipe(responseContent)\n      } else if (contentEncoding === 'deflate') {\n        responseContent = zlib.createInflate(zlibOptions)\n        response.pipe(responseContent)\n      } else {\n        // Since previous versions didn't check for Content-Encoding header,\n        // ignore any invalid values to preserve backwards-compatibility\n        if (contentEncoding !== 'identity') {\n          debug('ignoring unrecognized Content-Encoding ' + contentEncoding)\n        }\n        responseContent = response\n      }\n    } else {\n      responseContent = response\n    }\n\n    if (self.encoding) {\n      if (self.dests.length !== 0) {\n        console.error('Ignoring encoding parameter as this stream is being piped to another stream which makes the encoding option invalid.')\n      } else {\n        responseContent.setEncoding(self.encoding)\n      }\n    }\n\n    if (self._paused) {\n      responseContent.pause()\n    }\n\n    self.responseContent = responseContent\n\n    self.emit('response', response)\n\n    self.dests.forEach(function (dest) {\n      self.pipeDest(dest)\n    })\n\n    responseContent.on('data', function (chunk) {\n      if (self.timing && !self.responseStarted) {\n        self.responseStartTime = (new Date()).getTime()\n\n        // NOTE: responseStartTime is deprecated in favor of .timings\n        response.responseStartTime = self.responseStartTime\n      }\n      self._destdata = true\n      self.emit('data', chunk)\n    })\n    responseContent.once('end', function (chunk) {\n      self.emit('end', chunk)\n    })\n    responseContent.on('error', function (error) {\n      self.emit('error', error)\n    })\n    responseContent.on('close', function () { self.emit('close') })\n\n    if (self.callback) {\n      self.readResponseBody(response)\n    } else { // if no callback\n      self.on('end', function () {\n        if (self._aborted) {\n          debug('aborted', self.uri.href)\n          return\n        }\n        self.emit('complete', response)\n      })\n    }\n  }\n  debug('finish init function', self.uri.href)\n}\n\nRequest.prototype.readResponseBody = function (response) {\n  var self = this\n  debug(\"reading response's body\")\n  var buffers = []\n  var bufferLength = 0\n  var strings = []\n\n  self.on('data', function (chunk) {\n    if (!Buffer.isBuffer(chunk)) {\n      strings.push(chunk)\n    } else if (chunk.length) {\n      bufferLength += chunk.length\n      buffers.push(chunk)\n    }\n  })\n  self.on('end', function () {\n    debug('end event', self.uri.href)\n    if (self._aborted) {\n      debug('aborted', self.uri.href)\n      // `buffer` is defined in the parent scope and used in a closure it exists for the life of the request.\n      // This can lead to leaky behavior if the user retains a reference to the request object.\n      buffers = []\n      bufferLength = 0\n      return\n    }\n\n    if (bufferLength) {\n      debug('has body', self.uri.href, bufferLength)\n      response.body = Buffer.concat(buffers, bufferLength)\n      if (self.encoding !== null) {\n        response.body = response.body.toString(self.encoding)\n      }\n      // `buffer` is defined in the parent scope and used in a closure it exists for the life of the Request.\n      // This can lead to leaky behavior if the user retains a reference to the request object.\n      buffers = []\n      bufferLength = 0\n    } else if (strings.length) {\n      // The UTF8 BOM [0xEF,0xBB,0xBF] is converted to [0xFE,0xFF] in the JS UTC16/UCS2 representation.\n      // Strip this value out when the encoding is set to 'utf8', as upstream consumers won't expect it and it breaks JSON.parse().\n      if (self.encoding === 'utf8' && strings[0].length > 0 && strings[0][0] === '\\uFEFF') {\n        strings[0] = strings[0].substring(1)\n      }\n      response.body = strings.join('')\n    }\n\n    if (self._json) {\n      try {\n        response.body = JSON.parse(response.body, self._jsonReviver)\n      } catch (e) {\n        debug('invalid JSON received', self.uri.href)\n      }\n    }\n    debug('emitting complete', self.uri.href)\n    if (typeof response.body === 'undefined' && !self._json) {\n      response.body = self.encoding === null ? Buffer.alloc(0) : ''\n    }\n    self.emit('complete', response, response.body)\n  })\n}\n\nRequest.prototype.abort = function () {\n  var self = this\n  self._aborted = true\n\n  if (self.req) {\n    self.req.abort()\n  } else if (self.response) {\n    self.response.destroy()\n  }\n\n  self.clearTimeout()\n  self.emit('abort')\n}\n\nRequest.prototype.pipeDest = function (dest) {\n  var self = this\n  var response = self.response\n  // Called after the response is received\n  if (dest.headers && !dest.headersSent) {\n    if (response.caseless.has('content-type')) {\n      var ctname = response.caseless.has('content-type')\n      if (dest.setHeader) {\n        dest.setHeader(ctname, response.headers[ctname])\n      } else {\n        dest.headers[ctname] = response.headers[ctname]\n      }\n    }\n\n    if (response.caseless.has('content-length')) {\n      var clname = response.caseless.has('content-length')\n      if (dest.setHeader) {\n        dest.setHeader(clname, response.headers[clname])\n      } else {\n        dest.headers[clname] = response.headers[clname]\n      }\n    }\n  }\n  if (dest.setHeader && !dest.headersSent) {\n    for (var i in response.headers) {\n      // If the response content is being decoded, the Content-Encoding header\n      // of the response doesn't represent the piped content, so don't pass it.\n      if (!self.gzip || i !== 'content-encoding') {\n        dest.setHeader(i, response.headers[i])\n      }\n    }\n    dest.statusCode = response.statusCode\n  }\n  if (self.pipefilter) {\n    self.pipefilter(response, dest)\n  }\n}\n\nRequest.prototype.qs = function (q, clobber) {\n  var self = this\n  var base\n  if (!clobber && self.uri.query) {\n    base = self._qs.parse(self.uri.query)\n  } else {\n    base = {}\n  }\n\n  for (var i in q) {\n    base[i] = q[i]\n  }\n\n  var qs = self._qs.stringify(base)\n\n  if (qs === '') {\n    return self\n  }\n\n  self.uri = url.parse(self.uri.href.split('?')[0] + '?' + qs)\n  self.url = self.uri\n  self.path = self.uri.path\n\n  if (self.uri.host === 'unix') {\n    self.enableUnixSocket()\n  }\n\n  return self\n}\nRequest.prototype.form = function (form) {\n  var self = this\n  if (form) {\n    if (!/^application\\/x-www-form-urlencoded\\b/.test(self.getHeader('content-type'))) {\n      self.setHeader('content-type', 'application/x-www-form-urlencoded')\n    }\n    self.body = (typeof form === 'string')\n      ? self._qs.rfc3986(form.toString('utf8'))\n      : self._qs.stringify(form).toString('utf8')\n    return self\n  }\n  // create form-data object\n  self._form = new FormData()\n  self._form.on('error', function (err) {\n    err.message = 'form-data: ' + err.message\n    self.emit('error', err)\n    self.abort()\n  })\n  return self._form\n}\nRequest.prototype.multipart = function (multipart) {\n  var self = this\n\n  self._multipart.onRequest(multipart)\n\n  if (!self._multipart.chunked) {\n    self.body = self._multipart.body\n  }\n\n  return self\n}\nRequest.prototype.json = function (val) {\n  var self = this\n\n  if (!self.hasHeader('accept')) {\n    self.setHeader('accept', 'application/json')\n  }\n\n  if (typeof self.jsonReplacer === 'function') {\n    self._jsonReplacer = self.jsonReplacer\n  }\n\n  self._json = true\n  if (typeof val === 'boolean') {\n    if (self.body !== undefined) {\n      if (!/^application\\/x-www-form-urlencoded\\b/.test(self.getHeader('content-type'))) {\n        self.body = safeStringify(self.body, self._jsonReplacer)\n      } else {\n        self.body = self._qs.rfc3986(self.body)\n      }\n      if (!self.hasHeader('content-type')) {\n        self.setHeader('content-type', 'application/json')\n      }\n    }\n  } else {\n    self.body = safeStringify(val, self._jsonReplacer)\n    if (!self.hasHeader('content-type')) {\n      self.setHeader('content-type', 'application/json')\n    }\n  }\n\n  if (typeof self.jsonReviver === 'function') {\n    self._jsonReviver = self.jsonReviver\n  }\n\n  return self\n}\nRequest.prototype.getHeader = function (name, headers) {\n  var self = this\n  var result, re, match\n  if (!headers) {\n    headers = self.headers\n  }\n  Object.keys(headers).forEach(function (key) {\n    if (key.length !== name.length) {\n      return\n    }\n    re = new RegExp(name, 'i')\n    match = key.match(re)\n    if (match) {\n      result = headers[key]\n    }\n  })\n  return result\n}\nRequest.prototype.enableUnixSocket = function () {\n  // Get the socket & request paths from the URL\n  var unixParts = this.uri.path.split(':')\n  var host = unixParts[0]\n  var path = unixParts[1]\n  // Apply unix properties to request\n  this.socketPath = host\n  this.uri.pathname = path\n  this.uri.path = path\n  this.uri.host = host\n  this.uri.hostname = host\n  this.uri.isUnix = true\n}\n\nRequest.prototype.auth = function (user, pass, sendImmediately, bearer) {\n  var self = this\n\n  self._auth.onRequest(user, pass, sendImmediately, bearer)\n\n  return self\n}\nRequest.prototype.aws = function (opts, now) {\n  var self = this\n\n  if (!now) {\n    self._aws = opts\n    return self\n  }\n\n  if (opts.sign_version === 4 || opts.sign_version === '4') {\n    // use aws4\n    var options = {\n      host: self.uri.host,\n      path: self.uri.path,\n      method: self.method,\n      headers: self.headers,\n      body: self.body\n    }\n    if (opts.service) {\n      options.service = opts.service\n    }\n    var signRes = aws4.sign(options, {\n      accessKeyId: opts.key,\n      secretAccessKey: opts.secret,\n      sessionToken: opts.session\n    })\n    self.setHeader('authorization', signRes.headers.Authorization)\n    self.setHeader('x-amz-date', signRes.headers['X-Amz-Date'])\n    if (signRes.headers['X-Amz-Security-Token']) {\n      self.setHeader('x-amz-security-token', signRes.headers['X-Amz-Security-Token'])\n    }\n  } else {\n    // default: use aws-sign2\n    var date = new Date()\n    self.setHeader('date', date.toUTCString())\n    var auth = {\n      key: opts.key,\n      secret: opts.secret,\n      verb: self.method.toUpperCase(),\n      date: date,\n      contentType: self.getHeader('content-type') || '',\n      md5: self.getHeader('content-md5') || '',\n      amazonHeaders: aws2.canonicalizeHeaders(self.headers)\n    }\n    var path = self.uri.path\n    if (opts.bucket && path) {\n      auth.resource = '/' + opts.bucket + path\n    } else if (opts.bucket && !path) {\n      auth.resource = '/' + opts.bucket\n    } else if (!opts.bucket && path) {\n      auth.resource = path\n    } else if (!opts.bucket && !path) {\n      auth.resource = '/'\n    }\n    auth.resource = aws2.canonicalizeResource(auth.resource)\n    self.setHeader('authorization', aws2.authorization(auth))\n  }\n\n  return self\n}\nRequest.prototype.httpSignature = function (opts) {\n  var self = this\n  httpSignature.signRequest({\n    getHeader: function (header) {\n      return self.getHeader(header, self.headers)\n    },\n    setHeader: function (header, value) {\n      self.setHeader(header, value)\n    },\n    method: self.method,\n    path: self.path\n  }, opts)\n  debug('httpSignature authorization', self.getHeader('authorization'))\n\n  return self\n}\nRequest.prototype.hawk = function (opts) {\n  var self = this\n  self.setHeader('Authorization', hawk.header(self.uri, self.method, opts))\n}\nRequest.prototype.oauth = function (_oauth) {\n  var self = this\n\n  self._oauth.onRequest(_oauth)\n\n  return self\n}\n\nRequest.prototype.jar = function (jar) {\n  var self = this\n  var cookies\n\n  if (self._redirect.redirectsFollowed === 0) {\n    self.originalCookieHeader = self.getHeader('cookie')\n  }\n\n  if (!jar) {\n    // disable cookies\n    cookies = false\n    self._disableCookies = true\n  } else {\n    var targetCookieJar = jar.getCookieString ? jar : globalCookieJar\n    var urihref = self.uri.href\n    // fetch cookie in the Specified host\n    if (targetCookieJar) {\n      cookies = targetCookieJar.getCookieString(urihref)\n    }\n  }\n\n  // if need cookie and cookie is not empty\n  if (cookies && cookies.length) {\n    if (self.originalCookieHeader) {\n      // Don't overwrite existing Cookie header\n      self.setHeader('cookie', self.originalCookieHeader + '; ' + cookies)\n    } else {\n      self.setHeader('cookie', cookies)\n    }\n  }\n  self._jar = jar\n  return self\n}\n\n// Stream API\nRequest.prototype.pipe = function (dest, opts) {\n  var self = this\n\n  if (self.response) {\n    if (self._destdata) {\n      self.emit('error', new Error('You cannot pipe after data has been emitted from the response.'))\n    } else if (self._ended) {\n      self.emit('error', new Error('You cannot pipe after the response has been ended.'))\n    } else {\n      stream.Stream.prototype.pipe.call(self, dest, opts)\n      self.pipeDest(dest)\n      return dest\n    }\n  } else {\n    self.dests.push(dest)\n    stream.Stream.prototype.pipe.call(self, dest, opts)\n    return dest\n  }\n}\nRequest.prototype.write = function () {\n  var self = this\n  if (self._aborted) { return }\n\n  if (!self._started) {\n    self.start()\n  }\n  if (self.req) {\n    return self.req.write.apply(self.req, arguments)\n  }\n}\nRequest.prototype.end = function (chunk) {\n  var self = this\n  if (self._aborted) { return }\n\n  if (chunk) {\n    self.write(chunk)\n  }\n  if (!self._started) {\n    self.start()\n  }\n  if (self.req) {\n    self.req.end()\n  }\n}\nRequest.prototype.pause = function () {\n  var self = this\n  if (!self.responseContent) {\n    self._paused = true\n  } else {\n    self.responseContent.pause.apply(self.responseContent, arguments)\n  }\n}\nRequest.prototype.resume = function () {\n  var self = this\n  if (!self.responseContent) {\n    self._paused = false\n  } else {\n    self.responseContent.resume.apply(self.responseContent, arguments)\n  }\n}\nRequest.prototype.destroy = function () {\n  var self = this\n  this.clearTimeout()\n  if (!self._ended) {\n    self.end()\n  } else if (self.response) {\n    self.response.destroy()\n  }\n}\n\nRequest.prototype.clearTimeout = function () {\n  if (this.timeoutTimer) {\n    clearTimeout(this.timeoutTimer)\n    this.timeoutTimer = null\n  }\n}\n\nRequest.defaultProxyHeaderWhiteList =\n  Tunnel.defaultProxyHeaderWhiteList.slice()\n\nRequest.defaultProxyHeaderExclusiveList =\n  Tunnel.defaultProxyHeaderExclusiveList.slice()\n\n// Exports\n\nRequest.prototype.toJSON = requestToJSON\nmodule.exports = Request\n", "\n\nfunction formatHostname (hostname) {\n  // canonicalize the hostname, so that 'oogle.com' won't match 'google.com'\n  return hostname.replace(/^\\.*/, '.').toLowerCase()\n}\n\nfunction parseNoProxyZone (zone) {\n  zone = zone.trim().toLowerCase()\n\n  var zoneParts = zone.split(':', 2)\n  var zoneHost = formatHostname(zoneParts[0])\n  var zonePort = zoneParts[1]\n  var hasPort = zone.indexOf(':') > -1\n\n  return {hostname: zoneHost, port: zonePort, hasPort: hasPort}\n}\n\nfunction uriInNoProxy (uri, noProxy) {\n  var port = uri.port || (uri.protocol === 'https:' ? '443' : '80')\n  var hostname = formatHostname(uri.hostname)\n  var noProxyList = noProxy.split(',')\n\n  // iterate through the noProxyList until it finds a match.\n  return noProxyList.map(parseNoProxyZone).some(function (noProxyZone) {\n    var isMatchedAt = hostname.indexOf(noProxyZone.hostname)\n    var hostnameMatched = (\n      isMatchedAt > -1 &&\n        (isMatchedAt === hostname.length - noProxyZone.hostname.length)\n    )\n\n    if (noProxyZone.hasPort) {\n      return (port === noProxyZone.port) && hostnameMatched\n    }\n\n    return hostnameMatched\n  })\n}\n\nfunction getProxyFromURI (uri) {\n  // Decide the proper request proxy to use based on the request URI object and the\n  // environmental variables (NO_PROXY, HTTP_PROXY, etc.)\n  // respect NO_PROXY environment variables (see: https://lynx.invisible-island.net/lynx2.8.7/breakout/lynx_help/keystrokes/environments.html)\n\n  var noProxy = process.env.NO_PROXY || process.env.no_proxy || ''\n\n  // if the noProxy is a wildcard then return null\n\n  if (noProxy === '*') {\n    return null\n  }\n\n  // if the noProxy is not empty and the uri is found return null\n\n  if (noProxy !== '' && uriInNoProxy(uri, noProxy)) {\n    return null\n  }\n\n  // Check for HTTP or HTTPS Proxy in environment Else default to null\n\n  if (uri.protocol === 'http:') {\n    return process.env.HTTP_PROXY ||\n      process.env.http_proxy || null\n  }\n\n  if (uri.protocol === 'https:') {\n    return process.env.HTTPS_PROXY ||\n      process.env.https_proxy ||\n      process.env.HTTP_PROXY ||\n      process.env.http_proxy || null\n  }\n\n  // if none of that works, return null\n  // (What uri protocol are you using then?)\n\n  return null\n}\n\nmodule.exports = getProxyFromURI\n", "\n\nvar qs = require('qs')\nvar querystring = require('querystring')\n\nfunction Querystring (request) {\n  this.request = request\n  this.lib = null\n  this.useQuerystring = null\n  this.parseOptions = null\n  this.stringifyOptions = null\n}\n\nQuerystring.prototype.init = function (options) {\n  if (this.lib) { return }\n\n  this.useQuerystring = options.useQuerystring\n  this.lib = (this.useQuerystring ? querystring : qs)\n\n  this.parseOptions = options.qsParseOptions || {}\n  this.stringifyOptions = options.qsStringifyOptions || {}\n}\n\nQuerystring.prototype.stringify = function (obj) {\n  return (this.useQuerystring)\n    ? this.rfc3986(this.lib.stringify(obj,\n      this.stringifyOptions.sep || null,\n      this.stringifyOptions.eq || null,\n      this.stringifyOptions))\n    : this.lib.stringify(obj, this.stringifyOptions)\n}\n\nQuerystring.prototype.parse = function (str) {\n  return (this.useQuerystring)\n    ? this.lib.parse(str,\n      this.parseOptions.sep || null,\n      this.parseOptions.eq || null,\n      this.parseOptions)\n    : this.lib.parse(str, this.parseOptions)\n}\n\nQuerystring.prototype.rfc3986 = function (str) {\n  return str.replace(/[!'()*]/g, function (c) {\n    return '%' + c.charCodeAt(0).toString(16).toUpperCase()\n  })\n}\n\nQuerystring.prototype.unescape = querystring.unescape\n\nexports.Querystring = Querystring\n", "\n\nvar fs = require('fs')\nvar qs = require('querystring')\nvar validate = require('har-validator')\nvar extend = require('extend')\n\nfunction Har (request) {\n  this.request = request\n}\n\nHar.prototype.reducer = function (obj, pair) {\n  // new property ?\n  if (obj[pair.name] === undefined) {\n    obj[pair.name] = pair.value\n    return obj\n  }\n\n  // existing? convert to array\n  var arr = [\n    obj[pair.name],\n    pair.value\n  ]\n\n  obj[pair.name] = arr\n\n  return obj\n}\n\nHar.prototype.prep = function (data) {\n  // construct utility properties\n  data.queryObj = {}\n  data.headersObj = {}\n  data.postData.jsonObj = false\n  data.postData.paramsObj = false\n\n  // construct query objects\n  if (data.queryString && data.queryString.length) {\n    data.queryObj = data.queryString.reduce(this.reducer, {})\n  }\n\n  // construct headers objects\n  if (data.headers && data.headers.length) {\n    // loweCase header keys\n    data.headersObj = data.headers.reduceRight(function (headers, header) {\n      headers[header.name] = header.value\n      return headers\n    }, {})\n  }\n\n  // construct Cookie header\n  if (data.cookies && data.cookies.length) {\n    var cookies = data.cookies.map(function (cookie) {\n      return cookie.name + '=' + cookie.value\n    })\n\n    if (cookies.length) {\n      data.headersObj.cookie = cookies.join('; ')\n    }\n  }\n\n  // prep body\n  function some (arr) {\n    return arr.some(function (type) {\n      return data.postData.mimeType.indexOf(type) === 0\n    })\n  }\n\n  if (some([\n    'multipart/mixed',\n    'multipart/related',\n    'multipart/form-data',\n    'multipart/alternative'])) {\n    // reset values\n    data.postData.mimeType = 'multipart/form-data'\n  } else if (some([\n    'application/x-www-form-urlencoded'])) {\n    if (!data.postData.params) {\n      data.postData.text = ''\n    } else {\n      data.postData.paramsObj = data.postData.params.reduce(this.reducer, {})\n\n      // always overwrite\n      data.postData.text = qs.stringify(data.postData.paramsObj)\n    }\n  } else if (some([\n    'text/json',\n    'text/x-json',\n    'application/json',\n    'application/x-json'])) {\n    data.postData.mimeType = 'application/json'\n\n    if (data.postData.text) {\n      try {\n        data.postData.jsonObj = JSON.parse(data.postData.text)\n      } catch (e) {\n        this.request.debug(e)\n\n        // force back to text/plain\n        data.postData.mimeType = 'text/plain'\n      }\n    }\n  }\n\n  return data\n}\n\nHar.prototype.options = function (options) {\n  // skip if no har property defined\n  if (!options.har) {\n    return options\n  }\n\n  var har = {}\n  extend(har, options.har)\n\n  // only process the first entry\n  if (har.log && har.log.entries) {\n    har = har.log.entries[0]\n  }\n\n  // add optional properties to make validation successful\n  har.url = har.url || options.url || options.uri || options.baseUrl || '/'\n  har.httpVersion = har.httpVersion || 'HTTP/1.1'\n  har.queryString = har.queryString || []\n  har.headers = har.headers || []\n  har.cookies = har.cookies || []\n  har.postData = har.postData || {}\n  har.postData.mimeType = har.postData.mimeType || 'application/octet-stream'\n\n  har.bodySize = 0\n  har.headersSize = 0\n  har.postData.size = 0\n\n  if (!validate.request(har)) {\n    return options\n  }\n\n  // clean up and get some utility properties\n  var req = this.prep(har)\n\n  // construct new options\n  if (req.url) {\n    options.url = req.url\n  }\n\n  if (req.method) {\n    options.method = req.method\n  }\n\n  if (Object.keys(req.queryObj).length) {\n    options.qs = req.queryObj\n  }\n\n  if (Object.keys(req.headersObj).length) {\n    options.headers = req.headersObj\n  }\n\n  function test (type) {\n    return req.postData.mimeType.indexOf(type) === 0\n  }\n  if (test('application/x-www-form-urlencoded')) {\n    options.form = req.postData.paramsObj\n  } else if (test('application/json')) {\n    if (req.postData.jsonObj) {\n      options.body = req.postData.jsonObj\n      options.json = true\n    }\n  } else if (test('multipart/form-data')) {\n    options.formData = {}\n\n    req.postData.params.forEach(function (param) {\n      var attachment = {}\n\n      if (!param.fileName && !param.contentType) {\n        options.formData[param.name] = param.value\n        return\n      }\n\n      // attempt to read from disk!\n      if (param.fileName && !param.value) {\n        attachment.value = fs.createReadStream(param.fileName)\n      } else if (param.value) {\n        attachment.value = param.value\n      }\n\n      if (param.fileName) {\n        attachment.options = {\n          filename: param.fileName,\n          contentType: param.contentType ? param.contentType : null\n        }\n      }\n\n      options.formData[param.name] = attachment\n    })\n  } else {\n    if (req.postData.text) {\n      options.body = req.postData.text\n    }\n  }\n\n  return options\n}\n\nexports.Har = Har\n", "\n\nvar caseless = require('caseless')\nvar uuid = require('uuid/v4')\nvar helpers = require('./helpers')\n\nvar md5 = helpers.md5\nvar toBase64 = helpers.toBase64\n\nfunction Auth (request) {\n  // define all public properties here\n  this.request = request\n  this.hasAuth = false\n  this.sentAuth = false\n  this.bearerToken = null\n  this.user = null\n  this.pass = null\n}\n\nAuth.prototype.basic = function (user, pass, sendImmediately) {\n  var self = this\n  if (typeof user !== 'string' || (pass !== undefined && typeof pass !== 'string')) {\n    self.request.emit('error', new Error('auth() received invalid user or password'))\n  }\n  self.user = user\n  self.pass = pass\n  self.hasAuth = true\n  var header = user + ':' + (pass || '')\n  if (sendImmediately || typeof sendImmediately === 'undefined') {\n    var authHeader = 'Basic ' + toBase64(header)\n    self.sentAuth = true\n    return authHeader\n  }\n}\n\nAuth.prototype.bearer = function (bearer, sendImmediately) {\n  var self = this\n  self.bearerToken = bearer\n  self.hasAuth = true\n  if (sendImmediately || typeof sendImmediately === 'undefined') {\n    if (typeof bearer === 'function') {\n      bearer = bearer()\n    }\n    var authHeader = 'Bearer ' + (bearer || '')\n    self.sentAuth = true\n    return authHeader\n  }\n}\n\nAuth.prototype.digest = function (method, path, authHeader) {\n  // TODO: More complete implementation of RFC 2617.\n  //   - handle challenge.domain\n  //   - support qop=\"auth-int\" only\n  //   - handle Authentication-Info (not necessarily?)\n  //   - check challenge.stale (not necessarily?)\n  //   - increase nc (not necessarily?)\n  // For reference:\n  // http://tools.ietf.org/html/rfc2617#section-3\n  // https://github.com/bagder/curl/blob/master/lib/http_digest.c\n\n  var self = this\n\n  var challenge = {}\n  var re = /([a-z0-9_-]+)=(?:\"([^\"]+)\"|([a-z0-9_-]+))/gi\n  while (true) {\n    var match = re.exec(authHeader)\n    if (!match) {\n      break\n    }\n    challenge[match[1]] = match[2] || match[3]\n  }\n\n  /**\n   * RFC 2617: handle both MD5 and MD5-sess algorithms.\n   *\n   * If the algorithm directive's value is \"MD5\" or unspecified, then HA1 is\n   *   HA1=MD5(username:realm:password)\n   * If the algorithm directive's value is \"MD5-sess\", then HA1 is\n   *   HA1=MD5(MD5(username:realm:password):nonce:cnonce)\n   */\n  var ha1Compute = function (algorithm, user, realm, pass, nonce, cnonce) {\n    var ha1 = md5(user + ':' + realm + ':' + pass)\n    if (algorithm && algorithm.toLowerCase() === 'md5-sess') {\n      return md5(ha1 + ':' + nonce + ':' + cnonce)\n    } else {\n      return ha1\n    }\n  }\n\n  var qop = /(^|,)\\s*auth\\s*($|,)/.test(challenge.qop) && 'auth'\n  var nc = qop && '00000001'\n  var cnonce = qop && uuid().replace(/-/g, '')\n  var ha1 = ha1Compute(challenge.algorithm, self.user, challenge.realm, self.pass, challenge.nonce, cnonce)\n  var ha2 = md5(method + ':' + path)\n  var digestResponse = qop\n    ? md5(ha1 + ':' + challenge.nonce + ':' + nc + ':' + cnonce + ':' + qop + ':' + ha2)\n    : md5(ha1 + ':' + challenge.nonce + ':' + ha2)\n  var authValues = {\n    username: self.user,\n    realm: challenge.realm,\n    nonce: challenge.nonce,\n    uri: path,\n    qop: qop,\n    response: digestResponse,\n    nc: nc,\n    cnonce: cnonce,\n    algorithm: challenge.algorithm,\n    opaque: challenge.opaque\n  }\n\n  authHeader = []\n  for (var k in authValues) {\n    if (authValues[k]) {\n      if (k === 'qop' || k === 'nc' || k === 'algorithm') {\n        authHeader.push(k + '=' + authValues[k])\n      } else {\n        authHeader.push(k + '=\"' + authValues[k] + '\"')\n      }\n    }\n  }\n  authHeader = 'Digest ' + authHeader.join(', ')\n  self.sentAuth = true\n  return authHeader\n}\n\nAuth.prototype.onRequest = function (user, pass, sendImmediately, bearer) {\n  var self = this\n  var request = self.request\n\n  var authHeader\n  if (bearer === undefined && user === undefined) {\n    self.request.emit('error', new Error('no auth mechanism defined'))\n  } else if (bearer !== undefined) {\n    authHeader = self.bearer(bearer, sendImmediately)\n  } else {\n    authHeader = self.basic(user, pass, sendImmediately)\n  }\n  if (authHeader) {\n    request.setHeader('authorization', authHeader)\n  }\n}\n\nAuth.prototype.onResponse = function (response) {\n  var self = this\n  var request = self.request\n\n  if (!self.hasAuth || self.sentAuth) { return null }\n\n  var c = caseless(response.headers)\n\n  var authHeader = c.get('www-authenticate')\n  var authVerb = authHeader && authHeader.split(' ')[0].toLowerCase()\n  request.debug('reauth', authVerb)\n\n  switch (authVerb) {\n    case 'basic':\n      return self.basic(self.user, self.pass, true)\n\n    case 'bearer':\n      return self.bearer(self.bearerToken, true)\n\n    case 'digest':\n      return self.digest(request.method, request.path, authHeader)\n  }\n}\n\nexports.Auth = Auth\n", "\n\nvar url = require('url')\nvar qs = require('qs')\nvar caseless = require('caseless')\nvar uuid = require('uuid/v4')\nvar oauth = require('oauth-sign')\nvar crypto = require('crypto')\nvar Buffer = require('safe-buffer').Buffer\n\nfunction OAuth (request) {\n  this.request = request\n  this.params = null\n}\n\nOAuth.prototype.buildParams = function (_oauth, uri, method, query, form, qsLib) {\n  var oa = {}\n  for (var i in _oauth) {\n    oa['oauth_' + i] = _oauth[i]\n  }\n  if (!oa.oauth_version) {\n    oa.oauth_version = '1.0'\n  }\n  if (!oa.oauth_timestamp) {\n    oa.oauth_timestamp = Math.floor(Date.now() / 1000).toString()\n  }\n  if (!oa.oauth_nonce) {\n    oa.oauth_nonce = uuid().replace(/-/g, '')\n  }\n  if (!oa.oauth_signature_method) {\n    oa.oauth_signature_method = 'HMAC-SHA1'\n  }\n\n  var consumer_secret_or_private_key = oa.oauth_consumer_secret || oa.oauth_private_key // eslint-disable-line camelcase\n  delete oa.oauth_consumer_secret\n  delete oa.oauth_private_key\n\n  var token_secret = oa.oauth_token_secret // eslint-disable-line camelcase\n  delete oa.oauth_token_secret\n\n  var realm = oa.oauth_realm\n  delete oa.oauth_realm\n  delete oa.oauth_transport_method\n\n  var baseurl = uri.protocol + '//' + uri.host + uri.pathname\n  var params = qsLib.parse([].concat(query, form, qsLib.stringify(oa)).join('&'))\n\n  oa.oauth_signature = oauth.sign(\n    oa.oauth_signature_method,\n    method,\n    baseurl,\n    params,\n    consumer_secret_or_private_key, // eslint-disable-line camelcase\n    token_secret // eslint-disable-line camelcase\n  )\n\n  if (realm) {\n    oa.realm = realm\n  }\n\n  return oa\n}\n\nOAuth.prototype.buildBodyHash = function (_oauth, body) {\n  if (['HMAC-SHA1', 'RSA-SHA1'].indexOf(_oauth.signature_method || 'HMAC-SHA1') < 0) {\n    this.request.emit('error', new Error('oauth: ' + _oauth.signature_method +\n      ' signature_method not supported with body_hash signing.'))\n  }\n\n  var shasum = crypto.createHash('sha1')\n  shasum.update(body || '')\n  var sha1 = shasum.digest('hex')\n\n  return Buffer.from(sha1, 'hex').toString('base64')\n}\n\nOAuth.prototype.concatParams = function (oa, sep, wrap) {\n  wrap = wrap || ''\n\n  var params = Object.keys(oa).filter(function (i) {\n    return i !== 'realm' && i !== 'oauth_signature'\n  }).sort()\n\n  if (oa.realm) {\n    params.splice(0, 0, 'realm')\n  }\n  params.push('oauth_signature')\n\n  return params.map(function (i) {\n    return i + '=' + wrap + oauth.rfc3986(oa[i]) + wrap\n  }).join(sep)\n}\n\nOAuth.prototype.onRequest = function (_oauth) {\n  var self = this\n  self.params = _oauth\n\n  var uri = self.request.uri || {}\n  var method = self.request.method || ''\n  var headers = caseless(self.request.headers)\n  var body = self.request.body || ''\n  var qsLib = self.request.qsLib || qs\n\n  var form\n  var query\n  var contentType = headers.get('content-type') || ''\n  var formContentType = 'application/x-www-form-urlencoded'\n  var transport = _oauth.transport_method || 'header'\n\n  if (contentType.slice(0, formContentType.length) === formContentType) {\n    contentType = formContentType\n    form = body\n  }\n  if (uri.query) {\n    query = uri.query\n  }\n  if (transport === 'body' && (method !== 'POST' || contentType !== formContentType)) {\n    self.request.emit('error', new Error('oauth: transport_method of body requires POST ' +\n      'and content-type ' + formContentType))\n  }\n\n  if (!form && typeof _oauth.body_hash === 'boolean') {\n    _oauth.body_hash = self.buildBodyHash(_oauth, self.request.body.toString())\n  }\n\n  var oa = self.buildParams(_oauth, uri, method, query, form, qsLib)\n\n  switch (transport) {\n    case 'header':\n      self.request.setHeader('Authorization', 'OAuth ' + self.concatParams(oa, ',', '\"'))\n      break\n\n    case 'query':\n      var href = self.request.uri.href += (query ? '&' : '?') + self.concatParams(oa, '&')\n      self.request.uri = url.parse(href)\n      self.request.path = self.request.uri.path\n      break\n\n    case 'body':\n      self.request.body = (form ? form + '&' : '') + self.concatParams(oa, '&')\n      break\n\n    default:\n      self.request.emit('error', new Error('oauth: transport_method invalid'))\n  }\n}\n\nexports.OAuth = OAuth\n", "\n\nvar crypto = require('crypto')\n\nfunction randomString (size) {\n  var bits = (size + 1) * 6\n  var buffer = crypto.randomBytes(Math.ceil(bits / 8))\n  var string = buffer.toString('base64').replace(/\\+/g, '-').replace(/\\//g, '_').replace(/=/g, '')\n  return string.slice(0, size)\n}\n\nfunction calculatePayloadHash (payload, algorithm, contentType) {\n  var hash = crypto.createHash(algorithm)\n  hash.update('hawk.1.payload\\n')\n  hash.update((contentType ? contentType.split(';')[0].trim().toLowerCase() : '') + '\\n')\n  hash.update(payload || '')\n  hash.update('\\n')\n  return hash.digest('base64')\n}\n\nexports.calculateMac = function (credentials, opts) {\n  var normalized = 'hawk.1.header\\n' +\n    opts.ts + '\\n' +\n    opts.nonce + '\\n' +\n    (opts.method || '').toUpperCase() + '\\n' +\n    opts.resource + '\\n' +\n    opts.host.toLowerCase() + '\\n' +\n    opts.port + '\\n' +\n    (opts.hash || '') + '\\n'\n\n  if (opts.ext) {\n    normalized = normalized + opts.ext.replace('\\\\', '\\\\\\\\').replace('\\n', '\\\\n')\n  }\n\n  normalized = normalized + '\\n'\n\n  if (opts.app) {\n    normalized = normalized + opts.app + '\\n' + (opts.dlg || '') + '\\n'\n  }\n\n  var hmac = crypto.createHmac(credentials.algorithm, credentials.key).update(normalized)\n  var digest = hmac.digest('base64')\n  return digest\n}\n\nexports.header = function (uri, method, opts) {\n  var timestamp = opts.timestamp || Math.floor((Date.now() + (opts.localtimeOffsetMsec || 0)) / 1000)\n  var credentials = opts.credentials\n  if (!credentials || !credentials.id || !credentials.key || !credentials.algorithm) {\n    return ''\n  }\n\n  if (['sha1', 'sha256'].indexOf(credentials.algorithm) === -1) {\n    return ''\n  }\n\n  var artifacts = {\n    ts: timestamp,\n    nonce: opts.nonce || randomString(6),\n    method: method,\n    resource: uri.pathname + (uri.search || ''),\n    host: uri.hostname,\n    port: uri.port || (uri.protocol === 'http:' ? 80 : 443),\n    hash: opts.hash,\n    ext: opts.ext,\n    app: opts.app,\n    dlg: opts.dlg\n  }\n\n  if (!artifacts.hash && (opts.payload || opts.payload === '')) {\n    artifacts.hash = calculatePayloadHash(opts.payload, credentials.algorithm, opts.contentType)\n  }\n\n  var mac = exports.calculateMac(credentials, artifacts)\n\n  var hasExt = artifacts.ext !== null && artifacts.ext !== undefined && artifacts.ext !== ''\n  var header = 'Hawk id=\"' + credentials.id +\n    '\", ts=\"' + artifacts.ts +\n    '\", nonce=\"' + artifacts.nonce +\n    (artifacts.hash ? '\", hash=\"' + artifacts.hash : '') +\n    (hasExt ? '\", ext=\"' + artifacts.ext.replace(/\\\\/g, '\\\\\\\\').replace(/\"/g, '\\\\\"') : '') +\n    '\", mac=\"' + mac + '\"'\n\n  if (artifacts.app) {\n    header = header + ', app=\"' + artifacts.app + (artifacts.dlg ? '\", dlg=\"' + artifacts.dlg : '') + '\"'\n  }\n\n  return header\n}\n", "\n\nvar uuid = require('uuid/v4')\nvar CombinedStream = require('combined-stream')\nvar isstream = require('isstream')\nvar Buffer = require('safe-buffer').Buffer\n\nfunction Multipart (request) {\n  this.request = request\n  this.boundary = uuid()\n  this.chunked = false\n  this.body = null\n}\n\nMultipart.prototype.isChunked = function (options) {\n  var self = this\n  var chunked = false\n  var parts = options.data || options\n\n  if (!parts.forEach) {\n    self.request.emit('error', new Error('Argument error, options.multipart.'))\n  }\n\n  if (options.chunked !== undefined) {\n    chunked = options.chunked\n  }\n\n  if (self.request.getHeader('transfer-encoding') === 'chunked') {\n    chunked = true\n  }\n\n  if (!chunked) {\n    parts.forEach(function (part) {\n      if (typeof part.body === 'undefined') {\n        self.request.emit('error', new Error('Body attribute missing in multipart.'))\n      }\n      if (isstream(part.body)) {\n        chunked = true\n      }\n    })\n  }\n\n  return chunked\n}\n\nMultipart.prototype.setHeaders = function (chunked) {\n  var self = this\n\n  if (chunked && !self.request.hasHeader('transfer-encoding')) {\n    self.request.setHeader('transfer-encoding', 'chunked')\n  }\n\n  var header = self.request.getHeader('content-type')\n\n  if (!header || header.indexOf('multipart') === -1) {\n    self.request.setHeader('content-type', 'multipart/related; boundary=' + self.boundary)\n  } else {\n    if (header.indexOf('boundary') !== -1) {\n      self.boundary = header.replace(/.*boundary=([^\\s;]+).*/, '$1')\n    } else {\n      self.request.setHeader('content-type', header + '; boundary=' + self.boundary)\n    }\n  }\n}\n\nMultipart.prototype.build = function (parts, chunked) {\n  var self = this\n  var body = chunked ? new CombinedStream() : []\n\n  function add (part) {\n    if (typeof part === 'number') {\n      part = part.toString()\n    }\n    return chunked ? body.append(part) : body.push(Buffer.from(part))\n  }\n\n  if (self.request.preambleCRLF) {\n    add('\\r\\n')\n  }\n\n  parts.forEach(function (part) {\n    var preamble = '--' + self.boundary + '\\r\\n'\n    Object.keys(part).forEach(function (key) {\n      if (key === 'body') { return }\n      preamble += key + ': ' + part[key] + '\\r\\n'\n    })\n    preamble += '\\r\\n'\n    add(preamble)\n    add(part.body)\n    add('\\r\\n')\n  })\n  add('--' + self.boundary + '--')\n\n  if (self.request.postambleCRLF) {\n    add('\\r\\n')\n  }\n\n  return body\n}\n\nMultipart.prototype.onRequest = function (options) {\n  var self = this\n\n  var chunked = self.isChunked(options)\n  var parts = options.data || options\n\n  self.setHeaders(chunked)\n  self.chunked = chunked\n  self.body = self.build(parts, chunked)\n}\n\nexports.Multipart = Multipart\n", "\n\nvar url = require('url')\nvar isUrl = /^https?:/\n\nfunction Redirect (request) {\n  this.request = request\n  this.followRedirect = true\n  this.followRedirects = true\n  this.followAllRedirects = false\n  this.followOriginalHttpMethod = false\n  this.allowRedirect = function () { return true }\n  this.maxRedirects = 10\n  this.redirects = []\n  this.redirectsFollowed = 0\n  this.removeRefererHeader = false\n}\n\nRedirect.prototype.onRequest = function (options) {\n  var self = this\n\n  if (options.maxRedirects !== undefined) {\n    self.maxRedirects = options.maxRedirects\n  }\n  if (typeof options.followRedirect === 'function') {\n    self.allowRedirect = options.followRedirect\n  }\n  if (options.followRedirect !== undefined) {\n    self.followRedirects = !!options.followRedirect\n  }\n  if (options.followAllRedirects !== undefined) {\n    self.followAllRedirects = options.followAllRedirects\n  }\n  if (self.followRedirects || self.followAllRedirects) {\n    self.redirects = self.redirects || []\n  }\n  if (options.removeRefererHeader !== undefined) {\n    self.removeRefererHeader = options.removeRefererHeader\n  }\n  if (options.followOriginalHttpMethod !== undefined) {\n    self.followOriginalHttpMethod = options.followOriginalHttpMethod\n  }\n}\n\nRedirect.prototype.redirectTo = function (response) {\n  var self = this\n  var request = self.request\n\n  var redirectTo = null\n  if (response.statusCode >= 300 && response.statusCode < 400 && response.caseless.has('location')) {\n    var location = response.caseless.get('location')\n    request.debug('redirect', location)\n\n    if (self.followAllRedirects) {\n      redirectTo = location\n    } else if (self.followRedirects) {\n      switch (request.method) {\n        case 'PATCH':\n        case 'PUT':\n        case 'POST':\n        case 'DELETE':\n          // Do not follow redirects\n          break\n        default:\n          redirectTo = location\n          break\n      }\n    }\n  } else if (response.statusCode === 401) {\n    var authHeader = request._auth.onResponse(response)\n    if (authHeader) {\n      request.setHeader('authorization', authHeader)\n      redirectTo = request.uri\n    }\n  }\n  return redirectTo\n}\n\nRedirect.prototype.onResponse = function (response) {\n  var self = this\n  var request = self.request\n\n  var redirectTo = self.redirectTo(response)\n  if (!redirectTo || !self.allowRedirect.call(request, response)) {\n    return false\n  }\n\n  request.debug('redirect to', redirectTo)\n\n  // ignore any potential response body.  it cannot possibly be useful\n  // to us at this point.\n  // response.resume should be defined, but check anyway before calling. Workaround for browserify.\n  if (response.resume) {\n    response.resume()\n  }\n\n  if (self.redirectsFollowed >= self.maxRedirects) {\n    request.emit('error', new Error('Exceeded maxRedirects. Probably stuck in a redirect loop ' + request.uri.href))\n    return false\n  }\n  self.redirectsFollowed += 1\n\n  if (!isUrl.test(redirectTo)) {\n    redirectTo = url.resolve(request.uri.href, redirectTo)\n  }\n\n  var uriPrev = request.uri\n  request.uri = url.parse(redirectTo)\n\n  // handle the case where we change protocol from https to http or vice versa\n  if (request.uri.protocol !== uriPrev.protocol) {\n    delete request.agent\n  }\n\n  self.redirects.push({ statusCode: response.statusCode, redirectUri: redirectTo })\n\n  if (self.followAllRedirects && request.method !== 'HEAD' &&\n    response.statusCode !== 401 && response.statusCode !== 307) {\n    request.method = self.followOriginalHttpMethod ? request.method : 'GET'\n  }\n  // request.method = 'GET' // Force all redirects to use GET || commented out fixes #215\n  delete request.src\n  delete request.req\n  delete request._started\n  if (response.statusCode !== 401 && response.statusCode !== 307) {\n    // Remove parameters from the previous response, unless this is the second request\n    // for a server that requires digest authentication.\n    delete request.body\n    delete request._form\n    if (request.headers) {\n      request.removeHeader('host')\n      request.removeHeader('content-type')\n      request.removeHeader('content-length')\n      if (request.uri.hostname !== request.originalHost.split(':')[0]) {\n        // Remove authorization if changing hostnames (but not if just\n        // changing ports or protocols).  This matches the behavior of curl:\n        // https://github.com/bagder/curl/blob/6beb0eee/lib/http.c#L710\n        request.removeHeader('authorization')\n      }\n    }\n  }\n\n  if (!self.removeRefererHeader) {\n    request.setHeader('referer', uriPrev.href)\n  }\n\n  request.emit('redirect')\n\n  request.init()\n\n  return true\n}\n\nexports.Redirect = Redirect\n", "\n\nvar url = require('url')\nvar tunnel = require('tunnel-agent')\n\nvar defaultProxyHeaderWhiteList = [\n  'accept',\n  'accept-charset',\n  'accept-encoding',\n  'accept-language',\n  'accept-ranges',\n  'cache-control',\n  'content-encoding',\n  'content-language',\n  'content-location',\n  'content-md5',\n  'content-range',\n  'content-type',\n  'connection',\n  'date',\n  'expect',\n  'max-forwards',\n  'pragma',\n  'referer',\n  'te',\n  'user-agent',\n  'via'\n]\n\nvar defaultProxyHeaderExclusiveList = [\n  'proxy-authorization'\n]\n\nfunction constructProxyHost (uriObject) {\n  var port = uriObject.port\n  var protocol = uriObject.protocol\n  var proxyHost = uriObject.hostname + ':'\n\n  if (port) {\n    proxyHost += port\n  } else if (protocol === 'https:') {\n    proxyHost += '443'\n  } else {\n    proxyHost += '80'\n  }\n\n  return proxyHost\n}\n\nfunction constructProxyHeaderWhiteList (headers, proxyHeaderWhiteList) {\n  var whiteList = proxyHeaderWhiteList\n    .reduce(function (set, header) {\n      set[header.toLowerCase()] = true\n      return set\n    }, {})\n\n  return Object.keys(headers)\n    .filter(function (header) {\n      return whiteList[header.toLowerCase()]\n    })\n    .reduce(function (set, header) {\n      set[header] = headers[header]\n      return set\n    }, {})\n}\n\nfunction constructTunnelOptions (request, proxyHeaders) {\n  var proxy = request.proxy\n\n  var tunnelOptions = {\n    proxy: {\n      host: proxy.hostname,\n      port: +proxy.port,\n      proxyAuth: proxy.auth,\n      headers: proxyHeaders\n    },\n    headers: request.headers,\n    ca: request.ca,\n    cert: request.cert,\n    key: request.key,\n    passphrase: request.passphrase,\n    pfx: request.pfx,\n    ciphers: request.ciphers,\n    rejectUnauthorized: request.rejectUnauthorized,\n    secureOptions: request.secureOptions,\n    secureProtocol: request.secureProtocol\n  }\n\n  return tunnelOptions\n}\n\nfunction constructTunnelFnName (uri, proxy) {\n  var uriProtocol = (uri.protocol === 'https:' ? 'https' : 'http')\n  var proxyProtocol = (proxy.protocol === 'https:' ? 'Https' : 'Http')\n  return [uriProtocol, proxyProtocol].join('Over')\n}\n\nfunction getTunnelFn (request) {\n  var uri = request.uri\n  var proxy = request.proxy\n  var tunnelFnName = constructTunnelFnName(uri, proxy)\n  return tunnel[tunnelFnName]\n}\n\nfunction Tunnel (request) {\n  this.request = request\n  this.proxyHeaderWhiteList = defaultProxyHeaderWhiteList\n  this.proxyHeaderExclusiveList = []\n  if (typeof request.tunnel !== 'undefined') {\n    this.tunnelOverride = request.tunnel\n  }\n}\n\nTunnel.prototype.isEnabled = function () {\n  var self = this\n  var request = self.request\n    // Tunnel HTTPS by default. Allow the user to override this setting.\n\n  // If self.tunnelOverride is set (the user specified a value), use it.\n  if (typeof self.tunnelOverride !== 'undefined') {\n    return self.tunnelOverride\n  }\n\n  // If the destination is HTTPS, tunnel.\n  if (request.uri.protocol === 'https:') {\n    return true\n  }\n\n  // Otherwise, do not use tunnel.\n  return false\n}\n\nTunnel.prototype.setup = function (options) {\n  var self = this\n  var request = self.request\n\n  options = options || {}\n\n  if (typeof request.proxy === 'string') {\n    request.proxy = url.parse(request.proxy)\n  }\n\n  if (!request.proxy || !request.tunnel) {\n    return false\n  }\n\n  // Setup Proxy Header Exclusive List and White List\n  if (options.proxyHeaderWhiteList) {\n    self.proxyHeaderWhiteList = options.proxyHeaderWhiteList\n  }\n  if (options.proxyHeaderExclusiveList) {\n    self.proxyHeaderExclusiveList = options.proxyHeaderExclusiveList\n  }\n\n  var proxyHeaderExclusiveList = self.proxyHeaderExclusiveList.concat(defaultProxyHeaderExclusiveList)\n  var proxyHeaderWhiteList = self.proxyHeaderWhiteList.concat(proxyHeaderExclusiveList)\n\n  // Setup Proxy Headers and Proxy Headers Host\n  // Only send the Proxy White Listed Header names\n  var proxyHeaders = constructProxyHeaderWhiteList(request.headers, proxyHeaderWhiteList)\n  proxyHeaders.host = constructProxyHost(request.uri)\n\n  proxyHeaderExclusiveList.forEach(request.removeHeader, request)\n\n  // Set Agent from Tunnel Data\n  var tunnelFn = getTunnelFn(request)\n  var tunnelOptions = constructTunnelOptions(request, proxyHeaders)\n  request.agent = tunnelFn(tunnelOptions)\n\n  return true\n}\n\nTunnel.defaultProxyHeaderWhiteList = defaultProxyHeaderWhiteList\nTunnel.defaultProxyHeaderExclusiveList = defaultProxyHeaderExclusiveList\nexports.Tunnel = Tunnel\n"]}