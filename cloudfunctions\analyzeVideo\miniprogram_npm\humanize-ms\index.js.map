{"version": 3, "sources": ["index.js"], "names": [], "mappings": ";;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.js", "sourcesContent": ["/*!\n * humanize-ms - index.js\n * Copyright(c) 2014 dead_horse <<EMAIL>>\n * MIT Licensed\n */\n\n\n\n/**\n * Module dependencies.\n */\n\nvar util = require('util');\nvar ms = require('ms');\n\nmodule.exports = function (t) {\n  if (typeof t === 'number') return t;\n  var r = ms(t);\n  if (r === undefined) {\n    var err = new Error(util.format('humanize-ms(%j) result undefined', t));\n    console.warn(err.stack);\n  }\n  return r;\n};\n"]}