{"version": 3, "sources": ["parse-headers.js"], "names": [], "mappings": ";;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.js", "sourcesContent": ["var trim = function(string) {\n  return string.replace(/^\\s+|\\s+$/g, '');\n}\n  , isArray = function(arg) {\n      return Object.prototype.toString.call(arg) === '[object Array]';\n    }\n\nmodule.exports = function (headers) {\n  if (!headers)\n    return {}\n\n  var result = Object.create(null);\n\n  var headersArr = trim(headers).split('\\n')\n\n  for (var i = 0; i < headersArr.length; i++) {\n    var row = headersArr[i]\n    var index = row.indexOf(':')\n    , key = trim(row.slice(0, index)).toLowerCase()\n    , value = trim(row.slice(index + 1))\n\n    if (typeof(result[key]) === 'undefined') {\n      result[key] = value\n    } else if (isArray(result[key])) {\n      result[key].push(value)\n    } else {\n      result[key] = [ result[key], value ]\n    }\n  }\n\n  return result\n}\n"]}