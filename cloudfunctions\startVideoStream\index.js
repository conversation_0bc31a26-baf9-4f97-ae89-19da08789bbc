// 云函数入口文件
const cloud = require('wx-server-sdk')
const request = require('request-promise-native')  // 添加HTTP请求库

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})

const db = cloud.database()

// 定义支持的视频流格式和路径
const STREAM_FORMATS = [
  { path: '/video_stream', type: 'http-flv' },
  { path: '/live', type: 'http-flv' },
  { path: '/live.flv', type: 'http-flv' },
  { path: '/stream', type: 'http-flv' },
  { path: '/hls/live.m3u8', type: 'hls' },
  { path: '/live/stream.m3u8', type: 'hls' },
  { path: '/stream.m3u8', type: 'hls' },
  { path: '/live.ts', type: 'mpeg-ts' },          // 🆕 MPEG-TS流
  { path: '/stream.ts', type: 'mpeg-ts' },        // 🆕 MPEG-TS流
  { path: '/video.ts', type: 'mpeg-ts' },         // 🆕 MPEG-TS流
  { path: '/mpeg/stream.mpg', type: 'mpeg-ps' },  // 🆕 MPEG-PS流
  { path: '/stream.mpg', type: 'mpeg-ps' },       // 🆕 MPEG-PS流
  { path: '/mjpg/video.mjpg', type: 'mjpeg' },    // 🆕 MJPEG流
  { path: '/video.mjpg', type: 'mjpeg' },         // 🆕 常见MJPEG路径
  { path: '/mjpeg', type: 'mjpeg' },              // 🆕 简化MJPEG路径
  { path: '/video', type: 'raw-h264' },
  { path: '/media/video.mp4', type: 'mp4' },
];

// 云函数入口函数
exports.main = async (event, context) => {
  const wxContext = cloud.getWXContext()
  const openid = wxContext.OPENID

  try {
    const { deviceId, isLocalVideo, videoFileId, deviceIp } = event  // 添加deviceIp参数

    // 如果是本地视频
    if (isLocalVideo && videoFileId) {
      // 获取视频文件的临时访问链接
      const result = await cloud.getTempFileURL({
        fileList: [videoFileId]
      })

      if (result.fileList && result.fileList[0].tempFileURL) {
        return {
          success: true,
          data: {
            streamUrl: result.fileList[0].tempFileURL,
            streamType: 'local',
            deviceId: 'local'
          }
        }
      }
      throw new Error('获取视频链接失败')
    }

    // 如果是实时视频流
    // 验证设备是否存在且属于当前用户
    let deviceInfo;
    try {
      const deviceResult = await db.collection('devices').doc(deviceId).get()
      deviceInfo = deviceResult.data;
      
      if (!deviceInfo || deviceInfo.owner !== openid) {
        // 如果没有找到设备记录或不是当前用户的设备
        if (deviceId === 'default_device') {
          // 对于默认设备，创建一个测试设备记录
          deviceInfo = {
            id: 'default_device',
            name: '测试设备',
            status: 'active',
            owner: openid,
            streamConfig: {
              protocol: 'http',  // 修改为HTTP协议
              server: deviceIp || 'default'  // 使用提供的设备IP
            }
          };
        } else {
          throw new Error('设备不存在或无权限');
        }
      }
    } catch (deviceError) {
      console.error('获取设备信息失败：', deviceError);
      // 如果是默认设备，创建一个测试设备记录
      if (deviceId === 'default_device') {
        deviceInfo = {
          id: 'default_device',
          name: '测试设备',
          status: 'active',
          owner: openid,
          streamConfig: {
            protocol: 'http',  // 修改为HTTP协议
            server: deviceIp || 'default'  // 使用提供的设备IP
          }
        };
      } else {
        throw new Error('设备不存在或无权限访问');
      }
    }

    // 启动实际的视频流
    const streamInfo = await startDeviceStream(deviceInfo);
    
    // 记录流媒体会话
    const sessionDoc = await db.collection('streamSessions').add({
      data: {
        deviceId: deviceId,
        userId: openid,
        pushUrl: streamInfo.pushUrl,
        startTime: new Date(),
        streamUrl: streamInfo.streamUrl,
        streamType: streamInfo.streamType,
        status: 'active',
        deviceIp: deviceIp || null  // 记录设备IP
      }
    });

    return {
      success: true,
      data: {
        pushUrl: streamInfo.pushUrl,
        streamUrl: streamInfo.streamUrl,
        streamType: streamInfo.streamType,
        deviceId: deviceId,
        sessionId: sessionDoc._id || streamInfo.sessionId || null
      }
    }
  } catch (error) {
    console.error('启动视频流失败:', error)
    return {
      success: false,
      error: error.message || '启动视频流失败'
    }
  }
}

// 启动设备视频流
async function startDeviceStream(deviceInfo) {
  // 生成设备推流地址
  const pushUrl = generatePushUrl(deviceInfo);
  const sessionId = `stream_${Date.now()}`;
  
  if (deviceInfo.streamConfig && deviceInfo.streamConfig.protocol === 'http') {
    // 处理HTTP视频流
    const deviceIp = deviceInfo.streamConfig.server;
    if (deviceIp && deviceIp !== 'default') {
      // 自动检测支持的视频流格式
      const detectedStream = await detectStreamFormat(deviceIp);
      if (detectedStream) {
        return {
          pushUrl: pushUrl,
          streamUrl: detectedStream.url,
          streamType: detectedStream.type,
          sessionId: sessionId
        };
      }
      
      // 如果自动检测失败，尝试默认路径
      const httpStreamUrl = `http://${deviceIp}/video_stream`;
      
      try {
        // 检查设备视频流是否可访问
        await request.head(httpStreamUrl, { timeout: 3000 });
        
        return {
          pushUrl: pushUrl,
          streamUrl: httpStreamUrl,
          streamType: 'http-flv',  // 假设使用http-flv格式
          sessionId: sessionId
        };
      } catch (error) {
        console.error('设备视频流检查失败:', error);
        // 失败时返回默认测试视频
        return {
          pushUrl: pushUrl,
          streamUrl: 'https://test-videos.co.uk/vids/bigbuckbunny/mp4/h264/720/Big_Buck_Bunny_720_10s_1MB.mp4',
          streamType: 'mp4',
          sessionId: sessionId
        };
      }
    }
  }

  // 默认情况或RTMP流
  if (deviceInfo.streamConfig && deviceInfo.streamConfig.protocol === 'rtmp') {
    return {
      pushUrl: pushUrl,
      streamUrl: `https://example.com/live/stream/${deviceInfo.id}.m3u8?token=${Date.now()}`,
      streamType: 'hls',
      sessionId: sessionId
    };
  } else {
    // 默认测试视频
    return {
      pushUrl: pushUrl,
      streamUrl: 'https://test-videos.co.uk/vids/bigbuckbunny/mp4/h264/720/Big_Buck_Bunny_720_10s_1MB.mp4',
      streamType: 'mp4',
      sessionId: sessionId
    };
  }
}

// 自动检测设备支持的视频流格式
async function detectStreamFormat(deviceIp) {
  console.log(`开始检测设备 ${deviceIp} 支持的视频流格式...`);
  
  // 使用定义的流格式数组来检测
  for (const format of STREAM_FORMATS) {
    const streamUrl = `http://${deviceIp}${format.path}`;
    console.log(`测试URL: ${streamUrl}, 格式: ${format.type}`);
    
    try {
      // 设置请求选项，短超时以加快检测速度
      const options = {
        uri: streamUrl,
        method: 'HEAD',
        timeout: 2000,
        resolveWithFullResponse: true // 返回完整响应以检查状态码
      };
      
      const response = await request(options);
      
      // 检查响应状态码
      if (response.statusCode >= 200 && response.statusCode < 400) {
        console.log(`成功检测到视频流格式: ${format.type}, URL: ${streamUrl}`);
        
        // 根据Content-Type进一步确认格式
        const contentType = response.headers['content-type'] || '';
        console.log(`Content-Type: ${contentType}`);
        
        let confirmedType = format.type;
        
        // 根据Content-Type调整格式类型
        if (contentType.includes('video/mp4')) {
          confirmedType = 'mp4';
        } else if (contentType.includes('application/vnd.apple.mpegurl') || contentType.includes('application/x-mpegurl')) {
          confirmedType = 'hls';
        } else if (contentType.includes('video/x-flv')) {
          confirmedType = 'http-flv';
        } else if (contentType.includes('multipart/x-mixed-replace')) {
          confirmedType = 'mjpeg';
        } else if (contentType.includes('video/mp2t') || contentType.includes('video/MP2T')) {
          confirmedType = 'mpeg-ts';                    // 🆕 MPEG-TS格式
        } else if (contentType.includes('video/mpeg') || contentType.includes('video/mpg')) {
          confirmedType = 'mpeg-ps';                    // 🆕 MPEG-PS格式
        }
        
        // 返回检测到的流信息
        return {
          url: streamUrl,
          type: confirmedType,
          contentType: contentType
        };
      }
    } catch (error) {
      // 忽略错误，继续测试下一个格式
      console.log(`格式 ${format.type} 不可用: ${error.message || '未知错误'}`);
    }
  }
  
  // 如果没有检测到任何可用格式，返回null
  console.log('没有检测到支持的视频流格式');
  return null;
}

// 生成设备推流地址
function generatePushUrl(deviceInfo) {
  const token = Math.random().toString(36).substr(2, 16);
  
  if (deviceInfo.streamConfig && deviceInfo.streamConfig.protocol === 'http') {
    // HTTP推流地址
    return `http://${deviceInfo.streamConfig.server}/push?token=${token}&deviceId=${deviceInfo.id}`;
  } else {
    // RTMP推流地址
    return `rtmp://example.com/live/${deviceInfo.id}?token=${token}&t=${Date.now()}&session=${deviceInfo.id}`;
  }
}
