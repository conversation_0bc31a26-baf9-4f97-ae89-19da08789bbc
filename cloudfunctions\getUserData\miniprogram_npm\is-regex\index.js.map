{"version": 3, "sources": ["index.js"], "names": [], "mappings": ";;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.js", "sourcesContent": ["\n\nvar callBound = require('call-bound');\nvar hasToStringTag = require('has-tostringtag/shams')();\nvar hasOwn = require('hasown');\nvar gOPD = require('gopd');\n\n/** @type {import('.')} */\nvar fn;\n\nif (hasToStringTag) {\n\t/** @type {(receiver: ThisParameterType<typeof RegExp.prototype.exec>, ...args: Parameters<typeof RegExp.prototype.exec>) => ReturnType<typeof RegExp.prototype.exec>} */\n\tvar $exec = callBound('RegExp.prototype.exec');\n\t/** @type {object} */\n\tvar isRegexMarker = {};\n\n\tvar throwRegexMarker = function () {\n\t\tthrow isRegexMarker;\n\t};\n\t/** @type {{ toString(): never, valueOf(): never, [Symbol.toPrimitive]?(): never }} */\n\tvar badStringifier = {\n\t\ttoString: throwRegexMarker,\n\t\tvalueOf: throwRegexMarker\n\t};\n\n\tif (typeof Symbol.toPrimitive === 'symbol') {\n\t\tbadStringifier[Symbol.toPrimitive] = throwRegexMarker;\n\t}\n\n\t/** @type {import('.')} */\n\t// @ts-expect-error TS can't figure out that the $exec call always throws\n\t// eslint-disable-next-line consistent-return\n\tfn = function isRegex(value) {\n\t\tif (!value || typeof value !== 'object') {\n\t\t\treturn false;\n\t\t}\n\n\t\t// eslint-disable-next-line no-extra-parens\n\t\tvar descriptor = /** @type {NonNullable<typeof gOPD>} */ (gOPD)(/** @type {{ lastIndex?: unknown }} */ (value), 'lastIndex');\n\t\tvar hasLastIndexDataProperty = descriptor && hasOwn(descriptor, 'value');\n\t\tif (!hasLastIndexDataProperty) {\n\t\t\treturn false;\n\t\t}\n\n\t\ttry {\n\t\t\t// eslint-disable-next-line no-extra-parens\n\t\t\t$exec(value, /** @type {string} */ (/** @type {unknown} */ (badStringifier)));\n\t\t} catch (e) {\n\t\t\treturn e === isRegexMarker;\n\t\t}\n\t};\n} else {\n\t/** @type {(receiver: ThisParameterType<typeof Object.prototype.toString>, ...args: Parameters<typeof Object.prototype.toString>) => ReturnType<typeof Object.prototype.toString>} */\n\tvar $toString = callBound('Object.prototype.toString');\n\t/** @const @type {'[object RegExp]'} */\n\tvar regexClass = '[object RegExp]';\n\n\t/** @type {import('.')} */\n\tfn = function isRegex(value) {\n\t\t// In older browsers, typeof regex incorrectly returns 'function'\n\t\tif (!value || (typeof value !== 'object' && typeof value !== 'function')) {\n\t\t\treturn false;\n\t\t}\n\n\t\treturn $toString(value) === regexClass;\n\t};\n}\n\nmodule.exports = fn;\n"]}