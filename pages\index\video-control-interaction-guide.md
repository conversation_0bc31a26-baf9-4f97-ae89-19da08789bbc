# 🎬 视频播放控制交互指南

## 📋 交互逻辑说明

### 🎯 **点击行为定义**
基于主流移动设备交互标准，我们采用以下时间间隔：

- **单击与双击区分**：300ms间隔
- **双击与三击区分**：500ms间隔
- **进度条自动隐藏**：1.5秒无操作后自动隐藏

### 🎮 **具体交互行为**

#### 1️⃣ **单击（300ms内无后续点击）**
- **功能**：显示/隐藏进度条
- **动画效果**：
  - 显示：从下到上缓慢向上出现
  - 隐藏：从上到下缓慢隐入
- **逻辑**：
  - 如果进度条当前隐藏 → 显示进度条
  - 如果进度条当前显示 → 隐藏进度条

#### 2️⃣ **双击（300ms内连续点击2次）**
- **功能**：播放/暂停视频
- **逻辑**：
  - 如果视频正在播放 → 暂停播放 + 显示进度条
  - 如果视频暂停/未播放 → 开始播放 + 启动自动隐藏

#### 3️⃣ **三击（500ms内连续点击3次）**
- **功能**：进入/退出全屏模式
- **逻辑**：
  - 如果当前非全屏 → 进入全屏模式
  - 如果当前全屏 → 退出全屏模式

### ⏰ **自动隐藏机制**

#### 📱 **播放状态下**
- 进度条显示1.5秒后自动隐藏
- 用户操作进度条时重置定时器

#### ⏸️ **暂停状态下**
- 进度条始终显示
- 不启动自动隐藏定时器

#### 🚫 **未播放状态**
- 进度条始终显示
- 按暂停状态处理

### 🎨 **动画效果**

#### 📈 **显示动画（从下到上）**
```css
@keyframes progressBarSlideUp {
  0% {
    transform: translateY(20rpx);
    opacity: 0.7;
  }
  50% {
    transform: translateY(10rpx);
    opacity: 0.85;
  }
  100% {
    transform: translateY(0);
    opacity: 1;
  }
}
```

#### 📉 **隐藏动画（从上到下）**
- 使用CSS transition实现平滑过渡
- 动画时长：300ms
- 缓动函数：ease-in

### 🔧 **技术实现要点**

#### 🎯 **点击检测逻辑**
1. 记录每次点击的时间戳
2. 根据时间间隔判断点击类型
3. 使用setTimeout延迟执行，等待可能的后续点击

#### 🎬 **动画状态管理**
1. `progressBarAnimating`：控制显示动画
2. `showVideoControls`：控制进度条显示/隐藏
3. CSS类动态绑定实现动画效果

#### ⏱️ **定时器管理**
1. `_tapDelayTimer`：点击延迟检测定时器
2. `_progressBarHideTimer`：进度条自动隐藏定时器
3. 确保定时器正确清理，避免内存泄漏

### 📝 **使用示例**

#### 🎮 **用户操作流程**
1. **首次加载**：进度条显示（视频未播放状态）
2. **单击屏幕**：进度条隐藏（从上到下动画）
3. **再次单击**：进度条显示（从下到上动画）
4. **双击屏幕**：开始播放视频，1.5秒后进度条自动隐藏
5. **双击屏幕**：暂停播放，进度条显示并保持
6. **三击屏幕**：进入全屏模式

#### 🔄 **状态转换**
```
未播放 → 双击 → 播放中（1.5s后自动隐藏进度条）
播放中 → 双击 → 暂停（进度条显示）
任意状态 → 单击 → 切换进度条显示/隐藏
任意状态 → 三击 → 切换全屏/窗口模式
```

### ✅ **兼容性说明**

- ✅ 支持微信小程序video组件
- ✅ 支持本地视频和网络视频
- ✅ 支持录制模式下的特殊处理
- ✅ 支持直播流的播放控制
- ✅ 响应式设计，适配不同屏幕尺寸

### 🚨 **注意事项**

1. **录制模式下**：所有点击事件被禁用
2. **视频未加载**：点击事件不响应
3. **动画冲突**：确保动画完成后再执行下一个动画
4. **内存管理**：页面销毁时清理所有定时器

### 🎯 **优化建议**

1. **性能优化**：使用CSS3硬件加速
2. **用户体验**：提供视觉反馈和触觉反馈
3. **可访问性**：支持键盘导航和屏幕阅读器
4. **错误处理**：优雅处理视频加载失败等异常情况
