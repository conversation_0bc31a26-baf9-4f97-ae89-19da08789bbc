{"version": 3, "sources": ["xml2js.js", "defaults.js", "builder.js", "parser.js", "bom.js", "processors.js"], "names": [], "mappings": ";;;;;;;AAAA;AACA;AACA;ACFA,ADGA;AC<PERSON>,ADGA;ACFA,ADGA;AELA,ADGA,ADGA;AELA,ADGA,ADGA;AELA,ADGA,ADGA;AELA,ADGA,AENA,AHSA;AELA,ADGA,AENA,AHSA;AELA,ADGA,AENA,AHSA;AIXA,AFMA,ADGA,AENA,AHSA;AIXA,AFMA,ADGA,AENA,AHSA;AIXA,AFMA,ADGA,AENA,AHSA;AIXA,AFMA,ADGA,AENA,AENA,ALeA;AIXA,AFMA,ADGA,AENA,AENA,ALeA;AIXA,AFMA,ADGA,AENA,AENA,ALeA;AIXA,AFMA,ADGA,AENA,AENA,ALeA;AIXA,AFMA,ADGA,AENA,AENA,ALeA;AIXA,AFMA,ADGA,AENA,AENA,ALeA;AIXA,AFMA,ADGA,AENA,AENA,ALeA;AIXA,AFMA,ADGA,AENA,AENA,ALeA;AIXA,AFMA,ADGA,AENA,AENA,ALeA;AIXA,AFMA,ADGA,AENA,AENA,ALeA;AELA,ADGA,AENA,AENA,ALeA;AELA,ADGA,AENA,AENA,ALeA;AELA,ADGA,AENA,AENA,ALeA;AELA,ADGA,AENA,AENA,ALeA;AELA,ADGA,AENA,AENA,ALeA;AELA,ADGA,AENA,AENA,ALeA;AELA,ADGA,AENA,AENA,ALeA;AELA,ADGA,AENA,AENA,ALeA;AELA,ADGA,AENA,AENA,ALeA;AELA,ADGA,AENA,AENA,ALeA;AELA,ADGA,AENA,AENA,ALeA;AELA,ADGA,AENA,AENA,ALeA;AELA,ADGA,AENA,AENA,ALeA;AELA,ADGA,AENA,AENA,ALeA;AELA,ADGA,AENA,AENA,ALeA;AELA,ADGA,AENA,AENA;AHUA,ADGA,AENA,AENA;AHUA,ADGA,AENA,AENA;AHUA,ADGA,AENA,AENA;AHUA,ADGA,AENA,AENA;AHUA,ADGA,AENA,AENA;AHUA,ADGA,AENA,AENA;AHUA,ADGA,AENA,AENA;AHUA,ADGA,AENA,AENA;AHUA,ADGA,AENA,AENA;AHUA,ADGA,AENA;ADIA,ADGA,AENA;ADIA,ADGA,AENA;ADIA,ADGA,AENA;ADIA,ADGA,AENA;ADIA,ADGA,AENA;ADIA,ADGA,AENA;ADIA,ADGA,AENA;ADIA,ADGA,AENA;ADIA,ADGA,AENA;ADIA,ADGA,AENA;ADIA,ADGA,AENA;ADIA,ADGA,AENA;ADIA,ADGA,AENA;ADIA,ADGA,AENA;ADIA,ADGA,AENA;ADIA,ADGA,AENA;ADIA,ADGA,AENA;ADIA,ADGA,AENA;ADIA,ADGA,AENA;ADIA,ADGA,AENA;ADIA,ADGA,AENA;ADIA,ADGA,AENA;ADIA,ADGA,AENA;ADIA,ADGA,AENA;ADIA,ADGA,AENA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.js", "sourcesContent": ["// Generated by CoffeeScript 1.12.7\n(function() {\n  \n  var builder, defaults, parser, processors,\n    extend = function(child, parent) { for (var key in parent) { if (hasProp.call(parent, key)) child[key] = parent[key]; } function ctor() { this.constructor = child; } ctor.prototype = parent.prototype; child.prototype = new ctor(); child.__super__ = parent.prototype; return child; },\n    hasProp = {}.hasOwnProperty;\n\n  defaults = require('./defaults');\n\n  builder = require('./builder');\n\n  parser = require('./parser');\n\n  processors = require('./processors');\n\n  exports.defaults = defaults.defaults;\n\n  exports.processors = processors;\n\n  exports.ValidationError = (function(superClass) {\n    extend(ValidationError, superClass);\n\n    function ValidationError(message) {\n      this.message = message;\n    }\n\n    return ValidationError;\n\n  })(Error);\n\n  exports.Builder = builder.Builder;\n\n  exports.Parser = parser.Parser;\n\n  exports.parseString = parser.parseString;\n\n  exports.parseStringPromise = parser.parseStringPromise;\n\n}).call(this);\n", "// Generated by CoffeeScript 1.12.7\n(function() {\n  exports.defaults = {\n    \"0.1\": {\n      explicitCharkey: false,\n      trim: true,\n      normalize: true,\n      normalizeTags: false,\n      attrkey: \"@\",\n      charkey: \"#\",\n      explicitArray: false,\n      ignoreAttrs: false,\n      mergeAttrs: false,\n      explicitRoot: false,\n      validator: null,\n      xmlns: false,\n      explicitChildren: false,\n      childkey: '@@',\n      charsAsChildren: false,\n      includeWhiteChars: false,\n      async: false,\n      strict: true,\n      attrNameProcessors: null,\n      attrValueProcessors: null,\n      tagNameProcessors: null,\n      valueProcessors: null,\n      emptyTag: ''\n    },\n    \"0.2\": {\n      explicitCharkey: false,\n      trim: false,\n      normalize: false,\n      normalizeTags: false,\n      attrkey: \"$\",\n      charkey: \"_\",\n      explicitArray: true,\n      ignoreAttrs: false,\n      mergeAttrs: false,\n      explicitRoot: true,\n      validator: null,\n      xmlns: false,\n      explicitChildren: false,\n      preserveChildrenOrder: false,\n      childkey: '$$',\n      charsAsChildren: false,\n      includeWhiteChars: false,\n      async: false,\n      strict: true,\n      attrNameProcessors: null,\n      attrValueProcessors: null,\n      tagNameProcessors: null,\n      valueProcessors: null,\n      rootName: 'root',\n      xmldec: {\n        'version': '1.0',\n        'encoding': 'UTF-8',\n        'standalone': true\n      },\n      doctype: null,\n      renderOpts: {\n        'pretty': true,\n        'indent': '  ',\n        'newline': '\\n'\n      },\n      headless: false,\n      chunkSize: 10000,\n      emptyTag: '',\n      cdata: false\n    }\n  };\n\n}).call(this);\n", "// Generated by CoffeeScript 1.12.7\n(function() {\n  \n  var builder, defaults, escapeCDATA, requiresCDATA, wrapCDATA,\n    hasProp = {}.hasOwnProperty;\n\n  builder = require('xmlbuilder');\n\n  defaults = require('./defaults').defaults;\n\n  requiresCDATA = function(entry) {\n    return typeof entry === \"string\" && (entry.indexOf('&') >= 0 || entry.indexOf('>') >= 0 || entry.indexOf('<') >= 0);\n  };\n\n  wrapCDATA = function(entry) {\n    return \"<![CDATA[\" + (escapeCDATA(entry)) + \"]]>\";\n  };\n\n  escapeCDATA = function(entry) {\n    return entry.replace(']]>', ']]]]><![CDATA[>');\n  };\n\n  exports.Builder = (function() {\n    function Builder(opts) {\n      var key, ref, value;\n      this.options = {};\n      ref = defaults[\"0.2\"];\n      for (key in ref) {\n        if (!hasProp.call(ref, key)) continue;\n        value = ref[key];\n        this.options[key] = value;\n      }\n      for (key in opts) {\n        if (!hasProp.call(opts, key)) continue;\n        value = opts[key];\n        this.options[key] = value;\n      }\n    }\n\n    Builder.prototype.buildObject = function(rootObj) {\n      var attrkey, charkey, render, rootElement, rootName;\n      attrkey = this.options.attrkey;\n      charkey = this.options.charkey;\n      if ((Object.keys(rootObj).length === 1) && (this.options.rootName === defaults['0.2'].rootName)) {\n        rootName = Object.keys(rootObj)[0];\n        rootObj = rootObj[rootName];\n      } else {\n        rootName = this.options.rootName;\n      }\n      render = (function(_this) {\n        return function(element, obj) {\n          var attr, child, entry, index, key, value;\n          if (typeof obj !== 'object') {\n            if (_this.options.cdata && requiresCDATA(obj)) {\n              element.raw(wrapCDATA(obj));\n            } else {\n              element.txt(obj);\n            }\n          } else if (Array.isArray(obj)) {\n            for (index in obj) {\n              if (!hasProp.call(obj, index)) continue;\n              child = obj[index];\n              for (key in child) {\n                entry = child[key];\n                element = render(element.ele(key), entry).up();\n              }\n            }\n          } else {\n            for (key in obj) {\n              if (!hasProp.call(obj, key)) continue;\n              child = obj[key];\n              if (key === attrkey) {\n                if (typeof child === \"object\") {\n                  for (attr in child) {\n                    value = child[attr];\n                    element = element.att(attr, value);\n                  }\n                }\n              } else if (key === charkey) {\n                if (_this.options.cdata && requiresCDATA(child)) {\n                  element = element.raw(wrapCDATA(child));\n                } else {\n                  element = element.txt(child);\n                }\n              } else if (Array.isArray(child)) {\n                for (index in child) {\n                  if (!hasProp.call(child, index)) continue;\n                  entry = child[index];\n                  if (typeof entry === 'string') {\n                    if (_this.options.cdata && requiresCDATA(entry)) {\n                      element = element.ele(key).raw(wrapCDATA(entry)).up();\n                    } else {\n                      element = element.ele(key, entry).up();\n                    }\n                  } else {\n                    element = render(element.ele(key), entry).up();\n                  }\n                }\n              } else if (typeof child === \"object\") {\n                element = render(element.ele(key), child).up();\n              } else {\n                if (typeof child === 'string' && _this.options.cdata && requiresCDATA(child)) {\n                  element = element.ele(key).raw(wrapCDATA(child)).up();\n                } else {\n                  if (child == null) {\n                    child = '';\n                  }\n                  element = element.ele(key, child.toString()).up();\n                }\n              }\n            }\n          }\n          return element;\n        };\n      })(this);\n      rootElement = builder.create(rootName, this.options.xmldec, this.options.doctype, {\n        headless: this.options.headless,\n        allowSurrogateChars: this.options.allowSurrogateChars\n      });\n      return render(rootElement, rootObj).end(this.options.renderOpts);\n    };\n\n    return Builder;\n\n  })();\n\n}).call(this);\n", "// Generated by CoffeeScript 1.12.7\n(function() {\n  \n  var bom, defaults, events, isEmpty, processItem, processors, sax, setImmediate,\n    bind = function(fn, me){ return function(){ return fn.apply(me, arguments); }; },\n    extend = function(child, parent) { for (var key in parent) { if (hasProp.call(parent, key)) child[key] = parent[key]; } function ctor() { this.constructor = child; } ctor.prototype = parent.prototype; child.prototype = new ctor(); child.__super__ = parent.prototype; return child; },\n    hasProp = {}.hasOwnProperty;\n\n  sax = require('sax');\n\n  events = require('events');\n\n  bom = require('./bom');\n\n  processors = require('./processors');\n\n  setImmediate = require('timers').setImmediate;\n\n  defaults = require('./defaults').defaults;\n\n  isEmpty = function(thing) {\n    return typeof thing === \"object\" && (thing != null) && Object.keys(thing).length === 0;\n  };\n\n  processItem = function(processors, item, key) {\n    var i, len, process;\n    for (i = 0, len = processors.length; i < len; i++) {\n      process = processors[i];\n      item = process(item, key);\n    }\n    return item;\n  };\n\n  exports.Parser = (function(superClass) {\n    extend(Parser, superClass);\n\n    function Parser(opts) {\n      this.parseStringPromise = bind(this.parseStringPromise, this);\n      this.parseString = bind(this.parseString, this);\n      this.reset = bind(this.reset, this);\n      this.assignOrPush = bind(this.assignOrPush, this);\n      this.processAsync = bind(this.processAsync, this);\n      var key, ref, value;\n      if (!(this instanceof exports.Parser)) {\n        return new exports.Parser(opts);\n      }\n      this.options = {};\n      ref = defaults[\"0.2\"];\n      for (key in ref) {\n        if (!hasProp.call(ref, key)) continue;\n        value = ref[key];\n        this.options[key] = value;\n      }\n      for (key in opts) {\n        if (!hasProp.call(opts, key)) continue;\n        value = opts[key];\n        this.options[key] = value;\n      }\n      if (this.options.xmlns) {\n        this.options.xmlnskey = this.options.attrkey + \"ns\";\n      }\n      if (this.options.normalizeTags) {\n        if (!this.options.tagNameProcessors) {\n          this.options.tagNameProcessors = [];\n        }\n        this.options.tagNameProcessors.unshift(processors.normalize);\n      }\n      this.reset();\n    }\n\n    Parser.prototype.processAsync = function() {\n      var chunk, err;\n      try {\n        if (this.remaining.length <= this.options.chunkSize) {\n          chunk = this.remaining;\n          this.remaining = '';\n          this.saxParser = this.saxParser.write(chunk);\n          return this.saxParser.close();\n        } else {\n          chunk = this.remaining.substr(0, this.options.chunkSize);\n          this.remaining = this.remaining.substr(this.options.chunkSize, this.remaining.length);\n          this.saxParser = this.saxParser.write(chunk);\n          return setImmediate(this.processAsync);\n        }\n      } catch (error1) {\n        err = error1;\n        if (!this.saxParser.errThrown) {\n          this.saxParser.errThrown = true;\n          return this.emit(err);\n        }\n      }\n    };\n\n    Parser.prototype.assignOrPush = function(obj, key, newValue) {\n      if (!(key in obj)) {\n        if (!this.options.explicitArray) {\n          return obj[key] = newValue;\n        } else {\n          return obj[key] = [newValue];\n        }\n      } else {\n        if (!(obj[key] instanceof Array)) {\n          obj[key] = [obj[key]];\n        }\n        return obj[key].push(newValue);\n      }\n    };\n\n    Parser.prototype.reset = function() {\n      var attrkey, charkey, ontext, stack;\n      this.removeAllListeners();\n      this.saxParser = sax.parser(this.options.strict, {\n        trim: false,\n        normalize: false,\n        xmlns: this.options.xmlns\n      });\n      this.saxParser.errThrown = false;\n      this.saxParser.onerror = (function(_this) {\n        return function(error) {\n          _this.saxParser.resume();\n          if (!_this.saxParser.errThrown) {\n            _this.saxParser.errThrown = true;\n            return _this.emit(\"error\", error);\n          }\n        };\n      })(this);\n      this.saxParser.onend = (function(_this) {\n        return function() {\n          if (!_this.saxParser.ended) {\n            _this.saxParser.ended = true;\n            return _this.emit(\"end\", _this.resultObject);\n          }\n        };\n      })(this);\n      this.saxParser.ended = false;\n      this.EXPLICIT_CHARKEY = this.options.explicitCharkey;\n      this.resultObject = null;\n      stack = [];\n      attrkey = this.options.attrkey;\n      charkey = this.options.charkey;\n      this.saxParser.onopentag = (function(_this) {\n        return function(node) {\n          var key, newValue, obj, processedKey, ref;\n          obj = {};\n          obj[charkey] = \"\";\n          if (!_this.options.ignoreAttrs) {\n            ref = node.attributes;\n            for (key in ref) {\n              if (!hasProp.call(ref, key)) continue;\n              if (!(attrkey in obj) && !_this.options.mergeAttrs) {\n                obj[attrkey] = {};\n              }\n              newValue = _this.options.attrValueProcessors ? processItem(_this.options.attrValueProcessors, node.attributes[key], key) : node.attributes[key];\n              processedKey = _this.options.attrNameProcessors ? processItem(_this.options.attrNameProcessors, key) : key;\n              if (_this.options.mergeAttrs) {\n                _this.assignOrPush(obj, processedKey, newValue);\n              } else {\n                obj[attrkey][processedKey] = newValue;\n              }\n            }\n          }\n          obj[\"#name\"] = _this.options.tagNameProcessors ? processItem(_this.options.tagNameProcessors, node.name) : node.name;\n          if (_this.options.xmlns) {\n            obj[_this.options.xmlnskey] = {\n              uri: node.uri,\n              local: node.local\n            };\n          }\n          return stack.push(obj);\n        };\n      })(this);\n      this.saxParser.onclosetag = (function(_this) {\n        return function() {\n          var cdata, emptyStr, key, node, nodeName, obj, objClone, old, s, xpath;\n          obj = stack.pop();\n          nodeName = obj[\"#name\"];\n          if (!_this.options.explicitChildren || !_this.options.preserveChildrenOrder) {\n            delete obj[\"#name\"];\n          }\n          if (obj.cdata === true) {\n            cdata = obj.cdata;\n            delete obj.cdata;\n          }\n          s = stack[stack.length - 1];\n          if (obj[charkey].match(/^\\s*$/) && !cdata) {\n            emptyStr = obj[charkey];\n            delete obj[charkey];\n          } else {\n            if (_this.options.trim) {\n              obj[charkey] = obj[charkey].trim();\n            }\n            if (_this.options.normalize) {\n              obj[charkey] = obj[charkey].replace(/\\s{2,}/g, \" \").trim();\n            }\n            obj[charkey] = _this.options.valueProcessors ? processItem(_this.options.valueProcessors, obj[charkey], nodeName) : obj[charkey];\n            if (Object.keys(obj).length === 1 && charkey in obj && !_this.EXPLICIT_CHARKEY) {\n              obj = obj[charkey];\n            }\n          }\n          if (isEmpty(obj)) {\n            obj = _this.options.emptyTag !== '' ? _this.options.emptyTag : emptyStr;\n          }\n          if (_this.options.validator != null) {\n            xpath = \"/\" + ((function() {\n              var i, len, results;\n              results = [];\n              for (i = 0, len = stack.length; i < len; i++) {\n                node = stack[i];\n                results.push(node[\"#name\"]);\n              }\n              return results;\n            })()).concat(nodeName).join(\"/\");\n            (function() {\n              var err;\n              try {\n                return obj = _this.options.validator(xpath, s && s[nodeName], obj);\n              } catch (error1) {\n                err = error1;\n                return _this.emit(\"error\", err);\n              }\n            })();\n          }\n          if (_this.options.explicitChildren && !_this.options.mergeAttrs && typeof obj === 'object') {\n            if (!_this.options.preserveChildrenOrder) {\n              node = {};\n              if (_this.options.attrkey in obj) {\n                node[_this.options.attrkey] = obj[_this.options.attrkey];\n                delete obj[_this.options.attrkey];\n              }\n              if (!_this.options.charsAsChildren && _this.options.charkey in obj) {\n                node[_this.options.charkey] = obj[_this.options.charkey];\n                delete obj[_this.options.charkey];\n              }\n              if (Object.getOwnPropertyNames(obj).length > 0) {\n                node[_this.options.childkey] = obj;\n              }\n              obj = node;\n            } else if (s) {\n              s[_this.options.childkey] = s[_this.options.childkey] || [];\n              objClone = {};\n              for (key in obj) {\n                if (!hasProp.call(obj, key)) continue;\n                objClone[key] = obj[key];\n              }\n              s[_this.options.childkey].push(objClone);\n              delete obj[\"#name\"];\n              if (Object.keys(obj).length === 1 && charkey in obj && !_this.EXPLICIT_CHARKEY) {\n                obj = obj[charkey];\n              }\n            }\n          }\n          if (stack.length > 0) {\n            return _this.assignOrPush(s, nodeName, obj);\n          } else {\n            if (_this.options.explicitRoot) {\n              old = obj;\n              obj = {};\n              obj[nodeName] = old;\n            }\n            _this.resultObject = obj;\n            _this.saxParser.ended = true;\n            return _this.emit(\"end\", _this.resultObject);\n          }\n        };\n      })(this);\n      ontext = (function(_this) {\n        return function(text) {\n          var charChild, s;\n          s = stack[stack.length - 1];\n          if (s) {\n            s[charkey] += text;\n            if (_this.options.explicitChildren && _this.options.preserveChildrenOrder && _this.options.charsAsChildren && (_this.options.includeWhiteChars || text.replace(/\\\\n/g, '').trim() !== '')) {\n              s[_this.options.childkey] = s[_this.options.childkey] || [];\n              charChild = {\n                '#name': '__text__'\n              };\n              charChild[charkey] = text;\n              if (_this.options.normalize) {\n                charChild[charkey] = charChild[charkey].replace(/\\s{2,}/g, \" \").trim();\n              }\n              s[_this.options.childkey].push(charChild);\n            }\n            return s;\n          }\n        };\n      })(this);\n      this.saxParser.ontext = ontext;\n      return this.saxParser.oncdata = (function(_this) {\n        return function(text) {\n          var s;\n          s = ontext(text);\n          if (s) {\n            return s.cdata = true;\n          }\n        };\n      })(this);\n    };\n\n    Parser.prototype.parseString = function(str, cb) {\n      var err;\n      if ((cb != null) && typeof cb === \"function\") {\n        this.on(\"end\", function(result) {\n          this.reset();\n          return cb(null, result);\n        });\n        this.on(\"error\", function(err) {\n          this.reset();\n          return cb(err);\n        });\n      }\n      try {\n        str = str.toString();\n        if (str.trim() === '') {\n          this.emit(\"end\", null);\n          return true;\n        }\n        str = bom.stripBOM(str);\n        if (this.options.async) {\n          this.remaining = str;\n          setImmediate(this.processAsync);\n          return this.saxParser;\n        }\n        return this.saxParser.write(str).close();\n      } catch (error1) {\n        err = error1;\n        if (!(this.saxParser.errThrown || this.saxParser.ended)) {\n          this.emit('error', err);\n          return this.saxParser.errThrown = true;\n        } else if (this.saxParser.ended) {\n          throw err;\n        }\n      }\n    };\n\n    Parser.prototype.parseStringPromise = function(str) {\n      return new Promise((function(_this) {\n        return function(resolve, reject) {\n          return _this.parseString(str, function(err, value) {\n            if (err) {\n              return reject(err);\n            } else {\n              return resolve(value);\n            }\n          });\n        };\n      })(this));\n    };\n\n    return Parser;\n\n  })(events);\n\n  exports.parseString = function(str, a, b) {\n    var cb, options, parser;\n    if (b != null) {\n      if (typeof b === 'function') {\n        cb = b;\n      }\n      if (typeof a === 'object') {\n        options = a;\n      }\n    } else {\n      if (typeof a === 'function') {\n        cb = a;\n      }\n      options = {};\n    }\n    parser = new exports.Parser(options);\n    return parser.parseString(str, cb);\n  };\n\n  exports.parseStringPromise = function(str, a) {\n    var options, parser;\n    if (typeof a === 'object') {\n      options = a;\n    }\n    parser = new exports.Parser(options);\n    return parser.parseStringPromise(str);\n  };\n\n}).call(this);\n", "// Generated by CoffeeScript 1.12.7\n(function() {\n  \n  exports.stripBOM = function(str) {\n    if (str[0] === '\\uFEFF') {\n      return str.substring(1);\n    } else {\n      return str;\n    }\n  };\n\n}).call(this);\n", "// Generated by CoffeeScript 1.12.7\n(function() {\n  \n  var prefixMatch;\n\n  prefixMatch = new RegExp(/(?!xmlns)^.*:/);\n\n  exports.normalize = function(str) {\n    return str.toLowerCase();\n  };\n\n  exports.firstCharLowerCase = function(str) {\n    return str.charAt(0).toLowerCase() + str.slice(1);\n  };\n\n  exports.stripPrefix = function(str) {\n    return str.replace(prefixMatch, '');\n  };\n\n  exports.parseNumbers = function(str) {\n    if (!isNaN(str)) {\n      str = str % 1 === 0 ? parseInt(str, 10) : parseFloat(str);\n    }\n    return str;\n  };\n\n  exports.parseBooleans = function(str) {\n    if (/^(?:true|false)$/i.test(str)) {\n      str = str.toLowerCase() === 'true';\n    }\n    return str;\n  };\n\n}).call(this);\n"]}