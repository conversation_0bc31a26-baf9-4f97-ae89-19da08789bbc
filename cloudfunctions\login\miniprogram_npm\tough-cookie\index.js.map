{"version": 3, "sources": ["cookie.js", "pubsuffix-psl.js", "store.js", "memstore.js", "permuteDomain.js", "pathMatch.js", "version.js"], "names": [], "mappings": ";;;;;;;AAAA;AACA;AACA;AACA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA,ACHA;AFOA,ACHA,ACHA;AFOA,ACHA,ACHA;AFOA,AGTA,AFMA,ACHA;AFOA,AGTA,AFMA,ACHA;AFOA,AGTA,AFMA,ACHA;AFOA,AGTA,ACHA,AHSA,ACHA;AFOA,AGTA,ACHA,AHSA,ACHA;AFOA,AGTA,ACHA,AHSA,ACHA;AFOA,AGTA,AENA,ADGA,AHSA,ACHA;AFOA,AGTA,AENA,ADGA,AHSA,ACHA;AFOA,AGTA,AENA,ADGA,AHSA,ACHA;AFOA,AGTA,AENA,ADGA,AHSA,ACHA,AIZA;ANmBA,AGTA,AENA,ADGA,AHSA,ACHA,AIZA;ANmBA,AGTA,AENA,ADGA,AHSA,ACHA,AIZA;ANmBA,AGTA,AENA,ADGA,AHSA,ACHA;AFOA,AGTA,AENA,ADGA,AHSA,ACHA;AFOA,AGTA,AENA,ADGA,AHSA,ACHA;AFOA,AGTA,AENA,ADGA,AHSA,ACHA;AFOA,AGTA,AENA,ADGA,AHSA,ACHA;AFOA,AGTA,AENA,ADGA,AHSA,ACHA;AFOA,AGTA,AENA,ADGA,AHSA,ACHA;AFOA,AGTA,AENA,ADGA,AHSA,ACHA;AFOA,AGTA,AENA,ADGA,AHSA,ACHA;AFOA,AGTA,AENA,ADGA,AHSA,ACHA;AFOA,AGTA,AENA,ADGA,AHSA,ACHA;AFOA,AGTA,AENA,ADGA,AHSA,ACHA;AFOA,AGTA,AENA,ADGA,AHSA,ACHA;AFOA,AGTA,AENA,ADGA,AHSA,ACHA;AFOA,AGTA,AENA,ADGA,AHSA,ACHA;AFOA,AGTA,AENA,ADGA,AHSA,ACHA;AFOA,AGTA,AENA,ADGA,AHSA,ACHA;AFOA,AGTA,AENA,ADGA,AHSA,ACHA;AFOA,AGTA,AENA,ADGA,AHSA,ACHA;AFOA,AGTA,AENA,ADGA,AHSA,ACHA;AFOA,AGTA,AENA,ADGA,AHSA,ACHA;AFOA,AGTA,AENA,ADGA,AFMA;AFOA,AGTA,AENA,ADGA,AFMA;AFOA,AGTA,AENA,ADGA,AFMA;AFOA,AGTA,AENA,ADGA,AFMA;AFOA,AGTA,AENA,ADGA,AFMA;AFOA,AGTA,AENA,ADGA,AFMA;AFOA,AGTA,AENA,ADGA,AFMA;AFOA,AGTA,AENA,ADGA,AFMA;AFOA,AGTA,AENA,ADGA,AFMA;AFOA,AGTA,AENA,ADGA,AFMA;AFOA,AGTA,AENA,ADGA,AFMA;AFOA,AGTA,AENA,ADGA,AFMA;AFOA,AGTA,AENA,ADGA,AFMA;AFOA,AGTA,AENA,ADGA,AFMA;AFOA,AGTA,AENA,ADGA,AFMA;AFOA,AGTA,AENA,ADGA,AFMA;AFOA,AGTA,AENA,ADGA,AFMA;AFOA,AGTA,AENA,ADGA,AFMA;AFOA,AGTA,AENA,ADGA,AFMA;AFOA,AGTA,AENA,ADGA,AFMA;AFOA,AGTA,AENA,ADGA,AFMA;AFOA,AGTA,AENA,ADGA,AFMA;AFOA,AGTA,AENA,ADGA,AFMA;AFOA,AGTA,AENA,ADGA,AFMA;AFOA,AGTA,AENA,ADGA,AFMA;AFOA,AGTA,AENA,ADGA,AFMA;AFOA,AGTA,AENA,ADGA,AFMA;AFOA,AGTA,AENA,AHSA;AFOA,AGTA,AENA,AHSA;AFOA,AGTA,AENA,AHSA;AFOA,AGTA,AENA,AHSA;AFOA,AGTA,AENA,AHSA;AFOA,AGTA,AENA,AHSA;AFOA,AGTA,AENA,AHSA;AFOA,AGTA,AENA,AHSA;AFOA,AGTA,ADGA;AFOA,AGTA,ADGA;AFOA,AGTA,ADGA;AFOA,AGTA,ADGA;AFOA,AGTA,ADGA;AFOA,AGTA;AHUA,AGTA;AHUA,AGTA;AHUA,AGTA;AHUA,AGTA;AHUA,AGTA;AHUA,AGTA;AHUA,AGTA;AHUA,AGTA;AHUA,AGTA;AHUA,AGTA;AHUA,AGTA;AHUA,AGTA;AHUA,AGTA;AHUA,AGTA;AHUA,AGTA;AHUA,AGTA;AHUA,AGTA;AHUA,AGTA;AHUA,AGTA;AHUA,AGTA;AHUA,AGTA;AHUA,AGTA;AHUA,AGTA;AHUA,AGTA;AHUA,AGTA;AHUA,AGTA;AHUA,AGTA;AHUA,AGTA;AHUA,AGTA;AHUA,AGTA;AHUA,AGTA;AHUA,AGTA;AHUA,AGTA;AHUA,AGTA;AHUA,AGTA;AHUA,AGTA;AHUA,AGTA;AHUA,AGTA;AHUA,AGTA;AHUA,AGTA;AHUA,AGTA;AHUA,AGTA;AHUA,AGTA;AHUA,AGTA;AHUA,AGTA;AHUA,AGTA;AHUA,AGTA;AHUA,AGTA;AHUA,AGTA;AHUA,AGTA;AHUA,AGTA;AHUA,AGTA;AHUA,AGTA;AHUA,AGTA;AHUA,AGTA;AHUA,AGTA;AHUA,AGTA;AHUA,AGTA;AHUA,AGTA;AHUA,AGTA;AHUA,AGTA;AHUA,AGTA;AHUA,AGTA;AHUA,AGTA;AHUA,AGTA;AHUA,AGTA;AHUA,AGTA;AHUA,AGTA;AHUA,AGTA;AHUA,AGTA;AHUA,AGTA;AHUA,AGTA;AHUA,AGTA;AHUA,AGTA;AHUA,AGTA;AHUA,AGTA;AHUA,AGTA;AHUA,AGTA;AHUA,AGTA;AHUA,AGTA;AHUA,AGTA;AHUA,AGTA;AHUA,AGTA;AHUA,AGTA;AHUA,AGTA;AHUA,AGTA;AHUA,AGTA;AHUA,AGTA;AHUA,AGTA;AHUA,AGTA;AHUA,AGTA;AHUA,AGTA;AHUA,AGTA;AHUA,AGTA;AHUA,AGTA;AHUA,AGTA;AHUA,AGTA;AHUA,AGTA;AHUA,AGTA;AHUA,AGTA;AHUA,AGTA;AHUA,AGTA;AHUA,AGTA;AHUA,AGTA;AHUA,AGTA;AHUA,AGTA;AHUA,AGTA;AHUA,AGTA;AHUA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.js", "sourcesContent": ["/*!\n * Copyright (c) 2015, Salesforce.com, Inc.\n * All rights reserved.\n *\n * Redistribution and use in source and binary forms, with or without\n * modification, are permitted provided that the following conditions are met:\n *\n * 1. Redistributions of source code must retain the above copyright notice,\n * this list of conditions and the following disclaimer.\n *\n * 2. Redistributions in binary form must reproduce the above copyright notice,\n * this list of conditions and the following disclaimer in the documentation\n * and/or other materials provided with the distribution.\n *\n * 3. Neither the name of Salesforce.com nor the names of its contributors may\n * be used to endorse or promote products derived from this software without\n * specific prior written permission.\n *\n * THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS \"AS IS\"\n * AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE\n * IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE\n * ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE\n * LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, <PERSON>ECIAL, EXEMPLARY, OR\n * CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF\n * SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS\n * INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN\n * CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)\n * ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE\n * POSSIBILITY OF SUCH DAMAGE.\n */\n\nvar net = require('net');\nvar urlParse = require('url').parse;\nvar util = require('util');\nvar pubsuffix = require('./pubsuffix-psl');\nvar Store = require('./store').Store;\nvar MemoryCookieStore = require('./memstore').MemoryCookieStore;\nvar pathMatch = require('./pathMatch').pathMatch;\nvar VERSION = require('./version');\n\nvar punycode;\ntry {\n  punycode = require('punycode');\n} catch(e) {\n  console.warn(\"tough-cookie: can't load punycode; won't use punycode for domain normalization\");\n}\n\n// From RFC6265 S4.1.1\n// note that it excludes \\x3B \";\"\nvar COOKIE_OCTETS = /^[\\x21\\x23-\\x2B\\x2D-\\x3A\\x3C-\\x5B\\x5D-\\x7E]+$/;\n\nvar CONTROL_CHARS = /[\\x00-\\x1F]/;\n\n// From Chromium // '\\r', '\\n' and '\\0' should be treated as a terminator in\n// the \"relaxed\" mode, see:\n// https://github.com/ChromiumWebApps/chromium/blob/b3d3b4da8bb94c1b2e061600df106d590fda3620/net/cookies/parsed_cookie.cc#L60\nvar TERMINATORS = ['\\n', '\\r', '\\0'];\n\n// RFC6265 S4.1.1 defines path value as 'any CHAR except CTLs or \";\"'\n// Note ';' is \\x3B\nvar PATH_VALUE = /[\\x20-\\x3A\\x3C-\\x7E]+/;\n\n// date-time parsing constants (RFC6265 S5.1.1)\n\nvar DATE_DELIM = /[\\x09\\x20-\\x2F\\x3B-\\x40\\x5B-\\x60\\x7B-\\x7E]/;\n\nvar MONTH_TO_NUM = {\n  jan:0, feb:1, mar:2, apr:3, may:4, jun:5,\n  jul:6, aug:7, sep:8, oct:9, nov:10, dec:11\n};\nvar NUM_TO_MONTH = [\n  'Jan','Feb','Mar','Apr','May','Jun','Jul','Aug','Sep','Oct','Nov','Dec'\n];\nvar NUM_TO_DAY = [\n  'Sun','Mon','Tue','Wed','Thu','Fri','Sat'\n];\n\nvar MAX_TIME = 2147483647000; // 31-bit max\nvar MIN_TIME = 0; // 31-bit min\n\n/*\n * Parses a Natural number (i.e., non-negative integer) with either the\n *    <min>*<max>DIGIT ( non-digit *OCTET )\n * or\n *    <min>*<max>DIGIT\n * grammar (RFC6265 S5.1.1).\n *\n * The \"trailingOK\" boolean controls if the grammar accepts a\n * \"( non-digit *OCTET )\" trailer.\n */\nfunction parseDigits(token, minDigits, maxDigits, trailingOK) {\n  var count = 0;\n  while (count < token.length) {\n    var c = token.charCodeAt(count);\n    // \"non-digit = %x00-2F / %x3A-FF\"\n    if (c <= 0x2F || c >= 0x3A) {\n      break;\n    }\n    count++;\n  }\n\n  // constrain to a minimum and maximum number of digits.\n  if (count < minDigits || count > maxDigits) {\n    return null;\n  }\n\n  if (!trailingOK && count != token.length) {\n    return null;\n  }\n\n  return parseInt(token.substr(0,count), 10);\n}\n\nfunction parseTime(token) {\n  var parts = token.split(':');\n  var result = [0,0,0];\n\n  /* RF6256 S5.1.1:\n   *      time            = hms-time ( non-digit *OCTET )\n   *      hms-time        = time-field \":\" time-field \":\" time-field\n   *      time-field      = 1*2DIGIT\n   */\n\n  if (parts.length !== 3) {\n    return null;\n  }\n\n  for (var i = 0; i < 3; i++) {\n    // \"time-field\" must be strictly \"1*2DIGIT\", HOWEVER, \"hms-time\" can be\n    // followed by \"( non-digit *OCTET )\" so therefore the last time-field can\n    // have a trailer\n    var trailingOK = (i == 2);\n    var num = parseDigits(parts[i], 1, 2, trailingOK);\n    if (num === null) {\n      return null;\n    }\n    result[i] = num;\n  }\n\n  return result;\n}\n\nfunction parseMonth(token) {\n  token = String(token).substr(0,3).toLowerCase();\n  var num = MONTH_TO_NUM[token];\n  return num >= 0 ? num : null;\n}\n\n/*\n * RFC6265 S5.1.1 date parser (see RFC for full grammar)\n */\nfunction parseDate(str) {\n  if (!str) {\n    return;\n  }\n\n  /* RFC6265 S5.1.1:\n   * 2. Process each date-token sequentially in the order the date-tokens\n   * appear in the cookie-date\n   */\n  var tokens = str.split(DATE_DELIM);\n  if (!tokens) {\n    return;\n  }\n\n  var hour = null;\n  var minute = null;\n  var second = null;\n  var dayOfMonth = null;\n  var month = null;\n  var year = null;\n\n  for (var i=0; i<tokens.length; i++) {\n    var token = tokens[i].trim();\n    if (!token.length) {\n      continue;\n    }\n\n    var result;\n\n    /* 2.1. If the found-time flag is not set and the token matches the time\n     * production, set the found-time flag and set the hour- value,\n     * minute-value, and second-value to the numbers denoted by the digits in\n     * the date-token, respectively.  Skip the remaining sub-steps and continue\n     * to the next date-token.\n     */\n    if (second === null) {\n      result = parseTime(token);\n      if (result) {\n        hour = result[0];\n        minute = result[1];\n        second = result[2];\n        continue;\n      }\n    }\n\n    /* 2.2. If the found-day-of-month flag is not set and the date-token matches\n     * the day-of-month production, set the found-day-of- month flag and set\n     * the day-of-month-value to the number denoted by the date-token.  Skip\n     * the remaining sub-steps and continue to the next date-token.\n     */\n    if (dayOfMonth === null) {\n      // \"day-of-month = 1*2DIGIT ( non-digit *OCTET )\"\n      result = parseDigits(token, 1, 2, true);\n      if (result !== null) {\n        dayOfMonth = result;\n        continue;\n      }\n    }\n\n    /* 2.3. If the found-month flag is not set and the date-token matches the\n     * month production, set the found-month flag and set the month-value to\n     * the month denoted by the date-token.  Skip the remaining sub-steps and\n     * continue to the next date-token.\n     */\n    if (month === null) {\n      result = parseMonth(token);\n      if (result !== null) {\n        month = result;\n        continue;\n      }\n    }\n\n    /* 2.4. If the found-year flag is not set and the date-token matches the\n     * year production, set the found-year flag and set the year-value to the\n     * number denoted by the date-token.  Skip the remaining sub-steps and\n     * continue to the next date-token.\n     */\n    if (year === null) {\n      // \"year = 2*4DIGIT ( non-digit *OCTET )\"\n      result = parseDigits(token, 2, 4, true);\n      if (result !== null) {\n        year = result;\n        /* From S5.1.1:\n         * 3.  If the year-value is greater than or equal to 70 and less\n         * than or equal to 99, increment the year-value by 1900.\n         * 4.  If the year-value is greater than or equal to 0 and less\n         * than or equal to 69, increment the year-value by 2000.\n         */\n        if (year >= 70 && year <= 99) {\n          year += 1900;\n        } else if (year >= 0 && year <= 69) {\n          year += 2000;\n        }\n      }\n    }\n  }\n\n  /* RFC 6265 S5.1.1\n   * \"5. Abort these steps and fail to parse the cookie-date if:\n   *     *  at least one of the found-day-of-month, found-month, found-\n   *        year, or found-time flags is not set,\n   *     *  the day-of-month-value is less than 1 or greater than 31,\n   *     *  the year-value is less than 1601,\n   *     *  the hour-value is greater than 23,\n   *     *  the minute-value is greater than 59, or\n   *     *  the second-value is greater than 59.\n   *     (Note that leap seconds cannot be represented in this syntax.)\"\n   *\n   * So, in order as above:\n   */\n  if (\n    dayOfMonth === null || month === null || year === null || second === null ||\n    dayOfMonth < 1 || dayOfMonth > 31 ||\n    year < 1601 ||\n    hour > 23 ||\n    minute > 59 ||\n    second > 59\n  ) {\n    return;\n  }\n\n  return new Date(Date.UTC(year, month, dayOfMonth, hour, minute, second));\n}\n\nfunction formatDate(date) {\n  var d = date.getUTCDate(); d = d >= 10 ? d : '0'+d;\n  var h = date.getUTCHours(); h = h >= 10 ? h : '0'+h;\n  var m = date.getUTCMinutes(); m = m >= 10 ? m : '0'+m;\n  var s = date.getUTCSeconds(); s = s >= 10 ? s : '0'+s;\n  return NUM_TO_DAY[date.getUTCDay()] + ', ' +\n    d+' '+ NUM_TO_MONTH[date.getUTCMonth()] +' '+ date.getUTCFullYear() +' '+\n    h+':'+m+':'+s+' GMT';\n}\n\n// S5.1.2 Canonicalized Host Names\nfunction canonicalDomain(str) {\n  if (str == null) {\n    return null;\n  }\n  str = str.trim().replace(/^\\./,''); // S4.1.2.3 & S5.2.3: ignore leading .\n\n  // convert to IDN if any non-ASCII characters\n  if (punycode && /[^\\u0001-\\u007f]/.test(str)) {\n    str = punycode.toASCII(str);\n  }\n\n  return str.toLowerCase();\n}\n\n// S5.1.3 Domain Matching\nfunction domainMatch(str, domStr, canonicalize) {\n  if (str == null || domStr == null) {\n    return null;\n  }\n  if (canonicalize !== false) {\n    str = canonicalDomain(str);\n    domStr = canonicalDomain(domStr);\n  }\n\n  /*\n   * \"The domain string and the string are identical. (Note that both the\n   * domain string and the string will have been canonicalized to lower case at\n   * this point)\"\n   */\n  if (str == domStr) {\n    return true;\n  }\n\n  /* \"All of the following [three] conditions hold:\" (order adjusted from the RFC) */\n\n  /* \"* The string is a host name (i.e., not an IP address).\" */\n  if (net.isIP(str)) {\n    return false;\n  }\n\n  /* \"* The domain string is a suffix of the string\" */\n  var idx = str.indexOf(domStr);\n  if (idx <= 0) {\n    return false; // it's a non-match (-1) or prefix (0)\n  }\n\n  // e.g \"a.b.c\".indexOf(\"b.c\") === 2\n  // 5 === 3+2\n  if (str.length !== domStr.length + idx) { // it's not a suffix\n    return false;\n  }\n\n  /* \"* The last character of the string that is not included in the domain\n  * string is a %x2E (\".\") character.\" */\n  if (str.substr(idx-1,1) !== '.') {\n    return false;\n  }\n\n  return true;\n}\n\n\n// RFC6265 S5.1.4 Paths and Path-Match\n\n/*\n * \"The user agent MUST use an algorithm equivalent to the following algorithm\n * to compute the default-path of a cookie:\"\n *\n * Assumption: the path (and not query part or absolute uri) is passed in.\n */\nfunction defaultPath(path) {\n  // \"2. If the uri-path is empty or if the first character of the uri-path is not\n  // a %x2F (\"/\") character, output %x2F (\"/\") and skip the remaining steps.\n  if (!path || path.substr(0,1) !== \"/\") {\n    return \"/\";\n  }\n\n  // \"3. If the uri-path contains no more than one %x2F (\"/\") character, output\n  // %x2F (\"/\") and skip the remaining step.\"\n  if (path === \"/\") {\n    return path;\n  }\n\n  var rightSlash = path.lastIndexOf(\"/\");\n  if (rightSlash === 0) {\n    return \"/\";\n  }\n\n  // \"4. Output the characters of the uri-path from the first character up to,\n  // but not including, the right-most %x2F (\"/\").\"\n  return path.slice(0, rightSlash);\n}\n\nfunction trimTerminator(str) {\n  for (var t = 0; t < TERMINATORS.length; t++) {\n    var terminatorIdx = str.indexOf(TERMINATORS[t]);\n    if (terminatorIdx !== -1) {\n      str = str.substr(0,terminatorIdx);\n    }\n  }\n\n  return str;\n}\n\nfunction parseCookiePair(cookiePair, looseMode) {\n  cookiePair = trimTerminator(cookiePair);\n\n  var firstEq = cookiePair.indexOf('=');\n  if (looseMode) {\n    if (firstEq === 0) { // '=' is immediately at start\n      cookiePair = cookiePair.substr(1);\n      firstEq = cookiePair.indexOf('='); // might still need to split on '='\n    }\n  } else { // non-loose mode\n    if (firstEq <= 0) { // no '=' or is at start\n      return; // needs to have non-empty \"cookie-name\"\n    }\n  }\n\n  var cookieName, cookieValue;\n  if (firstEq <= 0) {\n    cookieName = \"\";\n    cookieValue = cookiePair.trim();\n  } else {\n    cookieName = cookiePair.substr(0, firstEq).trim();\n    cookieValue = cookiePair.substr(firstEq+1).trim();\n  }\n\n  if (CONTROL_CHARS.test(cookieName) || CONTROL_CHARS.test(cookieValue)) {\n    return;\n  }\n\n  var c = new Cookie();\n  c.key = cookieName;\n  c.value = cookieValue;\n  return c;\n}\n\nfunction parse(str, options) {\n  if (!options || typeof options !== 'object') {\n    options = {};\n  }\n  str = str.trim();\n\n  // We use a regex to parse the \"name-value-pair\" part of S5.2\n  var firstSemi = str.indexOf(';'); // S5.2 step 1\n  var cookiePair = (firstSemi === -1) ? str : str.substr(0, firstSemi);\n  var c = parseCookiePair(cookiePair, !!options.loose);\n  if (!c) {\n    return;\n  }\n\n  if (firstSemi === -1) {\n    return c;\n  }\n\n  // S5.2.3 \"unparsed-attributes consist of the remainder of the set-cookie-string\n  // (including the %x3B (\";\") in question).\" plus later on in the same section\n  // \"discard the first \";\" and trim\".\n  var unparsed = str.slice(firstSemi + 1).trim();\n\n  // \"If the unparsed-attributes string is empty, skip the rest of these\n  // steps.\"\n  if (unparsed.length === 0) {\n    return c;\n  }\n\n  /*\n   * S5.2 says that when looping over the items \"[p]rocess the attribute-name\n   * and attribute-value according to the requirements in the following\n   * subsections\" for every item.  Plus, for many of the individual attributes\n   * in S5.3 it says to use the \"attribute-value of the last attribute in the\n   * cookie-attribute-list\".  Therefore, in this implementation, we overwrite\n   * the previous value.\n   */\n  var cookie_avs = unparsed.split(';');\n  while (cookie_avs.length) {\n    var av = cookie_avs.shift().trim();\n    if (av.length === 0) { // happens if \";;\" appears\n      continue;\n    }\n    var av_sep = av.indexOf('=');\n    var av_key, av_value;\n\n    if (av_sep === -1) {\n      av_key = av;\n      av_value = null;\n    } else {\n      av_key = av.substr(0,av_sep);\n      av_value = av.substr(av_sep+1);\n    }\n\n    av_key = av_key.trim().toLowerCase();\n\n    if (av_value) {\n      av_value = av_value.trim();\n    }\n\n    switch(av_key) {\n    case 'expires': // S5.2.1\n      if (av_value) {\n        var exp = parseDate(av_value);\n        // \"If the attribute-value failed to parse as a cookie date, ignore the\n        // cookie-av.\"\n        if (exp) {\n          // over and underflow not realistically a concern: V8's getTime() seems to\n          // store something larger than a 32-bit time_t (even with 32-bit node)\n          c.expires = exp;\n        }\n      }\n      break;\n\n    case 'max-age': // S5.2.2\n      if (av_value) {\n        // \"If the first character of the attribute-value is not a DIGIT or a \"-\"\n        // character ...[or]... If the remainder of attribute-value contains a\n        // non-DIGIT character, ignore the cookie-av.\"\n        if (/^-?[0-9]+$/.test(av_value)) {\n          var delta = parseInt(av_value, 10);\n          // \"If delta-seconds is less than or equal to zero (0), let expiry-time\n          // be the earliest representable date and time.\"\n          c.setMaxAge(delta);\n        }\n      }\n      break;\n\n    case 'domain': // S5.2.3\n      // \"If the attribute-value is empty, the behavior is undefined.  However,\n      // the user agent SHOULD ignore the cookie-av entirely.\"\n      if (av_value) {\n        // S5.2.3 \"Let cookie-domain be the attribute-value without the leading %x2E\n        // (\".\") character.\"\n        var domain = av_value.trim().replace(/^\\./, '');\n        if (domain) {\n          // \"Convert the cookie-domain to lower case.\"\n          c.domain = domain.toLowerCase();\n        }\n      }\n      break;\n\n    case 'path': // S5.2.4\n      /*\n       * \"If the attribute-value is empty or if the first character of the\n       * attribute-value is not %x2F (\"/\"):\n       *   Let cookie-path be the default-path.\n       * Otherwise:\n       *   Let cookie-path be the attribute-value.\"\n       *\n       * We'll represent the default-path as null since it depends on the\n       * context of the parsing.\n       */\n      c.path = av_value && av_value[0] === \"/\" ? av_value : null;\n      break;\n\n    case 'secure': // S5.2.5\n      /*\n       * \"If the attribute-name case-insensitively matches the string \"Secure\",\n       * the user agent MUST append an attribute to the cookie-attribute-list\n       * with an attribute-name of Secure and an empty attribute-value.\"\n       */\n      c.secure = true;\n      break;\n\n    case 'httponly': // S5.2.6 -- effectively the same as 'secure'\n      c.httpOnly = true;\n      break;\n\n    default:\n      c.extensions = c.extensions || [];\n      c.extensions.push(av);\n      break;\n    }\n  }\n\n  return c;\n}\n\n// avoid the V8 deoptimization monster!\nfunction jsonParse(str) {\n  var obj;\n  try {\n    obj = JSON.parse(str);\n  } catch (e) {\n    return e;\n  }\n  return obj;\n}\n\nfunction fromJSON(str) {\n  if (!str) {\n    return null;\n  }\n\n  var obj;\n  if (typeof str === 'string') {\n    obj = jsonParse(str);\n    if (obj instanceof Error) {\n      return null;\n    }\n  } else {\n    // assume it's an Object\n    obj = str;\n  }\n\n  var c = new Cookie();\n  for (var i=0; i<Cookie.serializableProperties.length; i++) {\n    var prop = Cookie.serializableProperties[i];\n    if (obj[prop] === undefined ||\n        obj[prop] === Cookie.prototype[prop])\n    {\n      continue; // leave as prototype default\n    }\n\n    if (prop === 'expires' ||\n        prop === 'creation' ||\n        prop === 'lastAccessed')\n    {\n      if (obj[prop] === null) {\n        c[prop] = null;\n      } else {\n        c[prop] = obj[prop] == \"Infinity\" ?\n          \"Infinity\" : new Date(obj[prop]);\n      }\n    } else {\n      c[prop] = obj[prop];\n    }\n  }\n\n  return c;\n}\n\n/* Section 5.4 part 2:\n * \"*  Cookies with longer paths are listed before cookies with\n *     shorter paths.\n *\n *  *  Among cookies that have equal-length path fields, cookies with\n *     earlier creation-times are listed before cookies with later\n *     creation-times.\"\n */\n\nfunction cookieCompare(a,b) {\n  var cmp = 0;\n\n  // descending for length: b CMP a\n  var aPathLen = a.path ? a.path.length : 0;\n  var bPathLen = b.path ? b.path.length : 0;\n  cmp = bPathLen - aPathLen;\n  if (cmp !== 0) {\n    return cmp;\n  }\n\n  // ascending for time: a CMP b\n  var aTime = a.creation ? a.creation.getTime() : MAX_TIME;\n  var bTime = b.creation ? b.creation.getTime() : MAX_TIME;\n  cmp = aTime - bTime;\n  if (cmp !== 0) {\n    return cmp;\n  }\n\n  // break ties for the same millisecond (precision of JavaScript's clock)\n  cmp = a.creationIndex - b.creationIndex;\n\n  return cmp;\n}\n\n// Gives the permutation of all possible pathMatch()es of a given path. The\n// array is in longest-to-shortest order.  Handy for indexing.\nfunction permutePath(path) {\n  if (path === '/') {\n    return ['/'];\n  }\n  if (path.lastIndexOf('/') === path.length-1) {\n    path = path.substr(0,path.length-1);\n  }\n  var permutations = [path];\n  while (path.length > 1) {\n    var lindex = path.lastIndexOf('/');\n    if (lindex === 0) {\n      break;\n    }\n    path = path.substr(0,lindex);\n    permutations.push(path);\n  }\n  permutations.push('/');\n  return permutations;\n}\n\nfunction getCookieContext(url) {\n  if (url instanceof Object) {\n    return url;\n  }\n  // NOTE: decodeURI will throw on malformed URIs (see GH-32).\n  // Therefore, we will just skip decoding for such URIs.\n  try {\n    url = decodeURI(url);\n  }\n  catch(err) {\n    // Silently swallow error\n  }\n\n  return urlParse(url);\n}\n\nfunction Cookie(options) {\n  options = options || {};\n\n  Object.keys(options).forEach(function(prop) {\n    if (Cookie.prototype.hasOwnProperty(prop) &&\n        Cookie.prototype[prop] !== options[prop] &&\n        prop.substr(0,1) !== '_')\n    {\n      this[prop] = options[prop];\n    }\n  }, this);\n\n  this.creation = this.creation || new Date();\n\n  // used to break creation ties in cookieCompare():\n  Object.defineProperty(this, 'creationIndex', {\n    configurable: false,\n    enumerable: false, // important for assert.deepEqual checks\n    writable: true,\n    value: ++Cookie.cookiesCreated\n  });\n}\n\nCookie.cookiesCreated = 0; // incremented each time a cookie is created\n\nCookie.parse = parse;\nCookie.fromJSON = fromJSON;\n\nCookie.prototype.key = \"\";\nCookie.prototype.value = \"\";\n\n// the order in which the RFC has them:\nCookie.prototype.expires = \"Infinity\"; // coerces to literal Infinity\nCookie.prototype.maxAge = null; // takes precedence over expires for TTL\nCookie.prototype.domain = null;\nCookie.prototype.path = null;\nCookie.prototype.secure = false;\nCookie.prototype.httpOnly = false;\nCookie.prototype.extensions = null;\n\n// set by the CookieJar:\nCookie.prototype.hostOnly = null; // boolean when set\nCookie.prototype.pathIsDefault = null; // boolean when set\nCookie.prototype.creation = null; // Date when set; defaulted by Cookie.parse\nCookie.prototype.lastAccessed = null; // Date when set\nObject.defineProperty(Cookie.prototype, 'creationIndex', {\n  configurable: true,\n  enumerable: false,\n  writable: true,\n  value: 0\n});\n\nCookie.serializableProperties = Object.keys(Cookie.prototype)\n  .filter(function(prop) {\n    return !(\n      Cookie.prototype[prop] instanceof Function ||\n      prop === 'creationIndex' ||\n      prop.substr(0,1) === '_'\n    );\n  });\n\nCookie.prototype.inspect = function inspect() {\n  var now = Date.now();\n  return 'Cookie=\"'+this.toString() +\n    '; hostOnly='+(this.hostOnly != null ? this.hostOnly : '?') +\n    '; aAge='+(this.lastAccessed ? (now-this.lastAccessed.getTime())+'ms' : '?') +\n    '; cAge='+(this.creation ? (now-this.creation.getTime())+'ms' : '?') +\n    '\"';\n};\n\n// Use the new custom inspection symbol to add the custom inspect function if\n// available.\nif (util.inspect.custom) {\n  Cookie.prototype[util.inspect.custom] = Cookie.prototype.inspect;\n}\n\nCookie.prototype.toJSON = function() {\n  var obj = {};\n\n  var props = Cookie.serializableProperties;\n  for (var i=0; i<props.length; i++) {\n    var prop = props[i];\n    if (this[prop] === Cookie.prototype[prop]) {\n      continue; // leave as prototype default\n    }\n\n    if (prop === 'expires' ||\n        prop === 'creation' ||\n        prop === 'lastAccessed')\n    {\n      if (this[prop] === null) {\n        obj[prop] = null;\n      } else {\n        obj[prop] = this[prop] == \"Infinity\" ? // intentionally not ===\n          \"Infinity\" : this[prop].toISOString();\n      }\n    } else if (prop === 'maxAge') {\n      if (this[prop] !== null) {\n        // again, intentionally not ===\n        obj[prop] = (this[prop] == Infinity || this[prop] == -Infinity) ?\n          this[prop].toString() : this[prop];\n      }\n    } else {\n      if (this[prop] !== Cookie.prototype[prop]) {\n        obj[prop] = this[prop];\n      }\n    }\n  }\n\n  return obj;\n};\n\nCookie.prototype.clone = function() {\n  return fromJSON(this.toJSON());\n};\n\nCookie.prototype.validate = function validate() {\n  if (!COOKIE_OCTETS.test(this.value)) {\n    return false;\n  }\n  if (this.expires != Infinity && !(this.expires instanceof Date) && !parseDate(this.expires)) {\n    return false;\n  }\n  if (this.maxAge != null && this.maxAge <= 0) {\n    return false; // \"Max-Age=\" non-zero-digit *DIGIT\n  }\n  if (this.path != null && !PATH_VALUE.test(this.path)) {\n    return false;\n  }\n\n  var cdomain = this.cdomain();\n  if (cdomain) {\n    if (cdomain.match(/\\.$/)) {\n      return false; // S4.1.2.3 suggests that this is bad. domainMatch() tests confirm this\n    }\n    var suffix = pubsuffix.getPublicSuffix(cdomain);\n    if (suffix == null) { // it's a public suffix\n      return false;\n    }\n  }\n  return true;\n};\n\nCookie.prototype.setExpires = function setExpires(exp) {\n  if (exp instanceof Date) {\n    this.expires = exp;\n  } else {\n    this.expires = parseDate(exp) || \"Infinity\";\n  }\n};\n\nCookie.prototype.setMaxAge = function setMaxAge(age) {\n  if (age === Infinity || age === -Infinity) {\n    this.maxAge = age.toString(); // so JSON.stringify() works\n  } else {\n    this.maxAge = age;\n  }\n};\n\n// gives Cookie header format\nCookie.prototype.cookieString = function cookieString() {\n  var val = this.value;\n  if (val == null) {\n    val = '';\n  }\n  if (this.key === '') {\n    return val;\n  }\n  return this.key+'='+val;\n};\n\n// gives Set-Cookie header format\nCookie.prototype.toString = function toString() {\n  var str = this.cookieString();\n\n  if (this.expires != Infinity) {\n    if (this.expires instanceof Date) {\n      str += '; Expires='+formatDate(this.expires);\n    } else {\n      str += '; Expires='+this.expires;\n    }\n  }\n\n  if (this.maxAge != null && this.maxAge != Infinity) {\n    str += '; Max-Age='+this.maxAge;\n  }\n\n  if (this.domain && !this.hostOnly) {\n    str += '; Domain='+this.domain;\n  }\n  if (this.path) {\n    str += '; Path='+this.path;\n  }\n\n  if (this.secure) {\n    str += '; Secure';\n  }\n  if (this.httpOnly) {\n    str += '; HttpOnly';\n  }\n  if (this.extensions) {\n    this.extensions.forEach(function(ext) {\n      str += '; '+ext;\n    });\n  }\n\n  return str;\n};\n\n// TTL() partially replaces the \"expiry-time\" parts of S5.3 step 3 (setCookie()\n// elsewhere)\n// S5.3 says to give the \"latest representable date\" for which we use Infinity\n// For \"expired\" we use 0\nCookie.prototype.TTL = function TTL(now) {\n  /* RFC6265 S4.1.2.2 If a cookie has both the Max-Age and the Expires\n   * attribute, the Max-Age attribute has precedence and controls the\n   * expiration date of the cookie.\n   * (Concurs with S5.3 step 3)\n   */\n  if (this.maxAge != null) {\n    return this.maxAge<=0 ? 0 : this.maxAge*1000;\n  }\n\n  var expires = this.expires;\n  if (expires != Infinity) {\n    if (!(expires instanceof Date)) {\n      expires = parseDate(expires) || Infinity;\n    }\n\n    if (expires == Infinity) {\n      return Infinity;\n    }\n\n    return expires.getTime() - (now || Date.now());\n  }\n\n  return Infinity;\n};\n\n// expiryTime() replaces the \"expiry-time\" parts of S5.3 step 3 (setCookie()\n// elsewhere)\nCookie.prototype.expiryTime = function expiryTime(now) {\n  if (this.maxAge != null) {\n    var relativeTo = now || this.creation || new Date();\n    var age = (this.maxAge <= 0) ? -Infinity : this.maxAge*1000;\n    return relativeTo.getTime() + age;\n  }\n\n  if (this.expires == Infinity) {\n    return Infinity;\n  }\n  return this.expires.getTime();\n};\n\n// expiryDate() replaces the \"expiry-time\" parts of S5.3 step 3 (setCookie()\n// elsewhere), except it returns a Date\nCookie.prototype.expiryDate = function expiryDate(now) {\n  var millisec = this.expiryTime(now);\n  if (millisec == Infinity) {\n    return new Date(MAX_TIME);\n  } else if (millisec == -Infinity) {\n    return new Date(MIN_TIME);\n  } else {\n    return new Date(millisec);\n  }\n};\n\n// This replaces the \"persistent-flag\" parts of S5.3 step 3\nCookie.prototype.isPersistent = function isPersistent() {\n  return (this.maxAge != null || this.expires != Infinity);\n};\n\n// Mostly S5.1.2 and S5.2.3:\nCookie.prototype.cdomain =\nCookie.prototype.canonicalizedDomain = function canonicalizedDomain() {\n  if (this.domain == null) {\n    return null;\n  }\n  return canonicalDomain(this.domain);\n};\n\nfunction CookieJar(store, options) {\n  if (typeof options === \"boolean\") {\n    options = {rejectPublicSuffixes: options};\n  } else if (options == null) {\n    options = {};\n  }\n  if (options.rejectPublicSuffixes != null) {\n    this.rejectPublicSuffixes = options.rejectPublicSuffixes;\n  }\n  if (options.looseMode != null) {\n    this.enableLooseMode = options.looseMode;\n  }\n\n  if (!store) {\n    store = new MemoryCookieStore();\n  }\n  this.store = store;\n}\nCookieJar.prototype.store = null;\nCookieJar.prototype.rejectPublicSuffixes = true;\nCookieJar.prototype.enableLooseMode = false;\nvar CAN_BE_SYNC = [];\n\nCAN_BE_SYNC.push('setCookie');\nCookieJar.prototype.setCookie = function(cookie, url, options, cb) {\n  var err;\n  var context = getCookieContext(url);\n  if (options instanceof Function) {\n    cb = options;\n    options = {};\n  }\n\n  var host = canonicalDomain(context.hostname);\n  var loose = this.enableLooseMode;\n  if (options.loose != null) {\n    loose = options.loose;\n  }\n\n  // S5.3 step 1\n  if (!(cookie instanceof Cookie)) {\n    cookie = Cookie.parse(cookie, { loose: loose });\n  }\n  if (!cookie) {\n    err = new Error(\"Cookie failed to parse\");\n    return cb(options.ignoreError ? null : err);\n  }\n\n  // S5.3 step 2\n  var now = options.now || new Date(); // will assign later to save effort in the face of errors\n\n  // S5.3 step 3: NOOP; persistent-flag and expiry-time is handled by getCookie()\n\n  // S5.3 step 4: NOOP; domain is null by default\n\n  // S5.3 step 5: public suffixes\n  if (this.rejectPublicSuffixes && cookie.domain) {\n    var suffix = pubsuffix.getPublicSuffix(cookie.cdomain());\n    if (suffix == null) { // e.g. \"com\"\n      err = new Error(\"Cookie has domain set to a public suffix\");\n      return cb(options.ignoreError ? null : err);\n    }\n  }\n\n  // S5.3 step 6:\n  if (cookie.domain) {\n    if (!domainMatch(host, cookie.cdomain(), false)) {\n      err = new Error(\"Cookie not in this host's domain. Cookie:\"+cookie.cdomain()+\" Request:\"+host);\n      return cb(options.ignoreError ? null : err);\n    }\n\n    if (cookie.hostOnly == null) { // don't reset if already set\n      cookie.hostOnly = false;\n    }\n\n  } else {\n    cookie.hostOnly = true;\n    cookie.domain = host;\n  }\n\n  //S5.2.4 If the attribute-value is empty or if the first character of the\n  //attribute-value is not %x2F (\"/\"):\n  //Let cookie-path be the default-path.\n  if (!cookie.path || cookie.path[0] !== '/') {\n    cookie.path = defaultPath(context.pathname);\n    cookie.pathIsDefault = true;\n  }\n\n  // S5.3 step 8: NOOP; secure attribute\n  // S5.3 step 9: NOOP; httpOnly attribute\n\n  // S5.3 step 10\n  if (options.http === false && cookie.httpOnly) {\n    err = new Error(\"Cookie is HttpOnly and this isn't an HTTP API\");\n    return cb(options.ignoreError ? null : err);\n  }\n\n  var store = this.store;\n\n  if (!store.updateCookie) {\n    store.updateCookie = function(oldCookie, newCookie, cb) {\n      this.putCookie(newCookie, cb);\n    };\n  }\n\n  function withCookie(err, oldCookie) {\n    if (err) {\n      return cb(err);\n    }\n\n    var next = function(err) {\n      if (err) {\n        return cb(err);\n      } else {\n        cb(null, cookie);\n      }\n    };\n\n    if (oldCookie) {\n      // S5.3 step 11 - \"If the cookie store contains a cookie with the same name,\n      // domain, and path as the newly created cookie:\"\n      if (options.http === false && oldCookie.httpOnly) { // step 11.2\n        err = new Error(\"old Cookie is HttpOnly and this isn't an HTTP API\");\n        return cb(options.ignoreError ? null : err);\n      }\n      cookie.creation = oldCookie.creation; // step 11.3\n      cookie.creationIndex = oldCookie.creationIndex; // preserve tie-breaker\n      cookie.lastAccessed = now;\n      // Step 11.4 (delete cookie) is implied by just setting the new one:\n      store.updateCookie(oldCookie, cookie, next); // step 12\n\n    } else {\n      cookie.creation = cookie.lastAccessed = now;\n      store.putCookie(cookie, next); // step 12\n    }\n  }\n\n  store.findCookie(cookie.domain, cookie.path, cookie.key, withCookie);\n};\n\n// RFC6365 S5.4\nCAN_BE_SYNC.push('getCookies');\nCookieJar.prototype.getCookies = function(url, options, cb) {\n  var context = getCookieContext(url);\n  if (options instanceof Function) {\n    cb = options;\n    options = {};\n  }\n\n  var host = canonicalDomain(context.hostname);\n  var path = context.pathname || '/';\n\n  var secure = options.secure;\n  if (secure == null && context.protocol &&\n      (context.protocol == 'https:' || context.protocol == 'wss:'))\n  {\n    secure = true;\n  }\n\n  var http = options.http;\n  if (http == null) {\n    http = true;\n  }\n\n  var now = options.now || Date.now();\n  var expireCheck = options.expire !== false;\n  var allPaths = !!options.allPaths;\n  var store = this.store;\n\n  function matchingCookie(c) {\n    // \"Either:\n    //   The cookie's host-only-flag is true and the canonicalized\n    //   request-host is identical to the cookie's domain.\n    // Or:\n    //   The cookie's host-only-flag is false and the canonicalized\n    //   request-host domain-matches the cookie's domain.\"\n    if (c.hostOnly) {\n      if (c.domain != host) {\n        return false;\n      }\n    } else {\n      if (!domainMatch(host, c.domain, false)) {\n        return false;\n      }\n    }\n\n    // \"The request-uri's path path-matches the cookie's path.\"\n    if (!allPaths && !pathMatch(path, c.path)) {\n      return false;\n    }\n\n    // \"If the cookie's secure-only-flag is true, then the request-uri's\n    // scheme must denote a \"secure\" protocol\"\n    if (c.secure && !secure) {\n      return false;\n    }\n\n    // \"If the cookie's http-only-flag is true, then exclude the cookie if the\n    // cookie-string is being generated for a \"non-HTTP\" API\"\n    if (c.httpOnly && !http) {\n      return false;\n    }\n\n    // deferred from S5.3\n    // non-RFC: allow retention of expired cookies by choice\n    if (expireCheck && c.expiryTime() <= now) {\n      store.removeCookie(c.domain, c.path, c.key, function(){}); // result ignored\n      return false;\n    }\n\n    return true;\n  }\n\n  store.findCookies(host, allPaths ? null : path, function(err,cookies) {\n    if (err) {\n      return cb(err);\n    }\n\n    cookies = cookies.filter(matchingCookie);\n\n    // sorting of S5.4 part 2\n    if (options.sort !== false) {\n      cookies = cookies.sort(cookieCompare);\n    }\n\n    // S5.4 part 3\n    var now = new Date();\n    cookies.forEach(function(c) {\n      c.lastAccessed = now;\n    });\n    // TODO persist lastAccessed\n\n    cb(null,cookies);\n  });\n};\n\nCAN_BE_SYNC.push('getCookieString');\nCookieJar.prototype.getCookieString = function(/*..., cb*/) {\n  var args = Array.prototype.slice.call(arguments,0);\n  var cb = args.pop();\n  var next = function(err,cookies) {\n    if (err) {\n      cb(err);\n    } else {\n      cb(null, cookies\n        .sort(cookieCompare)\n        .map(function(c){\n          return c.cookieString();\n        })\n        .join('; '));\n    }\n  };\n  args.push(next);\n  this.getCookies.apply(this,args);\n};\n\nCAN_BE_SYNC.push('getSetCookieStrings');\nCookieJar.prototype.getSetCookieStrings = function(/*..., cb*/) {\n  var args = Array.prototype.slice.call(arguments,0);\n  var cb = args.pop();\n  var next = function(err,cookies) {\n    if (err) {\n      cb(err);\n    } else {\n      cb(null, cookies.map(function(c){\n        return c.toString();\n      }));\n    }\n  };\n  args.push(next);\n  this.getCookies.apply(this,args);\n};\n\nCAN_BE_SYNC.push('serialize');\nCookieJar.prototype.serialize = function(cb) {\n  var type = this.store.constructor.name;\n  if (type === 'Object') {\n    type = null;\n  }\n\n  // update README.md \"Serialization Format\" if you change this, please!\n  var serialized = {\n    // The version of tough-cookie that serialized this jar. Generally a good\n    // practice since future versions can make data import decisions based on\n    // known past behavior. When/if this matters, use `semver`.\n    version: 'tough-cookie@'+VERSION,\n\n    // add the store type, to make humans happy:\n    storeType: type,\n\n    // CookieJar configuration:\n    rejectPublicSuffixes: !!this.rejectPublicSuffixes,\n\n    // this gets filled from getAllCookies:\n    cookies: []\n  };\n\n  if (!(this.store.getAllCookies &&\n        typeof this.store.getAllCookies === 'function'))\n  {\n    return cb(new Error('store does not support getAllCookies and cannot be serialized'));\n  }\n\n  this.store.getAllCookies(function(err,cookies) {\n    if (err) {\n      return cb(err);\n    }\n\n    serialized.cookies = cookies.map(function(cookie) {\n      // convert to serialized 'raw' cookies\n      cookie = (cookie instanceof Cookie) ? cookie.toJSON() : cookie;\n\n      // Remove the index so new ones get assigned during deserialization\n      delete cookie.creationIndex;\n\n      return cookie;\n    });\n\n    return cb(null, serialized);\n  });\n};\n\n// well-known name that JSON.stringify calls\nCookieJar.prototype.toJSON = function() {\n  return this.serializeSync();\n};\n\n// use the class method CookieJar.deserialize instead of calling this directly\nCAN_BE_SYNC.push('_importCookies');\nCookieJar.prototype._importCookies = function(serialized, cb) {\n  var jar = this;\n  var cookies = serialized.cookies;\n  if (!cookies || !Array.isArray(cookies)) {\n    return cb(new Error('serialized jar has no cookies array'));\n  }\n  cookies = cookies.slice(); // do not modify the original\n\n  function putNext(err) {\n    if (err) {\n      return cb(err);\n    }\n\n    if (!cookies.length) {\n      return cb(err, jar);\n    }\n\n    var cookie;\n    try {\n      cookie = fromJSON(cookies.shift());\n    } catch (e) {\n      return cb(e);\n    }\n\n    if (cookie === null) {\n      return putNext(null); // skip this cookie\n    }\n\n    jar.store.putCookie(cookie, putNext);\n  }\n\n  putNext();\n};\n\nCookieJar.deserialize = function(strOrObj, store, cb) {\n  if (arguments.length !== 3) {\n    // store is optional\n    cb = store;\n    store = null;\n  }\n\n  var serialized;\n  if (typeof strOrObj === 'string') {\n    serialized = jsonParse(strOrObj);\n    if (serialized instanceof Error) {\n      return cb(serialized);\n    }\n  } else {\n    serialized = strOrObj;\n  }\n\n  var jar = new CookieJar(store, serialized.rejectPublicSuffixes);\n  jar._importCookies(serialized, function(err) {\n    if (err) {\n      return cb(err);\n    }\n    cb(null, jar);\n  });\n};\n\nCookieJar.deserializeSync = function(strOrObj, store) {\n  var serialized = typeof strOrObj === 'string' ?\n    JSON.parse(strOrObj) : strOrObj;\n  var jar = new CookieJar(store, serialized.rejectPublicSuffixes);\n\n  // catch this mistake early:\n  if (!jar.store.synchronous) {\n    throw new Error('CookieJar store is not synchronous; use async API instead.');\n  }\n\n  jar._importCookiesSync(serialized);\n  return jar;\n};\nCookieJar.fromJSON = CookieJar.deserializeSync;\n\nCookieJar.prototype.clone = function(newStore, cb) {\n  if (arguments.length === 1) {\n    cb = newStore;\n    newStore = null;\n  }\n\n  this.serialize(function(err,serialized) {\n    if (err) {\n      return cb(err);\n    }\n    CookieJar.deserialize(serialized, newStore, cb);\n  });\n};\n\nCAN_BE_SYNC.push('removeAllCookies');\nCookieJar.prototype.removeAllCookies = function(cb) {\n  var store = this.store;\n\n  // Check that the store implements its own removeAllCookies(). The default\n  // implementation in Store will immediately call the callback with a \"not\n  // implemented\" Error.\n  if (store.removeAllCookies instanceof Function &&\n      store.removeAllCookies !== Store.prototype.removeAllCookies)\n  {\n    return store.removeAllCookies(cb);\n  }\n\n  store.getAllCookies(function(err, cookies) {\n    if (err) {\n      return cb(err);\n    }\n\n    if (cookies.length === 0) {\n      return cb(null);\n    }\n\n    var completedCount = 0;\n    var removeErrors = [];\n\n    function removeCookieCb(removeErr) {\n      if (removeErr) {\n        removeErrors.push(removeErr);\n      }\n\n      completedCount++;\n\n      if (completedCount === cookies.length) {\n        return cb(removeErrors.length ? removeErrors[0] : null);\n      }\n    }\n\n    cookies.forEach(function(cookie) {\n      store.removeCookie(cookie.domain, cookie.path, cookie.key, removeCookieCb);\n    });\n  });\n};\n\nCookieJar.prototype._cloneSync = syncWrap('clone');\nCookieJar.prototype.cloneSync = function(newStore) {\n  if (!newStore.synchronous) {\n    throw new Error('CookieJar clone destination store is not synchronous; use async API instead.');\n  }\n  return this._cloneSync(newStore);\n};\n\n// Use a closure to provide a true imperative API for synchronous stores.\nfunction syncWrap(method) {\n  return function() {\n    if (!this.store.synchronous) {\n      throw new Error('CookieJar store is not synchronous; use async API instead.');\n    }\n\n    var args = Array.prototype.slice.call(arguments);\n    var syncErr, syncResult;\n    args.push(function syncCb(err, result) {\n      syncErr = err;\n      syncResult = result;\n    });\n    this[method].apply(this, args);\n\n    if (syncErr) {\n      throw syncErr;\n    }\n    return syncResult;\n  };\n}\n\n// wrap all declared CAN_BE_SYNC methods in the sync wrapper\nCAN_BE_SYNC.forEach(function(method) {\n  CookieJar.prototype[method+'Sync'] = syncWrap(method);\n});\n\nexports.version = VERSION;\nexports.CookieJar = CookieJar;\nexports.Cookie = Cookie;\nexports.Store = Store;\nexports.MemoryCookieStore = MemoryCookieStore;\nexports.parseDate = parseDate;\nexports.formatDate = formatDate;\nexports.parse = parse;\nexports.fromJSON = fromJSON;\nexports.domainMatch = domainMatch;\nexports.defaultPath = defaultPath;\nexports.pathMatch = pathMatch;\nexports.getPublicSuffix = pubsuffix.getPublicSuffix;\nexports.cookieCompare = cookieCompare;\nexports.permuteDomain = require('./permuteDomain').permuteDomain;\nexports.permutePath = permutePath;\nexports.canonicalDomain = canonicalDomain;\n", "/*!\n * Copyright (c) 2018, Salesforce.com, Inc.\n * All rights reserved.\n *\n * Redistribution and use in source and binary forms, with or without\n * modification, are permitted provided that the following conditions are met:\n *\n * 1. Redistributions of source code must retain the above copyright notice,\n * this list of conditions and the following disclaimer.\n *\n * 2. Redistributions in binary form must reproduce the above copyright notice,\n * this list of conditions and the following disclaimer in the documentation\n * and/or other materials provided with the distribution.\n *\n * 3. Neither the name of Salesforce.com nor the names of its contributors may\n * be used to endorse or promote products derived from this software without\n * specific prior written permission.\n *\n * THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS \"AS IS\"\n * AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE\n * IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE\n * ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE\n * LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, <PERSON>ECIAL, EXEMPLARY, OR\n * CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF\n * SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS\n * INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN\n * CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)\n * ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE\n * POSSIBILITY OF SUCH DAMAGE.\n */\n\nvar psl = require('psl');\n\nfunction getPublicSuffix(domain) {\n  return psl.get(domain);\n}\n\nexports.getPublicSuffix = getPublicSuffix;\n", "/*!\n * Copyright (c) 2015, Salesforce.com, Inc.\n * All rights reserved.\n *\n * Redistribution and use in source and binary forms, with or without\n * modification, are permitted provided that the following conditions are met:\n *\n * 1. Redistributions of source code must retain the above copyright notice,\n * this list of conditions and the following disclaimer.\n *\n * 2. Redistributions in binary form must reproduce the above copyright notice,\n * this list of conditions and the following disclaimer in the documentation\n * and/or other materials provided with the distribution.\n *\n * 3. Neither the name of Salesforce.com nor the names of its contributors may\n * be used to endorse or promote products derived from this software without\n * specific prior written permission.\n *\n * THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS \"AS IS\"\n * AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE\n * IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE\n * ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE\n * LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, <PERSON>ECIAL, EXEMPLARY, OR\n * CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF\n * SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS\n * INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN\n * CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)\n * ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE\n * POSSIBILITY OF SUCH DAMAGE.\n */\n\n/*jshint unused:false */\n\nfunction Store() {\n}\nexports.Store = Store;\n\n// Stores may be synchronous, but are still required to use a\n// Continuation-Passing Style API.  The CookieJar itself will expose a \"*Sync\"\n// API that converts from synchronous-callbacks to imperative style.\nStore.prototype.synchronous = false;\n\nStore.prototype.findCookie = function(domain, path, key, cb) {\n  throw new Error('findCookie is not implemented');\n};\n\nStore.prototype.findCookies = function(domain, path, cb) {\n  throw new Error('findCookies is not implemented');\n};\n\nStore.prototype.putCookie = function(cookie, cb) {\n  throw new Error('putCookie is not implemented');\n};\n\nStore.prototype.updateCookie = function(oldCookie, newCookie, cb) {\n  // recommended default implementation:\n  // return this.putCookie(newCookie, cb);\n  throw new Error('updateCookie is not implemented');\n};\n\nStore.prototype.removeCookie = function(domain, path, key, cb) {\n  throw new Error('removeCookie is not implemented');\n};\n\nStore.prototype.removeCookies = function(domain, path, cb) {\n  throw new Error('removeCookies is not implemented');\n};\n\nStore.prototype.removeAllCookies = function(cb) {\n  throw new Error('removeAllCookies is not implemented');\n}\n\nStore.prototype.getAllCookies = function(cb) {\n  throw new Error('getAllCookies is not implemented (therefore jar cannot be serialized)');\n};\n", "/*!\n * Copyright (c) 2015, Salesforce.com, Inc.\n * All rights reserved.\n *\n * Redistribution and use in source and binary forms, with or without\n * modification, are permitted provided that the following conditions are met:\n *\n * 1. Redistributions of source code must retain the above copyright notice,\n * this list of conditions and the following disclaimer.\n *\n * 2. Redistributions in binary form must reproduce the above copyright notice,\n * this list of conditions and the following disclaimer in the documentation\n * and/or other materials provided with the distribution.\n *\n * 3. Neither the name of Salesforce.com nor the names of its contributors may\n * be used to endorse or promote products derived from this software without\n * specific prior written permission.\n *\n * THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS \"AS IS\"\n * AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE\n * IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE\n * ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE\n * LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, <PERSON>ECIAL, EXEMPLARY, OR\n * CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF\n * SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS\n * INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN\n * CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)\n * ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE\n * POSSIBILITY OF SUCH DAMAGE.\n */\n\nvar Store = require('./store').Store;\nvar permuteDomain = require('./permuteDomain').permuteDomain;\nvar pathMatch = require('./pathMatch').pathMatch;\nvar util = require('util');\n\nfunction MemoryCookieStore() {\n  Store.call(this);\n  this.idx = {};\n}\nutil.inherits(MemoryCookieStore, Store);\nexports.MemoryCookieStore = MemoryCookieStore;\nMemoryCookieStore.prototype.idx = null;\n\n// Since it's just a struct in RAM, this Store is synchronous\nMemoryCookieStore.prototype.synchronous = true;\n\n// force a default depth:\nMemoryCookieStore.prototype.inspect = function() {\n  return \"{ idx: \"+util.inspect(this.idx, false, 2)+' }';\n};\n\n// Use the new custom inspection symbol to add the custom inspect function if\n// available.\nif (util.inspect.custom) {\n  MemoryCookieStore.prototype[util.inspect.custom] = MemoryCookieStore.prototype.inspect;\n}\n\nMemoryCookieStore.prototype.findCookie = function(domain, path, key, cb) {\n  if (!this.idx[domain]) {\n    return cb(null,undefined);\n  }\n  if (!this.idx[domain][path]) {\n    return cb(null,undefined);\n  }\n  return cb(null,this.idx[domain][path][key]||null);\n};\n\nMemoryCookieStore.prototype.findCookies = function(domain, path, cb) {\n  var results = [];\n  if (!domain) {\n    return cb(null,[]);\n  }\n\n  var pathMatcher;\n  if (!path) {\n    // null means \"all paths\"\n    pathMatcher = function matchAll(domainIndex) {\n      for (var curPath in domainIndex) {\n        var pathIndex = domainIndex[curPath];\n        for (var key in pathIndex) {\n          results.push(pathIndex[key]);\n        }\n      }\n    };\n\n  } else {\n    pathMatcher = function matchRFC(domainIndex) {\n       //NOTE: we should use path-match algorithm from S5.1.4 here\n       //(see : https://github.com/ChromiumWebApps/chromium/blob/b3d3b4da8bb94c1b2e061600df106d590fda3620/net/cookies/canonical_cookie.cc#L299)\n       Object.keys(domainIndex).forEach(function (cookiePath) {\n         if (pathMatch(path, cookiePath)) {\n           var pathIndex = domainIndex[cookiePath];\n\n           for (var key in pathIndex) {\n             results.push(pathIndex[key]);\n           }\n         }\n       });\n     };\n  }\n\n  var domains = permuteDomain(domain) || [domain];\n  var idx = this.idx;\n  domains.forEach(function(curDomain) {\n    var domainIndex = idx[curDomain];\n    if (!domainIndex) {\n      return;\n    }\n    pathMatcher(domainIndex);\n  });\n\n  cb(null,results);\n};\n\nMemoryCookieStore.prototype.putCookie = function(cookie, cb) {\n  if (!this.idx[cookie.domain]) {\n    this.idx[cookie.domain] = {};\n  }\n  if (!this.idx[cookie.domain][cookie.path]) {\n    this.idx[cookie.domain][cookie.path] = {};\n  }\n  this.idx[cookie.domain][cookie.path][cookie.key] = cookie;\n  cb(null);\n};\n\nMemoryCookieStore.prototype.updateCookie = function(oldCookie, newCookie, cb) {\n  // updateCookie() may avoid updating cookies that are identical.  For example,\n  // lastAccessed may not be important to some stores and an equality\n  // comparison could exclude that field.\n  this.putCookie(newCookie,cb);\n};\n\nMemoryCookieStore.prototype.removeCookie = function(domain, path, key, cb) {\n  if (this.idx[domain] && this.idx[domain][path] && this.idx[domain][path][key]) {\n    delete this.idx[domain][path][key];\n  }\n  cb(null);\n};\n\nMemoryCookieStore.prototype.removeCookies = function(domain, path, cb) {\n  if (this.idx[domain]) {\n    if (path) {\n      delete this.idx[domain][path];\n    } else {\n      delete this.idx[domain];\n    }\n  }\n  return cb(null);\n};\n\nMemoryCookieStore.prototype.removeAllCookies = function(cb) {\n  this.idx = {};\n  return cb(null);\n}\n\nMemoryCookieStore.prototype.getAllCookies = function(cb) {\n  var cookies = [];\n  var idx = this.idx;\n\n  var domains = Object.keys(idx);\n  domains.forEach(function(domain) {\n    var paths = Object.keys(idx[domain]);\n    paths.forEach(function(path) {\n      var keys = Object.keys(idx[domain][path]);\n      keys.forEach(function(key) {\n        if (key !== null) {\n          cookies.push(idx[domain][path][key]);\n        }\n      });\n    });\n  });\n\n  // Sort by creationIndex so deserializing retains the creation order.\n  // When implementing your own store, this SHOULD retain the order too\n  cookies.sort(function(a,b) {\n    return (a.creationIndex||0) - (b.creationIndex||0);\n  });\n\n  cb(null, cookies);\n};\n", "/*!\n * Copyright (c) 2015, Salesforce.com, Inc.\n * All rights reserved.\n *\n * Redistribution and use in source and binary forms, with or without\n * modification, are permitted provided that the following conditions are met:\n *\n * 1. Redistributions of source code must retain the above copyright notice,\n * this list of conditions and the following disclaimer.\n *\n * 2. Redistributions in binary form must reproduce the above copyright notice,\n * this list of conditions and the following disclaimer in the documentation\n * and/or other materials provided with the distribution.\n *\n * 3. Neither the name of Salesforce.com nor the names of its contributors may\n * be used to endorse or promote products derived from this software without\n * specific prior written permission.\n *\n * THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS \"AS IS\"\n * AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE\n * IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE\n * ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE\n * LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, <PERSON>ECIAL, EXEMPLARY, OR\n * CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF\n * SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS\n * INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN\n * CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)\n * ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE\n * POSSIBILITY OF SUCH DAMAGE.\n */\n\nvar pubsuffix = require('./pubsuffix-psl');\n\n// Gives the permutation of all possible domainMatch()es of a given domain. The\n// array is in shortest-to-longest order.  Handy for indexing.\nfunction permuteDomain (domain) {\n  var pubSuf = pubsuffix.getPublicSuffix(domain);\n  if (!pubSuf) {\n    return null;\n  }\n  if (pubSuf == domain) {\n    return [domain];\n  }\n\n  var prefix = domain.slice(0, -(pubSuf.length + 1)); // \".example.com\"\n  var parts = prefix.split('.').reverse();\n  var cur = pubSuf;\n  var permutations = [cur];\n  while (parts.length) {\n    cur = parts.shift() + '.' + cur;\n    permutations.push(cur);\n  }\n  return permutations;\n}\n\nexports.permuteDomain = permuteDomain;\n", "/*!\n * Copyright (c) 2015, Salesforce.com, Inc.\n * All rights reserved.\n *\n * Redistribution and use in source and binary forms, with or without\n * modification, are permitted provided that the following conditions are met:\n *\n * 1. Redistributions of source code must retain the above copyright notice,\n * this list of conditions and the following disclaimer.\n *\n * 2. Redistributions in binary form must reproduce the above copyright notice,\n * this list of conditions and the following disclaimer in the documentation\n * and/or other materials provided with the distribution.\n *\n * 3. Neither the name of Salesforce.com nor the names of its contributors may\n * be used to endorse or promote products derived from this software without\n * specific prior written permission.\n *\n * THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS \"AS IS\"\n * AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE\n * IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE\n * ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE\n * LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, <PERSON>ECIAL, EXEMPLARY, OR\n * CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF\n * SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS\n * INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN\n * CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)\n * ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE\n * POSSIBILITY OF SUCH DAMAGE.\n */\n\n/*\n * \"A request-path path-matches a given cookie-path if at least one of the\n * following conditions holds:\"\n */\nfunction pathMatch (reqPath, cookiePath) {\n  // \"o  The cookie-path and the request-path are identical.\"\n  if (cookiePath === reqPath) {\n    return true;\n  }\n\n  var idx = reqPath.indexOf(cookiePath);\n  if (idx === 0) {\n    // \"o  The cookie-path is a prefix of the request-path, and the last\n    // character of the cookie-path is %x2F (\"/\").\"\n    if (cookiePath.substr(-1) === \"/\") {\n      return true;\n    }\n\n    // \" o  The cookie-path is a prefix of the request-path, and the first\n    // character of the request-path that is not included in the cookie- path\n    // is a %x2F (\"/\") character.\"\n    if (reqPath.substr(cookiePath.length, 1) === \"/\") {\n      return true;\n    }\n  }\n\n  return false;\n}\n\nexports.pathMatch = pathMatch;\n", "// generated by genversion\nmodule.exports = '2.5.0'\n"]}