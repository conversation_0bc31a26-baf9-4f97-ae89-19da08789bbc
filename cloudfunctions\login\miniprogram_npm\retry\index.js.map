{"version": 3, "sources": ["index.js", "lib/retry.js", "lib/retry_operation.js"], "names": [], "mappings": ";;;;;;;AAAA;;;ACAA;AACA;AACA;AACA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.js", "sourcesContent": ["module.exports = require('./lib/retry');", "var RetryOperation = require('./retry_operation');\n\nexports.operation = function(options) {\n  var timeouts = exports.timeouts(options);\n  return new RetryOperation(timeouts, {\n      forever: options && options.forever,\n      unref: options && options.unref,\n      maxRetryTime: options && options.maxRetryTime\n  });\n};\n\nexports.timeouts = function(options) {\n  if (options instanceof Array) {\n    return [].concat(options);\n  }\n\n  var opts = {\n    retries: 10,\n    factor: 2,\n    minTimeout: 1 * 1000,\n    maxTimeout: Infinity,\n    randomize: false\n  };\n  for (var key in options) {\n    opts[key] = options[key];\n  }\n\n  if (opts.minTimeout > opts.maxTimeout) {\n    throw new Error('minTimeout is greater than maxTimeout');\n  }\n\n  var timeouts = [];\n  for (var i = 0; i < opts.retries; i++) {\n    timeouts.push(this.createTimeout(i, opts));\n  }\n\n  if (options && options.forever && !timeouts.length) {\n    timeouts.push(this.createTimeout(i, opts));\n  }\n\n  // sort the array numerically ascending\n  timeouts.sort(function(a,b) {\n    return a - b;\n  });\n\n  return timeouts;\n};\n\nexports.createTimeout = function(attempt, opts) {\n  var random = (opts.randomize)\n    ? (Math.random() + 1)\n    : 1;\n\n  var timeout = Math.round(random * opts.minTimeout * Math.pow(opts.factor, attempt));\n  timeout = Math.min(timeout, opts.maxTimeout);\n\n  return timeout;\n};\n\nexports.wrap = function(obj, options, methods) {\n  if (options instanceof Array) {\n    methods = options;\n    options = null;\n  }\n\n  if (!methods) {\n    methods = [];\n    for (var key in obj) {\n      if (typeof obj[key] === 'function') {\n        methods.push(key);\n      }\n    }\n  }\n\n  for (var i = 0; i < methods.length; i++) {\n    var method   = methods[i];\n    var original = obj[method];\n\n    obj[method] = function retryWrapper(original) {\n      var op       = exports.operation(options);\n      var args     = Array.prototype.slice.call(arguments, 1);\n      var callback = args.pop();\n\n      args.push(function(err) {\n        if (op.retry(err)) {\n          return;\n        }\n        if (err) {\n          arguments[0] = op.mainError();\n        }\n        callback.apply(this, arguments);\n      });\n\n      op.attempt(function() {\n        original.apply(obj, args);\n      });\n    }.bind(obj, original);\n    obj[method].options = options;\n  }\n};\n", "function RetryOperation(timeouts, options) {\n  // Compatibility for the old (timeouts, retryForever) signature\n  if (typeof options === 'boolean') {\n    options = { forever: options };\n  }\n\n  this._originalTimeouts = JSON.parse(JSON.stringify(timeouts));\n  this._timeouts = timeouts;\n  this._options = options || {};\n  this._maxRetryTime = options && options.maxRetryTime || Infinity;\n  this._fn = null;\n  this._errors = [];\n  this._attempts = 1;\n  this._operationTimeout = null;\n  this._operationTimeoutCb = null;\n  this._timeout = null;\n  this._operationStart = null;\n\n  if (this._options.forever) {\n    this._cachedTimeouts = this._timeouts.slice(0);\n  }\n}\nmodule.exports = RetryOperation;\n\nRetryOperation.prototype.reset = function() {\n  this._attempts = 1;\n  this._timeouts = this._originalTimeouts;\n}\n\nRetryOperation.prototype.stop = function() {\n  if (this._timeout) {\n    clearTimeout(this._timeout);\n  }\n\n  this._timeouts       = [];\n  this._cachedTimeouts = null;\n};\n\nRetryOperation.prototype.retry = function(err) {\n  if (this._timeout) {\n    clearTimeout(this._timeout);\n  }\n\n  if (!err) {\n    return false;\n  }\n  var currentTime = new Date().getTime();\n  if (err && currentTime - this._operationStart >= this._maxRetryTime) {\n    this._errors.unshift(new Error('RetryOperation timeout occurred'));\n    return false;\n  }\n\n  this._errors.push(err);\n\n  var timeout = this._timeouts.shift();\n  if (timeout === undefined) {\n    if (this._cachedTimeouts) {\n      // retry forever, only keep last error\n      this._errors.splice(this._errors.length - 1, this._errors.length);\n      this._timeouts = this._cachedTimeouts.slice(0);\n      timeout = this._timeouts.shift();\n    } else {\n      return false;\n    }\n  }\n\n  var self = this;\n  var timer = setTimeout(function() {\n    self._attempts++;\n\n    if (self._operationTimeoutCb) {\n      self._timeout = setTimeout(function() {\n        self._operationTimeoutCb(self._attempts);\n      }, self._operationTimeout);\n\n      if (self._options.unref) {\n          self._timeout.unref();\n      }\n    }\n\n    self._fn(self._attempts);\n  }, timeout);\n\n  if (this._options.unref) {\n      timer.unref();\n  }\n\n  return true;\n};\n\nRetryOperation.prototype.attempt = function(fn, timeoutOps) {\n  this._fn = fn;\n\n  if (timeoutOps) {\n    if (timeoutOps.timeout) {\n      this._operationTimeout = timeoutOps.timeout;\n    }\n    if (timeoutOps.cb) {\n      this._operationTimeoutCb = timeoutOps.cb;\n    }\n  }\n\n  var self = this;\n  if (this._operationTimeoutCb) {\n    this._timeout = setTimeout(function() {\n      self._operationTimeoutCb();\n    }, self._operationTimeout);\n  }\n\n  this._operationStart = new Date().getTime();\n\n  this._fn(this._attempts);\n};\n\nRetryOperation.prototype.try = function(fn) {\n  console.log('Using RetryOperation.try() is deprecated');\n  this.attempt(fn);\n};\n\nRetryOperation.prototype.start = function(fn) {\n  console.log('Using RetryOperation.start() is deprecated');\n  this.attempt(fn);\n};\n\nRetryOperation.prototype.start = RetryOperation.prototype.try;\n\nRetryOperation.prototype.errors = function() {\n  return this._errors;\n};\n\nRetryOperation.prototype.attempts = function() {\n  return this._attempts;\n};\n\nRetryOperation.prototype.mainError = function() {\n  if (this._errors.length === 0) {\n    return null;\n  }\n\n  var counts = {};\n  var mainError = null;\n  var mainErrorCount = 0;\n\n  for (var i = 0; i < this._errors.length; i++) {\n    var error = this._errors[i];\n    var message = error.message;\n    var count = (counts[message] || 0) + 1;\n\n    counts[message] = count;\n\n    if (count >= mainErrorCount) {\n      mainError = error;\n      mainErrorCount = count;\n    }\n  }\n\n  return mainError;\n};\n"]}