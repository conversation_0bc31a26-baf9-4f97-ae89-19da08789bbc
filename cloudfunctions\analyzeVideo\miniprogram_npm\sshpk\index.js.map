{"version": 3, "sources": ["index.js", "key.js", "algs.js", "fingerprint.js", "errors.js", "private-key.js", "signature.js", "utils.js", "ssh-buffer.js", "dhe.js", "ed-compat.js", "formats/auto.js", "formats/pem.js", "formats/pkcs1.js", "formats/pkcs8.js", "formats/ssh-private.js", "formats/rfc4253.js", "formats/ssh.js", "formats/dnssec.js", "formats/putty.js", "certificate.js", "identity.js", "formats/openssh-cert.js", "formats/x509.js", "formats/x509-pem.js"], "names": [], "mappings": ";;;;;;;AAAA;AACA;AACA;AACA,ACHA;ADIA,ACHA;ADIA,ACHA;ACFA,AFMA,ACHA;ACFA,AFMA,ACHA;ACFA,AFMA,ACHA;ACFA,ACHA,AHSA,ACHA;ACFA,ACHA,AHSA,ACHA;ACFA,ACHA,AHSA,ACHA;ACFA,AENA,ADGA,AHSA,ACHA;ACFA,AENA,ADGA,AHSA,ACHA;ACFA,AENA,ADGA,AHSA,ACHA;ACFA,AENA,ADGA,AHSA,ACHA,AIZA;AHUA,AENA,ADGA,AHSA,ACHA,AIZA;AHUA,AENA,ADGA,AHSA,ACHA,AIZA;AHUA,AENA,ADGA,AHSA,ACHA,AIZA,ACHA;AJaA,AENA,ADGA,AHSA,ACHA,AIZA,ACHA;AJaA,AENA,ADGA,AHSA,ACHA,AIZA,ACHA;AJaA,AENA,ADGA,AHSA,ACHA,AIZA,ACHA,ACHA;ALgBA,AENA,ADGA,AHSA,ACHA,AIZA,ACHA,ACHA;ALgBA,AENA,ADGA,AHSA,ACHA,AIZA,ACHA,ACHA;ALgBA,AENA,ADGA,AHSA,ACHA,AIZA,ACHA,AENA,ADGA;ALgBA,AENA,ADGA,AHSA,ACHA,AIZA,ACHA,AENA,ADGA;ALgBA,AENA,ADGA,AHSA,ACHA,AIZA,ACHA,AENA,ADGA;ALgBA,AOrBA,ALeA,ADGA,AHSA,ACHA,AIZA,ACHA,AENA,ADGA;ALgBA,AOrBA,ALeA,ADGA,AHSA,ACHA,AIZA,ACHA,AENA,ADGA;ALgBA,AOrBA,ALeA,ADGA,AHSA,ACHA,AIZA,ACHA,AENA,ADGA;ALgBA,AOrBA,ACHA,ANkBA,ADGA,AHSA,ACHA,AIZA,ACHA,AENA,ADGA;ALgBA,AOrBA,ACHA,ANkBA,ADGA,AHSA,ACHA,AIZA,ACHA,AENA,ADGA;ALgBA,AOrBA,ACHA,ANkBA,ADGA,AHSA,ACHA,AIZA,ACHA,AENA,ADGA;ALgBA,AOrBA,ACHA,ANkBA,ADGA,AQxBA,AXiCA,ACHA,AIZA,ACHA,AENA,ADGA;ALgBA,AOrBA,ACHA,ANkBA,ADGA,AQxBA,AXiCA,ACHA,AIZA,ACHA,AENA,ADGA;ALgBA,AOrBA,ACHA,ANkBA,ADGA,AQxBA,AXiCA,ACHA,AIZA,ACHA,AENA,ADGA;ALgBA,AOrBA,ACHA,ANkBA,ADGA,AQxBA,ACHA,AZoCA,ACHA,AIZA,ACHA,AENA,ADGA;ALgBA,AOrBA,ACHA,ANkBA,ADGA,AQxBA,ACHA,AZoCA,ACHA,AIZA,ACHA,AENA,ADGA;ALgBA,AOrBA,ACHA,ANkBA,ADGA,AQxBA,ACHA,AZoCA,ACHA,AIZA,ACHA,AENA,ADGA;ALgBA,AOrBA,ACHA,ANkBA,ADGA,AQxBA,ACHA,ACHA,AbuCA,ACHA,AIZA,ACHA,AENA,ADGA;ALgBA,AOrBA,ACHA,ANkBA,ADGA,AQxBA,ACHA,ACHA,AbuCA,ACHA,AIZA,ACHA,AENA,ADGA;ALgBA,AOrBA,ACHA,ANkBA,ADGA,AQxBA,ACHA,ACHA,AZoCA,AIZA,ACHA,AENA,ADGA;ALgBA,AOrBA,ACHA,ANkBA,ADGA,AQxBA,ACHA,ACHA,ACHA,AbuCA,AIZA,ACHA,AENA,ADGA;ALgBA,AOrBA,ACHA,ANkBA,ADGA,AQxBA,ACHA,ACHA,ACHA,AbuCA,AIZA,ACHA,AENA,ADGA;ALgBA,AOrBA,ACHA,ANkBA,ADGA,AQxBA,ACHA,ACHA,ACHA,AbuCA,AIZA,ACHA,AENA,ADGA;ALgBA,AOrBA,ACHA,ANkBA,ADGA,AQxBA,ACHA,ACHA,ACHA,ACHA,Ad0CA,AIZA,ACHA,AENA,ADGA;ALgBA,AOrBA,ACHA,ANkBA,ADGA,AQxBA,ACHA,ACHA,ACHA,ACHA,Ad0CA,AIZA,ACHA,AENA,ADGA;ALgBA,AOrBA,ACHA,ANkBA,ADGA,AQxBA,ACHA,ACHA,ACHA,ACHA,Ad0CA,AIZA,ACHA,AENA,ADGA;ALgBA,AOrBA,ACHA,ANkBA,ADGA,AQxBA,ACHA,ACHA,ACHA,AENA,ADGA,Ad0CA,AIZA,ACHA,AENA,ADGA;ALgBA,AOrBA,ACHA,ANkBA,ADGA,AQxBA,ACHA,ACHA,ACHA,AENA,ADGA,Ad0CA,AIZA,ACHA,AENA,ADGA;ALgBA,AOrBA,ACHA,ANkBA,ADGA,AQxBA,ACHA,ACHA,ACHA,AENA,ADGA,Ad0CA,AIZA,ACHA,AENA,ADGA;ALgBA,AOrBA,ACHA,ANkBA,ADGA,AQxBA,ACHA,ACHA,ACHA,AENA,ADGA,AENA,AhBgDA,AIZA,ACHA,AENA,ADGA;ALgBA,AOrBA,ACHA,ANkBA,ADGA,AQxBA,ACHA,ACHA,ACHA,AENA,ADGA,AENA,AhBgDA,AIZA,ACHA,AENA,ADGA;ALgBA,AOrBA,ACHA,ANkBA,ADGA,AQxBA,ACHA,ACHA,ACHA,AENA,ADGA,AENA,AhBgDA,AIZA,ACHA,AENA,ADGA;ALgBA,AOrBA,ACHA,ANkBA,ADGA,AQxBA,AOrBA,ANkBA,ACHA,ACHA,AENA,ADGA,AENA,AhBgDA,AIZA,ACHA,AENA,ADGA;ALgBA,AOrBA,ACHA,ANkBA,ADGA,AQxBA,AOrBA,ANkBA,ACHA,ACHA,AENA,ADGA,AENA,AhBgDA,AIZA,ACHA,AENA,ADGA;ALgBA,AOrBA,ACHA,ANkBA,ADGA,AQxBA,AOrBA,ANkBA,ACHA,ACHA,AENA,ADGA,AENA,AhBgDA,AIZA,ACHA,AENA,ADGA;ALgBA,AOrBA,ACHA,ANkBA,ADGA,AQxBA,AOrBA,ANkBA,ACHA,ACHA,AKfA,AHSA,ADGA,AENA,AhBgDA,AIZA,ACHA,AENA,ADGA;ALgBA,AOrBA,ACHA,ANkBA,ADGA,AQxBA,AOrBA,ANkBA,ACHA,ACHA,AKfA,AHSA,ADGA,AENA,AhBgDA,AIZA,ACHA,AENA,ADGA;ALgBA,AOrBA,ACHA,ANkBA,ADGA,AQxBA,AOrBA,ANkBA,ACHA,ACHA,AKfA,AHSA,ADGA,AENA,AhBgDA,AIZA,ACHA,AENA,ADGA;ALgBA,AkBtDA,AXiCA,ACHA,ANkBA,ADGA,AQxBA,AOrBA,ANkBA,ACHA,ACHA,AKfA,AHSA,ADGA,AENA,AhBgDA,AIZA,ACHA,AENA,ADGA;ALgBA,AkBtDA,AXiCA,ACHA,ANkBA,ADGA,AQxBA,AOrBA,ANkBA,ACHA,ACHA,AKfA,AHSA,ADGA,AENA,AhBgDA,AIZA,ACHA,AENA,ADGA;ALgBA,AkBtDA,AXiCA,ACHA,ANkBA,ADGA,AQxBA,AOrBA,ANkBA,ACHA,ACHA,AKfA,AHSA,ADGA,AENA,AhBgDA,AIZA,ACHA,AENA,ADGA;ALgBA,AkBtDA,AXiCA,ACHA,ANkBA,ADGA,AQxBA,AOrBA,ANkBA,ACHA,ACHA,AKfA,AHSA,ADGA,AENA,AIZA,ApB4DA,AIZA,ACHA,AENA,ADGA;ALgBA,AkBtDA,AXiCA,ACHA,ANkBA,ADGA,AQxBA,AOrBA,ANkBA,ACHA,ACHA,AKfA,AHSA,ADGA,AENA,AIZA,ApB4DA,AIZA,ACHA,AENA,ADGA;ALgBA,AkBtDA,AXiCA,ACHA,ANkBA,ADGA,AQxBA,AOrBA,ANkBA,ACHA,ACHA,AKfA,AHSA,ADGA,AENA,AIZA,ApB4DA,AIZA,ACHA,AENA,ADGA;ALgBA,AkBtDA,AXiCA,ACHA,ANkBA,ADGA,AQxBA,AOrBA,AIZA,AV8BA,ACHA,ACHA,AKfA,AHSA,ADGA,AENA,AIZA,ApB4DA,AIZA,ACHA,AENA,ADGA;ALgBA,AkBtDA,AXiCA,ACHA,ANkBA,ADGA,AQxBA,AOrBA,AIZA,AV8BA,ACHA,ACHA,AKfA,AHSA,ADGA,AENA,AIZA,ApB4DA,AIZA,ACHA,AENA,ADGA;ALgBA,AkBtDA,AXiCA,ACHA,ANkBA,ADGA,AQxBA,AOrBA,AIZA,AV8BA,ACHA,ACHA,AKfA,AHSA,ADGA,AENA,AIZA,ApB4DA,AIZA,ACHA,AENA,ADGA;ALgBA,AkBtDA,AXiCA,ACHA,ANkBA,ADGA,AQxBA,AOrBA,AIZA,AV8BA,ACHA,ACHA,AKfA,AHSA,ADGA,AENA,AMlBA,AFMA,ApB4DA,AIZA,ACHA,AENA,ADGA;ALgBA,AkBtDA,AXiCA,ACHA,ANkBA,ADGA,AQxBA,AOrBA,AIZA,AV8BA,ACHA,ACHA,AKfA,AHSA,ADGA,AENA,AMlBA,AFMA,ApB4DA,AIZA,ACHA,AENA,ADGA;ALgBA,AkBtDA,AXiCA,ACHA,ANkBA,ADGA,AQxBA,AOrBA,AIZA,AV8BA,ACHA,ACHA,AKfA,AHSA,ADGA,AENA,AMlBA,AFMA,ApB4DA,AIZA,ACHA,AENA,ADGA;ALgBA,AkBtDA,AXiCA,ACHA,ANkBA,ADGA,AQxBA,AOrBA,AIZA,AV8BA,ACHA,ACHA,AKfA,AHSA,ADGA,AENA,AOrBA,ADGA,AFMA,ApB4DA,AIZA,ACHA,AENA,ADGA;ALgBA,AkBtDA,AXiCA,ACHA,ANkBA,ADGA,AQxBA,AOrBA,AIZA,AV8BA,ACHA,ACHA,AKfA,AHSA,ADGA,AENA,AOrBA,ADGA,AFMA,ApB4DA,AIZA,ACHA,AENA,ADGA;ALgBA,AkBtDA,AXiCA,ACHA,ANkBA,ADGA,AQxBA,AOrBA,AIZA,AV8BA,ACHA,ACHA,AKfA,AHSA,ADGA,AENA,AOrBA,ADGA,AFMA,ApB4DA,AIZA,ACHA,AENA,ADGA;ALgBA,AkBtDA,AXiCA,ACHA,ANkBA,ADGA,AQxBA,AOrBA,AIZA,AV8BA,ACHA,ACHA,AKfA,AHSA,ADGA,AENA,AOrBA,ADGA,AFMA,ApB4DA,AIZA,ACHA,AENA,ADGA;ALgBA,AkBtDA,AXiCA,ACHA,ANkBA,ADGA,AQxBA,AOrBA,AIZA,AV8BA,ACHA,ACHA,AKfA,AHSA,ADGA,AENA,AOrBA,ADGA,AFMA,ApB4DA,AIZA,ACHA,AENA,ADGA;ALgBA,AkBtDA,AXiCA,ACHA,ANkBA,ADGA,AQxBA,AOrBA,AIZA,AV8BA,ACHA,ACHA,AKfA,AHSA,ADGA,AENA,AOrBA,ADGA,AFMA,ApB4DA,AIZA,ACHA,AENA,ADGA;ALgBA,AkBtDA,AXiCA,ACHA,ANkBA,ADGA,AQxBA,AOrBA,AIZA,AV8BA,ACHA,ACHA,AKfA,AHSA,ADGA,AENA,AOrBA,ADGA,AFMA,ApB4DA,AIZA,ACHA,AENA,ADGA;ALgBA,AkBtDA,AXiCA,ACHA,ANkBA,ADGA,AQxBA,AOrBA,AIZA,AV8BA,ACHA,ACHA,AKfA,AHSA,ADGA,AENA,AOrBA,ADGA,AFMA,ApB4DA,AIZA,ACHA,AENA,ADGA;ALgBA,AkBtDA,AXiCA,ACHA,ANkBA,ADGA,AQxBA,AOrBA,AIZA,AV8BA,ACHA,ACHA,AKfA,AHSA,ADGA,AENA,AOrBA,ADGA,AFMA,ApB4DA,AIZA,ACHA,AENA,ADGA;ALgBA,AkBtDA,AXiCA,ACHA,ANkBA,ADGA,AQxBA,AOrBA,AIZA,AV8BA,ACHA,ACHA,AKfA,AHSA,ADGA,AENA,AOrBA,ADGA,AFMA,ApB4DA,AIZA,ACHA,AENA,ADGA;ALgBA,AkBtDA,AXiCA,ACHA,ANkBA,ADGA,AQxBA,AOrBA,AIZA,AV8BA,ACHA,ACHA,AKfA,AHSA,ADGA,AENA,AOrBA,ADGA,AFMA,ApB4DA,AIZA,ACHA,AENA,ADGA;ALgBA,AkBtDA,AXiCA,ACHA,ANkBA,ADGA,AQxBA,AOrBA,AIZA,AV8BA,ACHA,ACHA,AKfA,AHSA,ADGA,AENA,AOrBA,ADGA,AFMA,ApB4DA,AIZA,ACHA,AENA,ADGA;ALgBA,AkBtDA,AXiCA,ACHA,ANkBA,ADGA,AQxBA,AOrBA,AIZA,AV8BA,ACHA,ACHA,AKfA,AHSA,ADGA,AENA,AOrBA,ADGA,AFMA,ApB4DA,AIZA,ACHA,AENA,ADGA;ALgBA,AkBtDA,AXiCA,ACHA,ANkBA,ADGA,AQxBA,AOrBA,AIZA,AV8BA,ACHA,ACHA,AKfA,AHSA,ADGA,AENA,AOrBA,ADGA,AFMA,ApB4DA,AIZA,ACHA,AENA,ADGA;ALgBA,AkBtDA,AXiCA,ACHA,ANkBA,ADGA,AQxBA,AOrBA,AIZA,AV8BA,ACHA,ACHA,AKfA,AHSA,ADGA,AENA,AOrBA,ADGA,AFMA,ApB4DA,AIZA,ACHA,AENA,ADGA;ALgBA,AkBtDA,AXiCA,ACHA,ANkBA,ADGA,AQxBA,AOrBA,AIZA,AV8BA,ACHA,ACHA,AKfA,AHSA,ADGA,AENA,AOrBA,ADGA,AFMA,ApB4DA,AIZA,ACHA,AENA,ADGA;ALgBA,AkBtDA,AXiCA,ACHA,ANkBA,ADGA,AQxBA,AOrBA,AIZA,AV8BA,ACHA,ACHA,AKfA,AHSA,ADGA,AENA,AOrBA,ADGA,AFMA,ApB4DA,AIZA,ACHA,AENA,ADGA;ALgBA,AkBtDA,AXiCA,ACHA,ANkBA,ADGA,AQxBA,AOrBA,AIZA,AV8BA,ACHA,ACHA,AKfA,AHSA,ADGA,AENA,AOrBA,ADGA,AFMA,ApB4DA,AIZA,ACHA,AENA,ADGA;ALgBA,AkBtDA,AXiCA,ACHA,ANkBA,ADGA,AQxBA,AOrBA,AIZA,AV8BA,ACHA,ACHA,AKfA,AHSA,ADGA,AENA,AOrBA,ADGA,AFMA,ApB4DA,AIZA,ACHA,AENA,ADGA;ALgBA,AkBtDA,AXiCA,ACHA,ANkBA,ADGA,AQxBA,AOrBA,AIZA,AV8BA,ACHA,ACHA,AKfA,AHSA,ADGA,AENA,AOrBA,ADGA,AFMA,ApB4DA,AIZA,ACHA,AENA,ADGA;ALgBA,AkBtDA,AXiCA,ACHA,ANkBA,ADGA,AQxBA,AOrBA,AIZA,AV8BA,ACHA,ACHA,AKfA,AHSA,ADGA,AENA,AOrBA,ADGA,AFMA,ApB4DA,AIZA,ACHA,AENA,ADGA;ALgBA,AkBtDA,AXiCA,ACHA,ANkBA,ADGA,AQxBA,AOrBA,AIZA,AV8BA,ACHA,ACHA,AKfA,AHSA,ADGA,AENA,AOrBA,ADGA,AFMA,ApB4DA,AIZA,ACHA,AENA,ADGA;ALgBA,AkBtDA,AXiCA,ACHA,ANkBA,ADGA,AQxBA,AOrBA,AIZA,AV8BA,ACHA,ACHA,AKfA,AHSA,ADGA,AENA,AOrBA,ADGA,AFMA,ApB4DA,AIZA,ACHA,AENA,ADGA;ALgBA,AkBtDA,AXiCA,ACHA,ANkBA,ADGA,AQxBA,AOrBA,AIZA,AV8BA,ACHA,ACHA,AKfA,AHSA,ADGA,AENA,AOrBA,ADGA,AFMA,ApB4DA,AIZA,ACHA,AENA,ADGA;ALgBA,AkBtDA,AXiCA,ACHA,ANkBA,ADGA,AQxBA,AOrBA,AIZA,AV8BA,ACHA,ACHA,AKfA,AHSA,ADGA,AENA,AOrBA,ADGA,AFMA,ApB4DA,AIZA,ACHA,AENA,ADGA;ALgBA,AkBtDA,AXiCA,ACHA,APqBA,AQxBA,AOrBA,AIZA,AV8BA,ACHA,ACHA,AKfA,AHSA,ADGA,AENA,AOrBA,ADGA,AFMA,ApB4DA,AIZA,ACHA,AENA,ADGA;ALgBA,AkBtDA,AXiCA,ACHA,APqBA,AQxBA,AOrBA,AIZA,AV8BA,ACHA,ACHA,AKfA,AHSA,ADGA,AENA,AOrBA,ADGA,AFMA,ApB4DA,AIZA,ACHA,AENA,ADGA;ALgBA,AkBtDA,AXiCA,ACHA,APqBA,AQxBA,AOrBA,AIZA,AV8BA,ACHA,ACHA,AKfA,AHSA,ADGA,AENA,AOrBA,ADGA,AFMA,ApB4DA,AIZA,ACHA,AENA,ADGA;ALgBA,AkBtDA,AXiCA,ACHA,APqBA,AQxBA,AOrBA,AIZA,AV8BA,ACHA,ACHA,AKfA,AHSA,ADGA,AENA,AOrBA,ADGA,AFMA,ApB4DA,AIZA,ACHA,AENA,ADGA;ALgBA,AkBtDA,AXiCA,ACHA,APqBA,AQxBA,AOrBA,AIZA,AV8BA,ACHA,ACHA,AKfA,AHSA,ADGA,AENA,AOrBA,ADGA,AFMA,ApB4DA,AIZA,ACHA,AENA,ADGA;ALgBA,AkBtDA,AXiCA,ACHA,APqBA,AQxBA,AOrBA,AIZA,AV8BA,ACHA,ACHA,AKfA,AHSA,ADGA,AENA,AOrBA,ADGA,AFMA,ApB4DA,AIZA,ACHA,AENA,ADGA;ALgBA,AkBtDA,AXiCA,ACHA,APqBA,AQxBA,AOrBA,AIZA,AV8BA,ACHA,ACHA,AKfA,AHSA,ADGA,AENA,AOrBA,ADGA,AFMA,ApB4DA,AIZA,ACHA,AENA,ADGA;ALgBA,AkBtDA,AXiCA,ACHA,APqBA,AQxBA,AOrBA,AIZA,AV8BA,ACHA,ACHA,AKfA,AHSA,ADGA,AENA,AOrBA,ADGA,AFMA,ApB4DA,AIZA,ACHA,AENA,ADGA;ALgBA,AkBtDA,AXiCA,ACHA,APqBA,AQxBA,AOrBA,AIZA,AV8BA,ACHA,ACHA,AKfA,AHSA,ADGA,AENA,AOrBA,ADGA,AFMA,ApB4DA,AIZA,ACHA,AENA,ADGA;ALgBA,AkBtDA,AXiCA,ACHA,APqBA,AQxBA,AOrBA,AIZA,AV8BA,ACHA,ACHA,AKfA,AHSA,ADGA,AENA,AOrBA,ADGA,AFMA,ApB4DA,AIZA,ACHA,AENA,ADGA;ALgBA,AkBtDA,AXiCA,ACHA,APqBA,AQxBA,AOrBA,AIZA,AV8BA,ACHA,ACHA,AKfA,AHSA,ADGA,AENA,AOrBA,ADGA,AFMA,ApB4DA,AIZA,ACHA,AENA,ADGA;ALgBA,AkBtDA,AXiCA,ACHA,APqBA,AQxBA,AOrBA,AIZA,AV8BA,ACHA,ACHA,AKfA,AHSA,ADGA,AENA,AOrBA,ADGA,AFMA,ApB4DA,AIZA,ACHA,AENA,ADGA;ALgBA,AkBtDA,AXiCA,ACHA,APqBA,AQxBA,AOrBA,AIZA,AV8BA,ACHA,ACHA,AKfA,AHSA,ADGA,AENA,AOrBA,ADGA,AFMA,ApB4DA,AIZA,ACHA,AENA,ADGA;ALgBA,AkBtDA,AXiCA,ACHA,APqBA,AQxBA,AOrBA,AIZA,AV8BA,ACHA,ACHA,AKfA,AHSA,ADGA,AENA,AOrBA,ADGA,AFMA,ApB4DA,AIZA,ACHA,AENA,ADGA;ALgBA,AkBtDA,AXiCA,ACHA,APqBA,AQxBA,AOrBA,AIZA,AV8BA,ACHA,ACHA,AKfA,AHSA,ADGA,AENA,AOrBA,ADGA,AFMA,ApB4DA,AIZA,ACHA,AENA,ADGA;ALgBA,AkBtDA,AXiCA,ACHA,APqBA,AQxBA,AOrBA,AIZA,AV8BA,ACHA,ACHA,AKfA,AHSA,ADGA,AENA,AOrBA,ADGA,AFMA,ApB4DA,AIZA,ACHA,AENA,ADGA;ALgBA,AkBtDA,AXiCA,ACHA,APqBA,AQxBA,AOrBA,AIZA,AV8BA,ACHA,ACHA,AKfA,AHSA,ADGA,AENA,AOrBA,ADGA,AFMA,ApB4DA,AIZA,ACHA,AENA,ADGA;ALgBA,AkBtDA,AXiCA,ACHA,APqBA,AQxBA,AOrBA,AIZA,AV8BA,ACHA,ACHA,AKfA,AHSA,ADGA,AENA,AOrBA,ADGA,AFMA,ApB4DA,AIZA,ACHA,AENA,ADGA;ALgBA,AkBtDA,AXiCA,ACHA,APqBA,AQxBA,AOrBA,AIZA,AV8BA,ACHA,ACHA,AKfA,AHSA,ADGA,AENA,AOrBA,ADGA,AFMA,ApB4DA,AIZA,ACHA,AENA,ADGA;ALgBA,AkBtDA,AXiCA,ACHA,APqBA,AQxBA,AOrBA,AIZA,AV8BA,ACHA,ACHA,AKfA,AHSA,ADGA,AENA,AOrBA,ADGA,AFMA,ApB4DA,AIZA,ACHA,AENA,ADGA;ALgBA,AkBtDA,AXiCA,ACHA,APqBA,AQxBA,AOrBA,AIZA,AV8BA,ACHA,ACHA,AKfA,AHSA,ADGA,AENA,AOrBA,ADGA,AFMA,ApB4DA,AIZA,ACHA,AENA,ADGA;ALgBA,AkBtDA,AXiCA,ACHA,APqBA,AQxBA,AOrBA,AIZA,AV8BA,ACHA,ACHA,AKfA,AHSA,ADGA,AENA,AOrBA,ADGA,AFMA,ApB4DA,AIZA,ACHA,AENA,ADGA;ALgBA,AkBtDA,AXiCA,ACHA,APqBA,AQxBA,AOrBA,AIZA,AV8BA,ACHA,ACHA,AKfA,AHSA,ADGA,AENA,AOrBA,ADGA,AFMA,ApB4DA,AIZA,ACHA,AENA,ADGA;ALgBA,AkBtDA,AXiCA,ACHA,APqBA,AQxBA,AOrBA,AIZA,AV8BA,ACHA,ACHA,AKfA,AHSA,ADGA,AENA,AOrBA,ADGA,AFMA,ApB4DA,AIZA,ACHA,AENA,ADGA;ALgBA,AkBtDA,AXiCA,ACHA,APqBA,AQxBA,AOrBA,AIZA,AV8BA,ACHA,ACHA,AKfA,AHSA,ADGA,AENA,AOrBA,ADGA,AFMA,ApB4DA,AIZA,ACHA,AENA,ADGA;ALgBA,AkBtDA,AXiCA,ACHA,APqBA,AQxBA,AOrBA,AIZA,AV8BA,ACHA,ACHA,AKfA,AHSA,ADGA,AENA,AOrBA,ADGA,AFMA,ApB4DA,AIZA,ACHA,AENA,ADGA;ALgBA,AkBtDA,AXiCA,ANkBA,AQxBA,AOrBA,AIZA,AV8BA,ACHA,ACHA,AKfA,AHSA,ADGA,AENA,AOrBA,ADGA,AFMA,ApB4DA,AIZA,ACHA,AENA,ADGA;ALgBA,AkBtDA,AXiCA,ANkBA,AQxBA,AOrBA,AIZA,AV8BA,ACHA,ACHA,AKfA,AHSA,ADGA,AENA,AOrBA,ADGA,AFMA,ApB4DA,AIZA,ACHA,AENA,ADGA;ALgBA,AkBtDA,AXiCA,ANkBA,AQxBA,AOrBA,AIZA,AV8BA,ACHA,ACHA,AKfA,AHSA,ADGA,AENA,AOrBA,ADGA,AFMA,ApB4DA,AIZA,ACHA,AENA,ADGA;ALgBA,AkBtDA,AXiCA,ANkBA,AQxBA,AOrBA,AIZA,AV8BA,ACHA,ACHA,AKfA,AHSA,ADGA,AENA,AOrBA,ADGA,AFMA,ApB4DA,AIZA,ACHA,AENA,ADGA;ALgBA,AkBtDA,AXiCA,ANkBA,AQxBA,AOrBA,AIZA,AV8BA,ACHA,ACHA,AKfA,AHSA,ADGA,AENA,AOrBA,ADGA,AFMA,ApB4DA,AIZA,ACHA,AENA,ADGA;ALgBA,AkBtDA,AXiCA,ANkBA,AQxBA,AOrBA,AIZA,AV8BA,ACHA,ACHA,AKfA,AHSA,ADGA,AENA,AOrBA,ADGA,AFMA,ApB4DA,AIZA,ACHA,AENA,ADGA;ALgBA,AkBtDA,AXiCA,ANkBA,AQxBA,AOrBA,AIZA,AV8BA,ACHA,ACHA,AKfA,AHSA,ADGA,AENA,AOrBA,ADGA,AFMA,ApB4DA,AIZA,ACHA,AENA,ADGA;ALgBA,AkBtDA,AXiCA,ANkBA,AQxBA,AOrBA,AIZA,AV8BA,ACHA,ACHA,AKfA,AHSA,ADGA,AENA,AOrBA,ADGA,AFMA,ApB4DA,AIZA,ACHA,AENA,ADGA;ALgBA,AkBtDA,AXiCA,ANkBA,AQxBA,AOrBA,AIZA,AV8BA,ACHA,ACHA,AKfA,AHSA,ADGA,AENA,AOrBA,ADGA,AFMA,ApB4DA,AIZA,ACHA,AENA,ADGA;ALgBA,AkBtDA,AXiCA,ANkBA,AQxBA,AOrBA,AIZA,AV8BA,ACHA,ACHA,AKfA,AHSA,ADGA,AENA,AOrBA,ADGA,AFMA,ApB4DA,AIZA,ACHA,AENA,ADGA;ALgBA,AkBtDA,AXiCA,ANkBA,AQxBA,AOrBA,AIZA,AV8BA,ACHA,ACHA,AKfA,AHSA,ADGA,AENA,AOrBA,ADGA,AFMA,ApB4DA,AIZA,ACHA,AENA,ADGA;ALgBA,AkBtDA,AXiCA,ANkBA,AQxBA,AOrBA,AIZA,AV8BA,ACHA,ACHA,AKfA,AHSA,ADGA,AENA,AOrBA,ADGA,AFMA,ApB4DA,AIZA,ACHA,AENA,ADGA;ALgBA,AkBtDA,AXiCA,ANkBA,AQxBA,AOrBA,AIZA,AV8BA,ACHA,ACHA,AKfA,AHSA,ADGA,AENA,AOrBA,ADGA,AFMA,ApB4DA,AIZA,ACHA,AENA,ADGA;ALgBA,AkBtDA,AXiCA,ANkBA,AQxBA,AOrBA,AIZA,AV8BA,ACHA,ACHA,AKfA,AHSA,ADGA,AENA,AOrBA,ADGA,AFMA,ApB4DA,AIZA,ACHA,AENA,ADGA;ALgBA,AkBtDA,AXiCA,ANkBA,AQxBA,AOrBA,AIZA,AV8BA,ACHA,ACHA,AKfA,AHSA,ADGA,AENA,AOrBA,ADGA,AFMA,ApB4DA,AIZA,ACHA,AENA,ADGA;ALgBA,AkBtDA,AXiCA,ANkBA,AQxBA,AOrBA,AIZA,AV8BA,ACHA,ACHA,AKfA,AHSA,ADGA,AENA,AOrBA,ADGA,AFMA,ApB4DA,AIZA,ACHA,AENA,ADGA;ALgBA,AkBtDA,AXiCA,ANkBA,AQxBA,AOrBA,AIZA,AV8BA,ACHA,ACHA,AKfA,AHSA,ADGA,AENA,AOrBA,ADGA,AFMA,ApB4DA,AIZA,ACHA,AENA,ADGA;ALgBA,AkBtDA,AXiCA,ANkBA,AQxBA,AOrBA,AIZA,AV8BA,ACHA,ACHA,AKfA,AHSA,ADGA,AENA,AOrBA,ADGA,AFMA,ApB4DA,AIZA,ACHA,AENA,ADGA;ALgBA,AkBtDA,AXiCA,ANkBA,AQxBA,AOrBA,AIZA,AV8BA,ACHA,ACHA,AKfA,AHSA,ADGA,AENA,AOrBA,ADGA,AFMA,ApB4DA,AIZA,ACHA,AENA,ADGA;ALgBA,AkBtDA,AXiCA,ANkBA,AQxBA,AOrBA,AIZA,AV8BA,ACHA,ACHA,AKfA,AHSA,ADGA,AENA,AOrBA,ADGA,AFMA,ApB4DA,AIZA,ACHA,AENA,ADGA;ALgBA,AkBtDA,AXiCA,ANkBA,AQxBA,AOrBA,AIZA,AV8BA,ACHA,ACHA,AKfA,AHSA,ADGA,AENA,AOrBA,ADGA,AFMA,ApB4DA,AIZA,ACHA,AENA,ADGA;ALgBA,AkBtDA,AXiCA,ANkBA,AQxBA,AOrBA,AIZA,AV8BA,ACHA,ACHA,AKfA,AHSA,ADGA,AENA,AOrBA,ADGA,AFMA,ApB4DA,AIZA,ACHA,AENA,ADGA;ALgBA,AkBtDA,AXiCA,ANkBA,AQxBA,AOrBA,AIZA,AV8BA,ACHA,ACHA,AKfA,AHSA,ADGA,AENA,AOrBA,ADGA,AFMA,ApB4DA,AIZA,ACHA,AENA,ADGA;ALgBA,AkBtDA,AXiCA,ANkBA,AQxBA,AOrBA,AIZA,AV8BA,ACHA,ACHA,AKfA,AHSA,ADGA,AENA,AOrBA,ADGA,AFMA,ApB4DA,AIZA,ACHA,AENA,ADGA;ALgBA,AkBtDA,AXiCA,ANkBA,AQxBA,AOrBA,AIZA,AV8BA,ACHA,ACHA,AKfA,AHSA,ADGA,AENA,AOrBA,ADGA,AFMA,ApB4DA,AIZA,ACHA,AENA,ADGA;ALgBA,AkBtDA,AXiCA,ANkBA,AQxBA,AOrBA,AIZA,AV8BA,ACHA,ACHA,AKfA,AHSA,ADGA,AENA,AOrBA,ADGA,AFMA,ApB4DA,AIZA,ACHA,AENA,ADGA;ALgBA,AkBtDA,AXiCA,ANkBA,AQxBA,AOrBA,AIZA,AV8BA,ACHA,ACHA,AKfA,AHSA,ADGA,AENA,AOrBA,ADGA,AFMA,ApB4DA,AIZA,ACHA,AENA,ADGA;ALgBA,AkBtDA,AXiCA,ANkBA,AQxBA,AOrBA,AIZA,AV8BA,ACHA,ACHA,AKfA,AHSA,ADGA,AENA,AOrBA,ADGA,AFMA,ApB4DA,AIZA,ACHA,AENA,ADGA;ALgBA,AkBtDA,AXiCA,ANkBA,AQxBA,AOrBA,AIZA,AV8BA,ACHA,ACHA,AKfA,AHSA,ADGA,AENA,AOrBA,ADGA,AFMA,ApB4DA,AIZA,ACHA,AENA,ADGA;ALgBA,AkBtDA,AXiCA,ANkBA,AQxBA,AOrBA,AIZA,AV8BA,ACHA,ACHA,AKfA,AHSA,ADGA,AENA,AOrBA,ADGA,AFMA,ApB4DA,AIZA,ACHA,AENA,ADGA;ALgBA,AkBtDA,AXiCA,ANkBA,AQxBA,AOrBA,AIZA,AV8BA,ACHA,ACHA,AKfA,AHSA,ADGA,AENA,AOrBA,ADGA,AFMA,ApB4DA,AIZA,ACHA,AENA,ADGA;ALgBA,AkBtDA,AXiCA,ANkBA,AQxBA,AOrBA,AIZA,AV8BA,ACHA,ACHA,AKfA,AHSA,ADGA,AENA,AOrBA,ADGA,AFMA,ApB4DA,AIZA,ACHA,AENA,ADGA;ALgBA,AkBtDA,AXiCA,ANkBA,AQxBA,AOrBA,AIZA,AV8BA,ACHA,ACHA,AKfA,AHSA,ADGA,AENA,AOrBA,ADGA,AFMA,ApB4DA,AIZA,ACHA,AENA,ADGA;ALgBA,AkBtDA,AXiCA,ANkBA,AQxBA,AOrBA,AIZA,AV8BA,ACHA,ACHA,AKfA,AHSA,ADGA,AENA,AOrBA,ADGA,AFMA,ApB4DA,AIZA,ACHA,AENA,ADGA;ALgBA,AkBtDA,AXiCA,ANkBA,AQxBA,AOrBA,AIZA,AV8BA,ACHA,ACHA,AKfA,AHSA,ADGA,AENA,AOrBA,ADGA,AFMA,ApB4DA,AIZA,ACHA,AENA,ADGA;ALgBA,AkBtDA,AXiCA,ANkBA,Ae7CA,AIZA,AV8BA,ACHA,ACHA,AKfA,AHSA,ADGA,AENA,AOrBA,ADGA,AFMA,ApB4DA,AIZA,ACHA,AENA,ADGA;ALgBA,AkBtDA,AXiCA,ANkBA,Ae7CA,AIZA,AV8BA,ACHA,ACHA,AKfA,AHSA,ADGA,AENA,AOrBA,ADGA,AFMA,ApB4DA,AIZA,ACHA,AENA,ADGA;ALgBA,AkBtDA,AXiCA,ANkBA,Ae7CA,AIZA,AV8BA,ACHA,ACHA,AKfA,AHSA,ADGA,AENA,AOrBA,ADGA,AFMA,ApB4DA,AIZA,ACHA,AENA,ADGA;ALgBA,AkBtDA,AXiCA,ANkBA,Ae7CA,AIZA,AV8BA,ACHA,ACHA,AKfA,AHSA,ADGA,AENA,AMlBA,AFMA,ApB4DA,AIZA,ACHA,AENA,ADGA;ALgBA,AkBtDA,AXiCA,ANkBA,Ae7CA,AIZA,AV8BA,ACHA,ACHA,AKfA,AHSA,ADGA,AENA,AMlBA,AFMA,ApB4DA,AIZA,ACHA,AENA,ADGA;ALgBA,AkBtDA,AXiCA,ANkBA,Ae7CA,AIZA,AV8BA,ACHA,ACHA,AKfA,AHSA,ADGA,AENA,AMlBA,AFMA,ApB4DA,AIZA,ACHA,AENA,ADGA;ALgBA,AkBtDA,AXiCA,ANkBA,Ae7CA,AIZA,AV8BA,ACHA,ACHA,AKfA,AHSA,ADGA,AENA,AMlBA,AFMA,ApB4DA,AIZA,ACHA,AENA,ADGA;ALgBA,AkBtDA,AXiCA,ANkBA,Ae7CA,AIZA,AV8BA,ACHA,ACHA,AKfA,AHSA,ADGA,AENA,AMlBA,AFMA,ApB4DA,AIZA,ACHA,AENA,ADGA;ALgBA,AkBtDA,AXiCA,ANkBA,Ae7CA,AIZA,AV8BA,ACHA,ACHA,AKfA,AHSA,ADGA,AENA,AMlBA,AFMA,ApB4DA,AIZA,ACHA,AENA,ADGA;ALgBA,AkBtDA,AXiCA,ANkBA,Ae7CA,AIZA,AV8BA,ACHA,ACHA,AKfA,AHSA,ADGA,AQxBA,AFMA,ApB4DA,AIZA,ACHA,AENA,ADGA;ALgBA,AkBtDA,AXiCA,ANkBA,Ae7CA,AIZA,AV8BA,ACHA,ACHA,AKfA,AHSA,ADGA,AQxBA,AFMA,ApB4DA,AIZA,ACHA,AENA,ADGA;ALgBA,AkBtDA,AXiCA,ANkBA,Ae7CA,AIZA,AV8BA,ACHA,ACHA,AKfA,AHSA,ADGA,AQxBA,AFMA,ApB4DA,AIZA,ACHA,AENA,ADGA;ALgBA,AkBtDA,AXiCA,ANkBA,Ae7CA,AIZA,AV8BA,ACHA,ACHA,AKfA,AHSA,ADGA,AQxBA,AFMA,ApB4DA,AIZA,ACHA,AENA,ADGA;ALgBA,AkBtDA,AXiCA,ANkBA,Ae7CA,AIZA,AV8BA,ACHA,ACHA,AKfA,AHSA,ADGA,AQxBA,AFMA,ApB4DA,AIZA,ACHA,AENA,ADGA;ALgBA,AkBtDA,AXiCA,ANkBA,Ae7CA,AIZA,AV8BA,ACHA,ACHA,AKfA,AHSA,ADGA,AQxBA,AFMA,ApB4DA,AIZA,ACHA,AENA,ADGA;ALgBA,AkBtDA,AXiCA,ANkBA,Ae7CA,AIZA,AV8BA,ACHA,ACHA,AKfA,AHSA,ADGA,AQxBA,AFMA,ApB4DA,AIZA,ACHA,AENA,ADGA;ALgBA,AkBtDA,AXiCA,ANkBA,Ae7CA,AIZA,AV8BA,ACHA,ACHA,AKfA,AHSA,ADGA,AQxBA,AFMA,ApB4DA,AIZA,ACHA,ACHA;AatCA,AXiCA,ANkBA,Ae7CA,AIZA,AV8BA,ACHA,ACHA,AKfA,AHSA,ADGA,AQxBA,AFMA,ApB4DA,AIZA,ACHA,ACHA;AatCA,AXiCA,ANkBA,Ae7CA,AIZA,AV8BA,ACHA,ACHA,AKfA,AHSA,ADGA,AQxBA,AFMA,ApB4DA,AIZA,ACHA,ACHA;AatCA,AXiCA,ANkBA,Ae7CA,AIZA,AV8BA,ACHA,ACHA,AKfA,AHSA,ADGA,AQxBA,AFMA,ApB4DA,AIZA,ACHA,ACHA;AatCA,AXiCA,ANkBA,Ae7CA,AIZA,AV8BA,ACHA,ACHA,AKfA,AHSA,ADGA,AQxBA,AFMA,ApB4DA,AIZA,ACHA,ACHA;AatCA,AXiCA,ANkBA,Ae7CA,AIZA,AV8BA,ACHA,ACHA,AKfA,AHSA,ADGA,AQxBA,AFMA,ApB4DA,AIZA,ACHA,ACHA;AatCA,AXiCA,ANkBA,Ae7CA,AIZA,AV8BA,ACHA,ACHA,AKfA,AHSA,ADGA,AQxBA,AFMA,ApB4DA,AIZA,ACHA,ACHA;AatCA,AXiCA,ANkBA,Ae7CA,AIZA,AV8BA,ACHA,ACHA,AKfA,AHSA,ADGA,AQxBA,AFMA,ApB4DA,AIZA,ACHA,ACHA;AatCA,AXiCA,ANkBA,Ae7CA,AIZA,AV8BA,ACHA,ACHA,AKfA,AHSA,ADGA,AQxBA,AFMA,ApB4DA,AIZA,ACHA,ACHA;AatCA,AXiCA,ANkBA,Ae7CA,AIZA,AV8BA,ACHA,ACHA,AKfA,AHSA,ADGA,AQxBA,AFMA,ApB4DA,AIZA,ACHA,ACHA;AatCA,AXiCA,ANkBA,Ae7CA,AIZA,AV8BA,ACHA,ACHA,AKfA,AHSA,ADGA,AQxBA,AFMA,ApB4DA,AIZA,ACHA,ACHA;AatCA,AXiCA,ANkBA,Ae7CA,AIZA,AV8BA,ACHA,ACHA,AKfA,AHSA,ADGA,AQxBA,AFMA,ApB4DA,AIZA,ACHA,ACHA;AatCA,AXiCA,ANkBA,Ae7CA,AIZA,AV8BA,ACHA,ACHA,AKfA,AHSA,ADGA,AQxBA,AFMA,ApB4DA,AIZA,ACHA,ACHA;AatCA,AXiCA,ANkBA,Ae7CA,AIZA,AV8BA,ACHA,ACHA,AKfA,AHSA,ADGA,AQxBA,AFMA,ApB4DA,AIZA,ACHA,ACHA;AatCA,AXiCA,ANkBA,Ae7CA,AIZA,AV8BA,ACHA,ACHA,AKfA,AHSA,ADGA,AQxBA,AFMA,ApB4DA,AIZA,ACHA,ACHA;AatCA,AXiCA,ANkBA,Ae7CA,AIZA,AV8BA,ACHA,ACHA,AKfA,AHSA,ADGA,AQxBA,AFMA,ApB4DA,AIZA,ACHA,ACHA;AatCA,AXiCA,ANkBA,Ae7CA,AIZA,AV8BA,ACHA,ACHA,AKfA,AHSA,ADGA,AQxBA,AFMA,ApB4DA,AIZA,ACHA,ACHA;AatCA,AXiCA,ANkBA,Ae7CA,AIZA,AV8BA,ACHA,ACHA,AKfA,AHSA,ADGA,AQxBA,AFMA,ApB4DA,AIZA,ACHA,ACHA;AatCA,AXiCA,ANkBA,Ae7CA,AIZA,AV8BA,ACHA,ACHA,AKfA,AHSA,ADGA,AQxBA,AFMA,ApB4DA,AIZA,ACHA,ACHA;AatCA,AXiCA,ANkBA,Ae7CA,AIZA,AV8BA,ACHA,ACHA,AKfA,AHSA,ADGA,AQxBA,AFMA,ApB4DA,AIZA,ACHA,ACHA;AatCA,AXiCA,ANkBA,Ae7CA,AIZA,AV8BA,ACHA,ACHA,AKfA,AHSA,ADGA,AQxBA,AFMA,ApB4DA,AIZA,ACHA,ACHA;AatCA,AXiCA,ANkBA,Ae7CA,AIZA,AV8BA,ACHA,ACHA,AKfA,AHSA,ADGA,AQxBA,AFMA,ApB4DA,AIZA,ACHA,ACHA;AatCA,AXiCA,ANkBA,Ae7CA,AIZA,AV8BA,ACHA,ACHA,AKfA,AHSA,ADGA,AQxBA,AFMA,ApB4DA,AIZA,ACHA,ACHA;AatCA,AXiCA,ANkBA,Ae7CA,AIZA,AV8BA,ACHA,ACHA,AKfA,AHSA,ADGA,AQxBA,AFMA,ApB4DA,AIZA,ACHA,ACHA;AatCA,AXiCA,ANkBA,Ae7CA,AIZA,AV8BA,ACHA,ACHA,AKfA,AHSA,ADGA,AQxBA,AFMA,ApB4DA,AIZA,ACHA,ACHA;AatCA,AXiCA,ANkBA,Ae7CA,AIZA,AV8BA,ACHA,ACHA,AKfA,AHSA,ADGA,AQxBA,AFMA,ApB4DA,AIZA,ACHA,ACHA;AatCA,AXiCA,ANkBA,Ae7CA,AIZA,AV8BA,ACHA,ACHA,AKfA,AHSA,ADGA,AQxBA,AFMA,ApB4DA,AIZA,ACHA,ACHA;AatCA,AXiCA,ANkBA,Ae7CA,AIZA,AV8BA,ACHA,ACHA,AKfA,AHSA,ADGA,AQxBA,AFMA,ApB4DA,AIZA,ACHA,ACHA;AatCA,AXiCA,ANkBA,Ae7CA,AIZA,AV8BA,ACHA,ACHA,AKfA,AHSA,ADGA,AQxBA,AFMA,ApB4DA,AIZA,ACHA,ACHA;AatCA,AXiCA,ANkBA,Ae7CA,AIZA,AV8BA,ACHA,ACHA,AKfA,AHSA,ADGA,AQxBA,AFMA,ApB4DA,AIZA,ACHA,ACHA;AatCA,AXiCA,ANkBA,Ae7CA,AIZA,AV8BA,ACHA,ACHA,AKfA,AHSA,ADGA,AQxBA,AFMA,ApB4DA,AIZA,ACHA,ACHA;AatCA,AXiCA,ANkBA,Ae7CA,AIZA,AV8BA,ACHA,ACHA,AKfA,AHSA,ADGA,AQxBA,AFMA,ApB4DA,AIZA,ACHA,ACHA;AatCA,AXiCA,ANkBA,Ae7CA,AIZA,AV8BA,ACHA,ACHA,AKfA,AHSA,ADGA,AQxBA,AFMA,ApB4DA,AIZA,ACHA,ACHA;AatCA,AXiCA,ANkBA,Ae7CA,AIZA,AV8BA,ACHA,ACHA,AKfA,AHSA,ADGA,AQxBA,AFMA,ApB4DA,AIZA,ACHA,ACHA;AatCA,AXiCA,ANkBA,Ae7CA,AIZA,AV8BA,ACHA,ACHA,AKfA,AHSA,ADGA,AQxBA,AFMA,ApB4DA,AIZA,ACHA,ACHA;AatCA,AXiCA,ANkBA,Ae7CA,AIZA,AV8BA,ACHA,ACHA,AKfA,AHSA,ADGA,AQxBA,AFMA,ApB4DA,AIZA,ACHA,ACHA;AatCA,AXiCA,ANkBA,Ae7CA,AIZA,AV8BA,ACHA,ACHA,AKfA,AHSA,ADGA,AQxBA,AFMA,ApB4DA,AIZA,ACHA,ACHA;AatCA,AXiCA,ANkBA,Ae7CA,AIZA,AV8BA,ACHA,ACHA,AKfA,AHSA,ADGA,AQxBA,AFMA,ApB4DA,AIZA,ACHA,ACHA;AatCA,AXiCA,ANkBA,Ae7CA,AIZA,AV8BA,ACHA,ACHA,AKfA,AHSA,ADGA,AQxBA,AFMA,ApB4DA,AIZA,ACHA,ACHA;AatCA,AXiCA,ANkBA,Ae7CA,AIZA,AV8BA,ACHA,ACHA,AKfA,AHSA,ADGA,AQxBA,AFMA,ApB4DA,AIZA,ACHA,ACHA;AatCA,AXiCA,ANkBA,Ae7CA,AIZA,AV8BA,ACHA,ACHA,AKfA,AHSA,ADGA,AQxBA,AFMA,ApB4DA,AIZA,ACHA,ACHA;AatCA,AXiCA,ANkBA,Ae7CA,AIZA,AV8BA,ACHA,ACHA,AKfA,AJYA,AQxBA,AFMA,ApB4DA,AIZA,ACHA,ACHA;AatCA,AXiCA,ANkBA,Ae7CA,AIZA,AV8BA,ACHA,ACHA,AKfA,AJYA,AQxBA,AFMA,ApB4DA,AIZA,ACHA,ACHA;AatCA,AXiCA,ANkBA,Ae7CA,AIZA,AV8BA,ACHA,ACHA,AKfA,AJYA,AQxBA,AFMA,ApB4DA,AIZA,ACHA,ACHA;AatCA,AXiCA,ANkBA,Ae7CA,AIZA,AV8BA,ACHA,ACHA,AKfA,AJYA,AQxBA,AFMA,ApB4DA,AIZA,ACHA,ACHA;AatCA,AXiCA,ANkBA,Ae7CA,AIZA,AV8BA,ACHA,ACHA,AKfA,AJYA,AQxBA,AFMA,ApB4DA,AIZA,ACHA,ACHA;AatCA,AXiCA,ANkBA,Ae7CA,AIZA,AV8BA,ACHA,ACHA,AKfA,AJYA,AQxBA,AFMA,ApB4DA,AIZA,ACHA,ACHA;AatCA,AXiCA,ANkBA,Ae7CA,AIZA,AV8BA,ACHA,ACHA,AKfA,AJYA,AQxBA,AFMA,ApB4DA,AIZA,ACHA,ACHA;AatCA,AXiCA,ANkBA,Ae7CA,AIZA,AV8BA,ACHA,ACHA,AKfA,AJYA,AQxBA,AFMA,ApB4DA,AIZA,ACHA,ACHA;AatCA,AXiCA,ANkBA,Ae7CA,AIZA,AV8BA,ACHA,ACHA,AKfA,AJYA,AQxBA,AFMA,ApB4DA,AIZA,ACHA,ACHA;AatCA,AXiCA,ANkBA,Ae7CA,AIZA,AV8BA,ACHA,ACHA,AKfA,AJYA,AQxBA,AFMA,ApB4DA,AIZA,ACHA,ACHA;AatCA,AXiCA,ANkBA,Ae7CA,AIZA,AV8BA,ACHA,ACHA,AKfA,AJYA,AQxBA,AFMA,ApB4DA,AIZA,ACHA,ACHA;AatCA,AXiCA,ANkBA,Ae7CA,AIZA,AV8BA,ACHA,ACHA,AKfA,AJYA,AQxBA,AFMA,ApB4DA,AIZA,ACHA,ACHA;AatCA,AXiCA,ANkBA,Ae7CA,AIZA,AV8BA,ACHA,ACHA,AKfA,AJYA,AQxBA,AFMA,ApB4DA,AIZA,ACHA,ACHA;AatCA,AXiCA,ANkBA,Ae7CA,AIZA,AV8BA,ACHA,ACHA,AKfA,AJYA,AQxBA,AFMA,ApB4DA,AIZA,ACHA,ACHA;AatCA,AXiCA,ANkBA,Ae7CA,AIZA,AV8BA,ACHA,ACHA,AKfA,AJYA,AQxBA,AFMA,ApB4DA,AIZA,ACHA,ACHA;AatCA,AXiCA,AS3BA,AIZA,AV8BA,ACHA,ACHA,AKfA,AJYA,AQxBA,AFMA,ApB4DA,AIZA,ACHA,ACHA;AatCA,AXiCA,AS3BA,AIZA,AV8BA,ACHA,ACHA,AKfA,AJYA,AQxBA,AFMA,ApB4DA,AIZA,ACHA,ACHA;AatCA,AXiCA,AS3BA,AIZA,AV8BA,ACHA,ACHA,AKfA,AJYA,AQxBA,AFMA,ApB4DA,AIZA,ACHA,ACHA;AatCA,AXiCA,AS3BA,AIZA,AV8BA,ACHA,ACHA,AKfA,AJYA,AQxBA,AFMA,ApB4DA,AIZA,ACHA,ACHA;AatCA,AXiCA,AS3BA,AIZA,AV8BA,ACHA,ACHA,AKfA,AJYA,AQxBA,AFMA,ApB4DA,AIZA,ACHA,ACHA;AatCA,AXiCA,AS3BA,AIZA,AV8BA,ACHA,ACHA,AKfA,AJYA,AQxBA,AFMA,ApB4DA,AIZA,ACHA,ACHA;AatCA,AXiCA,AS3BA,AIZA,AV8BA,ACHA,ACHA,AKfA,AJYA,AQxBA,AFMA,ApB4DA,AIZA,ACHA,ACHA;AatCA,AXiCA,AS3BA,AIZA,AV8BA,ACHA,ACHA,AKfA,AJYA,AQxBA,AFMA,ApB4DA,AIZA,ACHA,ACHA;AatCA,AXiCA,AS3BA,AIZA,AV8BA,ACHA,ACHA,AKfA,AJYA,AQxBA,AFMA,ApB4DA,AIZA,ACHA,ACHA;AatCA,AXiCA,AS3BA,AIZA,AV8BA,ACHA,ACHA,AKfA,AJYA,AQxBA,AFMA,ApB4DA,AIZA,ACHA,ACHA;AatCA,AXiCA,AS3BA,AIZA,AV8BA,ACHA,ACHA,AKfA,AJYA,AQxBA,AFMA,ApB4DA,AIZA,ACHA,ACHA;AatCA,AXiCA,AS3BA,AIZA,AV8BA,ACHA,ACHA,AKfA,AJYA,AQxBA,AFMA,ApB4DA,AIZA,ACHA,ACHA;AatCA,AXiCA,AS3BA,AIZA,AV8BA,ACHA,ACHA,AKfA,AJYA,AQxBA,AFMA,ApB4DA,AIZA,ACHA,ACHA;AatCA,AXiCA,AS3BA,AIZA,AV8BA,ACHA,ACHA,AKfA,AJYA,AQxBA,AFMA,ApB4DA,AIZA,ACHA,ACHA;AatCA,AXiCA,AS3BA,AIZA,AV8BA,ACHA,ACHA,AKfA,AJYA,AQxBA,AFMA,ApB4DA,AIZA,ACHA,ACHA;AatCA,AXiCA,AS3BA,AIZA,AV8BA,ACHA,ACHA,AKfA,AJYA,AQxBA,AFMA,ApB4DA,AIZA,ACHA,ACHA;AatCA,AXiCA,AS3BA,AIZA,AV8BA,ACHA,ACHA,AKfA,AJYA,AQxBA,AFMA,ApB4DA,AIZA,ACHA,ACHA;AatCA,AXiCA,AS3BA,AIZA,AV8BA,ACHA,ACHA,AKfA,AJYA,AQxBA,AFMA,ApB4DA,AIZA,ACHA,ACHA;AatCA,AXiCA,AS3BA,AIZA,AV8BA,ACHA,ACHA,AKfA,AJYA,AQxBA,AFMA,ApB4DA,AIZA,ACHA,ACHA;AatCA,AXiCA,AS3BA,AIZA,AV8BA,ACHA,ACHA,AKfA,AJYA,AQxBA,AFMA,ApB4DA,AIZA,ACHA,ACHA;AatCA,AXiCA,AS3BA,AIZA,AV8BA,ACHA,ACHA,AKfA,AJYA,AQxBA,AFMA,ApB4DA,AIZA,ACHA,ACHA;AatCA,AXiCA,AS3BA,AIZA,AV8BA,ACHA,ACHA,AKfA,AJYA,AQxBA,AFMA,ApB4DA,AIZA,ACHA,ACHA;AatCA,AXiCA,AS3BA,AIZA,AV8BA,ACHA,ACHA,ACHA,AQxBA,AFMA,ApB4DA,AIZA,ACHA,ACHA;AatCA,AXiCA,AS3BA,AIZA,AV8BA,ACHA,ACHA,ACHA,AQxBA,AFMA,ApB4DA,AIZA,ACHA,ACHA;AatCA,AXiCA,AS3BA,AIZA,AV8BA,ACHA,ACHA,ACHA,AQxBA,AFMA,ApB4DA,AIZA,ACHA,ACHA;AatCA,AXiCA,AS3BA,AIZA,AV8BA,ACHA,ACHA,ACHA,AQxBA,AFMA,ApB4DA,AIZA,ACHA,ACHA;AatCA,AXiCA,AS3BA,AIZA,AV8BA,ACHA,ACHA,ACHA,AQxBA,AFMA,ApB4DA,AIZA,ACHA,ACHA;AatCA,AXiCA,AS3BA,AIZA,AV8BA,ACHA,ACHA,ACHA,AQxBA,AFMA,ApB4DA,AIZA,ACHA,ACHA;AatCA,AXiCA,AS3BA,AIZA,AV8BA,ACHA,ACHA,ACHA,AQxBA,AFMA,ApB4DA,AIZA,ACHA,ACHA;AatCA,AXiCA,AS3BA,AIZA,AV8BA,ACHA,ACHA,ACHA,AQxBA,AFMA,ApB4DA,AIZA,ACHA,ACHA;AatCA,AXiCA,AS3BA,AIZA,AV8BA,ACHA,ACHA,ACHA,AQxBA,AFMA,ApB4DA,AIZA,ACHA,ACHA;AatCA,AXiCA,AS3BA,AIZA,AV8BA,ACHA,ACHA,ACHA,AQxBA,AFMA,ApB4DA,AIZA,ACHA,ACHA;AatCA,AXiCA,AS3BA,AIZA,AV8BA,ACHA,ACHA,ACHA,AQxBA,AFMA,ApB4DA,AIZA,ACHA,ACHA;AatCA,AXiCA,AS3BA,AIZA,AV8BA,ACHA,ACHA,ACHA,AQxBA,AFMA,ApB4DA,AKfA,ACHA;AatCA,AXiCA,AS3BA,AIZA,AV8BA,ACHA,ACHA,ACHA,AQxBA,AFMA,ApB4DA,AKfA,ACHA;AatCA,AXiCA,AS3BA,AIZA,AV8BA,ACHA,ACHA,ACHA,AQxBA,AFMA,ApB4DA,AKfA,ACHA;AatCA,AXiCA,AS3BA,AIZA,AV8BA,ACHA,ACHA,ACHA,AQxBA,AFMA,ApB4DA,AKfA,ACHA;AatCA,AXiCA,AS3BA,AIZA,AV8BA,ACHA,ACHA,ACHA,AQxBA,AFMA,ApB4DA,AKfA,ACHA;AatCA,AXiCA,AS3BA,AIZA,AV8BA,ACHA,ACHA,ACHA,AQxBA,AFMA,ApB4DA,AKfA,ACHA;AatCA,AXiCA,AS3BA,AIZA,AV8BA,ACHA,ACHA,ACHA,AQxBA,AFMA,ApB4DA,AKfA,ACHA;AatCA,AXiCA,AS3BA,AIZA,AV8BA,ACHA,ACHA,ACHA,AQxBA,AFMA,ApB4DA,AKfA,ACHA;AatCA,AXiCA,AS3BA,AIZA,AV8BA,ACHA,ACHA,ACHA,AQxBA,AFMA,ApB4DA,AKfA,ACHA;AatCA,AXiCA,AS3BA,AIZA,AV8BA,ACHA,ACHA,ACHA,AQxBA,AFMA,ApB4DA,AKfA,ACHA;AatCA,AXiCA,AS3BA,AIZA,AV8BA,ACHA,ACHA,ACHA,AQxBA,AFMA,ApB4DA,AKfA,ACHA;AatCA,AXiCA,AS3BA,AIZA,AV8BA,ACHA,ACHA,ACHA,AQxBA,AFMA,ApB4DA,AKfA,ACHA;AatCA,AXiCA,AS3BA,AIZA,AV8BA,ACHA,ACHA,ACHA,AQxBA,AFMA,ApB4DA,AKfA,ACHA;AatCA,AXiCA,AS3BA,AIZA,AV8BA,ACHA,ACHA,ACHA,AQxBA,AFMA,ApB4DA,AKfA,ACHA;AatCA,AXiCA,AS3BA,AIZA,AV8BA,ACHA,ACHA,ACHA,AQxBA,AFMA,ApB4DA,AKfA,ACHA;AatCA,AXiCA,AS3BA,AIZA,AV8BA,ACHA,ACHA,ACHA,AQxBA,AFMA,ApB4DA,AKfA,ACHA;AatCA,AXiCA,AS3BA,AIZA,AV8BA,ACHA,ACHA,ACHA,AQxBA,AFMA,ApB4DA,AKfA,ACHA;AatCA,AXiCA,AS3BA,AIZA,AV8BA,ACHA,ACHA,ACHA,AQxBA,AFMA,ApB4DA,AKfA,ACHA;AatCA,AXiCA,AS3BA,AIZA,AV8BA,ACHA,ACHA,ACHA,AQxBA,AFMA,ApB4DA,AKfA,ACHA;AatCA,AXiCA,AS3BA,AIZA,AV8BA,ACHA,ACHA,ACHA,AQxBA,AFMA,ApB4DA,AKfA,ACHA;AatCA,AXiCA,AS3BA,AIZA,AV8BA,ACHA,ACHA,ACHA,AQxBA,AFMA,ApB4DA,AKfA,ACHA;AatCA,AXiCA,AS3BA,AIZA,AV8BA,ACHA,ACHA,ACHA,AQxBA,AFMA,ApB4DA,AKfA,ACHA;AatCA,AXiCA,AS3BA,AIZA,AV8BA,ACHA,ACHA,ACHA,AQxBA,AFMA,ApB4DA,AKfA,ACHA;AatCA,AXiCA,AS3BA,AIZA,AV8BA,ACHA,ACHA,ACHA,AQxBA,AFMA,ApB4DA,AKfA,ACHA;AatCA,AXiCA,AS3BA,AIZA,AV8BA,ACHA,ACHA,ACHA,AQxBA,AFMA,ApB4DA,AKfA,ACHA;AatCA,AXiCA,AS3BA,AIZA,AV8BA,ACHA,ACHA,ACHA,AQxBA,AFMA,ApB4DA,AKfA,ACHA;AatCA,AXiCA,AS3BA,AIZA,AV8BA,ACHA,ACHA,ACHA,AQxBA,AFMA,ApB4DA,AKfA,ACHA;AatCA,AXiCA,AS3BA,AIZA,AV8BA,ACHA,ACHA,ACHA,AQxBA,AFMA,ApB4DA,AKfA,ACHA;AatCA,AXiCA,AS3BA,AIZA,AV8BA,ACHA,ACHA,ACHA,AQxBA,AFMA,ApB4DA,AKfA,ACHA;AatCA,AXiCA,AS3BA,AIZA,AV8BA,ACHA,ACHA,ACHA,AQxBA,AFMA,ApB4DA,AKfA,ACHA;AatCA,AXiCA,AS3BA,AIZA,AV8BA,ACHA,ACHA,ACHA,AQxBA,AFMA,ApB4DA,AKfA,ACHA;AatCA,AXiCA,AS3BA,AIZA,AV8BA,ACHA,ACHA,ACHA,AQxBA,AFMA,ApB4DA,AKfA,ACHA;AatCA,AXiCA,AS3BA,AIZA,AV8BA,ACHA,ACHA,ACHA,AQxBA,AFMA,ApB4DA,AKfA,ACHA;AatCA,AXiCA,AS3BA,AIZA,AV8BA,ACHA,ACHA,ACHA,AQxBA,AFMA,ApB4DA,AKfA,ACHA;AatCA,AXiCA,AS3BA,AIZA,AV8BA,ACHA,ACHA,ACHA,AQxBA,AFMA,ApB4DA,AKfA,ACHA;AatCA,AXiCA,AS3BA,AIZA,AV8BA,ACHA,ACHA,ACHA,AQxBA,AFMA,Af6CA,ACHA;AatCA,AXiCA,AS3BA,AIZA,AV8BA,ACHA,ACHA,ACHA,AQxBA,AFMA,Af6CA,ACHA;AatCA,AXiCA,AS3BA,AIZA,AV8BA,ACHA,ACHA,ACHA,AQxBA,AFMA,Af6CA,ACHA;AatCA,AXiCA,AS3BA,AIZA,AV8BA,ACHA,ACHA,ACHA,AQxBA,AFMA,Af6CA,ACHA;AatCA,AXiCA,AS3BA,AIZA,AV8BA,ACHA,ACHA,ACHA,AQxBA,AFMA,Af6CA,ACHA;AatCA,AXiCA,AS3BA,AIZA,AV8BA,ACHA,ACHA,ACHA,AQxBA,AFMA,Af6CA,ACHA;AatCA,AXiCA,AS3BA,AIZA,AV8BA,ACHA,ACHA,ACHA,AQxBA,AFMA,Af6CA,ACHA;AatCA,AXiCA,AS3BA,AIZA,AV8BA,ACHA,ACHA,ACHA,AQxBA,AFMA,Af6CA,ACHA;AatCA,AXiCA,AS3BA,AIZA,AV8BA,ACHA,ACHA,ACHA,AQxBA,AFMA,Af6CA,ACHA;AatCA,AXiCA,AS3BA,AIZA,AV8BA,ACHA,ACHA,ACHA,AQxBA,AFMA,Af6CA,ACHA;AatCA,AXiCA,AS3BA,AIZA,AV8BA,ACHA,ACHA,AS3BA,AFMA,Af6CA,ACHA;AatCA,AXiCA,AS3BA,AIZA,AV8BA,ACHA,ACHA,AS3BA,AFMA,Af6CA,ACHA;AatCA,AXiCA,AS3BA,AIZA,AV8BA,ACHA,ACHA,AS3BA,AFMA,Af6CA,ACHA;AatCA,AXiCA,AS3BA,AIZA,AV8BA,ACHA,ACHA,AS3BA,AFMA,Af6CA,ACHA;AatCA,AXiCA,AS3BA,AIZA,AV8BA,ACHA,ACHA,AS3BA,AFMA,Af6CA,ACHA;AatCA,AXiCA,AS3BA,AIZA,AV8BA,ACHA,ACHA,AS3BA,AFMA,Af6CA,ACHA;AatCA,AXiCA,AS3BA,AIZA,AV8BA,ACHA,ACHA,AS3BA,AFMA,Af6CA,ACHA;AatCA,AXiCA,AS3BA,AIZA,AV8BA,ACHA,ACHA,AS3BA,AFMA,Af6CA,ACHA;AatCA,AXiCA,AS3BA,AIZA,AV8BA,ACHA,ACHA,AS3BA,AFMA,Af6CA,ACHA;AatCA,AXiCA,AS3BA,AIZA,AV8BA,ACHA,ACHA,AS3BA,AFMA,Af6CA,ACHA;AatCA,AXiCA,AS3BA,AIZA,AV8BA,ACHA,ACHA,AS3BA,AFMA,Af6CA,ACHA;AatCA,AXiCA,AS3BA,AIZA,AV8BA,ACHA,ACHA,AS3BA,AFMA,Af6CA,ACHA;AatCA,AXiCA,AS3BA,AIZA,AV8BA,ACHA,ACHA,AS3BA,AFMA,Af6CA,ACHA;AatCA,AXiCA,AS3BA,AIZA,AV8BA,ACHA,ACHA,AS3BA,AFMA,Af6CA,ACHA;AatCA,AXiCA,AS3BA,AIZA,AV8BA,ACHA,ACHA,AS3BA,AFMA,Af6CA,ACHA;AatCA,AXiCA,AS3BA,AIZA,AV8BA,ACHA,ACHA,AS3BA,AFMA,Af6CA,ACHA;AatCA,AXiCA,AS3BA,AIZA,AV8BA,ACHA,ACHA,AS3BA,AFMA,Af6CA,ACHA;AatCA,AXiCA,AS3BA,AIZA,AV8BA,ACHA,ACHA,AS3BA,AFMA,Af6CA,ACHA;AatCA,AXiCA,AS3BA,AIZA,AV8BA,ACHA,ACHA,AS3BA,AFMA,Af6CA,ACHA;AatCA,AXiCA,AS3BA,AIZA,AT2BA,ACHA,AS3BA,AFMA,Af6CA,ACHA;AatCA,AXiCA,AS3BA,AIZA,AT2BA,ACHA,AS3BA,AFMA,Af6CA,ACHA;AatCA,AXiCA,AS3BA,AIZA,AT2BA,ACHA,AS3BA,AFMA,Af6CA,ACHA;AatCA,AXiCA,AS3BA,AIZA,AT2BA,ACHA,AS3BA,AFMA,Af6CA,ACHA;AatCA,AXiCA,AS3BA,AIZA,AT2BA,ACHA,AS3BA,AFMA,Af6CA,ACHA;AatCA,AXiCA,AS3BA,AIZA,AT2BA,ACHA,AS3BA,AFMA,Af6CA,ACHA;AatCA,AXiCA,AS3BA,AIZA,AT2BA,ACHA,AS3BA,AFMA,Ad0CA;AatCA,AXiCA,AS3BA,AIZA,AT2BA,ACHA,AS3BA,AFMA,Ad0CA;AatCA,AXiCA,AS3BA,AIZA,AT2BA,ACHA,AS3BA,AFMA,Ad0CA;AatCA,AXiCA,AS3BA,AIZA,AT2BA,ACHA,AS3BA,AFMA,Ad0CA;AatCA,AXiCA,AS3BA,AIZA,AT2BA,ACHA,AS3BA,AFMA,Ad0CA;AatCA,AXiCA,AS3BA,AIZA,AT2BA,ACHA,AS3BA,AFMA,Ad0CA;AatCA,AXiCA,AS3BA,AIZA,AT2BA,ACHA,AS3BA,AFMA,Ad0CA;AatCA,AXiCA,AS3BA,AIZA,AT2BA,ACHA,AS3BA,AFMA,Ad0CA;AatCA,AXiCA,AS3BA,AIZA,AT2BA,ACHA,AS3BA,AFMA,Ad0CA;AatCA,AXiCA,AavCA,AT2BA,ACHA,AS3BA,AFMA,Ad0CA;AatCA,AXiCA,AavCA,AT2BA,ACHA,AS3BA,AFMA,Ad0CA;AatCA,AXiCA,AavCA,AT2BA,ACHA,AS3BA,AFMA,Ad0CA;AatCA,AXiCA,AavCA,AT2BA,ACHA,AS3BA,AFMA,Ad0CA;AatCA,AXiCA,AavCA,AT2BA,ACHA,AS3BA,AFMA,Ad0CA;AatCA,AXiCA,AavCA,AT2BA,ACHA,AS3BA,AFMA,Ad0CA;AatCA,AXiCA,AavCA,AT2BA,ACHA,AS3BA,AFMA,Ad0CA;AatCA,AXiCA,AavCA,AT2BA,ACHA,AS3BA,AFMA,Ad0CA;AatCA,AXiCA,AavCA,AT2BA,ACHA,AS3BA,AFMA,Ad0CA;AatCA,AXiCA,AavCA,AT2BA,ACHA,AS3BA,AFMA,Ad0CA;AatCA,AXiCA,AavCA,AT2BA,ACHA,AS3BA,AFMA,Ad0CA;AatCA,AXiCA,AavCA,AT2BA,ACHA,AS3BA,AFMA,Ad0CA;AatCA,AXiCA,AavCA,AT2BA,ACHA,AS3BA,AFMA,Ad0CA;AatCA,AXiCA,AavCA,AT2BA,ACHA,AS3BA,AFMA,Ad0CA;AatCA,AXiCA,AavCA,AT2BA,ACHA,AS3BA,AFMA,Ad0CA;AatCA,AXiCA,AavCA,AT2BA,ACHA,AS3BA,AFMA,Ad0CA;AatCA,AXiCA,AavCA,AT2BA,ACHA,AS3BA,AFMA,Ad0CA;AatCA,AXiCA,AavCA,AT2BA,ACHA,AS3BA,AFMA,Ad0CA;AatCA,AXiCA,AavCA,AT2BA,ACHA,AS3BA,AFMA,Ad0CA;AatCA,AXiCA,AavCA,AT2BA,ACHA,AS3BA,AFMA,Ad0CA;AatCA,AXiCA,AavCA,AT2BA,ACHA,AS3BA,AFMA,Ad0CA;AatCA,AXiCA,AavCA,AT2BA,ACHA,AS3BA,AFMA,Ad0CA;AatCA,AXiCA,AavCA,AT2BA,ACHA,AS3BA,AFMA,Ad0CA;AatCA,AXiCA,AavCA,AT2BA,ACHA,AS3BA,AFMA,Ad0CA;AatCA,AXiCA,AavCA,AT2BA,ACHA,AS3BA,AFMA,Ad0CA;AatCA,AXiCA,AavCA,AT2BA,ACHA,AS3BA,AFMA,Ad0CA;AatCA,AXiCA,AavCA,AT2BA,ACHA,AS3BA,AFMA,Ad0CA;AatCA,AXiCA,AavCA,AT2BA,ACHA,AS3BA,AFMA,Ad0CA;AatCA,AXiCA,AavCA,AT2BA,ACHA,AS3BA,AFMA,Ad0CA;AatCA,AXiCA,AavCA,AT2BA,ACHA,AS3BA,AFMA,Ad0CA;AatCA,AXiCA,AavCA,AT2BA,ACHA,AS3BA,AFMA,Ad0CA;AatCA,AXiCA,AavCA,AT2BA,ACHA,AS3BA,AFMA,Ad0CA;AatCA,AXiCA,AavCA,AT2BA,ACHA,AS3BA,AFMA,Ad0CA;AatCA,AXiCA,AavCA,AT2BA,ACHA,AS3BA,AFMA,Ad0CA;AatCA,AXiCA,AavCA,AT2BA,ACHA,AS3BA,AFMA,Ad0CA;AatCA,AXiCA,AavCA,AT2BA,ACHA,AS3BA,AFMA,Ad0CA;AatCA,AXiCA,AavCA,AT2BA,ACHA,AS3BA,AFMA,Ad0CA;AatCA,AXiCA,AavCA,AT2BA,ACHA,AS3BA,AFMA,Ad0CA;AatCA,AXiCA,AavCA,AT2BA,ACHA,AS3BA,AFMA,Ad0CA;AatCA,AXiCA,AavCA,AT2BA,ACHA,AS3BA,AFMA,Ad0CA;AatCA,AXiCA,AavCA,AT2BA,ACHA,AS3BA,AFMA,Ad0CA;AatCA,AXiCA,AavCA,AT2BA,ACHA,AS3BA,AFMA,Ad0CA;AatCA,AXiCA,AavCA,AT2BA,ACHA,AS3BA,AFMA,Ad0CA;AatCA,AXiCA,AavCA,AT2BA,ACHA,AS3BA,AFMA,Ad0CA;AatCA,AXiCA,AavCA,AT2BA,ACHA,AS3BA,AFMA,Ad0CA;AatCA,AXiCA,AavCA,AT2BA,ACHA,AS3BA,AFMA,Ad0CA;AatCA,AXiCA,AavCA,AT2BA,ACHA,AS3BA,AFMA,Ad0CA;AatCA,AXiCA,AavCA,AT2BA,ACHA,AS3BA,AFMA,Ad0CA;AatCA,AXiCA,AavCA,AT2BA,ACHA,AS3BA,AFMA,Ad0CA;AatCA,AXiCA,AavCA,AT2BA,ACHA,AS3BA,AFMA,Ad0CA;AatCA,AXiCA,AavCA,AT2BA,ACHA,AS3BA,AFMA,Ad0CA;AatCA,AXiCA,AavCA,AT2BA,ACHA,AS3BA,AFMA,Ad0CA;AatCA,AXiCA,AavCA,AT2BA,ACHA,AS3BA,AFMA,Ad0CA;AatCA,AXiCA,AavCA,AT2BA,ACHA,AS3BA,AFMA,Ad0CA;AatCA,AXiCA,AavCA,AT2BA,ACHA,AS3BA,AFMA,Ad0CA;AatCA,AXiCA,AavCA,AT2BA,ACHA,AS3BA,AFMA,Ad0CA;AatCA,AXiCA,AavCA,AT2BA,ACHA,AS3BA,AFMA,Ad0CA;AatCA,AXiCA,AavCA,AT2BA,ACHA,AS3BA,AFMA,Ad0CA;AatCA,AXiCA,AavCA,AT2BA,ACHA,AS3BA,AFMA,Ad0CA;AatCA,AXiCA,AavCA,AT2BA,ACHA,AS3BA,AFMA,Ad0CA;AatCA,AXiCA,AavCA,AT2BA,ACHA,AS3BA,AFMA,Ad0CA;AatCA,AXiCA,AavCA,AT2BA,ACHA,AS3BA,AFMA,Ad0CA;AatCA,AXiCA,AavCA,AT2BA,ACHA,AS3BA,AFMA,Ad0CA;AatCA,AXiCA,AavCA,AT2BA,ACHA,AS3BA,AFMA,Ad0CA;AatCA,AXiCA,AavCA,AT2BA,ACHA,AS3BA,AFMA,Ad0CA;AatCA,AXiCA,AavCA,AT2BA,ACHA,AS3BA,AFMA,Ad0CA;AatCA,AXiCA,AavCA,AT2BA,ACHA,AS3BA,AFMA,Ad0CA;AatCA,AXiCA,AavCA,AT2BA,ACHA,AS3BA,AFMA,Ad0CA;AatCA,AXiCA,AavCA,AT2BA,ACHA,AS3BA,AFMA,Ad0CA;AatCA,AXiCA,AavCA,AT2BA,ACHA,AS3BA,AFMA,Ad0CA;AatCA,AXiCA,AavCA,AT2BA,ACHA,AS3BA,AFMA,Ad0CA;AatCA,AXiCA,AavCA,ARwBA,AS3BA,AFMA,Ad0CA;AatCA,AXiCA,AavCA,ARwBA,AS3BA,AFMA,Ad0CA;AatCA,AXiCA,AavCA,ARwBA,AS3BA,AFMA,Ad0CA;AatCA,AXiCA,AavCA,ARwBA,AS3BA,AFMA,Ad0CA;AatCA,AXiCA,AavCA,ARwBA,AS3BA,AFMA,Ad0CA;AatCA,AXiCA,AavCA,ARwBA,AS3BA,AFMA,Ad0CA;AatCA,AXiCA,AKfA,AS3BA,AFMA,Ad0CA;AatCA,AXiCA,AKfA,AS3BA,AFMA,Ad0CA;AatCA,AXiCA,AKfA,AS3BA,AFMA,Ad0CA;AatCA,AXiCA,AKfA,AS3BA,AFMA,Ad0CA;AatCA,AXiCA,AKfA,AS3BA,AFMA,Ad0CA;AatCA,AXiCA,AKfA,AS3BA,AFMA,Ad0CA;AatCA,ANkBA,AS3BA,AFMA,Ad0CA;AatCA,ANkBA,AS3BA,AFMA;ADIA,ANkBA,AS3BA,AFMA;ADIA,ANkBA,AS3BA,AFMA;ADIA,ANkBA,AS3BA,AFMA;ADIA,ANkBA,AS3BA,AFMA;ADIA,ANkBA,AS3BA,AFMA;ADIA,ANkBA,AS3BA,AFMA;ADIA,ANkBA,AS3BA,AFMA;ADIA,ANkBA,AS3BA,AFMA;ADIA,ANkBA,AS3BA,AFMA;ADIA,ANkBA,AS3BA,AFMA;ADIA,ANkBA,AS3BA;AHUA,ANkBA,AS3BA;AHUA,ANkBA,AS3BA;AHUA,ANkBA,AS3BA;AHUA,ANkBA,AS3BA;AHUA,ANkBA,AS3BA;AHUA,ANkBA,AS3BA;AHUA,ANkBA,AS3BA;AHUA,ANkBA,AS3BA;AHUA,ANkBA,AS3BA;AHUA,ANkBA,AS3BA;AHUA,ANkBA,AS3BA;AHUA,ANkBA,AS3BA;AHUA,ANkBA,AS3BA;AHUA,ANkBA,AS3BA;AHUA,ANkBA,AS3BA;AHUA,ANkBA,AS3BA;AHUA,ANkBA,AS3BA;AHUA,ANkBA,AS3BA;AHUA,ANkBA,AS3BA;AHUA,ANkBA,AS3BA;AHUA,ANkBA,AS3BA;AHUA,ANkBA,AS3BA;AHUA,ANkBA,AS3BA;AHUA,ANkBA,AS3BA;AHUA,ANkBA,AS3BA;AHUA,ANkBA,AS3BA;AHUA,ANkBA,AS3BA;AHUA,ANkBA,AS3BA;AHUA,ANkBA,AS3BA;AHUA,ANkBA,AS3BA;AHUA,ANkBA,AS3BA;AHUA,ANkBA,AS3BA;AHUA,ANkBA,AS3BA;AT4BA,AS3BA;AT4BA,AS3BA;AT4BA,AS3BA;AT4BA,AS3BA;AT4BA,AS3BA;AT4BA,AS3BA;AT4BA,AS3BA;AT4BA,AS3BA;AT4BA,AS3BA;AT4BA,AS3BA;AT4BA,AS3BA;AT4BA,AS3BA;AT4BA,AS3BA;AT4BA,AS3BA;AT4BA,AS3BA;AT4BA,AS3BA;AT4BA,AS3BA;AT4BA,AS3BA;AT4BA,AS3BA;AT4BA,AS3BA;AT4BA,AS3BA;AT4BA,AS3BA;AT4BA,AS3BA;AT4BA,AS3BA;AT4BA,AS3BA;AT4BA,AS3BA;AT4BA,AS3BA;AT4BA,AS3BA;AT4BA,AS3BA;AT4BA,AS3BA;AT4BA,AS3BA;AT4BA,AS3BA;AT4BA,AS3BA;AT4BA,AS3BA;AT4BA,AS3BA;AT4BA,AS3BA;AT4BA,AS3BA;AT4BA,AS3BA;AT4BA,AS3BA;AT4BA,AS3BA;AT4BA,AS3BA;AT4BA,AS3BA;AT4BA,AS3BA;AT4BA,AS3BA;AT4BA,AS3BA;AT4BA,AS3BA;AT4BA,AS3BA;AT4BA,AS3BA;AT4BA,AS3BA;AT4BA,AS3BA;AT4BA,AS3BA;AT4BA,AS3BA;AT4BA,AS3BA;AT4BA,AS3BA;AT4BA,AS3BA;AT4BA,AS3BA;AT4BA,AS3BA;AT4BA,AS3BA;AT4BA,AS3BA;AT4BA,AS3BA;AT4BA,AS3BA;AT4BA,AS3BA;AT4BA,AS3BA;AT4BA,AS3BA;AT4BA,AS3BA;AT4BA,AS3BA;AT4BA,AS3BA;AT4BA,AS3BA;AT4BA,AS3BA;AT4BA,AS3BA;AT4BA,AS3BA;AT4BA,AS3BA;AT4BA,AS3BA;AT4BA,AS3BA;AT4BA,AS3BA;AT4BA,AS3BA;AT4BA,AS3BA;AT4BA,AS3BA;AT4BA,AS3BA;AT4BA,AS3BA;AT4BA,AS3BA;AT4BA,AS3BA;AT4BA,AS3BA;AT4BA,AS3BA;AT4BA,AS3BA;AT4BA,AS3BA;AT4BA,AS3BA;AT4BA,AS3BA;AT4BA,AS3BA;AT4BA,AS3BA;AT4BA,AS3BA;AT4BA,AS3BA;AT4BA,AS3BA;AT4BA,AS3BA;AT4BA,AS3BA;AT4BA,AS3BA;AT4BA,AS3BA;AT4BA,AS3BA;AT4BA,AS3BA;AT4BA,AS3BA;AT4BA,AS3BA;AT4BA,AS3BA;AT4BA,AS3BA;AT4BA,AS3BA;AT4BA,AS3BA;AT4BA,AS3BA;AT4BA,AS3BA;AT4BA,AS3BA;AT4BA,AS3BA;AT4BA,AS3BA;AT4BA,AS3BA;AT4BA,AS3BA;AT4BA,AS3BA;AT4BA,AS3BA;AT4BA,AS3BA;AT4BA,AS3BA;AT4BA,AS3BA;AT4BA,AS3BA;AT4BA,AS3BA;AT4BA,AS3BA;AT4BA,AS3BA;AT4BA,AS3BA;AT4BA,AS3BA;AT4BA,AS3BA;AT4BA,AS3BA;AT4BA,AS3BA;AT4BA,AS3BA;AT4BA,AS3BA;AT4BA,AS3BA;AT4BA,AS3BA;AT4BA,AS3BA;AT4BA,AS3BA;AT4BA,AS3BA;AT4BA,AS3BA;AT4BA,AS3BA;AT4BA,AS3BA;AT4BA,AS3BA;AT4BA,AS3BA;AT4BA,AS3BA;AT4BA,AS3BA;AT4BA,AS3BA;AT4BA,AS3BA;AT4BA,AS3BA;AT4BA,AS3BA;AT4BA,AS3BA;AT4BA,AS3BA;AT4BA,AS3BA;AT4BA,AS3BA;AT4BA,AS3BA;AT4BA,AS3BA;AT4BA,AS3BA;AT4BA,AS3BA;AT4BA,AS3BA;AT4BA,AS3BA;AT4BA,AS3BA;AT4BA,AS3BA;AT4BA,AS3BA;AT4BA,AS3BA;AT4BA,AS3BA;AT4BA,AS3BA;AT4BA,AS3BA;AT4BA,AS3BA;AT4BA,AS3BA;AT4BA,AS3BA;AT4BA,AS3BA;AT4BA,AS3BA;AT4BA,AS3BA;AT4BA,AS3BA;AT4BA,AS3BA;AT4BA,AS3BA;AT4BA,AS3BA;AT4BA,AS3BA;AT4BA,AS3BA;AT4BA,AS3BA;AT4BA,AS3BA;AT4BA,AS3BA;AT4BA,AS3BA;AT4BA,AS3BA;AT4BA,AS3BA;AT4BA,AS3BA;AT4BA,AS3BA;AT4BA,AS3BA;AT4BA,AS3BA;AT4BA,AS3BA;AT4BA,AS3BA;AT4BA,AS3BA;AT4BA,AS3BA;AT4BA,AS3BA;AT4BA,AS3BA;AT4BA,AS3BA;AT4BA,AS3BA;AT4BA,AS3BA;AT4BA,AS3BA;AT4BA,AS3BA;AT4BA,AS3BA;AT4BA,AS3BA;AT4BA,AS3BA;AT4BA,AS3BA;AT4BA,AS3BA;AT4BA,AS3BA;AT4BA,AS3BA;AT4BA,AS3BA;AT4BA,AS3BA;AT4BA,AS3BA;AT4BA,AS3BA;AT4BA,AS3BA;AT4BA,AS3BA;AT4BA,AS3BA;AT4BA,AS3BA;AT4BA,AS3BA;AT4BA,AS3BA;AT4BA,AS3BA;AT4BA,AS3BA;AT4BA,AS3BA;AT4BA,AS3BA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.js", "sourcesContent": ["// Copyright 2015 Joyent, Inc.\n\nvar Key = require('./key');\nvar Fingerprint = require('./fingerprint');\nvar Signature = require('./signature');\nvar PrivateKey = require('./private-key');\nvar Certificate = require('./certificate');\nvar Identity = require('./identity');\nvar errs = require('./errors');\n\nmodule.exports = {\n\t/* top-level classes */\n\tKey: Key,\n\tparseKey: Key.parse,\n\tFingerprint: Fingerprint,\n\tparseFingerprint: Fingerprint.parse,\n\tSignature: Signature,\n\tparseSignature: Signature.parse,\n\tPrivateKey: PrivateKey,\n\tparsePrivateKey: PrivateKey.parse,\n\tgeneratePrivateKey: PrivateKey.generate,\n\tCertificate: Certificate,\n\tparseCertificate: Certificate.parse,\n\tcreateSelfSignedCertificate: Certificate.createSelfSigned,\n\tcreateCertificate: Certificate.create,\n\tIdentity: Identity,\n\tidentityFromDN: Identity.parseDN,\n\tidentityForHost: Identity.forHost,\n\tidentityForUser: Identity.forUser,\n\tidentityForEmail: Identity.forEmail,\n\tidentityFromArray: Identity.fromArray,\n\n\t/* errors */\n\tFingerprintFormatError: errs.FingerprintFormatError,\n\tInvalidAlgorithmError: errs.InvalidAlgorithmError,\n\tKeyParseError: errs.KeyParseError,\n\tSignatureParseError: errs.SignatureParseError,\n\tKeyEncryptedError: errs.KeyEncryptedError,\n\tCertificateParseError: errs.CertificateParseError\n};\n", "// Copyright 2018 Joyent, Inc.\n\nmodule.exports = Key;\n\nvar assert = require('assert-plus');\nvar algs = require('./algs');\nvar crypto = require('crypto');\nvar Fingerprint = require('./fingerprint');\nvar Signature = require('./signature');\nvar <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> = require('./dhe').<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>;\nvar errs = require('./errors');\nvar utils = require('./utils');\nvar PrivateKey = require('./private-key');\nvar edCompat;\n\ntry {\n\tedCompat = require('./ed-compat');\n} catch (e) {\n\t/* Just continue through, and bail out if we try to use it. */\n}\n\nvar InvalidAlgorithmError = errs.InvalidAlgorithmError;\nvar KeyParseError = errs.KeyParseError;\n\nvar formats = {};\nformats['auto'] = require('./formats/auto');\nformats['pem'] = require('./formats/pem');\nformats['pkcs1'] = require('./formats/pkcs1');\nformats['pkcs8'] = require('./formats/pkcs8');\nformats['rfc4253'] = require('./formats/rfc4253');\nformats['ssh'] = require('./formats/ssh');\nformats['ssh-private'] = require('./formats/ssh-private');\nformats['openssh'] = formats['ssh-private'];\nformats['dnssec'] = require('./formats/dnssec');\nformats['putty'] = require('./formats/putty');\nformats['ppk'] = formats['putty'];\n\nfunction Key(opts) {\n\tassert.object(opts, 'options');\n\tassert.arrayOfObject(opts.parts, 'options.parts');\n\tassert.string(opts.type, 'options.type');\n\tassert.optionalString(opts.comment, 'options.comment');\n\n\tvar algInfo = algs.info[opts.type];\n\tif (typeof (algInfo) !== 'object')\n\t\tthrow (new InvalidAlgorithmError(opts.type));\n\n\tvar partLookup = {};\n\tfor (var i = 0; i < opts.parts.length; ++i) {\n\t\tvar part = opts.parts[i];\n\t\tpartLookup[part.name] = part;\n\t}\n\n\tthis.type = opts.type;\n\tthis.parts = opts.parts;\n\tthis.part = partLookup;\n\tthis.comment = undefined;\n\tthis.source = opts.source;\n\n\t/* for speeding up hashing/fingerprint operations */\n\tthis._rfc4253Cache = opts._rfc4253Cache;\n\tthis._hashCache = {};\n\n\tvar sz;\n\tthis.curve = undefined;\n\tif (this.type === 'ecdsa') {\n\t\tvar curve = this.part.curve.data.toString();\n\t\tthis.curve = curve;\n\t\tsz = algs.curves[curve].size;\n\t} else if (this.type === 'ed25519' || this.type === 'curve25519') {\n\t\tsz = 256;\n\t\tthis.curve = 'curve25519';\n\t} else {\n\t\tvar szPart = this.part[algInfo.sizePart];\n\t\tsz = szPart.data.length;\n\t\tsz = sz * 8 - utils.countZeros(szPart.data);\n\t}\n\tthis.size = sz;\n}\n\nKey.formats = formats;\n\nKey.prototype.toBuffer = function (format, options) {\n\tif (format === undefined)\n\t\tformat = 'ssh';\n\tassert.string(format, 'format');\n\tassert.object(formats[format], 'formats[format]');\n\tassert.optionalObject(options, 'options');\n\n\tif (format === 'rfc4253') {\n\t\tif (this._rfc4253Cache === undefined)\n\t\t\tthis._rfc4253Cache = formats['rfc4253'].write(this);\n\t\treturn (this._rfc4253Cache);\n\t}\n\n\treturn (formats[format].write(this, options));\n};\n\nKey.prototype.toString = function (format, options) {\n\treturn (this.toBuffer(format, options).toString());\n};\n\nKey.prototype.hash = function (algo, type) {\n\tassert.string(algo, 'algorithm');\n\tassert.optionalString(type, 'type');\n\tif (type === undefined)\n\t\ttype = 'ssh';\n\talgo = algo.toLowerCase();\n\tif (algs.hashAlgs[algo] === undefined)\n\t\tthrow (new InvalidAlgorithmError(algo));\n\n\tvar cacheKey = algo + '||' + type;\n\tif (this._hashCache[cacheKey])\n\t\treturn (this._hashCache[cacheKey]);\n\n\tvar buf;\n\tif (type === 'ssh') {\n\t\tbuf = this.toBuffer('rfc4253');\n\t} else if (type === 'spki') {\n\t\tbuf = formats.pkcs8.pkcs8ToBuffer(this);\n\t} else {\n\t\tthrow (new Error('Hash type ' + type + ' not supported'));\n\t}\n\tvar hash = crypto.createHash(algo).update(buf).digest();\n\tthis._hashCache[cacheKey] = hash;\n\treturn (hash);\n};\n\nKey.prototype.fingerprint = function (algo, type) {\n\tif (algo === undefined)\n\t\talgo = 'sha256';\n\tif (type === undefined)\n\t\ttype = 'ssh';\n\tassert.string(algo, 'algorithm');\n\tassert.string(type, 'type');\n\tvar opts = {\n\t\ttype: 'key',\n\t\thash: this.hash(algo, type),\n\t\talgorithm: algo,\n\t\thashType: type\n\t};\n\treturn (new Fingerprint(opts));\n};\n\nKey.prototype.defaultHashAlgorithm = function () {\n\tvar hashAlgo = 'sha1';\n\tif (this.type === 'rsa')\n\t\thashAlgo = 'sha256';\n\tif (this.type === 'dsa' && this.size > 1024)\n\t\thashAlgo = 'sha256';\n\tif (this.type === 'ed25519')\n\t\thashAlgo = 'sha512';\n\tif (this.type === 'ecdsa') {\n\t\tif (this.size <= 256)\n\t\t\thashAlgo = 'sha256';\n\t\telse if (this.size <= 384)\n\t\t\thashAlgo = 'sha384';\n\t\telse\n\t\t\thashAlgo = 'sha512';\n\t}\n\treturn (hashAlgo);\n};\n\nKey.prototype.createVerify = function (hashAlgo) {\n\tif (hashAlgo === undefined)\n\t\thashAlgo = this.defaultHashAlgorithm();\n\tassert.string(hashAlgo, 'hash algorithm');\n\n\t/* ED25519 is not supported by OpenSSL, use a javascript impl. */\n\tif (this.type === 'ed25519' && edCompat !== undefined)\n\t\treturn (new edCompat.Verifier(this, hashAlgo));\n\tif (this.type === 'curve25519')\n\t\tthrow (new Error('Curve25519 keys are not suitable for ' +\n\t\t    'signing or verification'));\n\n\tvar v, nm, err;\n\ttry {\n\t\tnm = hashAlgo.toUpperCase();\n\t\tv = crypto.createVerify(nm);\n\t} catch (e) {\n\t\terr = e;\n\t}\n\tif (v === undefined || (err instanceof Error &&\n\t    err.message.match(/Unknown message digest/))) {\n\t\tnm = 'RSA-';\n\t\tnm += hashAlgo.toUpperCase();\n\t\tv = crypto.createVerify(nm);\n\t}\n\tassert.ok(v, 'failed to create verifier');\n\tvar oldVerify = v.verify.bind(v);\n\tvar key = this.toBuffer('pkcs8');\n\tvar curve = this.curve;\n\tvar self = this;\n\tv.verify = function (signature, fmt) {\n\t\tif (Signature.isSignature(signature, [2, 0])) {\n\t\t\tif (signature.type !== self.type)\n\t\t\t\treturn (false);\n\t\t\tif (signature.hashAlgorithm &&\n\t\t\t    signature.hashAlgorithm !== hashAlgo)\n\t\t\t\treturn (false);\n\t\t\tif (signature.curve && self.type === 'ecdsa' &&\n\t\t\t    signature.curve !== curve)\n\t\t\t\treturn (false);\n\t\t\treturn (oldVerify(key, signature.toBuffer('asn1')));\n\n\t\t} else if (typeof (signature) === 'string' ||\n\t\t    Buffer.isBuffer(signature)) {\n\t\t\treturn (oldVerify(key, signature, fmt));\n\n\t\t/*\n\t\t * Avoid doing this on valid arguments, walking the prototype\n\t\t * chain can be quite slow.\n\t\t */\n\t\t} else if (Signature.isSignature(signature, [1, 0])) {\n\t\t\tthrow (new Error('signature was created by too old ' +\n\t\t\t    'a version of sshpk and cannot be verified'));\n\n\t\t} else {\n\t\t\tthrow (new TypeError('signature must be a string, ' +\n\t\t\t    'Buffer, or Signature object'));\n\t\t}\n\t};\n\treturn (v);\n};\n\nKey.prototype.createDiffieHellman = function () {\n\tif (this.type === 'rsa')\n\t\tthrow (new Error('RSA keys do not support Diffie-Hellman'));\n\n\treturn (new DiffieHellman(this));\n};\nKey.prototype.createDH = Key.prototype.createDiffieHellman;\n\nKey.parse = function (data, format, options) {\n\tif (typeof (data) !== 'string')\n\t\tassert.buffer(data, 'data');\n\tif (format === undefined)\n\t\tformat = 'auto';\n\tassert.string(format, 'format');\n\tif (typeof (options) === 'string')\n\t\toptions = { filename: options };\n\tassert.optionalObject(options, 'options');\n\tif (options === undefined)\n\t\toptions = {};\n\tassert.optionalString(options.filename, 'options.filename');\n\tif (options.filename === undefined)\n\t\toptions.filename = '(unnamed)';\n\n\tassert.object(formats[format], 'formats[format]');\n\n\ttry {\n\t\tvar k = formats[format].read(data, options);\n\t\tif (k instanceof PrivateKey)\n\t\t\tk = k.toPublic();\n\t\tif (!k.comment)\n\t\t\tk.comment = options.filename;\n\t\treturn (k);\n\t} catch (e) {\n\t\tif (e.name === 'KeyEncryptedError')\n\t\t\tthrow (e);\n\t\tthrow (new KeyParseError(options.filename, format, e));\n\t}\n};\n\nKey.isKey = function (obj, ver) {\n\treturn (utils.isCompatible(obj, Key, ver));\n};\n\n/*\n * API versions for Key:\n * [1,0] -- initial ver, may take Signature for createVerify or may not\n * [1,1] -- added pkcs1, pkcs8 formats\n * [1,2] -- added auto, ssh-private, openssh formats\n * [1,3] -- added defaultHashAlgorithm\n * [1,4] -- added ed support, createDH\n * [1,5] -- first explicitly tagged version\n * [1,6] -- changed ed25519 part names\n * [1,7] -- spki hash types\n */\nKey.prototype._sshpkApiVersion = [1, 7];\n\nKey._oldVersionDetect = function (obj) {\n\tassert.func(obj.toBuffer);\n\tassert.func(obj.fingerprint);\n\tif (obj.createDH)\n\t\treturn ([1, 4]);\n\tif (obj.defaultHashAlgorithm)\n\t\treturn ([1, 3]);\n\tif (obj.formats['auto'])\n\t\treturn ([1, 2]);\n\tif (obj.formats['pkcs1'])\n\t\treturn ([1, 1]);\n\treturn ([1, 0]);\n};\n", "// Copyright 2015 Joyent, Inc.\n\nvar Buffer = require('safer-buffer').Buffer;\n\nvar algInfo = {\n\t'dsa': {\n\t\tparts: ['p', 'q', 'g', 'y'],\n\t\tsizePart: 'p'\n\t},\n\t'rsa': {\n\t\tparts: ['e', 'n'],\n\t\tsizePart: 'n'\n\t},\n\t'ecdsa': {\n\t\tparts: ['curve', 'Q'],\n\t\tsizePart: 'Q'\n\t},\n\t'ed25519': {\n\t\tparts: ['A'],\n\t\tsizePart: 'A'\n\t}\n};\nalgInfo['curve25519'] = algInfo['ed25519'];\n\nvar algPrivInfo = {\n\t'dsa': {\n\t\tparts: ['p', 'q', 'g', 'y', 'x']\n\t},\n\t'rsa': {\n\t\tparts: ['n', 'e', 'd', 'iqmp', 'p', 'q']\n\t},\n\t'ecdsa': {\n\t\tparts: ['curve', 'Q', 'd']\n\t},\n\t'ed25519': {\n\t\tparts: ['A', 'k']\n\t}\n};\nalgPrivInfo['curve25519'] = algPrivInfo['ed25519'];\n\nvar hashAlgs = {\n\t'md5': true,\n\t'sha1': true,\n\t'sha256': true,\n\t'sha384': true,\n\t'sha512': true\n};\n\n/*\n * Taken from\n * http://csrc.nist.gov/groups/ST/toolkit/documents/dss/NISTReCur.pdf\n */\nvar curves = {\n\t'nistp256': {\n\t\tsize: 256,\n\t\tpkcs8oid: '1.2.840.10045.3.1.7',\n\t\tp: Buffer.from(('00' +\n\t\t    'ffffffff 00000001 00000000 00000000' +\n\t\t    '00000000 ffffffff ffffffff ffffffff').\n\t\t    replace(/ /g, ''), 'hex'),\n\t\ta: Buffer.from(('00' +\n\t\t    'FFFFFFFF 00000001 00000000 00000000' +\n\t\t    '00000000 FFFFFFFF FFFFFFFF FFFFFFFC').\n\t\t    replace(/ /g, ''), 'hex'),\n\t\tb: Buffer.from((\n\t\t    '5ac635d8 aa3a93e7 b3ebbd55 769886bc' +\n\t\t    '651d06b0 cc53b0f6 3bce3c3e 27d2604b').\n\t\t    replace(/ /g, ''), 'hex'),\n\t\ts: Buffer.from(('00' +\n\t\t    'c49d3608 86e70493 6a6678e1 139d26b7' +\n\t\t    '819f7e90').\n\t\t    replace(/ /g, ''), 'hex'),\n\t\tn: Buffer.from(('00' +\n\t\t    'ffffffff 00000000 ffffffff ffffffff' +\n\t\t    'bce6faad a7179e84 f3b9cac2 fc632551').\n\t\t    replace(/ /g, ''), 'hex'),\n\t\tG: Buffer.from(('04' +\n\t\t    '6b17d1f2 e12c4247 f8bce6e5 63a440f2' +\n\t\t    '77037d81 2deb33a0 f4a13945 d898c296' +\n\t\t    '4fe342e2 fe1a7f9b 8ee7eb4a 7c0f9e16' +\n\t\t    '2bce3357 6b315ece cbb64068 37bf51f5').\n\t\t    replace(/ /g, ''), 'hex')\n\t},\n\t'nistp384': {\n\t\tsize: 384,\n\t\tpkcs8oid: '1.3.132.0.34',\n\t\tp: Buffer.from(('00' +\n\t\t    'ffffffff ffffffff ffffffff ffffffff' +\n\t\t    'ffffffff ffffffff ffffffff fffffffe' +\n\t\t    'ffffffff 00000000 00000000 ffffffff').\n\t\t    replace(/ /g, ''), 'hex'),\n\t\ta: Buffer.from(('00' +\n\t\t    'FFFFFFFF FFFFFFFF FFFFFFFF FFFFFFFF' +\n\t\t    'FFFFFFFF FFFFFFFF FFFFFFFF FFFFFFFE' +\n\t\t    'FFFFFFFF 00000000 00000000 FFFFFFFC').\n\t\t    replace(/ /g, ''), 'hex'),\n\t\tb: Buffer.from((\n\t\t    'b3312fa7 e23ee7e4 988e056b e3f82d19' +\n\t\t    '181d9c6e fe814112 0314088f 5013875a' +\n\t\t    'c656398d 8a2ed19d 2a85c8ed d3ec2aef').\n\t\t    replace(/ /g, ''), 'hex'),\n\t\ts: Buffer.from(('00' +\n\t\t    'a335926a a319a27a 1d00896a 6773a482' +\n\t\t    '7acdac73').\n\t\t    replace(/ /g, ''), 'hex'),\n\t\tn: Buffer.from(('00' +\n\t\t    'ffffffff ffffffff ffffffff ffffffff' +\n\t\t    'ffffffff ffffffff c7634d81 f4372ddf' +\n\t\t    '581a0db2 48b0a77a ecec196a ccc52973').\n\t\t    replace(/ /g, ''), 'hex'),\n\t\tG: Buffer.from(('04' +\n\t\t    'aa87ca22 be8b0537 8eb1c71e f320ad74' +\n\t\t    '6e1d3b62 8ba79b98 59f741e0 82542a38' +\n\t\t    '5502f25d bf55296c 3a545e38 72760ab7' +\n\t\t    '3617de4a 96262c6f 5d9e98bf 9292dc29' +\n\t\t    'f8f41dbd 289a147c e9da3113 b5f0b8c0' +\n\t\t    '0a60b1ce 1d7e819d 7a431d7c 90ea0e5f').\n\t\t    replace(/ /g, ''), 'hex')\n\t},\n\t'nistp521': {\n\t\tsize: 521,\n\t\tpkcs8oid: '1.3.132.0.35',\n\t\tp: Buffer.from((\n\t\t    '01ffffff ffffffff ffffffff ffffffff' +\n\t\t    'ffffffff ffffffff ffffffff ffffffff' +\n\t\t    'ffffffff ffffffff ffffffff ffffffff' +\n\t\t    'ffffffff ffffffff ffffffff ffffffff' +\n\t\t    'ffff').replace(/ /g, ''), 'hex'),\n\t\ta: Buffer.from(('01FF' +\n\t\t    'FFFFFFFF FFFFFFFF FFFFFFFF FFFFFFFF' +\n\t\t    'FFFFFFFF FFFFFFFF FFFFFFFF FFFFFFFF' +\n\t\t    'FFFFFFFF FFFFFFFF FFFFFFFF FFFFFFFF' +\n\t\t    'FFFFFFFF FFFFFFFF FFFFFFFF FFFFFFFC').\n\t\t    replace(/ /g, ''), 'hex'),\n\t\tb: Buffer.from(('51' +\n\t\t    '953eb961 8e1c9a1f 929a21a0 b68540ee' +\n\t\t    'a2da725b 99b315f3 b8b48991 8ef109e1' +\n\t\t    '56193951 ec7e937b 1652c0bd 3bb1bf07' +\n\t\t    '3573df88 3d2c34f1 ef451fd4 6b503f00').\n\t\t    replace(/ /g, ''), 'hex'),\n\t\ts: Buffer.from(('00' +\n\t\t    'd09e8800 291cb853 96cc6717 393284aa' +\n\t\t    'a0da64ba').replace(/ /g, ''), 'hex'),\n\t\tn: Buffer.from(('01ff' +\n\t\t    'ffffffff ffffffff ffffffff ffffffff' +\n\t\t    'ffffffff ffffffff ffffffff fffffffa' +\n\t\t    '51868783 bf2f966b 7fcc0148 f709a5d0' +\n\t\t    '3bb5c9b8 899c47ae bb6fb71e 91386409').\n\t\t    replace(/ /g, ''), 'hex'),\n\t\tG: Buffer.from(('04' +\n\t\t    '00c6 858e06b7 0404e9cd 9e3ecb66 2395b442' +\n\t\t         '9c648139 053fb521 f828af60 6b4d3dba' +\n\t\t         'a14b5e77 efe75928 fe1dc127 a2ffa8de' +\n\t\t         '3348b3c1 856a429b f97e7e31 c2e5bd66' +\n\t\t    '0118 39296a78 9a3bc004 5c8a5fb4 2c7d1bd9' +\n\t\t         '98f54449 579b4468 17afbd17 273e662c' +\n\t\t         '97ee7299 5ef42640 c550b901 3fad0761' +\n\t\t         '353c7086 a272c240 88be9476 9fd16650').\n\t\t    replace(/ /g, ''), 'hex')\n\t}\n};\n\nmodule.exports = {\n\tinfo: algInfo,\n\tprivInfo: algPrivInfo,\n\thashAlgs: hashAlgs,\n\tcurves: curves\n};\n", "// Copyright 2018 Joyent, Inc.\n\nmodule.exports = Fingerprint;\n\nvar assert = require('assert-plus');\nvar Buffer = require('safer-buffer').Buffer;\nvar algs = require('./algs');\nvar crypto = require('crypto');\nvar errs = require('./errors');\nvar Key = require('./key');\nvar PrivateKey = require('./private-key');\nvar Certificate = require('./certificate');\nvar utils = require('./utils');\n\nvar FingerprintFormatError = errs.FingerprintFormatError;\nvar InvalidAlgorithmError = errs.InvalidAlgorithmError;\n\nfunction Fingerprint(opts) {\n\tassert.object(opts, 'options');\n\tassert.string(opts.type, 'options.type');\n\tassert.buffer(opts.hash, 'options.hash');\n\tassert.string(opts.algorithm, 'options.algorithm');\n\n\tthis.algorithm = opts.algorithm.toLowerCase();\n\tif (algs.hashAlgs[this.algorithm] !== true)\n\t\tthrow (new InvalidAlgorithmError(this.algorithm));\n\n\tthis.hash = opts.hash;\n\tthis.type = opts.type;\n\tthis.hashType = opts.hashType;\n}\n\nFingerprint.prototype.toString = function (format) {\n\tif (format === undefined) {\n\t\tif (this.algorithm === 'md5' || this.hashType === 'spki')\n\t\t\tformat = 'hex';\n\t\telse\n\t\t\tformat = 'base64';\n\t}\n\tassert.string(format);\n\n\tswitch (format) {\n\tcase 'hex':\n\t\tif (this.hashType === 'spki')\n\t\t\treturn (this.hash.toString('hex'));\n\t\treturn (addColons(this.hash.toString('hex')));\n\tcase 'base64':\n\t\tif (this.hashType === 'spki')\n\t\t\treturn (this.hash.toString('base64'));\n\t\treturn (sshBase64Format(this.algorithm,\n\t\t    this.hash.toString('base64')));\n\tdefault:\n\t\tthrow (new FingerprintFormatError(undefined, format));\n\t}\n};\n\nFingerprint.prototype.matches = function (other) {\n\tassert.object(other, 'key or certificate');\n\tif (this.type === 'key' && this.hashType !== 'ssh') {\n\t\tutils.assertCompatible(other, Key, [1, 7], 'key with spki');\n\t\tif (PrivateKey.isPrivateKey(other)) {\n\t\t\tutils.assertCompatible(other, PrivateKey, [1, 6],\n\t\t\t    'privatekey with spki support');\n\t\t}\n\t} else if (this.type === 'key') {\n\t\tutils.assertCompatible(other, Key, [1, 0], 'key');\n\t} else {\n\t\tutils.assertCompatible(other, Certificate, [1, 0],\n\t\t    'certificate');\n\t}\n\n\tvar theirHash = other.hash(this.algorithm, this.hashType);\n\tvar theirHash2 = crypto.createHash(this.algorithm).\n\t    update(theirHash).digest('base64');\n\n\tif (this.hash2 === undefined)\n\t\tthis.hash2 = crypto.createHash(this.algorithm).\n\t\t    update(this.hash).digest('base64');\n\n\treturn (this.hash2 === theirHash2);\n};\n\n/*JSSTYLED*/\nvar base64RE = /^[A-Za-z0-9+\\/=]+$/;\n/*JSSTYLED*/\nvar hexRE = /^[a-fA-F0-9]+$/;\n\nFingerprint.parse = function (fp, options) {\n\tassert.string(fp, 'fingerprint');\n\n\tvar alg, hash, enAlgs;\n\tif (Array.isArray(options)) {\n\t\tenAlgs = options;\n\t\toptions = {};\n\t}\n\tassert.optionalObject(options, 'options');\n\tif (options === undefined)\n\t\toptions = {};\n\tif (options.enAlgs !== undefined)\n\t\tenAlgs = options.enAlgs;\n\tif (options.algorithms !== undefined)\n\t\tenAlgs = options.algorithms;\n\tassert.optionalArrayOfString(enAlgs, 'algorithms');\n\n\tvar hashType = 'ssh';\n\tif (options.hashType !== undefined)\n\t\thashType = options.hashType;\n\tassert.string(hashType, 'options.hashType');\n\n\tvar parts = fp.split(':');\n\tif (parts.length == 2) {\n\t\talg = parts[0].toLowerCase();\n\t\tif (!base64RE.test(parts[1]))\n\t\t\tthrow (new FingerprintFormatError(fp));\n\t\ttry {\n\t\t\thash = Buffer.from(parts[1], 'base64');\n\t\t} catch (e) {\n\t\t\tthrow (new FingerprintFormatError(fp));\n\t\t}\n\t} else if (parts.length > 2) {\n\t\talg = 'md5';\n\t\tif (parts[0].toLowerCase() === 'md5')\n\t\t\tparts = parts.slice(1);\n\t\tparts = parts.map(function (p) {\n\t\t\twhile (p.length < 2)\n\t\t\t\tp = '0' + p;\n\t\t\tif (p.length > 2)\n\t\t\t\tthrow (new FingerprintFormatError(fp));\n\t\t\treturn (p);\n\t\t});\n\t\tparts = parts.join('');\n\t\tif (!hexRE.test(parts) || parts.length % 2 !== 0)\n\t\t\tthrow (new FingerprintFormatError(fp));\n\t\ttry {\n\t\t\thash = Buffer.from(parts, 'hex');\n\t\t} catch (e) {\n\t\t\tthrow (new FingerprintFormatError(fp));\n\t\t}\n\t} else {\n\t\tif (hexRE.test(fp)) {\n\t\t\thash = Buffer.from(fp, 'hex');\n\t\t} else if (base64RE.test(fp)) {\n\t\t\thash = Buffer.from(fp, 'base64');\n\t\t} else {\n\t\t\tthrow (new FingerprintFormatError(fp));\n\t\t}\n\n\t\tswitch (hash.length) {\n\t\tcase 32:\n\t\t\talg = 'sha256';\n\t\t\tbreak;\n\t\tcase 16:\n\t\t\talg = 'md5';\n\t\t\tbreak;\n\t\tcase 20:\n\t\t\talg = 'sha1';\n\t\t\tbreak;\n\t\tcase 64:\n\t\t\talg = 'sha512';\n\t\t\tbreak;\n\t\tdefault:\n\t\t\tthrow (new FingerprintFormatError(fp));\n\t\t}\n\n\t\t/* Plain hex/base64: guess it's probably SPKI unless told. */\n\t\tif (options.hashType === undefined)\n\t\t\thashType = 'spki';\n\t}\n\n\tif (alg === undefined)\n\t\tthrow (new FingerprintFormatError(fp));\n\n\tif (algs.hashAlgs[alg] === undefined)\n\t\tthrow (new InvalidAlgorithmError(alg));\n\n\tif (enAlgs !== undefined) {\n\t\tenAlgs = enAlgs.map(function (a) { return a.toLowerCase(); });\n\t\tif (enAlgs.indexOf(alg) === -1)\n\t\t\tthrow (new InvalidAlgorithmError(alg));\n\t}\n\n\treturn (new Fingerprint({\n\t\talgorithm: alg,\n\t\thash: hash,\n\t\ttype: options.type || 'key',\n\t\thashType: hashType\n\t}));\n};\n\nfunction addColons(s) {\n\t/*JSSTYLED*/\n\treturn (s.replace(/(.{2})(?=.)/g, '$1:'));\n}\n\nfunction base64Strip(s) {\n\t/*JSSTYLED*/\n\treturn (s.replace(/=*$/, ''));\n}\n\nfunction sshBase64Format(alg, h) {\n\treturn (alg.toUpperCase() + ':' + base64Strip(h));\n}\n\nFingerprint.isFingerprint = function (obj, ver) {\n\treturn (utils.isCompatible(obj, Fingerprint, ver));\n};\n\n/*\n * API versions for Fingerprint:\n * [1,0] -- initial ver\n * [1,1] -- first tagged ver\n * [1,2] -- hashType and spki support\n */\nFingerprint.prototype._sshpkApiVersion = [1, 2];\n\nFingerprint._oldVersionDetect = function (obj) {\n\tassert.func(obj.toString);\n\tassert.func(obj.matches);\n\treturn ([1, 0]);\n};\n", "// Copyright 2015 Joyent, Inc.\n\nvar assert = require('assert-plus');\nvar util = require('util');\n\nfunction FingerprintFormatError(fp, format) {\n\tif (Error.captureStackTrace)\n\t\tError.captureStackTrace(this, FingerprintFormatError);\n\tthis.name = 'FingerprintFormatError';\n\tthis.fingerprint = fp;\n\tthis.format = format;\n\tthis.message = 'Fingerprint format is not supported, or is invalid: ';\n\tif (fp !== undefined)\n\t\tthis.message += ' fingerprint = ' + fp;\n\tif (format !== undefined)\n\t\tthis.message += ' format = ' + format;\n}\nutil.inherits(FingerprintFormatError, Error);\n\nfunction InvalidAlgorithmError(alg) {\n\tif (Error.captureStackTrace)\n\t\tError.captureStackTrace(this, InvalidAlgorithmError);\n\tthis.name = 'InvalidAlgorithmError';\n\tthis.algorithm = alg;\n\tthis.message = 'Algorithm \"' + alg + '\" is not supported';\n}\nutil.inherits(InvalidAlgorithmError, Error);\n\nfunction KeyParseError(name, format, innerErr) {\n\tif (Error.captureStackTrace)\n\t\tError.captureStackTrace(this, KeyParseError);\n\tthis.name = 'KeyParseError';\n\tthis.format = format;\n\tthis.keyName = name;\n\tthis.innerErr = innerErr;\n\tthis.message = 'Failed to parse ' + name + ' as a valid ' + format +\n\t    ' format key: ' + innerErr.message;\n}\nutil.inherits(KeyParseError, Error);\n\nfunction SignatureParseError(type, format, innerErr) {\n\tif (Error.captureStackTrace)\n\t\tError.captureStackTrace(this, SignatureParseError);\n\tthis.name = 'SignatureParseError';\n\tthis.type = type;\n\tthis.format = format;\n\tthis.innerErr = innerErr;\n\tthis.message = 'Failed to parse the given data as a ' + type +\n\t    ' signature in ' + format + ' format: ' + innerErr.message;\n}\nutil.inherits(SignatureParseError, Error);\n\nfunction CertificateParseError(name, format, innerErr) {\n\tif (Error.captureStackTrace)\n\t\tError.captureStackTrace(this, CertificateParseError);\n\tthis.name = 'CertificateParseError';\n\tthis.format = format;\n\tthis.certName = name;\n\tthis.innerErr = innerErr;\n\tthis.message = 'Failed to parse ' + name + ' as a valid ' + format +\n\t    ' format certificate: ' + innerErr.message;\n}\nutil.inherits(CertificateParseError, Error);\n\nfunction KeyEncryptedError(name, format) {\n\tif (Error.captureStackTrace)\n\t\tError.captureStackTrace(this, KeyEncryptedError);\n\tthis.name = 'KeyEncryptedError';\n\tthis.format = format;\n\tthis.keyName = name;\n\tthis.message = 'The ' + format + ' format key ' + name + ' is ' +\n\t    'encrypted (password-protected), and no passphrase was ' +\n\t    'provided in `options`';\n}\nutil.inherits(KeyEncryptedError, Error);\n\nmodule.exports = {\n\tFingerprintFormatError: FingerprintFormatError,\n\tInvalidAlgorithmError: InvalidAlgorithmError,\n\tKeyParseError: KeyParseError,\n\tSignatureParseError: SignatureParseError,\n\tKeyEncryptedError: KeyEncryptedError,\n\tCertificateParseError: CertificateParseError\n};\n", "// Copyright 2017 Joyent, Inc.\n\nmodule.exports = PrivateKey;\n\nvar assert = require('assert-plus');\nvar Buffer = require('safer-buffer').Buffer;\nvar algs = require('./algs');\nvar crypto = require('crypto');\nvar Fingerprint = require('./fingerprint');\nvar Signature = require('./signature');\nvar errs = require('./errors');\nvar util = require('util');\nvar utils = require('./utils');\nvar dhe = require('./dhe');\nvar generateECDSA = dhe.generateECDSA;\nvar generateED25519 = dhe.generateED25519;\nvar edCompat = require('./ed-compat');\nvar nacl = require('tweetnacl');\n\nvar Key = require('./key');\n\nvar InvalidAlgorithmError = errs.InvalidAlgorithmError;\nvar KeyParseError = errs.KeyParseError;\nvar KeyEncryptedError = errs.KeyEncryptedError;\n\nvar formats = {};\nformats['auto'] = require('./formats/auto');\nformats['pem'] = require('./formats/pem');\nformats['pkcs1'] = require('./formats/pkcs1');\nformats['pkcs8'] = require('./formats/pkcs8');\nformats['rfc4253'] = require('./formats/rfc4253');\nformats['ssh-private'] = require('./formats/ssh-private');\nformats['openssh'] = formats['ssh-private'];\nformats['ssh'] = formats['ssh-private'];\nformats['dnssec'] = require('./formats/dnssec');\nformats['putty'] = require('./formats/putty');\n\nfunction PrivateKey(opts) {\n\tassert.object(opts, 'options');\n\tKey.call(this, opts);\n\n\tthis._pubCache = undefined;\n}\nutil.inherits(PrivateKey, Key);\n\nPrivateKey.formats = formats;\n\nPrivateKey.prototype.toBuffer = function (format, options) {\n\tif (format === undefined)\n\t\tformat = 'pkcs1';\n\tassert.string(format, 'format');\n\tassert.object(formats[format], 'formats[format]');\n\tassert.optionalObject(options, 'options');\n\n\treturn (formats[format].write(this, options));\n};\n\nPrivateKey.prototype.hash = function (algo, type) {\n\treturn (this.toPublic().hash(algo, type));\n};\n\nPrivateKey.prototype.fingerprint = function (algo, type) {\n\treturn (this.toPublic().fingerprint(algo, type));\n};\n\nPrivateKey.prototype.toPublic = function () {\n\tif (this._pubCache)\n\t\treturn (this._pubCache);\n\n\tvar algInfo = algs.info[this.type];\n\tvar pubParts = [];\n\tfor (var i = 0; i < algInfo.parts.length; ++i) {\n\t\tvar p = algInfo.parts[i];\n\t\tpubParts.push(this.part[p]);\n\t}\n\n\tthis._pubCache = new Key({\n\t\ttype: this.type,\n\t\tsource: this,\n\t\tparts: pubParts\n\t});\n\tif (this.comment)\n\t\tthis._pubCache.comment = this.comment;\n\treturn (this._pubCache);\n};\n\nPrivateKey.prototype.derive = function (newType) {\n\tassert.string(newType, 'type');\n\tvar priv, pub, pair;\n\n\tif (this.type === 'ed25519' && newType === 'curve25519') {\n\t\tpriv = this.part.k.data;\n\t\tif (priv[0] === 0x00)\n\t\t\tpriv = priv.slice(1);\n\n\t\tpair = nacl.box.keyPair.fromSecretKey(new Uint8Array(priv));\n\t\tpub = Buffer.from(pair.publicKey);\n\n\t\treturn (new PrivateKey({\n\t\t\ttype: 'curve25519',\n\t\t\tparts: [\n\t\t\t\t{ name: 'A', data: utils.mpNormalize(pub) },\n\t\t\t\t{ name: 'k', data: utils.mpNormalize(priv) }\n\t\t\t]\n\t\t}));\n\t} else if (this.type === 'curve25519' && newType === 'ed25519') {\n\t\tpriv = this.part.k.data;\n\t\tif (priv[0] === 0x00)\n\t\t\tpriv = priv.slice(1);\n\n\t\tpair = nacl.sign.keyPair.fromSeed(new Uint8Array(priv));\n\t\tpub = Buffer.from(pair.publicKey);\n\n\t\treturn (new PrivateKey({\n\t\t\ttype: 'ed25519',\n\t\t\tparts: [\n\t\t\t\t{ name: 'A', data: utils.mpNormalize(pub) },\n\t\t\t\t{ name: 'k', data: utils.mpNormalize(priv) }\n\t\t\t]\n\t\t}));\n\t}\n\tthrow (new Error('Key derivation not supported from ' + this.type +\n\t    ' to ' + newType));\n};\n\nPrivateKey.prototype.createVerify = function (hashAlgo) {\n\treturn (this.toPublic().createVerify(hashAlgo));\n};\n\nPrivateKey.prototype.createSign = function (hashAlgo) {\n\tif (hashAlgo === undefined)\n\t\thashAlgo = this.defaultHashAlgorithm();\n\tassert.string(hashAlgo, 'hash algorithm');\n\n\t/* ED25519 is not supported by OpenSSL, use a javascript impl. */\n\tif (this.type === 'ed25519' && edCompat !== undefined)\n\t\treturn (new edCompat.Signer(this, hashAlgo));\n\tif (this.type === 'curve25519')\n\t\tthrow (new Error('Curve25519 keys are not suitable for ' +\n\t\t    'signing or verification'));\n\n\tvar v, nm, err;\n\ttry {\n\t\tnm = hashAlgo.toUpperCase();\n\t\tv = crypto.createSign(nm);\n\t} catch (e) {\n\t\terr = e;\n\t}\n\tif (v === undefined || (err instanceof Error &&\n\t    err.message.match(/Unknown message digest/))) {\n\t\tnm = 'RSA-';\n\t\tnm += hashAlgo.toUpperCase();\n\t\tv = crypto.createSign(nm);\n\t}\n\tassert.ok(v, 'failed to create verifier');\n\tvar oldSign = v.sign.bind(v);\n\tvar key = this.toBuffer('pkcs1');\n\tvar type = this.type;\n\tvar curve = this.curve;\n\tv.sign = function () {\n\t\tvar sig = oldSign(key);\n\t\tif (typeof (sig) === 'string')\n\t\t\tsig = Buffer.from(sig, 'binary');\n\t\tsig = Signature.parse(sig, type, 'asn1');\n\t\tsig.hashAlgorithm = hashAlgo;\n\t\tsig.curve = curve;\n\t\treturn (sig);\n\t};\n\treturn (v);\n};\n\nPrivateKey.parse = function (data, format, options) {\n\tif (typeof (data) !== 'string')\n\t\tassert.buffer(data, 'data');\n\tif (format === undefined)\n\t\tformat = 'auto';\n\tassert.string(format, 'format');\n\tif (typeof (options) === 'string')\n\t\toptions = { filename: options };\n\tassert.optionalObject(options, 'options');\n\tif (options === undefined)\n\t\toptions = {};\n\tassert.optionalString(options.filename, 'options.filename');\n\tif (options.filename === undefined)\n\t\toptions.filename = '(unnamed)';\n\n\tassert.object(formats[format], 'formats[format]');\n\n\ttry {\n\t\tvar k = formats[format].read(data, options);\n\t\tassert.ok(k instanceof PrivateKey, 'key is not a private key');\n\t\tif (!k.comment)\n\t\t\tk.comment = options.filename;\n\t\treturn (k);\n\t} catch (e) {\n\t\tif (e.name === 'KeyEncryptedError')\n\t\t\tthrow (e);\n\t\tthrow (new KeyParseError(options.filename, format, e));\n\t}\n};\n\nPrivateKey.isPrivateKey = function (obj, ver) {\n\treturn (utils.isCompatible(obj, PrivateKey, ver));\n};\n\nPrivateKey.generate = function (type, options) {\n\tif (options === undefined)\n\t\toptions = {};\n\tassert.object(options, 'options');\n\n\tswitch (type) {\n\tcase 'ecdsa':\n\t\tif (options.curve === undefined)\n\t\t\toptions.curve = 'nistp256';\n\t\tassert.string(options.curve, 'options.curve');\n\t\treturn (generateECDSA(options.curve));\n\tcase 'ed25519':\n\t\treturn (generateED25519());\n\tdefault:\n\t\tthrow (new Error('Key generation not supported with key ' +\n\t\t    'type \"' + type + '\"'));\n\t}\n};\n\n/*\n * API versions for PrivateKey:\n * [1,0] -- initial ver\n * [1,1] -- added auto, pkcs[18], openssh/ssh-private formats\n * [1,2] -- added defaultHashAlgorithm\n * [1,3] -- added derive, ed, createDH\n * [1,4] -- first tagged version\n * [1,5] -- changed ed25519 part names and format\n * [1,6] -- type arguments for hash() and fingerprint()\n */\nPrivateKey.prototype._sshpkApiVersion = [1, 6];\n\nPrivateKey._oldVersionDetect = function (obj) {\n\tassert.func(obj.toPublic);\n\tassert.func(obj.createSign);\n\tif (obj.derive)\n\t\treturn ([1, 3]);\n\tif (obj.defaultHashAlgorithm)\n\t\treturn ([1, 2]);\n\tif (obj.formats['auto'])\n\t\treturn ([1, 1]);\n\treturn ([1, 0]);\n};\n", "// Copyright 2015 Joyent, Inc.\n\nmodule.exports = Signature;\n\nvar assert = require('assert-plus');\nvar Buffer = require('safer-buffer').Buffer;\nvar algs = require('./algs');\nvar crypto = require('crypto');\nvar errs = require('./errors');\nvar utils = require('./utils');\nvar asn1 = require('asn1');\nvar SSHBuffer = require('./ssh-buffer');\n\nvar InvalidAlgorithmError = errs.InvalidAlgorithmError;\nvar SignatureParseError = errs.SignatureParseError;\n\nfunction Signature(opts) {\n\tassert.object(opts, 'options');\n\tassert.arrayOfObject(opts.parts, 'options.parts');\n\tassert.string(opts.type, 'options.type');\n\n\tvar partLookup = {};\n\tfor (var i = 0; i < opts.parts.length; ++i) {\n\t\tvar part = opts.parts[i];\n\t\tpartLookup[part.name] = part;\n\t}\n\n\tthis.type = opts.type;\n\tthis.hashAlgorithm = opts.hashAlgo;\n\tthis.curve = opts.curve;\n\tthis.parts = opts.parts;\n\tthis.part = partLookup;\n}\n\nSignature.prototype.toBuffer = function (format) {\n\tif (format === undefined)\n\t\tformat = 'asn1';\n\tassert.string(format, 'format');\n\n\tvar buf;\n\tvar stype = 'ssh-' + this.type;\n\n\tswitch (this.type) {\n\tcase 'rsa':\n\t\tswitch (this.hashAlgorithm) {\n\t\tcase 'sha256':\n\t\t\tstype = 'rsa-sha2-256';\n\t\t\tbreak;\n\t\tcase 'sha512':\n\t\t\tstype = 'rsa-sha2-512';\n\t\t\tbreak;\n\t\tcase 'sha1':\n\t\tcase undefined:\n\t\t\tbreak;\n\t\tdefault:\n\t\t\tthrow (new Error('SSH signature ' +\n\t\t\t    'format does not support hash ' +\n\t\t\t    'algorithm ' + this.hashAlgorithm));\n\t\t}\n\t\tif (format === 'ssh') {\n\t\t\tbuf = new SSHBuffer({});\n\t\t\tbuf.writeString(stype);\n\t\t\tbuf.writePart(this.part.sig);\n\t\t\treturn (buf.toBuffer());\n\t\t} else {\n\t\t\treturn (this.part.sig.data);\n\t\t}\n\t\tbreak;\n\n\tcase 'ed25519':\n\t\tif (format === 'ssh') {\n\t\t\tbuf = new SSHBuffer({});\n\t\t\tbuf.writeString(stype);\n\t\t\tbuf.writePart(this.part.sig);\n\t\t\treturn (buf.toBuffer());\n\t\t} else {\n\t\t\treturn (this.part.sig.data);\n\t\t}\n\t\tbreak;\n\n\tcase 'dsa':\n\tcase 'ecdsa':\n\t\tvar r, s;\n\t\tif (format === 'asn1') {\n\t\t\tvar der = new asn1.BerWriter();\n\t\t\tder.startSequence();\n\t\t\tr = utils.mpNormalize(this.part.r.data);\n\t\t\ts = utils.mpNormalize(this.part.s.data);\n\t\t\tder.writeBuffer(r, asn1.Ber.Integer);\n\t\t\tder.writeBuffer(s, asn1.Ber.Integer);\n\t\t\tder.endSequence();\n\t\t\treturn (der.buffer);\n\t\t} else if (format === 'ssh' && this.type === 'dsa') {\n\t\t\tbuf = new SSHBuffer({});\n\t\t\tbuf.writeString('ssh-dss');\n\t\t\tr = this.part.r.data;\n\t\t\tif (r.length > 20 && r[0] === 0x00)\n\t\t\t\tr = r.slice(1);\n\t\t\ts = this.part.s.data;\n\t\t\tif (s.length > 20 && s[0] === 0x00)\n\t\t\t\ts = s.slice(1);\n\t\t\tif ((this.hashAlgorithm &&\n\t\t\t    this.hashAlgorithm !== 'sha1') ||\n\t\t\t    r.length + s.length !== 40) {\n\t\t\t\tthrow (new Error('OpenSSH only supports ' +\n\t\t\t\t    'DSA signatures with SHA1 hash'));\n\t\t\t}\n\t\t\tbuf.writeBuffer(Buffer.concat([r, s]));\n\t\t\treturn (buf.toBuffer());\n\t\t} else if (format === 'ssh' && this.type === 'ecdsa') {\n\t\t\tvar inner = new SSHBuffer({});\n\t\t\tr = this.part.r.data;\n\t\t\tinner.writeBuffer(r);\n\t\t\tinner.writePart(this.part.s);\n\n\t\t\tbuf = new SSHBuffer({});\n\t\t\t/* XXX: find a more proper way to do this? */\n\t\t\tvar curve;\n\t\t\tif (r[0] === 0x00)\n\t\t\t\tr = r.slice(1);\n\t\t\tvar sz = r.length * 8;\n\t\t\tif (sz === 256)\n\t\t\t\tcurve = 'nistp256';\n\t\t\telse if (sz === 384)\n\t\t\t\tcurve = 'nistp384';\n\t\t\telse if (sz === 528)\n\t\t\t\tcurve = 'nistp521';\n\t\t\tbuf.writeString('ecdsa-sha2-' + curve);\n\t\t\tbuf.writeBuffer(inner.toBuffer());\n\t\t\treturn (buf.toBuffer());\n\t\t}\n\t\tthrow (new Error('Invalid signature format'));\n\tdefault:\n\t\tthrow (new Error('Invalid signature data'));\n\t}\n};\n\nSignature.prototype.toString = function (format) {\n\tassert.optionalString(format, 'format');\n\treturn (this.toBuffer(format).toString('base64'));\n};\n\nSignature.parse = function (data, type, format) {\n\tif (typeof (data) === 'string')\n\t\tdata = Buffer.from(data, 'base64');\n\tassert.buffer(data, 'data');\n\tassert.string(format, 'format');\n\tassert.string(type, 'type');\n\n\tvar opts = {};\n\topts.type = type.toLowerCase();\n\topts.parts = [];\n\n\ttry {\n\t\tassert.ok(data.length > 0, 'signature must not be empty');\n\t\tswitch (opts.type) {\n\t\tcase 'rsa':\n\t\t\treturn (parseOneNum(data, type, format, opts));\n\t\tcase 'ed25519':\n\t\t\treturn (parseOneNum(data, type, format, opts));\n\n\t\tcase 'dsa':\n\t\tcase 'ecdsa':\n\t\t\tif (format === 'asn1')\n\t\t\t\treturn (parseDSAasn1(data, type, format, opts));\n\t\t\telse if (opts.type === 'dsa')\n\t\t\t\treturn (parseDSA(data, type, format, opts));\n\t\t\telse\n\t\t\t\treturn (parseECDSA(data, type, format, opts));\n\n\t\tdefault:\n\t\t\tthrow (new InvalidAlgorithmError(type));\n\t\t}\n\n\t} catch (e) {\n\t\tif (e instanceof InvalidAlgorithmError)\n\t\t\tthrow (e);\n\t\tthrow (new SignatureParseError(type, format, e));\n\t}\n};\n\nfunction parseOneNum(data, type, format, opts) {\n\tif (format === 'ssh') {\n\t\ttry {\n\t\t\tvar buf = new SSHBuffer({buffer: data});\n\t\t\tvar head = buf.readString();\n\t\t} catch (e) {\n\t\t\t/* fall through */\n\t\t}\n\t\tif (buf !== undefined) {\n\t\t\tvar msg = 'SSH signature does not match expected ' +\n\t\t\t    'type (expected ' + type + ', got ' + head + ')';\n\t\t\tswitch (head) {\n\t\t\tcase 'ssh-rsa':\n\t\t\t\tassert.strictEqual(type, 'rsa', msg);\n\t\t\t\topts.hashAlgo = 'sha1';\n\t\t\t\tbreak;\n\t\t\tcase 'rsa-sha2-256':\n\t\t\t\tassert.strictEqual(type, 'rsa', msg);\n\t\t\t\topts.hashAlgo = 'sha256';\n\t\t\t\tbreak;\n\t\t\tcase 'rsa-sha2-512':\n\t\t\t\tassert.strictEqual(type, 'rsa', msg);\n\t\t\t\topts.hashAlgo = 'sha512';\n\t\t\t\tbreak;\n\t\t\tcase 'ssh-ed25519':\n\t\t\t\tassert.strictEqual(type, 'ed25519', msg);\n\t\t\t\topts.hashAlgo = 'sha512';\n\t\t\t\tbreak;\n\t\t\tdefault:\n\t\t\t\tthrow (new Error('Unknown SSH signature ' +\n\t\t\t\t    'type: ' + head));\n\t\t\t}\n\t\t\tvar sig = buf.readPart();\n\t\t\tassert.ok(buf.atEnd(), 'extra trailing bytes');\n\t\t\tsig.name = 'sig';\n\t\t\topts.parts.push(sig);\n\t\t\treturn (new Signature(opts));\n\t\t}\n\t}\n\topts.parts.push({name: 'sig', data: data});\n\treturn (new Signature(opts));\n}\n\nfunction parseDSAasn1(data, type, format, opts) {\n\tvar der = new asn1.BerReader(data);\n\tder.readSequence();\n\tvar r = der.readString(asn1.Ber.Integer, true);\n\tvar s = der.readString(asn1.Ber.Integer, true);\n\n\topts.parts.push({name: 'r', data: utils.mpNormalize(r)});\n\topts.parts.push({name: 's', data: utils.mpNormalize(s)});\n\n\treturn (new Signature(opts));\n}\n\nfunction parseDSA(data, type, format, opts) {\n\tif (data.length != 40) {\n\t\tvar buf = new SSHBuffer({buffer: data});\n\t\tvar d = buf.readBuffer();\n\t\tif (d.toString('ascii') === 'ssh-dss')\n\t\t\td = buf.readBuffer();\n\t\tassert.ok(buf.atEnd(), 'extra trailing bytes');\n\t\tassert.strictEqual(d.length, 40, 'invalid inner length');\n\t\tdata = d;\n\t}\n\topts.parts.push({name: 'r', data: data.slice(0, 20)});\n\topts.parts.push({name: 's', data: data.slice(20, 40)});\n\treturn (new Signature(opts));\n}\n\nfunction parseECDSA(data, type, format, opts) {\n\tvar buf = new SSHBuffer({buffer: data});\n\n\tvar r, s;\n\tvar inner = buf.readBuffer();\n\tvar stype = inner.toString('ascii');\n\tif (stype.slice(0, 6) === 'ecdsa-') {\n\t\tvar parts = stype.split('-');\n\t\tassert.strictEqual(parts[0], 'ecdsa');\n\t\tassert.strictEqual(parts[1], 'sha2');\n\t\topts.curve = parts[2];\n\t\tswitch (opts.curve) {\n\t\tcase 'nistp256':\n\t\t\topts.hashAlgo = 'sha256';\n\t\t\tbreak;\n\t\tcase 'nistp384':\n\t\t\topts.hashAlgo = 'sha384';\n\t\t\tbreak;\n\t\tcase 'nistp521':\n\t\t\topts.hashAlgo = 'sha512';\n\t\t\tbreak;\n\t\tdefault:\n\t\t\tthrow (new Error('Unsupported ECDSA curve: ' +\n\t\t\t    opts.curve));\n\t\t}\n\t\tinner = buf.readBuffer();\n\t\tassert.ok(buf.atEnd(), 'extra trailing bytes on outer');\n\t\tbuf = new SSHBuffer({buffer: inner});\n\t\tr = buf.readPart();\n\t} else {\n\t\tr = {data: inner};\n\t}\n\n\ts = buf.readPart();\n\tassert.ok(buf.atEnd(), 'extra trailing bytes');\n\n\tr.name = 'r';\n\ts.name = 's';\n\n\topts.parts.push(r);\n\topts.parts.push(s);\n\treturn (new Signature(opts));\n}\n\nSignature.isSignature = function (obj, ver) {\n\treturn (utils.isCompatible(obj, Signature, ver));\n};\n\n/*\n * API versions for Signature:\n * [1,0] -- initial ver\n * [2,0] -- support for rsa in full ssh format, compat with sshpk-agent\n *          hashAlgorithm property\n * [2,1] -- first tagged version\n */\nSignature.prototype._sshpkApiVersion = [2, 1];\n\nSignature._oldVersionDetect = function (obj) {\n\tassert.func(obj.toBuffer);\n\tif (obj.hasOwnProperty('hashAlgorithm'))\n\t\treturn ([2, 0]);\n\treturn ([1, 0]);\n};\n", "// Copyright 2015 Joyent, Inc.\n\nmodule.exports = {\n\tbufferSplit: bufferSplit,\n\taddRSAMissing: addRSAMissing,\n\tcalculateDSAPublic: calculateDSAPublic,\n\tcalculateED25519Public: calculateED25519Public,\n\tcalculateX25519Public: calculateX25519Public,\n\tmpNormalize: mpNormalize,\n\tmpDenormalize: mpDenormalize,\n\tecNormalize: ecNormalize,\n\tcountZeros: countZeros,\n\tassertCompatible: assertCompatible,\n\tisCompatible: isCompatible,\n\topensslKeyDeriv: opensslKeyDeriv,\n\topensshCipherInfo: opensshCipherInfo,\n\tpublicFromPrivateECDSA: publicFromPrivateECDSA,\n\tzeroPadToLength: zeroPadToLength,\n\twriteBitString: writeBitString,\n\treadBitString: readBitString,\n\tpbkdf2: pbkdf2\n};\n\nvar assert = require('assert-plus');\nvar Buffer = require('safer-buffer').Buffer;\nvar PrivateKey = require('./private-key');\nvar Key = require('./key');\nvar crypto = require('crypto');\nvar algs = require('./algs');\nvar asn1 = require('asn1');\n\nvar ec = require('ecc-jsbn/lib/ec');\nvar jsbn = require('jsbn').BigInteger;\nvar nacl = require('tweetnacl');\n\nvar MAX_CLASS_DEPTH = 3;\n\nfunction isCompatible(obj, klass, needVer) {\n\tif (obj === null || typeof (obj) !== 'object')\n\t\treturn (false);\n\tif (needVer === undefined)\n\t\tneedVer = klass.prototype._sshpkApiVersion;\n\tif (obj instanceof klass &&\n\t    klass.prototype._sshpkApiVersion[0] == needVer[0])\n\t\treturn (true);\n\tvar proto = Object.getPrototypeOf(obj);\n\tvar depth = 0;\n\twhile (proto.constructor.name !== klass.name) {\n\t\tproto = Object.getPrototypeOf(proto);\n\t\tif (!proto || ++depth > MAX_CLASS_DEPTH)\n\t\t\treturn (false);\n\t}\n\tif (proto.constructor.name !== klass.name)\n\t\treturn (false);\n\tvar ver = proto._sshpkApiVersion;\n\tif (ver === undefined)\n\t\tver = klass._oldVersionDetect(obj);\n\tif (ver[0] != needVer[0] || ver[1] < needVer[1])\n\t\treturn (false);\n\treturn (true);\n}\n\nfunction assertCompatible(obj, klass, needVer, name) {\n\tif (name === undefined)\n\t\tname = 'object';\n\tassert.ok(obj, name + ' must not be null');\n\tassert.object(obj, name + ' must be an object');\n\tif (needVer === undefined)\n\t\tneedVer = klass.prototype._sshpkApiVersion;\n\tif (obj instanceof klass &&\n\t    klass.prototype._sshpkApiVersion[0] == needVer[0])\n\t\treturn;\n\tvar proto = Object.getPrototypeOf(obj);\n\tvar depth = 0;\n\twhile (proto.constructor.name !== klass.name) {\n\t\tproto = Object.getPrototypeOf(proto);\n\t\tassert.ok(proto && ++depth <= MAX_CLASS_DEPTH,\n\t\t    name + ' must be a ' + klass.name + ' instance');\n\t}\n\tassert.strictEqual(proto.constructor.name, klass.name,\n\t    name + ' must be a ' + klass.name + ' instance');\n\tvar ver = proto._sshpkApiVersion;\n\tif (ver === undefined)\n\t\tver = klass._oldVersionDetect(obj);\n\tassert.ok(ver[0] == needVer[0] && ver[1] >= needVer[1],\n\t    name + ' must be compatible with ' + klass.name + ' klass ' +\n\t    'version ' + needVer[0] + '.' + needVer[1]);\n}\n\nvar CIPHER_LEN = {\n\t'des-ede3-cbc': { key: 24, iv: 8 },\n\t'aes-128-cbc': { key: 16, iv: 16 },\n\t'aes-256-cbc': { key: 32, iv: 16 }\n};\nvar PKCS5_SALT_LEN = 8;\n\nfunction opensslKeyDeriv(cipher, salt, passphrase, count) {\n\tassert.buffer(salt, 'salt');\n\tassert.buffer(passphrase, 'passphrase');\n\tassert.number(count, 'iteration count');\n\n\tvar clen = CIPHER_LEN[cipher];\n\tassert.object(clen, 'supported cipher');\n\n\tsalt = salt.slice(0, PKCS5_SALT_LEN);\n\n\tvar D, D_prev, bufs;\n\tvar material = Buffer.alloc(0);\n\twhile (material.length < clen.key + clen.iv) {\n\t\tbufs = [];\n\t\tif (D_prev)\n\t\t\tbufs.push(D_prev);\n\t\tbufs.push(passphrase);\n\t\tbufs.push(salt);\n\t\tD = Buffer.concat(bufs);\n\t\tfor (var j = 0; j < count; ++j)\n\t\t\tD = crypto.createHash('md5').update(D).digest();\n\t\tmaterial = Buffer.concat([material, D]);\n\t\tD_prev = D;\n\t}\n\n\treturn ({\n\t    key: material.slice(0, clen.key),\n\t    iv: material.slice(clen.key, clen.key + clen.iv)\n\t});\n}\n\n/* See: RFC2898 */\nfunction pbkdf2(hashAlg, salt, iterations, size, passphrase) {\n\tvar hkey = Buffer.alloc(salt.length + 4);\n\tsalt.copy(hkey);\n\n\tvar gen = 0, ts = [];\n\tvar i = 1;\n\twhile (gen < size) {\n\t\tvar t = T(i++);\n\t\tgen += t.length;\n\t\tts.push(t);\n\t}\n\treturn (Buffer.concat(ts).slice(0, size));\n\n\tfunction T(I) {\n\t\thkey.writeUInt32BE(I, hkey.length - 4);\n\n\t\tvar hmac = crypto.createHmac(hashAlg, passphrase);\n\t\thmac.update(hkey);\n\n\t\tvar Ti = hmac.digest();\n\t\tvar Uc = Ti;\n\t\tvar c = 1;\n\t\twhile (c++ < iterations) {\n\t\t\thmac = crypto.createHmac(hashAlg, passphrase);\n\t\t\thmac.update(Uc);\n\t\t\tUc = hmac.digest();\n\t\t\tfor (var x = 0; x < Ti.length; ++x)\n\t\t\t\tTi[x] ^= Uc[x];\n\t\t}\n\t\treturn (Ti);\n\t}\n}\n\n/* Count leading zero bits on a buffer */\nfunction countZeros(buf) {\n\tvar o = 0, obit = 8;\n\twhile (o < buf.length) {\n\t\tvar mask = (1 << obit);\n\t\tif ((buf[o] & mask) === mask)\n\t\t\tbreak;\n\t\tobit--;\n\t\tif (obit < 0) {\n\t\t\to++;\n\t\t\tobit = 8;\n\t\t}\n\t}\n\treturn (o*8 + (8 - obit) - 1);\n}\n\nfunction bufferSplit(buf, chr) {\n\tassert.buffer(buf);\n\tassert.string(chr);\n\n\tvar parts = [];\n\tvar lastPart = 0;\n\tvar matches = 0;\n\tfor (var i = 0; i < buf.length; ++i) {\n\t\tif (buf[i] === chr.charCodeAt(matches))\n\t\t\t++matches;\n\t\telse if (buf[i] === chr.charCodeAt(0))\n\t\t\tmatches = 1;\n\t\telse\n\t\t\tmatches = 0;\n\n\t\tif (matches >= chr.length) {\n\t\t\tvar newPart = i + 1;\n\t\t\tparts.push(buf.slice(lastPart, newPart - matches));\n\t\t\tlastPart = newPart;\n\t\t\tmatches = 0;\n\t\t}\n\t}\n\tif (lastPart <= buf.length)\n\t\tparts.push(buf.slice(lastPart, buf.length));\n\n\treturn (parts);\n}\n\nfunction ecNormalize(buf, addZero) {\n\tassert.buffer(buf);\n\tif (buf[0] === 0x00 && buf[1] === 0x04) {\n\t\tif (addZero)\n\t\t\treturn (buf);\n\t\treturn (buf.slice(1));\n\t} else if (buf[0] === 0x04) {\n\t\tif (!addZero)\n\t\t\treturn (buf);\n\t} else {\n\t\twhile (buf[0] === 0x00)\n\t\t\tbuf = buf.slice(1);\n\t\tif (buf[0] === 0x02 || buf[0] === 0x03)\n\t\t\tthrow (new Error('Compressed elliptic curve points ' +\n\t\t\t    'are not supported'));\n\t\tif (buf[0] !== 0x04)\n\t\t\tthrow (new Error('Not a valid elliptic curve point'));\n\t\tif (!addZero)\n\t\t\treturn (buf);\n\t}\n\tvar b = Buffer.alloc(buf.length + 1);\n\tb[0] = 0x0;\n\tbuf.copy(b, 1);\n\treturn (b);\n}\n\nfunction readBitString(der, tag) {\n\tif (tag === undefined)\n\t\ttag = asn1.Ber.BitString;\n\tvar buf = der.readString(tag, true);\n\tassert.strictEqual(buf[0], 0x00, 'bit strings with unused bits are ' +\n\t    'not supported (0x' + buf[0].toString(16) + ')');\n\treturn (buf.slice(1));\n}\n\nfunction writeBitString(der, buf, tag) {\n\tif (tag === undefined)\n\t\ttag = asn1.Ber.BitString;\n\tvar b = Buffer.alloc(buf.length + 1);\n\tb[0] = 0x00;\n\tbuf.copy(b, 1);\n\tder.writeBuffer(b, tag);\n}\n\nfunction mpNormalize(buf) {\n\tassert.buffer(buf);\n\twhile (buf.length > 1 && buf[0] === 0x00 && (buf[1] & 0x80) === 0x00)\n\t\tbuf = buf.slice(1);\n\tif ((buf[0] & 0x80) === 0x80) {\n\t\tvar b = Buffer.alloc(buf.length + 1);\n\t\tb[0] = 0x00;\n\t\tbuf.copy(b, 1);\n\t\tbuf = b;\n\t}\n\treturn (buf);\n}\n\nfunction mpDenormalize(buf) {\n\tassert.buffer(buf);\n\twhile (buf.length > 1 && buf[0] === 0x00)\n\t\tbuf = buf.slice(1);\n\treturn (buf);\n}\n\nfunction zeroPadToLength(buf, len) {\n\tassert.buffer(buf);\n\tassert.number(len);\n\twhile (buf.length > len) {\n\t\tassert.equal(buf[0], 0x00);\n\t\tbuf = buf.slice(1);\n\t}\n\twhile (buf.length < len) {\n\t\tvar b = Buffer.alloc(buf.length + 1);\n\t\tb[0] = 0x00;\n\t\tbuf.copy(b, 1);\n\t\tbuf = b;\n\t}\n\treturn (buf);\n}\n\nfunction bigintToMpBuf(bigint) {\n\tvar buf = Buffer.from(bigint.toByteArray());\n\tbuf = mpNormalize(buf);\n\treturn (buf);\n}\n\nfunction calculateDSAPublic(g, p, x) {\n\tassert.buffer(g);\n\tassert.buffer(p);\n\tassert.buffer(x);\n\tg = new jsbn(g);\n\tp = new jsbn(p);\n\tx = new jsbn(x);\n\tvar y = g.modPow(x, p);\n\tvar ybuf = bigintToMpBuf(y);\n\treturn (ybuf);\n}\n\nfunction calculateED25519Public(k) {\n\tassert.buffer(k);\n\n\tvar kp = nacl.sign.keyPair.fromSeed(new Uint8Array(k));\n\treturn (Buffer.from(kp.publicKey));\n}\n\nfunction calculateX25519Public(k) {\n\tassert.buffer(k);\n\n\tvar kp = nacl.box.keyPair.fromSeed(new Uint8Array(k));\n\treturn (Buffer.from(kp.publicKey));\n}\n\nfunction addRSAMissing(key) {\n\tassert.object(key);\n\tassertCompatible(key, PrivateKey, [1, 1]);\n\n\tvar d = new jsbn(key.part.d.data);\n\tvar buf;\n\n\tif (!key.part.dmodp) {\n\t\tvar p = new jsbn(key.part.p.data);\n\t\tvar dmodp = d.mod(p.subtract(1));\n\n\t\tbuf = bigintToMpBuf(dmodp);\n\t\tkey.part.dmodp = {name: 'dmodp', data: buf};\n\t\tkey.parts.push(key.part.dmodp);\n\t}\n\tif (!key.part.dmodq) {\n\t\tvar q = new jsbn(key.part.q.data);\n\t\tvar dmodq = d.mod(q.subtract(1));\n\n\t\tbuf = bigintToMpBuf(dmodq);\n\t\tkey.part.dmodq = {name: 'dmodq', data: buf};\n\t\tkey.parts.push(key.part.dmodq);\n\t}\n}\n\nfunction publicFromPrivateECDSA(curveName, priv) {\n\tassert.string(curveName, 'curveName');\n\tassert.buffer(priv);\n\tvar params = algs.curves[curveName];\n\tvar p = new jsbn(params.p);\n\tvar a = new jsbn(params.a);\n\tvar b = new jsbn(params.b);\n\tvar curve = new ec.ECCurveFp(p, a, b);\n\tvar G = curve.decodePointHex(params.G.toString('hex'));\n\n\tvar d = new jsbn(mpNormalize(priv));\n\tvar pub = G.multiply(d);\n\tpub = Buffer.from(curve.encodePointHex(pub), 'hex');\n\n\tvar parts = [];\n\tparts.push({name: 'curve', data: Buffer.from(curveName)});\n\tparts.push({name: 'Q', data: pub});\n\n\tvar key = new Key({type: 'ecdsa', curve: curve, parts: parts});\n\treturn (key);\n}\n\nfunction opensshCipherInfo(cipher) {\n\tvar inf = {};\n\tswitch (cipher) {\n\tcase '3des-cbc':\n\t\tinf.keySize = 24;\n\t\tinf.blockSize = 8;\n\t\tinf.opensslName = 'des-ede3-cbc';\n\t\tbreak;\n\tcase 'blowfish-cbc':\n\t\tinf.keySize = 16;\n\t\tinf.blockSize = 8;\n\t\tinf.opensslName = 'bf-cbc';\n\t\tbreak;\n\tcase 'aes128-cbc':\n\tcase 'aes128-ctr':\n\tcase '<EMAIL>':\n\t\tinf.keySize = 16;\n\t\tinf.blockSize = 16;\n\t\tinf.opensslName = 'aes-128-' + cipher.slice(7, 10);\n\t\tbreak;\n\tcase 'aes192-cbc':\n\tcase 'aes192-ctr':\n\tcase '<EMAIL>':\n\t\tinf.keySize = 24;\n\t\tinf.blockSize = 16;\n\t\tinf.opensslName = 'aes-192-' + cipher.slice(7, 10);\n\t\tbreak;\n\tcase 'aes256-cbc':\n\tcase 'aes256-ctr':\n\tcase '<EMAIL>':\n\t\tinf.keySize = 32;\n\t\tinf.blockSize = 16;\n\t\tinf.opensslName = 'aes-256-' + cipher.slice(7, 10);\n\t\tbreak;\n\tdefault:\n\t\tthrow (new Error(\n\t\t    'Unsupported openssl cipher \"' + cipher + '\"'));\n\t}\n\treturn (inf);\n}\n", "// Copyright 2015 Joyent, Inc.\n\nmodule.exports = SSHBuffer;\n\nvar assert = require('assert-plus');\nvar Buffer = require('safer-buffer').Buffer;\n\nfunction SSHBuffer(opts) {\n\tassert.object(opts, 'options');\n\tif (opts.buffer !== undefined)\n\t\tassert.buffer(opts.buffer, 'options.buffer');\n\n\tthis._size = opts.buffer ? opts.buffer.length : 1024;\n\tthis._buffer = opts.buffer || Buffer.alloc(this._size);\n\tthis._offset = 0;\n}\n\nSSHBuffer.prototype.toBuffer = function () {\n\treturn (this._buffer.slice(0, this._offset));\n};\n\nSSHBuffer.prototype.atEnd = function () {\n\treturn (this._offset >= this._buffer.length);\n};\n\nSSHBuffer.prototype.remainder = function () {\n\treturn (this._buffer.slice(this._offset));\n};\n\nSSHBuffer.prototype.skip = function (n) {\n\tthis._offset += n;\n};\n\nSSHBuffer.prototype.expand = function () {\n\tthis._size *= 2;\n\tvar buf = Buffer.alloc(this._size);\n\tthis._buffer.copy(buf, 0);\n\tthis._buffer = buf;\n};\n\nSSHBuffer.prototype.readPart = function () {\n\treturn ({data: this.readBuffer()});\n};\n\nSSHBuffer.prototype.readBuffer = function () {\n\tvar len = this._buffer.readUInt32BE(this._offset);\n\tthis._offset += 4;\n\tassert.ok(this._offset + len <= this._buffer.length,\n\t    'length out of bounds at +0x' + this._offset.toString(16) +\n\t    ' (data truncated?)');\n\tvar buf = this._buffer.slice(this._offset, this._offset + len);\n\tthis._offset += len;\n\treturn (buf);\n};\n\nSSHBuffer.prototype.readString = function () {\n\treturn (this.readBuffer().toString());\n};\n\nSSHBuffer.prototype.readCString = function () {\n\tvar offset = this._offset;\n\twhile (offset < this._buffer.length &&\n\t    this._buffer[offset] !== 0x00)\n\t\toffset++;\n\tassert.ok(offset < this._buffer.length, 'c string does not terminate');\n\tvar str = this._buffer.slice(this._offset, offset).toString();\n\tthis._offset = offset + 1;\n\treturn (str);\n};\n\nSSHBuffer.prototype.readInt = function () {\n\tvar v = this._buffer.readUInt32BE(this._offset);\n\tthis._offset += 4;\n\treturn (v);\n};\n\nSSHBuffer.prototype.readInt64 = function () {\n\tassert.ok(this._offset + 8 < this._buffer.length,\n\t    'buffer not long enough to read Int64');\n\tvar v = this._buffer.slice(this._offset, this._offset + 8);\n\tthis._offset += 8;\n\treturn (v);\n};\n\nSSHBuffer.prototype.readChar = function () {\n\tvar v = this._buffer[this._offset++];\n\treturn (v);\n};\n\nSSHBuffer.prototype.writeBuffer = function (buf) {\n\twhile (this._offset + 4 + buf.length > this._size)\n\t\tthis.expand();\n\tthis._buffer.writeUInt32BE(buf.length, this._offset);\n\tthis._offset += 4;\n\tbuf.copy(this._buffer, this._offset);\n\tthis._offset += buf.length;\n};\n\nSSHBuffer.prototype.writeString = function (str) {\n\tthis.writeBuffer(Buffer.from(str, 'utf8'));\n};\n\nSSHBuffer.prototype.writeCString = function (str) {\n\twhile (this._offset + 1 + str.length > this._size)\n\t\tthis.expand();\n\tthis._buffer.write(str, this._offset);\n\tthis._offset += str.length;\n\tthis._buffer[this._offset++] = 0;\n};\n\nSSHBuffer.prototype.writeInt = function (v) {\n\twhile (this._offset + 4 > this._size)\n\t\tthis.expand();\n\tthis._buffer.writeUInt32BE(v, this._offset);\n\tthis._offset += 4;\n};\n\nSSHBuffer.prototype.writeInt64 = function (v) {\n\tassert.buffer(v, 'value');\n\tif (v.length > 8) {\n\t\tvar lead = v.slice(0, v.length - 8);\n\t\tfor (var i = 0; i < lead.length; ++i) {\n\t\t\tassert.strictEqual(lead[i], 0,\n\t\t\t    'must fit in 64 bits of precision');\n\t\t}\n\t\tv = v.slice(v.length - 8, v.length);\n\t}\n\twhile (this._offset + 8 > this._size)\n\t\tthis.expand();\n\tv.copy(this._buffer, this._offset);\n\tthis._offset += 8;\n};\n\nSSHBuffer.prototype.writeChar = function (v) {\n\twhile (this._offset + 1 > this._size)\n\t\tthis.expand();\n\tthis._buffer[this._offset++] = v;\n};\n\nSSHBuffer.prototype.writePart = function (p) {\n\tthis.writeBuffer(p.data);\n};\n\nSSHBuffer.prototype.write = function (buf) {\n\twhile (this._offset + buf.length > this._size)\n\t\tthis.expand();\n\tbuf.copy(this._buffer, this._offset);\n\tthis._offset += buf.length;\n};\n", "// Copyright 2017 Joyent, Inc.\n\nmodule.exports = {\n\tDi<PERSON><PERSON><PERSON><PERSON><PERSON>: <PERSON><PERSON><PERSON><PERSON><PERSON>,\n\tgenerateECDSA: generateECDSA,\n\tgenerateED25519: generateED25519\n};\n\nvar assert = require('assert-plus');\nvar crypto = require('crypto');\nvar Buffer = require('safer-buffer').Buffer;\nvar algs = require('./algs');\nvar utils = require('./utils');\nvar nacl = require('tweetnacl');\n\nvar Key = require('./key');\nvar PrivateKey = require('./private-key');\n\nvar CRYPTO_HAVE_ECDH = (crypto.createECDH !== undefined);\n\nvar ecdh = require('ecc-jsbn');\nvar ec = require('ecc-jsbn/lib/ec');\nvar jsbn = require('jsbn').BigInteger;\n\nfunction Diffie<PERSON><PERSON>man(key) {\n\tutils.assertCompatible(key, Key, [1, 4], 'key');\n\tthis._isPriv = PrivateKey.isPrivateKey(key, [1, 3]);\n\tthis._algo = key.type;\n\tthis._curve = key.curve;\n\tthis._key = key;\n\tif (key.type === 'dsa') {\n\t\tif (!CRYPTO_HAVE_ECDH) {\n\t\t\tthrow (new Error('Due to bugs in the node 0.10 ' +\n\t\t\t    'crypto API, node 0.12.x or later is required ' +\n\t\t\t    'to use DH'));\n\t\t}\n\t\tthis._dh = crypto.createDiffieHellman(\n\t\t    key.part.p.data, undefined,\n\t\t    key.part.g.data, undefined);\n\t\tthis._p = key.part.p;\n\t\tthis._g = key.part.g;\n\t\tif (this._isPriv)\n\t\t\tthis._dh.setPrivateKey(key.part.x.data);\n\t\tthis._dh.setPublicKey(key.part.y.data);\n\n\t} else if (key.type === 'ecdsa') {\n\t\tif (!CRYPTO_HAVE_ECDH) {\n\t\t\tthis._ecParams = new X9ECParameters(this._curve);\n\n\t\t\tif (this._isPriv) {\n\t\t\t\tthis._priv = new ECPrivate(\n\t\t\t\t    this._ecParams, key.part.d.data);\n\t\t\t}\n\t\t\treturn;\n\t\t}\n\n\t\tvar curve = {\n\t\t\t'nistp256': 'prime256v1',\n\t\t\t'nistp384': 'secp384r1',\n\t\t\t'nistp521': 'secp521r1'\n\t\t}[key.curve];\n\t\tthis._dh = crypto.createECDH(curve);\n\t\tif (typeof (this._dh) !== 'object' ||\n\t\t    typeof (this._dh.setPrivateKey) !== 'function') {\n\t\t\tCRYPTO_HAVE_ECDH = false;\n\t\t\tDiffieHellman.call(this, key);\n\t\t\treturn;\n\t\t}\n\t\tif (this._isPriv)\n\t\t\tthis._dh.setPrivateKey(key.part.d.data);\n\t\tthis._dh.setPublicKey(key.part.Q.data);\n\n\t} else if (key.type === 'curve25519') {\n\t\tif (this._isPriv) {\n\t\t\tutils.assertCompatible(key, PrivateKey, [1, 5], 'key');\n\t\t\tthis._priv = key.part.k.data;\n\t\t}\n\n\t} else {\n\t\tthrow (new Error('DH not supported for ' + key.type + ' keys'));\n\t}\n}\n\nDiffieHellman.prototype.getPublicKey = function () {\n\tif (this._isPriv)\n\t\treturn (this._key.toPublic());\n\treturn (this._key);\n};\n\nDiffieHellman.prototype.getPrivateKey = function () {\n\tif (this._isPriv)\n\t\treturn (this._key);\n\telse\n\t\treturn (undefined);\n};\nDiffieHellman.prototype.getKey = DiffieHellman.prototype.getPrivateKey;\n\nDiffieHellman.prototype._keyCheck = function (pk, isPub) {\n\tassert.object(pk, 'key');\n\tif (!isPub)\n\t\tutils.assertCompatible(pk, PrivateKey, [1, 3], 'key');\n\tutils.assertCompatible(pk, Key, [1, 4], 'key');\n\n\tif (pk.type !== this._algo) {\n\t\tthrow (new Error('A ' + pk.type + ' key cannot be used in ' +\n\t\t    this._algo + ' Diffie-Hellman'));\n\t}\n\n\tif (pk.curve !== this._curve) {\n\t\tthrow (new Error('A key from the ' + pk.curve + ' curve ' +\n\t\t    'cannot be used with a ' + this._curve +\n\t\t    ' Diffie-Hellman'));\n\t}\n\n\tif (pk.type === 'dsa') {\n\t\tassert.deepEqual(pk.part.p, this._p,\n\t\t    'DSA key prime does not match');\n\t\tassert.deepEqual(pk.part.g, this._g,\n\t\t    'DSA key generator does not match');\n\t}\n};\n\nDiffieHellman.prototype.setKey = function (pk) {\n\tthis._keyCheck(pk);\n\n\tif (pk.type === 'dsa') {\n\t\tthis._dh.setPrivateKey(pk.part.x.data);\n\t\tthis._dh.setPublicKey(pk.part.y.data);\n\n\t} else if (pk.type === 'ecdsa') {\n\t\tif (CRYPTO_HAVE_ECDH) {\n\t\t\tthis._dh.setPrivateKey(pk.part.d.data);\n\t\t\tthis._dh.setPublicKey(pk.part.Q.data);\n\t\t} else {\n\t\t\tthis._priv = new ECPrivate(\n\t\t\t    this._ecParams, pk.part.d.data);\n\t\t}\n\n\t} else if (pk.type === 'curve25519') {\n\t\tvar k = pk.part.k;\n\t\tif (!pk.part.k)\n\t\t\tk = pk.part.r;\n\t\tthis._priv = k.data;\n\t\tif (this._priv[0] === 0x00)\n\t\t\tthis._priv = this._priv.slice(1);\n\t\tthis._priv = this._priv.slice(0, 32);\n\t}\n\tthis._key = pk;\n\tthis._isPriv = true;\n};\nDiffieHellman.prototype.setPrivateKey = DiffieHellman.prototype.setKey;\n\nDiffieHellman.prototype.computeSecret = function (otherpk) {\n\tthis._keyCheck(otherpk, true);\n\tif (!this._isPriv)\n\t\tthrow (new Error('DH exchange has not been initialized with ' +\n\t\t    'a private key yet'));\n\n\tvar pub;\n\tif (this._algo === 'dsa') {\n\t\treturn (this._dh.computeSecret(\n\t\t    otherpk.part.y.data));\n\n\t} else if (this._algo === 'ecdsa') {\n\t\tif (CRYPTO_HAVE_ECDH) {\n\t\t\treturn (this._dh.computeSecret(\n\t\t\t    otherpk.part.Q.data));\n\t\t} else {\n\t\t\tpub = new ECPublic(\n\t\t\t    this._ecParams, otherpk.part.Q.data);\n\t\t\treturn (this._priv.deriveSharedSecret(pub));\n\t\t}\n\n\t} else if (this._algo === 'curve25519') {\n\t\tpub = otherpk.part.A.data;\n\t\twhile (pub[0] === 0x00 && pub.length > 32)\n\t\t\tpub = pub.slice(1);\n\t\tvar priv = this._priv;\n\t\tassert.strictEqual(pub.length, 32);\n\t\tassert.strictEqual(priv.length, 32);\n\n\t\tvar secret = nacl.box.before(new Uint8Array(pub),\n\t\t    new Uint8Array(priv));\n\n\t\treturn (Buffer.from(secret));\n\t}\n\n\tthrow (new Error('Invalid algorithm: ' + this._algo));\n};\n\nDiffieHellman.prototype.generateKey = function () {\n\tvar parts = [];\n\tvar priv, pub;\n\tif (this._algo === 'dsa') {\n\t\tthis._dh.generateKeys();\n\n\t\tparts.push({name: 'p', data: this._p.data});\n\t\tparts.push({name: 'q', data: this._key.part.q.data});\n\t\tparts.push({name: 'g', data: this._g.data});\n\t\tparts.push({name: 'y', data: this._dh.getPublicKey()});\n\t\tparts.push({name: 'x', data: this._dh.getPrivateKey()});\n\t\tthis._key = new PrivateKey({\n\t\t\ttype: 'dsa',\n\t\t\tparts: parts\n\t\t});\n\t\tthis._isPriv = true;\n\t\treturn (this._key);\n\n\t} else if (this._algo === 'ecdsa') {\n\t\tif (CRYPTO_HAVE_ECDH) {\n\t\t\tthis._dh.generateKeys();\n\n\t\t\tparts.push({name: 'curve',\n\t\t\t    data: Buffer.from(this._curve)});\n\t\t\tparts.push({name: 'Q', data: this._dh.getPublicKey()});\n\t\t\tparts.push({name: 'd', data: this._dh.getPrivateKey()});\n\t\t\tthis._key = new PrivateKey({\n\t\t\t\ttype: 'ecdsa',\n\t\t\t\tcurve: this._curve,\n\t\t\t\tparts: parts\n\t\t\t});\n\t\t\tthis._isPriv = true;\n\t\t\treturn (this._key);\n\n\t\t} else {\n\t\t\tvar n = this._ecParams.getN();\n\t\t\tvar r = new jsbn(crypto.randomBytes(n.bitLength()));\n\t\t\tvar n1 = n.subtract(jsbn.ONE);\n\t\t\tpriv = r.mod(n1).add(jsbn.ONE);\n\t\t\tpub = this._ecParams.getG().multiply(priv);\n\n\t\t\tpriv = Buffer.from(priv.toByteArray());\n\t\t\tpub = Buffer.from(this._ecParams.getCurve().\n\t\t\t    encodePointHex(pub), 'hex');\n\n\t\t\tthis._priv = new ECPrivate(this._ecParams, priv);\n\n\t\t\tparts.push({name: 'curve',\n\t\t\t    data: Buffer.from(this._curve)});\n\t\t\tparts.push({name: 'Q', data: pub});\n\t\t\tparts.push({name: 'd', data: priv});\n\n\t\t\tthis._key = new PrivateKey({\n\t\t\t\ttype: 'ecdsa',\n\t\t\t\tcurve: this._curve,\n\t\t\t\tparts: parts\n\t\t\t});\n\t\t\tthis._isPriv = true;\n\t\t\treturn (this._key);\n\t\t}\n\n\t} else if (this._algo === 'curve25519') {\n\t\tvar pair = nacl.box.keyPair();\n\t\tpriv = Buffer.from(pair.secretKey);\n\t\tpub = Buffer.from(pair.publicKey);\n\t\tpriv = Buffer.concat([priv, pub]);\n\t\tassert.strictEqual(priv.length, 64);\n\t\tassert.strictEqual(pub.length, 32);\n\n\t\tparts.push({name: 'A', data: pub});\n\t\tparts.push({name: 'k', data: priv});\n\t\tthis._key = new PrivateKey({\n\t\t\ttype: 'curve25519',\n\t\t\tparts: parts\n\t\t});\n\t\tthis._isPriv = true;\n\t\treturn (this._key);\n\t}\n\n\tthrow (new Error('Invalid algorithm: ' + this._algo));\n};\nDiffieHellman.prototype.generateKeys = DiffieHellman.prototype.generateKey;\n\n/* These are helpers for using ecc-jsbn (for node 0.10 compatibility). */\n\nfunction X9ECParameters(name) {\n\tvar params = algs.curves[name];\n\tassert.object(params);\n\n\tvar p = new jsbn(params.p);\n\tvar a = new jsbn(params.a);\n\tvar b = new jsbn(params.b);\n\tvar n = new jsbn(params.n);\n\tvar h = jsbn.ONE;\n\tvar curve = new ec.ECCurveFp(p, a, b);\n\tvar G = curve.decodePointHex(params.G.toString('hex'));\n\n\tthis.curve = curve;\n\tthis.g = G;\n\tthis.n = n;\n\tthis.h = h;\n}\nX9ECParameters.prototype.getCurve = function () { return (this.curve); };\nX9ECParameters.prototype.getG = function () { return (this.g); };\nX9ECParameters.prototype.getN = function () { return (this.n); };\nX9ECParameters.prototype.getH = function () { return (this.h); };\n\nfunction ECPublic(params, buffer) {\n\tthis._params = params;\n\tif (buffer[0] === 0x00)\n\t\tbuffer = buffer.slice(1);\n\tthis._pub = params.getCurve().decodePointHex(buffer.toString('hex'));\n}\n\nfunction ECPrivate(params, buffer) {\n\tthis._params = params;\n\tthis._priv = new jsbn(utils.mpNormalize(buffer));\n}\nECPrivate.prototype.deriveSharedSecret = function (pubKey) {\n\tassert.ok(pubKey instanceof ECPublic);\n\tvar S = pubKey._pub.multiply(this._priv);\n\treturn (Buffer.from(S.getX().toBigInteger().toByteArray()));\n};\n\nfunction generateED25519() {\n\tvar pair = nacl.sign.keyPair();\n\tvar priv = Buffer.from(pair.secretKey);\n\tvar pub = Buffer.from(pair.publicKey);\n\tassert.strictEqual(priv.length, 64);\n\tassert.strictEqual(pub.length, 32);\n\n\tvar parts = [];\n\tparts.push({name: 'A', data: pub});\n\tparts.push({name: 'k', data: priv.slice(0, 32)});\n\tvar key = new PrivateKey({\n\t\ttype: 'ed25519',\n\t\tparts: parts\n\t});\n\treturn (key);\n}\n\n/* Generates a new ECDSA private key on a given curve. */\nfunction generateECDSA(curve) {\n\tvar parts = [];\n\tvar key;\n\n\tif (CRYPTO_HAVE_ECDH) {\n\t\t/*\n\t\t * Node crypto doesn't expose key generation directly, but the\n\t\t * ECDH instances can generate keys. It turns out this just\n\t\t * calls into the OpenSSL generic key generator, and we can\n\t\t * read its output happily without doing an actual DH. So we\n\t\t * use that here.\n\t\t */\n\t\tvar osCurve = {\n\t\t\t'nistp256': 'prime256v1',\n\t\t\t'nistp384': 'secp384r1',\n\t\t\t'nistp521': 'secp521r1'\n\t\t}[curve];\n\n\t\tvar dh = crypto.createECDH(osCurve);\n\t\tdh.generateKeys();\n\n\t\tparts.push({name: 'curve',\n\t\t    data: Buffer.from(curve)});\n\t\tparts.push({name: 'Q', data: dh.getPublicKey()});\n\t\tparts.push({name: 'd', data: dh.getPrivateKey()});\n\n\t\tkey = new PrivateKey({\n\t\t\ttype: 'ecdsa',\n\t\t\tcurve: curve,\n\t\t\tparts: parts\n\t\t});\n\t\treturn (key);\n\t} else {\n\n\t\tvar ecParams = new X9ECParameters(curve);\n\n\t\t/* This algorithm taken from FIPS PUB 186-4 (section B.4.1) */\n\t\tvar n = ecParams.getN();\n\t\t/*\n\t\t * The crypto.randomBytes() function can only give us whole\n\t\t * bytes, so taking a nod from X9.62, we round up.\n\t\t */\n\t\tvar cByteLen = Math.ceil((n.bitLength() + 64) / 8);\n\t\tvar c = new jsbn(crypto.randomBytes(cByteLen));\n\n\t\tvar n1 = n.subtract(jsbn.ONE);\n\t\tvar priv = c.mod(n1).add(jsbn.ONE);\n\t\tvar pub = ecParams.getG().multiply(priv);\n\n\t\tpriv = Buffer.from(priv.toByteArray());\n\t\tpub = Buffer.from(ecParams.getCurve().\n\t\t    encodePointHex(pub), 'hex');\n\n\t\tparts.push({name: 'curve', data: Buffer.from(curve)});\n\t\tparts.push({name: 'Q', data: pub});\n\t\tparts.push({name: 'd', data: priv});\n\n\t\tkey = new PrivateKey({\n\t\t\ttype: 'ecdsa',\n\t\t\tcurve: curve,\n\t\t\tparts: parts\n\t\t});\n\t\treturn (key);\n\t}\n}\n", "// Copyright 2015 Joyent, Inc.\n\nmodule.exports = {\n\tVerifier: Verifier,\n\tSigner: Signer\n};\n\nvar nacl = require('tweetnacl');\nvar stream = require('stream');\nvar util = require('util');\nvar assert = require('assert-plus');\nvar Buffer = require('safer-buffer').Buffer;\nvar Signature = require('./signature');\n\nfunction Verifier(key, hashAlgo) {\n\tif (hashAlgo.toLowerCase() !== 'sha512')\n\t\tthrow (new Error('ED25519 only supports the use of ' +\n\t\t    'SHA-512 hashes'));\n\n\tthis.key = key;\n\tthis.chunks = [];\n\n\tstream.Writable.call(this, {});\n}\nutil.inherits(Verifier, stream.Writable);\n\nVerifier.prototype._write = function (chunk, enc, cb) {\n\tthis.chunks.push(chunk);\n\tcb();\n};\n\nVerifier.prototype.update = function (chunk) {\n\tif (typeof (chunk) === 'string')\n\t\tchunk = Buffer.from(chunk, 'binary');\n\tthis.chunks.push(chunk);\n};\n\nVerifier.prototype.verify = function (signature, fmt) {\n\tvar sig;\n\tif (Signature.isSignature(signature, [2, 0])) {\n\t\tif (signature.type !== 'ed25519')\n\t\t\treturn (false);\n\t\tsig = signature.toBuffer('raw');\n\n\t} else if (typeof (signature) === 'string') {\n\t\tsig = Buffer.from(signature, 'base64');\n\n\t} else if (Signature.isSignature(signature, [1, 0])) {\n\t\tthrow (new Error('signature was created by too old ' +\n\t\t    'a version of sshpk and cannot be verified'));\n\t}\n\n\tassert.buffer(sig);\n\treturn (nacl.sign.detached.verify(\n\t    new Uint8Array(Buffer.concat(this.chunks)),\n\t    new Uint8Array(sig),\n\t    new Uint8Array(this.key.part.A.data)));\n};\n\nfunction Signer(key, hashAlgo) {\n\tif (hashAlgo.toLowerCase() !== 'sha512')\n\t\tthrow (new Error('ED25519 only supports the use of ' +\n\t\t    'SHA-512 hashes'));\n\n\tthis.key = key;\n\tthis.chunks = [];\n\n\tstream.Writable.call(this, {});\n}\nutil.inherits(Signer, stream.Writable);\n\nSigner.prototype._write = function (chunk, enc, cb) {\n\tthis.chunks.push(chunk);\n\tcb();\n};\n\nSigner.prototype.update = function (chunk) {\n\tif (typeof (chunk) === 'string')\n\t\tchunk = Buffer.from(chunk, 'binary');\n\tthis.chunks.push(chunk);\n};\n\nSigner.prototype.sign = function () {\n\tvar sig = nacl.sign.detached(\n\t    new Uint8Array(Buffer.concat(this.chunks)),\n\t    new Uint8Array(Buffer.concat([\n\t\tthis.key.part.k.data, this.key.part.A.data])));\n\tvar sigBuf = Buffer.from(sig);\n\tvar sigObj = Signature.parse(sigBuf, 'ed25519', 'raw');\n\tsigObj.hashAlgorithm = 'sha512';\n\treturn (sigObj);\n};\n", "// Copyright 2018 Joyent, Inc.\n\nmodule.exports = {\n\tread: read,\n\twrite: write\n};\n\nvar assert = require('assert-plus');\nvar Buffer = require('safer-buffer').Buffer;\nvar utils = require('../utils');\nvar Key = require('../key');\nvar PrivateKey = require('../private-key');\n\nvar pem = require('./pem');\nvar ssh = require('./ssh');\nvar rfc4253 = require('./rfc4253');\nvar dnssec = require('./dnssec');\nvar putty = require('./putty');\n\nvar DNSSEC_PRIVKEY_HEADER_PREFIX = 'Private-key-format: v1';\n\nfunction read(buf, options) {\n\tif (typeof (buf) === 'string') {\n\t\tif (buf.trim().match(/^[-]+[ ]*BEGIN/))\n\t\t\treturn (pem.read(buf, options));\n\t\tif (buf.match(/^\\s*ssh-[a-z]/))\n\t\t\treturn (ssh.read(buf, options));\n\t\tif (buf.match(/^\\s*ecdsa-/))\n\t\t\treturn (ssh.read(buf, options));\n\t\tif (buf.match(/^putty-user-key-file-2:/i))\n\t\t\treturn (putty.read(buf, options));\n\t\tif (findDNSSECHeader(buf))\n\t\t\treturn (dnssec.read(buf, options));\n\t\tbuf = Buffer.from(buf, 'binary');\n\t} else {\n\t\tassert.buffer(buf);\n\t\tif (findPEMHeader(buf))\n\t\t\treturn (pem.read(buf, options));\n\t\tif (findSSHHeader(buf))\n\t\t\treturn (ssh.read(buf, options));\n\t\tif (findPuTTYHeader(buf))\n\t\t\treturn (putty.read(buf, options));\n\t\tif (findDNSSECHeader(buf))\n\t\t\treturn (dnssec.read(buf, options));\n\t}\n\tif (buf.readUInt32BE(0) < buf.length)\n\t\treturn (rfc4253.read(buf, options));\n\tthrow (new Error('Failed to auto-detect format of key'));\n}\n\nfunction findPuTTYHeader(buf) {\n\tvar offset = 0;\n\twhile (offset < buf.length &&\n\t    (buf[offset] === 32 || buf[offset] === 10 || buf[offset] === 9))\n\t\t++offset;\n\tif (offset + 22 <= buf.length &&\n\t    buf.slice(offset, offset + 22).toString('ascii').toLowerCase() ===\n\t    'putty-user-key-file-2:')\n\t\treturn (true);\n\treturn (false);\n}\n\nfunction findSSHHeader(buf) {\n\tvar offset = 0;\n\twhile (offset < buf.length &&\n\t    (buf[offset] === 32 || buf[offset] === 10 || buf[offset] === 9))\n\t\t++offset;\n\tif (offset + 4 <= buf.length &&\n\t    buf.slice(offset, offset + 4).toString('ascii') === 'ssh-')\n\t\treturn (true);\n\tif (offset + 6 <= buf.length &&\n\t    buf.slice(offset, offset + 6).toString('ascii') === 'ecdsa-')\n\t\treturn (true);\n\treturn (false);\n}\n\nfunction findPEMHeader(buf) {\n\tvar offset = 0;\n\twhile (offset < buf.length &&\n\t    (buf[offset] === 32 || buf[offset] === 10))\n\t\t++offset;\n\tif (buf[offset] !== 45)\n\t\treturn (false);\n\twhile (offset < buf.length &&\n\t    (buf[offset] === 45))\n\t\t++offset;\n\twhile (offset < buf.length &&\n\t    (buf[offset] === 32))\n\t\t++offset;\n\tif (offset + 5 > buf.length ||\n\t    buf.slice(offset, offset + 5).toString('ascii') !== 'BEGIN')\n\t\treturn (false);\n\treturn (true);\n}\n\nfunction findDNSSECHeader(buf) {\n\t// private case first\n\tif (buf.length <= DNSSEC_PRIVKEY_HEADER_PREFIX.length)\n\t\treturn (false);\n\tvar headerCheck = buf.slice(0, DNSSEC_PRIVKEY_HEADER_PREFIX.length);\n\tif (headerCheck.toString('ascii') === DNSSEC_PRIVKEY_HEADER_PREFIX)\n\t\treturn (true);\n\n\t// public-key RFC3110 ?\n\t// 'domain.com. IN KEY ...' or 'domain.com. IN DNSKEY ...'\n\t// skip any comment-lines\n\tif (typeof (buf) !== 'string') {\n\t\tbuf = buf.toString('ascii');\n\t}\n\tvar lines = buf.split('\\n');\n\tvar line = 0;\n\t/* JSSTYLED */\n\twhile (lines[line].match(/^\\;/))\n\t\tline++;\n\tif (lines[line].toString('ascii').match(/\\. IN KEY /))\n\t\treturn (true);\n\tif (lines[line].toString('ascii').match(/\\. IN DNSKEY /))\n\t\treturn (true);\n\treturn (false);\n}\n\nfunction write(key, options) {\n\tthrow (new Error('\"auto\" format cannot be used for writing'));\n}\n", "// Copyright 2018 Joyent, Inc.\n\nmodule.exports = {\n\tread: read,\n\twrite: write\n};\n\nvar assert = require('assert-plus');\nvar asn1 = require('asn1');\nvar crypto = require('crypto');\nvar Buffer = require('safer-buffer').Buffer;\nvar algs = require('../algs');\nvar utils = require('../utils');\nvar Key = require('../key');\nvar PrivateKey = require('../private-key');\n\nvar pkcs1 = require('./pkcs1');\nvar pkcs8 = require('./pkcs8');\nvar sshpriv = require('./ssh-private');\nvar rfc4253 = require('./rfc4253');\n\nvar errors = require('../errors');\n\nvar OID_PBES2 = '1.2.840.113549.1.5.13';\nvar OID_PBKDF2 = '1.2.840.113549.1.5.12';\n\nvar OID_TO_CIPHER = {\n\t'1.2.840.113549.3.7': '3des-cbc',\n\t'2.16.840.*********.1.2': 'aes128-cbc',\n\t'2.16.840.*********.1.42': 'aes256-cbc'\n};\nvar CIPHER_TO_OID = {};\nObject.keys(OID_TO_CIPHER).forEach(function (k) {\n\tCIPHER_TO_OID[OID_TO_CIPHER[k]] = k;\n});\n\nvar OID_TO_HASH = {\n\t'1.2.840.113549.2.7': 'sha1',\n\t'1.2.840.113549.2.9': 'sha256',\n\t'1.2.840.113549.2.11': 'sha512'\n};\nvar HASH_TO_OID = {};\nObject.keys(OID_TO_HASH).forEach(function (k) {\n\tHASH_TO_OID[OID_TO_HASH[k]] = k;\n});\n\n/*\n * For reading we support both PKCS#1 and PKCS#8. If we find a private key,\n * we just take the public component of it and use that.\n */\nfunction read(buf, options, forceType) {\n\tvar input = buf;\n\tif (typeof (buf) !== 'string') {\n\t\tassert.buffer(buf, 'buf');\n\t\tbuf = buf.toString('ascii');\n\t}\n\n\tvar lines = buf.trim().split(/[\\r\\n]+/g);\n\n\tvar m;\n\tvar si = -1;\n\twhile (!m && si < lines.length) {\n\t\tm = lines[++si].match(/*JSSTYLED*/\n\t\t    /[-]+[ ]*BEGIN ([A-Z0-9][A-Za-z0-9]+ )?(PUBLIC|PRIVATE) KEY[ ]*[-]+/);\n\t}\n\tassert.ok(m, 'invalid PEM header');\n\n\tvar m2;\n\tvar ei = lines.length;\n\twhile (!m2 && ei > 0) {\n\t\tm2 = lines[--ei].match(/*JSSTYLED*/\n\t\t    /[-]+[ ]*END ([A-Z0-9][A-Za-z0-9]+ )?(PUBLIC|PRIVATE) KEY[ ]*[-]+/);\n\t}\n\tassert.ok(m2, 'invalid PEM footer');\n\n\t/* Begin and end banners must match key type */\n\tassert.equal(m[2], m2[2]);\n\tvar type = m[2].toLowerCase();\n\n\tvar alg;\n\tif (m[1]) {\n\t\t/* They also must match algorithms, if given */\n\t\tassert.equal(m[1], m2[1], 'PEM header and footer mismatch');\n\t\talg = m[1].trim();\n\t}\n\n\tlines = lines.slice(si, ei + 1);\n\n\tvar headers = {};\n\twhile (true) {\n\t\tlines = lines.slice(1);\n\t\tm = lines[0].match(/*JSSTYLED*/\n\t\t    /^([A-Za-z0-9-]+): (.+)$/);\n\t\tif (!m)\n\t\t\tbreak;\n\t\theaders[m[1].toLowerCase()] = m[2];\n\t}\n\n\t/* Chop off the first and last lines */\n\tlines = lines.slice(0, -1).join('');\n\tbuf = Buffer.from(lines, 'base64');\n\n\tvar cipher, key, iv;\n\tif (headers['proc-type']) {\n\t\tvar parts = headers['proc-type'].split(',');\n\t\tif (parts[0] === '4' && parts[1] === 'ENCRYPTED') {\n\t\t\tif (typeof (options.passphrase) === 'string') {\n\t\t\t\toptions.passphrase = Buffer.from(\n\t\t\t\t    options.passphrase, 'utf-8');\n\t\t\t}\n\t\t\tif (!Buffer.isBuffer(options.passphrase)) {\n\t\t\t\tthrow (new errors.KeyEncryptedError(\n\t\t\t\t    options.filename, 'PEM'));\n\t\t\t} else {\n\t\t\t\tparts = headers['dek-info'].split(',');\n\t\t\t\tassert.ok(parts.length === 2);\n\t\t\t\tcipher = parts[0].toLowerCase();\n\t\t\t\tiv = Buffer.from(parts[1], 'hex');\n\t\t\t\tkey = utils.opensslKeyDeriv(cipher, iv,\n\t\t\t\t    options.passphrase, 1).key;\n\t\t\t}\n\t\t}\n\t}\n\n\tif (alg && alg.toLowerCase() === 'encrypted') {\n\t\tvar eder = new asn1.BerReader(buf);\n\t\tvar pbesEnd;\n\t\teder.readSequence();\n\n\t\teder.readSequence();\n\t\tpbesEnd = eder.offset + eder.length;\n\n\t\tvar method = eder.readOID();\n\t\tif (method !== OID_PBES2) {\n\t\t\tthrow (new Error('Unsupported PEM/PKCS8 encryption ' +\n\t\t\t    'scheme: ' + method));\n\t\t}\n\n\t\teder.readSequence();\t/* PBES2-params */\n\n\t\teder.readSequence();\t/* keyDerivationFunc */\n\t\tvar kdfEnd = eder.offset + eder.length;\n\t\tvar kdfOid = eder.readOID();\n\t\tif (kdfOid !== OID_PBKDF2)\n\t\t\tthrow (new Error('Unsupported PBES2 KDF: ' + kdfOid));\n\t\teder.readSequence();\n\t\tvar salt = eder.readString(asn1.Ber.OctetString, true);\n\t\tvar iterations = eder.readInt();\n\t\tvar hashAlg = 'sha1';\n\t\tif (eder.offset < kdfEnd) {\n\t\t\teder.readSequence();\n\t\t\tvar hashAlgOid = eder.readOID();\n\t\t\thashAlg = OID_TO_HASH[hashAlgOid];\n\t\t\tif (hashAlg === undefined) {\n\t\t\t\tthrow (new Error('Unsupported PBKDF2 hash: ' +\n\t\t\t\t    hashAlgOid));\n\t\t\t}\n\t\t}\n\t\teder._offset = kdfEnd;\n\n\t\teder.readSequence();\t/* encryptionScheme */\n\t\tvar cipherOid = eder.readOID();\n\t\tcipher = OID_TO_CIPHER[cipherOid];\n\t\tif (cipher === undefined) {\n\t\t\tthrow (new Error('Unsupported PBES2 cipher: ' +\n\t\t\t    cipherOid));\n\t\t}\n\t\tiv = eder.readString(asn1.Ber.OctetString, true);\n\n\t\teder._offset = pbesEnd;\n\t\tbuf = eder.readString(asn1.Ber.OctetString, true);\n\n\t\tif (typeof (options.passphrase) === 'string') {\n\t\t\toptions.passphrase = Buffer.from(\n\t\t\t    options.passphrase, 'utf-8');\n\t\t}\n\t\tif (!Buffer.isBuffer(options.passphrase)) {\n\t\t\tthrow (new errors.KeyEncryptedError(\n\t\t\t    options.filename, 'PEM'));\n\t\t}\n\n\t\tvar cinfo = utils.opensshCipherInfo(cipher);\n\n\t\tcipher = cinfo.opensslName;\n\t\tkey = utils.pbkdf2(hashAlg, salt, iterations, cinfo.keySize,\n\t\t    options.passphrase);\n\t\talg = undefined;\n\t}\n\n\tif (cipher && key && iv) {\n\t\tvar cipherStream = crypto.createDecipheriv(cipher, key, iv);\n\t\tvar chunk, chunks = [];\n\t\tcipherStream.once('error', function (e) {\n\t\t\tif (e.toString().indexOf('bad decrypt') !== -1) {\n\t\t\t\tthrow (new Error('Incorrect passphrase ' +\n\t\t\t\t    'supplied, could not decrypt key'));\n\t\t\t}\n\t\t\tthrow (e);\n\t\t});\n\t\tcipherStream.write(buf);\n\t\tcipherStream.end();\n\t\twhile ((chunk = cipherStream.read()) !== null)\n\t\t\tchunks.push(chunk);\n\t\tbuf = Buffer.concat(chunks);\n\t}\n\n\t/* The new OpenSSH internal format abuses PEM headers */\n\tif (alg && alg.toLowerCase() === 'openssh')\n\t\treturn (sshpriv.readSSHPrivate(type, buf, options));\n\tif (alg && alg.toLowerCase() === 'ssh2')\n\t\treturn (rfc4253.readType(type, buf, options));\n\n\tvar der = new asn1.BerReader(buf);\n\tder.originalInput = input;\n\n\t/*\n\t * All of the PEM file types start with a sequence tag, so chop it\n\t * off here\n\t */\n\tder.readSequence();\n\n\t/* PKCS#1 type keys name an algorithm in the banner explicitly */\n\tif (alg) {\n\t\tif (forceType)\n\t\t\tassert.strictEqual(forceType, 'pkcs1');\n\t\treturn (pkcs1.readPkcs1(alg, type, der));\n\t} else {\n\t\tif (forceType)\n\t\t\tassert.strictEqual(forceType, 'pkcs8');\n\t\treturn (pkcs8.readPkcs8(alg, type, der));\n\t}\n}\n\nfunction write(key, options, type) {\n\tassert.object(key);\n\n\tvar alg = {\n\t    'ecdsa': 'EC',\n\t    'rsa': 'RSA',\n\t    'dsa': 'DSA',\n\t    'ed25519': 'EdDSA'\n\t}[key.type];\n\tvar header;\n\n\tvar der = new asn1.BerWriter();\n\n\tif (PrivateKey.isPrivateKey(key)) {\n\t\tif (type && type === 'pkcs8') {\n\t\t\theader = 'PRIVATE KEY';\n\t\t\tpkcs8.writePkcs8(der, key);\n\t\t} else {\n\t\t\tif (type)\n\t\t\t\tassert.strictEqual(type, 'pkcs1');\n\t\t\theader = alg + ' PRIVATE KEY';\n\t\t\tpkcs1.writePkcs1(der, key);\n\t\t}\n\n\t} else if (Key.isKey(key)) {\n\t\tif (type && type === 'pkcs1') {\n\t\t\theader = alg + ' PUBLIC KEY';\n\t\t\tpkcs1.writePkcs1(der, key);\n\t\t} else {\n\t\t\tif (type)\n\t\t\t\tassert.strictEqual(type, 'pkcs8');\n\t\t\theader = 'PUBLIC KEY';\n\t\t\tpkcs8.writePkcs8(der, key);\n\t\t}\n\n\t} else {\n\t\tthrow (new Error('key is not a Key or PrivateKey'));\n\t}\n\n\tvar tmp = der.buffer.toString('base64');\n\tvar len = tmp.length + (tmp.length / 64) +\n\t    18 + 16 + header.length*2 + 10;\n\tvar buf = Buffer.alloc(len);\n\tvar o = 0;\n\to += buf.write('-----BEGIN ' + header + '-----\\n', o);\n\tfor (var i = 0; i < tmp.length; ) {\n\t\tvar limit = i + 64;\n\t\tif (limit > tmp.length)\n\t\t\tlimit = tmp.length;\n\t\to += buf.write(tmp.slice(i, limit), o);\n\t\tbuf[o++] = 10;\n\t\ti = limit;\n\t}\n\to += buf.write('-----END ' + header + '-----\\n', o);\n\n\treturn (buf.slice(0, o));\n}\n", "// Copyright 2015 Joyent, Inc.\n\nmodule.exports = {\n\tread: read,\n\treadPkcs1: readPkcs1,\n\twrite: write,\n\twritePkcs1: writePkcs1\n};\n\nvar assert = require('assert-plus');\nvar asn1 = require('asn1');\nvar Buffer = require('safer-buffer').Buffer;\nvar algs = require('../algs');\nvar utils = require('../utils');\n\nvar Key = require('../key');\nvar PrivateKey = require('../private-key');\nvar pem = require('./pem');\n\nvar pkcs8 = require('./pkcs8');\nvar readECDSACurve = pkcs8.readECDSACurve;\n\nfunction read(buf, options) {\n\treturn (pem.read(buf, options, 'pkcs1'));\n}\n\nfunction write(key, options) {\n\treturn (pem.write(key, options, 'pkcs1'));\n}\n\n/* Helper to read in a single mpint */\nfunction readMPInt(der, nm) {\n\tassert.strictEqual(der.peek(), asn1.Ber.Integer,\n\t    nm + ' is not an Integer');\n\treturn (utils.mpNormalize(der.readString(asn1.Ber.Integer, true)));\n}\n\nfunction readPkcs1(alg, type, der) {\n\tswitch (alg) {\n\tcase 'RSA':\n\t\tif (type === 'public')\n\t\t\treturn (readPkcs1RSAPublic(der));\n\t\telse if (type === 'private')\n\t\t\treturn (readPkcs1RSAPrivate(der));\n\t\tthrow (new Error('Unknown key type: ' + type));\n\tcase 'DSA':\n\t\tif (type === 'public')\n\t\t\treturn (readPkcs1DSAPublic(der));\n\t\telse if (type === 'private')\n\t\t\treturn (readPkcs1DSAPrivate(der));\n\t\tthrow (new Error('Unknown key type: ' + type));\n\tcase 'EC':\n\tcase 'ECDSA':\n\t\tif (type === 'private')\n\t\t\treturn (readPkcs1ECDSAPrivate(der));\n\t\telse if (type === 'public')\n\t\t\treturn (readPkcs1ECDSAPublic(der));\n\t\tthrow (new Error('Unknown key type: ' + type));\n\tcase 'EDDSA':\n\tcase 'EdDSA':\n\t\tif (type === 'private')\n\t\t\treturn (readPkcs1EdDSAPrivate(der));\n\t\tthrow (new Error(type + ' keys not supported with EdDSA'));\n\tdefault:\n\t\tthrow (new Error('Unknown key algo: ' + alg));\n\t}\n}\n\nfunction readPkcs1RSAPublic(der) {\n\t// modulus and exponent\n\tvar n = readMPInt(der, 'modulus');\n\tvar e = readMPInt(der, 'exponent');\n\n\t// now, make the key\n\tvar key = {\n\t\ttype: 'rsa',\n\t\tparts: [\n\t\t\t{ name: 'e', data: e },\n\t\t\t{ name: 'n', data: n }\n\t\t]\n\t};\n\n\treturn (new Key(key));\n}\n\nfunction readPkcs1RSAPrivate(der) {\n\tvar version = readMPInt(der, 'version');\n\tassert.strictEqual(version[0], 0);\n\n\t// modulus then public exponent\n\tvar n = readMPInt(der, 'modulus');\n\tvar e = readMPInt(der, 'public exponent');\n\tvar d = readMPInt(der, 'private exponent');\n\tvar p = readMPInt(der, 'prime1');\n\tvar q = readMPInt(der, 'prime2');\n\tvar dmodp = readMPInt(der, 'exponent1');\n\tvar dmodq = readMPInt(der, 'exponent2');\n\tvar iqmp = readMPInt(der, 'iqmp');\n\n\t// now, make the key\n\tvar key = {\n\t\ttype: 'rsa',\n\t\tparts: [\n\t\t\t{ name: 'n', data: n },\n\t\t\t{ name: 'e', data: e },\n\t\t\t{ name: 'd', data: d },\n\t\t\t{ name: 'iqmp', data: iqmp },\n\t\t\t{ name: 'p', data: p },\n\t\t\t{ name: 'q', data: q },\n\t\t\t{ name: 'dmodp', data: dmodp },\n\t\t\t{ name: 'dmodq', data: dmodq }\n\t\t]\n\t};\n\n\treturn (new PrivateKey(key));\n}\n\nfunction readPkcs1DSAPrivate(der) {\n\tvar version = readMPInt(der, 'version');\n\tassert.strictEqual(version.readUInt8(0), 0);\n\n\tvar p = readMPInt(der, 'p');\n\tvar q = readMPInt(der, 'q');\n\tvar g = readMPInt(der, 'g');\n\tvar y = readMPInt(der, 'y');\n\tvar x = readMPInt(der, 'x');\n\n\t// now, make the key\n\tvar key = {\n\t\ttype: 'dsa',\n\t\tparts: [\n\t\t\t{ name: 'p', data: p },\n\t\t\t{ name: 'q', data: q },\n\t\t\t{ name: 'g', data: g },\n\t\t\t{ name: 'y', data: y },\n\t\t\t{ name: 'x', data: x }\n\t\t]\n\t};\n\n\treturn (new PrivateKey(key));\n}\n\nfunction readPkcs1EdDSAPrivate(der) {\n\tvar version = readMPInt(der, 'version');\n\tassert.strictEqual(version.readUInt8(0), 1);\n\n\t// private key\n\tvar k = der.readString(asn1.Ber.OctetString, true);\n\n\tder.readSequence(0xa0);\n\tvar oid = der.readOID();\n\tassert.strictEqual(oid, '1.3.101.112', 'the ed25519 curve identifier');\n\n\tder.readSequence(0xa1);\n\tvar A = utils.readBitString(der);\n\n\tvar key = {\n\t\ttype: 'ed25519',\n\t\tparts: [\n\t\t\t{ name: 'A', data: utils.zeroPadToLength(A, 32) },\n\t\t\t{ name: 'k', data: k }\n\t\t]\n\t};\n\n\treturn (new PrivateKey(key));\n}\n\nfunction readPkcs1DSAPublic(der) {\n\tvar y = readMPInt(der, 'y');\n\tvar p = readMPInt(der, 'p');\n\tvar q = readMPInt(der, 'q');\n\tvar g = readMPInt(der, 'g');\n\n\tvar key = {\n\t\ttype: 'dsa',\n\t\tparts: [\n\t\t\t{ name: 'y', data: y },\n\t\t\t{ name: 'p', data: p },\n\t\t\t{ name: 'q', data: q },\n\t\t\t{ name: 'g', data: g }\n\t\t]\n\t};\n\n\treturn (new Key(key));\n}\n\nfunction readPkcs1ECDSAPublic(der) {\n\tder.readSequence();\n\n\tvar oid = der.readOID();\n\tassert.strictEqual(oid, '1.2.840.10045.2.1', 'must be ecPublicKey');\n\n\tvar curveOid = der.readOID();\n\n\tvar curve;\n\tvar curves = Object.keys(algs.curves);\n\tfor (var j = 0; j < curves.length; ++j) {\n\t\tvar c = curves[j];\n\t\tvar cd = algs.curves[c];\n\t\tif (cd.pkcs8oid === curveOid) {\n\t\t\tcurve = c;\n\t\t\tbreak;\n\t\t}\n\t}\n\tassert.string(curve, 'a known ECDSA named curve');\n\n\tvar Q = der.readString(asn1.Ber.BitString, true);\n\tQ = utils.ecNormalize(Q);\n\n\tvar key = {\n\t\ttype: 'ecdsa',\n\t\tparts: [\n\t\t\t{ name: 'curve', data: Buffer.from(curve) },\n\t\t\t{ name: 'Q', data: Q }\n\t\t]\n\t};\n\n\treturn (new Key(key));\n}\n\nfunction readPkcs1ECDSAPrivate(der) {\n\tvar version = readMPInt(der, 'version');\n\tassert.strictEqual(version.readUInt8(0), 1);\n\n\t// private key\n\tvar d = der.readString(asn1.Ber.OctetString, true);\n\n\tder.readSequence(0xa0);\n\tvar curve = readECDSACurve(der);\n\tassert.string(curve, 'a known elliptic curve');\n\n\tder.readSequence(0xa1);\n\tvar Q = der.readString(asn1.Ber.BitString, true);\n\tQ = utils.ecNormalize(Q);\n\n\tvar key = {\n\t\ttype: 'ecdsa',\n\t\tparts: [\n\t\t\t{ name: 'curve', data: Buffer.from(curve) },\n\t\t\t{ name: 'Q', data: Q },\n\t\t\t{ name: 'd', data: d }\n\t\t]\n\t};\n\n\treturn (new PrivateKey(key));\n}\n\nfunction writePkcs1(der, key) {\n\tder.startSequence();\n\n\tswitch (key.type) {\n\tcase 'rsa':\n\t\tif (PrivateKey.isPrivateKey(key))\n\t\t\twritePkcs1RSAPrivate(der, key);\n\t\telse\n\t\t\twritePkcs1RSAPublic(der, key);\n\t\tbreak;\n\tcase 'dsa':\n\t\tif (PrivateKey.isPrivateKey(key))\n\t\t\twritePkcs1DSAPrivate(der, key);\n\t\telse\n\t\t\twritePkcs1DSAPublic(der, key);\n\t\tbreak;\n\tcase 'ecdsa':\n\t\tif (PrivateKey.isPrivateKey(key))\n\t\t\twritePkcs1ECDSAPrivate(der, key);\n\t\telse\n\t\t\twritePkcs1ECDSAPublic(der, key);\n\t\tbreak;\n\tcase 'ed25519':\n\t\tif (PrivateKey.isPrivateKey(key))\n\t\t\twritePkcs1EdDSAPrivate(der, key);\n\t\telse\n\t\t\twritePkcs1EdDSAPublic(der, key);\n\t\tbreak;\n\tdefault:\n\t\tthrow (new Error('Unknown key algo: ' + key.type));\n\t}\n\n\tder.endSequence();\n}\n\nfunction writePkcs1RSAPublic(der, key) {\n\tder.writeBuffer(key.part.n.data, asn1.Ber.Integer);\n\tder.writeBuffer(key.part.e.data, asn1.Ber.Integer);\n}\n\nfunction writePkcs1RSAPrivate(der, key) {\n\tvar ver = Buffer.from([0]);\n\tder.writeBuffer(ver, asn1.Ber.Integer);\n\n\tder.writeBuffer(key.part.n.data, asn1.Ber.Integer);\n\tder.writeBuffer(key.part.e.data, asn1.Ber.Integer);\n\tder.writeBuffer(key.part.d.data, asn1.Ber.Integer);\n\tder.writeBuffer(key.part.p.data, asn1.Ber.Integer);\n\tder.writeBuffer(key.part.q.data, asn1.Ber.Integer);\n\tif (!key.part.dmodp || !key.part.dmodq)\n\t\tutils.addRSAMissing(key);\n\tder.writeBuffer(key.part.dmodp.data, asn1.Ber.Integer);\n\tder.writeBuffer(key.part.dmodq.data, asn1.Ber.Integer);\n\tder.writeBuffer(key.part.iqmp.data, asn1.Ber.Integer);\n}\n\nfunction writePkcs1DSAPrivate(der, key) {\n\tvar ver = Buffer.from([0]);\n\tder.writeBuffer(ver, asn1.Ber.Integer);\n\n\tder.writeBuffer(key.part.p.data, asn1.Ber.Integer);\n\tder.writeBuffer(key.part.q.data, asn1.Ber.Integer);\n\tder.writeBuffer(key.part.g.data, asn1.Ber.Integer);\n\tder.writeBuffer(key.part.y.data, asn1.Ber.Integer);\n\tder.writeBuffer(key.part.x.data, asn1.Ber.Integer);\n}\n\nfunction writePkcs1DSAPublic(der, key) {\n\tder.writeBuffer(key.part.y.data, asn1.Ber.Integer);\n\tder.writeBuffer(key.part.p.data, asn1.Ber.Integer);\n\tder.writeBuffer(key.part.q.data, asn1.Ber.Integer);\n\tder.writeBuffer(key.part.g.data, asn1.Ber.Integer);\n}\n\nfunction writePkcs1ECDSAPublic(der, key) {\n\tder.startSequence();\n\n\tder.writeOID('1.2.840.10045.2.1'); /* ecPublicKey */\n\tvar curve = key.part.curve.data.toString();\n\tvar curveOid = algs.curves[curve].pkcs8oid;\n\tassert.string(curveOid, 'a known ECDSA named curve');\n\tder.writeOID(curveOid);\n\n\tder.endSequence();\n\n\tvar Q = utils.ecNormalize(key.part.Q.data, true);\n\tder.writeBuffer(Q, asn1.Ber.BitString);\n}\n\nfunction writePkcs1ECDSAPrivate(der, key) {\n\tvar ver = Buffer.from([1]);\n\tder.writeBuffer(ver, asn1.Ber.Integer);\n\n\tder.writeBuffer(key.part.d.data, asn1.Ber.OctetString);\n\n\tder.startSequence(0xa0);\n\tvar curve = key.part.curve.data.toString();\n\tvar curveOid = algs.curves[curve].pkcs8oid;\n\tassert.string(curveOid, 'a known ECDSA named curve');\n\tder.writeOID(curveOid);\n\tder.endSequence();\n\n\tder.startSequence(0xa1);\n\tvar Q = utils.ecNormalize(key.part.Q.data, true);\n\tder.writeBuffer(Q, asn1.Ber.BitString);\n\tder.endSequence();\n}\n\nfunction writePkcs1EdDSAPrivate(der, key) {\n\tvar ver = Buffer.from([1]);\n\tder.writeBuffer(ver, asn1.Ber.Integer);\n\n\tder.writeBuffer(key.part.k.data, asn1.Ber.OctetString);\n\n\tder.startSequence(0xa0);\n\tder.writeOID('1.3.101.112');\n\tder.endSequence();\n\n\tder.startSequence(0xa1);\n\tutils.writeBitString(der, key.part.A.data);\n\tder.endSequence();\n}\n\nfunction writePkcs1EdDSAPublic(der, key) {\n\tthrow (new Error('Public keys are not supported for EdDSA PKCS#1'));\n}\n", "// Copyright 2018 Joyent, Inc.\n\nmodule.exports = {\n\tread: read,\n\treadPkcs8: readPkcs8,\n\twrite: write,\n\twritePkcs8: writePkcs8,\n\tpkcs8ToBuffer: pkcs8ToBuffer,\n\n\treadECDSACurve: readECDSACurve,\n\twriteECDSACurve: writeECDSACurve\n};\n\nvar assert = require('assert-plus');\nvar asn1 = require('asn1');\nvar Buffer = require('safer-buffer').Buffer;\nvar algs = require('../algs');\nvar utils = require('../utils');\nvar Key = require('../key');\nvar PrivateKey = require('../private-key');\nvar pem = require('./pem');\n\nfunction read(buf, options) {\n\treturn (pem.read(buf, options, 'pkcs8'));\n}\n\nfunction write(key, options) {\n\treturn (pem.write(key, options, 'pkcs8'));\n}\n\n/* Helper to read in a single mpint */\nfunction readMPInt(der, nm) {\n\tassert.strictEqual(der.peek(), asn1.Ber.<PERSON>,\n\t    nm + ' is not an Integer');\n\treturn (utils.mpNormalize(der.readString(asn1.Ber.Integer, true)));\n}\n\nfunction readPkcs8(alg, type, der) {\n\t/* Private keys in pkcs#8 format have a weird extra int */\n\tif (der.peek() === asn1.Ber.Integer) {\n\t\tassert.strictEqual(type, 'private',\n\t\t    'unexpected Integer at start of public key');\n\t\tder.readString(asn1.Ber.Integer, true);\n\t}\n\n\tder.readSequence();\n\tvar next = der.offset + der.length;\n\n\tvar oid = der.readOID();\n\tswitch (oid) {\n\tcase '1.2.840.113549.1.1.1':\n\t\tder._offset = next;\n\t\tif (type === 'public')\n\t\t\treturn (readPkcs8RSAPublic(der));\n\t\telse\n\t\t\treturn (readPkcs8RSAPrivate(der));\n\tcase '1.2.840.10040.4.1':\n\t\tif (type === 'public')\n\t\t\treturn (readPkcs8DSAPublic(der));\n\t\telse\n\t\t\treturn (readPkcs8DSAPrivate(der));\n\tcase '1.2.840.10045.2.1':\n\t\tif (type === 'public')\n\t\t\treturn (readPkcs8ECDSAPublic(der));\n\t\telse\n\t\t\treturn (readPkcs8ECDSAPrivate(der));\n\tcase '1.3.101.112':\n\t\tif (type === 'public') {\n\t\t\treturn (readPkcs8EdDSAPublic(der));\n\t\t} else {\n\t\t\treturn (readPkcs8EdDSAPrivate(der));\n\t\t}\n\tcase '1.3.101.110':\n\t\tif (type === 'public') {\n\t\t\treturn (readPkcs8X25519Public(der));\n\t\t} else {\n\t\t\treturn (readPkcs8X25519Private(der));\n\t\t}\n\tdefault:\n\t\tthrow (new Error('Unknown key type OID ' + oid));\n\t}\n}\n\nfunction readPkcs8RSAPublic(der) {\n\t// bit string sequence\n\tder.readSequence(asn1.Ber.BitString);\n\tder.readByte();\n\tder.readSequence();\n\n\t// modulus\n\tvar n = readMPInt(der, 'modulus');\n\tvar e = readMPInt(der, 'exponent');\n\n\t// now, make the key\n\tvar key = {\n\t\ttype: 'rsa',\n\t\tsource: der.originalInput,\n\t\tparts: [\n\t\t\t{ name: 'e', data: e },\n\t\t\t{ name: 'n', data: n }\n\t\t]\n\t};\n\n\treturn (new Key(key));\n}\n\nfunction readPkcs8RSAPrivate(der) {\n\tder.readSequence(asn1.Ber.OctetString);\n\tder.readSequence();\n\n\tvar ver = readMPInt(der, 'version');\n\tassert.equal(ver[0], 0x0, 'unknown RSA private key version');\n\n\t// modulus then public exponent\n\tvar n = readMPInt(der, 'modulus');\n\tvar e = readMPInt(der, 'public exponent');\n\tvar d = readMPInt(der, 'private exponent');\n\tvar p = readMPInt(der, 'prime1');\n\tvar q = readMPInt(der, 'prime2');\n\tvar dmodp = readMPInt(der, 'exponent1');\n\tvar dmodq = readMPInt(der, 'exponent2');\n\tvar iqmp = readMPInt(der, 'iqmp');\n\n\t// now, make the key\n\tvar key = {\n\t\ttype: 'rsa',\n\t\tparts: [\n\t\t\t{ name: 'n', data: n },\n\t\t\t{ name: 'e', data: e },\n\t\t\t{ name: 'd', data: d },\n\t\t\t{ name: 'iqmp', data: iqmp },\n\t\t\t{ name: 'p', data: p },\n\t\t\t{ name: 'q', data: q },\n\t\t\t{ name: 'dmodp', data: dmodp },\n\t\t\t{ name: 'dmodq', data: dmodq }\n\t\t]\n\t};\n\n\treturn (new PrivateKey(key));\n}\n\nfunction readPkcs8DSAPublic(der) {\n\tder.readSequence();\n\n\tvar p = readMPInt(der, 'p');\n\tvar q = readMPInt(der, 'q');\n\tvar g = readMPInt(der, 'g');\n\n\t// bit string sequence\n\tder.readSequence(asn1.Ber.BitString);\n\tder.readByte();\n\n\tvar y = readMPInt(der, 'y');\n\n\t// now, make the key\n\tvar key = {\n\t\ttype: 'dsa',\n\t\tparts: [\n\t\t\t{ name: 'p', data: p },\n\t\t\t{ name: 'q', data: q },\n\t\t\t{ name: 'g', data: g },\n\t\t\t{ name: 'y', data: y }\n\t\t]\n\t};\n\n\treturn (new Key(key));\n}\n\nfunction readPkcs8DSAPrivate(der) {\n\tder.readSequence();\n\n\tvar p = readMPInt(der, 'p');\n\tvar q = readMPInt(der, 'q');\n\tvar g = readMPInt(der, 'g');\n\n\tder.readSequence(asn1.Ber.OctetString);\n\tvar x = readMPInt(der, 'x');\n\n\t/* The pkcs#8 format does not include the public key */\n\tvar y = utils.calculateDSAPublic(g, p, x);\n\n\tvar key = {\n\t\ttype: 'dsa',\n\t\tparts: [\n\t\t\t{ name: 'p', data: p },\n\t\t\t{ name: 'q', data: q },\n\t\t\t{ name: 'g', data: g },\n\t\t\t{ name: 'y', data: y },\n\t\t\t{ name: 'x', data: x }\n\t\t]\n\t};\n\n\treturn (new PrivateKey(key));\n}\n\nfunction readECDSACurve(der) {\n\tvar curveName, curveNames;\n\tvar j, c, cd;\n\n\tif (der.peek() === asn1.Ber.OID) {\n\t\tvar oid = der.readOID();\n\n\t\tcurveNames = Object.keys(algs.curves);\n\t\tfor (j = 0; j < curveNames.length; ++j) {\n\t\t\tc = curveNames[j];\n\t\t\tcd = algs.curves[c];\n\t\t\tif (cd.pkcs8oid === oid) {\n\t\t\t\tcurveName = c;\n\t\t\t\tbreak;\n\t\t\t}\n\t\t}\n\n\t} else {\n\t\t// ECParameters sequence\n\t\tder.readSequence();\n\t\tvar version = der.readString(asn1.Ber.Integer, true);\n\t\tassert.strictEqual(version[0], 1, 'ECDSA key not version 1');\n\n\t\tvar curve = {};\n\n\t\t// FieldID sequence\n\t\tder.readSequence();\n\t\tvar fieldTypeOid = der.readOID();\n\t\tassert.strictEqual(fieldTypeOid, '1.2.840.10045.1.1',\n\t\t    'ECDSA key is not from a prime-field');\n\t\tvar p = curve.p = utils.mpNormalize(\n\t\t    der.readString(asn1.Ber.Integer, true));\n\t\t/*\n\t\t * p always starts with a 1 bit, so count the zeros to get its\n\t\t * real size.\n\t\t */\n\t\tcurve.size = p.length * 8 - utils.countZeros(p);\n\n\t\t// Curve sequence\n\t\tder.readSequence();\n\t\tcurve.a = utils.mpNormalize(\n\t\t    der.readString(asn1.Ber.OctetString, true));\n\t\tcurve.b = utils.mpNormalize(\n\t\t    der.readString(asn1.Ber.OctetString, true));\n\t\tif (der.peek() === asn1.Ber.BitString)\n\t\t\tcurve.s = der.readString(asn1.Ber.BitString, true);\n\n\t\t// Combined Gx and Gy\n\t\tcurve.G = der.readString(asn1.Ber.OctetString, true);\n\t\tassert.strictEqual(curve.G[0], 0x4,\n\t\t    'uncompressed G is required');\n\n\t\tcurve.n = utils.mpNormalize(\n\t\t    der.readString(asn1.Ber.Integer, true));\n\t\tcurve.h = utils.mpNormalize(\n\t\t    der.readString(asn1.Ber.Integer, true));\n\t\tassert.strictEqual(curve.h[0], 0x1, 'a cofactor=1 curve is ' +\n\t\t    'required');\n\n\t\tcurveNames = Object.keys(algs.curves);\n\t\tvar ks = Object.keys(curve);\n\t\tfor (j = 0; j < curveNames.length; ++j) {\n\t\t\tc = curveNames[j];\n\t\t\tcd = algs.curves[c];\n\t\t\tvar equal = true;\n\t\t\tfor (var i = 0; i < ks.length; ++i) {\n\t\t\t\tvar k = ks[i];\n\t\t\t\tif (cd[k] === undefined)\n\t\t\t\t\tcontinue;\n\t\t\t\tif (typeof (cd[k]) === 'object' &&\n\t\t\t\t    cd[k].equals !== undefined) {\n\t\t\t\t\tif (!cd[k].equals(curve[k])) {\n\t\t\t\t\t\tequal = false;\n\t\t\t\t\t\tbreak;\n\t\t\t\t\t}\n\t\t\t\t} else if (Buffer.isBuffer(cd[k])) {\n\t\t\t\t\tif (cd[k].toString('binary')\n\t\t\t\t\t    !== curve[k].toString('binary')) {\n\t\t\t\t\t\tequal = false;\n\t\t\t\t\t\tbreak;\n\t\t\t\t\t}\n\t\t\t\t} else {\n\t\t\t\t\tif (cd[k] !== curve[k]) {\n\t\t\t\t\t\tequal = false;\n\t\t\t\t\t\tbreak;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t\tif (equal) {\n\t\t\t\tcurveName = c;\n\t\t\t\tbreak;\n\t\t\t}\n\t\t}\n\t}\n\treturn (curveName);\n}\n\nfunction readPkcs8ECDSAPrivate(der) {\n\tvar curveName = readECDSACurve(der);\n\tassert.string(curveName, 'a known elliptic curve');\n\n\tder.readSequence(asn1.Ber.OctetString);\n\tder.readSequence();\n\n\tvar version = readMPInt(der, 'version');\n\tassert.equal(version[0], 1, 'unknown version of ECDSA key');\n\n\tvar d = der.readString(asn1.Ber.OctetString, true);\n\tvar Q;\n\n\tif (der.peek() == 0xa0) {\n\t\tder.readSequence(0xa0);\n\t\tder._offset += der.length;\n\t}\n\tif (der.peek() == 0xa1) {\n\t\tder.readSequence(0xa1);\n\t\tQ = der.readString(asn1.Ber.BitString, true);\n\t\tQ = utils.ecNormalize(Q);\n\t}\n\n\tif (Q === undefined) {\n\t\tvar pub = utils.publicFromPrivateECDSA(curveName, d);\n\t\tQ = pub.part.Q.data;\n\t}\n\n\tvar key = {\n\t\ttype: 'ecdsa',\n\t\tparts: [\n\t\t\t{ name: 'curve', data: Buffer.from(curveName) },\n\t\t\t{ name: 'Q', data: Q },\n\t\t\t{ name: 'd', data: d }\n\t\t]\n\t};\n\n\treturn (new PrivateKey(key));\n}\n\nfunction readPkcs8ECDSAPublic(der) {\n\tvar curveName = readECDSACurve(der);\n\tassert.string(curveName, 'a known elliptic curve');\n\n\tvar Q = der.readString(asn1.Ber.BitString, true);\n\tQ = utils.ecNormalize(Q);\n\n\tvar key = {\n\t\ttype: 'ecdsa',\n\t\tparts: [\n\t\t\t{ name: 'curve', data: Buffer.from(curveName) },\n\t\t\t{ name: 'Q', data: Q }\n\t\t]\n\t};\n\n\treturn (new Key(key));\n}\n\nfunction readPkcs8EdDSAPublic(der) {\n\tif (der.peek() === 0x00)\n\t\tder.readByte();\n\n\tvar A = utils.readBitString(der);\n\n\tvar key = {\n\t\ttype: 'ed25519',\n\t\tparts: [\n\t\t\t{ name: 'A', data: utils.zeroPadToLength(A, 32) }\n\t\t]\n\t};\n\n\treturn (new Key(key));\n}\n\nfunction readPkcs8X25519Public(der) {\n\tvar A = utils.readBitString(der);\n\n\tvar key = {\n\t\ttype: 'curve25519',\n\t\tparts: [\n\t\t\t{ name: 'A', data: utils.zeroPadToLength(A, 32) }\n\t\t]\n\t};\n\n\treturn (new Key(key));\n}\n\nfunction readPkcs8EdDSAPrivate(der) {\n\tif (der.peek() === 0x00)\n\t\tder.readByte();\n\n\tder.readSequence(asn1.Ber.OctetString);\n\tvar k = der.readString(asn1.Ber.OctetString, true);\n\tk = utils.zeroPadToLength(k, 32);\n\n\tvar A, tag;\n\twhile ((tag = der.peek()) !== null) {\n\t\tif (tag === (asn1.Ber.Context | 1)) {\n\t\t\tA = utils.readBitString(der, tag);\n\t\t} else {\n\t\t\tder.readSequence(tag);\n\t\t\tder._offset += der.length;\n\t\t}\n\t}\n\tif (A === undefined)\n\t\tA = utils.calculateED25519Public(k);\n\n\tvar key = {\n\t\ttype: 'ed25519',\n\t\tparts: [\n\t\t\t{ name: 'A', data: utils.zeroPadToLength(A, 32) },\n\t\t\t{ name: 'k', data: utils.zeroPadToLength(k, 32) }\n\t\t]\n\t};\n\n\treturn (new PrivateKey(key));\n}\n\nfunction readPkcs8X25519Private(der) {\n\tif (der.peek() === 0x00)\n\t\tder.readByte();\n\n\tder.readSequence(asn1.Ber.OctetString);\n\tvar k = der.readString(asn1.Ber.OctetString, true);\n\tk = utils.zeroPadToLength(k, 32);\n\n\tvar A = utils.calculateX25519Public(k);\n\n\tvar key = {\n\t\ttype: 'curve25519',\n\t\tparts: [\n\t\t\t{ name: 'A', data: utils.zeroPadToLength(A, 32) },\n\t\t\t{ name: 'k', data: utils.zeroPadToLength(k, 32) }\n\t\t]\n\t};\n\n\treturn (new PrivateKey(key));\n}\n\nfunction pkcs8ToBuffer(key) {\n\tvar der = new asn1.BerWriter();\n\twritePkcs8(der, key);\n\treturn (der.buffer);\n}\n\nfunction writePkcs8(der, key) {\n\tder.startSequence();\n\n\tif (PrivateKey.isPrivateKey(key)) {\n\t\tvar version = 0;\n\t\tif (key.type === 'ed25519')\n\t\t\tversion = 1;\n\t\tvar vbuf = Buffer.from([version]);\n\t\tder.writeBuffer(vbuf, asn1.Ber.Integer);\n\t}\n\n\tder.startSequence();\n\tswitch (key.type) {\n\tcase 'rsa':\n\t\tder.writeOID('1.2.840.113549.1.1.1');\n\t\tif (PrivateKey.isPrivateKey(key))\n\t\t\twritePkcs8RSAPrivate(key, der);\n\t\telse\n\t\t\twritePkcs8RSAPublic(key, der);\n\t\tbreak;\n\tcase 'dsa':\n\t\tder.writeOID('1.2.840.10040.4.1');\n\t\tif (PrivateKey.isPrivateKey(key))\n\t\t\twritePkcs8DSAPrivate(key, der);\n\t\telse\n\t\t\twritePkcs8DSAPublic(key, der);\n\t\tbreak;\n\tcase 'ecdsa':\n\t\tder.writeOID('1.2.840.10045.2.1');\n\t\tif (PrivateKey.isPrivateKey(key))\n\t\t\twritePkcs8ECDSAPrivate(key, der);\n\t\telse\n\t\t\twritePkcs8ECDSAPublic(key, der);\n\t\tbreak;\n\tcase 'ed25519':\n\t\tder.writeOID('1.3.101.112');\n\t\tif (PrivateKey.isPrivateKey(key))\n\t\t\twritePkcs8EdDSAPrivate(key, der);\n\t\telse\n\t\t\twritePkcs8EdDSAPublic(key, der);\n\t\tbreak;\n\tdefault:\n\t\tthrow (new Error('Unsupported key type: ' + key.type));\n\t}\n\n\tder.endSequence();\n}\n\nfunction writePkcs8RSAPrivate(key, der) {\n\tder.writeNull();\n\tder.endSequence();\n\n\tder.startSequence(asn1.Ber.OctetString);\n\tder.startSequence();\n\n\tvar version = Buffer.from([0]);\n\tder.writeBuffer(version, asn1.Ber.Integer);\n\n\tder.writeBuffer(key.part.n.data, asn1.Ber.Integer);\n\tder.writeBuffer(key.part.e.data, asn1.Ber.Integer);\n\tder.writeBuffer(key.part.d.data, asn1.Ber.Integer);\n\tder.writeBuffer(key.part.p.data, asn1.Ber.Integer);\n\tder.writeBuffer(key.part.q.data, asn1.Ber.Integer);\n\tif (!key.part.dmodp || !key.part.dmodq)\n\t\tutils.addRSAMissing(key);\n\tder.writeBuffer(key.part.dmodp.data, asn1.Ber.Integer);\n\tder.writeBuffer(key.part.dmodq.data, asn1.Ber.Integer);\n\tder.writeBuffer(key.part.iqmp.data, asn1.Ber.Integer);\n\n\tder.endSequence();\n\tder.endSequence();\n}\n\nfunction writePkcs8RSAPublic(key, der) {\n\tder.writeNull();\n\tder.endSequence();\n\n\tder.startSequence(asn1.Ber.BitString);\n\tder.writeByte(0x00);\n\n\tder.startSequence();\n\tder.writeBuffer(key.part.n.data, asn1.Ber.Integer);\n\tder.writeBuffer(key.part.e.data, asn1.Ber.Integer);\n\tder.endSequence();\n\n\tder.endSequence();\n}\n\nfunction writePkcs8DSAPrivate(key, der) {\n\tder.startSequence();\n\tder.writeBuffer(key.part.p.data, asn1.Ber.Integer);\n\tder.writeBuffer(key.part.q.data, asn1.Ber.Integer);\n\tder.writeBuffer(key.part.g.data, asn1.Ber.Integer);\n\tder.endSequence();\n\n\tder.endSequence();\n\n\tder.startSequence(asn1.Ber.OctetString);\n\tder.writeBuffer(key.part.x.data, asn1.Ber.Integer);\n\tder.endSequence();\n}\n\nfunction writePkcs8DSAPublic(key, der) {\n\tder.startSequence();\n\tder.writeBuffer(key.part.p.data, asn1.Ber.Integer);\n\tder.writeBuffer(key.part.q.data, asn1.Ber.Integer);\n\tder.writeBuffer(key.part.g.data, asn1.Ber.Integer);\n\tder.endSequence();\n\tder.endSequence();\n\n\tder.startSequence(asn1.Ber.BitString);\n\tder.writeByte(0x00);\n\tder.writeBuffer(key.part.y.data, asn1.Ber.Integer);\n\tder.endSequence();\n}\n\nfunction writeECDSACurve(key, der) {\n\tvar curve = algs.curves[key.curve];\n\tif (curve.pkcs8oid) {\n\t\t/* This one has a name in pkcs#8, so just write the oid */\n\t\tder.writeOID(curve.pkcs8oid);\n\n\t} else {\n\t\t// ECParameters sequence\n\t\tder.startSequence();\n\n\t\tvar version = Buffer.from([1]);\n\t\tder.writeBuffer(version, asn1.Ber.Integer);\n\n\t\t// FieldID sequence\n\t\tder.startSequence();\n\t\tder.writeOID('1.2.840.10045.1.1'); // prime-field\n\t\tder.writeBuffer(curve.p, asn1.Ber.Integer);\n\t\tder.endSequence();\n\n\t\t// Curve sequence\n\t\tder.startSequence();\n\t\tvar a = curve.p;\n\t\tif (a[0] === 0x0)\n\t\t\ta = a.slice(1);\n\t\tder.writeBuffer(a, asn1.Ber.OctetString);\n\t\tder.writeBuffer(curve.b, asn1.Ber.OctetString);\n\t\tder.writeBuffer(curve.s, asn1.Ber.BitString);\n\t\tder.endSequence();\n\n\t\tder.writeBuffer(curve.G, asn1.Ber.OctetString);\n\t\tder.writeBuffer(curve.n, asn1.Ber.Integer);\n\t\tvar h = curve.h;\n\t\tif (!h) {\n\t\t\th = Buffer.from([1]);\n\t\t}\n\t\tder.writeBuffer(h, asn1.Ber.Integer);\n\n\t\t// ECParameters\n\t\tder.endSequence();\n\t}\n}\n\nfunction writePkcs8ECDSAPublic(key, der) {\n\twriteECDSACurve(key, der);\n\tder.endSequence();\n\n\tvar Q = utils.ecNormalize(key.part.Q.data, true);\n\tder.writeBuffer(Q, asn1.Ber.BitString);\n}\n\nfunction writePkcs8ECDSAPrivate(key, der) {\n\twriteECDSACurve(key, der);\n\tder.endSequence();\n\n\tder.startSequence(asn1.Ber.OctetString);\n\tder.startSequence();\n\n\tvar version = Buffer.from([1]);\n\tder.writeBuffer(version, asn1.Ber.Integer);\n\n\tder.writeBuffer(key.part.d.data, asn1.Ber.OctetString);\n\n\tder.startSequence(0xa1);\n\tvar Q = utils.ecNormalize(key.part.Q.data, true);\n\tder.writeBuffer(Q, asn1.Ber.BitString);\n\tder.endSequence();\n\n\tder.endSequence();\n\tder.endSequence();\n}\n\nfunction writePkcs8EdDSAPublic(key, der) {\n\tder.endSequence();\n\n\tutils.writeBitString(der, key.part.A.data);\n}\n\nfunction writePkcs8EdDSAPrivate(key, der) {\n\tder.endSequence();\n\n\tder.startSequence(asn1.Ber.OctetString);\n\tvar k = utils.mpNormalize(key.part.k.data);\n\t/* RFCs call for storing exactly 32 bytes, so strip any leading zeros */\n\twhile (k.length > 32 && k[0] === 0x00)\n\t\tk = k.slice(1);\n\tder.writeBuffer(k, asn1.Ber.OctetString);\n\tder.endSequence();\n\n\tutils.writeBitString(der, key.part.A.data, asn1.Ber.Context | 1);\n}\n", "// Copyright 2015 Joyent, Inc.\n\nmodule.exports = {\n\tread: read,\n\treadSSHPrivate: readSSHPrivate,\n\twrite: write\n};\n\nvar assert = require('assert-plus');\nvar asn1 = require('asn1');\nvar Buffer = require('safer-buffer').Buffer;\nvar algs = require('../algs');\nvar utils = require('../utils');\nvar crypto = require('crypto');\n\nvar Key = require('../key');\nvar PrivateKey = require('../private-key');\nvar pem = require('./pem');\nvar rfc4253 = require('./rfc4253');\nvar SSHBuffer = require('../ssh-buffer');\nvar errors = require('../errors');\n\nvar bcrypt;\n\nfunction read(buf, options) {\n\treturn (pem.read(buf, options));\n}\n\nvar MAGIC = 'openssh-key-v1';\n\nfunction readSSHPrivate(type, buf, options) {\n\tbuf = new SSHBuffer({buffer: buf});\n\n\tvar magic = buf.readCString();\n\tassert.strictEqual(magic, MAGIC, 'bad magic string');\n\n\tvar cipher = buf.readString();\n\tvar kdf = buf.readString();\n\tvar kdfOpts = buf.readBuffer();\n\n\tvar nkeys = buf.readInt();\n\tif (nkeys !== 1) {\n\t\tthrow (new Error('OpenSSH-format key file contains ' +\n\t\t    'multiple keys: this is unsupported.'));\n\t}\n\n\tvar pubKey = buf.readBuffer();\n\n\tif (type === 'public') {\n\t\tassert.ok(buf.atEnd(), 'excess bytes left after key');\n\t\treturn (rfc4253.read(pubKey));\n\t}\n\n\tvar privKeyBlob = buf.readBuffer();\n\tassert.ok(buf.atEnd(), 'excess bytes left after key');\n\n\tvar kdfOptsBuf = new SSHBuffer({ buffer: kdfOpts });\n\tswitch (kdf) {\n\tcase 'none':\n\t\tif (cipher !== 'none') {\n\t\t\tthrow (new Error('OpenSSH-format key uses KDF \"none\" ' +\n\t\t\t     'but specifies a cipher other than \"none\"'));\n\t\t}\n\t\tbreak;\n\tcase 'bcrypt':\n\t\tvar salt = kdfOptsBuf.readBuffer();\n\t\tvar rounds = kdfOptsBuf.readInt();\n\t\tvar cinf = utils.opensshCipherInfo(cipher);\n\t\tif (bcrypt === undefined) {\n\t\t\tbcrypt = require('bcrypt-pbkdf');\n\t\t}\n\n\t\tif (typeof (options.passphrase) === 'string') {\n\t\t\toptions.passphrase = Buffer.from(options.passphrase,\n\t\t\t    'utf-8');\n\t\t}\n\t\tif (!Buffer.isBuffer(options.passphrase)) {\n\t\t\tthrow (new errors.KeyEncryptedError(\n\t\t\t    options.filename, 'OpenSSH'));\n\t\t}\n\n\t\tvar pass = new Uint8Array(options.passphrase);\n\t\tvar salti = new Uint8Array(salt);\n\t\t/* Use the pbkdf to derive both the key and the IV. */\n\t\tvar out = new Uint8Array(cinf.keySize + cinf.blockSize);\n\t\tvar res = bcrypt.pbkdf(pass, pass.length, salti, salti.length,\n\t\t    out, out.length, rounds);\n\t\tif (res !== 0) {\n\t\t\tthrow (new Error('bcrypt_pbkdf function returned ' +\n\t\t\t    'failure, parameters invalid'));\n\t\t}\n\t\tout = Buffer.from(out);\n\t\tvar ckey = out.slice(0, cinf.keySize);\n\t\tvar iv = out.slice(cinf.keySize, cinf.keySize + cinf.blockSize);\n\t\tvar cipherStream = crypto.createDecipheriv(cinf.opensslName,\n\t\t    ckey, iv);\n\t\tcipherStream.setAutoPadding(false);\n\t\tvar chunk, chunks = [];\n\t\tcipherStream.once('error', function (e) {\n\t\t\tif (e.toString().indexOf('bad decrypt') !== -1) {\n\t\t\t\tthrow (new Error('Incorrect passphrase ' +\n\t\t\t\t    'supplied, could not decrypt key'));\n\t\t\t}\n\t\t\tthrow (e);\n\t\t});\n\t\tcipherStream.write(privKeyBlob);\n\t\tcipherStream.end();\n\t\twhile ((chunk = cipherStream.read()) !== null)\n\t\t\tchunks.push(chunk);\n\t\tprivKeyBlob = Buffer.concat(chunks);\n\t\tbreak;\n\tdefault:\n\t\tthrow (new Error(\n\t\t    'OpenSSH-format key uses unknown KDF \"' + kdf + '\"'));\n\t}\n\n\tbuf = new SSHBuffer({buffer: privKeyBlob});\n\n\tvar checkInt1 = buf.readInt();\n\tvar checkInt2 = buf.readInt();\n\tif (checkInt1 !== checkInt2) {\n\t\tthrow (new Error('Incorrect passphrase supplied, could not ' +\n\t\t    'decrypt key'));\n\t}\n\n\tvar ret = {};\n\tvar key = rfc4253.readInternal(ret, 'private', buf.remainder());\n\n\tbuf.skip(ret.consumed);\n\n\tvar comment = buf.readString();\n\tkey.comment = comment;\n\n\treturn (key);\n}\n\nfunction write(key, options) {\n\tvar pubKey;\n\tif (PrivateKey.isPrivateKey(key))\n\t\tpubKey = key.toPublic();\n\telse\n\t\tpubKey = key;\n\n\tvar cipher = 'none';\n\tvar kdf = 'none';\n\tvar kdfopts = Buffer.alloc(0);\n\tvar cinf = { blockSize: 8 };\n\tvar passphrase;\n\tif (options !== undefined) {\n\t\tpassphrase = options.passphrase;\n\t\tif (typeof (passphrase) === 'string')\n\t\t\tpassphrase = Buffer.from(passphrase, 'utf-8');\n\t\tif (passphrase !== undefined) {\n\t\t\tassert.buffer(passphrase, 'options.passphrase');\n\t\t\tassert.optionalString(options.cipher, 'options.cipher');\n\t\t\tcipher = options.cipher;\n\t\t\tif (cipher === undefined)\n\t\t\t\tcipher = 'aes128-ctr';\n\t\t\tcinf = utils.opensshCipherInfo(cipher);\n\t\t\tkdf = 'bcrypt';\n\t\t}\n\t}\n\n\tvar privBuf;\n\tif (PrivateKey.isPrivateKey(key)) {\n\t\tprivBuf = new SSHBuffer({});\n\t\tvar checkInt = crypto.randomBytes(4).readUInt32BE(0);\n\t\tprivBuf.writeInt(checkInt);\n\t\tprivBuf.writeInt(checkInt);\n\t\tprivBuf.write(key.toBuffer('rfc4253'));\n\t\tprivBuf.writeString(key.comment || '');\n\n\t\tvar n = 1;\n\t\twhile (privBuf._offset % cinf.blockSize !== 0)\n\t\t\tprivBuf.writeChar(n++);\n\t\tprivBuf = privBuf.toBuffer();\n\t}\n\n\tswitch (kdf) {\n\tcase 'none':\n\t\tbreak;\n\tcase 'bcrypt':\n\t\tvar salt = crypto.randomBytes(16);\n\t\tvar rounds = 16;\n\t\tvar kdfssh = new SSHBuffer({});\n\t\tkdfssh.writeBuffer(salt);\n\t\tkdfssh.writeInt(rounds);\n\t\tkdfopts = kdfssh.toBuffer();\n\n\t\tif (bcrypt === undefined) {\n\t\t\tbcrypt = require('bcrypt-pbkdf');\n\t\t}\n\t\tvar pass = new Uint8Array(passphrase);\n\t\tvar salti = new Uint8Array(salt);\n\t\t/* Use the pbkdf to derive both the key and the IV. */\n\t\tvar out = new Uint8Array(cinf.keySize + cinf.blockSize);\n\t\tvar res = bcrypt.pbkdf(pass, pass.length, salti, salti.length,\n\t\t    out, out.length, rounds);\n\t\tif (res !== 0) {\n\t\t\tthrow (new Error('bcrypt_pbkdf function returned ' +\n\t\t\t    'failure, parameters invalid'));\n\t\t}\n\t\tout = Buffer.from(out);\n\t\tvar ckey = out.slice(0, cinf.keySize);\n\t\tvar iv = out.slice(cinf.keySize, cinf.keySize + cinf.blockSize);\n\n\t\tvar cipherStream = crypto.createCipheriv(cinf.opensslName,\n\t\t    ckey, iv);\n\t\tcipherStream.setAutoPadding(false);\n\t\tvar chunk, chunks = [];\n\t\tcipherStream.once('error', function (e) {\n\t\t\tthrow (e);\n\t\t});\n\t\tcipherStream.write(privBuf);\n\t\tcipherStream.end();\n\t\twhile ((chunk = cipherStream.read()) !== null)\n\t\t\tchunks.push(chunk);\n\t\tprivBuf = Buffer.concat(chunks);\n\t\tbreak;\n\tdefault:\n\t\tthrow (new Error('Unsupported kdf ' + kdf));\n\t}\n\n\tvar buf = new SSHBuffer({});\n\n\tbuf.writeCString(MAGIC);\n\tbuf.writeString(cipher);\t/* cipher */\n\tbuf.writeString(kdf);\t\t/* kdf */\n\tbuf.writeBuffer(kdfopts);\t/* kdfoptions */\n\n\tbuf.writeInt(1);\t\t/* nkeys */\n\tbuf.writeBuffer(pubKey.toBuffer('rfc4253'));\n\n\tif (privBuf)\n\t\tbuf.writeBuffer(privBuf);\n\n\tbuf = buf.toBuffer();\n\n\tvar header;\n\tif (PrivateKey.isPrivateKey(key))\n\t\theader = 'OPENSSH PRIVATE KEY';\n\telse\n\t\theader = 'OPENSSH PUBLIC KEY';\n\n\tvar tmp = buf.toString('base64');\n\tvar len = tmp.length + (tmp.length / 70) +\n\t    18 + 16 + header.length*2 + 10;\n\tbuf = Buffer.alloc(len);\n\tvar o = 0;\n\to += buf.write('-----BEGIN ' + header + '-----\\n', o);\n\tfor (var i = 0; i < tmp.length; ) {\n\t\tvar limit = i + 70;\n\t\tif (limit > tmp.length)\n\t\t\tlimit = tmp.length;\n\t\to += buf.write(tmp.slice(i, limit), o);\n\t\tbuf[o++] = 10;\n\t\ti = limit;\n\t}\n\to += buf.write('-----END ' + header + '-----\\n', o);\n\n\treturn (buf.slice(0, o));\n}\n", "// Copyright 2015 Joyent, Inc.\n\nmodule.exports = {\n\tread: read.bind(undefined, false, undefined),\n\treadType: read.bind(undefined, false),\n\twrite: write,\n\t/* semi-private api, used by sshpk-agent */\n\treadPartial: read.bind(undefined, true),\n\n\t/* shared with ssh format */\n\treadInternal: read,\n\tkeyTypeToAlg: keyTypeToAlg,\n\talgToKeyType: algToKeyType\n};\n\nvar assert = require('assert-plus');\nvar Buffer = require('safer-buffer').Buffer;\nvar algs = require('../algs');\nvar utils = require('../utils');\nvar Key = require('../key');\nvar PrivateKey = require('../private-key');\nvar SSHBuffer = require('../ssh-buffer');\n\nfunction algToKeyType(alg) {\n\tassert.string(alg);\n\tif (alg === 'ssh-dss')\n\t\treturn ('dsa');\n\telse if (alg === 'ssh-rsa')\n\t\treturn ('rsa');\n\telse if (alg === 'ssh-ed25519')\n\t\treturn ('ed25519');\n\telse if (alg === 'ssh-curve25519')\n\t\treturn ('curve25519');\n\telse if (alg.match(/^ecdsa-sha2-/))\n\t\treturn ('ecdsa');\n\telse\n\t\tthrow (new Error('Unknown algorithm ' + alg));\n}\n\nfunction keyTypeToAlg(key) {\n\tassert.object(key);\n\tif (key.type === 'dsa')\n\t\treturn ('ssh-dss');\n\telse if (key.type === 'rsa')\n\t\treturn ('ssh-rsa');\n\telse if (key.type === 'ed25519')\n\t\treturn ('ssh-ed25519');\n\telse if (key.type === 'curve25519')\n\t\treturn ('ssh-curve25519');\n\telse if (key.type === 'ecdsa')\n\t\treturn ('ecdsa-sha2-' + key.part.curve.data.toString());\n\telse\n\t\tthrow (new Error('Unknown key type ' + key.type));\n}\n\nfunction read(partial, type, buf, options) {\n\tif (typeof (buf) === 'string')\n\t\tbuf = Buffer.from(buf);\n\tassert.buffer(buf, 'buf');\n\n\tvar key = {};\n\n\tvar parts = key.parts = [];\n\tvar sshbuf = new SSHBuffer({buffer: buf});\n\n\tvar alg = sshbuf.readString();\n\tassert.ok(!sshbuf.atEnd(), 'key must have at least one part');\n\n\tkey.type = algToKeyType(alg);\n\n\tvar partCount = algs.info[key.type].parts.length;\n\tif (type && type === 'private')\n\t\tpartCount = algs.privInfo[key.type].parts.length;\n\n\twhile (!sshbuf.atEnd() && parts.length < partCount)\n\t\tparts.push(sshbuf.readPart());\n\twhile (!partial && !sshbuf.atEnd())\n\t\tparts.push(sshbuf.readPart());\n\n\tassert.ok(parts.length >= 1,\n\t    'key must have at least one part');\n\tassert.ok(partial || sshbuf.atEnd(),\n\t    'leftover bytes at end of key');\n\n\tvar Constructor = Key;\n\tvar algInfo = algs.info[key.type];\n\tif (type === 'private' || algInfo.parts.length !== parts.length) {\n\t\talgInfo = algs.privInfo[key.type];\n\t\tConstructor = PrivateKey;\n\t}\n\tassert.strictEqual(algInfo.parts.length, parts.length);\n\n\tif (key.type === 'ecdsa') {\n\t\tvar res = /^ecdsa-sha2-(.+)$/.exec(alg);\n\t\tassert.ok(res !== null);\n\t\tassert.strictEqual(res[1], parts[0].data.toString());\n\t}\n\n\tvar normalized = true;\n\tfor (var i = 0; i < algInfo.parts.length; ++i) {\n\t\tvar p = parts[i];\n\t\tp.name = algInfo.parts[i];\n\t\t/*\n\t\t * OpenSSH stores ed25519 \"private\" keys as seed + public key\n\t\t * concat'd together (k followed by A). We want to keep them\n\t\t * separate for other formats that don't do this.\n\t\t */\n\t\tif (key.type === 'ed25519' && p.name === 'k')\n\t\t\tp.data = p.data.slice(0, 32);\n\n\t\tif (p.name !== 'curve' && algInfo.normalize !== false) {\n\t\t\tvar nd;\n\t\t\tif (key.type === 'ed25519') {\n\t\t\t\tnd = utils.zeroPadToLength(p.data, 32);\n\t\t\t} else {\n\t\t\t\tnd = utils.mpNormalize(p.data);\n\t\t\t}\n\t\t\tif (nd.toString('binary') !==\n\t\t\t    p.data.toString('binary')) {\n\t\t\t\tp.data = nd;\n\t\t\t\tnormalized = false;\n\t\t\t}\n\t\t}\n\t}\n\n\tif (normalized)\n\t\tkey._rfc4253Cache = sshbuf.toBuffer();\n\n\tif (partial && typeof (partial) === 'object') {\n\t\tpartial.remainder = sshbuf.remainder();\n\t\tpartial.consumed = sshbuf._offset;\n\t}\n\n\treturn (new Constructor(key));\n}\n\nfunction write(key, options) {\n\tassert.object(key);\n\n\tvar alg = keyTypeToAlg(key);\n\tvar i;\n\n\tvar algInfo = algs.info[key.type];\n\tif (PrivateKey.isPrivateKey(key))\n\t\talgInfo = algs.privInfo[key.type];\n\tvar parts = algInfo.parts;\n\n\tvar buf = new SSHBuffer({});\n\n\tbuf.writeString(alg);\n\n\tfor (i = 0; i < parts.length; ++i) {\n\t\tvar data = key.part[parts[i]].data;\n\t\tif (algInfo.normalize !== false) {\n\t\t\tif (key.type === 'ed25519')\n\t\t\t\tdata = utils.zeroPadToLength(data, 32);\n\t\t\telse\n\t\t\t\tdata = utils.mpNormalize(data);\n\t\t}\n\t\tif (key.type === 'ed25519' && parts[i] === 'k')\n\t\t\tdata = Buffer.concat([data, key.part.A.data]);\n\t\tbuf.writeBuffer(data);\n\t}\n\n\treturn (buf.toBuffer());\n}\n", "// Copyright 2015 Joyent, Inc.\n\nmodule.exports = {\n\tread: read,\n\twrite: write\n};\n\nvar assert = require('assert-plus');\nvar Buffer = require('safer-buffer').Buffer;\nvar rfc4253 = require('./rfc4253');\nvar utils = require('../utils');\nvar Key = require('../key');\nvar PrivateKey = require('../private-key');\n\nvar sshpriv = require('./ssh-private');\n\n/*JSSTYLED*/\nvar SSHKEY_RE = /^([a-z0-9-]+)[ \\t]+([a-zA-Z0-9+\\/]+[=]*)([ \\t]+([^ \\t][^\\n]*[\\n]*)?)?$/;\n/*JSSTYLED*/\nvar SSHKEY_RE2 = /^([a-z0-9-]+)[ \\t\\n]+([a-zA-Z0-9+\\/][a-zA-Z0-9+\\/ \\t\\n=]*)([^a-zA-Z0-9+\\/ \\t\\n=].*)?$/;\n\nfunction read(buf, options) {\n\tif (typeof (buf) !== 'string') {\n\t\tassert.buffer(buf, 'buf');\n\t\tbuf = buf.toString('ascii');\n\t}\n\n\tvar trimmed = buf.trim().replace(/[\\\\\\r]/g, '');\n\tvar m = trimmed.match(SSHKEY_RE);\n\tif (!m)\n\t\tm = trimmed.match(SSHKEY_RE2);\n\tassert.ok(m, 'key must match regex');\n\n\tvar type = rfc4253.algToKeyType(m[1]);\n\tvar kbuf = Buffer.from(m[2], 'base64');\n\n\t/*\n\t * This is a bit tricky. If we managed to parse the key and locate the\n\t * key comment with the regex, then do a non-partial read and assert\n\t * that we have consumed all bytes. If we couldn't locate the key\n\t * comment, though, there may be whitespace shenanigans going on that\n\t * have conjoined the comment to the rest of the key. We do a partial\n\t * read in this case to try to make the best out of a sorry situation.\n\t */\n\tvar key;\n\tvar ret = {};\n\tif (m[4]) {\n\t\ttry {\n\t\t\tkey = rfc4253.read(kbuf);\n\n\t\t} catch (e) {\n\t\t\tm = trimmed.match(SSHKEY_RE2);\n\t\t\tassert.ok(m, 'key must match regex');\n\t\t\tkbuf = Buffer.from(m[2], 'base64');\n\t\t\tkey = rfc4253.readInternal(ret, 'public', kbuf);\n\t\t}\n\t} else {\n\t\tkey = rfc4253.readInternal(ret, 'public', kbuf);\n\t}\n\n\tassert.strictEqual(type, key.type);\n\n\tif (m[4] && m[4].length > 0) {\n\t\tkey.comment = m[4];\n\n\t} else if (ret.consumed) {\n\t\t/*\n\t\t * Now the magic: trying to recover the key comment when it's\n\t\t * gotten conjoined to the key or otherwise shenanigan'd.\n\t\t *\n\t\t * Work out how much base64 we used, then drop all non-base64\n\t\t * chars from the beginning up to this point in the the string.\n\t\t * Then offset in this and try to make up for missing = chars.\n\t\t */\n\t\tvar data = m[2] + (m[3] ? m[3] : '');\n\t\tvar realOffset = Math.ceil(ret.consumed / 3) * 4;\n\t\tdata = data.slice(0, realOffset - 2). /*JSSTYLED*/\n\t\t    replace(/[^a-zA-Z0-9+\\/=]/g, '') +\n\t\t    data.slice(realOffset - 2);\n\n\t\tvar padding = ret.consumed % 3;\n\t\tif (padding > 0 &&\n\t\t    data.slice(realOffset - 1, realOffset) !== '=')\n\t\t\trealOffset--;\n\t\twhile (data.slice(realOffset, realOffset + 1) === '=')\n\t\t\trealOffset++;\n\n\t\t/* Finally, grab what we think is the comment & clean it up. */\n\t\tvar trailer = data.slice(realOffset);\n\t\ttrailer = trailer.replace(/[\\r\\n]/g, ' ').\n\t\t    replace(/^\\s+/, '');\n\t\tif (trailer.match(/^[a-zA-Z0-9]/))\n\t\t\tkey.comment = trailer;\n\t}\n\n\treturn (key);\n}\n\nfunction write(key, options) {\n\tassert.object(key);\n\tif (!Key.isKey(key))\n\t\tthrow (new Error('Must be a public key'));\n\n\tvar parts = [];\n\tvar alg = rfc4253.keyTypeToAlg(key);\n\tparts.push(alg);\n\n\tvar buf = rfc4253.write(key);\n\tparts.push(buf.toString('base64'));\n\n\tif (key.comment)\n\t\tparts.push(key.comment);\n\n\treturn (Buffer.from(parts.join(' ')));\n}\n", "// Copyright 2017 Joyent, Inc.\n\nmodule.exports = {\n\tread: read,\n\twrite: write\n};\n\nvar assert = require('assert-plus');\nvar Buffer = require('safer-buffer').Buffer;\nvar Key = require('../key');\nvar PrivateKey = require('../private-key');\nvar utils = require('../utils');\nvar SSHBuffer = require('../ssh-buffer');\nvar Dhe = require('../dhe');\n\nvar supportedAlgos = {\n\t'rsa-sha1' : 5,\n\t'rsa-sha256' : 8,\n\t'rsa-sha512' : 10,\n\t'ecdsa-p256-sha256' : 13,\n\t'ecdsa-p384-sha384' : 14\n\t/*\n\t * ed25519 is hypothetically supported with id 15\n\t * but the common tools available don't appear to be\n\t * capable of generating/using ed25519 keys\n\t */\n};\n\nvar supportedAlgosById = {};\nObject.keys(supportedAlgos).forEach(function (k) {\n\tsupportedAlgosById[supportedAlgos[k]] = k.toUpperCase();\n});\n\nfunction read(buf, options) {\n\tif (typeof (buf) !== 'string') {\n\t\tassert.buffer(buf, 'buf');\n\t\tbuf = buf.toString('ascii');\n\t}\n\tvar lines = buf.split('\\n');\n\tif (lines[0].match(/^Private-key-format\\: v1/)) {\n\t\tvar algElems = lines[1].split(' ');\n\t\tvar algoNum = parseInt(algElems[1], 10);\n\t\tvar algoName = algElems[2];\n\t\tif (!supportedAlgosById[algoNum])\n\t\t\tthrow (new Error('Unsupported algorithm: ' + algoName));\n\t\treturn (readDNSSECPrivateKey(algoNum, lines.slice(2)));\n\t}\n\n\t// skip any comment-lines\n\tvar line = 0;\n\t/* JSSTYLED */\n\twhile (lines[line].match(/^\\;/))\n\t\tline++;\n\t// we should now have *one single* line left with our KEY on it.\n\tif ((lines[line].match(/\\. IN KEY /) ||\n\t    lines[line].match(/\\. IN DNSKEY /)) && lines[line+1].length === 0) {\n\t\treturn (readRFC3110(lines[line]));\n\t}\n\tthrow (new Error('Cannot parse dnssec key'));\n}\n\nfunction readRFC3110(keyString) {\n\tvar elems = keyString.split(' ');\n\t//unused var flags = parseInt(elems[3], 10);\n\t//unused var protocol = parseInt(elems[4], 10);\n\tvar algorithm = parseInt(elems[5], 10);\n\tif (!supportedAlgosById[algorithm])\n\t\tthrow (new Error('Unsupported algorithm: ' + algorithm));\n\tvar base64key = elems.slice(6, elems.length).join();\n\tvar keyBuffer = Buffer.from(base64key, 'base64');\n\tif (supportedAlgosById[algorithm].match(/^RSA-/)) {\n\t\t// join the rest of the body into a single base64-blob\n\t\tvar publicExponentLen = keyBuffer.readUInt8(0);\n\t\tif (publicExponentLen != 3 && publicExponentLen != 1)\n\t\t\tthrow (new Error('Cannot parse dnssec key: ' +\n\t\t\t    'unsupported exponent length'));\n\n\t\tvar publicExponent = keyBuffer.slice(1, publicExponentLen+1);\n\t\tpublicExponent = utils.mpNormalize(publicExponent);\n\t\tvar modulus = keyBuffer.slice(1+publicExponentLen);\n\t\tmodulus = utils.mpNormalize(modulus);\n\t\t// now, make the key\n\t\tvar rsaKey = {\n\t\t\ttype: 'rsa',\n\t\t\tparts: []\n\t\t};\n\t\trsaKey.parts.push({ name: 'e', data: publicExponent});\n\t\trsaKey.parts.push({ name: 'n', data: modulus});\n\t\treturn (new Key(rsaKey));\n\t}\n\tif (supportedAlgosById[algorithm] === 'ECDSA-P384-SHA384' ||\n\t    supportedAlgosById[algorithm] === 'ECDSA-P256-SHA256') {\n\t\tvar curve = 'nistp384';\n\t\tvar size = 384;\n\t\tif (supportedAlgosById[algorithm].match(/^ECDSA-P256-SHA256/)) {\n\t\t\tcurve = 'nistp256';\n\t\t\tsize = 256;\n\t\t}\n\n\t\tvar ecdsaKey = {\n\t\t\ttype: 'ecdsa',\n\t\t\tcurve: curve,\n\t\t\tsize: size,\n\t\t\tparts: [\n\t\t\t\t{name: 'curve', data: Buffer.from(curve) },\n\t\t\t\t{name: 'Q', data: utils.ecNormalize(keyBuffer) }\n\t\t\t]\n\t\t};\n\t\treturn (new Key(ecdsaKey));\n\t}\n\tthrow (new Error('Unsupported algorithm: ' +\n\t    supportedAlgosById[algorithm]));\n}\n\nfunction elementToBuf(e) {\n\treturn (Buffer.from(e.split(' ')[1], 'base64'));\n}\n\nfunction readDNSSECRSAPrivateKey(elements) {\n\tvar rsaParams = {};\n\telements.forEach(function (element) {\n\t\tif (element.split(' ')[0] === 'Modulus:')\n\t\t\trsaParams['n'] = elementToBuf(element);\n\t\telse if (element.split(' ')[0] === 'PublicExponent:')\n\t\t\trsaParams['e'] = elementToBuf(element);\n\t\telse if (element.split(' ')[0] === 'PrivateExponent:')\n\t\t\trsaParams['d'] = elementToBuf(element);\n\t\telse if (element.split(' ')[0] === 'Prime1:')\n\t\t\trsaParams['p'] = elementToBuf(element);\n\t\telse if (element.split(' ')[0] === 'Prime2:')\n\t\t\trsaParams['q'] = elementToBuf(element);\n\t\telse if (element.split(' ')[0] === 'Exponent1:')\n\t\t\trsaParams['dmodp'] = elementToBuf(element);\n\t\telse if (element.split(' ')[0] === 'Exponent2:')\n\t\t\trsaParams['dmodq'] = elementToBuf(element);\n\t\telse if (element.split(' ')[0] === 'Coefficient:')\n\t\t\trsaParams['iqmp'] = elementToBuf(element);\n\t});\n\t// now, make the key\n\tvar key = {\n\t\ttype: 'rsa',\n\t\tparts: [\n\t\t\t{ name: 'e', data: utils.mpNormalize(rsaParams['e'])},\n\t\t\t{ name: 'n', data: utils.mpNormalize(rsaParams['n'])},\n\t\t\t{ name: 'd', data: utils.mpNormalize(rsaParams['d'])},\n\t\t\t{ name: 'p', data: utils.mpNormalize(rsaParams['p'])},\n\t\t\t{ name: 'q', data: utils.mpNormalize(rsaParams['q'])},\n\t\t\t{ name: 'dmodp',\n\t\t\t    data: utils.mpNormalize(rsaParams['dmodp'])},\n\t\t\t{ name: 'dmodq',\n\t\t\t    data: utils.mpNormalize(rsaParams['dmodq'])},\n\t\t\t{ name: 'iqmp',\n\t\t\t    data: utils.mpNormalize(rsaParams['iqmp'])}\n\t\t]\n\t};\n\treturn (new PrivateKey(key));\n}\n\nfunction readDNSSECPrivateKey(alg, elements) {\n\tif (supportedAlgosById[alg].match(/^RSA-/)) {\n\t\treturn (readDNSSECRSAPrivateKey(elements));\n\t}\n\tif (supportedAlgosById[alg] === 'ECDSA-P384-SHA384' ||\n\t    supportedAlgosById[alg] === 'ECDSA-P256-SHA256') {\n\t\tvar d = Buffer.from(elements[0].split(' ')[1], 'base64');\n\t\tvar curve = 'nistp384';\n\t\tvar size = 384;\n\t\tif (supportedAlgosById[alg] === 'ECDSA-P256-SHA256') {\n\t\t\tcurve = 'nistp256';\n\t\t\tsize = 256;\n\t\t}\n\t\t// DNSSEC generates the public-key on the fly (go calculate it)\n\t\tvar publicKey = utils.publicFromPrivateECDSA(curve, d);\n\t\tvar Q = publicKey.part['Q'].data;\n\t\tvar ecdsaKey = {\n\t\t\ttype: 'ecdsa',\n\t\t\tcurve: curve,\n\t\t\tsize: size,\n\t\t\tparts: [\n\t\t\t\t{name: 'curve', data: Buffer.from(curve) },\n\t\t\t\t{name: 'd', data: d },\n\t\t\t\t{name: 'Q', data: Q }\n\t\t\t]\n\t\t};\n\t\treturn (new PrivateKey(ecdsaKey));\n\t}\n\tthrow (new Error('Unsupported algorithm: ' + supportedAlgosById[alg]));\n}\n\nfunction dnssecTimestamp(date) {\n\tvar year = date.getFullYear() + ''; //stringify\n\tvar month = (date.getMonth() + 1);\n\tvar timestampStr = year + month + date.getUTCDate();\n\ttimestampStr += '' + date.getUTCHours() + date.getUTCMinutes();\n\ttimestampStr += date.getUTCSeconds();\n\treturn (timestampStr);\n}\n\nfunction rsaAlgFromOptions(opts) {\n\tif (!opts || !opts.hashAlgo || opts.hashAlgo === 'sha1')\n\t\treturn ('5 (RSASHA1)');\n\telse if (opts.hashAlgo === 'sha256')\n\t\treturn ('8 (RSASHA256)');\n\telse if (opts.hashAlgo === 'sha512')\n\t\treturn ('10 (RSASHA512)');\n\telse\n\t\tthrow (new Error('Unknown or unsupported hash: ' +\n\t\t    opts.hashAlgo));\n}\n\nfunction writeRSA(key, options) {\n\t// if we're missing parts, add them.\n\tif (!key.part.dmodp || !key.part.dmodq) {\n\t\tutils.addRSAMissing(key);\n\t}\n\n\tvar out = '';\n\tout += 'Private-key-format: v1.3\\n';\n\tout += 'Algorithm: ' + rsaAlgFromOptions(options) + '\\n';\n\tvar n = utils.mpDenormalize(key.part['n'].data);\n\tout += 'Modulus: ' + n.toString('base64') + '\\n';\n\tvar e = utils.mpDenormalize(key.part['e'].data);\n\tout += 'PublicExponent: ' + e.toString('base64') + '\\n';\n\tvar d = utils.mpDenormalize(key.part['d'].data);\n\tout += 'PrivateExponent: ' + d.toString('base64') + '\\n';\n\tvar p = utils.mpDenormalize(key.part['p'].data);\n\tout += 'Prime1: ' + p.toString('base64') + '\\n';\n\tvar q = utils.mpDenormalize(key.part['q'].data);\n\tout += 'Prime2: ' + q.toString('base64') + '\\n';\n\tvar dmodp = utils.mpDenormalize(key.part['dmodp'].data);\n\tout += 'Exponent1: ' + dmodp.toString('base64') + '\\n';\n\tvar dmodq = utils.mpDenormalize(key.part['dmodq'].data);\n\tout += 'Exponent2: ' + dmodq.toString('base64') + '\\n';\n\tvar iqmp = utils.mpDenormalize(key.part['iqmp'].data);\n\tout += 'Coefficient: ' + iqmp.toString('base64') + '\\n';\n\t// Assume that we're valid as-of now\n\tvar timestamp = new Date();\n\tout += 'Created: ' + dnssecTimestamp(timestamp) + '\\n';\n\tout += 'Publish: ' + dnssecTimestamp(timestamp) + '\\n';\n\tout += 'Activate: ' + dnssecTimestamp(timestamp) + '\\n';\n\treturn (Buffer.from(out, 'ascii'));\n}\n\nfunction writeECDSA(key, options) {\n\tvar out = '';\n\tout += 'Private-key-format: v1.3\\n';\n\n\tif (key.curve === 'nistp256') {\n\t\tout += 'Algorithm: 13 (ECDSAP256SHA256)\\n';\n\t} else if (key.curve === 'nistp384') {\n\t\tout += 'Algorithm: 14 (ECDSAP384SHA384)\\n';\n\t} else {\n\t\tthrow (new Error('Unsupported curve'));\n\t}\n\tvar base64Key = key.part['d'].data.toString('base64');\n\tout += 'PrivateKey: ' + base64Key + '\\n';\n\n\t// Assume that we're valid as-of now\n\tvar timestamp = new Date();\n\tout += 'Created: ' + dnssecTimestamp(timestamp) + '\\n';\n\tout += 'Publish: ' + dnssecTimestamp(timestamp) + '\\n';\n\tout += 'Activate: ' + dnssecTimestamp(timestamp) + '\\n';\n\n\treturn (Buffer.from(out, 'ascii'));\n}\n\nfunction write(key, options) {\n\tif (PrivateKey.isPrivateKey(key)) {\n\t\tif (key.type === 'rsa') {\n\t\t\treturn (writeRSA(key, options));\n\t\t} else if (key.type === 'ecdsa') {\n\t\t\treturn (writeECDSA(key, options));\n\t\t} else {\n\t\t\tthrow (new Error('Unsupported algorithm: ' + key.type));\n\t\t}\n\t} else if (Key.isKey(key)) {\n\t\t/*\n\t\t * RFC3110 requires a keyname, and a keytype, which we\n\t\t * don't really have a mechanism for specifying such\n\t\t * additional metadata.\n\t\t */\n\t\tthrow (new Error('Format \"dnssec\" only supports ' +\n\t\t    'writing private keys'));\n\t} else {\n\t\tthrow (new Error('key is not a Key or PrivateKey'));\n\t}\n}\n", "// Copyright 2018 Joyent, Inc.\n\nmodule.exports = {\n\tread: read,\n\twrite: write\n};\n\nvar assert = require('assert-plus');\nvar Buffer = require('safer-buffer').Buffer;\nvar rfc4253 = require('./rfc4253');\nvar Key = require('../key');\nvar SSHBuffer = require('../ssh-buffer');\nvar crypto = require('crypto');\nvar PrivateKey = require('../private-key');\n\nvar errors = require('../errors');\n\n// https://tartarus.org/~simon/putty-prerel-snapshots/htmldoc/AppendixC.html\nfunction read(buf, options) {\n\tvar lines = buf.toString('ascii').split(/[\\r\\n]+/);\n\tvar found = false;\n\tvar parts;\n\tvar si = 0;\n\tvar formatVersion;\n\twhile (si < lines.length) {\n\t\tparts = splitHeader(lines[si++]);\n\t\tif (parts) {\n\t\t\tformatVersion = {\n\t\t\t\t'putty-user-key-file-2': 2,\n\t\t\t\t'putty-user-key-file-3': 3\n\t\t\t}[parts[0].toLowerCase()];\n\t\t\tif (formatVersion) {\n\t\t\t\tfound = true;\n\t\t\t\tbreak;\n\t\t\t}\n\t\t}\n\t}\n\tif (!found) {\n\t\tthrow (new Error('No PuTTY format first line found'));\n\t}\n\tvar alg = parts[1];\n\n\tparts = splitHeader(lines[si++]);\n\tassert.equal(parts[0].toLowerCase(), 'encryption');\n\tvar encryption = parts[1];\n\n\tparts = splitHeader(lines[si++]);\n\tassert.equal(parts[0].toLowerCase(), 'comment');\n\tvar comment = parts[1];\n\n\tparts = splitHeader(lines[si++]);\n\tassert.equal(parts[0].toLowerCase(), 'public-lines');\n\tvar publicLines = parseInt(parts[1], 10);\n\tif (!isFinite(publicLines) || publicLines < 0 ||\n\t    publicLines > lines.length) {\n\t\tthrow (new Error('Invalid public-lines count'));\n\t}\n\n\tvar publicBuf = Buffer.from(\n\t    lines.slice(si, si + publicLines).join(''), 'base64');\n\tvar keyType = rfc4253.algToKeyType(alg);\n\tvar key = rfc4253.read(publicBuf);\n\tif (key.type !== keyType) {\n\t\tthrow (new Error('Outer key algorithm mismatch'));\n\t}\n\n\tsi += publicLines;\n\tif (lines[si]) {\n\t\tparts = splitHeader(lines[si++]);\n\t\tassert.equal(parts[0].toLowerCase(), 'private-lines');\n\t\tvar privateLines = parseInt(parts[1], 10);\n\t\tif (!isFinite(privateLines) || privateLines < 0 ||\n\t\t    privateLines > lines.length) {\n\t\t\tthrow (new Error('Invalid private-lines count'));\n\t\t}\n\n\t\tvar privateBuf = Buffer.from(\n\t\t\tlines.slice(si, si + privateLines).join(''), 'base64');\n\n\t\tif (encryption !== 'none' && formatVersion === 3) {\n\t\t\tthrow new Error('Encrypted keys arenot supported for' +\n\t\t\t' PuTTY format version 3');\n\t\t}\n\n\t\tif (encryption === 'aes256-cbc') {\n\t\t\tif (!options.passphrase) {\n\t\t\t\tthrow (new errors.KeyEncryptedError(\n\t\t\t\t\toptions.filename, 'PEM'));\n\t\t\t}\n\n\t\t\tvar iv = Buffer.alloc(16, 0);\n\t\t\tvar decipher = crypto.createDecipheriv(\n\t\t\t\t'aes-256-cbc',\n\t\t\t\tderivePPK2EncryptionKey(options.passphrase),\n\t\t\t\tiv);\n\t\t\tdecipher.setAutoPadding(false);\n\t\t\tprivateBuf = Buffer.concat([\n\t\t\t\tdecipher.update(privateBuf), decipher.final()]);\n\t\t}\n\n\t\tkey = new PrivateKey(key);\n\t\tif (key.type !== keyType) {\n\t\t\tthrow (new Error('Outer key algorithm mismatch'));\n\t\t}\n\n\t\tvar sshbuf = new SSHBuffer({buffer: privateBuf});\n\t\tvar privateKeyParts;\n\t\tif (alg === 'ssh-dss') {\n\t\t\tprivateKeyParts = [ {\n\t\t\t\tname: 'x',\n\t\t\t\tdata: sshbuf.readBuffer()\n\t\t\t}];\n\t\t} else if (alg === 'ssh-rsa') {\n\t\t\tprivateKeyParts = [\n\t\t\t\t{ name: 'd', data: sshbuf.readBuffer() },\n\t\t\t\t{ name: 'p', data: sshbuf.readBuffer() },\n\t\t\t\t{ name: 'q', data: sshbuf.readBuffer() },\n\t\t\t\t{ name: 'iqmp', data: sshbuf.readBuffer() }\n\t\t\t];\n\t\t} else if (alg.match(/^ecdsa-sha2-nistp/)) {\n\t\t\tprivateKeyParts = [ {\n\t\t\t\tname: 'd', data: sshbuf.readBuffer()\n\t\t\t} ];\n\t\t} else if (alg === 'ssh-ed25519') {\n\t\t\tprivateKeyParts = [ {\n\t\t\t\tname: 'k', data: sshbuf.readBuffer()\n\t\t\t} ];\n\t\t} else {\n\t\t\tthrow new Error('Unsupported PPK key type: ' + alg);\n\t\t}\n\n\t\tkey = new PrivateKey({\n\t\t\ttype: key.type,\n\t\t\tparts: key.parts.concat(privateKeyParts)\n\t\t});\n\t}\n\n\tkey.comment = comment;\n\treturn (key);\n}\n\nfunction derivePPK2EncryptionKey(passphrase) {\n\tvar hash1 = crypto.createHash('sha1').update(Buffer.concat([\n\t\tBuffer.from([0, 0, 0, 0]),\n\t\tBuffer.from(passphrase)\n\t])).digest();\n\tvar hash2 = crypto.createHash('sha1').update(Buffer.concat([\n\t\tBuffer.from([0, 0, 0, 1]),\n\t\tBuffer.from(passphrase)\n\t])).digest();\n\treturn (Buffer.concat([hash1, hash2]).slice(0, 32));\n}\n\nfunction splitHeader(line) {\n\tvar idx = line.indexOf(':');\n\tif (idx === -1)\n\t\treturn (null);\n\tvar header = line.slice(0, idx);\n\t++idx;\n\twhile (line[idx] === ' ')\n\t\t++idx;\n\tvar rest = line.slice(idx);\n\treturn ([header, rest]);\n}\n\nfunction write(key, options) {\n\tassert.object(key);\n\tif (!Key.isKey(key))\n\t\tthrow (new Error('Must be a public key'));\n\n\tvar alg = rfc4253.keyTypeToAlg(key);\n\tvar buf = rfc4253.write(key);\n\tvar comment = key.comment || '';\n\n\tvar b64 = buf.toString('base64');\n\tvar lines = wrap(b64, 64);\n\n\tlines.unshift('Public-Lines: ' + lines.length);\n\tlines.unshift('Comment: ' + comment);\n\tlines.unshift('Encryption: none');\n\tlines.unshift('PuTTY-User-Key-File-2: ' + alg);\n\n\treturn (Buffer.from(lines.join('\\n') + '\\n'));\n}\n\nfunction wrap(txt, len) {\n\tvar lines = [];\n\tvar pos = 0;\n\twhile (pos < txt.length) {\n\t\tlines.push(txt.slice(pos, pos + 64));\n\t\tpos += 64;\n\t}\n\treturn (lines);\n}\n", "// Copyright 2016 Joyent, Inc.\n\nmodule.exports = Certificate;\n\nvar assert = require('assert-plus');\nvar Buffer = require('safer-buffer').Buffer;\nvar algs = require('./algs');\nvar crypto = require('crypto');\nvar Fingerprint = require('./fingerprint');\nvar Signature = require('./signature');\nvar errs = require('./errors');\nvar util = require('util');\nvar utils = require('./utils');\nvar Key = require('./key');\nvar PrivateKey = require('./private-key');\nvar Identity = require('./identity');\n\nvar formats = {};\nformats['openssh'] = require('./formats/openssh-cert');\nformats['x509'] = require('./formats/x509');\nformats['pem'] = require('./formats/x509-pem');\n\nvar CertificateParseError = errs.CertificateParseError;\nvar InvalidAlgorithmError = errs.InvalidAlgorithmError;\n\nfunction Certificate(opts) {\n\tassert.object(opts, 'options');\n\tassert.arrayOfObject(opts.subjects, 'options.subjects');\n\tutils.assertCompatible(opts.subjects[0], Identity, [1, 0],\n\t    'options.subjects');\n\tutils.assertCompatible(opts.subjectKey, Key, [1, 0],\n\t    'options.subjectKey');\n\tutils.assertCompatible(opts.issuer, Identity, [1, 0], 'options.issuer');\n\tif (opts.issuerKey !== undefined) {\n\t\tutils.assertCompatible(opts.issuerKey, Key, [1, 0],\n\t\t    'options.issuerKey');\n\t}\n\tassert.object(opts.signatures, 'options.signatures');\n\tassert.buffer(opts.serial, 'options.serial');\n\tassert.date(opts.validFrom, 'options.validFrom');\n\tassert.date(opts.validUntil, 'optons.validUntil');\n\n\tassert.optionalArrayOfString(opts.purposes, 'options.purposes');\n\n\tthis._hashCache = {};\n\n\tthis.subjects = opts.subjects;\n\tthis.issuer = opts.issuer;\n\tthis.subjectKey = opts.subjectKey;\n\tthis.issuerKey = opts.issuerKey;\n\tthis.signatures = opts.signatures;\n\tthis.serial = opts.serial;\n\tthis.validFrom = opts.validFrom;\n\tthis.validUntil = opts.validUntil;\n\tthis.purposes = opts.purposes;\n}\n\nCertificate.formats = formats;\n\nCertificate.prototype.toBuffer = function (format, options) {\n\tif (format === undefined)\n\t\tformat = 'x509';\n\tassert.string(format, 'format');\n\tassert.object(formats[format], 'formats[format]');\n\tassert.optionalObject(options, 'options');\n\n\treturn (formats[format].write(this, options));\n};\n\nCertificate.prototype.toString = function (format, options) {\n\tif (format === undefined)\n\t\tformat = 'pem';\n\treturn (this.toBuffer(format, options).toString());\n};\n\nCertificate.prototype.fingerprint = function (algo) {\n\tif (algo === undefined)\n\t\talgo = 'sha256';\n\tassert.string(algo, 'algorithm');\n\tvar opts = {\n\t\ttype: 'certificate',\n\t\thash: this.hash(algo),\n\t\talgorithm: algo\n\t};\n\treturn (new Fingerprint(opts));\n};\n\nCertificate.prototype.hash = function (algo) {\n\tassert.string(algo, 'algorithm');\n\talgo = algo.toLowerCase();\n\tif (algs.hashAlgs[algo] === undefined)\n\t\tthrow (new InvalidAlgorithmError(algo));\n\n\tif (this._hashCache[algo])\n\t\treturn (this._hashCache[algo]);\n\n\tvar hash = crypto.createHash(algo).\n\t    update(this.toBuffer('x509')).digest();\n\tthis._hashCache[algo] = hash;\n\treturn (hash);\n};\n\nCertificate.prototype.isExpired = function (when) {\n\tif (when === undefined)\n\t\twhen = new Date();\n\treturn (!((when.getTime() >= this.validFrom.getTime()) &&\n\t\t(when.getTime() < this.validUntil.getTime())));\n};\n\nCertificate.prototype.isSignedBy = function (issuerCert) {\n\tutils.assertCompatible(issuerCert, Certificate, [1, 0], 'issuer');\n\n\tif (!this.issuer.equals(issuerCert.subjects[0]))\n\t\treturn (false);\n\tif (this.issuer.purposes && this.issuer.purposes.length > 0 &&\n\t    this.issuer.purposes.indexOf('ca') === -1) {\n\t\treturn (false);\n\t}\n\n\treturn (this.isSignedByKey(issuerCert.subjectKey));\n};\n\nCertificate.prototype.getExtension = function (keyOrOid) {\n\tassert.string(keyOrOid, 'keyOrOid');\n\tvar ext = this.getExtensions().filter(function (maybeExt) {\n\t\tif (maybeExt.format === 'x509')\n\t\t\treturn (maybeExt.oid === keyOrOid);\n\t\tif (maybeExt.format === 'openssh')\n\t\t\treturn (maybeExt.name === keyOrOid);\n\t\treturn (false);\n\t})[0];\n\treturn (ext);\n};\n\nCertificate.prototype.getExtensions = function () {\n\tvar exts = [];\n\tvar x509 = this.signatures.x509;\n\tif (x509 && x509.extras && x509.extras.exts) {\n\t\tx509.extras.exts.forEach(function (ext) {\n\t\t\text.format = 'x509';\n\t\t\texts.push(ext);\n\t\t});\n\t}\n\tvar openssh = this.signatures.openssh;\n\tif (openssh && openssh.exts) {\n\t\topenssh.exts.forEach(function (ext) {\n\t\t\text.format = 'openssh';\n\t\t\texts.push(ext);\n\t\t});\n\t}\n\treturn (exts);\n};\n\nCertificate.prototype.isSignedByKey = function (issuerKey) {\n\tutils.assertCompatible(issuerKey, Key, [1, 2], 'issuerKey');\n\n\tif (this.issuerKey !== undefined) {\n\t\treturn (this.issuerKey.\n\t\t    fingerprint('sha512').matches(issuerKey));\n\t}\n\n\tvar fmt = Object.keys(this.signatures)[0];\n\tvar valid = formats[fmt].verify(this, issuerKey);\n\tif (valid)\n\t\tthis.issuerKey = issuerKey;\n\treturn (valid);\n};\n\nCertificate.prototype.signWith = function (key) {\n\tutils.assertCompatible(key, PrivateKey, [1, 2], 'key');\n\tvar fmts = Object.keys(formats);\n\tvar didOne = false;\n\tfor (var i = 0; i < fmts.length; ++i) {\n\t\tif (fmts[i] !== 'pem') {\n\t\t\tvar ret = formats[fmts[i]].sign(this, key);\n\t\t\tif (ret === true)\n\t\t\t\tdidOne = true;\n\t\t}\n\t}\n\tif (!didOne) {\n\t\tthrow (new Error('Failed to sign the certificate for any ' +\n\t\t    'available certificate formats'));\n\t}\n};\n\nCertificate.createSelfSigned = function (subjectOrSubjects, key, options) {\n\tvar subjects;\n\tif (Array.isArray(subjectOrSubjects))\n\t\tsubjects = subjectOrSubjects;\n\telse\n\t\tsubjects = [subjectOrSubjects];\n\n\tassert.arrayOfObject(subjects);\n\tsubjects.forEach(function (subject) {\n\t\tutils.assertCompatible(subject, Identity, [1, 0], 'subject');\n\t});\n\n\tutils.assertCompatible(key, PrivateKey, [1, 2], 'private key');\n\n\tassert.optionalObject(options, 'options');\n\tif (options === undefined)\n\t\toptions = {};\n\tassert.optionalObject(options.validFrom, 'options.validFrom');\n\tassert.optionalObject(options.validUntil, 'options.validUntil');\n\tvar validFrom = options.validFrom;\n\tvar validUntil = options.validUntil;\n\tif (validFrom === undefined)\n\t\tvalidFrom = new Date();\n\tif (validUntil === undefined) {\n\t\tassert.optionalNumber(options.lifetime, 'options.lifetime');\n\t\tvar lifetime = options.lifetime;\n\t\tif (lifetime === undefined)\n\t\t\tlifetime = 10*365*24*3600;\n\t\tvalidUntil = new Date();\n\t\tvalidUntil.setTime(validUntil.getTime() + lifetime*1000);\n\t}\n\tassert.optionalBuffer(options.serial, 'options.serial');\n\tvar serial = options.serial;\n\tif (serial === undefined)\n\t\tserial = Buffer.from('0000000000000001', 'hex');\n\n\tvar purposes = options.purposes;\n\tif (purposes === undefined)\n\t\tpurposes = [];\n\n\tif (purposes.indexOf('signature') === -1)\n\t\tpurposes.push('signature');\n\n\t/* Self-signed certs are always CAs. */\n\tif (purposes.indexOf('ca') === -1)\n\t\tpurposes.push('ca');\n\tif (purposes.indexOf('crl') === -1)\n\t\tpurposes.push('crl');\n\n\t/*\n\t * If we weren't explicitly given any other purposes, do the sensible\n\t * thing and add some basic ones depending on the subject type.\n\t */\n\tif (purposes.length <= 3) {\n\t\tvar hostSubjects = subjects.filter(function (subject) {\n\t\t\treturn (subject.type === 'host');\n\t\t});\n\t\tvar userSubjects = subjects.filter(function (subject) {\n\t\t\treturn (subject.type === 'user');\n\t\t});\n\t\tif (hostSubjects.length > 0) {\n\t\t\tif (purposes.indexOf('serverAuth') === -1)\n\t\t\t\tpurposes.push('serverAuth');\n\t\t}\n\t\tif (userSubjects.length > 0) {\n\t\t\tif (purposes.indexOf('clientAuth') === -1)\n\t\t\t\tpurposes.push('clientAuth');\n\t\t}\n\t\tif (userSubjects.length > 0 || hostSubjects.length > 0) {\n\t\t\tif (purposes.indexOf('keyAgreement') === -1)\n\t\t\t\tpurposes.push('keyAgreement');\n\t\t\tif (key.type === 'rsa' &&\n\t\t\t    purposes.indexOf('encryption') === -1)\n\t\t\t\tpurposes.push('encryption');\n\t\t}\n\t}\n\n\tvar cert = new Certificate({\n\t\tsubjects: subjects,\n\t\tissuer: subjects[0],\n\t\tsubjectKey: key.toPublic(),\n\t\tissuerKey: key.toPublic(),\n\t\tsignatures: {},\n\t\tserial: serial,\n\t\tvalidFrom: validFrom,\n\t\tvalidUntil: validUntil,\n\t\tpurposes: purposes\n\t});\n\tcert.signWith(key);\n\n\treturn (cert);\n};\n\nCertificate.create =\n    function (subjectOrSubjects, key, issuer, issuerKey, options) {\n\tvar subjects;\n\tif (Array.isArray(subjectOrSubjects))\n\t\tsubjects = subjectOrSubjects;\n\telse\n\t\tsubjects = [subjectOrSubjects];\n\n\tassert.arrayOfObject(subjects);\n\tsubjects.forEach(function (subject) {\n\t\tutils.assertCompatible(subject, Identity, [1, 0], 'subject');\n\t});\n\n\tutils.assertCompatible(key, Key, [1, 0], 'key');\n\tif (PrivateKey.isPrivateKey(key))\n\t\tkey = key.toPublic();\n\tutils.assertCompatible(issuer, Identity, [1, 0], 'issuer');\n\tutils.assertCompatible(issuerKey, PrivateKey, [1, 2], 'issuer key');\n\n\tassert.optionalObject(options, 'options');\n\tif (options === undefined)\n\t\toptions = {};\n\tassert.optionalObject(options.validFrom, 'options.validFrom');\n\tassert.optionalObject(options.validUntil, 'options.validUntil');\n\tvar validFrom = options.validFrom;\n\tvar validUntil = options.validUntil;\n\tif (validFrom === undefined)\n\t\tvalidFrom = new Date();\n\tif (validUntil === undefined) {\n\t\tassert.optionalNumber(options.lifetime, 'options.lifetime');\n\t\tvar lifetime = options.lifetime;\n\t\tif (lifetime === undefined)\n\t\t\tlifetime = 10*365*24*3600;\n\t\tvalidUntil = new Date();\n\t\tvalidUntil.setTime(validUntil.getTime() + lifetime*1000);\n\t}\n\tassert.optionalBuffer(options.serial, 'options.serial');\n\tvar serial = options.serial;\n\tif (serial === undefined)\n\t\tserial = Buffer.from('0000000000000001', 'hex');\n\n\tvar purposes = options.purposes;\n\tif (purposes === undefined)\n\t\tpurposes = [];\n\n\tif (purposes.indexOf('signature') === -1)\n\t\tpurposes.push('signature');\n\n\tif (options.ca === true) {\n\t\tif (purposes.indexOf('ca') === -1)\n\t\t\tpurposes.push('ca');\n\t\tif (purposes.indexOf('crl') === -1)\n\t\t\tpurposes.push('crl');\n\t}\n\n\tvar hostSubjects = subjects.filter(function (subject) {\n\t\treturn (subject.type === 'host');\n\t});\n\tvar userSubjects = subjects.filter(function (subject) {\n\t\treturn (subject.type === 'user');\n\t});\n\tif (hostSubjects.length > 0) {\n\t\tif (purposes.indexOf('serverAuth') === -1)\n\t\t\tpurposes.push('serverAuth');\n\t}\n\tif (userSubjects.length > 0) {\n\t\tif (purposes.indexOf('clientAuth') === -1)\n\t\t\tpurposes.push('clientAuth');\n\t}\n\tif (userSubjects.length > 0 || hostSubjects.length > 0) {\n\t\tif (purposes.indexOf('keyAgreement') === -1)\n\t\t\tpurposes.push('keyAgreement');\n\t\tif (key.type === 'rsa' &&\n\t\t    purposes.indexOf('encryption') === -1)\n\t\t\tpurposes.push('encryption');\n\t}\n\n\tvar cert = new Certificate({\n\t\tsubjects: subjects,\n\t\tissuer: issuer,\n\t\tsubjectKey: key,\n\t\tissuerKey: issuerKey.toPublic(),\n\t\tsignatures: {},\n\t\tserial: serial,\n\t\tvalidFrom: validFrom,\n\t\tvalidUntil: validUntil,\n\t\tpurposes: purposes\n\t});\n\tcert.signWith(issuerKey);\n\n\treturn (cert);\n};\n\nCertificate.parse = function (data, format, options) {\n\tif (typeof (data) !== 'string')\n\t\tassert.buffer(data, 'data');\n\tif (format === undefined)\n\t\tformat = 'auto';\n\tassert.string(format, 'format');\n\tif (typeof (options) === 'string')\n\t\toptions = { filename: options };\n\tassert.optionalObject(options, 'options');\n\tif (options === undefined)\n\t\toptions = {};\n\tassert.optionalString(options.filename, 'options.filename');\n\tif (options.filename === undefined)\n\t\toptions.filename = '(unnamed)';\n\n\tassert.object(formats[format], 'formats[format]');\n\n\ttry {\n\t\tvar k = formats[format].read(data, options);\n\t\treturn (k);\n\t} catch (e) {\n\t\tthrow (new CertificateParseError(options.filename, format, e));\n\t}\n};\n\nCertificate.isCertificate = function (obj, ver) {\n\treturn (utils.isCompatible(obj, Certificate, ver));\n};\n\n/*\n * API versions for Certificate:\n * [1,0] -- initial ver\n * [1,1] -- openssh format now unpacks extensions\n */\nCertificate.prototype._sshpkApiVersion = [1, 1];\n\nCertificate._oldVersionDetect = function (obj) {\n\treturn ([1, 0]);\n};\n", "// Copyright 2017 Joyent, Inc.\n\nmodule.exports = Identity;\n\nvar assert = require('assert-plus');\nvar algs = require('./algs');\nvar crypto = require('crypto');\nvar Fingerprint = require('./fingerprint');\nvar Signature = require('./signature');\nvar errs = require('./errors');\nvar util = require('util');\nvar utils = require('./utils');\nvar asn1 = require('asn1');\nvar Buffer = require('safer-buffer').Buffer;\n\n/*JSSTYLED*/\nvar DNS_NAME_RE = /^([*]|[a-z0-9][a-z0-9\\-]{0,62})(?:\\.([*]|[a-z0-9][a-z0-9\\-]{0,62}))*$/i;\n\nvar oids = {};\noids.cn = '*******';\noids.o = '********';\noids.ou = '********';\noids.l = '*******';\noids.s = '*******';\noids.c = '*******';\noids.sn = '*******';\noids.postalCode = '********';\noids.serialNumber = '*******';\noids.street = '*******';\noids.x500UniqueIdentifier = '*******5';\noids.role = '*******2';\noids.telephoneNumber = '********';\noids.description = '********';\noids.dc = '0.9.2342.19200300.100.1.25';\noids.uid = '0.9.2342.19200300.100.1.1';\noids.mail = '0.9.2342.19200300.100.1.3';\noids.title = '********';\noids.gn = '********';\noids.initials = '********';\noids.pseudonym = '********';\noids.emailAddress = '1.2.840.113549.1.9.1';\n\nvar unoids = {};\nObject.keys(oids).forEach(function (k) {\n\tunoids[oids[k]] = k;\n});\n\nfunction Identity(opts) {\n\tvar self = this;\n\tassert.object(opts, 'options');\n\tassert.arrayOfObject(opts.components, 'options.components');\n\tthis.components = opts.components;\n\tthis.componentLookup = {};\n\tthis.components.forEach(function (c) {\n\t\tif (c.name && !c.oid)\n\t\t\tc.oid = oids[c.name];\n\t\tif (c.oid && !c.name)\n\t\t\tc.name = unoids[c.oid];\n\t\tif (self.componentLookup[c.name] === undefined)\n\t\t\tself.componentLookup[c.name] = [];\n\t\tself.componentLookup[c.name].push(c);\n\t});\n\tif (this.componentLookup.cn && this.componentLookup.cn.length > 0) {\n\t\tthis.cn = this.componentLookup.cn[0].value;\n\t}\n\tassert.optionalString(opts.type, 'options.type');\n\tif (opts.type === undefined) {\n\t\tif (this.components.length === 1 &&\n\t\t    this.componentLookup.cn &&\n\t\t    this.componentLookup.cn.length === 1 &&\n\t\t    this.componentLookup.cn[0].value.match(DNS_NAME_RE)) {\n\t\t\tthis.type = 'host';\n\t\t\tthis.hostname = this.componentLookup.cn[0].value;\n\n\t\t} else if (this.componentLookup.dc &&\n\t\t    this.components.length === this.componentLookup.dc.length) {\n\t\t\tthis.type = 'host';\n\t\t\tthis.hostname = this.componentLookup.dc.map(\n\t\t\t    function (c) {\n\t\t\t\treturn (c.value);\n\t\t\t}).join('.');\n\n\t\t} else if (this.componentLookup.uid &&\n\t\t    this.components.length ===\n\t\t    this.componentLookup.uid.length) {\n\t\t\tthis.type = 'user';\n\t\t\tthis.uid = this.componentLookup.uid[0].value;\n\n\t\t} else if (this.componentLookup.cn &&\n\t\t    this.componentLookup.cn.length === 1 &&\n\t\t    this.componentLookup.cn[0].value.match(DNS_NAME_RE)) {\n\t\t\tthis.type = 'host';\n\t\t\tthis.hostname = this.componentLookup.cn[0].value;\n\n\t\t} else if (this.componentLookup.uid &&\n\t\t    this.componentLookup.uid.length === 1) {\n\t\t\tthis.type = 'user';\n\t\t\tthis.uid = this.componentLookup.uid[0].value;\n\n\t\t} else if (this.componentLookup.mail &&\n\t\t    this.componentLookup.mail.length === 1) {\n\t\t\tthis.type = 'email';\n\t\t\tthis.email = this.componentLookup.mail[0].value;\n\n\t\t} else if (this.componentLookup.cn &&\n\t\t    this.componentLookup.cn.length === 1) {\n\t\t\tthis.type = 'user';\n\t\t\tthis.uid = this.componentLookup.cn[0].value;\n\n\t\t} else {\n\t\t\tthis.type = 'unknown';\n\t\t}\n\t} else {\n\t\tthis.type = opts.type;\n\t\tif (this.type === 'host')\n\t\t\tthis.hostname = opts.hostname;\n\t\telse if (this.type === 'user')\n\t\t\tthis.uid = opts.uid;\n\t\telse if (this.type === 'email')\n\t\t\tthis.email = opts.email;\n\t\telse\n\t\t\tthrow (new Error('Unknown type ' + this.type));\n\t}\n}\n\nIdentity.prototype.toString = function () {\n\treturn (this.components.map(function (c) {\n\t\tvar n = c.name.toUpperCase();\n\t\t/*JSSTYLED*/\n\t\tn = n.replace(/=/g, '\\\\=');\n\t\tvar v = c.value;\n\t\t/*JSSTYLED*/\n\t\tv = v.replace(/,/g, '\\\\,');\n\t\treturn (n + '=' + v);\n\t}).join(', '));\n};\n\nIdentity.prototype.get = function (name, asArray) {\n\tassert.string(name, 'name');\n\tvar arr = this.componentLookup[name];\n\tif (arr === undefined || arr.length === 0)\n\t\treturn (undefined);\n\tif (!asArray && arr.length > 1)\n\t\tthrow (new Error('Multiple values for attribute ' + name));\n\tif (!asArray)\n\t\treturn (arr[0].value);\n\treturn (arr.map(function (c) {\n\t\treturn (c.value);\n\t}));\n};\n\nIdentity.prototype.toArray = function (idx) {\n\treturn (this.components.map(function (c) {\n\t\treturn ({\n\t\t\tname: c.name,\n\t\t\tvalue: c.value\n\t\t});\n\t}));\n};\n\n/*\n * These are from X.680 -- PrintableString allowed chars are in section 37.4\n * table 8. Spec for IA5Strings is \"1,6 + SPACE + DEL\" where 1 refers to\n * ISO IR #001 (standard ASCII control characters) and 6 refers to ISO IR #006\n * (the basic ASCII character set).\n */\n/* JSSTYLED */\nvar NOT_PRINTABLE = /[^a-zA-Z0-9 '(),+.\\/:=?-]/;\n/* JSSTYLED */\nvar NOT_IA5 = /[^\\x00-\\x7f]/;\n\nIdentity.prototype.toAsn1 = function (der, tag) {\n\tder.startSequence(tag);\n\tthis.components.forEach(function (c) {\n\t\tder.startSequence(asn1.Ber.Constructor | asn1.Ber.Set);\n\t\tder.startSequence();\n\t\tder.writeOID(c.oid);\n\t\t/*\n\t\t * If we fit in a PrintableString, use that. Otherwise use an\n\t\t * IA5String or UTF8String.\n\t\t *\n\t\t * If this identity was parsed from a DN, use the ASN.1 types\n\t\t * from the original representation (otherwise this might not\n\t\t * be a full match for the original in some validators).\n\t\t */\n\t\tif (c.asn1type === asn1.Ber.Utf8String ||\n\t\t    c.value.match(NOT_IA5)) {\n\t\t\tvar v = Buffer.from(c.value, 'utf8');\n\t\t\tder.writeBuffer(v, asn1.Ber.Utf8String);\n\n\t\t} else if (c.asn1type === asn1.Ber.IA5String ||\n\t\t    c.value.match(NOT_PRINTABLE)) {\n\t\t\tder.writeString(c.value, asn1.Ber.IA5String);\n\n\t\t} else {\n\t\t\tvar type = asn1.Ber.PrintableString;\n\t\t\tif (c.asn1type !== undefined)\n\t\t\t\ttype = c.asn1type;\n\t\t\tder.writeString(c.value, type);\n\t\t}\n\t\tder.endSequence();\n\t\tder.endSequence();\n\t});\n\tder.endSequence();\n};\n\nfunction globMatch(a, b) {\n\tif (a === '**' || b === '**')\n\t\treturn (true);\n\tvar aParts = a.split('.');\n\tvar bParts = b.split('.');\n\tif (aParts.length !== bParts.length)\n\t\treturn (false);\n\tfor (var i = 0; i < aParts.length; ++i) {\n\t\tif (aParts[i] === '*' || bParts[i] === '*')\n\t\t\tcontinue;\n\t\tif (aParts[i] !== bParts[i])\n\t\t\treturn (false);\n\t}\n\treturn (true);\n}\n\nIdentity.prototype.equals = function (other) {\n\tif (!Identity.isIdentity(other, [1, 0]))\n\t\treturn (false);\n\tif (other.components.length !== this.components.length)\n\t\treturn (false);\n\tfor (var i = 0; i < this.components.length; ++i) {\n\t\tif (this.components[i].oid !== other.components[i].oid)\n\t\t\treturn (false);\n\t\tif (!globMatch(this.components[i].value,\n\t\t    other.components[i].value)) {\n\t\t\treturn (false);\n\t\t}\n\t}\n\treturn (true);\n};\n\nIdentity.forHost = function (hostname) {\n\tassert.string(hostname, 'hostname');\n\treturn (new Identity({\n\t\ttype: 'host',\n\t\thostname: hostname,\n\t\tcomponents: [ { name: 'cn', value: hostname } ]\n\t}));\n};\n\nIdentity.forUser = function (uid) {\n\tassert.string(uid, 'uid');\n\treturn (new Identity({\n\t\ttype: 'user',\n\t\tuid: uid,\n\t\tcomponents: [ { name: 'uid', value: uid } ]\n\t}));\n};\n\nIdentity.forEmail = function (email) {\n\tassert.string(email, 'email');\n\treturn (new Identity({\n\t\ttype: 'email',\n\t\temail: email,\n\t\tcomponents: [ { name: 'mail', value: email } ]\n\t}));\n};\n\nIdentity.parseDN = function (dn) {\n\tassert.string(dn, 'dn');\n\tvar parts = [''];\n\tvar idx = 0;\n\tvar rem = dn;\n\twhile (rem.length > 0) {\n\t\tvar m;\n\t\t/*JSSTYLED*/\n\t\tif ((m = /^,/.exec(rem)) !== null) {\n\t\t\tparts[++idx] = '';\n\t\t\trem = rem.slice(m[0].length);\n\t\t/*JSSTYLED*/\n\t\t} else if ((m = /^\\\\,/.exec(rem)) !== null) {\n\t\t\tparts[idx] += ',';\n\t\t\trem = rem.slice(m[0].length);\n\t\t/*JSSTYLED*/\n\t\t} else if ((m = /^\\\\./.exec(rem)) !== null) {\n\t\t\tparts[idx] += m[0];\n\t\t\trem = rem.slice(m[0].length);\n\t\t/*JSSTYLED*/\n\t\t} else if ((m = /^[^\\\\,]+/.exec(rem)) !== null) {\n\t\t\tparts[idx] += m[0];\n\t\t\trem = rem.slice(m[0].length);\n\t\t} else {\n\t\t\tthrow (new Error('Failed to parse DN'));\n\t\t}\n\t}\n\tvar cmps = parts.map(function (c) {\n\t\tc = c.trim();\n\t\tvar eqPos = c.indexOf('=');\n\t\twhile (eqPos > 0 && c.charAt(eqPos - 1) === '\\\\')\n\t\t\teqPos = c.indexOf('=', eqPos + 1);\n\t\tif (eqPos === -1) {\n\t\t\tthrow (new Error('Failed to parse DN'));\n\t\t}\n\t\t/*JSSTYLED*/\n\t\tvar name = c.slice(0, eqPos).toLowerCase().replace(/\\\\=/g, '=');\n\t\tvar value = c.slice(eqPos + 1);\n\t\treturn ({ name: name, value: value });\n\t});\n\treturn (new Identity({ components: cmps }));\n};\n\nIdentity.fromArray = function (components) {\n\tassert.arrayOfObject(components, 'components');\n\tcomponents.forEach(function (cmp) {\n\t\tassert.object(cmp, 'component');\n\t\tassert.string(cmp.name, 'component.name');\n\t\tif (!Buffer.isBuffer(cmp.value) &&\n\t\t    !(typeof (cmp.value) === 'string')) {\n\t\t\tthrow (new Error('Invalid component value'));\n\t\t}\n\t});\n\treturn (new Identity({ components: components }));\n};\n\nIdentity.parseAsn1 = function (der, top) {\n\tvar components = [];\n\tder.readSequence(top);\n\tvar end = der.offset + der.length;\n\twhile (der.offset < end) {\n\t\tder.readSequence(asn1.Ber.Constructor | asn1.Ber.Set);\n\t\tvar after = der.offset + der.length;\n\t\tder.readSequence();\n\t\tvar oid = der.readOID();\n\t\tvar type = der.peek();\n\t\tvar value;\n\t\tswitch (type) {\n\t\tcase asn1.Ber.PrintableString:\n\t\tcase asn1.Ber.IA5String:\n\t\tcase asn1.Ber.OctetString:\n\t\tcase asn1.Ber.T61String:\n\t\t\tvalue = der.readString(type);\n\t\t\tbreak;\n\t\tcase asn1.Ber.Utf8String:\n\t\t\tvalue = der.readString(type, true);\n\t\t\tvalue = value.toString('utf8');\n\t\t\tbreak;\n\t\tcase asn1.Ber.CharacterString:\n\t\tcase asn1.Ber.BMPString:\n\t\t\tvalue = der.readString(type, true);\n\t\t\tvalue = value.toString('utf16le');\n\t\t\tbreak;\n\t\tdefault:\n\t\t\tthrow (new Error('Unknown asn1 type ' + type));\n\t\t}\n\t\tcomponents.push({ oid: oid, asn1type: type, value: value });\n\t\tder._offset = after;\n\t}\n\tder._offset = end;\n\treturn (new Identity({\n\t\tcomponents: components\n\t}));\n};\n\nIdentity.isIdentity = function (obj, ver) {\n\treturn (utils.isCompatible(obj, Identity, ver));\n};\n\n/*\n * API versions for Identity:\n * [1,0] -- initial ver\n */\nIdentity.prototype._sshpkApiVersion = [1, 0];\n\nIdentity._oldVersionDetect = function (obj) {\n\treturn ([1, 0]);\n};\n", "// Copyright 2017 Joyent, Inc.\n\nmodule.exports = {\n\tread: read,\n\tverify: verify,\n\tsign: sign,\n\tsignAsync: signAsync,\n\twrite: write,\n\n\t/* Internal private API */\n\tfromBuffer: fromBuffer,\n\ttoBuffer: toBuffer\n};\n\nvar assert = require('assert-plus');\nvar SSHBuffer = require('../ssh-buffer');\nvar crypto = require('crypto');\nvar Buffer = require('safer-buffer').Buffer;\nvar algs = require('../algs');\nvar Key = require('../key');\nvar PrivateKey = require('../private-key');\nvar Identity = require('../identity');\nvar rfc4253 = require('./rfc4253');\nvar Signature = require('../signature');\nvar utils = require('../utils');\nvar Certificate = require('../certificate');\n\nfunction verify(cert, key) {\n\t/*\n\t * We always give an issuerKey, so if our verify() is being called then\n\t * there was no signature. Return false.\n\t */\n\treturn (false);\n}\n\nvar TYPES = {\n\t'user': 1,\n\t'host': 2\n};\nObject.keys(TYPES).forEach(function (k) { TYPES[TYPES[k]] = k; });\n\nvar ECDSA_ALGO = /^ecdsa-sha2-([^@-]+)-<EMAIL>$/;\n\nfunction read(buf, options) {\n\tif (Buffer.isBuffer(buf))\n\t\tbuf = buf.toString('ascii');\n\tvar parts = buf.trim().split(/[ \\t\\n]+/g);\n\tif (parts.length < 2 || parts.length > 3)\n\t\tthrow (new Error('Not a valid SSH certificate line'));\n\n\tvar algo = parts[0];\n\tvar data = parts[1];\n\n\tdata = Buffer.from(data, 'base64');\n\treturn (fromBuffer(data, algo));\n}\n\nfunction fromBuffer(data, algo, partial) {\n\tvar sshbuf = new SSHBuffer({ buffer: data });\n\tvar innerAlgo = sshbuf.readString();\n\tif (algo !== undefined && innerAlgo !== algo)\n\t\tthrow (new Error('SSH certificate algorithm mismatch'));\n\tif (algo === undefined)\n\t\talgo = innerAlgo;\n\n\tvar cert = {};\n\tcert.signatures = {};\n\tcert.signatures.openssh = {};\n\n\tcert.signatures.openssh.nonce = sshbuf.readBuffer();\n\n\tvar key = {};\n\tvar parts = (key.parts = []);\n\tkey.type = getAlg(algo);\n\n\tvar partCount = algs.info[key.type].parts.length;\n\twhile (parts.length < partCount)\n\t\tparts.push(sshbuf.readPart());\n\tassert.ok(parts.length >= 1, 'key must have at least one part');\n\n\tvar algInfo = algs.info[key.type];\n\tif (key.type === 'ecdsa') {\n\t\tvar res = ECDSA_ALGO.exec(algo);\n\t\tassert.ok(res !== null);\n\t\tassert.strictEqual(res[1], parts[0].data.toString());\n\t}\n\n\tfor (var i = 0; i < algInfo.parts.length; ++i) {\n\t\tparts[i].name = algInfo.parts[i];\n\t\tif (parts[i].name !== 'curve' &&\n\t\t    algInfo.normalize !== false) {\n\t\t\tvar p = parts[i];\n\t\t\tp.data = utils.mpNormalize(p.data);\n\t\t}\n\t}\n\n\tcert.subjectKey = new Key(key);\n\n\tcert.serial = sshbuf.readInt64();\n\n\tvar type = TYPES[sshbuf.readInt()];\n\tassert.string(type, 'valid cert type');\n\n\tcert.signatures.openssh.keyId = sshbuf.readString();\n\n\tvar principals = [];\n\tvar pbuf = sshbuf.readBuffer();\n\tvar psshbuf = new SSHBuffer({ buffer: pbuf });\n\twhile (!psshbuf.atEnd())\n\t\tprincipals.push(psshbuf.readString());\n\tif (principals.length === 0)\n\t\tprincipals = ['*'];\n\n\tcert.subjects = principals.map(function (pr) {\n\t\tif (type === 'user')\n\t\t\treturn (Identity.forUser(pr));\n\t\telse if (type === 'host')\n\t\t\treturn (Identity.forHost(pr));\n\t\tthrow (new Error('Unknown identity type ' + type));\n\t});\n\n\tcert.validFrom = int64ToDate(sshbuf.readInt64());\n\tcert.validUntil = int64ToDate(sshbuf.readInt64());\n\n\tvar exts = [];\n\tvar extbuf = new SSHBuffer({ buffer: sshbuf.readBuffer() });\n\tvar ext;\n\twhile (!extbuf.atEnd()) {\n\t\text = { critical: true };\n\t\text.name = extbuf.readString();\n\t\text.data = extbuf.readBuffer();\n\t\texts.push(ext);\n\t}\n\textbuf = new SSHBuffer({ buffer: sshbuf.readBuffer() });\n\twhile (!extbuf.atEnd()) {\n\t\text = { critical: false };\n\t\text.name = extbuf.readString();\n\t\text.data = extbuf.readBuffer();\n\t\texts.push(ext);\n\t}\n\tcert.signatures.openssh.exts = exts;\n\n\t/* reserved */\n\tsshbuf.readBuffer();\n\n\tvar signingKeyBuf = sshbuf.readBuffer();\n\tcert.issuerKey = rfc4253.read(signingKeyBuf);\n\n\t/*\n\t * OpenSSH certs don't give the identity of the issuer, just their\n\t * public key. So, we use an Identity that matches anything. The\n\t * isSignedBy() function will later tell you if the key matches.\n\t */\n\tcert.issuer = Identity.forHost('**');\n\n\tvar sigBuf = sshbuf.readBuffer();\n\tcert.signatures.openssh.signature =\n\t    Signature.parse(sigBuf, cert.issuerKey.type, 'ssh');\n\n\tif (partial !== undefined) {\n\t\tpartial.remainder = sshbuf.remainder();\n\t\tpartial.consumed = sshbuf._offset;\n\t}\n\n\treturn (new Certificate(cert));\n}\n\nfunction int64ToDate(buf) {\n\tvar i = buf.readUInt32BE(0) * 4294967296;\n\ti += buf.readUInt32BE(4);\n\tvar d = new Date();\n\td.setTime(i * 1000);\n\td.sourceInt64 = buf;\n\treturn (d);\n}\n\nfunction dateToInt64(date) {\n\tif (date.sourceInt64 !== undefined)\n\t\treturn (date.sourceInt64);\n\tvar i = Math.round(date.getTime() / 1000);\n\tvar upper = Math.floor(i / 4294967296);\n\tvar lower = Math.floor(i % 4294967296);\n\tvar buf = Buffer.alloc(8);\n\tbuf.writeUInt32BE(upper, 0);\n\tbuf.writeUInt32BE(lower, 4);\n\treturn (buf);\n}\n\nfunction sign(cert, key) {\n\tif (cert.signatures.openssh === undefined)\n\t\tcert.signatures.openssh = {};\n\ttry {\n\t\tvar blob = toBuffer(cert, true);\n\t} catch (e) {\n\t\tdelete (cert.signatures.openssh);\n\t\treturn (false);\n\t}\n\tvar sig = cert.signatures.openssh;\n\tvar hashAlgo = undefined;\n\tif (key.type === 'rsa' || key.type === 'dsa')\n\t\thashAlgo = 'sha1';\n\tvar signer = key.createSign(hashAlgo);\n\tsigner.write(blob);\n\tsig.signature = signer.sign();\n\treturn (true);\n}\n\nfunction signAsync(cert, signer, done) {\n\tif (cert.signatures.openssh === undefined)\n\t\tcert.signatures.openssh = {};\n\ttry {\n\t\tvar blob = toBuffer(cert, true);\n\t} catch (e) {\n\t\tdelete (cert.signatures.openssh);\n\t\tdone(e);\n\t\treturn;\n\t}\n\tvar sig = cert.signatures.openssh;\n\n\tsigner(blob, function (err, signature) {\n\t\tif (err) {\n\t\t\tdone(err);\n\t\t\treturn;\n\t\t}\n\t\ttry {\n\t\t\t/*\n\t\t\t * This will throw if the signature isn't of a\n\t\t\t * type/algo that can be used for SSH.\n\t\t\t */\n\t\t\tsignature.toBuffer('ssh');\n\t\t} catch (e) {\n\t\t\tdone(e);\n\t\t\treturn;\n\t\t}\n\t\tsig.signature = signature;\n\t\tdone();\n\t});\n}\n\nfunction write(cert, options) {\n\tif (options === undefined)\n\t\toptions = {};\n\n\tvar blob = toBuffer(cert);\n\tvar out = getCertType(cert.subjectKey) + ' ' + blob.toString('base64');\n\tif (options.comment)\n\t\tout = out + ' ' + options.comment;\n\treturn (out);\n}\n\n\nfunction toBuffer(cert, noSig) {\n\tassert.object(cert.signatures.openssh, 'signature for openssh format');\n\tvar sig = cert.signatures.openssh;\n\n\tif (sig.nonce === undefined)\n\t\tsig.nonce = crypto.randomBytes(16);\n\tvar buf = new SSHBuffer({});\n\tbuf.writeString(getCertType(cert.subjectKey));\n\tbuf.writeBuffer(sig.nonce);\n\n\tvar key = cert.subjectKey;\n\tvar algInfo = algs.info[key.type];\n\talgInfo.parts.forEach(function (part) {\n\t\tbuf.writePart(key.part[part]);\n\t});\n\n\tbuf.writeInt64(cert.serial);\n\n\tvar type = cert.subjects[0].type;\n\tassert.notStrictEqual(type, 'unknown');\n\tcert.subjects.forEach(function (id) {\n\t\tassert.strictEqual(id.type, type);\n\t});\n\ttype = TYPES[type];\n\tbuf.writeInt(type);\n\n\tif (sig.keyId === undefined) {\n\t\tsig.keyId = cert.subjects[0].type + '_' +\n\t\t    (cert.subjects[0].uid || cert.subjects[0].hostname);\n\t}\n\tbuf.writeString(sig.keyId);\n\n\tvar sub = new SSHBuffer({});\n\tcert.subjects.forEach(function (id) {\n\t\tif (type === TYPES.host)\n\t\t\tsub.writeString(id.hostname);\n\t\telse if (type === TYPES.user)\n\t\t\tsub.writeString(id.uid);\n\t});\n\tbuf.writeBuffer(sub.toBuffer());\n\n\tbuf.writeInt64(dateToInt64(cert.validFrom));\n\tbuf.writeInt64(dateToInt64(cert.validUntil));\n\n\tvar exts = sig.exts;\n\tif (exts === undefined)\n\t\texts = [];\n\n\tvar extbuf = new SSHBuffer({});\n\texts.forEach(function (ext) {\n\t\tif (ext.critical !== true)\n\t\t\treturn;\n\t\textbuf.writeString(ext.name);\n\t\textbuf.writeBuffer(ext.data);\n\t});\n\tbuf.writeBuffer(extbuf.toBuffer());\n\n\textbuf = new SSHBuffer({});\n\texts.forEach(function (ext) {\n\t\tif (ext.critical === true)\n\t\t\treturn;\n\t\textbuf.writeString(ext.name);\n\t\textbuf.writeBuffer(ext.data);\n\t});\n\tbuf.writeBuffer(extbuf.toBuffer());\n\n\t/* reserved */\n\tbuf.writeBuffer(Buffer.alloc(0));\n\n\tsub = rfc4253.write(cert.issuerKey);\n\tbuf.writeBuffer(sub);\n\n\tif (!noSig)\n\t\tbuf.writeBuffer(sig.signature.toBuffer('ssh'));\n\n\treturn (buf.toBuffer());\n}\n\nfunction getAlg(certType) {\n\tif (certType === '<EMAIL>')\n\t\treturn ('rsa');\n\tif (certType === '<EMAIL>')\n\t\treturn ('dsa');\n\tif (certType.match(ECDSA_ALGO))\n\t\treturn ('ecdsa');\n\tif (certType === '<EMAIL>')\n\t\treturn ('ed25519');\n\tthrow (new Error('Unsupported cert type ' + certType));\n}\n\nfunction getCertType(key) {\n\tif (key.type === 'rsa')\n\t\treturn ('<EMAIL>');\n\tif (key.type === 'dsa')\n\t\treturn ('<EMAIL>');\n\tif (key.type === 'ecdsa')\n\t\treturn ('ecdsa-sha2-' + key.curve + '-<EMAIL>');\n\tif (key.type === 'ed25519')\n\t\treturn ('<EMAIL>');\n\tthrow (new Error('Unsupported key type ' + key.type));\n}\n", "// Copyright 2017 Joyent, Inc.\n\nmodule.exports = {\n\tread: read,\n\tverify: verify,\n\tsign: sign,\n\tsignAsync: signAsync,\n\twrite: write\n};\n\nvar assert = require('assert-plus');\nvar asn1 = require('asn1');\nvar Buffer = require('safer-buffer').Buffer;\nvar algs = require('../algs');\nvar utils = require('../utils');\nvar Key = require('../key');\nvar PrivateKey = require('../private-key');\nvar pem = require('./pem');\nvar Identity = require('../identity');\nvar Signature = require('../signature');\nvar Certificate = require('../certificate');\nvar pkcs8 = require('./pkcs8');\n\n/*\n * This file is based on RFC5280 (X.509).\n */\n\n/* Helper to read in a single mpint */\nfunction readMPInt(der, nm) {\n\tassert.strictEqual(der.peek(), asn1.Ber.Integer,\n\t    nm + ' is not an Integer');\n\treturn (utils.mpNormalize(der.readString(asn1.Ber.<PERSON>ger, true)));\n}\n\nfunction verify(cert, key) {\n\tvar sig = cert.signatures.x509;\n\tassert.object(sig, 'x509 signature');\n\n\tvar algParts = sig.algo.split('-');\n\tif (algParts[0] !== key.type)\n\t\treturn (false);\n\n\tvar blob = sig.cache;\n\tif (blob === undefined) {\n\t\tvar der = new asn1.BerWriter();\n\t\twriteTBSCert(cert, der);\n\t\tblob = der.buffer;\n\t}\n\n\tvar verifier = key.createVerify(algParts[1]);\n\tverifier.write(blob);\n\treturn (verifier.verify(sig.signature));\n}\n\nfunction Local(i) {\n\treturn (asn1.Ber.Context | asn1.Ber.Constructor | i);\n}\n\nfunction Context(i) {\n\treturn (asn1.Ber.Context | i);\n}\n\nvar SIGN_ALGS = {\n\t'rsa-md5': '1.2.840.113549.1.1.4',\n\t'rsa-sha1': '1.2.840.113549.1.1.5',\n\t'rsa-sha256': '1.2.840.113549.1.1.11',\n\t'rsa-sha384': '1.2.840.113549.1.1.12',\n\t'rsa-sha512': '1.2.840.113549.1.1.13',\n\t'dsa-sha1': '1.2.840.10040.4.3',\n\t'dsa-sha256': '2.16.840.*********.3.2',\n\t'ecdsa-sha1': '1.2.840.10045.4.1',\n\t'ecdsa-sha256': '1.2.840.10045.4.3.2',\n\t'ecdsa-sha384': '1.2.840.10045.4.3.3',\n\t'ecdsa-sha512': '1.2.840.10045.4.3.4',\n\t'ed25519-sha512': '1.3.101.112'\n};\nObject.keys(SIGN_ALGS).forEach(function (k) {\n\tSIGN_ALGS[SIGN_ALGS[k]] = k;\n});\nSIGN_ALGS['1.3.14.3.2.3'] = 'rsa-md5';\nSIGN_ALGS['1.3.14.3.2.29'] = 'rsa-sha1';\n\nvar EXTS = {\n\t'issuerKeyId': '2.5.29.35',\n\t'altName': '2.5.29.17',\n\t'basicConstraints': '2.5.29.19',\n\t'keyUsage': '2.5.29.15',\n\t'extKeyUsage': '2.5.29.37'\n};\n\nfunction read(buf, options) {\n\tif (typeof (buf) === 'string') {\n\t\tbuf = Buffer.from(buf, 'binary');\n\t}\n\tassert.buffer(buf, 'buf');\n\n\tvar der = new asn1.BerReader(buf);\n\n\tder.readSequence();\n\tif (Math.abs(der.length - der.remain) > 1) {\n\t\tthrow (new Error('DER sequence does not contain whole byte ' +\n\t\t    'stream'));\n\t}\n\n\tvar tbsStart = der.offset;\n\tder.readSequence();\n\tvar sigOffset = der.offset + der.length;\n\tvar tbsEnd = sigOffset;\n\n\tif (der.peek() === Local(0)) {\n\t\tder.readSequence(Local(0));\n\t\tvar version = der.readInt();\n\t\tassert.ok(version <= 3,\n\t\t    'only x.509 versions up to v3 supported');\n\t}\n\n\tvar cert = {};\n\tcert.signatures = {};\n\tvar sig = (cert.signatures.x509 = {});\n\tsig.extras = {};\n\n\tcert.serial = readMPInt(der, 'serial');\n\n\tder.readSequence();\n\tvar after = der.offset + der.length;\n\tvar certAlgOid = der.readOID();\n\tvar certAlg = SIGN_ALGS[certAlgOid];\n\tif (certAlg === undefined)\n\t\tthrow (new Error('unknown signature algorithm ' + certAlgOid));\n\n\tder._offset = after;\n\tcert.issuer = Identity.parseAsn1(der);\n\n\tder.readSequence();\n\tcert.validFrom = readDate(der);\n\tcert.validUntil = readDate(der);\n\n\tcert.subjects = [Identity.parseAsn1(der)];\n\n\tder.readSequence();\n\tafter = der.offset + der.length;\n\tcert.subjectKey = pkcs8.readPkcs8(undefined, 'public', der);\n\tder._offset = after;\n\n\t/* issuerUniqueID */\n\tif (der.peek() === Local(1)) {\n\t\tder.readSequence(Local(1));\n\t\tsig.extras.issuerUniqueID =\n\t\t    buf.slice(der.offset, der.offset + der.length);\n\t\tder._offset += der.length;\n\t}\n\n\t/* subjectUniqueID */\n\tif (der.peek() === Local(2)) {\n\t\tder.readSequence(Local(2));\n\t\tsig.extras.subjectUniqueID =\n\t\t    buf.slice(der.offset, der.offset + der.length);\n\t\tder._offset += der.length;\n\t}\n\n\t/* extensions */\n\tif (der.peek() === Local(3)) {\n\t\tder.readSequence(Local(3));\n\t\tvar extEnd = der.offset + der.length;\n\t\tder.readSequence();\n\n\t\twhile (der.offset < extEnd)\n\t\t\treadExtension(cert, buf, der);\n\n\t\tassert.strictEqual(der.offset, extEnd);\n\t}\n\n\tassert.strictEqual(der.offset, sigOffset);\n\n\tder.readSequence();\n\tafter = der.offset + der.length;\n\tvar sigAlgOid = der.readOID();\n\tvar sigAlg = SIGN_ALGS[sigAlgOid];\n\tif (sigAlg === undefined)\n\t\tthrow (new Error('unknown signature algorithm ' + sigAlgOid));\n\tder._offset = after;\n\n\tvar sigData = der.readString(asn1.Ber.BitString, true);\n\tif (sigData[0] === 0)\n\t\tsigData = sigData.slice(1);\n\tvar algParts = sigAlg.split('-');\n\n\tsig.signature = Signature.parse(sigData, algParts[0], 'asn1');\n\tsig.signature.hashAlgorithm = algParts[1];\n\tsig.algo = sigAlg;\n\tsig.cache = buf.slice(tbsStart, tbsEnd);\n\n\treturn (new Certificate(cert));\n}\n\nfunction readDate(der) {\n\tif (der.peek() === asn1.Ber.UTCTime) {\n\t\treturn (utcTimeToDate(der.readString(asn1.Ber.UTCTime)));\n\t} else if (der.peek() === asn1.Ber.GeneralizedTime) {\n\t\treturn (gTimeToDate(der.readString(asn1.Ber.GeneralizedTime)));\n\t} else {\n\t\tthrow (new Error('Unsupported date format'));\n\t}\n}\n\nfunction writeDate(der, date) {\n\tif (date.getUTCFullYear() >= 2050 || date.getUTCFullYear() < 1950) {\n\t\tder.writeString(dateToGTime(date), asn1.Ber.GeneralizedTime);\n\t} else {\n\t\tder.writeString(dateToUTCTime(date), asn1.Ber.UTCTime);\n\t}\n}\n\n/* RFC5280, section ******* (GeneralName type) */\nvar ALTNAME = {\n\tOtherName: Local(0),\n\tRFC822Name: Context(1),\n\tDNSName: Context(2),\n\tX400Address: Local(3),\n\tDirectoryName: Local(4),\n\tEDIPartyName: Local(5),\n\tURI: Context(6),\n\tIPAddress: Context(7),\n\tOID: Context(8)\n};\n\n/* RFC5280, section ******** (KeyPurposeId) */\nvar EXTPURPOSE = {\n\t'serverAuth': '*******.*******.1',\n\t'clientAuth': '*******.*******.2',\n\t'codeSigning': '*******.*******.3',\n\n\t/* See https://github.com/joyent/oid-docs/blob/master/root.md */\n\t'joyentDocker': '*******.4.1.38678.1.4.1',\n\t'joyentCmon': '*******.4.1.38678.1.4.2'\n};\nvar EXTPURPOSE_REV = {};\nObject.keys(EXTPURPOSE).forEach(function (k) {\n\tEXTPURPOSE_REV[EXTPURPOSE[k]] = k;\n});\n\nvar KEYUSEBITS = [\n\t'signature', 'identity', 'keyEncryption',\n\t'encryption', 'keyAgreement', 'ca', 'crl'\n];\n\nfunction readExtension(cert, buf, der) {\n\tder.readSequence();\n\tvar after = der.offset + der.length;\n\tvar extId = der.readOID();\n\tvar id;\n\tvar sig = cert.signatures.x509;\n\tif (!sig.extras.exts)\n\t\tsig.extras.exts = [];\n\n\tvar critical;\n\tif (der.peek() === asn1.Ber.Boolean)\n\t\tcritical = der.readBoolean();\n\n\tswitch (extId) {\n\tcase (EXTS.basicConstraints):\n\t\tder.readSequence(asn1.Ber.OctetString);\n\t\tder.readSequence();\n\t\tvar bcEnd = der.offset + der.length;\n\t\tvar ca = false;\n\t\tif (der.peek() === asn1.Ber.Boolean)\n\t\t\tca = der.readBoolean();\n\t\tif (cert.purposes === undefined)\n\t\t\tcert.purposes = [];\n\t\tif (ca === true)\n\t\t\tcert.purposes.push('ca');\n\t\tvar bc = { oid: extId, critical: critical };\n\t\tif (der.offset < bcEnd && der.peek() === asn1.Ber.Integer)\n\t\t\tbc.pathLen = der.readInt();\n\t\tsig.extras.exts.push(bc);\n\t\tbreak;\n\tcase (EXTS.extKeyUsage):\n\t\tder.readSequence(asn1.Ber.OctetString);\n\t\tder.readSequence();\n\t\tif (cert.purposes === undefined)\n\t\t\tcert.purposes = [];\n\t\tvar ekEnd = der.offset + der.length;\n\t\twhile (der.offset < ekEnd) {\n\t\t\tvar oid = der.readOID();\n\t\t\tcert.purposes.push(EXTPURPOSE_REV[oid] || oid);\n\t\t}\n\t\t/*\n\t\t * This is a bit of a hack: in the case where we have a cert\n\t\t * that's only allowed to do serverAuth or clientAuth (and not\n\t\t * the other), we want to make sure all our Subjects are of\n\t\t * the right type. But we already parsed our Subjects and\n\t\t * decided if they were hosts or users earlier (since it appears\n\t\t * first in the cert).\n\t\t *\n\t\t * So we go through and mutate them into the right kind here if\n\t\t * it doesn't match. This might not be hugely beneficial, as it\n\t\t * seems that single-purpose certs are not often seen in the\n\t\t * wild.\n\t\t */\n\t\tif (cert.purposes.indexOf('serverAuth') !== -1 &&\n\t\t    cert.purposes.indexOf('clientAuth') === -1) {\n\t\t\tcert.subjects.forEach(function (ide) {\n\t\t\t\tif (ide.type !== 'host') {\n\t\t\t\t\tide.type = 'host';\n\t\t\t\t\tide.hostname = ide.uid ||\n\t\t\t\t\t    ide.email ||\n\t\t\t\t\t    ide.components[0].value;\n\t\t\t\t}\n\t\t\t});\n\t\t} else if (cert.purposes.indexOf('clientAuth') !== -1 &&\n\t\t    cert.purposes.indexOf('serverAuth') === -1) {\n\t\t\tcert.subjects.forEach(function (ide) {\n\t\t\t\tif (ide.type !== 'user') {\n\t\t\t\t\tide.type = 'user';\n\t\t\t\t\tide.uid = ide.hostname ||\n\t\t\t\t\t    ide.email ||\n\t\t\t\t\t    ide.components[0].value;\n\t\t\t\t}\n\t\t\t});\n\t\t}\n\t\tsig.extras.exts.push({ oid: extId, critical: critical });\n\t\tbreak;\n\tcase (EXTS.keyUsage):\n\t\tder.readSequence(asn1.Ber.OctetString);\n\t\tvar bits = der.readString(asn1.Ber.BitString, true);\n\t\tvar setBits = readBitField(bits, KEYUSEBITS);\n\t\tsetBits.forEach(function (bit) {\n\t\t\tif (cert.purposes === undefined)\n\t\t\t\tcert.purposes = [];\n\t\t\tif (cert.purposes.indexOf(bit) === -1)\n\t\t\t\tcert.purposes.push(bit);\n\t\t});\n\t\tsig.extras.exts.push({ oid: extId, critical: critical,\n\t\t    bits: bits });\n\t\tbreak;\n\tcase (EXTS.altName):\n\t\tder.readSequence(asn1.Ber.OctetString);\n\t\tder.readSequence();\n\t\tvar aeEnd = der.offset + der.length;\n\t\twhile (der.offset < aeEnd) {\n\t\t\tswitch (der.peek()) {\n\t\t\tcase ALTNAME.OtherName:\n\t\t\tcase ALTNAME.EDIPartyName:\n\t\t\t\tder.readSequence();\n\t\t\t\tder._offset += der.length;\n\t\t\t\tbreak;\n\t\t\tcase ALTNAME.OID:\n\t\t\t\tder.readOID(ALTNAME.OID);\n\t\t\t\tbreak;\n\t\t\tcase ALTNAME.RFC822Name:\n\t\t\t\t/* RFC822 specifies email addresses */\n\t\t\t\tvar email = der.readString(ALTNAME.RFC822Name);\n\t\t\t\tid = Identity.forEmail(email);\n\t\t\t\tif (!cert.subjects[0].equals(id))\n\t\t\t\t\tcert.subjects.push(id);\n\t\t\t\tbreak;\n\t\t\tcase ALTNAME.DirectoryName:\n\t\t\t\tder.readSequence(ALTNAME.DirectoryName);\n\t\t\t\tid = Identity.parseAsn1(der);\n\t\t\t\tif (!cert.subjects[0].equals(id))\n\t\t\t\t\tcert.subjects.push(id);\n\t\t\t\tbreak;\n\t\t\tcase ALTNAME.DNSName:\n\t\t\t\tvar host = der.readString(\n\t\t\t\t    ALTNAME.DNSName);\n\t\t\t\tid = Identity.forHost(host);\n\t\t\t\tif (!cert.subjects[0].equals(id))\n\t\t\t\t\tcert.subjects.push(id);\n\t\t\t\tbreak;\n\t\t\tdefault:\n\t\t\t\tder.readString(der.peek());\n\t\t\t\tbreak;\n\t\t\t}\n\t\t}\n\t\tsig.extras.exts.push({ oid: extId, critical: critical });\n\t\tbreak;\n\tdefault:\n\t\tsig.extras.exts.push({\n\t\t\toid: extId,\n\t\t\tcritical: critical,\n\t\t\tdata: der.readString(asn1.Ber.OctetString, true)\n\t\t});\n\t\tbreak;\n\t}\n\n\tder._offset = after;\n}\n\nvar UTCTIME_RE =\n    /^([0-9]{2})([0-9]{2})([0-9]{2})([0-9]{2})([0-9]{2})([0-9]{2})?Z$/;\nfunction utcTimeToDate(t) {\n\tvar m = t.match(UTCTIME_RE);\n\tassert.ok(m, 'timestamps must be in UTC');\n\tvar d = new Date();\n\n\tvar thisYear = d.getUTCFullYear();\n\tvar century = Math.floor(thisYear / 100) * 100;\n\n\tvar year = parseInt(m[1], 10);\n\tif (thisYear % 100 < 50 && year >= 60)\n\t\tyear += (century - 1);\n\telse\n\t\tyear += century;\n\td.setUTCFullYear(year, parseInt(m[2], 10) - 1, parseInt(m[3], 10));\n\td.setUTCHours(parseInt(m[4], 10), parseInt(m[5], 10));\n\tif (m[6] && m[6].length > 0)\n\t\td.setUTCSeconds(parseInt(m[6], 10));\n\treturn (d);\n}\n\nvar GTIME_RE =\n    /^([0-9]{4})([0-9]{2})([0-9]{2})([0-9]{2})([0-9]{2})([0-9]{2})?Z$/;\nfunction gTimeToDate(t) {\n\tvar m = t.match(GTIME_RE);\n\tassert.ok(m);\n\tvar d = new Date();\n\n\td.setUTCFullYear(parseInt(m[1], 10), parseInt(m[2], 10) - 1,\n\t    parseInt(m[3], 10));\n\td.setUTCHours(parseInt(m[4], 10), parseInt(m[5], 10));\n\tif (m[6] && m[6].length > 0)\n\t\td.setUTCSeconds(parseInt(m[6], 10));\n\treturn (d);\n}\n\nfunction zeroPad(n, m) {\n\tif (m === undefined)\n\t\tm = 2;\n\tvar s = '' + n;\n\twhile (s.length < m)\n\t\ts = '0' + s;\n\treturn (s);\n}\n\nfunction dateToUTCTime(d) {\n\tvar s = '';\n\ts += zeroPad(d.getUTCFullYear() % 100);\n\ts += zeroPad(d.getUTCMonth() + 1);\n\ts += zeroPad(d.getUTCDate());\n\ts += zeroPad(d.getUTCHours());\n\ts += zeroPad(d.getUTCMinutes());\n\ts += zeroPad(d.getUTCSeconds());\n\ts += 'Z';\n\treturn (s);\n}\n\nfunction dateToGTime(d) {\n\tvar s = '';\n\ts += zeroPad(d.getUTCFullYear(), 4);\n\ts += zeroPad(d.getUTCMonth() + 1);\n\ts += zeroPad(d.getUTCDate());\n\ts += zeroPad(d.getUTCHours());\n\ts += zeroPad(d.getUTCMinutes());\n\ts += zeroPad(d.getUTCSeconds());\n\ts += 'Z';\n\treturn (s);\n}\n\nfunction sign(cert, key) {\n\tif (cert.signatures.x509 === undefined)\n\t\tcert.signatures.x509 = {};\n\tvar sig = cert.signatures.x509;\n\n\tsig.algo = key.type + '-' + key.defaultHashAlgorithm();\n\tif (SIGN_ALGS[sig.algo] === undefined)\n\t\treturn (false);\n\n\tvar der = new asn1.BerWriter();\n\twriteTBSCert(cert, der);\n\tvar blob = der.buffer;\n\tsig.cache = blob;\n\n\tvar signer = key.createSign();\n\tsigner.write(blob);\n\tcert.signatures.x509.signature = signer.sign();\n\n\treturn (true);\n}\n\nfunction signAsync(cert, signer, done) {\n\tif (cert.signatures.x509 === undefined)\n\t\tcert.signatures.x509 = {};\n\tvar sig = cert.signatures.x509;\n\n\tvar der = new asn1.BerWriter();\n\twriteTBSCert(cert, der);\n\tvar blob = der.buffer;\n\tsig.cache = blob;\n\n\tsigner(blob, function (err, signature) {\n\t\tif (err) {\n\t\t\tdone(err);\n\t\t\treturn;\n\t\t}\n\t\tsig.algo = signature.type + '-' + signature.hashAlgorithm;\n\t\tif (SIGN_ALGS[sig.algo] === undefined) {\n\t\t\tdone(new Error('Invalid signing algorithm \"' +\n\t\t\t    sig.algo + '\"'));\n\t\t\treturn;\n\t\t}\n\t\tsig.signature = signature;\n\t\tdone();\n\t});\n}\n\nfunction write(cert, options) {\n\tvar sig = cert.signatures.x509;\n\tassert.object(sig, 'x509 signature');\n\n\tvar der = new asn1.BerWriter();\n\tder.startSequence();\n\tif (sig.cache) {\n\t\tder._ensure(sig.cache.length);\n\t\tsig.cache.copy(der._buf, der._offset);\n\t\tder._offset += sig.cache.length;\n\t} else {\n\t\twriteTBSCert(cert, der);\n\t}\n\n\tder.startSequence();\n\tder.writeOID(SIGN_ALGS[sig.algo]);\n\tif (sig.algo.match(/^rsa-/))\n\t\tder.writeNull();\n\tder.endSequence();\n\n\tvar sigData = sig.signature.toBuffer('asn1');\n\tvar data = Buffer.alloc(sigData.length + 1);\n\tdata[0] = 0;\n\tsigData.copy(data, 1);\n\tder.writeBuffer(data, asn1.Ber.BitString);\n\tder.endSequence();\n\n\treturn (der.buffer);\n}\n\nfunction writeTBSCert(cert, der) {\n\tvar sig = cert.signatures.x509;\n\tassert.object(sig, 'x509 signature');\n\n\tder.startSequence();\n\n\tder.startSequence(Local(0));\n\tder.writeInt(2);\n\tder.endSequence();\n\n\tder.writeBuffer(utils.mpNormalize(cert.serial), asn1.Ber.Integer);\n\n\tder.startSequence();\n\tder.writeOID(SIGN_ALGS[sig.algo]);\n\tif (sig.algo.match(/^rsa-/))\n\t\tder.writeNull();\n\tder.endSequence();\n\n\tcert.issuer.toAsn1(der);\n\n\tder.startSequence();\n\twriteDate(der, cert.validFrom);\n\twriteDate(der, cert.validUntil);\n\tder.endSequence();\n\n\tvar subject = cert.subjects[0];\n\tvar altNames = cert.subjects.slice(1);\n\tsubject.toAsn1(der);\n\n\tpkcs8.writePkcs8(der, cert.subjectKey);\n\n\tif (sig.extras && sig.extras.issuerUniqueID) {\n\t\tder.writeBuffer(sig.extras.issuerUniqueID, Local(1));\n\t}\n\n\tif (sig.extras && sig.extras.subjectUniqueID) {\n\t\tder.writeBuffer(sig.extras.subjectUniqueID, Local(2));\n\t}\n\n\tif (altNames.length > 0 || subject.type === 'host' ||\n\t    (cert.purposes !== undefined && cert.purposes.length > 0) ||\n\t    (sig.extras && sig.extras.exts)) {\n\t\tder.startSequence(Local(3));\n\t\tder.startSequence();\n\n\t\tvar exts = [];\n\t\tif (cert.purposes !== undefined && cert.purposes.length > 0) {\n\t\t\texts.push({\n\t\t\t\toid: EXTS.basicConstraints,\n\t\t\t\tcritical: true\n\t\t\t});\n\t\t\texts.push({\n\t\t\t\toid: EXTS.keyUsage,\n\t\t\t\tcritical: true\n\t\t\t});\n\t\t\texts.push({\n\t\t\t\toid: EXTS.extKeyUsage,\n\t\t\t\tcritical: true\n\t\t\t});\n\t\t}\n\t\texts.push({ oid: EXTS.altName });\n\t\tif (sig.extras && sig.extras.exts)\n\t\t\texts = sig.extras.exts;\n\n\t\tfor (var i = 0; i < exts.length; ++i) {\n\t\t\tder.startSequence();\n\t\t\tder.writeOID(exts[i].oid);\n\n\t\t\tif (exts[i].critical !== undefined)\n\t\t\t\tder.writeBoolean(exts[i].critical);\n\n\t\t\tif (exts[i].oid === EXTS.altName) {\n\t\t\t\tder.startSequence(asn1.Ber.OctetString);\n\t\t\t\tder.startSequence();\n\t\t\t\tif (subject.type === 'host') {\n\t\t\t\t\tder.writeString(subject.hostname,\n\t\t\t\t\t    Context(2));\n\t\t\t\t}\n\t\t\t\tfor (var j = 0; j < altNames.length; ++j) {\n\t\t\t\t\tif (altNames[j].type === 'host') {\n\t\t\t\t\t\tder.writeString(\n\t\t\t\t\t\t    altNames[j].hostname,\n\t\t\t\t\t\t    ALTNAME.DNSName);\n\t\t\t\t\t} else if (altNames[j].type ===\n\t\t\t\t\t    'email') {\n\t\t\t\t\t\tder.writeString(\n\t\t\t\t\t\t    altNames[j].email,\n\t\t\t\t\t\t    ALTNAME.RFC822Name);\n\t\t\t\t\t} else {\n\t\t\t\t\t\t/*\n\t\t\t\t\t\t * Encode anything else as a\n\t\t\t\t\t\t * DN style name for now.\n\t\t\t\t\t\t */\n\t\t\t\t\t\tder.startSequence(\n\t\t\t\t\t\t    ALTNAME.DirectoryName);\n\t\t\t\t\t\taltNames[j].toAsn1(der);\n\t\t\t\t\t\tder.endSequence();\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\tder.endSequence();\n\t\t\t\tder.endSequence();\n\t\t\t} else if (exts[i].oid === EXTS.basicConstraints) {\n\t\t\t\tder.startSequence(asn1.Ber.OctetString);\n\t\t\t\tder.startSequence();\n\t\t\t\tvar ca = (cert.purposes.indexOf('ca') !== -1);\n\t\t\t\tvar pathLen = exts[i].pathLen;\n\t\t\t\tder.writeBoolean(ca);\n\t\t\t\tif (pathLen !== undefined)\n\t\t\t\t\tder.writeInt(pathLen);\n\t\t\t\tder.endSequence();\n\t\t\t\tder.endSequence();\n\t\t\t} else if (exts[i].oid === EXTS.extKeyUsage) {\n\t\t\t\tder.startSequence(asn1.Ber.OctetString);\n\t\t\t\tder.startSequence();\n\t\t\t\tcert.purposes.forEach(function (purpose) {\n\t\t\t\t\tif (purpose === 'ca')\n\t\t\t\t\t\treturn;\n\t\t\t\t\tif (KEYUSEBITS.indexOf(purpose) !== -1)\n\t\t\t\t\t\treturn;\n\t\t\t\t\tvar oid = purpose;\n\t\t\t\t\tif (EXTPURPOSE[purpose] !== undefined)\n\t\t\t\t\t\toid = EXTPURPOSE[purpose];\n\t\t\t\t\tder.writeOID(oid);\n\t\t\t\t});\n\t\t\t\tder.endSequence();\n\t\t\t\tder.endSequence();\n\t\t\t} else if (exts[i].oid === EXTS.keyUsage) {\n\t\t\t\tder.startSequence(asn1.Ber.OctetString);\n\t\t\t\t/*\n\t\t\t\t * If we parsed this certificate from a byte\n\t\t\t\t * stream (i.e. we didn't generate it in sshpk)\n\t\t\t\t * then we'll have a \".bits\" property on the\n\t\t\t\t * ext with the original raw byte contents.\n\t\t\t\t *\n\t\t\t\t * If we have this, use it here instead of\n\t\t\t\t * regenerating it. This guarantees we output\n\t\t\t\t * the same data we parsed, so signatures still\n\t\t\t\t * validate.\n\t\t\t\t */\n\t\t\t\tif (exts[i].bits !== undefined) {\n\t\t\t\t\tder.writeBuffer(exts[i].bits,\n\t\t\t\t\t    asn1.Ber.BitString);\n\t\t\t\t} else {\n\t\t\t\t\tvar bits = writeBitField(cert.purposes,\n\t\t\t\t\t    KEYUSEBITS);\n\t\t\t\t\tder.writeBuffer(bits,\n\t\t\t\t\t    asn1.Ber.BitString);\n\t\t\t\t}\n\t\t\t\tder.endSequence();\n\t\t\t} else {\n\t\t\t\tder.writeBuffer(exts[i].data,\n\t\t\t\t    asn1.Ber.OctetString);\n\t\t\t}\n\n\t\t\tder.endSequence();\n\t\t}\n\n\t\tder.endSequence();\n\t\tder.endSequence();\n\t}\n\n\tder.endSequence();\n}\n\n/*\n * Reads an ASN.1 BER bitfield out of the Buffer produced by doing\n * `BerReader#readString(asn1.Ber.BitString)`. That function gives us the raw\n * contents of the BitString tag, which is a count of unused bits followed by\n * the bits as a right-padded byte string.\n *\n * `bits` is the Buffer, `bitIndex` should contain an array of string names\n * for the bits in the string, ordered starting with bit #0 in the ASN.1 spec.\n *\n * Returns an array of Strings, the names of the bits that were set to 1.\n */\nfunction readBitField(bits, bitIndex) {\n\tvar bitLen = 8 * (bits.length - 1) - bits[0];\n\tvar setBits = {};\n\tfor (var i = 0; i < bitLen; ++i) {\n\t\tvar byteN = 1 + Math.floor(i / 8);\n\t\tvar bit = 7 - (i % 8);\n\t\tvar mask = 1 << bit;\n\t\tvar bitVal = ((bits[byteN] & mask) !== 0);\n\t\tvar name = bitIndex[i];\n\t\tif (bitVal && typeof (name) === 'string') {\n\t\t\tsetBits[name] = true;\n\t\t}\n\t}\n\treturn (Object.keys(setBits));\n}\n\n/*\n * `setBits` is an array of strings, containing the names for each bit that\n * sould be set to 1. `bitIndex` is same as in `readBitField()`.\n *\n * Returns a Buffer, ready to be written out with `BerWriter#writeString()`.\n */\nfunction writeBitField(setBits, bitIndex) {\n\tvar bitLen = bitIndex.length;\n\tvar blen = Math.ceil(bitLen / 8);\n\tvar unused = blen * 8 - bitLen;\n\tvar bits = Buffer.alloc(1 + blen); // zero-filled\n\tbits[0] = unused;\n\tfor (var i = 0; i < bitLen; ++i) {\n\t\tvar byteN = 1 + Math.floor(i / 8);\n\t\tvar bit = 7 - (i % 8);\n\t\tvar mask = 1 << bit;\n\t\tvar name = bitIndex[i];\n\t\tif (name === undefined)\n\t\t\tcontinue;\n\t\tvar bitVal = (setBits.indexOf(name) !== -1);\n\t\tif (bitVal) {\n\t\t\tbits[byteN] |= mask;\n\t\t}\n\t}\n\treturn (bits);\n}\n", "// Copyright 2016 Joyent, Inc.\n\nvar x509 = require('./x509');\n\nmodule.exports = {\n\tread: read,\n\tverify: x509.verify,\n\tsign: x509.sign,\n\twrite: write\n};\n\nvar assert = require('assert-plus');\nvar asn1 = require('asn1');\nvar Buffer = require('safer-buffer').Buffer;\nvar algs = require('../algs');\nvar utils = require('../utils');\nvar Key = require('../key');\nvar PrivateKey = require('../private-key');\nvar pem = require('./pem');\nvar Identity = require('../identity');\nvar Signature = require('../signature');\nvar Certificate = require('../certificate');\n\nfunction read(buf, options) {\n\tif (typeof (buf) !== 'string') {\n\t\tassert.buffer(buf, 'buf');\n\t\tbuf = buf.toString('ascii');\n\t}\n\n\tvar lines = buf.trim().split(/[\\r\\n]+/g);\n\n\tvar m;\n\tvar si = -1;\n\twhile (!m && si < lines.length) {\n\t\tm = lines[++si].match(/*JSSTYLED*/\n\t\t    /[-]+[ ]*BEGIN CERTIFICATE[ ]*[-]+/);\n\t}\n\tassert.ok(m, 'invalid PEM header');\n\n\tvar m2;\n\tvar ei = lines.length;\n\twhile (!m2 && ei > 0) {\n\t\tm2 = lines[--ei].match(/*JSSTYLED*/\n\t\t    /[-]+[ ]*END CERTIFICATE[ ]*[-]+/);\n\t}\n\tassert.ok(m2, 'invalid PEM footer');\n\n\tlines = lines.slice(si, ei + 1);\n\n\tvar headers = {};\n\twhile (true) {\n\t\tlines = lines.slice(1);\n\t\tm = lines[0].match(/*JSSTYLED*/\n\t\t    /^([A-Za-z0-9-]+): (.+)$/);\n\t\tif (!m)\n\t\t\tbreak;\n\t\theaders[m[1].toLowerCase()] = m[2];\n\t}\n\n\t/* Chop off the first and last lines */\n\tlines = lines.slice(0, -1).join('');\n\tbuf = Buffer.from(lines, 'base64');\n\n\treturn (x509.read(buf, options));\n}\n\nfunction write(cert, options) {\n\tvar dbuf = x509.write(cert, options);\n\n\tvar header = 'CERTIFICATE';\n\tvar tmp = dbuf.toString('base64');\n\tvar len = tmp.length + (tmp.length / 64) +\n\t    18 + 16 + header.length*2 + 10;\n\tvar buf = Buffer.alloc(len);\n\tvar o = 0;\n\to += buf.write('-----BEGIN ' + header + '-----\\n', o);\n\tfor (var i = 0; i < tmp.length; ) {\n\t\tvar limit = i + 64;\n\t\tif (limit > tmp.length)\n\t\t\tlimit = tmp.length;\n\t\to += buf.write(tmp.slice(i, limit), o);\n\t\tbuf[o++] = 10;\n\t\ti = limit;\n\t}\n\to += buf.write('-----END ' + header + '-----\\n', o);\n\n\treturn (buf.slice(0, o));\n}\n"]}