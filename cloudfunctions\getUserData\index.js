// 云函数入口文件
const cloud = require('wx-server-sdk')

// 固定的环境ID，用于出错时的备用方案
const FIXED_ENV_ID = 'wlksapp_4g54hauu5cbf43dc';
// 完整的环境ID，包含存储空间标识
const FULL_ENV_ID = 'wlksapp_4g54hauu5cbf43dc.776c_wlksapp_4g54hauu5cbf43dc_1329876191';

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})

// 定义常量
const USERS_PATH = 'users';

// 确保路径使用下划线格式
function ensureUnderscoreFormat(path) {
  if (!path) return path;
  // 将所有连字符替换为下划线
  return path.replace(/-/g, '_');
}

// 确保文件ID包含正确的环境信息
function ensureFileIDWithEnv(fileID) {
  if (!fileID) return fileID;
  
  // 检查是否是URL格式
  if (fileID.startsWith('http://') || fileID.startsWith('https://')) {
    console.log('文件ID已经是URL格式，直接返回:', fileID);
    return fileID;
  }
  
  // 检查文件ID是否已经包含环境信息
  if (fileID.startsWith('cloud://')) {
    const envEndIndex = fileID.indexOf('.', 8); // 跳过'cloud://'前缀
    const pathStartIndex = fileID.indexOf('/', 8);
    
    // 如果文件ID中已有完整环境信息(包含点号)，直接返回
    if (envEndIndex !== -1 && envEndIndex < pathStartIndex) {
      console.log('文件ID已包含完整环境信息:', fileID);
      return fileID;
    }
    
    // 如果只有简单环境ID，没有存储空间ID，添加完整环境ID
    if (pathStartIndex !== -1) {
      // 提取纯路径部分
      const purePath = fileID.substring(pathStartIndex);
      // 使用完整环境ID
      const result = `cloud://${FULL_ENV_ID}${purePath}`;
      console.log('添加完整环境ID后:', result);
      return result;
    }
  }
  
  // 不是标准格式的文件ID，尝试构建一个标准格式
  if (fileID.startsWith('/')) {
    const result = `cloud://${FULL_ENV_ID}${fileID}`;
    console.log('构建云文件ID:', result);
    return result;
  } else if (!fileID.startsWith('cloud://')) {
    // 如果不是以cloud://开头，也不是以/开头，尝试添加/前缀
    const result = `cloud://${FULL_ENV_ID}/${fileID}`;
    console.log('构建完整云文件ID:', result);
    return result;
  }
  
  // 无法处理的情况，返回原始ID
  console.log('无法处理的文件ID格式，保持原样:', fileID);
  return fileID;
}

// 新增：从云存储文件ID生成直接可访问的下载URL
async function generateDirectDownloadUrl(fileID) {
  try {
    if (!fileID) return null;
    
    // 检查是否是视频文件
    const isVideo = fileID.toLowerCase().includes('.mp4') || 
                     fileID.toLowerCase().includes('/video') || 
                     fileID.includes('/videos/');
    
    // 已经是URL格式则直接返回
    if (fileID.startsWith('http://') || fileID.startsWith('https://')) {
      // 确保URL中的下划线被正确替换为连字符
      let url = fileID.replace(/wlksapp_4g54hauu5cbf43dc/g, 'wlksapp-4g54hauu5cbf43dc');
      
      // 对于视频确保有时间戳参数
      if (isVideo && !url.includes('?')) {
        const timestamp = Date.now();
        url = `${url}?t=${timestamp}`;
      }
      
      return url;
    }
    
    // 检查是否是简化的视频路径
    const isSimplifiedPath = fileID.includes('/videos/') && !fileID.includes('/users/');
    if (isSimplifiedPath) {
      console.log('检测到简化的视频路径，尝试推断完整路径');
      
      // 从调用上下文中获取openid
      const wxContext = cloud.getWXContext();
      const openid = ensureUnderscoreFormat(wxContext.OPENID);
      
      // 从文件路径中提取文件名
      const fileName = fileID.split('/').pop();
      
      if (fileName) {
        // 尝试从数据库中查找包含该视频的记录
        const db = cloud.database();
        try {
          // 使用正则表达式匹配文件名
          const videoMatch = await db.collection('analysis_index')
            .where({
              _openid: openid,
              videoPath: db.RegExp({
                regexp: fileName,
                options: 'i'
              })
            })
            .limit(1)
            .get();
          
          if (videoMatch.data && videoMatch.data.length > 0) {
            // 找到匹配的记录，使用数据库中存储的完整路径
            fileID = videoMatch.data[0].videoPath;
            console.log('使用数据库中找到的完整视频路径:', fileID);
          } else {
            // 如果没有找到记录，尝试构建一个可能的路径
            // 查找用户的最新会话记录
            const latestSession = await db.collection('analysis_index')
              .where({
                _openid: openid
              })
              .orderBy('createTime', 'desc')
              .limit(1)
              .get();
              
            if (latestSession.data && latestSession.data.length > 0) {
              const sessionId = latestSession.data[0].sessionId;
              const userPath = `users/${openid}_data/${sessionId}`;
              // 构建可能的完整路径
              fileID = `${userPath}/videos/${fileName}`;
              console.log('构建的可能完整路径:', fileID);
            }
          }
        } catch (dbError) {
          console.error('查询数据库获取完整路径失败:', dbError);
        }
      }
    }
    
    // 确保文件ID格式正确
    const formattedFileID = ensureFileIDWithEnv(fileID);
    
    // 通过getTempFileURL获取带签名的直接下载链接
    try {
      console.log('尝试获取带签名的下载链接:', formattedFileID);
      const result = await cloud.getTempFileURL({
        fileList: [formattedFileID]
      });
      
      if (result.fileList && result.fileList.length > 0 && 
          result.fileList[0].status === 0 && result.fileList[0].tempFileURL) {
        // 确保URL中的下划线被正确替换为连字符
        let tempFileURL = result.fileList[0].tempFileURL.replace(/wlksapp_4g54hauu5cbf43dc/g, 'wlksapp-4g54hauu5cbf43dc');
        
        // 移除所有URL中的时间戳参数，保持与图片URL格式一致
        tempFileURL = tempFileURL.split('?')[0];
        
        console.log('成功获取下载链接:', tempFileURL);
        return tempFileURL;
      }
    } catch (tempUrlError) {
      console.error('获取带签名的下载链接失败:', tempUrlError);
      // 获取失败时，继续使用无签名的直接链接方式
    }
    
    // 从cloud://格式的fileID提取有效路径
    let filePath = '';
    if (fileID.startsWith('cloud://')) {
      // 跳过cloud://前缀和环境ID部分
      const pathStartIndex = fileID.indexOf('/', 8);
      if (pathStartIndex === -1) return null;
      
      filePath = fileID.substring(pathStartIndex + 1); // +1 跳过斜杠
    } else if (fileID.startsWith('/')) {
      // 如果以/开头，去除开头的斜杠
      filePath = fileID.substring(1);
    } else {
      // 其他情况直接使用
      filePath = fileID;
    }
    
    // 根据云环境ID构建无签名的直接下载链接（作为备选方案）
    // 从FULL_ENV_ID中提取环境ID的第一部分
    const envId = FULL_ENV_ID.split('.')[0];
    
    // 构建无签名的URL，确保环境ID中的下划线被替换为连字符
    const normalizedEnvId = envId.replace(/_/g, '-');
    const downloadUrl = `https://776c-${normalizedEnvId}-1329876191.tcb.qcloud.la/${filePath}`;
    console.log('生成无签名的直接下载链接:', downloadUrl);
    
    // 为视频添加时间戳参数
    if (isVideo) {
      const timestamp = Date.now();
      return `${downloadUrl}?t=${timestamp}`;
    }
    
    return downloadUrl;
    
    // 获取文件有效链接的最后尝试：使用云函数downloadFile请求下载后重新上传到云存储
    try {
      const downloadResult = await cloud.downloadFile({
        fileID: formattedFileID
      });
      
      if (downloadResult && downloadResult.fileContent) {
        // 文件存在且能正常下载，但无法获取临时URL
        console.log('文件能够下载但无法获取临时URL，需要用户使用无签名链接或base64方式查看');
      }
    } catch (downloadError) {
      console.error('下载文件也失败，确认文件可能不存在:', downloadError);
      return null;
    }
    
    return downloadUrl;
  } catch (error) {
    console.error('生成直接下载链接失败:', error);
    return null;
  }
}

// 下载文件内容
async function downloadFile(fileID) {
  try {
    // 确保文件ID格式正确
    const formattedFileID = ensureFileIDWithEnv(fileID);
    console.log('开始下载文件:', formattedFileID);
    
    const result = await cloud.downloadFile({
      fileID: formattedFileID
    });
    
    if (result && result.fileContent) {
      console.log('文件下载成功，大小:', result.fileContent.length, '字节');
      return result.fileContent.toString('utf8');
    } else {
      console.error('文件下载结果无效');
      return null;
    }
  } catch (err) {
    console.error('下载文件失败:', err);
    return null;
  }
}

// 获取临时文件URL
async function getTempFileURLHandler(fileID) {
  if (!fileID) {
    return {
      success: false,
      error: '文件ID不能为空',
      message: '文件ID不能为空'
    };
  }
  
  console.log('请求获取临时URL, 文件ID:', fileID);
  
  try {
    // 检查是否是简化的视频路径，尝试修复
    const isSimplifiedPath = fileID.includes('/videos/') && !fileID.includes('/users/');
    if (isSimplifiedPath) {
      console.log('检测到简化的视频路径格式，尝试推断完整路径');
      
      // 从调用上下文中获取openid
      const wxContext = cloud.getWXContext();
      const openid = ensureUnderscoreFormat(wxContext.OPENID);
      
      // 从文件路径提取视频文件名
      const fileName = fileID.split('/').pop();
      if (fileName) {
        // 尝试从数据库中查找包含该视频的记录
        const db = cloud.database();
        try {
          const videoMatch = await db.collection('analysis_index')
            .where({
              _openid: openid,
              videoPath: db.RegExp({
                regexp: fileName,
                options: 'i'
              })
            })
            .limit(1)
            .get();
          
          if (videoMatch.data && videoMatch.data.length > 0) {
            const record = videoMatch.data[0];
            fileID = record.videoPath; // 使用数据库中存储的完整路径
            console.log('从数据库找到的完整路径:', fileID);
          } else {
            console.log('未在数据库中找到匹配的视频记录');
            
            // 尝试构建一个可能的完整路径作为备选
            // 这种情况下无法确定sessionId，使用用户的最新会话尝试
            const latestSession = await db.collection('analysis_index')
              .where({
                _openid: openid
              })
              .orderBy('createTime', 'desc')
              .limit(1)
              .get();
              
            if (latestSession.data && latestSession.data.length > 0) {
              const sessionId = latestSession.data[0].sessionId;
              const userPath = `users/${openid}_data/${sessionId}`;
              const possiblePath = `${userPath}/videos/${sessionId}_${fileName}`;
              
              console.log('推测的完整视频路径:', possiblePath);
              
              // 尝试检查这个文件是否存在
              try {
                const formattedFileID = ensureFileIDWithEnv(possiblePath);
                const tempResult = await cloud.getTempFileURL({
                  fileList: [formattedFileID]
                });
                
                if (tempResult.fileList && tempResult.fileList.length > 0 && 
                    tempResult.fileList[0].status === 0) {
                  // 文件存在，使用这个路径
                  fileID = possiblePath;
                  console.log('推测的路径验证成功，使用它');
                }
              } catch (checkError) {
                console.error('推测路径检查失败:', checkError);
              }
            }
          }
        } catch (dbError) {
          console.error('查询数据库失败:', dbError);
        }
      }
    }
    
    // 尝试直接生成下载链接 - 优先使用此方法，避免临时URL获取失败
    const directDownloadUrl = await generateDirectDownloadUrl(fileID);
    if (directDownloadUrl) {
      // 确保URL中的下划线被正确替换为连字符
      const normalizedUrl = directDownloadUrl.replace(/wlksapp_4g54hauu5cbf43dc/g, 'wlksapp-4g54hauu5cbf43dc');
      console.log('直接生成下载链接成功，URL规范化后:', normalizedUrl);
      return {
        success: true,
        fileUrl: normalizedUrl,
        fileID: fileID,
        isDirect: true
      };
    }
    
    // 如果直接生成链接失败，再尝试获取临时URL
    // 确保文件ID格式正确
    const formattedFileID = ensureFileIDWithEnv(fileID);
    console.log('格式化后的文件ID:', formattedFileID);
    
    // 在云函数中使用getTempFileURL
    const result = await cloud.getTempFileURL({
      fileList: [formattedFileID]
    });
    
    console.log('获取临时URL结果:', result);
    
    if (result.fileList && result.fileList.length > 0) {
      const fileResult = result.fileList[0];
      
      if (fileResult.status === 0 && fileResult.tempFileURL) {
        // 规范化URL，替换下划线为连字符
        const normalizedUrl = fileResult.tempFileURL.replace(/wlksapp_4g54hauu5cbf43dc/g, 'wlksapp-4g54hauu5cbf43dc');
        console.log('成功获取临时URL:', normalizedUrl);
        return {
          success: true,
          fileUrl: normalizedUrl,
          fileID: formattedFileID
        };
      } else {
        console.error('获取临时URL失败:', fileResult.errMsg || '未知错误', 'status:', fileResult.status);
        
        // 如果是文件不存在错误（-501001），返回默认图片
        if (fileResult.status === -501001) {
          console.log('文件不存在，返回默认图片URL');
          
          // 判断是哪种图片类型，并提供对应的默认图片
          let imageType = 'default';
          if (fileID.includes('c_area')) {
            imageType = 'cAreaImage';
          } else if (fileID.includes('t_area')) {
            imageType = 'tAreaImage';
          } else if (fileID.includes('video')) {
            imageType = 'video';
          }
          
          const defaultImageUrl = getDefaultImageUrl(imageType);
          
          return {
            success: true,
            fileUrl: defaultImageUrl,
            fileID: formattedFileID,
            isDefault: true,
            message: '原始图片不存在，已返回默认图片'
          };
        }
        
        // 如果不是文件不存在错误，尝试再次生成直接链接
        const retryDirectUrl = await generateDirectDownloadUrl(fileID);
        if (retryDirectUrl) {
          console.log('临时URL获取失败，使用直接链接:', retryDirectUrl);
          return {
            success: true,
            fileUrl: retryDirectUrl,
            fileID: fileID,
            isDirect: true,
            message: '临时URL获取失败，已使用直接链接'
          };
        }
        
        return {
          success: false,
          error: `获取临时URL失败: ${fileResult.errMsg || '未知错误'}`,
          message: '无法获取文件访问链接'
        };
      }
    } else {
      console.error('获取临时URL返回为空');
      
      // 再次尝试直接链接
      const fallbackDirectUrl = await generateDirectDownloadUrl(fileID);
      if (fallbackDirectUrl) {
        console.log('临时URL获取为空，使用直接链接:', fallbackDirectUrl);
        return {
          success: true,
          fileUrl: fallbackDirectUrl,
          fileID: fileID,
          isDirect: true,
          message: '临时URL获取为空，已使用直接链接'
        };
      }
      
      return {
        success: false,
        error: '获取临时URL返回为空',
        message: '无法获取文件访问链接'
      };
    }
  } catch (error) {
    console.error('处理获取临时URL请求失败:', error);
    
    // 捕获到异常也尝试直接链接
    const errorFallbackUrl = await generateDirectDownloadUrl(fileID);
    if (errorFallbackUrl) {
      console.log('处理异常，使用直接链接:', errorFallbackUrl);
      return {
        success: true,
        fileUrl: errorFallbackUrl,
        fileID: fileID,
        isDirect: true,
        message: '处理异常，已使用直接链接'
      };
    }
    
    return {
      success: false,
      error: error.message,
      message: '处理获取临时URL请求失败'
    };
  }
}

// 直接下载文件并转换为base64
async function downloadFileAsBase64(fileID) {
  if (!fileID) {
    return {
      success: false,
      error: '文件ID不能为空',
      message: '文件ID不能为空'
    };
  }
  
  console.log('请求下载文件并转换为base64, 文件ID:', fileID);
  
  try {
    // 首先尝试生成直接下载链接
    const directDownloadUrl = await generateDirectDownloadUrl(fileID);
    if (directDownloadUrl) {
      console.log('生成直接下载链接成功:', directDownloadUrl);
      return {
        success: true,
        fileUrl: directDownloadUrl,
        fileID: fileID,
        isDirect: true,
        message: '已生成直接访问链接'
      };
    }
    
    // 如果无法生成直接链接，继续尝试下载为base64
    // 确保文件ID格式正确
    const formattedFileID = ensureFileIDWithEnv(fileID);
    console.log('格式化后的文件ID:', formattedFileID);
    
    try {
      // 下载文件
      const downloadResult = await cloud.downloadFile({
        fileID: formattedFileID
      });
      
      console.log('下载文件结果:', downloadResult);
      
      if (downloadResult && downloadResult.fileContent) {
        // 将文件内容转换为base64
        const base64Content = downloadResult.fileContent.toString('base64');
        console.log('文件成功转换为base64, 大小:', base64Content.length);
        
        // 获取文件类型以构建完整的data URI
        let contentType = 'image/png'; // 默认类型
        if (fileID.toLowerCase().endsWith('.jpg') || fileID.toLowerCase().endsWith('.jpeg')) {
          contentType = 'image/jpeg';
        } else if (fileID.toLowerCase().endsWith('.gif')) {
          contentType = 'image/gif';
        } else if (fileID.toLowerCase().endsWith('.mp4')) {
          contentType = 'video/mp4';
        }
        
        const dataUrl = `data:${contentType};base64,${base64Content}`;
        
        return {
          success: true,
          dataUrl: dataUrl,
          fileID: formattedFileID,
          message: '文件成功下载并转换为base64'
        };
      } else {
        console.error('下载文件结果无效');
        throw new Error('下载文件结果无效');
      }
    } catch (downloadError) {
      console.error('下载文件失败:', downloadError);
      
      // 判断是否是文件不存在错误，如果是则返回默认图片
      if (downloadError.message && (
          downloadError.message.includes('resource not found') || 
          downloadError.message.includes('resource system error') ||
          downloadError.message.includes('-501001'))
      ) {
        console.log('文件不存在，返回默认图片');
        
        // 判断是哪种图片类型，并提供对应的默认图片URL
        let imageType = 'default';
        if (fileID.includes('c_area')) {
          imageType = 'cAreaImage';
        } else if (fileID.includes('t_area')) {
          imageType = 'tAreaImage';
        } else if (fileID.includes('video')) {
          imageType = 'video';
        }
        
        const defaultImageUrl = getDefaultImageUrl(imageType);
        
        // 获取默认图片内容
        try {
          console.log('尝试获取默认图片URL:', defaultImageUrl);
          
          // 返回默认图片的URL，让前端直接加载
          return {
            success: true,
            fileUrl: defaultImageUrl,
            isDefault: true,
            message: '原始图片不存在，已返回默认图片URL'
          };
        } catch (defaultImgError) {
          console.error('获取默认图片也失败:', defaultImgError);
          throw defaultImgError;
        }
      }
      
      // 如果不是资源不存在错误，再次尝试生成直接链接
      const retryDirectUrl = await generateDirectDownloadUrl(fileID);
      if (retryDirectUrl) {
        console.log('下载失败，使用直接链接:', retryDirectUrl);
        return {
          success: true,
          fileUrl: retryDirectUrl,
          fileID: fileID,
          isDirect: true,
          message: '下载失败，已使用直接链接'
        };
      }
      
      throw downloadError;
    }
  } catch (error) {
    console.error('下载文件失败:', error);
    return {
      success: false,
      error: error.message,
      message: '下载文件失败，请稍后重试'
    };
  }
}

// 获取默认图片URL
function getDefaultImageUrl(type) {
  // 使用公共可访问的默认图片URL
  const defaultImages = {
    cAreaImage: "https://636c-cloud1-8g8cixem649d1a27-1315773097.tcb.qcloud.la/default_images/default_c_area.png",
    tAreaImage: "https://636c-cloud1-8g8cixem649d1a27-1315773097.tcb.qcloud.la/default_images/default_t_area.png",
    video: "https://636c-cloud1-8g8cixem649d1a27-1315773097.tcb.qcloud.la/default_images/default_video.png",
    default: "https://636c-cloud1-8g8cixem649d1a27-1315773097.tcb.qcloud.la/default_images/default_image.png"
  };
  
  return defaultImages[type] || defaultImages.default;
}

// 云函数入口函数
exports.main = async (event, context) => {
  try {
    // 获取用户OpenID
    const wxContext = cloud.getWXContext();
    // 确保使用下划线格式的OpenID
    const openid = ensureUnderscoreFormat(wxContext.OPENID);
    console.log('处理用户数据请求, openid:', openid);
    
    // 处理特定媒体文件请求
    if (event.action === 'getMediaFile') {
      return await getMediaFile(openid, event.sessionId, event.mediaType);
    }
    
    // 处理获取临时文件URL的请求
    if (event.action === 'getTempFileURL') {
      return await getTempFileURLHandler(event.fileID);
    }
    
    // 处理直接下载文件为base64的请求
    if (event.action === 'downloadFileAsBase64') {
      return await downloadFileAsBase64(event.fileID);
    }
    
    // 新增：处理获取视频分析结果详细信息的请求
    if (event.action === 'getAnalysisDetails') {
      return await getAnalysisDetails(openid, event.sessionId);
    }
    
    // 处理更新分析类型的请求
    if (event.action === 'updateAnalysisType') {
      return await updateAnalysisType(openid, event.itemId, event.isTemp);
    }
    
    // 初始化数据库
    const db = cloud.database();
    
    // 首先尝试从数据库中获取分析索引数据
    console.log('尝试从analysis_index集合获取数据...');
    let databaseResults = [];
    try {
      const indexResult = await db.collection('analysis_index')
        .where({ _openid: openid })
        .orderBy('createTime', 'desc')
        .get();
      
      console.log(`从数据库获取到 ${indexResult.data.length} 条索引记录`);
      
      if (indexResult.data && indexResult.data.length > 0) {
        databaseResults = indexResult.data.map(item => {
          // 首先保留原始数据中的所有字段
          const result = {
            // 保留所有已有字段
            sessionId: item.sessionId,
            cAreaValue: item.cAreaValue || 0,
            tAreaValue: item.tAreaValue || 0,
            tcValue: item.tcValue || 0,
            concentration: item.concentration || 0,
            analysisTime: item.analysisTime || item.createTime,
            // 确保videoParameters字段也被传递给前端
            videoParameters: item.videoParameters || item.parameters || {
              brightness: 115,
              contrast: 115,
              saturation: 106,
              white_balance_temperature_auto: 0,
              gain: 0,
              power_line_frequency: 2,
              white_balance_temperature: 4650,
              sharpness: 10,
              exposure_auto: 3,
              exposure_absolute: 1250,
              pan_absolute: 0,
              tilt_absolute: 0,
              focus_absolute: 0,
              zoom_absolute: 100,
              setVoltage: 12,
              concentration: '',
              isLocalVideo: true,
              deviceIp: ''
            },
            // 保持向后兼容
            parameters: item.videoParameters || item.parameters || {
              brightness: 115,
              contrast: 115,
              saturation: 106,
              white_balance_temperature_auto: 0,
              gain: 0,
              power_line_frequency: 2,
              white_balance_temperature: 4650,
              sharpness: 10,
              exposure_auto: 3,
              exposure_absolute: 1250,
              pan_absolute: 0,
              tilt_absolute: 0,
              focus_absolute: 0,
              zoom_absolute: 100,
              setVoltage: 12,
              concentration: '',
              isLocalVideo: true,
              deviceIp: ''
            },
            cAreaImage: item.cAreaImagePath ? ensureFileIDWithEnv(item.cAreaImagePath) : null,
            tAreaImage: item.tAreaImagePath ? ensureFileIDWithEnv(item.tAreaImagePath) : null,
            videoFile: item.videoPath ? ensureFileIDWithEnv(item.videoPath) : null,
            cAreaImageUrl: item.cAreaImageUrl || null,
            tAreaImageUrl: item.tAreaImageUrl || null,
            videoUrl: item.videoUrl || null,
            
            // 添加原始数据库记录中的所有其他字段
            createTime: item.createTime,
            updateTime: item.updateTime,
            resultFilePath: item.resultFilePath,
            resultFileUrl: item.resultFileUrl,
            isTemp: item.isTemp !== undefined ? item.isTemp : true,
            
            // 添加数据库记录中的其他任何字段
            ...item
          };
          
          // 记录日志，以便调试
          console.log(`处理会话${item.sessionId}的数据，包含的字段:`, Object.keys(result));
          
          return result;
        });
        
        console.log('从数据库成功获取数据，返回结果');
        return {
          success: true,
          data: databaseResults,
          message: '从数据库获取数据成功',
          source: 'database'
        };
      } else {
        console.log('数据库中无记录，但无法直接列出云存储中的文件');
        return {
          success: true,
          data: [],
          message: '暂无分析数据',
          source: 'database_empty'
        };
      }
    } catch (dbError) {
      console.error('查询数据库失败:', dbError);
      return {
        success: false,
        error: '获取数据失败: ' + dbError.message,
        message: '获取数据失败，请稍后重试'
      };
    }
  } catch (error) {
    console.error('处理请求出错:', error);
    return {
      success: false,
      error: error.message,
      message: '服务异常，请稍后重试'
    };
  }
};

// 获取特定的媒体文件
async function getMediaFile(openid, sessionId, mediaType) {
  console.log(`获取用户 ${openid} 的会话 ${sessionId} 的 ${mediaType} 文件`);
  
  if (!sessionId) {
    return {
      success: false,
      error: '会话ID不能为空',
      message: '会话ID不能为空'
    };
  }
  
  try {
    // 初始化数据库
    const db = cloud.database();
    
    // 从数据库中获取会话信息
    const indexResult = await db.collection('analysis_index')
      .where({
        _openid: openid,
        sessionId: sessionId
      })
      .get();
    
    console.log(`从数据库中查询会话 ${sessionId} 结果:`, indexResult);
    
    if (!indexResult.data || indexResult.data.length === 0) {
      console.log(`未找到会话 ${sessionId} 的数据`);
      return {
        success: false,
        error: '未找到会话数据',
        message: '未找到该会话的相关数据'
      };
    }
    
    // 获取会话数据
    const sessionData = indexResult.data[0];
    
    // 优先检查数据库中是否已有临时URL
    switch (mediaType) {
      case 'video':
        if (sessionData.videoUrl) {
          // 规范化存储的URL，但保留查询参数（对视频播放很重要）
          let normalizedUrl = sessionData.videoUrl.replace(/wlksapp_4g54hauu5cbf43dc/g, 'wlksapp-4g54hauu5cbf43dc');
          
          // 确保URL有时间戳参数，如果没有则添加
          if (!normalizedUrl.includes('?')) {
            const timestamp = Date.now();
            normalizedUrl = `${normalizedUrl}?t=${timestamp}`;
          }
          
          console.log('数据库中已存在视频临时URL，规范化后:', normalizedUrl);
          return {
            success: true,
            fileUrl: normalizedUrl,
            fileID: sessionData.videoPath,
            isFromDatabase: true
          };
        }
        break;
      case 'cAreaImage':
        if (sessionData.cAreaImageUrl) {
          // 规范化存储的URL
          const normalizedUrl = sessionData.cAreaImageUrl.replace(/wlksapp_4g54hauu5cbf43dc/g, 'wlksapp-4g54hauu5cbf43dc');
          console.log('数据库中已存在C区图片临时URL，规范化后:', normalizedUrl);
          return {
            success: true,
            fileUrl: normalizedUrl,
            fileID: sessionData.cAreaImagePath,
            isFromDatabase: true
          };
        }
        break;
      case 'tAreaImage':
        if (sessionData.tAreaImageUrl) {
          // 规范化存储的URL
          const normalizedUrl = sessionData.tAreaImageUrl.replace(/wlksapp_4g54hauu5cbf43dc/g, 'wlksapp-4g54hauu5cbf43dc');
          console.log('数据库中已存在T区图片临时URL，规范化后:', normalizedUrl);
          return {
            success: true,
            fileUrl: normalizedUrl,
            fileID: sessionData.tAreaImagePath,
            isFromDatabase: true
          };
        }
        break;
    }
    
    // 如果数据库中没有临时URL，则获取媒体文件路径
    let mediaPath = '';
    switch (mediaType) {
      case 'video':
        mediaPath = sessionData.videoPath || '';
        // 检查videoPath是否是简化路径，如果是则尝试构建完整路径
        if (mediaPath && mediaPath.includes('/videos/') && !mediaPath.includes('/users/')) {
          // 构建完整的文件路径
          const fileName = mediaPath.split('/').pop();
          const userPath = `users/${openid}_data/${sessionId}`;
          mediaPath = `${userPath}/videos/${fileName}`;
          console.log('视频路径修正为完整路径:', mediaPath);
          
          // 更新数据库中的路径记录，避免下次再次需要修复
          try {
            await db.collection('analysis_index')
              .where({
                _openid: openid,
                sessionId: sessionId
              })
              .update({
                data: {
                  videoPath: mediaPath
                }
              });
            console.log('数据库中的视频路径已更新为完整路径');
          } catch (updateError) {
            console.error('更新数据库中的视频路径失败:', updateError);
          }
        }
        break;
      case 'cAreaImage':
        mediaPath = sessionData.cAreaImagePath || '';
        break;
      case 'tAreaImage':
        mediaPath = sessionData.tAreaImagePath || '';
        break;
      default:
        return {
          success: false,
          error: '不支持的媒体类型',
          message: '不支持的媒体类型'
        };
    }
    
    console.log(`从数据库获取到的${mediaType}路径: ${mediaPath}`);
    
    if (!mediaPath) {
      return {
        success: false,
        error: `未找到${mediaType}路径`,
        message: `未找到${mediaType}文件路径`
      };
    }
    
    // 第0种方法：检查是否已是HTTP URL
    if (mediaPath.startsWith('http://') || mediaPath.startsWith('https://')) {
      console.log('路径已经是HTTP URL:', mediaPath);
      
      // 对于视频类型保留查询参数，其他类型可以移除
      if (mediaType === 'video') {
        // 确保有时间戳参数
        let videoUrl = mediaPath;
        if (!videoUrl.includes('?')) {
          const timestamp = Date.now();
          videoUrl = `${videoUrl}?t=${timestamp}`;
        }
        return {
          success: true,
          fileUrl: videoUrl,
          fileID: mediaPath
        };
      } else {
        // 非视频类型移除查询参数
        const cleanUrl = mediaPath.split('?')[0];
        return {
          success: true,
          fileUrl: cleanUrl,
          fileID: mediaPath
        };
      }
    }
    
    // 新增：尝试生成直接下载链接
    const directDownloadUrl = await generateDirectDownloadUrl(mediaPath);
    if (directDownloadUrl) {
      console.log('生成直接下载链接成功:', directDownloadUrl);
      
      // 更新数据库中存储的URL
      try {
        const updateField = {};
        if (mediaType === 'video') {
          updateField.videoUrl = directDownloadUrl;
        } else if (mediaType === 'cAreaImage') {
          updateField.cAreaImageUrl = directDownloadUrl;
        } else if (mediaType === 'tAreaImage') {
          updateField.tAreaImageUrl = directDownloadUrl;
        }
        
        await db.collection('analysis_index')
          .where({
            _openid: openid,
            sessionId: sessionId
          })
          .update({
            data: updateField
          });
        console.log(`已更新数据库中的${mediaType}URL`);
      } catch (updateError) {
        console.error(`更新${mediaType}URL失败:`, updateError);
      }
      
      return {
        success: true,
        fileUrl: directDownloadUrl,
        fileID: mediaPath,
        isDirect: true
      };
    }
    
    // 如果生成直接链接失败，继续尝试获取临时URL
    // 获取文件临时链接的尝试方法
    const getTempUrlMethods = [
      // 方法1：使用原始路径
      async () => {
        console.log('方法1：使用原始路径获取临时URL');
        const formattedFileID = ensureFileIDWithEnv(mediaPath);
        console.log('格式化后文件ID:', formattedFileID);
        
        const tempResult = await cloud.getTempFileURL({
          fileList: [formattedFileID]
        });
        
        if (tempResult.fileList && tempResult.fileList.length > 0 && 
            tempResult.fileList[0].status === 0 && tempResult.fileList[0].tempFileURL) {
          // 规范化临时URL
          const normalizedUrl = tempResult.fileList[0].tempFileURL.replace(/wlksapp_4g54hauu5cbf43dc/g, 'wlksapp-4g54hauu5cbf43dc');
          return {
            success: true,
            fileUrl: normalizedUrl,
            fileID: formattedFileID
          };
        }
        throw new Error('获取临时URL失败');
      },
      
      // 方法2：通过云函数调用
      async () => {
        console.log('方法2：通过云函数调用获取临时URL');
        const formattedFileID = ensureFileIDWithEnv(mediaPath);
        
        const result = await cloud.callFunction({
          name: 'getTempFileURL',
          data: {
            fileID: formattedFileID
          }
        });
        
        if (result.result && result.result.fileList && 
            result.result.fileList.length > 0 && 
            result.result.fileList[0].tempFileURL) {
          // 规范化临时URL
          const normalizedUrl = result.result.fileList[0].tempFileURL.replace(/wlksapp_4g54hauu5cbf43dc/g, 'wlksapp-4g54hauu5cbf43dc');
          return {
            success: true,
            fileUrl: normalizedUrl,
            fileID: formattedFileID
          };
        }
        throw new Error('通过云函数获取临时URL失败');
      },
      
      // 方法3：尝试提取文件名并构建新路径
      async () => {
        console.log('方法3：提取文件名并构建新路径');
        const fileName = mediaPath.split('/').pop();
        if (!fileName) throw new Error('无法提取文件名');
        
        let newPath = '';
        if (mediaType === 'video') {
          newPath = `videos/${fileName}`;
        } else if (mediaType === 'cAreaImage') {
          newPath = `analysis_images/c_area/${fileName}`;
        } else if (mediaType === 'tAreaImage') {
          newPath = `analysis_images/t_area/${fileName}`;
        }
        
        if (!newPath) throw new Error('无法构建新路径');
        
        const formattedFileID = ensureFileIDWithEnv(newPath);
        console.log('新构建的文件ID:', formattedFileID);
        
        const tempResult = await cloud.getTempFileURL({
          fileList: [formattedFileID]
        });
        
        if (tempResult.fileList && tempResult.fileList.length > 0 && 
            tempResult.fileList[0].status === 0 && tempResult.fileList[0].tempFileURL) {
          // 规范化临时URL
          const normalizedUrl = tempResult.fileList[0].tempFileURL.replace(/wlksapp_4g54hauu5cbf43dc/g, 'wlksapp-4g54hauu5cbf43dc');
          return {
            success: true,
            fileUrl: normalizedUrl,
            fileID: formattedFileID
          };
        }
        throw new Error('使用新路径获取临时URL失败');
      }
    ];
    
    // 检查是否是资源不存在错误
    let resourceNotFoundError = false;
    
    for (let i = 0; i < getTempUrlMethods.length; i++) {
      try {
        const result = await getTempUrlMethods[i]();
        console.log(`方法${i+1}成功获取临时URL:`, result.fileUrl);
        return result;
      } catch (error) {
        console.log(`方法${i+1}失败:`, error.message);
        // 检查是否是资源不存在错误
        if (error.message && (
          error.message.includes('resource not found') || 
          error.message.includes('resource system error') ||
          error.message.includes('-501001')
        )) {
          resourceNotFoundError = true;
        }
      }
    }
    
    // 所有方法都失败，作为最后手段，检查是否是资源不存在
    console.log('所有获取临时URL的方法都失败，检查是否是资源不存在');
    
    if (resourceNotFoundError) {
      console.log('资源不存在，返回默认图片');
      
      // 判断是哪种媒体类型，并提供对应的默认图片
      let defaultImageUrl = getDefaultImageUrl(mediaType);
      
      return {
        success: true,
        fileUrl: defaultImageUrl,
        fileID: mediaPath,
        isDefault: true,
        message: '原始图片不存在，已返回默认图片'
      };
    }
    
    // 如果不是资源不存在的错误，尝试再次用直接链接方式返回
    const fallbackDirectUrl = await generateDirectDownloadUrl(mediaPath);
    if (fallbackDirectUrl) {
      return {
        success: true,
        fileUrl: fallbackDirectUrl,
        fileID: mediaPath,
        isDirect: true,
        message: '无法获取临时URL，已生成直接访问链接'
      };
    }
    
    return {
      success: true,
      useDirectFileID: true,
      fileUrl: mediaPath, // 返回原始路径，前端可直接尝试使用
      fileID: mediaPath,
      message: '无法获取临时URL，请尝试直接使用文件ID'
    };
    
  } catch (error) {
    console.error('处理媒体文件请求失败:', error);
    return {
      success: false,
      error: error.message,
      message: '处理请求失败，请稍后重试'
    };
  }
}

// 新增函数：获取视频分析结果的详细信息
async function getAnalysisDetails(openid, sessionId) {
  console.log(`获取用户 ${openid} 的会话 ${sessionId} 的详细分析结果`);
  
  if (!sessionId) {
    return {
      success: false,
      error: '会话ID不能为空',
      message: '会话ID不能为空'
    };
  }
  
  try {
    // 初始化数据库
    const db = cloud.database();
    
    // 从数据库中获取详细的分析结果
    const analysisResult = await db.collection('analysis_index')
      .where({
        _openid: openid,
        sessionId: sessionId
      })
      .get();
    
    if (!analysisResult.data || analysisResult.data.length === 0) {
      return {
        success: false,
        error: '未找到分析结果',
        message: '未找到该会话的分析结果数据'
      };
    }
    
    // 获取分析结果数据并记录日志
    const resultData = analysisResult.data[0];
    console.log(`获取到会话 ${sessionId} 的分析结果数据，字段:`, Object.keys(resultData));
    
    // 处理视频URL，确保有时间戳参数
    if (resultData.videoUrl && !resultData.videoUrl.includes('?')) {
      const timestamp = Date.now();
      resultData.videoUrl = `${resultData.videoUrl}?t=${timestamp}`;
    }
    
    return {
      success: true,
      data: resultData,
      message: '获取分析结果详情成功'
    };
  } catch (error) {
    console.error('获取分析详情失败:', error);
    return {
      success: false,
      error: error.message,
      message: '获取分析结果详情失败，请稍后重试'
    };
  }
}

// 更新分析类型状态（临时/正式）
async function updateAnalysisType(openid, itemId, isTemp) {
  try {
    if (!itemId) {
      return {
        success: false,
        error: '缺少必要参数',
        message: '请提供项目ID'
      };
    }
    
    console.log(`更新分析类型: openid=${openid}, itemId=${itemId}, isTemp=${isTemp}`);
    
    // 格式化OpenID，确保使用下划线格式
    const formattedOpenid = ensureUnderscoreFormat(openid);
    console.log(`格式化后的OpenID: ${formattedOpenid}`);
    
    // 格式化ItemID，尝试规范化ID
    let formattedItemId = itemId;
    // 如果是32位十六进制格式(例如f4ec2f0f6849f14d029352b71c2d92d4)，转换为标准格式
    if (/^[a-f\d]{32}$/i.test(itemId)) {
      // 转换为标准的MongoDB ObjectId格式 (24位)
      formattedItemId = itemId.substring(0, 24);
      console.log(`转换后的ObjectId格式: ${formattedItemId}`);
    }
    
    // 初始化数据库
    const db = cloud.database();
    const _ = db.command;
    
    // 查询策略：尝试多种查询方式
    const queryStrategies = [
      // 策略1: 使用传入的原始itemId作为_id
      {
        _id: itemId,
        desc: "原始itemId作为_id"
      },
      
      // 策略2: 使用截断后的24位itemId作为_id
      {
        _id: formattedItemId,
        desc: "截断后的24位itemId作为_id"
      },
      
      // 策略3: 使用原始itemId作为sessionId
      {
        _openid: formattedOpenid,
        sessionId: itemId,
        desc: "使用原始itemId作为sessionId"
      },
      
      // 策略4: 使用ID部分匹配
      {
        _openid: formattedOpenid,
        $or: [
          { _id: db.RegExp({ regexp: itemId.substring(0, 8), options: 'i' }) },
          { sessionId: db.RegExp({ regexp: itemId.substring(0, 8), options: 'i' }) },
          { taskId: db.RegExp({ regexp: itemId.substring(0, 8), options: 'i' }) }
        ],
        desc: "使用ID部分匹配"
      },
      
      // 策略5: 宽松搜索，只匹配openid，返回最新记录
      {
        _openid: formattedOpenid,
        desc: "宽松搜索，匹配openid，返回最新记录"
      }
    ];
    
    // 数据库调试信息
    try {
      // 获取该用户的所有记录数量
      const countResult = await db.collection('analysis_index')
        .where({ _openid: formattedOpenid })
        .count();
      
      console.log(`数据库中用户(${formattedOpenid})共有${countResult.total}条记录`);
      
      // 列出部分记录的ID信息进行调试
      const sampleRecords = await db.collection('analysis_index')
        .where({ _openid: formattedOpenid })
        .field({ _id: true, sessionId: true, taskId: true })
        .orderBy('createTime', 'desc')
        .limit(5)
        .get();
      
      console.log(`用户最近5条记录的ID信息:`);
      if (sampleRecords.data && sampleRecords.data.length > 0) {
        sampleRecords.data.forEach((record, index) => {
          console.log(`记录${index+1}: _id=${record._id}, sessionId=${record.sessionId || 'undefined'}, taskId=${record.taskId || 'undefined'}`);
        });
      } else {
        console.log('没有找到任何记录');
      }
    } catch (debugErr) {
      console.error('获取调试信息失败:', debugErr);
    }
    
    // 依次尝试各种查询策略
    for (let i = 0; i < queryStrategies.length; i++) {
      const strategy = queryStrategies[i];
      try {
        console.log(`尝试查询策略${i+1}: ${strategy.desc}`);
        
        let query = {};
        if (i === 3) {
          // 特殊处理正则表达式查询，因为它需要使用command
          query = {
            _openid: formattedOpenid,
            $or: [
              { _id: db.RegExp({ regexp: itemId.substring(0, 8), options: 'i' }) },
              { sessionId: db.RegExp({ regexp: itemId.substring(0, 8), options: 'i' }) },
              { taskId: db.RegExp({ regexp: itemId.substring(0, 8), options: 'i' }) }
            ]
          };
        } else if (i === 4) {
          // 宽松搜索，只查询最新记录
          const result = await db.collection('analysis_index')
            .where({ _openid: formattedOpenid })
            .orderBy('createTime', 'desc')
            .limit(1)
            .get();
          
          if (result.data && result.data.length > 0) {
            console.log('找到最新记录:', result.data[0]._id);
            
            // 更新记录
            const updateResult = await db.collection('analysis_index')
              .doc(result.data[0]._id)
              .update({
                data: {
                  isTemp: isTemp,
                  updateTime: new Date()
                }
              });
            
            console.log('更新分析类型结果:', updateResult);
            
            return {
              success: true,
              message: `成功将分析类型更新为${isTemp ? '临时' : '正式'}`
            };
          }
          
          // 如果没有找到记录，继续尝试下一个策略
          continue;
        } else {
          // 其他策略使用普通查询
          query = strategy;
          delete query.desc; // 移除描述字段
        }
        
        console.log('查询条件:', JSON.stringify(query));
        const record = await db.collection('analysis_index').where(query).get();
        
        if (record.data && record.data.length > 0) {
          console.log(`查询策略${i+1}成功，找到匹配记录:`, record.data[0]._id);
          
          // 使用doc方法更新特定记录
          const docId = record.data[0]._id;
          const updateResult = await db.collection('analysis_index')
            .doc(docId)
            .update({
              data: {
                isTemp: isTemp,
                updateTime: new Date()
              }
            });
          
          console.log('更新分析类型结果:', updateResult);
          
          if (updateResult.stats && updateResult.stats.updated > 0) {
            return {
              success: true,
              message: `成功将分析类型更新为${isTemp ? '临时' : '正式'}`
            };
          } else {
            // 即使stats.updated为0，也返回成功，因为可能值没有变化
            return {
              success: true,
              message: `分析类型已是${isTemp ? '临时' : '正式'}状态`
            };
          }
        } else {
          console.log(`查询策略${i+1}未找到匹配记录`);
        }
      } catch (strategyErr) {
        console.error(`查询策略${i+1}执行失败:`, strategyErr);
      }
    }
    
    // 所有策略都失败，尝试创建新记录
    try {
      console.log('所有查询策略都失败，尝试创建新记录');
      
      // 创建一个新记录
      const newRecord = {
        _openid: formattedOpenid,
        sessionId: itemId, // 使用itemId作为sessionId
        createTime: new Date(),
        updateTime: new Date(),
        isTemp: isTemp,
        // 添加一些基本信息
        customName: '手动创建的记录',
        platform: 'miniprogram',
        cAreaValue: 0,
        tAreaValue: 0,
        tcValue: 0
      };
      
      const addResult = await db.collection('analysis_index').add({
        data: newRecord
      });
      
      console.log('创建新记录结果:', addResult);
      
      if (addResult._id) {
        return {
          success: true,
          message: `创建新记录并设置为${isTemp ? '临时' : '正式'}状态`,
          isNewRecord: true
        };
      }
    } catch (createErr) {
      console.error('创建新记录失败:', createErr);
    }
    
    // 如果所有尝试都失败，返回错误
    return {
      success: false,
      error: '未找到匹配的记录',
      message: '未找到要更新的记录，请重新加载数据'
    };
  } catch (error) {
    console.error('更新分析类型出错:', error);
    return {
      success: false,
      error: error.message,
      message: '服务异常，请稍后重试'
    };
  }
}