{"version": 3, "sources": ["url.js"], "names": [], "mappings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file": "index.js", "sourcesContent": ["/*\n * Copyright Joyent, Inc. and other Node contributors.\n *\n * Permission is hereby granted, free of charge, to any person obtaining a\n * copy of this software and associated documentation files (the\n * \"Software\"), to deal in the Software without restriction, including\n * without limitation the rights to use, copy, modify, merge, publish,\n * distribute, sublicense, and/or sell copies of the Software, and to permit\n * persons to whom the Software is furnished to do so, subject to the\n * following conditions:\n *\n * The above copyright notice and this permission notice shall be included\n * in all copies or substantial portions of the Software.\n *\n * THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS\n * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\n * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN\n * NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM,\n * DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR\n * OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE\n * USE OR OTHER DEALINGS IN THE SOFTWARE.\n */\n\n\n\nvar punycode = require('punycode/');\n\nfunction Url() {\n  this.protocol = null;\n  this.slashes = null;\n  this.auth = null;\n  this.host = null;\n  this.port = null;\n  this.hostname = null;\n  this.hash = null;\n  this.search = null;\n  this.query = null;\n  this.pathname = null;\n  this.path = null;\n  this.href = null;\n}\n\n// Reference: RFC 3986, RFC 1808, RFC 2396\n\n/*\n * define these here so at least they only have to be\n * compiled once on the first module load.\n */\nvar protocolPattern = /^([a-z0-9.+-]+:)/i,\n  portPattern = /:[0-9]*$/,\n\n  // Special case for a simple path URL\n  simplePathPattern = /^(\\/\\/?(?!\\/)[^?\\s]*)(\\?[^\\s]*)?$/,\n\n  /*\n   * RFC 2396: characters reserved for delimiting URLs.\n   * We actually just auto-escape these.\n   */\n  delims = [\n    '<', '>', '\"', '`', ' ', '\\r', '\\n', '\\t'\n  ],\n\n  // RFC 2396: characters not allowed for various reasons.\n  unwise = [\n    '{', '}', '|', '\\\\', '^', '`'\n  ].concat(delims),\n\n  // Allowed by RFCs, but cause of XSS attacks.  Always escape these.\n  autoEscape = ['\\''].concat(unwise),\n  /*\n   * Characters that are never ever allowed in a hostname.\n   * Note that any invalid chars are also handled, but these\n   * are the ones that are *expected* to be seen, so we fast-path\n   * them.\n   */\n  nonHostChars = [\n    '%', '/', '?', ';', '#'\n  ].concat(autoEscape),\n  hostEndingChars = [\n    '/', '?', '#'\n  ],\n  hostnameMaxLen = 255,\n  hostnamePartPattern = /^[+a-z0-9A-Z_-]{0,63}$/,\n  hostnamePartStart = /^([+a-z0-9A-Z_-]{0,63})(.*)$/,\n  // protocols that can allow \"unsafe\" and \"unwise\" chars.\n  unsafeProtocol = {\n    javascript: true,\n    'javascript:': true\n  },\n  // protocols that never have a hostname.\n  hostlessProtocol = {\n    javascript: true,\n    'javascript:': true\n  },\n  // protocols that always contain a // bit.\n  slashedProtocol = {\n    http: true,\n    https: true,\n    ftp: true,\n    gopher: true,\n    file: true,\n    'http:': true,\n    'https:': true,\n    'ftp:': true,\n    'gopher:': true,\n    'file:': true\n  },\n  querystring = require('qs');\n\nfunction urlParse(url, parseQueryString, slashesDenoteHost) {\n  if (url && typeof url === 'object' && url instanceof Url) { return url; }\n\n  var u = new Url();\n  u.parse(url, parseQueryString, slashesDenoteHost);\n  return u;\n}\n\nUrl.prototype.parse = function (url, parseQueryString, slashesDenoteHost) {\n  if (typeof url !== 'string') {\n    throw new TypeError(\"Parameter 'url' must be a string, not \" + typeof url);\n  }\n\n  /*\n   * Copy chrome, IE, opera backslash-handling behavior.\n   * Back slashes before the query string get converted to forward slashes\n   * See: https://code.google.com/p/chromium/issues/detail?id=25916\n   */\n  var queryIndex = url.indexOf('?'),\n    splitter = queryIndex !== -1 && queryIndex < url.indexOf('#') ? '?' : '#',\n    uSplit = url.split(splitter),\n    slashRegex = /\\\\/g;\n  uSplit[0] = uSplit[0].replace(slashRegex, '/');\n  url = uSplit.join(splitter);\n\n  var rest = url;\n\n  /*\n   * trim before proceeding.\n   * This is to support parse stuff like \"  http://foo.com  \\n\"\n   */\n  rest = rest.trim();\n\n  if (!slashesDenoteHost && url.split('#').length === 1) {\n    // Try fast path regexp\n    var simplePath = simplePathPattern.exec(rest);\n    if (simplePath) {\n      this.path = rest;\n      this.href = rest;\n      this.pathname = simplePath[1];\n      if (simplePath[2]) {\n        this.search = simplePath[2];\n        if (parseQueryString) {\n          this.query = querystring.parse(this.search.substr(1));\n        } else {\n          this.query = this.search.substr(1);\n        }\n      } else if (parseQueryString) {\n        this.search = '';\n        this.query = {};\n      }\n      return this;\n    }\n  }\n\n  var proto = protocolPattern.exec(rest);\n  if (proto) {\n    proto = proto[0];\n    var lowerProto = proto.toLowerCase();\n    this.protocol = lowerProto;\n    rest = rest.substr(proto.length);\n  }\n\n  /*\n   * figure out if it's got a host\n   * user@server is *always* interpreted as a hostname, and url\n   * resolution will treat //foo/bar as host=foo,path=bar because that's\n   * how the browser resolves relative URLs.\n   */\n  if (slashesDenoteHost || proto || rest.match(/^\\/\\/[^@/]+@[^@/]+/)) {\n    var slashes = rest.substr(0, 2) === '//';\n    if (slashes && !(proto && hostlessProtocol[proto])) {\n      rest = rest.substr(2);\n      this.slashes = true;\n    }\n  }\n\n  if (!hostlessProtocol[proto] && (slashes || (proto && !slashedProtocol[proto]))) {\n\n    /*\n     * there's a hostname.\n     * the first instance of /, ?, ;, or # ends the host.\n     *\n     * If there is an @ in the hostname, then non-host chars *are* allowed\n     * to the left of the last @ sign, unless some host-ending character\n     * comes *before* the @-sign.\n     * URLs are obnoxious.\n     *\n     * ex:\n     * http://a@b@c/ => user:a@b host:c\n     * http://a@b?@c => user:a host:c path:/?@c\n     */\n\n    /*\n     * v0.12 TODO(isaacs): This is not quite how Chrome does things.\n     * Review our test case against browsers more comprehensively.\n     */\n\n    // find the first instance of any hostEndingChars\n    var hostEnd = -1;\n    for (var i = 0; i < hostEndingChars.length; i++) {\n      var hec = rest.indexOf(hostEndingChars[i]);\n      if (hec !== -1 && (hostEnd === -1 || hec < hostEnd)) { hostEnd = hec; }\n    }\n\n    /*\n     * at this point, either we have an explicit point where the\n     * auth portion cannot go past, or the last @ char is the decider.\n     */\n    var auth, atSign;\n    if (hostEnd === -1) {\n      // atSign can be anywhere.\n      atSign = rest.lastIndexOf('@');\n    } else {\n      /*\n       * atSign must be in auth portion.\n       * http://a@b/c@d => host:b auth:a path:/c@d\n       */\n      atSign = rest.lastIndexOf('@', hostEnd);\n    }\n\n    /*\n     * Now we have a portion which is definitely the auth.\n     * Pull that off.\n     */\n    if (atSign !== -1) {\n      auth = rest.slice(0, atSign);\n      rest = rest.slice(atSign + 1);\n      this.auth = decodeURIComponent(auth);\n    }\n\n    // the host is the remaining to the left of the first non-host char\n    hostEnd = -1;\n    for (var i = 0; i < nonHostChars.length; i++) {\n      var hec = rest.indexOf(nonHostChars[i]);\n      if (hec !== -1 && (hostEnd === -1 || hec < hostEnd)) { hostEnd = hec; }\n    }\n    // if we still have not hit it, then the entire thing is a host.\n    if (hostEnd === -1) { hostEnd = rest.length; }\n\n    this.host = rest.slice(0, hostEnd);\n    rest = rest.slice(hostEnd);\n\n    // pull out port.\n    this.parseHost();\n\n    /*\n     * we've indicated that there is a hostname,\n     * so even if it's empty, it has to be present.\n     */\n    this.hostname = this.hostname || '';\n\n    /*\n     * if hostname begins with [ and ends with ]\n     * assume that it's an IPv6 address.\n     */\n    var ipv6Hostname = this.hostname[0] === '[' && this.hostname[this.hostname.length - 1] === ']';\n\n    // validate a little.\n    if (!ipv6Hostname) {\n      var hostparts = this.hostname.split(/\\./);\n      for (var i = 0, l = hostparts.length; i < l; i++) {\n        var part = hostparts[i];\n        if (!part) { continue; }\n        if (!part.match(hostnamePartPattern)) {\n          var newpart = '';\n          for (var j = 0, k = part.length; j < k; j++) {\n            if (part.charCodeAt(j) > 127) {\n              /*\n               * we replace non-ASCII char with a temporary placeholder\n               * we need this to make sure size of hostname is not\n               * broken by replacing non-ASCII by nothing\n               */\n              newpart += 'x';\n            } else {\n              newpart += part[j];\n            }\n          }\n          // we test again with ASCII char only\n          if (!newpart.match(hostnamePartPattern)) {\n            var validParts = hostparts.slice(0, i);\n            var notHost = hostparts.slice(i + 1);\n            var bit = part.match(hostnamePartStart);\n            if (bit) {\n              validParts.push(bit[1]);\n              notHost.unshift(bit[2]);\n            }\n            if (notHost.length) {\n              rest = '/' + notHost.join('.') + rest;\n            }\n            this.hostname = validParts.join('.');\n            break;\n          }\n        }\n      }\n    }\n\n    if (this.hostname.length > hostnameMaxLen) {\n      this.hostname = '';\n    } else {\n      // hostnames are always lower case.\n      this.hostname = this.hostname.toLowerCase();\n    }\n\n    if (!ipv6Hostname) {\n      /*\n       * IDNA Support: Returns a punycoded representation of \"domain\".\n       * It only converts parts of the domain name that\n       * have non-ASCII characters, i.e. it doesn't matter if\n       * you call it with a domain that already is ASCII-only.\n       */\n      this.hostname = punycode.toASCII(this.hostname);\n    }\n\n    var p = this.port ? ':' + this.port : '';\n    var h = this.hostname || '';\n    this.host = h + p;\n    this.href += this.host;\n\n    /*\n     * strip [ and ] from the hostname\n     * the host field still retains them, though\n     */\n    if (ipv6Hostname) {\n      this.hostname = this.hostname.substr(1, this.hostname.length - 2);\n      if (rest[0] !== '/') {\n        rest = '/' + rest;\n      }\n    }\n  }\n\n  /*\n   * now rest is set to the post-host stuff.\n   * chop off any delim chars.\n   */\n  if (!unsafeProtocol[lowerProto]) {\n\n    /*\n     * First, make 100% sure that any \"autoEscape\" chars get\n     * escaped, even if encodeURIComponent doesn't think they\n     * need to be.\n     */\n    for (var i = 0, l = autoEscape.length; i < l; i++) {\n      var ae = autoEscape[i];\n      if (rest.indexOf(ae) === -1) { continue; }\n      var esc = encodeURIComponent(ae);\n      if (esc === ae) {\n        esc = escape(ae);\n      }\n      rest = rest.split(ae).join(esc);\n    }\n  }\n\n  // chop off from the tail first.\n  var hash = rest.indexOf('#');\n  if (hash !== -1) {\n    // got a fragment string.\n    this.hash = rest.substr(hash);\n    rest = rest.slice(0, hash);\n  }\n  var qm = rest.indexOf('?');\n  if (qm !== -1) {\n    this.search = rest.substr(qm);\n    this.query = rest.substr(qm + 1);\n    if (parseQueryString) {\n      this.query = querystring.parse(this.query);\n    }\n    rest = rest.slice(0, qm);\n  } else if (parseQueryString) {\n    // no query string, but parseQueryString still requested\n    this.search = '';\n    this.query = {};\n  }\n  if (rest) { this.pathname = rest; }\n  if (slashedProtocol[lowerProto] && this.hostname && !this.pathname) {\n    this.pathname = '/';\n  }\n\n  // to support http.request\n  if (this.pathname || this.search) {\n    var p = this.pathname || '';\n    var s = this.search || '';\n    this.path = p + s;\n  }\n\n  // finally, reconstruct the href based on what has been validated.\n  this.href = this.format();\n  return this;\n};\n\n// format a parsed object into a url string\nfunction urlFormat(obj) {\n  /*\n   * ensure it's an object, and not a string url.\n   * If it's an obj, this is a no-op.\n   * this way, you can call url_format() on strings\n   * to clean up potentially wonky urls.\n   */\n  if (typeof obj === 'string') { obj = urlParse(obj); }\n  if (!(obj instanceof Url)) { return Url.prototype.format.call(obj); }\n  return obj.format();\n}\n\nUrl.prototype.format = function () {\n  var auth = this.auth || '';\n  if (auth) {\n    auth = encodeURIComponent(auth);\n    auth = auth.replace(/%3A/i, ':');\n    auth += '@';\n  }\n\n  var protocol = this.protocol || '',\n    pathname = this.pathname || '',\n    hash = this.hash || '',\n    host = false,\n    query = '';\n\n  if (this.host) {\n    host = auth + this.host;\n  } else if (this.hostname) {\n    host = auth + (this.hostname.indexOf(':') === -1 ? this.hostname : '[' + this.hostname + ']');\n    if (this.port) {\n      host += ':' + this.port;\n    }\n  }\n\n  if (this.query && typeof this.query === 'object' && Object.keys(this.query).length) {\n    query = querystring.stringify(this.query, {\n      arrayFormat: 'repeat',\n      addQueryPrefix: false\n    });\n  }\n\n  var search = this.search || (query && ('?' + query)) || '';\n\n  if (protocol && protocol.substr(-1) !== ':') { protocol += ':'; }\n\n  /*\n   * only the slashedProtocols get the //.  Not mailto:, xmpp:, etc.\n   * unless they had them to begin with.\n   */\n  if (this.slashes || (!protocol || slashedProtocol[protocol]) && host !== false) {\n    host = '//' + (host || '');\n    if (pathname && pathname.charAt(0) !== '/') { pathname = '/' + pathname; }\n  } else if (!host) {\n    host = '';\n  }\n\n  if (hash && hash.charAt(0) !== '#') { hash = '#' + hash; }\n  if (search && search.charAt(0) !== '?') { search = '?' + search; }\n\n  pathname = pathname.replace(/[?#]/g, function (match) {\n    return encodeURIComponent(match);\n  });\n  search = search.replace('#', '%23');\n\n  return protocol + host + pathname + search + hash;\n};\n\nfunction urlResolve(source, relative) {\n  return urlParse(source, false, true).resolve(relative);\n}\n\nUrl.prototype.resolve = function (relative) {\n  return this.resolveObject(urlParse(relative, false, true)).format();\n};\n\nfunction urlResolveObject(source, relative) {\n  if (!source) { return relative; }\n  return urlParse(source, false, true).resolveObject(relative);\n}\n\nUrl.prototype.resolveObject = function (relative) {\n  if (typeof relative === 'string') {\n    var rel = new Url();\n    rel.parse(relative, false, true);\n    relative = rel;\n  }\n\n  var result = new Url();\n  var tkeys = Object.keys(this);\n  for (var tk = 0; tk < tkeys.length; tk++) {\n    var tkey = tkeys[tk];\n    result[tkey] = this[tkey];\n  }\n\n  /*\n   * hash is always overridden, no matter what.\n   * even href=\"\" will remove it.\n   */\n  result.hash = relative.hash;\n\n  // if the relative url is empty, then there's nothing left to do here.\n  if (relative.href === '') {\n    result.href = result.format();\n    return result;\n  }\n\n  // hrefs like //foo/bar always cut to the protocol.\n  if (relative.slashes && !relative.protocol) {\n    // take everything except the protocol from relative\n    var rkeys = Object.keys(relative);\n    for (var rk = 0; rk < rkeys.length; rk++) {\n      var rkey = rkeys[rk];\n      if (rkey !== 'protocol') { result[rkey] = relative[rkey]; }\n    }\n\n    // urlParse appends trailing / to urls like http://www.example.com\n    if (slashedProtocol[result.protocol] && result.hostname && !result.pathname) {\n      result.pathname = '/';\n      result.path = result.pathname;\n    }\n\n    result.href = result.format();\n    return result;\n  }\n\n  if (relative.protocol && relative.protocol !== result.protocol) {\n    /*\n     * if it's a known url protocol, then changing\n     * the protocol does weird things\n     * first, if it's not file:, then we MUST have a host,\n     * and if there was a path\n     * to begin with, then we MUST have a path.\n     * if it is file:, then the host is dropped,\n     * because that's known to be hostless.\n     * anything else is assumed to be absolute.\n     */\n    if (!slashedProtocol[relative.protocol]) {\n      var keys = Object.keys(relative);\n      for (var v = 0; v < keys.length; v++) {\n        var k = keys[v];\n        result[k] = relative[k];\n      }\n      result.href = result.format();\n      return result;\n    }\n\n    result.protocol = relative.protocol;\n    if (!relative.host && !hostlessProtocol[relative.protocol]) {\n      var relPath = (relative.pathname || '').split('/');\n      while (relPath.length && !(relative.host = relPath.shift())) { }\n      if (!relative.host) { relative.host = ''; }\n      if (!relative.hostname) { relative.hostname = ''; }\n      if (relPath[0] !== '') { relPath.unshift(''); }\n      if (relPath.length < 2) { relPath.unshift(''); }\n      result.pathname = relPath.join('/');\n    } else {\n      result.pathname = relative.pathname;\n    }\n    result.search = relative.search;\n    result.query = relative.query;\n    result.host = relative.host || '';\n    result.auth = relative.auth;\n    result.hostname = relative.hostname || relative.host;\n    result.port = relative.port;\n    // to support http.request\n    if (result.pathname || result.search) {\n      var p = result.pathname || '';\n      var s = result.search || '';\n      result.path = p + s;\n    }\n    result.slashes = result.slashes || relative.slashes;\n    result.href = result.format();\n    return result;\n  }\n\n  var isSourceAbs = result.pathname && result.pathname.charAt(0) === '/',\n    isRelAbs = relative.host || relative.pathname && relative.pathname.charAt(0) === '/',\n    mustEndAbs = isRelAbs || isSourceAbs || (result.host && relative.pathname),\n    removeAllDots = mustEndAbs,\n    srcPath = result.pathname && result.pathname.split('/') || [],\n    relPath = relative.pathname && relative.pathname.split('/') || [],\n    psychotic = result.protocol && !slashedProtocol[result.protocol];\n\n  /*\n   * if the url is a non-slashed url, then relative\n   * links like ../.. should be able\n   * to crawl up to the hostname, as well.  This is strange.\n   * result.protocol has already been set by now.\n   * Later on, put the first path part into the host field.\n   */\n  if (psychotic) {\n    result.hostname = '';\n    result.port = null;\n    if (result.host) {\n      if (srcPath[0] === '') { srcPath[0] = result.host; } else { srcPath.unshift(result.host); }\n    }\n    result.host = '';\n    if (relative.protocol) {\n      relative.hostname = null;\n      relative.port = null;\n      if (relative.host) {\n        if (relPath[0] === '') { relPath[0] = relative.host; } else { relPath.unshift(relative.host); }\n      }\n      relative.host = null;\n    }\n    mustEndAbs = mustEndAbs && (relPath[0] === '' || srcPath[0] === '');\n  }\n\n  if (isRelAbs) {\n    // it's absolute.\n    result.host = relative.host || relative.host === '' ? relative.host : result.host;\n    result.hostname = relative.hostname || relative.hostname === '' ? relative.hostname : result.hostname;\n    result.search = relative.search;\n    result.query = relative.query;\n    srcPath = relPath;\n    // fall through to the dot-handling below.\n  } else if (relPath.length) {\n    /*\n     * it's relative\n     * throw away the existing file, and take the new path instead.\n     */\n    if (!srcPath) { srcPath = []; }\n    srcPath.pop();\n    srcPath = srcPath.concat(relPath);\n    result.search = relative.search;\n    result.query = relative.query;\n  } else if (relative.search != null) {\n    /*\n     * just pull out the search.\n     * like href='?foo'.\n     * Put this after the other two cases because it simplifies the booleans\n     */\n    if (psychotic) {\n      result.host = srcPath.shift();\n      result.hostname = result.host;\n      /*\n       * occationaly the auth can get stuck only in host\n       * this especially happens in cases like\n       * url.resolveObject('mailto:local1@domain1', 'local2@domain2')\n       */\n      var authInHost = result.host && result.host.indexOf('@') > 0 ? result.host.split('@') : false;\n      if (authInHost) {\n        result.auth = authInHost.shift();\n        result.hostname = authInHost.shift();\n        result.host = result.hostname;\n      }\n    }\n    result.search = relative.search;\n    result.query = relative.query;\n    // to support http.request\n    if (result.pathname !== null || result.search !== null) {\n      result.path = (result.pathname ? result.pathname : '') + (result.search ? result.search : '');\n    }\n    result.href = result.format();\n    return result;\n  }\n\n  if (!srcPath.length) {\n    /*\n     * no path at all.  easy.\n     * we've already handled the other stuff above.\n     */\n    result.pathname = null;\n    // to support http.request\n    if (result.search) {\n      result.path = '/' + result.search;\n    } else {\n      result.path = null;\n    }\n    result.href = result.format();\n    return result;\n  }\n\n  /*\n   * if a url ENDs in . or .., then it must get a trailing slash.\n   * however, if it ends in anything else non-slashy,\n   * then it must NOT get a trailing slash.\n   */\n  var last = srcPath.slice(-1)[0];\n  var hasTrailingSlash = (result.host || relative.host || srcPath.length > 1) && (last === '.' || last === '..') || last === '';\n\n  /*\n   * strip single dots, resolve double dots to parent dir\n   * if the path tries to go above the root, `up` ends up > 0\n   */\n  var up = 0;\n  for (var i = srcPath.length; i >= 0; i--) {\n    last = srcPath[i];\n    if (last === '.') {\n      srcPath.splice(i, 1);\n    } else if (last === '..') {\n      srcPath.splice(i, 1);\n      up++;\n    } else if (up) {\n      srcPath.splice(i, 1);\n      up--;\n    }\n  }\n\n  // if the path is allowed to go above the root, restore leading ..s\n  if (!mustEndAbs && !removeAllDots) {\n    for (; up--; up) {\n      srcPath.unshift('..');\n    }\n  }\n\n  if (mustEndAbs && srcPath[0] !== '' && (!srcPath[0] || srcPath[0].charAt(0) !== '/')) {\n    srcPath.unshift('');\n  }\n\n  if (hasTrailingSlash && (srcPath.join('/').substr(-1) !== '/')) {\n    srcPath.push('');\n  }\n\n  var isAbsolute = srcPath[0] === '' || (srcPath[0] && srcPath[0].charAt(0) === '/');\n\n  // put the host back\n  if (psychotic) {\n    result.hostname = isAbsolute ? '' : srcPath.length ? srcPath.shift() : '';\n    result.host = result.hostname;\n    /*\n     * occationaly the auth can get stuck only in host\n     * this especially happens in cases like\n     * url.resolveObject('mailto:local1@domain1', 'local2@domain2')\n     */\n    var authInHost = result.host && result.host.indexOf('@') > 0 ? result.host.split('@') : false;\n    if (authInHost) {\n      result.auth = authInHost.shift();\n      result.hostname = authInHost.shift();\n      result.host = result.hostname;\n    }\n  }\n\n  mustEndAbs = mustEndAbs || (result.host && srcPath.length);\n\n  if (mustEndAbs && !isAbsolute) {\n    srcPath.unshift('');\n  }\n\n  if (srcPath.length > 0) {\n    result.pathname = srcPath.join('/');\n  } else {\n    result.pathname = null;\n    result.path = null;\n  }\n\n  // to support request.http\n  if (result.pathname !== null || result.search !== null) {\n    result.path = (result.pathname ? result.pathname : '') + (result.search ? result.search : '');\n  }\n  result.auth = relative.auth || result.auth;\n  result.slashes = result.slashes || relative.slashes;\n  result.href = result.format();\n  return result;\n};\n\nUrl.prototype.parseHost = function () {\n  var host = this.host;\n  var port = portPattern.exec(host);\n  if (port) {\n    port = port[0];\n    if (port !== ':') {\n      this.port = port.substr(1);\n    }\n    host = host.substr(0, host.length - port.length);\n  }\n  if (host) { this.hostname = host; }\n};\n\nexports.parse = urlParse;\nexports.resolve = urlResolve;\nexports.resolveObject = urlResolveObject;\nexports.format = urlFormat;\n\nexports.Url = Url;\n"]}