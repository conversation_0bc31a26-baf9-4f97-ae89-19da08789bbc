{"version": 3, "sources": ["UTIF.js"], "names": [], "mappings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file": "index.js", "sourcesContent": ["\n\n\n\n;(function(){\nvar UTIF = {};\n\n// Make available for import by `require()`\nif (typeof module == \"object\") {module.exports = UTIF;}\nelse {self.UTIF = UTIF;}\n\nvar pako;\nif (typeof require == \"function\") {pako = require(\"pako\");}\nelse {pako = self.pako;}\n\nfunction log() { if (typeof process==\"undefined\" || process.env.NODE_ENV==\"development\") console.log.apply(console, arguments);  }\n\n(function(UTIF, pako){\n\t\n// Following lines add a JPEG decoder  to UTIF.JpegDecoder\n(function(){var V=\"function\"===typeof Symbol&&\"symbol\"===typeof Symbol.iterator?function(g){return typeof g}:function(g){return g&&\"function\"===typeof Symbol&&g.constructor===Symbol&&g!==Symbol.prototype?\"symbol\":typeof g},D=function(){function g(g){this.message=\"JPEG error: \"+g}g.prototype=Error();g.prototype.name=\"JpegError\";return g.constructor=g}(),P=function(){function g(g,D){this.message=g;this.g=D}g.prototype=Error();g.prototype.name=\"DNLMarkerError\";return g.constructor=g}();(function(){function g(){this.M=\nnull;this.B=-1}function W(a,d){for(var f=0,e=[],b,B,k=16;0<k&&!a[k-1];)k--;e.push({children:[],index:0});var l=e[0],r;for(b=0;b<k;b++){for(B=0;B<a[b];B++){l=e.pop();for(l.children[l.index]=d[f];0<l.index;)l=e.pop();l.index++;for(e.push(l);e.length<=b;)e.push(r={children:[],index:0}),l.children[l.index]=r.children,l=r;f++}b+1<k&&(e.push(r={children:[],index:0}),l.children[l.index]=r.children,l=r)}return e[0].children}function X(a,d,f,e,b,B,k,l,r){function n(){if(0<x)return x--,z>>x&1;z=a[d++];if(255===\nz){var c=a[d++];if(c){if(220===c&&g){d+=2;var b=a[d++]<<8|a[d++];if(0<b&&b!==f.g)throw new P(\"Found DNL marker (0xFFDC) while parsing scan data\",b);}throw new D(\"unexpected marker \"+(z<<8|c).toString(16));}}x=7;return z>>>7}function q(a){for(;;){a=a[n()];if(\"number\"===typeof a)return a;if(\"object\"!==(\"undefined\"===typeof a?\"undefined\":V(a)))throw new D(\"invalid huffman sequence\");}}function h(a){for(var c=0;0<a;)c=c<<1|n(),a--;return c}function c(a){if(1===a)return 1===n()?1:-1;var c=h(a);return c>=\n1<<a-1?c:c+(-1<<a)+1}function C(a,b){var d=q(a.D);d=0===d?0:c(d);a.a[b]=a.m+=d;for(d=1;64>d;){var h=q(a.o),k=h&15;h>>=4;if(0===k){if(15>h)break;d+=16}else d+=h,a.a[b+J[d]]=c(k),d++}}function w(a,d){var b=q(a.D);b=0===b?0:c(b)<<r;a.a[d]=a.m+=b}function p(a,c){a.a[c]|=n()<<r}function m(a,b){if(0<A)A--;else for(var d=B;d<=k;){var e=q(a.o),f=e&15;e>>=4;if(0===f){if(15>e){A=h(e)+(1<<e)-1;break}d+=16}else d+=e,a.a[b+J[d]]=c(f)*(1<<r),d++}}function t(a,d){for(var b=B,e=0,f;b<=k;){f=d+J[b];var l=0>a.a[f]?\n-1:1;switch(E){case 0:e=q(a.o);f=e&15;e>>=4;if(0===f)15>e?(A=h(e)+(1<<e),E=4):(e=16,E=1);else{if(1!==f)throw new D(\"invalid ACn encoding\");Q=c(f);E=e?2:3}continue;case 1:case 2:a.a[f]?a.a[f]+=l*(n()<<r):(e--,0===e&&(E=2===E?3:0));break;case 3:a.a[f]?a.a[f]+=l*(n()<<r):(a.a[f]=Q<<r,E=0);break;case 4:a.a[f]&&(a.a[f]+=l*(n()<<r))}b++}4===E&&(A--,0===A&&(E=0))}var g=9<arguments.length&&void 0!==arguments[9]?arguments[9]:!1,u=f.P,v=d,z=0,x=0,A=0,E=0,Q,K=e.length,F,L,M,I;var R=f.S?0===B?0===l?w:p:0===l?\nm:t:C;var G=0;var O=1===K?e[0].c*e[0].l:u*f.O;for(var S,T;G<O;){var U=b?Math.min(O-G,b):O;for(F=0;F<K;F++)e[F].m=0;A=0;if(1===K){var y=e[0];for(I=0;I<U;I++)R(y,64*((y.c+1)*(G/y.c|0)+G%y.c)),G++}else for(I=0;I<U;I++){for(F=0;F<K;F++)for(y=e[F],S=y.h,T=y.j,L=0;L<T;L++)for(M=0;M<S;M++)R(y,64*((y.c+1)*((G/u|0)*y.j+L)+(G%u*y.h+M)));G++}x=0;(y=N(a,d))&&y.f&&((0,_util.warn)(\"decodeScan - unexpected MCU data, current marker is: \"+y.f),d=y.offset);y=y&&y.F;if(!y||65280>=y)throw new D(\"marker was not found\");\nif(65488<=y&&65495>=y)d+=2;else break}(y=N(a,d))&&y.f&&((0,_util.warn)(\"decodeScan - unexpected Scan data, current marker is: \"+y.f),d=y.offset);return d-v}function Y(a,d){for(var f=d.c,e=d.l,b=new Int16Array(64),B=0;B<e;B++)for(var k=0;k<f;k++){var l=64*((d.c+1)*B+k),r=b,n=d.G,q=d.a;if(!n)throw new D(\"missing required Quantization Table.\");for(var h=0;64>h;h+=8){var c=q[l+h];var C=q[l+h+1];var w=q[l+h+2];var p=q[l+h+3];var m=q[l+h+4];var t=q[l+h+5];var g=q[l+h+6];var u=q[l+h+7];c*=n[h];if(0===(C|\nw|p|m|t|g|u))c=5793*c+512>>10,r[h]=c,r[h+1]=c,r[h+2]=c,r[h+3]=c,r[h+4]=c,r[h+5]=c,r[h+6]=c,r[h+7]=c;else{C*=n[h+1];w*=n[h+2];p*=n[h+3];m*=n[h+4];t*=n[h+5];g*=n[h+6];u*=n[h+7];var v=5793*c+128>>8;var z=5793*m+128>>8;var x=w;var A=g;m=2896*(C-u)+128>>8;u=2896*(C+u)+128>>8;p<<=4;t<<=4;v=v+z+1>>1;z=v-z;c=3784*x+1567*A+128>>8;x=1567*x-3784*A+128>>8;A=c;m=m+t+1>>1;t=m-t;u=u+p+1>>1;p=u-p;v=v+A+1>>1;A=v-A;z=z+x+1>>1;x=z-x;c=2276*m+3406*u+2048>>12;m=3406*m-2276*u+2048>>12;u=c;c=799*p+4017*t+2048>>12;p=4017*\np-799*t+2048>>12;t=c;r[h]=v+u;r[h+7]=v-u;r[h+1]=z+t;r[h+6]=z-t;r[h+2]=x+p;r[h+5]=x-p;r[h+3]=A+m;r[h+4]=A-m}}for(n=0;8>n;++n)c=r[n],C=r[n+8],w=r[n+16],p=r[n+24],m=r[n+32],t=r[n+40],g=r[n+48],u=r[n+56],0===(C|w|p|m|t|g|u)?(c=5793*c+8192>>14,c=-2040>c?0:2024<=c?255:c+2056>>4,q[l+n]=c,q[l+n+8]=c,q[l+n+16]=c,q[l+n+24]=c,q[l+n+32]=c,q[l+n+40]=c,q[l+n+48]=c,q[l+n+56]=c):(v=5793*c+2048>>12,z=5793*m+2048>>12,x=w,A=g,m=2896*(C-u)+2048>>12,u=2896*(C+u)+2048>>12,v=(v+z+1>>1)+4112,z=v-z,c=3784*x+1567*A+2048>>\n12,x=1567*x-3784*A+2048>>12,A=c,m=m+t+1>>1,t=m-t,u=u+p+1>>1,p=u-p,v=v+A+1>>1,A=v-A,z=z+x+1>>1,x=z-x,c=2276*m+3406*u+2048>>12,m=3406*m-2276*u+2048>>12,u=c,c=799*p+4017*t+2048>>12,p=4017*p-799*t+2048>>12,t=c,c=v+u,u=v-u,C=z+t,g=z-t,w=x+p,t=x-p,p=A+m,m=A-m,c=16>c?0:4080<=c?255:c>>4,C=16>C?0:4080<=C?255:C>>4,w=16>w?0:4080<=w?255:w>>4,p=16>p?0:4080<=p?255:p>>4,m=16>m?0:4080<=m?255:m>>4,t=16>t?0:4080<=t?255:t>>4,g=16>g?0:4080<=g?255:g>>4,u=16>u?0:4080<=u?255:u>>4,q[l+n]=c,q[l+n+8]=C,q[l+n+16]=w,q[l+n+24]=\np,q[l+n+32]=m,q[l+n+40]=t,q[l+n+48]=g,q[l+n+56]=u)}return d.a}function N(a,d){var f=2<arguments.length&&void 0!==arguments[2]?arguments[2]:d,e=a.length-1;f=f<d?f:d;if(d>=e)return null;var b=a[d]<<8|a[d+1];if(65472<=b&&65534>=b)return{f:null,F:b,offset:d};for(var B=a[f]<<8|a[f+1];!(65472<=B&&65534>=B);){if(++f>=e)return null;B=a[f]<<8|a[f+1]}return{f:b.toString(16),F:B,offset:f}}var J=new Uint8Array([0,1,8,16,9,2,3,10,17,24,32,25,18,11,4,5,12,19,26,33,40,48,41,34,27,20,13,6,7,14,21,28,35,42,49,56,\n57,50,43,36,29,22,15,23,30,37,44,51,58,59,52,45,38,31,39,46,53,60,61,54,47,55,62,63]);g.prototype={parse:function(a){function d(){var d=a[k]<<8|a[k+1];k+=2;return d}function f(){var b=d();b=k+b-2;var c=N(a,b,k);c&&c.f&&((0,_util.warn)(\"readDataBlock - incorrect length, current marker is: \"+c.f),b=c.offset);b=a.subarray(k,b);k+=b.length;return b}function e(a){for(var b=Math.ceil(a.v/8/a.s),c=Math.ceil(a.g/8/a.u),d=0;d<a.b.length;d++){v=a.b[d];var e=Math.ceil(Math.ceil(a.v/8)*v.h/a.s),f=Math.ceil(Math.ceil(a.g/\n8)*v.j/a.u);v.a=new Int16Array(64*c*v.j*(b*v.h+1));v.c=e;v.l=f}a.P=b;a.O=c}var b=(1<arguments.length&&void 0!==arguments[1]?arguments[1]:{}).N,B=void 0===b?null:b,k=0,l=null,r=0;b=[];var n=[],q=[],h=d();if(65496!==h)throw new D(\"SOI not found\");for(h=d();65497!==h;){switch(h){case 65504:case 65505:case 65506:case 65507:case 65508:case 65509:case 65510:case 65511:case 65512:case 65513:case 65514:case 65515:case 65516:case 65517:case 65518:case 65519:case 65534:var c=f();65518===h&&65===c[0]&&100===\nc[1]&&111===c[2]&&98===c[3]&&101===c[4]&&(l={version:c[5]<<8|c[6],Y:c[7]<<8|c[8],Z:c[9]<<8|c[10],W:c[11]});break;case 65499:h=d()+k-2;for(var g;k<h;){var w=a[k++],p=new Uint16Array(64);if(0===w>>4)for(c=0;64>c;c++)g=J[c],p[g]=a[k++];else if(1===w>>4)for(c=0;64>c;c++)g=J[c],p[g]=d();else throw new D(\"DQT - invalid table spec\");b[w&15]=p}break;case 65472:case 65473:case 65474:if(m)throw new D(\"Only single frame JPEGs supported\");d();var m={};m.X=65473===h;m.S=65474===h;m.precision=a[k++];h=d();m.g=\nB||h;m.v=d();m.b=[];m.C={};c=a[k++];for(h=p=w=0;h<c;h++){g=a[k];var t=a[k+1]>>4;var H=a[k+1]&15;w<t&&(w=t);p<H&&(p=H);t=m.b.push({h:t,j:H,T:a[k+2],G:null});m.C[g]=t-1;k+=3}m.s=w;m.u=p;e(m);break;case 65476:g=d();for(h=2;h<g;){w=a[k++];p=new Uint8Array(16);for(c=t=0;16>c;c++,k++)t+=p[c]=a[k];H=new Uint8Array(t);for(c=0;c<t;c++,k++)H[c]=a[k];h+=17+t;(0===w>>4?q:n)[w&15]=W(p,H)}break;case 65501:d();var u=d();break;case 65498:c=1===++r&&!B;d();w=a[k++];g=[];for(h=0;h<w;h++){p=m.C[a[k++]];var v=m.b[p];\np=a[k++];v.D=q[p>>4];v.o=n[p&15];g.push(v)}h=a[k++];w=a[k++];p=a[k++];try{var z=X(a,k,m,g,u,h,w,p>>4,p&15,c);k+=z}catch(x){if(x instanceof P)return(0,_util.warn)('Attempting to re-parse JPEG image using \"scanLines\" parameter found in DNL marker (0xFFDC) segment.'),this.parse(a,{N:x.g});throw x;}break;case 65500:k+=4;break;case 65535:255!==a[k]&&k--;break;default:if(255===a[k-3]&&192<=a[k-2]&&254>=a[k-2])k-=3;else if((c=N(a,k-2))&&c.f)(0,_util.warn)(\"JpegImage.parse - unexpected data, current marker is: \"+\nc.f),k=c.offset;else throw new D(\"unknown marker \"+h.toString(16));}h=d()}this.width=m.v;this.height=m.g;this.A=l;this.b=[];for(h=0;h<m.b.length;h++){v=m.b[h];if(u=b[v.T])v.G=u;this.b.push({R:Y(m,v),U:v.h/m.s,V:v.j/m.u,c:v.c,l:v.l})}this.i=this.b.length},L:function(a,d){var f=this.width/a,e=this.height/d,b,g,k=this.b.length,l=a*d*k,r=new Uint8ClampedArray(l),n=new Uint32Array(a);for(g=0;g<k;g++){var q=this.b[g];var h=q.U*f;var c=q.V*e;var C=g;var w=q.R;var p=q.c+1<<3;for(b=0;b<a;b++)q=0|b*h,n[b]=\n(q&4294967288)<<3|q&7;for(h=0;h<d;h++)for(q=0|h*c,q=p*(q&4294967288)|(q&7)<<3,b=0;b<a;b++)r[C]=w[q+n[b]],C+=k}if(e=this.M)for(g=0;g<l;)for(f=q=0;q<k;q++,g++,f+=2)r[g]=(r[g]*e[f]>>8)+e[f+1];return r},w:function(){return this.A?!!this.A.W:3===this.i?0===this.B?!1:!0:1===this.B?!0:!1},I:function(a){for(var d,f,e,b=0,g=a.length;b<g;b+=3)d=a[b],f=a[b+1],e=a[b+2],a[b]=d-179.456+1.402*e,a[b+1]=d+135.459-.344*f-.714*e,a[b+2]=d-226.816+1.772*f;return a},K:function(a){for(var d,f,e,b,g=0,k=0,l=a.length;k<l;k+=\n4)d=a[k],f=a[k+1],e=a[k+2],b=a[k+3],a[g++]=-122.67195406894+f*(-6.60635669420364E-5*f+4.37130475926232E-4*e-5.4080610064599E-5*d+4.8449797120281E-4*b-.154362151871126)+e*(-9.57964378445773E-4*e+8.17076911346625E-4*d-.00477271405408747*b+1.53380253221734)+d*(9.61250184130688E-4*d-.00266257332283933*b+.48357088451265)+b*(-3.36197177618394E-4*b+.484791561490776),a[g++]=107.268039397724+f*(2.19927104525741E-5*f-6.40992018297945E-4*e+6.59397001245577E-4*d+4.26105652938837E-4*b-.176491792462875)+e*(-7.78269941513683E-4*\ne+.00130872261408275*d+7.70482631801132E-4*b-.151051492775562)+d*(.00126935368114843*d-.00265090189010898*b+.25802910206845)+b*(-3.18913117588328E-4*b-.213742400323665),a[g++]=-20.810012546947+f*(-5.70115196973677E-4*f-2.63409051004589E-5*e+.0020741088115012*d-.00288260236853442*b+.814272968359295)+e*(-1.53496057440975E-5*e-1.32689043961446E-4*d+5.60833691242812E-4*b-.195152027534049)+d*(.00174418132927582*d-.00255243321439347*b+.116935020465145)+b*(-3.43531996510555E-4*b+.24165260232407);return a.subarray(0,\ng)},J:function(a){for(var d,f,e,b=0,g=a.length;b<g;b+=4)d=a[b],f=a[b+1],e=a[b+2],a[b]=434.456-d-1.402*e,a[b+1]=119.541-d+.344*f+.714*e,a[b+2]=481.816-d-1.772*f;return a},H:function(a){for(var d,f,e,b,g=0,k=1/255,l=0,r=a.length;l<r;l+=4)d=a[l]*k,f=a[l+1]*k,e=a[l+2]*k,b=a[l+3]*k,a[g++]=255+d*(-4.387332384609988*d+54.48615194189176*f+18.82290502165302*e+212.25662451639585*b-285.2331026137004)+f*(1.7149763477362134*f-5.6096736904047315*e-17.873870861415444*b-5.497006427196366)+e*(-2.5217340131683033*\ne-21.248923337353073*b+17.5119270841813)-b*(21.86122147463605*b+189.48180835922747),a[g++]=255+d*(8.841041422036149*d+60.118027045597366*f+6.871425592049007*e+31.159100130055922*b-79.2970844816548)+f*(-15.310361306967817*f+17.575251261109482*e+131.35250912493976*b-190.9453302588951)+e*(4.444339102852739*e+9.8632861493405*b-24.86741582555878)-b*(20.737325471181034*b+187.80453709719578),a[g++]=255+d*(.8842522430003296*d+8.078677503112928*f+30.89978309703729*e-.23883238689178934*b-14.183576799673286)+\nf*(10.49593273432072*f+63.02378494754052*e+50.606957656360734*b-112.23884253719248)+e*(.03296041114873217*e+115.60384449646641*b-193.58209356861505)-b*(22.33816807309886*b+180.12613974708367);return a.subarray(0,g)},getData:function(a,d,f){if(4<this.i)throw new D(\"Unsupported color mode\");a=this.L(a,d);if(1===this.i&&f){f=a.length;d=new Uint8ClampedArray(3*f);for(var e=0,b=0;b<f;b++){var g=a[b];d[e++]=g;d[e++]=g;d[e++]=g}return d}if(3===this.i&&this.w())return this.I(a);if(4===this.i){if(this.w())return f?\nthis.K(a):this.J(a);if(f)return this.H(a)}return a}}; UTIF.JpegDecoder=g})()})();\n\n//UTIF.JpegDecoder = window.JpegDecoder;\n\nUTIF.encodeImage = function(rgba, w, h, metadata)\n{\n\tvar idf = { \"t256\":[w], \"t257\":[h], \"t258\":[8,8,8,8], \"t259\":[1], \"t262\":[2], \"t273\":[1000], // strips offset\n\t\t\t\t\"t277\":[4], \"t278\":[h], /* rows per strip */          \"t279\":[w*h*4], // strip byte counts\n\t\t\t\t\"t282\":[1], \"t283\":[1], \"t284\":[1], \"t286\":[0], \"t287\":[0], \"t296\":[1], \"t305\": [\"Photopea (UTIF.js)\"], \"t338\":[1]\n\t\t};\n\tif (metadata) for (var i in metadata) idf[i] = metadata[i];\n\t\n\tvar prfx = new Uint8Array(UTIF.encode([idf]));\n\tvar img = new Uint8Array(rgba);\n\tvar data = new Uint8Array(1000+w*h*4);\n\tfor(var i=0; i<prfx.length; i++) data[i] = prfx[i];\n\tfor(var i=0; i<img .length; i++) data[1000+i] = img[i];\n\treturn data.buffer;\n}\n\nUTIF.encode = function(ifds)\n{\n\tvar data = new Uint8Array(20000), offset = 4, bin = UTIF._binBE;\n\tdata[0]=77;  data[1]=77;  data[3]=42;\n\n\tvar ifdo = 8;\n\tbin.writeUint(data, offset, ifdo);  offset+=4;\n\tfor(var i=0; i<ifds.length; i++)\n\t{\n\t\tvar noffs = UTIF._writeIFD(bin, data, ifdo, ifds[i]);\n\t\tifdo = noffs[1];\n\t\tif(i<ifds.length-1) bin.writeUint(data, noffs[0], ifdo);\n\t}\n\treturn data.slice(0, ifdo).buffer;\n}\n//UTIF.encode._writeIFD\n\nUTIF.decode = function(buff)\n{\n\tUTIF.decode._decodeG3.allow2D = null;\n\tvar data = new Uint8Array(buff), offset = 0;\n\n\tvar id = UTIF._binBE.readASCII(data, offset, 2);  offset+=2;\n\tvar bin = id==\"II\" ? UTIF._binLE : UTIF._binBE;\n\tvar num = bin.readUshort(data, offset);  offset+=2;\n\n\tvar ifdo = bin.readUint(data, offset);  offset+=4;\n\tvar ifds = [];\n\twhile(true)\n\t{\n\t\tvar noff = UTIF._readIFD(bin, data, ifdo, ifds);\n\t\t//var ifd = ifds[ifds.length-1];   if(ifd[\"t34665\"]) {  ifd.exifIFD = [];  UTIF._readIFD(bin, data, ifd[\"t34665\"][0], ifd.exifIFD);  }\n\t\tifdo = bin.readUint(data, noff);\n\t\tif(ifdo==0) break;\n\t}\n\treturn ifds;\n}\n\nUTIF.decodeImages = function(buff, ifds)\n{\n\tvar data = new Uint8Array(buff);\n\tvar id = UTIF._binBE.readASCII(data, 0, 2);\n\n\tfor(var ii=0; ii<ifds.length; ii++)\n\t{\n\t\tvar img = ifds[ii];\n\t\tif(img[\"t256\"]==null) continue;\t// EXIF files don't have TIFF tags\n\t\timg.isLE = id==\"II\";\n\t\timg.width  = img[\"t256\"][0];  //delete img[\"t256\"];\n\t\timg.height = img[\"t257\"][0];  //delete img[\"t257\"];\n\n\t\tvar cmpr   = img[\"t259\"] ? img[\"t259\"][0] : 1;  //delete img[\"t259\"];\n\t\tvar fo = img[\"t266\"] ? img[\"t266\"][0] : 1;  //delete img[\"t266\"];\n\t\tif(img[\"t284\"] && img[\"t284\"][0]==2) log(\"PlanarConfiguration 2 should not be used!\");\n\n\t\tvar bipp = (img[\"t258\"]?Math.min(32,img[\"t258\"][0]):1) * (img[\"t277\"]?img[\"t277\"][0]:1);  // bits per pixel\n\t\tvar bipl = Math.ceil(img.width*bipp/8)*8;\n\t\tvar soff = img[\"t273\"];  if(soff==null) soff = img[\"t324\"];\n\t\tvar bcnt = img[\"t279\"];  if(cmpr==1 && soff.length==1) bcnt = [img.height*(bipl>>>3)];  if(bcnt==null) bcnt = img[\"t325\"];\n\t\tvar bytes = new Uint8Array(img.height*(bipl>>>3)), bilen = 0;\n\n\t\tif(img[\"t322\"]!=null) // tiled\n\t\t{\n\t\t\tvar tw = img[\"t322\"][0], th = img[\"t323\"][0];\n\t\t\tvar tx = Math.floor((img.width  + tw - 1) / tw);\n\t\t\tvar ty = Math.floor((img.height + th - 1) / th);\n\t\t\tvar tbuff = new Uint8Array(Math.ceil(tw*th*bipp/8)|0);\n\t\t\tfor(var y=0; y<ty; y++)\n\t\t\t\tfor(var x=0; x<tx; x++)\n\t\t\t\t{\n\t\t\t\t\tvar i = y*tx+x;  for(var j=0; j<tbuff.length; j++) tbuff[j]=0;\n\t\t\t\t\tUTIF.decode._decompress(img, data, soff[i], bcnt[i], cmpr, tbuff, 0, fo);\n\t\t\t\t\t// Might be required for 7 too. Need to check\n\t\t\t\t\tif (cmpr==6) bytes = tbuff;\n\t\t\t\t\telse UTIF._copyTile(tbuff, Math.ceil(tw*bipp/8)|0, th, bytes, Math.ceil(img.width*bipp/8)|0, img.height, Math.ceil(x*tw*bipp/8)|0, y*th);\n\t\t\t\t}\n\t\t\tbilen = bytes.length*8;\n\t\t}\n\t\telse\t// stripped\n\t\t{\n\t\t\tvar rps = img[\"t278\"] ? img[\"t278\"][0] : img.height;   rps = Math.min(rps, img.height);\n\t\t\tfor(var i=0; i<soff.length; i++)\n\t\t\t{\n\t\t\t\tUTIF.decode._decompress(img, data, soff[i], bcnt[i], cmpr, bytes, Math.ceil(bilen/8)|0, fo);\n\t\t\t\tbilen += bipl * rps;\n\t\t\t}\n\t\t\tbilen = Math.min(bilen, bytes.length*8);\n\t\t}\n\t\timg.data = new Uint8Array(bytes.buffer, 0, Math.ceil(bilen/8)|0);\n\t}\n}\n\nUTIF.decode._decompress = function(img, data, off, len, cmpr, tgt, toff, fo)  // fill order\n{\n\t//console.log(\"compression\", cmpr);\n\tif(false) {}\n\telse if(cmpr==1) for(var j=0; j<len; j++) tgt[toff+j] = data[off+j];\n\telse if(cmpr==3) UTIF.decode._decodeG3 (data, off, len, tgt, toff, img.width, fo);\n\telse if(cmpr==4) UTIF.decode._decodeG4 (data, off, len, tgt, toff, img.width, fo);\n\telse if(cmpr==5) UTIF.decode._decodeLZW(data, off, tgt, toff);\n\telse if(cmpr==6) UTIF.decode._decodeOldJPEG(img, data, off, len, tgt, toff);\n\telse if(cmpr==7) UTIF.decode._decodeNewJPEG(img, data, off, len, tgt, toff);\n\telse if(cmpr==8) {  var src = new Uint8Array(data.buffer,off,len);  var bin = pako[\"inflate\"](src);  for(var i=0; i<bin.length; i++) tgt[toff+i]=bin[i];  }\n\telse if(cmpr==32773) UTIF.decode._decodePackBits(data, off, len, tgt, toff);\n\telse if(cmpr==32809) UTIF.decode._decodeThunder (data, off, len, tgt, toff);\n\t//else if(cmpr==34713) UTIF.decode._decodeNikon   (data, off, len, tgt, toff);\n\telse log(\"Unknown compression\", cmpr);\n\n\tif(img[\"t317\"] && img[\"t317\"][0]==2)\n\t{\n\t\tvar noc = (img[\"t277\"]?img[\"t277\"][0]:1), h = (img[\"t278\"] ? img[\"t278\"][0] : img.height), bpr = img.width*noc;\n\t\t//log(noc);\n\t\tfor(var y=0; y<h; y++)\n\t\t{\n\t\t\tvar ntoff = toff+y*bpr;\n\t\t\tif(noc==3) for(var j=  3; j<bpr; j+=3)\n\t\t\t{\n\t\t\t\ttgt[ntoff+j  ] = (tgt[ntoff+j  ] + tgt[ntoff+j-3])&255;\n\t\t\t\ttgt[ntoff+j+1] = (tgt[ntoff+j+1] + tgt[ntoff+j-2])&255;\n\t\t\t\ttgt[ntoff+j+2] = (tgt[ntoff+j+2] + tgt[ntoff+j-1])&255;\n\t\t\t}\n\t\t\telse for(var j=noc; j<bpr; j++) tgt[ntoff+j] = (tgt[ntoff+j] + tgt[ntoff+j-noc])&255;\n\t\t}\n\t}\n}\n\nUTIF.decode._decodeNikon = function(data, off, len, tgt, toff)\n{\n\tvar nikon_tree = [\n    [ 0,1,5,1,1,1,1,1,1,2,0,0,0,0,0,0,\t/* 12-bit lossy */\n      5,4,3,6,2,7,1,0,8,9,11,10,12 ],\n    [ 0,1,5,1,1,1,1,1,1,2,0,0,0,0,0,0,\t/* 12-bit lossy after split */\n      0x39,0x5a,0x38,0x27,0x16,5,4,3,2,1,0,11,12,12 ],\n    [ 0,1,4,2,3,1,2,0,0,0,0,0,0,0,0,0,  /* 12-bit lossless */\n      5,4,6,3,7,2,8,1,9,0,10,11,12 ],\n    [ 0,1,4,3,1,1,1,1,1,2,0,0,0,0,0,0,\t/* 14-bit lossy */\n      5,6,4,7,8,3,9,2,1,0,10,11,12,13,14 ],\n    [ 0,1,5,1,1,1,1,1,1,1,2,0,0,0,0,0,\t/* 14-bit lossy after split */\n      8,0x5c,0x4b,0x3a,0x29,7,6,5,4,3,2,1,0,13,14 ],\n    [ 0,1,4,2,2,3,1,2,0,0,0,0,0,0,0,0,\t/* 14-bit lossless */\n      7,6,8,5,9,4,10,3,11,12,2,0,1,13,14 ] ];\n\n\t//struct decode *dindex;\n\tvar ver0, ver1, vpred, hpred, csize;\n\tvar i, min, max, step=0, huff=0, split=0, row, col, len, shl, diff;\n\n\tlog(data.slice(off,off+100));\n\tver0 = data[off];  off++;\n\tver1 = data[off];  off++;\n\tlog(ver0.toString(16), ver1.toString(16), len);\n}\n\nUTIF.decode._decodeNewJPEG = function(img, data, off, len, tgt, toff)\n{\n\tvar tables = img[\"t347\"], tlen = tables ? tables.length : 0, buff = new Uint8Array(tlen + len);\n\t\n\tif (tables)\n\t{\n\t\tvar SOI = 216, EOI = 217, boff = 0;\n\t\tfor (var i=0; i<(tlen-1); i++)\n\t\t{\n\t\t\t// Skip EOI marker from JPEGTables\n\t\t\tif (tables[i]==255 && tables[i+1]==EOI) break;\n\t\t\tbuff[boff++] = tables[i];\n\t\t}\n\n\t\t// Skip SOI marker from data\n\t\tvar byte1 = data[off], byte2 = data[off + 1];\n\t\tif (byte1!=255 || byte2!=SOI)\n\t\t{\n\t\t\tbuff[boff++] = byte1;\n\t\t\tbuff[boff++] = byte2;\n\t\t}\n\t\tfor (var i=2; i<len; i++) buff[boff++] = data[off+i];\n\t}\n\telse for (var i=0; i<len; i++) buff[i] = data[off+i];\n\n\tif(img[\"t262\"]==32803) // lossless JPEG (used in DNG files) is not available in JpegDecoder.\n\t{\n\t\tvar bps = img[\"t258\"][0], dcdr = new LosslessJpegDecoder();\n\t\tvar out = dcdr.decode(buff), olen=out.length;\n\n\t\tif(false) {}\n\t\telse if(bps==16) for(var i=0; i<olen; i++) {  tgt[toff++] = (out[i]&255);  tgt[toff++] = (out[i]>>>8);  }\n\t\telse if(bps==12) for(var i=0; i<olen; i+=2) {  tgt[toff++] = (out[i]>>>4);  tgt[toff++] = ((out[i]<<4)|(out[i+1]>>>8))&255;  tgt[toff++] = out[i+1]&255;  }\n\t\telse throw new Error(\"unsupported bit depth \"+bps);\n\t}\n\telse\n\t{\n\t\tvar parser = new UTIF.JpegDecoder();  parser.parse(buff);\n\t\tvar decoded = parser.getData(parser.width, parser.height);\n\t\tfor (var i=0; i<decoded.length; i++) tgt[toff + i] = decoded[i];\n\t}\n\n\t// PhotometricInterpretation is 6 (YCbCr) for JPEG, but after decoding we populate data in\n\t// RGB format, so updating the tag value\n\tif(img[\"t262\"][0] == 6)  img[\"t262\"][0] = 2;\n}\n\nUTIF.decode._decodeOldJPEGInit = function(img, data, off, len)\n{\n\tvar SOI = 216, EOI = 217, DQT = 219, DHT = 196, DRI = 221, SOF0 = 192, SOS = 218;\n\tvar joff = 0, soff = 0, tables, sosMarker, isTiled = false, i, j, k;\n\tvar jpgIchgFmt    = img[\"t513\"], jifoff = jpgIchgFmt ? jpgIchgFmt[0] : 0;\n\tvar jpgIchgFmtLen = img[\"t514\"], jiflen = jpgIchgFmtLen ? jpgIchgFmtLen[0] : 0;\n\tvar soffTag       = img[\"t324\"] || img[\"t273\"] || jpgIchgFmt;\n\tvar ycbcrss       = img[\"t530\"], ssx = 0, ssy = 0;\n\tvar spp           = img[\"t277\"]?img[\"t277\"][0]:1;\n\tvar jpgresint     = img[\"t515\"];\n\n\tif(soffTag)\n\t{\n\t\tsoff = soffTag[0];\n\t\tisTiled = (soffTag.length > 1);\n\t}\n\n\tif(!isTiled)\n\t{\n\t\tif(data[off]==255 && data[off+1]==SOI) return { jpegOffset: off };\n\t\tif(jpgIchgFmt!=null)\n\t\t{\n\t\t\tif(data[off+jifoff]==255 && data[off+jifoff+1]==SOI) joff = off+jifoff;\n\t\t\telse log(\"JPEGInterchangeFormat does not point to SOI\");\n\n\t\t\tif(jpgIchgFmtLen==null) log(\"JPEGInterchangeFormatLength field is missing\");\n\t\t\telse if(jifoff >= soff || (jifoff+jiflen) <= soff) log(\"JPEGInterchangeFormatLength field value is invalid\");\n\n\t\t\tif(joff != null) return { jpegOffset: joff };\n\t\t}\n\t}\n\n\tif(ycbcrss!=null) {  ssx = ycbcrss[0];  ssy = ycbcrss[1];  }\n\n\tif(jpgIchgFmt!=null)\n\t\tif(jpgIchgFmtLen!=null)\n\t\t\tif(jiflen >= 2 && (jifoff+jiflen) <= soff)\n\t\t\t{\n\t\t\t\tif(data[off+jifoff+jiflen-2]==255 && data[off+jifoff+jiflen-1]==SOI) tables = new Uint8Array(jiflen-2);\n\t\t\t\telse tables = new Uint8Array(jiflen);\n\n\t\t\t\tfor(i=0; i<tables.length; i++) tables[i] = data[off+jifoff+i];\n\t\t\t\tlog(\"Incorrect JPEG interchange format: using JPEGInterchangeFormat offset to derive tables\");\n\t\t\t}\n\t\t\telse log(\"JPEGInterchangeFormat+JPEGInterchangeFormatLength > offset to first strip or tile\");\n\n\tif(tables == null)\n\t{\n\t\tvar ooff = 0, out = [];\n\t\tout[ooff++] = 255; out[ooff++] = SOI;\n\n\t\tvar qtables = img[\"t519\"];\n\t\tif(qtables==null) throw new Error(\"JPEGQTables tag is missing\");\n\t\tfor(i=0; i<qtables.length; i++)\n\t\t{\n\t\t\tout[ooff++] = 255; out[ooff++] = DQT; out[ooff++] = 0; out[ooff++] = 67; out[ooff++] = i;\n\t\t\tfor(j=0; j<64; j++) out[ooff++] = data[off+qtables[i]+j];\n\t\t}\n\n\t\tfor(k=0; k<2; k++)\n\t\t{\n\t\t\tvar htables = img[(k == 0) ? \"t520\" : \"t521\"];\n\t\t\tif(htables==null) throw new Error(((k == 0) ? \"JPEGDCTables\" : \"JPEGACTables\") + \" tag is missing\");\n\t\t\tfor(i=0; i<htables.length; i++)\n\t\t\t{\n\t\t\t\tout[ooff++] = 255; out[ooff++] = DHT;\n\t\t\t\t//out[ooff++] = 0; out[ooff++] = 67; out[ooff++] = i;\n\t\t\t\tvar nc = 19;\n\t\t\t\tfor(j=0; j<16; j++) nc += data[off+htables[i]+j];\n\n\t\t\t\tout[ooff++] = (nc >>> 8); out[ooff++] = nc & 255;\n\t\t\t\tout[ooff++] = (i | (k << 4));\n\t\t\t\tfor(j=0; j<16; j++) out[ooff++] = data[off+htables[i]+j];\n\t\t\t\tfor(j=0; j<nc; j++) out[ooff++] = data[off+htables[i]+16+j];\n\t\t\t}\n\t\t}\n\n\t\tout[ooff++] = 255; out[ooff++] = SOF0;\n\t\tout[ooff++] = 0;  out[ooff++] = 8 + 3*spp;  out[ooff++] = 8;\n\t\tout[ooff++] = (img.height >>> 8) & 255;  out[ooff++] = img.height & 255;\n\t\tout[ooff++] = (img.width  >>> 8) & 255;  out[ooff++] = img.width  & 255;\n\t\tout[ooff++] = spp;\n\t\tif(spp==1) {  out[ooff++] = 1;  out[ooff++] = 17;  out[ooff++] = 0;  }\n\t\telse for(i=0; i<3; i++)\n\t\t{\n\t\t\tout[ooff++] = i + 1;\n\t\t\tout[ooff++] = (i != 0) ? 17 : (((ssx & 15) << 4) | (ssy & 15));\n\t\t\tout[ooff++] = i;\n\t\t}\n\n\t\tif(jpgresint!=null && jpgresint[0]!=0)\n\t\t{\n\t\t\tout[ooff++] = 255;  out[ooff++] = DRI;  out[ooff++] = 0;  out[ooff++] = 4;\n\t\t\tout[ooff++] = (jpgresint[0] >>> 8) & 255;\n\t\t\tout[ooff++] = jpgresint[0] & 255;\n\t\t}\n\n\t\ttables = new Uint8Array(out);\n\t}\n\n\tvar sofpos = -1;\n\ti = 0;\n\twhile(i < (tables.length - 1)) {\n\t\tif(tables[i]==255 && tables[i+1]==SOF0) {  sofpos = i; break;  }\n\t\ti++;\n\t}\n\n\tif(sofpos == -1)\n\t{\n\t\tvar tmptab = new Uint8Array(tables.length + 10 + 3*spp);\n\t\ttmptab.set(tables);\n\t\tvar tmpoff = tables.length;\n\t\tsofpos = tables.length;\n\t\ttables = tmptab;\n\n\t\ttables[tmpoff++] = 255; tables[tmpoff++] = SOF0;\n\t\ttables[tmpoff++] = 0;  tables[tmpoff++] = 8 + 3*spp;  tables[tmpoff++] = 8;\n\t\ttables[tmpoff++] = (img.height >>> 8) & 255;  tables[tmpoff++] = img.height & 255;\n\t\ttables[tmpoff++] = (img.width  >>> 8) & 255;  tables[tmpoff++] = img.width  & 255;\n\t\ttables[tmpoff++] = spp;\n\t\tif(spp==1) {  tables[tmpoff++] = 1;  tables[tmpoff++] = 17;  tables[tmpoff++] = 0;  }\n\t\telse for(i=0; i<3; i++)\n\t\t{\n\t\t\ttables[tmpoff++] = i + 1;\n\t\t\ttables[tmpoff++] = (i != 0) ? 17 : (((ssx & 15) << 4) | (ssy & 15));\n\t\t\ttables[tmpoff++] = i;\n\t\t}\n\t}\n\n\tif(data[soff]==255 && data[soff+1]==SOS)\n\t{\n\t\tvar soslen = (data[soff+2]<<8) | data[soff+3];\n\t\tsosMarker = new Uint8Array(soslen+2);\n\t\tsosMarker[0] = data[soff];  sosMarker[1] = data[soff+1]; sosMarker[2] = data[soff+2];  sosMarker[3] = data[soff+3];\n\t\tfor(i=0; i<(soslen-2); i++) sosMarker[i+4] = data[soff+i+4];\n\t}\n\telse\n\t{\n\t\tsosMarker = new Uint8Array(2 + 6 + 2*spp);\n\t\tvar sosoff = 0;\n\t\tsosMarker[sosoff++] = 255;  sosMarker[sosoff++] = SOS;\n\t\tsosMarker[sosoff++] = 0;  sosMarker[sosoff++] = 6 + 2*spp;  sosMarker[sosoff++] = spp;\n\t\tif(spp==1) {  sosMarker[sosoff++] = 1;  sosMarker[sosoff++] = 0;  }\n\t\telse for(i=0; i<3; i++)\n\t\t{\n\t\t\tsosMarker[sosoff++] = i+1;  sosMarker[sosoff++] = (i << 4) | i;\n\t\t}\n\t\tsosMarker[sosoff++] = 0;  sosMarker[sosoff++] = 63;  sosMarker[sosoff++] = 0;\n\t}\n\n\treturn { jpegOffset: off, tables: tables, sosMarker: sosMarker, sofPosition: sofpos };\n}\n\nUTIF.decode._decodeOldJPEG = function(img, data, off, len, tgt, toff)\n{\n\tvar i, dlen, tlen, buff, buffoff;\n\tvar jpegData = UTIF.decode._decodeOldJPEGInit(img, data, off, len);\n\n\tif(jpegData.jpegOffset!=null)\n\t{\n\t\tdlen = off+len-jpegData.jpegOffset;\n\t\tbuff = new Uint8Array(dlen);\n\t\tfor(i=0; i<dlen; i++) buff[i] = data[jpegData.jpegOffset+i];\n\t}\n\telse\n\t{\n\t\ttlen = jpegData.tables.length;\n\t\tbuff = new Uint8Array(tlen + jpegData.sosMarker.length + len + 2);\n\t\tbuff.set(jpegData.tables);\n\t\tbuffoff = tlen;\n\n\t\tbuff[jpegData.sofPosition+5] = (img.height >>> 8) & 255;  buff[jpegData.sofPosition+6] = img.height & 255;\n\t\tbuff[jpegData.sofPosition+7] = (img.width  >>> 8) & 255;  buff[jpegData.sofPosition+8] = img.width  & 255;\n\n\t\tif(data[off]!=255 || data[off+1]!=SOS)\n\t\t{\n\t\t\tbuff.set(jpegData.sosMarker, bufoff);\n\t\t\tbufoff += sosMarker.length;\n\t\t}\n\t\tfor(i=0; i<len; i++) buff[bufoff++] = data[off+i];\n\t\tbuff[bufoff++] = 255;  buff[bufoff++] = EOI;\n\t}\n\n\tvar parser = new UTIF.JpegDecoder();  parser.parse(buff);\n\tvar decoded = parser.getData(parser.width, parser.height);\n\tfor (var i=0; i<decoded.length; i++) tgt[toff + i] = decoded[i];\n\n\t// PhotometricInterpretation is 6 (YCbCr) for JPEG, but after decoding we populate data in\n\t// RGB format, so updating the tag value\n\tif(img[\"t262\"][0] == 6)  img[\"t262\"][0] = 2;\n}\n\nUTIF.decode._decodePackBits = function(data, off, len, tgt, toff)\n{\n\tvar sa = new Int8Array(data.buffer), ta = new Int8Array(tgt.buffer), lim = off+len;\n\twhile(off<lim)\n\t{\n\t\tvar n = sa[off];  off++;\n\t\tif(n>=0  && n<128)    for(var i=0; i< n+1; i++) {  ta[toff]=sa[off];  toff++;  off++;   }\n\t\tif(n>=-127 && n<0) {  for(var i=0; i<-n+1; i++) {  ta[toff]=sa[off];  toff++;           }  off++;  }\n\t}\n}\n\nUTIF.decode._decodeThunder = function(data, off, len, tgt, toff)\n{\n\tvar d2 = [ 0, 1, 0, -1 ],  d3 = [ 0, 1, 2, 3, 0, -3, -2, -1 ];\n\tvar lim = off+len, qoff = toff*2, px = 0;\n\twhile(off<lim)\n\t{\n\t\tvar b = data[off], msk = (b>>>6), n = (b&63);  off++;\n\t\tif(msk==3) { px=(n&15);  tgt[qoff>>>1] |= (px<<(4*(1-qoff&1)));  qoff++;   }\n\t\tif(msk==0) for(var i=0; i<n; i++) {  tgt[qoff>>>1] |= (px<<(4*(1-qoff&1)));  qoff++;   }\n\t\tif(msk==2) for(var i=0; i<2; i++) {  var d=(n>>>(3*(1-i)))&7;  if(d!=4) { px+=d3[d];  tgt[qoff>>>1] |= (px<<(4*(1-qoff&1)));  qoff++; }  }\n\t\tif(msk==1) for(var i=0; i<3; i++) {  var d=(n>>>(2*(2-i)))&3;  if(d!=2) { px+=d2[d];  tgt[qoff>>>1] |= (px<<(4*(1-qoff&1)));  qoff++; }  }\n\t}\n}\n\nUTIF.decode._dmap = { \"1\":0,\"011\":1,\"000011\":2,\"0000011\":3, \"010\":-1,\"000010\":-2,\"0000010\":-3  };\nUTIF.decode._lens = ( function()\n{\n\tvar addKeys = function(lens, arr, i0, inc) {  for(var i=0; i<arr.length; i++) lens[arr[i]] = i0 + i*inc;  }\n\n\tvar termW = \"00110101,000111,0111,1000,1011,1100,1110,1111,10011,10100,00111,01000,001000,000011,110100,110101,\" // 15\n\t+ \"101010,101011,0100111,0001100,0001000,0010111,0000011,0000100,0101000,0101011,0010011,0100100,0011000,00000010,00000011,00011010,\" // 31\n\t+ \"00011011,00010010,00010011,00010100,00010101,00010110,00010111,00101000,00101001,00101010,00101011,00101100,00101101,00000100,00000101,00001010,\" // 47\n\t+ \"00001011,01010010,01010011,01010100,01010101,00100100,00100101,01011000,01011001,01011010,01011011,01001010,01001011,00110010,00110011,00110100\";\n\n\tvar termB = \"0000110111,010,11,10,011,0011,0010,00011,000101,000100,0000100,0000101,0000111,00000100,00000111,000011000,\" // 15\n\t+ \"0000010111,0000011000,0000001000,00001100111,00001101000,00001101100,00000110111,00000101000,00000010111,00000011000,000011001010,000011001011,000011001100,000011001101,000001101000,000001101001,\" // 31\n\t+ \"000001101010,000001101011,000011010010,000011010011,000011010100,000011010101,000011010110,000011010111,000001101100,000001101101,000011011010,000011011011,000001010100,000001010101,000001010110,000001010111,\" // 47\n\t+ \"000001100100,000001100101,000001010010,000001010011,000000100100,000000110111,000000111000,000000100111,000000101000,000001011000,000001011001,000000101011,000000101100,000001011010,000001100110,000001100111\";\n\n\tvar makeW = \"11011,10010,010111,0110111,00110110,00110111,01100100,01100101,01101000,01100111,011001100,011001101,011010010,011010011,011010100,011010101,011010110,\"\n\t+ \"011010111,011011000,011011001,011011010,011011011,010011000,010011001,010011010,011000,010011011\";\n\n\tvar makeB = \"0000001111,000011001000,000011001001,000001011011,000000110011,000000110100,000000110101,0000001101100,0000001101101,0000001001010,0000001001011,0000001001100,\"\n\t+ \"0000001001101,0000001110010,0000001110011,0000001110100,0000001110101,0000001110110,0000001110111,0000001010010,0000001010011,0000001010100,0000001010101,0000001011010,\"\n\t+ \"0000001011011,0000001100100,0000001100101\";\n\n\tvar makeA = \"00000001000,00000001100,00000001101,000000010010,000000010011,000000010100,000000010101,000000010110,000000010111,000000011100,000000011101,000000011110,000000011111\";\n\n\ttermW = termW.split(\",\");  termB = termB.split(\",\");  makeW = makeW.split(\",\");  makeB = makeB.split(\",\");  makeA = makeA.split(\",\");\n\n\tvar lensW = {}, lensB = {};\n\taddKeys(lensW, termW, 0, 1);  addKeys(lensW, makeW, 64,64);  addKeys(lensW, makeA, 1792,64);\n\taddKeys(lensB, termB, 0, 1);  addKeys(lensB, makeB, 64,64);  addKeys(lensB, makeA, 1792,64);\n\treturn [lensW, lensB];\n} )();\n\nUTIF.decode._decodeG4 = function(data, off, slen, tgt, toff, w, fo)\n{\n\tvar U = UTIF.decode, boff=off<<3, len=0, wrd=\"\";\t// previous starts with 1\n\tvar line=[], pline=[];  for(var i=0; i<w; i++) pline.push(0);  pline=U._makeDiff(pline);\n\tvar a0=0, a1=0, a2=0, b1=0, b2=0, clr=0;\n\tvar y=0, mode=\"\", toRead=0;\n\tvar bipl = Math.ceil(w/8)*8;\n\n\twhile((boff>>>3)<off+slen)\n\t{\n\t\tb1 = U._findDiff(pline, a0+(a0==0?0:1), 1-clr), b2 = U._findDiff(pline, b1, clr);\t// could be precomputed\n\t\tvar bit =0;\n\t\tif(fo==1) bit = (data[boff>>>3]>>>(7-(boff&7)))&1;\n\t\tif(fo==2) bit = (data[boff>>>3]>>>(  (boff&7)))&1;\n\t\tboff++;  wrd+=bit;\n\t\tif(mode==\"H\")\n\t\t{\n\t\t\tif(U._lens[clr][wrd]!=null)\n\t\t\t{\n\t\t\t\tvar dl=U._lens[clr][wrd];  wrd=\"\";  len+=dl;\n\t\t\t\tif(dl<64) {  U._addNtimes(line,len,clr);  a0+=len;  clr=1-clr;  len=0;  toRead--;  if(toRead==0) mode=\"\";  }\n\t\t\t}\n\t\t}\n\t\telse\n\t\t{\n\t\t\tif(wrd==\"0001\")  {  wrd=\"\";  U._addNtimes(line,b2-a0,clr);  a0=b2;   }\n\t\t\tif(wrd==\"001\" )  {  wrd=\"\";  mode=\"H\";  toRead=2;  }\n\t\t\tif(U._dmap[wrd]!=null) {  a1 = b1+U._dmap[wrd];  U._addNtimes(line, a1-a0, clr);  a0=a1;  wrd=\"\";  clr=1-clr;  }\n\t\t}\n\t\tif(line.length==w && mode==\"\")\n\t\t{\n\t\t\tU._writeBits(line, tgt, toff*8+y*bipl);\n\t\t\tclr=0;  y++;  a0=0;\n\t\t\tpline=U._makeDiff(line);  line=[];\n\t\t}\n\t\t//if(wrd.length>150) {  log(wrd);  break;  throw \"e\";  }\n\t}\n}\n\nUTIF.decode._findDiff = function(line, x, clr) {  for(var i=0; i<line.length; i+=2) if(line[i]>=x && line[i+1]==clr)  return line[i];  }\n\nUTIF.decode._makeDiff = function(line)\n{\n\tvar out = [];  if(line[0]==1) out.push(0,1);\n\tfor(var i=1; i<line.length; i++) if(line[i-1]!=line[i]) out.push(i, line[i]);\n\tout.push(line.length,0,line.length,1);  return out;\n}\n\nUTIF.decode._decodeG3 = function(data, off, slen, tgt, toff, w, fo)\n{\n\tvar U = UTIF.decode, boff=off<<3, len=0, wrd=\"\";\n\tvar line=[], pline=[];  for(var i=0; i<w; i++) line.push(0);\n\tvar a0=0, a1=0, a2=0, b1=0, b2=0, clr=0;\n\tvar y=-1, mode=\"\", toRead=0, is1D=false;\n\tvar bipl = Math.ceil(w/8)*8;\n\twhile((boff>>>3)<off+slen)\n\t{\n\t\tb1 = U._findDiff(pline, a0+(a0==0?0:1), 1-clr), b2 = U._findDiff(pline, b1, clr);\t// could be precomputed\n\t\tvar bit =0;\n\t\tif(fo==1) bit = (data[boff>>>3]>>>(7-(boff&7)))&1;\n\t\tif(fo==2) bit = (data[boff>>>3]>>>(  (boff&7)))&1;\n\t\tboff++;  wrd+=bit;\n\n\t\tif(is1D)\n\t\t{\n\t\t\tif(U._lens[clr][wrd]!=null)\n\t\t\t{\n\t\t\t\tvar dl=U._lens[clr][wrd];  wrd=\"\";  len+=dl;\n\t\t\t\tif(dl<64) {  U._addNtimes(line,len,clr);  clr=1-clr;  len=0;  }\n\t\t\t}\n\t\t}\n\t\telse\n\t\t{\n\t\t\tif(mode==\"H\")\n\t\t\t{\n\t\t\t\tif(U._lens[clr][wrd]!=null)\n\t\t\t\t{\n\t\t\t\t\tvar dl=U._lens[clr][wrd];  wrd=\"\";  len+=dl;\n\t\t\t\t\tif(dl<64) {  U._addNtimes(line,len,clr);  a0+=len;  clr=1-clr;  len=0;  toRead--;  if(toRead==0) mode=\"\";  }\n\t\t\t\t}\n\t\t\t}\n\t\t\telse\n\t\t\t{\n\t\t\t\tif(wrd==\"0001\")  {  wrd=\"\";  U._addNtimes(line,b2-a0,clr);  a0=b2;   }\n\t\t\t\tif(wrd==\"001\" )  {  wrd=\"\";  mode=\"H\";  toRead=2;  }\n\t\t\t\tif(U._dmap[wrd]!=null) {  a1 = b1+U._dmap[wrd];  U._addNtimes(line, a1-a0, clr);  a0=a1;  wrd=\"\";  clr=1-clr;  }\n\t\t\t}\n\t\t}\n\t\tif(wrd.endsWith(\"000000000001\")) // needed for some files\n\t\t{\n\t\t\tif(y>=0) U._writeBits(line, tgt, toff*8+y*bipl);\n\t\t\tif(fo==1) is1D = ((data[boff>>>3]>>>(7-(boff&7)))&1)==1;\n\t\t\tif(fo==2) is1D = ((data[boff>>>3]>>>(  (boff&7)))&1)==1;\n\t\t\tboff++;\n\t\t\tif(U._decodeG3.allow2D==null) U._decodeG3.allow2D=is1D;\n\t\t\tif(!U._decodeG3.allow2D) {  is1D = true;  boff--;  }\n\t\t\t//log(\"EOL\",y, \"next 1D:\", is1D);\n\t\t\twrd=\"\";  clr=0;  y++;  a0=0;\n\t\t\tpline=U._makeDiff(line);  line=[];\n\t\t}\n\t}\n\tif(line.length==w) U._writeBits(line, tgt, toff*8+y*bipl);\n}\n\nUTIF.decode._addNtimes = function(arr, n, val) {  for(var i=0; i<n; i++) arr.push(val);  }\n\nUTIF.decode._writeBits = function(bits, tgt, boff)\n{\n\tfor(var i=0; i<bits.length; i++) tgt[(boff+i)>>>3] |= (bits[i]<<(7-((boff+i)&7)));\n}\n\nUTIF.decode._decodeLZW = function(data, off, tgt, toff)\n{\n\tif(UTIF.decode._lzwTab==null)\n\t{\n\t\tvar tb=new Uint32Array(0xffff), tn=new Uint16Array(0xffff), chr=new Uint8Array(2e6);\n\t\tfor(var i=0; i<256; i++) { chr[i<<2]=i;  tb[i]=i<<2;  tn[i]=1;  }\n\t\tUTIF.decode._lzwTab = [tb,tn,chr];\n\t}\n\tvar copy = UTIF.decode._copyData;\n\tvar tab = UTIF.decode._lzwTab[0], tln=UTIF.decode._lzwTab[1], chr=UTIF.decode._lzwTab[2], totl = 258, chrl = 258<<2;\n\tvar bits = 9, boff = off<<3;  // offset in bits\n\n\tvar ClearCode = 256, EoiCode = 257;\n\tvar v = 0, Code = 0, OldCode = 0;\n\twhile(true)\n\t{\n\t\tv = (data[boff>>>3]<<16) | (data[(boff+8)>>>3]<<8) | data[(boff+16)>>>3];\n\t\tCode = ( v>>(24-(boff&7)-bits) )    &   ((1<<bits)-1);  boff+=bits;\n\n\t\tif(Code==EoiCode) break;\n\t\tif(Code==ClearCode)\n\t\t{\n\t\t\tbits=9;  totl = 258;  chrl = 258<<2;\n\n\t\t\tv = (data[boff>>>3]<<16) | (data[(boff+8)>>>3]<<8) | data[(boff+16)>>>3];\n\t\t\tCode = ( v>>(24-(boff&7)-bits) )    &   ((1<<bits)-1);  boff+=bits;\n\t\t\tif(Code==EoiCode) break;\n\t\t\ttgt[toff]=Code;  toff++;\n\t\t}\n\t\telse if(Code<totl)\n\t\t{\n\t\t\tvar cd = tab[Code], cl = tln[Code];\n\t\t\tcopy(chr,cd,tgt,toff,cl);  toff += cl;\n\n\t\t\tif(OldCode>=totl) {  tab[totl] = chrl;  chr[tab[totl]] = cd[0];  tln[totl]=1;  chrl=(chrl+1+3)&~0x03;  totl++;  }\n\t\t\telse\n\t\t\t{\n\t\t\t\ttab[totl] = chrl;\n\t\t\t\tvar nit = tab[OldCode], nil = tln[OldCode];\n\t\t\t\tcopy(chr,nit,chr,chrl,nil);\n\t\t\t\tchr[chrl+nil]=chr[cd];  nil++;\n\t\t\t\ttln[totl]=nil;  totl++;\n\n\t\t\t\tchrl=(chrl+nil+3)&~0x03;\n\t\t\t}\n\t\t\tif(totl+1==(1<<bits)) bits++;\n\t\t}\n\t\telse\n\t\t{\n\t\t\tif(OldCode>=totl) {  tab[totl] = chrl;  tln[totl]=0;  totl++;  }\n\t\t\telse\n\t\t\t{\n\t\t\t\ttab[totl] = chrl;\n\t\t\t\tvar nit = tab[OldCode], nil = tln[OldCode];\n\t\t\t\tcopy(chr,nit,chr,chrl,nil);\n\t\t\t\tchr[chrl+nil]=chr[chrl];  nil++;\n\t\t\t\ttln[totl]=nil;  totl++;\n\n\t\t\t\tcopy(chr,chrl,tgt,toff,nil);  toff += nil;\n\t\t\t\tchrl=(chrl+nil+3)&~0x03;\n\t\t\t}\n\t\t\tif(totl+1==(1<<bits)) bits++;\n\t\t}\n\t\tOldCode = Code;\n\t}\n}\n\nUTIF.decode._copyData = function(s,so,t,to,l) {  for(var i=0;i<l;i+=4) {  t[to+i]=s[so+i];  t[to+i+1]=s[so+i+1];  t[to+i+2]=s[so+i+2];  t[to+i+3]=s[so+i+3];  }  }\n\nUTIF.tags = {254:\"NewSubfileType\",255:\"SubfileType\",256:\"ImageWidth\",257:\"ImageLength\",258:\"BitsPerSample\",259:\"Compression\",262:\"PhotometricInterpretation\",266:\"FillOrder\",\n\t\t\t269:\"DocumentName\",270:\"ImageDescription\",271:\"Make\",272:\"Model\",273:\"StripOffset\",274:\"Orientation\",277:\"SamplesPerPixel\",278:\"RowsPerStrip\",\n\t\t\t279:\"StripByteCounts\",280:\"MinSampleValue\",281:\"MaxSampleValue\",282:\"XResolution\",283:\"YResolution\",284:\"PlanarConfiguration\",285:\"PageName\",\n\t\t\t286:\"XPosition\",287:\"YPosition\",\n\t\t\t292:\"T4Options\",296:\"ResolutionUnit\",297:\"PageNumber\",305:\"Software\",306:\"DateTime\",\n\t\t\t315:\"Artist\",316:\"HostComputer\",317:\"Predictor\",318:\"WhitePoint\",319:\"PrimaryChromaticities\",320:\"ColorMap\",\n\t\t\t321:\"HalftoneHints\",322:\"TileWidth\",\n\t\t\t323:\"TileLength\",324:\"TileOffset\",325:\"TileByteCounts\",330:\"SubIFDs\",336:\"DotRange\",338:\"ExtraSample\",339:\"SampleFormat\", 347:\"JPEGTables\",\n\t\t\t512:\"JPEGProc\",513:\"JPEGInterchangeFormat\",514:\"JPEGInterchangeFormatLength\",519:\"JPEGQTables\",520:\"JPEGDCTables\",521:\"JPEGACTables\",\n\t\t\t529:\"YCbCrCoefficients\",530:\"YCbCrSubSampling\",531:\"YCbCrPositioning\",532:\"ReferenceBlackWhite\",700:\"XMP\",\n\t\t\t33421:\"CFARepeatPatternDim\",33422:\"CFAPattern\",33432:\"Copyright\",33434:\"ExposureTime\",33437:\"FNumber\",33723:\"IPTC/NAA\",34377:\"Photoshop\",\n\t\t\t34665:\"ExifIFD\",34675:\"ICC Profile\",34850:\"ExposureProgram\",34853:\"GPSInfo\",34855:\"ISOSpeedRatings\",34858:\"TimeZoneOffset\",34859:\"SelfTimeMode\",\n\t\t\t36867:\"DateTimeOriginal\",36868:\"DateTimeDigitized\",\n\t\t\t37377:\"ShutterSpeedValue\",37378:\"ApertureValue\",37380:\"ExposureBiasValue\",37383:\"MeteringMode\",37385:\"Flash\",37386:\"FocalLength\",\n\t\t\t37390:\"FocalPlaneXResolution\",37391:\"FocalPlaneYResolution\",37392:\"FocalPlaneResolutionUnit\",37393:\"ImageNumber\",37398:\"TIFF/EPStandardID\",37399:\"SensingMethod\",\n\t\t\t37500:\"MakerNote\",37510:\"UserComment\",37724:\"ImageSourceData\",\n\t\t\t40092:\"XPComment\",40094:\"XPKeywords\",\n\t\t\t40961:\"ColorSpace\",40962:\"PixelXDimension\",40963:\"PixelXDimension\",41486:\"FocalPlaneXResolution\",41487:\"FocalPlaneYResolution\",41488:\"FocalPlaneResolutionUnit\",\n\t\t\t41985:\"CustomRendered\",41986:\"ExposureMode\",41987:\"WhiteBalance\",41990:\"SceneCaptureType\",\n\t\t\t50706:\"DNGVersion\",50707:\"DNGBackwardVersion\",50708:\"UniqueCameraModel\",50709:\"LocalizedCameraModel\",50710:\"CFAPlaneColor\",\n\t\t\t50711:\"CFALayout\",50712:\"LinearizationTable\",50713:\"BlackLevelRepeatDim\",50714:\"BlackLevel\",50716:\"BlackLevelDeltaV\",50717:\"WhiteLevel\",\n\t\t\t50718:\"DefaultScale\",50719:\"DefaultCropOrigin\",\n\t\t\t50720:\"DefaultCropSize\",50733:\"BayerGreenSplit\",50738:\"AntiAliasStrength\",\n\t\t\t50721:\"ColorMatrix1\",50722:\"ColorMatrix2\",50723:\"CameraCalibration1\",50724:\"CameraCalibration2\",50727:\"AnalogBalance\",50728:\"AsShotNeutral\",\n\t\t\t50730:\"BaselineExposure\",50731:\"BaselineNoise\",50732:\"BaselineSharpness\",50734:\"LinearResponseLimit\",50735:\"CameraSerialNumber\",50736:\"LensInfo\",50739:\"ShadowScale\",\n\t\t\t50740:\"DNGPrivateData\",50741:\"MakerNoteSafety\",50778:\"CalibrationIlluminant1\",50779:\"CalibrationIlluminant2\",50780:\"BestQualityScale\",\n\t\t\t50781:\"RawDataUniqueID\",50827:\"OriginalRawFileName\",50829:\"ActiveArea\",50830:\"MaskedAreas\",50931:\"CameraCalibrationSignature\",50932:\"ProfileCalibrationSignature\",\n\t\t\t50935:\"NoiseReductionApplied\",50936:\"ProfileName\",50937:\"ProfileHueSatMapDims\",50938:\"ProfileHueSatMapData1\",50939:\"ProfileHueSatMapData2\",\n\t\t\t50940:\"ProfileToneCurve\",50941:\"ProfileEmbedPolicy\",50942:\"ProfileCopyright\",\n\t\t\t50964:\"ForwardMatrix1\",50965:\"ForwardMatrix2\",50966:\"PreviewApplicationName\",50967:\"PreviewApplicationVersion\",50969:\"PreviewSettingsDigest\",\n\t\t\t50970:\"PreviewColorSpace\",50971:\"PreviewDateTime\",50972:\"RawImageDigest\",\n\t\t\t51008:\"OpcodeList1\",51009:\"OpcodeList2\",51022:\"OpcodeList3\",51041:\"NoiseProfile\",51089:\"OriginalDefaultFinalSize\",\n\t\t\t51090:\"OriginalBestQualityFinalSize\",51091:\"OriginalDefaultCropSize\",51125:\"DefaultUserCrop\"};\n\nUTIF.ttypes = {  256:3,257:3,258:3,   259:3, 262:3,  273:4,  274:3, 277:3,278:4,279:4, 282:5, 283:5, 284:3, 286:5,287:5, 296:3, 305:2, 306:2, 338:3, 513:4, 514:4, 34665:4  };\n\nUTIF._readIFD = function(bin, data, offset, ifds)\n{\n\tvar cnt = bin.readUshort(data, offset);  offset+=2;\n\tvar ifd = {};  ifds.push(ifd);\n\n\t//log(\">>>----------------\");\n\tfor(var i=0; i<cnt; i++)\n\t{\n\t\tvar tag  = bin.readUshort(data, offset);    offset+=2;\n\t\tvar type = bin.readUshort(data, offset);    offset+=2;\n\t\tvar num  = bin.readUint  (data, offset);    offset+=4;\n\t\tvar voff = bin.readUint  (data, offset);    offset+=4;\n\n\t\tvar arr = [];\n\t\tifd[\"t\"+tag] = arr;\n\t\t//ifd[\"t\"+tag+\"-\"+UTIF.tags[tag]] = arr;\n\t\tif(type== 1 || type==7) {  for(var j=0; j<num; j++) arr.push(data[(num<5 ? offset-4 : voff)+j]); }\n\t\tif(type== 2) {  arr.push( bin.readASCII(data, (num<5 ? offset-4 : voff), num-1) );  }\n\t\tif(type== 3) {  for(var j=0; j<num; j++) arr.push(bin.readUshort(data, (num<3 ? offset-4 : voff)+2*j));  }\n\t\tif(type== 4) {  for(var j=0; j<num; j++) arr.push(bin.readUint  (data, (num<2 ? offset-4 : voff)+4*j));  }\n\t\tif(type== 5) {  for(var j=0; j<num; j++) arr.push(bin.readUint  (data, voff+j*8) / bin.readUint(data,voff+j*8+4));  }\n\t\tif(type== 8) {  for(var j=0; j<num; j++) arr.push(bin.readShort (data, (num<3 ? offset-4 : voff)+2*j));  }\n\t\tif(type== 9) {  for(var j=0; j<num; j++) arr.push(bin.readInt   (data, (num<2 ? offset-4 : voff)+4*j));  }\n\t\tif(type==10) {  for(var j=0; j<num; j++) arr.push(bin.readInt   (data, voff+j*8) / bin.readInt (data,voff+j*8+4));  }\n\t\tif(type==11) {  for(var j=0; j<num; j++) arr.push(bin.readFloat (data, voff+j*4));  }\n\t\tif(type==12) {  for(var j=0; j<num; j++) arr.push(bin.readDouble(data, voff+j*8));  }\n\t\tif(num!=0 && arr.length==0) log(\"unknown TIFF tag type: \", type, \"num:\",num);\n\t\t//log(tag, type, UTIF.tags[tag], arr);\n\t\tif(tag==330) for(var j=0; j<num; j++) UTIF._readIFD(bin, data, arr[j], ifds);\n\t\t//if(tag==34665) UTIF._readIFD(bin, data, arr[0], ifds);\n\t}\n\t//log(\"<<<---------------\");\n\treturn offset;\n}\n\nUTIF._writeIFD = function(bin, data, offset, ifd)\n{\n\tvar keys = Object.keys(ifd);\n\tbin.writeUshort(data, offset, keys.length);  offset+=2;\n\n\tvar eoff = offset + keys.length*12 + 4;\n\n\tfor(var ki=0; ki<keys.length; ki++)\n\t{\n\t\tvar key = keys[ki];\n\t\tvar tag = parseInt(key.slice(1)), type = UTIF.ttypes[tag];  if(type==null) throw new Error(\"unknown type of tag: \"+tag);\n\t\tvar val = ifd[key];  if(type==2) val=val[0]+\"\\u0000\";  var num = val.length;\n\t\tbin.writeUshort(data, offset, tag );  offset+=2;\n\t\tbin.writeUshort(data, offset, type);  offset+=2;\n\t\tbin.writeUint  (data, offset, num );  offset+=4;\n\n\t\tvar dlen = [-1, 1, 1, 2, 4, 8, 0, 0, 0, 0, 0, 0, 8][type] * num;\n\t\tvar toff = offset;\n\t\tif(dlen>4) {  bin.writeUint(data, offset, eoff);  toff=eoff;  }\n\n\t\tif(type==2) {  bin.writeASCII(data, toff, val);   }\n\t\tif(type==3) {  for(var i=0; i<num; i++) bin.writeUshort(data, toff+2*i, val[i]);    }\n\t\tif(type==4) {  for(var i=0; i<num; i++) bin.writeUint  (data, toff+4*i, val[i]);    }\n\t\tif(type==5) {  for(var i=0; i<num; i++) {  bin.writeUint(data, toff+8*i, Math.round(val[i]*10000));  bin.writeUint(data, toff+8*i+4, 10000);  }   }\n\t\tif (type == 12) {  for (var i = 0; i < num; i++) bin.writeDouble(data, toff + 8 * i, val[i]); }\n\n\t\tif(dlen>4) {  dlen += (dlen&1);  eoff += dlen;  }\n\t\toffset += 4;\n\t}\n\treturn [offset, eoff];\n}\n\nUTIF.toRGBA8 = function(out)\n{\n\tvar w = out.width, h = out.height, area = w*h, qarea = area*4, data = out.data;\n\tvar img = new Uint8Array(area*4);\n\t// 0: WhiteIsZero, 1: BlackIsZero, 2: RGB, 3: Palette color, 4: Transparency mask, 5: CMYK\n\tvar intp = out[\"t262\"][0], bps = (out[\"t258\"]?Math.min(32,out[\"t258\"][0]):1), isLE = out.isLE ? 1 : 0;\n\t//log(\"interpretation: \", intp, \"bps\", bps, out);\n\tif(false) {}\n\telse if(intp==0)\n\t{\n\t\tvar bpl = Math.ceil(bps*w/8);\n\t\tfor(var y=0; y<h; y++) {\n\t\t\tvar off = y*bpl, io = y*w;\n\t\t\tif(bps== 1) for(var i=0; i<w; i++) {  var qi=(io+i)<<2, px=((data[off+(i>>3)])>>(7-  (i&7)))& 1;  img[qi]=img[qi+1]=img[qi+2]=( 1-px)*255;  img[qi+3]=255;    }\n\t\t\tif(bps== 4) for(var i=0; i<w; i++) {  var qi=(io+i)<<2, px=((data[off+(i>>1)])>>(4-4*(i&1)))&15;  img[qi]=img[qi+1]=img[qi+2]=(15-px)* 17;  img[qi+3]=255;    }\n\t\t\tif(bps== 8) for(var i=0; i<w; i++) {  var qi=(io+i)<<2, px=data[off+i];  img[qi]=img[qi+1]=img[qi+2]=255-px;  img[qi+3]=255;    }\n\t\t}\n\t}\n\telse if(intp==1)\n\t{\n\t\tvar bpl = Math.ceil(bps*w/8);\n\t\tfor(var y=0; y<h; y++) {\n\t\t\tvar off = y*bpl, io = y*w;\n\t\t\tif(bps== 1) for(var i=0; i<w; i++) {  var qi=(io+i)<<2, px=((data[off+(i>>3)])>>(7-  (i&7)))&1;   img[qi]=img[qi+1]=img[qi+2]=(px)*255;  img[qi+3]=255;    }\n\t\t\tif(bps== 2) for(var i=0; i<w; i++) {  var qi=(io+i)<<2, px=((data[off+(i>>2)])>>(6-2*(i&3)))&3;   img[qi]=img[qi+1]=img[qi+2]=(px)* 85;  img[qi+3]=255;    }\n\t\t\tif(bps== 8) for(var i=0; i<w; i++) {  var qi=(io+i)<<2, px=data[off+i];  img[qi]=img[qi+1]=img[qi+2]=    px;  img[qi+3]=255;    }\n\t\t\tif(bps==16) for(var i=0; i<w; i++) {  var qi=(io+i)<<2, px=data[off+(2*i+isLE)];  img[qi]=img[qi+1]=img[qi+2]= Math.min(255,px);  img[qi+3]=255;    } // ladoga.tif\n\t\t}\n\t}\n\telse if(intp==2)\n\t{\n\t\tif(bps== 8) // this needs to be simplified ... how many channels are there???\n\t\t{\n\t\t\tif(out[\"t338\"])\n\t\t\t{\n\t\t\t\t if(out[\"t338\"][0]>0) for(var i=0; i<qarea; i++) img[i] = data[i];\t// sometimes t338 is 1 or 2 in case of Alpha\n\t\t\t\t else  for(var i=0; i<qarea; i+=4) {  img[i] = data[i];  img[i+1] = data[i+1];  img[i+2] = data[i+2];  img[i+3] = 255;  }\n\t\t\t}\n\t\t\telse\n\t\t\t{\n\t\t\t\tvar smpls = out[\"t258\"]?out[\"t258\"].length : 3;\n\t\t\t\tif(smpls==4) for(var i=0; i<qarea; i++) img[i] = data[i];\n\t\t\t\tif(smpls==3) for(var i=0; i< area; i++) {  var qi=i<<2, ti=i*3;  img[qi]=data[ti];  img[qi+1]=data[ti+1];  img[qi+2]=data[ti+2];  img[qi+3]=255;    }\n\t\t\t}\n\t\t}\n\t\telse  // 3x 16-bit channel\n\t\t\tfor(var i=0; i<area; i++) {  var qi=i<<2, ti=i*6;  img[qi]=data[ti];  img[qi+1]=data[ti+2];  img[qi+2]=data[ti+4];  img[qi+3]=255;    }\n\t}\n\telse if(intp==3)\n\t{\n\t\tvar map = out[\"t320\"];\n\t\tfor(var i=0; i<area; i++) {  var qi=i<<2, mi=data[i];  img[qi]=(map[mi]>>8);  img[qi+1]=(map[256+mi]>>8);  img[qi+2]=(map[512+mi]>>8);  img[qi+3]=255;    }\n\t}\n\telse if(intp==5) \n\t{\n\t\tvar smpls = out[\"t258\"]?out[\"t258\"].length : 4;\n\t\tvar gotAlpha = smpls>4 ? 1 : 0;\n\t\tfor(var i=0; i<area; i++) {\n\t\t\tvar qi=i<<2, si=i*smpls;  var C=255-data[si], M=255-data[si+1], Y=255-data[si+2], K=(255-data[si+3])*(1/255);\n\t\t\timg[qi]=~~(C*K+0.5);  img[qi+1]=~~(M*K+0.5);  img[qi+2]=~~(Y*K+0.5);  img[qi+3]=255*(1-gotAlpha)+data[si+4]*gotAlpha;\n\t\t}\n\t}\n\telse log(\"Unknown Photometric interpretation: \"+intp);\n\treturn img;\n}\n\nUTIF.replaceIMG = function()\n{\n\tvar imgs = document.getElementsByTagName(\"img\");\n\tfor (var i=0; i<imgs.length; i++)\n\t{\n\t\tvar img=imgs[i], src=img.getAttribute(\"src\");  if(src==null) continue;\n\t\tvar suff=src.split(\".\").pop().toLowerCase();\n\t\tif(suff!=\"tif\" && suff!=\"tiff\") continue;\n\t\tvar xhr = new XMLHttpRequest();  UTIF._xhrs.push(xhr);  UTIF._imgs.push(img);\n\t\txhr.open(\"GET\", src);  xhr.responseType = \"arraybuffer\";\n\t\txhr.onload = UTIF._imgLoaded;   xhr.send();\n\t}\n}\n\nUTIF._xhrs = [];  UTIF._imgs = [];\nUTIF._imgLoaded = function(e)\n{\n\tvar buff = e.target.response;\n\tvar ifds = UTIF.decode(buff), page = ifds[0];  UTIF.decodeImages(buff, ifds);\n\tvar rgba = UTIF.toRGBA8(page), w=page.width, h=page.height;\n\tvar ind = UTIF._xhrs.indexOf(e.target), img = UTIF._imgs[ind];\n\tUTIF._xhrs.splice(ind,1);  UTIF._imgs.splice(ind,1);\n\tvar cnv = document.createElement(\"canvas\");  cnv.width=w;  cnv.height=h;\n\tvar ctx = cnv.getContext(\"2d\"), imgd = ctx.createImageData(w,h);\n\tfor(var i=0; i<rgba.length; i++) imgd.data[i]=rgba[i];       ctx.putImageData(imgd,0,0);\n\tvar attr = [\"style\",\"class\",\"id\"];\n\tfor(var i=0; i<attr.length; i++) cnv.setAttribute(attr[i], img.getAttribute(attr[i]));\n\timg.parentNode.replaceChild(cnv,img);\n}\n\n\nUTIF._binBE =\n{\n\tnextZero   : function(data, o) {  while(data[o]!=0) o++;  return o;  },\n\treadUshort : function(buff, p) {  return (buff[p]<< 8) |  buff[p+1];  },\n\treadShort  : function(buff, p) {  var a=UTIF._binBE.ui8;  a[0]=buff[p+1];  a[1]=buff[p+0];                                    return UTIF._binBE. i16[0];  },\n\treadInt    : function(buff, p) {  var a=UTIF._binBE.ui8;  a[0]=buff[p+3];  a[1]=buff[p+2];  a[2]=buff[p+1];  a[3]=buff[p+0];  return UTIF._binBE. i32[0];  },\n\treadUint   : function(buff, p) {  var a=UTIF._binBE.ui8;  a[0]=buff[p+3];  a[1]=buff[p+2];  a[2]=buff[p+1];  a[3]=buff[p+0];  return UTIF._binBE.ui32[0];  },\n\treadASCII  : function(buff, p, l) {  var s = \"\";   for(var i=0; i<l; i++) s += String.fromCharCode(buff[p+i]);   return s; },\n\treadFloat  : function(buff, p) {  var a=UTIF._binBE.ui8;  for(var i=0;i<4;i++) a[i]=buff[p+3-i];  return UTIF._binBE.fl32[0];  },\n\treadDouble : function(buff, p) {  var a=UTIF._binBE.ui8;  for(var i=0;i<8;i++) a[i]=buff[p+7-i];  return UTIF._binBE.fl64[0];  },\n\n\twriteUshort: function(buff, p, n) {  buff[p] = (n>> 8)&255;  buff[p+1] =  n&255;  },\n\twriteUint  : function(buff, p, n) {  buff[p] = (n>>24)&255;  buff[p+1] = (n>>16)&255;  buff[p+2] = (n>>8)&255;  buff[p+3] = (n>>0)&255;  },\n\twriteASCII : function(buff, p, s) {  for(var i = 0; i < s.length; i++)  buff[p+i] = s.charCodeAt(i);  },\n\twriteDouble: function(buff, p, n)\n\t{\n\t\tUTIF._binBE.fl64[0] = n;\n\t\tfor (var i = 0; i < 8; i++) buff[p + i] = UTIF._binBE.ui8[7 - i];\n\t}\n}\nUTIF._binBE.ui8  = new Uint8Array  (8);\nUTIF._binBE.i16  = new Int16Array  (UTIF._binBE.ui8.buffer);\nUTIF._binBE.i32  = new Int32Array  (UTIF._binBE.ui8.buffer);\nUTIF._binBE.ui32 = new Uint32Array (UTIF._binBE.ui8.buffer);\nUTIF._binBE.fl32 = new Float32Array(UTIF._binBE.ui8.buffer);\nUTIF._binBE.fl64 = new Float64Array(UTIF._binBE.ui8.buffer);\n\nUTIF._binLE =\n{\n\tnextZero   : UTIF._binBE.nextZero,\n\treadUshort : function(buff, p) {  return (buff[p+1]<< 8) |  buff[p];  },\n\treadShort  : function(buff, p) {  var a=UTIF._binBE.ui8;  a[0]=buff[p+0];  a[1]=buff[p+1];                                    return UTIF._binBE. i16[0];  },\n\treadInt    : function(buff, p) {  var a=UTIF._binBE.ui8;  a[0]=buff[p+0];  a[1]=buff[p+1];  a[2]=buff[p+2];  a[3]=buff[p+3];  return UTIF._binBE. i32[0];  },\n\treadUint   : function(buff, p) {  var a=UTIF._binBE.ui8;  a[0]=buff[p+0];  a[1]=buff[p+1];  a[2]=buff[p+2];  a[3]=buff[p+3];  return UTIF._binBE.ui32[0];  },\n\treadASCII  : UTIF._binBE.readASCII,\n\treadFloat  : function(buff, p) {  var a=UTIF._binBE.ui8;  for(var i=0;i<4;i++) a[i]=buff[p+  i];  return UTIF._binBE.fl32[0];  },\n\treadDouble : function(buff, p) {  var a=UTIF._binBE.ui8;  for(var i=0;i<8;i++) a[i]=buff[p+  i];  return UTIF._binBE.fl64[0];  }\n}\nUTIF._copyTile = function(tb, tw, th, b, w, h, xoff, yoff)\n{\n\t//log(\"copyTile\", tw, th,  w, h, xoff, yoff);\n\tvar xlim = Math.min(tw, w-xoff);\n\tvar ylim = Math.min(th, h-yoff);\n\tfor(var y=0; y<ylim; y++)\n\t{\n\t\tvar tof = (yoff+y)*w+xoff;\n\t\tvar sof = y*tw;\n\t\tfor(var x=0; x<xlim; x++) b[tof+x] = tb[sof+x];\n\t}\n}\n\n\n})(UTIF, pako);\n})();"]}