{"version": 3, "sources": ["verror.js"], "names": [], "mappings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file": "index.js", "sourcesContent": ["/*\n * verror.js: richer JavaScript errors\n */\n\nvar mod_assertplus = require('assert-plus');\nvar mod_util = require('util');\n\nvar mod_extsprintf = require('extsprintf');\nvar mod_isError = require('core-util-is').isError;\nvar sprintf = mod_extsprintf.sprintf;\n\n/*\n * Public interface\n */\n\n/* So you can 'var VError = require('verror')' */\nmodule.exports = VError;\n/* For compatibility */\nVError.VError = VError;\n/* Other exported classes */\nVError.SError = SError;\nVError.WError = WError;\nVError.MultiError = MultiError;\n\n/*\n * Common function used to parse constructor arguments for VError, WError, and\n * SError.  Named arguments to this function:\n *\n *     strict\t\tforce strict interpretation of sprintf arguments, even\n *     \t\t\tif the options in \"argv\" don't say so\n *\n *     argv\t\terror's constructor arguments, which are to be\n *     \t\t\tinterpreted as described in README.md.  For quick\n *     \t\t\treference, \"argv\" has one of the following forms:\n *\n *          [ sprintf_args... ]           (argv[0] is a string)\n *          [ cause, sprintf_args... ]    (argv[0] is an Error)\n *          [ options, sprintf_args... ]  (argv[0] is an object)\n *\n * This function normalizes these forms, producing an object with the following\n * properties:\n *\n *    options           equivalent to \"options\" in third form.  This will never\n *    \t\t\tbe a direct reference to what the caller passed in\n *    \t\t\t(i.e., it may be a shallow copy), so it can be freely\n *    \t\t\tmodified.\n *\n *    shortmessage      result of sprintf(sprintf_args), taking options.strict\n *    \t\t\tinto account as described in README.md.\n */\nfunction parseConstructorArguments(args)\n{\n\tvar argv, options, sprintf_args, shortmessage, k;\n\n\tmod_assertplus.object(args, 'args');\n\tmod_assertplus.bool(args.strict, 'args.strict');\n\tmod_assertplus.array(args.argv, 'args.argv');\n\targv = args.argv;\n\n\t/*\n\t * First, figure out which form of invocation we've been given.\n\t */\n\tif (argv.length === 0) {\n\t\toptions = {};\n\t\tsprintf_args = [];\n\t} else if (mod_isError(argv[0])) {\n\t\toptions = { 'cause': argv[0] };\n\t\tsprintf_args = argv.slice(1);\n\t} else if (typeof (argv[0]) === 'object') {\n\t\toptions = {};\n\t\tfor (k in argv[0]) {\n\t\t\toptions[k] = argv[0][k];\n\t\t}\n\t\tsprintf_args = argv.slice(1);\n\t} else {\n\t\tmod_assertplus.string(argv[0],\n\t\t    'first argument to VError, SError, or WError ' +\n\t\t    'constructor must be a string, object, or Error');\n\t\toptions = {};\n\t\tsprintf_args = argv;\n\t}\n\n\t/*\n\t * Now construct the error's message.\n\t *\n\t * extsprintf (which we invoke here with our caller's arguments in order\n\t * to construct this Error's message) is strict in its interpretation of\n\t * values to be processed by the \"%s\" specifier.  The value passed to\n\t * extsprintf must actually be a string or something convertible to a\n\t * String using .toString().  Passing other values (notably \"null\" and\n\t * \"undefined\") is considered a programmer error.  The assumption is\n\t * that if you actually want to print the string \"null\" or \"undefined\",\n\t * then that's easy to do that when you're calling extsprintf; on the\n\t * other hand, if you did NOT want that (i.e., there's actually a bug\n\t * where the program assumes some variable is non-null and tries to\n\t * print it, which might happen when constructing a packet or file in\n\t * some specific format), then it's better to stop immediately than\n\t * produce bogus output.\n\t *\n\t * However, sometimes the bug is only in the code calling VError, and a\n\t * programmer might prefer to have the error message contain \"null\" or\n\t * \"undefined\" rather than have the bug in the error path crash the\n\t * program (making the first bug harder to identify).  For that reason,\n\t * by default VError converts \"null\" or \"undefined\" arguments to their\n\t * string representations and passes those to extsprintf.  Programmers\n\t * desiring the strict behavior can use the SError class or pass the\n\t * \"strict\" option to the VError constructor.\n\t */\n\tmod_assertplus.object(options);\n\tif (!options.strict && !args.strict) {\n\t\tsprintf_args = sprintf_args.map(function (a) {\n\t\t\treturn (a === null ? 'null' :\n\t\t\t    a === undefined ? 'undefined' : a);\n\t\t});\n\t}\n\n\tif (sprintf_args.length === 0) {\n\t\tshortmessage = '';\n\t} else {\n\t\tshortmessage = sprintf.apply(null, sprintf_args);\n\t}\n\n\treturn ({\n\t    'options': options,\n\t    'shortmessage': shortmessage\n\t});\n}\n\n/*\n * See README.md for reference documentation.\n */\nfunction VError()\n{\n\tvar args, obj, parsed, cause, ctor, message, k;\n\n\targs = Array.prototype.slice.call(arguments, 0);\n\n\t/*\n\t * This is a regrettable pattern, but JavaScript's built-in Error class\n\t * is defined to work this way, so we allow the constructor to be called\n\t * without \"new\".\n\t */\n\tif (!(this instanceof VError)) {\n\t\tobj = Object.create(VError.prototype);\n\t\tVError.apply(obj, arguments);\n\t\treturn (obj);\n\t}\n\n\t/*\n\t * For convenience and backwards compatibility, we support several\n\t * different calling forms.  Normalize them here.\n\t */\n\tparsed = parseConstructorArguments({\n\t    'argv': args,\n\t    'strict': false\n\t});\n\n\t/*\n\t * If we've been given a name, apply it now.\n\t */\n\tif (parsed.options.name) {\n\t\tmod_assertplus.string(parsed.options.name,\n\t\t    'error\\'s \"name\" must be a string');\n\t\tthis.name = parsed.options.name;\n\t}\n\n\t/*\n\t * For debugging, we keep track of the original short message (attached\n\t * this Error particularly) separately from the complete message (which\n\t * includes the messages of our cause chain).\n\t */\n\tthis.jse_shortmsg = parsed.shortmessage;\n\tmessage = parsed.shortmessage;\n\n\t/*\n\t * If we've been given a cause, record a reference to it and update our\n\t * message appropriately.\n\t */\n\tcause = parsed.options.cause;\n\tif (cause) {\n\t\tmod_assertplus.ok(mod_isError(cause), 'cause is not an Error');\n\t\tthis.jse_cause = cause;\n\n\t\tif (!parsed.options.skipCauseMessage) {\n\t\t\tmessage += ': ' + cause.message;\n\t\t}\n\t}\n\n\t/*\n\t * If we've been given an object with properties, shallow-copy that\n\t * here.  We don't want to use a deep copy in case there are non-plain\n\t * objects here, but we don't want to use the original object in case\n\t * the caller modifies it later.\n\t */\n\tthis.jse_info = {};\n\tif (parsed.options.info) {\n\t\tfor (k in parsed.options.info) {\n\t\t\tthis.jse_info[k] = parsed.options.info[k];\n\t\t}\n\t}\n\n\tthis.message = message;\n\tError.call(this, message);\n\n\tif (Error.captureStackTrace) {\n\t\tctor = parsed.options.constructorOpt || this.constructor;\n\t\tError.captureStackTrace(this, ctor);\n\t}\n\n\treturn (this);\n}\n\nmod_util.inherits(VError, Error);\nVError.prototype.name = 'VError';\n\nVError.prototype.toString = function ve_toString()\n{\n\tvar str = (this.hasOwnProperty('name') && this.name ||\n\t\tthis.constructor.name || this.constructor.prototype.name);\n\tif (this.message)\n\t\tstr += ': ' + this.message;\n\n\treturn (str);\n};\n\n/*\n * This method is provided for compatibility.  New callers should use\n * VError.cause() instead.  That method also uses the saner `null` return value\n * when there is no cause.\n */\nVError.prototype.cause = function ve_cause()\n{\n\tvar cause = VError.cause(this);\n\treturn (cause === null ? undefined : cause);\n};\n\n/*\n * Static methods\n *\n * These class-level methods are provided so that callers can use them on\n * instances of Errors that are not VErrors.  New interfaces should be provided\n * only using static methods to eliminate the class of programming mistake where\n * people fail to check whether the Error object has the corresponding methods.\n */\n\nVError.cause = function (err)\n{\n\tmod_assertplus.ok(mod_isError(err), 'err must be an Error');\n\treturn (mod_isError(err.jse_cause) ? err.jse_cause : null);\n};\n\nVError.info = function (err)\n{\n\tvar rv, cause, k;\n\n\tmod_assertplus.ok(mod_isError(err), 'err must be an Error');\n\tcause = VError.cause(err);\n\tif (cause !== null) {\n\t\trv = VError.info(cause);\n\t} else {\n\t\trv = {};\n\t}\n\n\tif (typeof (err.jse_info) == 'object' && err.jse_info !== null) {\n\t\tfor (k in err.jse_info) {\n\t\t\trv[k] = err.jse_info[k];\n\t\t}\n\t}\n\n\treturn (rv);\n};\n\nVError.findCauseByName = function (err, name)\n{\n\tvar cause;\n\n\tmod_assertplus.ok(mod_isError(err), 'err must be an Error');\n\tmod_assertplus.string(name, 'name');\n\tmod_assertplus.ok(name.length > 0, 'name cannot be empty');\n\n\tfor (cause = err; cause !== null; cause = VError.cause(cause)) {\n\t\tmod_assertplus.ok(mod_isError(cause));\n\t\tif (cause.name == name) {\n\t\t\treturn (cause);\n\t\t}\n\t}\n\n\treturn (null);\n};\n\nVError.hasCauseWithName = function (err, name)\n{\n\treturn (VError.findCauseByName(err, name) !== null);\n};\n\nVError.fullStack = function (err)\n{\n\tmod_assertplus.ok(mod_isError(err), 'err must be an Error');\n\n\tvar cause = VError.cause(err);\n\n\tif (cause) {\n\t\treturn (err.stack + '\\ncaused by: ' + VError.fullStack(cause));\n\t}\n\n\treturn (err.stack);\n};\n\nVError.errorFromList = function (errors)\n{\n\tmod_assertplus.arrayOfObject(errors, 'errors');\n\n\tif (errors.length === 0) {\n\t\treturn (null);\n\t}\n\n\terrors.forEach(function (e) {\n\t\tmod_assertplus.ok(mod_isError(e));\n\t});\n\n\tif (errors.length == 1) {\n\t\treturn (errors[0]);\n\t}\n\n\treturn (new MultiError(errors));\n};\n\nVError.errorForEach = function (err, func)\n{\n\tmod_assertplus.ok(mod_isError(err), 'err must be an Error');\n\tmod_assertplus.func(func, 'func');\n\n\tif (err instanceof MultiError) {\n\t\terr.errors().forEach(function iterError(e) { func(e); });\n\t} else {\n\t\tfunc(err);\n\t}\n};\n\n\n/*\n * SError is like VError, but stricter about types.  You cannot pass \"null\" or\n * \"undefined\" as string arguments to the formatter.\n */\nfunction SError()\n{\n\tvar args, obj, parsed, options;\n\n\targs = Array.prototype.slice.call(arguments, 0);\n\tif (!(this instanceof SError)) {\n\t\tobj = Object.create(SError.prototype);\n\t\tSError.apply(obj, arguments);\n\t\treturn (obj);\n\t}\n\n\tparsed = parseConstructorArguments({\n\t    'argv': args,\n\t    'strict': true\n\t});\n\n\toptions = parsed.options;\n\tVError.call(this, options, '%s', parsed.shortmessage);\n\n\treturn (this);\n}\n\n/*\n * We don't bother setting SError.prototype.name because once constructed,\n * SErrors are just like VErrors.\n */\nmod_util.inherits(SError, VError);\n\n\n/*\n * Represents a collection of errors for the purpose of consumers that generally\n * only deal with one error.  Callers can extract the individual errors\n * contained in this object, but may also just treat it as a normal single\n * error, in which case a summary message will be printed.\n */\nfunction MultiError(errors)\n{\n\tmod_assertplus.array(errors, 'list of errors');\n\tmod_assertplus.ok(errors.length > 0, 'must be at least one error');\n\tthis.ase_errors = errors;\n\n\tVError.call(this, {\n\t    'cause': errors[0]\n\t}, 'first of %d error%s', errors.length, errors.length == 1 ? '' : 's');\n}\n\nmod_util.inherits(MultiError, VError);\nMultiError.prototype.name = 'MultiError';\n\nMultiError.prototype.errors = function me_errors()\n{\n\treturn (this.ase_errors.slice(0));\n};\n\n\n/*\n * See README.md for reference details.\n */\nfunction WError()\n{\n\tvar args, obj, parsed, options;\n\n\targs = Array.prototype.slice.call(arguments, 0);\n\tif (!(this instanceof WError)) {\n\t\tobj = Object.create(WError.prototype);\n\t\tWError.apply(obj, args);\n\t\treturn (obj);\n\t}\n\n\tparsed = parseConstructorArguments({\n\t    'argv': args,\n\t    'strict': false\n\t});\n\n\toptions = parsed.options;\n\toptions['skipCauseMessage'] = true;\n\tVError.call(this, options, '%s', parsed.shortmessage);\n\n\treturn (this);\n}\n\nmod_util.inherits(WError, VError);\nWError.prototype.name = 'WError';\n\nWError.prototype.toString = function we_toString()\n{\n\tvar str = (this.hasOwnProperty('name') && this.name ||\n\t\tthis.constructor.name || this.constructor.prototype.name);\n\tif (this.message)\n\t\tstr += ': ' + this.message;\n\tif (this.jse_cause && this.jse_cause.message)\n\t\tstr += '; caused by ' + this.jse_cause.toString();\n\n\treturn (str);\n};\n\n/*\n * For purely historical reasons, WError's cause() function allows you to set\n * the cause.\n */\nWError.prototype.cause = function we_cause(c)\n{\n\tif (mod_isError(c))\n\t\tthis.jse_cause = c;\n\n\treturn (this.jse_cause);\n};\n"]}