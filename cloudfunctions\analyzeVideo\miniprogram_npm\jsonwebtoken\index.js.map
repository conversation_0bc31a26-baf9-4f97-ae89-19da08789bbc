{"version": 3, "sources": ["index.js", "decode.js", "verify.js", "lib/JsonWebTokenError.js", "lib/NotBeforeError.js", "lib/TokenExpiredError.js", "lib/timespan.js", "lib/psSupported.js", "sign.js"], "names": [], "mappings": ";;;;;;;AAAA;AACA;AACA;ACFA,ADGA;AC<PERSON>,ADGA;ACFA,ADGA;ACFA,ADGA,AENA;ADIA,ADGA,AENA;ADIA,ADGA,AENA;ADIA,AENA,ADGA;ADIA,AENA,ADGA;ADIA,AENA,ADGA;ADIA,AENA,ACHA,AFMA;ADIA,AENA,ACHA,AFMA;ADIA,AENA,ACHA,AFMA;ADIA,AENA,ACHA,ACHA,AHSA;ADIA,AENA,ACHA,ACHA,AHSA;ADIA,AENA,ACHA,ACHA,AHSA;ADIA,AENA,ACHA,ACHA,ACHA,AJYA;ADIA,AENA,ACHA,ACHA,ACHA,AJYA;ADIA,AENA,ACHA,ACHA,ACHA,AJYA;ADIA,AENA,ACHA,ACHA,AENA,ADGA,AJYA;ADIA,AENA,ACHA,ACHA,AENA,ADGA,AJYA;ADIA,AENA,ACHA,ACHA,AENA,ADGA,AJYA;ADIA,AGTA,ACHA,AENA,ADGA,AENA,ANkBA;ADIA,AIZA,ACHA,AENA,ANkBA;ADIA,AIZA,ACHA,AENA,ANkBA;ADIA,AIZA,ACHA,AENA,ANkBA;ADIA,AKfA,AENA,ANkBA;ADIA,AKfA,AENA,ANkBA;ADIA,AKfA,AENA,ANkBA;ADIA,AKfA,AENA,ANkBA;ADIA,AKfA,AENA,ANkBA;ADIA,AKfA,AENA,ANkBA;AIXA,AENA,ANkBA;AIXA,AENA,ANkBA;AMjBA,ANkBA;AMjBA,ANkBA;AMjBA,ANkBA;AMjBA,ANkBA;AMjBA,ANkBA;AMjBA,ANkBA;AMjBA,ANkBA;AMjBA,ANkBA;AMjBA,ANkBA;AMjBA,ANkBA;AMjBA,ANkBA;AMjBA,ANkBA;AMjBA,ANkBA;AMjBA,ANkBA;AMjBA,ANkBA;AMjBA,ANkBA;AMjBA,ANkBA;AMjBA,ANkBA;AMjBA,ANkBA;AMjBA,ANkBA;AMjBA,ANkBA;AMjBA,ANkBA;AMjBA,ANkBA;AMjBA,ANkBA;AMjBA,ANkBA;AMjBA,ANkBA;AMjBA,ANkBA;AMjBA,ANkBA;AMjBA,ANkBA;AMjBA,ANkBA;AMjBA,ANkBA;AMjBA,ANkBA;AMjBA,ANkBA;AMjBA,ANkBA;AMjBA,ANkBA;AMjBA,ANkBA;AMjBA,ANkBA;AMjBA,ANkBA;AMjBA,ANkBA;AMjBA,ANkBA;AMjBA,ANkBA;AMjBA,ANkBA;AMjBA,ANkBA;AMjBA,ANkBA;AMjBA,ANkBA;AMjBA,ANkBA;AMjBA,ANkBA;AMjBA,ANkBA;AMjBA,ANkBA;AMjBA,ANkBA;AMjBA,ANkBA;AMjBA,ANkBA;AMjBA,ANkBA;AMjBA,ANkBA;AMjBA,ANkBA;AMjBA,ANkBA;AMjBA,ANkBA;AMjBA,ANkBA;AMjBA,ANkBA;AMjBA,ANkBA;AMjBA,ANkBA;AMjBA,ANkBA;AMjBA,ANkBA;AMjBA,ANkBA;AMjBA,ANkBA;AMjBA,ANkBA;AMjBA,ANkBA;AMjBA,ANkBA;AMjBA,ANkBA;AMjBA,ANkBA;AMjBA,ANkBA;AMjBA,ANkBA;AMjBA,ANkBA;AMjBA,ANkBA;AMjBA,ANkBA;AMjBA,ANkBA;AMjBA,ANkBA;AMjBA,ANkBA;AMjBA,ANkBA;AMjBA,ANkBA;AMjBA,ANkBA;AMjBA,ANkBA;AMjBA,ANkBA;AMjBA,ANkBA;AMjBA,ANkBA;AMjBA,ANkBA;AMjBA,ANkBA;AMjBA,ANkBA;AMjBA,ANkBA;AMjBA,ANkBA;AMjBA,ANkBA;AMjBA,ANkBA;AMjBA,ANkBA;AMjBA,ANkBA;AMjBA,ANkBA;AMjBA,ANkBA;AMjBA,ANkBA;AMjBA,ANkBA;AMjBA,ANkBA;AMjBA,ANkBA;AMjBA,ANkBA;AMjBA,ANkBA;AMjBA,ANkBA;AMjBA,ANkBA;AMjBA,ANkBA;AMjBA,ANkBA;AMjBA,ANkBA;AMjBA,ANkBA;AMjBA,ANkBA;AMjBA,ANkBA;AMjBA,ANkBA;AMjBA,ANkBA;AMjBA,ANkBA;AMjBA,ANkBA;AMjBA,ANkBA;AMjBA,ANkBA;AMjBA,ANkBA;AMjBA,ANkBA;AMjBA,ANkBA;AMjBA,ANkBA;AMjBA,ANkBA;AMjBA,ANkBA;AMjBA,ANkBA;AMjBA,ANkBA;AMjBA,ANkBA;AMjBA,ANkBA;AMjBA,ANkBA;AMjBA,ANkBA;AMjBA,ANkBA;AMjBA,ANkBA;AMjBA,ANkBA;AMjBA,ANkBA;AMjBA,ANkBA;AMjBA,ANkBA;AMjBA,ANkBA;AMjBA,ANkBA;AMjBA,ANkBA;AMjBA,ANkBA;AMjBA,ANkBA;AMjBA,ANkBA;AMjBA,ANkBA;AMjBA,ANkBA;AMjBA,ANkBA;AMjBA,ANkBA;AMjBA,ANkBA;AMjBA,ANkBA;AMjBA,ANkBA;AMjBA,ANkBA;AMjBA,ANkBA;AMjBA,ANkBA;AMjBA,ANkBA;AMjBA,ANkBA;AMjBA,ANkBA;AMjBA,ANkBA;AMjBA,ANkBA;AMjBA,ANkBA;AMjBA,ANkBA;AMjBA,ANkBA;AMjBA,ANkBA;AMjBA,ANkBA;AMjBA,ANkBA;AMjBA,ANkBA;AMjBA,ANkBA;AMjBA,ANkBA;AMjBA,ANkBA;AMjBA,ANkBA;AMjBA,ANkBA;AMjBA,ANkBA;AMjBA,ANkBA;AMjBA,ANkBA;AMjBA,ANkBA;AMjBA,ANkBA;AMjBA,ANkBA;AMjBA,ANkBA;AMjBA,ANkBA;AMjBA,ANkBA;AMjBA,ANkBA;AMjBA,ANkBA;AMjBA,ANkBA;AMjBA,ANkBA;AMjBA,ANkBA;AMjBA,ANkBA;AMjBA,ANkBA;AMjBA,ANkBA;AMjBA,ANkBA;AMjBA,ANkBA;AMjBA,ANkBA;AMjBA,ANkBA;AMjBA,ANkBA;AMjBA,ANkBA;AMjBA,ANkBA;AMjBA,ANkBA;AMjBA,ANkBA;AMjBA,ANkBA;AMjBA,ANkBA;AACA", "file": "index.js", "sourcesContent": ["module.exports = {\n  decode: require('./decode'),\n  verify: require('./verify'),\n  sign: require('./sign'),\n  JsonWebTokenError: require('./lib/JsonWebTokenError'),\n  NotBeforeError: require('./lib/NotBeforeError'),\n  TokenExpiredError: require('./lib/TokenExpiredError'),\n};\n", "var jws = require('jws');\n\nmodule.exports = function (jwt, options) {\n  options = options || {};\n  var decoded = jws.decode(jwt, options);\n  if (!decoded) { return null; }\n  var payload = decoded.payload;\n\n  //try parse the payload\n  if(typeof payload === 'string') {\n    try {\n      var obj = JSON.parse(payload);\n      if(obj !== null && typeof obj === 'object') {\n        payload = obj;\n      }\n    } catch (e) { }\n  }\n\n  //return header if `complete` option is enabled.  header includes claims\n  //such as `kid` and `alg` used to select the key within a JWKS needed to\n  //verify the signature\n  if (options.complete === true) {\n    return {\n      header: decoded.header,\n      payload: payload,\n      signature: decoded.signature\n    };\n  }\n  return payload;\n};\n", "var JsonWebTokenError = require('./lib/JsonWebTokenError');\nvar NotBeforeError    = require('./lib/NotBeforeError');\nvar TokenExpiredError = require('./lib/TokenExpiredError');\nvar decode            = require('./decode');\nvar timespan          = require('./lib/timespan');\nvar PS_SUPPORTED      = require('./lib/psSupported');\nvar jws               = require('jws');\n\nvar PUB_KEY_ALGS = ['RS256', 'RS384', 'RS512', 'ES256', 'ES384', 'ES512'];\nvar RSA_KEY_ALGS = ['RS256', 'RS384', 'RS512'];\nvar HS_ALGS = ['HS256', 'HS384', 'HS512'];\n\nif (PS_SUPPORTED) {\n  PUB_KEY_ALGS.splice(3, 0, 'PS256', 'PS384', 'PS512');\n  RSA_KEY_ALGS.splice(3, 0, 'PS256', 'PS384', 'PS512');\n}\n\nmodule.exports = function (jwtString, secretOrPublicKey, options, callback) {\n  if ((typeof options === 'function') && !callback) {\n    callback = options;\n    options = {};\n  }\n\n  if (!options) {\n    options = {};\n  }\n\n  //clone this object since we are going to mutate it.\n  options = Object.assign({}, options);\n\n  var done;\n\n  if (callback) {\n    done = callback;\n  } else {\n    done = function(err, data) {\n      if (err) throw err;\n      return data;\n    };\n  }\n\n  if (options.clockTimestamp && typeof options.clockTimestamp !== 'number') {\n    return done(new JsonWebTokenError('clockTimestamp must be a number'));\n  }\n\n  if (options.nonce !== undefined && (typeof options.nonce !== 'string' || options.nonce.trim() === '')) {\n    return done(new JsonWebTokenError('nonce must be a non-empty string'));\n  }\n\n  var clockTimestamp = options.clockTimestamp || Math.floor(Date.now() / 1000);\n\n  if (!jwtString){\n    return done(new JsonWebTokenError('jwt must be provided'));\n  }\n\n  if (typeof jwtString !== 'string') {\n    return done(new JsonWebTokenError('jwt must be a string'));\n  }\n\n  var parts = jwtString.split('.');\n\n  if (parts.length !== 3){\n    return done(new JsonWebTokenError('jwt malformed'));\n  }\n\n  var decodedToken;\n\n  try {\n    decodedToken = decode(jwtString, { complete: true });\n  } catch(err) {\n    return done(err);\n  }\n\n  if (!decodedToken) {\n    return done(new JsonWebTokenError('invalid token'));\n  }\n\n  var header = decodedToken.header;\n  var getSecret;\n\n  if(typeof secretOrPublicKey === 'function') {\n    if(!callback) {\n      return done(new JsonWebTokenError('verify must be called asynchronous if secret or public key is provided as a callback'));\n    }\n\n    getSecret = secretOrPublicKey;\n  }\n  else {\n    getSecret = function(header, secretCallback) {\n      return secretCallback(null, secretOrPublicKey);\n    };\n  }\n\n  return getSecret(header, function(err, secretOrPublicKey) {\n    if(err) {\n      return done(new JsonWebTokenError('error in secret or public key callback: ' + err.message));\n    }\n\n    var hasSignature = parts[2].trim() !== '';\n\n    if (!hasSignature && secretOrPublicKey){\n      return done(new JsonWebTokenError('jwt signature is required'));\n    }\n\n    if (hasSignature && !secretOrPublicKey) {\n      return done(new JsonWebTokenError('secret or public key must be provided'));\n    }\n\n    if (!hasSignature && !options.algorithms) {\n      options.algorithms = ['none'];\n    }\n\n    if (!options.algorithms) {\n      options.algorithms = ~secretOrPublicKey.toString().indexOf('BEGIN CERTIFICATE') ||\n        ~secretOrPublicKey.toString().indexOf('BEGIN PUBLIC KEY') ? PUB_KEY_ALGS :\n        ~secretOrPublicKey.toString().indexOf('BEGIN RSA PUBLIC KEY') ? RSA_KEY_ALGS : HS_ALGS;\n\n    }\n\n    if (!~options.algorithms.indexOf(decodedToken.header.alg)) {\n      return done(new JsonWebTokenError('invalid algorithm'));\n    }\n\n    var valid;\n\n    try {\n      valid = jws.verify(jwtString, decodedToken.header.alg, secretOrPublicKey);\n    } catch (e) {\n      return done(e);\n    }\n\n    if (!valid) {\n      return done(new JsonWebTokenError('invalid signature'));\n    }\n\n    var payload = decodedToken.payload;\n\n    if (typeof payload.nbf !== 'undefined' && !options.ignoreNotBefore) {\n      if (typeof payload.nbf !== 'number') {\n        return done(new JsonWebTokenError('invalid nbf value'));\n      }\n      if (payload.nbf > clockTimestamp + (options.clockTolerance || 0)) {\n        return done(new NotBeforeError('jwt not active', new Date(payload.nbf * 1000)));\n      }\n    }\n\n    if (typeof payload.exp !== 'undefined' && !options.ignoreExpiration) {\n      if (typeof payload.exp !== 'number') {\n        return done(new JsonWebTokenError('invalid exp value'));\n      }\n      if (clockTimestamp >= payload.exp + (options.clockTolerance || 0)) {\n        return done(new TokenExpiredError('jwt expired', new Date(payload.exp * 1000)));\n      }\n    }\n\n    if (options.audience) {\n      var audiences = Array.isArray(options.audience) ? options.audience : [options.audience];\n      var target = Array.isArray(payload.aud) ? payload.aud : [payload.aud];\n\n      var match = target.some(function (targetAudience) {\n        return audiences.some(function (audience) {\n          return audience instanceof RegExp ? audience.test(targetAudience) : audience === targetAudience;\n        });\n      });\n\n      if (!match) {\n        return done(new JsonWebTokenError('jwt audience invalid. expected: ' + audiences.join(' or ')));\n      }\n    }\n\n    if (options.issuer) {\n      var invalid_issuer =\n              (typeof options.issuer === 'string' && payload.iss !== options.issuer) ||\n              (Array.isArray(options.issuer) && options.issuer.indexOf(payload.iss) === -1);\n\n      if (invalid_issuer) {\n        return done(new JsonWebTokenError('jwt issuer invalid. expected: ' + options.issuer));\n      }\n    }\n\n    if (options.subject) {\n      if (payload.sub !== options.subject) {\n        return done(new JsonWebTokenError('jwt subject invalid. expected: ' + options.subject));\n      }\n    }\n\n    if (options.jwtid) {\n      if (payload.jti !== options.jwtid) {\n        return done(new JsonWebTokenError('jwt jwtid invalid. expected: ' + options.jwtid));\n      }\n    }\n\n    if (options.nonce) {\n      if (payload.nonce !== options.nonce) {\n        return done(new JsonWebTokenError('jwt nonce invalid. expected: ' + options.nonce));\n      }\n    }\n\n    if (options.maxAge) {\n      if (typeof payload.iat !== 'number') {\n        return done(new JsonWebTokenError('iat required when maxAge is specified'));\n      }\n\n      var maxAgeTimestamp = timespan(options.maxAge, payload.iat);\n      if (typeof maxAgeTimestamp === 'undefined') {\n        return done(new JsonWebTokenError('\"maxAge\" should be a number of seconds or string representing a timespan eg: \"1d\", \"20h\", 60'));\n      }\n      if (clockTimestamp >= maxAgeTimestamp + (options.clockTolerance || 0)) {\n        return done(new TokenExpiredError('maxAge exceeded', new Date(maxAgeTimestamp * 1000)));\n      }\n    }\n\n    if (options.complete === true) {\n      var signature = decodedToken.signature;\n\n      return done(null, {\n        header: header,\n        payload: payload,\n        signature: signature\n      });\n    }\n\n    return done(null, payload);\n  });\n};\n", "var JsonWebTokenError = function (message, error) {\n  Error.call(this, message);\n  if(Error.captureStackTrace) {\n    Error.captureStackTrace(this, this.constructor);\n  }\n  this.name = 'JsonWebTokenError';\n  this.message = message;\n  if (error) this.inner = error;\n};\n\nJsonWebTokenError.prototype = Object.create(Error.prototype);\nJsonWebTokenError.prototype.constructor = JsonWebTokenError;\n\nmodule.exports = JsonWebTokenError;\n", "var JsonWebTokenError = require('./JsonWebTokenError');\n\nvar NotBeforeError = function (message, date) {\n  JsonWebTokenError.call(this, message);\n  this.name = 'NotBeforeError';\n  this.date = date;\n};\n\nNotBeforeError.prototype = Object.create(JsonWebTokenError.prototype);\n\nNotBeforeError.prototype.constructor = NotBeforeError;\n\nmodule.exports = NotBeforeError;", "var JsonWebTokenError = require('./JsonWebTokenError');\n\nvar TokenExpiredError = function (message, expiredAt) {\n  JsonWebTokenError.call(this, message);\n  this.name = 'TokenExpiredError';\n  this.expiredAt = expiredAt;\n};\n\nTokenExpiredError.prototype = Object.create(JsonWebTokenError.prototype);\n\nTokenExpiredError.prototype.constructor = TokenExpiredError;\n\nmodule.exports = TokenExpiredError;", "var ms = require('ms');\n\nmodule.exports = function (time, iat) {\n  var timestamp = iat || Math.floor(Date.now() / 1000);\n\n  if (typeof time === 'string') {\n    var milliseconds = ms(time);\n    if (typeof milliseconds === 'undefined') {\n      return;\n    }\n    return Math.floor(timestamp + milliseconds / 1000);\n  } else if (typeof time === 'number') {\n    return timestamp + time;\n  } else {\n    return;\n  }\n\n};", "var semver = require('semver');\n\nmodule.exports = semver.satisfies(process.version, '^6.12.0 || >=8.0.0');\n", "var timespan = require('./lib/timespan');\nvar PS_SUPPORTED = require('./lib/psSupported');\nvar jws = require('jws');\nvar includes = require('lodash.includes');\nvar isBoolean = require('lodash.isboolean');\nvar isInteger = require('lodash.isinteger');\nvar isNumber = require('lodash.isnumber');\nvar isPlainObject = require('lodash.isplainobject');\nvar isString = require('lodash.isstring');\nvar once = require('lodash.once');\n\nvar SUPPORTED_ALGS = ['RS256', 'RS384', 'RS512', 'ES256', 'ES384', 'ES512', 'HS256', 'HS384', 'HS512', 'none']\nif (PS_SUPPORTED) {\n  SUPPORTED_ALGS.splice(3, 0, 'PS256', 'PS384', 'PS512');\n}\n\nvar sign_options_schema = {\n  expiresIn: { isValid: function(value) { return isInteger(value) || (isString(value) && value); }, message: '\"expiresIn\" should be a number of seconds or string representing a timespan' },\n  notBefore: { isValid: function(value) { return isInteger(value) || (isString(value) && value); }, message: '\"notBefore\" should be a number of seconds or string representing a timespan' },\n  audience: { isValid: function(value) { return isString(value) || Array.isArray(value); }, message: '\"audience\" must be a string or array' },\n  algorithm: { isValid: includes.bind(null, SUPPORTED_ALGS), message: '\"algorithm\" must be a valid string enum value' },\n  header: { isValid: isPlainObject, message: '\"header\" must be an object' },\n  encoding: { isValid: isString, message: '\"encoding\" must be a string' },\n  issuer: { isValid: isString, message: '\"issuer\" must be a string' },\n  subject: { isValid: isString, message: '\"subject\" must be a string' },\n  jwtid: { isValid: isString, message: '\"jwtid\" must be a string' },\n  noTimestamp: { isValid: isBoolean, message: '\"noTimestamp\" must be a boolean' },\n  keyid: { isValid: isString, message: '\"keyid\" must be a string' },\n  mutatePayload: { isValid: isBoolean, message: '\"mutatePayload\" must be a boolean' }\n};\n\nvar registered_claims_schema = {\n  iat: { isValid: isNumber, message: '\"iat\" should be a number of seconds' },\n  exp: { isValid: isNumber, message: '\"exp\" should be a number of seconds' },\n  nbf: { isValid: isNumber, message: '\"nbf\" should be a number of seconds' }\n};\n\nfunction validate(schema, allowUnknown, object, parameterName) {\n  if (!isPlainObject(object)) {\n    throw new Error('Expected \"' + parameterName + '\" to be a plain object.');\n  }\n  Object.keys(object)\n    .forEach(function(key) {\n      var validator = schema[key];\n      if (!validator) {\n        if (!allowUnknown) {\n          throw new Error('\"' + key + '\" is not allowed in \"' + parameterName + '\"');\n        }\n        return;\n      }\n      if (!validator.isValid(object[key])) {\n        throw new Error(validator.message);\n      }\n    });\n}\n\nfunction validateOptions(options) {\n  return validate(sign_options_schema, false, options, 'options');\n}\n\nfunction validatePayload(payload) {\n  return validate(registered_claims_schema, true, payload, 'payload');\n}\n\nvar options_to_payload = {\n  'audience': 'aud',\n  'issuer': 'iss',\n  'subject': 'sub',\n  'jwtid': 'jti'\n};\n\nvar options_for_objects = [\n  'expiresIn',\n  'notBefore',\n  'noTimestamp',\n  'audience',\n  'issuer',\n  'subject',\n  'jwtid',\n];\n\nmodule.exports = function (payload, secretOrPrivateKey, options, callback) {\n  if (typeof options === 'function') {\n    callback = options;\n    options = {};\n  } else {\n    options = options || {};\n  }\n\n  var isObjectPayload = typeof payload === 'object' &&\n                        !Buffer.isBuffer(payload);\n\n  var header = Object.assign({\n    alg: options.algorithm || 'HS256',\n    typ: isObjectPayload ? 'JWT' : undefined,\n    kid: options.keyid\n  }, options.header);\n\n  function failure(err) {\n    if (callback) {\n      return callback(err);\n    }\n    throw err;\n  }\n\n  if (!secretOrPrivateKey && options.algorithm !== 'none') {\n    return failure(new Error('secretOrPrivateKey must have a value'));\n  }\n\n  if (typeof payload === 'undefined') {\n    return failure(new Error('payload is required'));\n  } else if (isObjectPayload) {\n    try {\n      validatePayload(payload);\n    }\n    catch (error) {\n      return failure(error);\n    }\n    if (!options.mutatePayload) {\n      payload = Object.assign({},payload);\n    }\n  } else {\n    var invalid_options = options_for_objects.filter(function (opt) {\n      return typeof options[opt] !== 'undefined';\n    });\n\n    if (invalid_options.length > 0) {\n      return failure(new Error('invalid ' + invalid_options.join(',') + ' option for ' + (typeof payload ) + ' payload'));\n    }\n  }\n\n  if (typeof payload.exp !== 'undefined' && typeof options.expiresIn !== 'undefined') {\n    return failure(new Error('Bad \"options.expiresIn\" option the payload already has an \"exp\" property.'));\n  }\n\n  if (typeof payload.nbf !== 'undefined' && typeof options.notBefore !== 'undefined') {\n    return failure(new Error('Bad \"options.notBefore\" option the payload already has an \"nbf\" property.'));\n  }\n\n  try {\n    validateOptions(options);\n  }\n  catch (error) {\n    return failure(error);\n  }\n\n  var timestamp = payload.iat || Math.floor(Date.now() / 1000);\n\n  if (options.noTimestamp) {\n    delete payload.iat;\n  } else if (isObjectPayload) {\n    payload.iat = timestamp;\n  }\n\n  if (typeof options.notBefore !== 'undefined') {\n    try {\n      payload.nbf = timespan(options.notBefore, timestamp);\n    }\n    catch (err) {\n      return failure(err);\n    }\n    if (typeof payload.nbf === 'undefined') {\n      return failure(new Error('\"notBefore\" should be a number of seconds or string representing a timespan eg: \"1d\", \"20h\", 60'));\n    }\n  }\n\n  if (typeof options.expiresIn !== 'undefined' && typeof payload === 'object') {\n    try {\n      payload.exp = timespan(options.expiresIn, timestamp);\n    }\n    catch (err) {\n      return failure(err);\n    }\n    if (typeof payload.exp === 'undefined') {\n      return failure(new Error('\"expiresIn\" should be a number of seconds or string representing a timespan eg: \"1d\", \"20h\", 60'));\n    }\n  }\n\n  Object.keys(options_to_payload).forEach(function (key) {\n    var claim = options_to_payload[key];\n    if (typeof options[key] !== 'undefined') {\n      if (typeof payload[claim] !== 'undefined') {\n        return failure(new Error('Bad \"options.' + key + '\" option. The payload already has an \"' + claim + '\" property.'));\n      }\n      payload[claim] = options[key];\n    }\n  });\n\n  var encoding = options.encoding || 'utf8';\n\n  if (typeof callback === 'function') {\n    callback = callback && once(callback);\n\n    jws.createSign({\n      header: header,\n      privateKey: secretOrPrivateKey,\n      payload: payload,\n      encoding: encoding\n    }).once('error', callback)\n      .once('done', function (signature) {\n        callback(null, signature);\n      });\n  } else {\n    return jws.sign({header: header, payload: payload, secret: secretOrPrivateKey, encoding: encoding});\n  }\n};\n"]}