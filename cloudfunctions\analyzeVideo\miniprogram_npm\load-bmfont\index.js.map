{"version": 3, "sources": ["index.js", "lib/is-binary.js"], "names": [], "mappings": ";;;;;;;AAAA;AACA;AACA;AACA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.js", "sourcesContent": ["var fs = require('fs')\nvar url = require('url')\nvar path = require('path')\nvar request = require('phin')\nvar parseASCII = require('parse-bmfont-ascii')\nvar parseXML = require('parse-bmfont-xml')\nvar readBinary = require('parse-bmfont-binary')\nvar mime = require('mime')\nvar noop = function() {}\nvar isBinary = require('./lib/is-binary')\n\nfunction parseFont(file, data, cb) {\n  var result, binary\n\n  if (isBinary(data)) {\n    if (typeof data === 'string') data = Buffer.from(data, 'binary')\n    binary = true\n  } else data = data.toString().trim()\n\n  try {\n    if (binary) result = readBinary(data)\n    else if (/json/.test(mime.lookup(file)) || data.charAt(0) === '{')\n      result = JSON.parse(data)\n    else if (/xml/.test(mime.lookup(file)) || data.charAt(0) === '<')\n      result = parseXML(data)\n    else result = parseASCII(data)\n  } catch (e) {\n    cb(e)\n    cb = noop\n  }\n\n  cb(null, result)\n}\n\nmodule.exports = function loadFont(opt, cb) {\n  cb = typeof cb === 'function' ? cb : noop\n\n  if (typeof opt === 'string') opt = { uri: opt, url: opt }\n  else if (!opt) opt = {}\n\n  var file = opt.uri || opt.url\n  \n  function handleData(err, data) {\n    if (err) return cb(err)\n    parseFont(file, data.body || data, cb)\n  }\n\n  if (url.parse(file).host) {\n    request(opt).then(function (res) {\n      handleData(null, res)\n    }).catch(function (err) {\n      handleData(err)\n    })\n  } else {\n    fs.readFile(file, opt, handleData)\n  }\n}\n", "var equal = require('buffer-equal')\nvar HEADER = Buffer.from([66, 77, 70, 3])\n\nmodule.exports = function(buf) {\n  if (typeof buf === 'string')\n    return buf.substring(0, 3) === 'BMF'\n  return buf.length > 4 && equal(buf.slice(0, 4), HEADER)\n}"]}