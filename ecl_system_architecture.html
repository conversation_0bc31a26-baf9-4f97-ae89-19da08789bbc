<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ECL检测系统架构图</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', <PERSON>l, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .container {
            background: white;
            border-radius: 15px;
            padding: 40px;
            box-shadow: 0 15px 35px rgba(0,0,0,0.1);
            width: 1400px;
            height: 800px;
            position: relative;
            display: flex;
            flex-direction: column;
        }

        .container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            border: 3px solid #2196F3;
            border-radius: 15px;
            pointer-events: none;
        }

        .title {
            text-align: center;
            font-size: 36px;
            font-weight: bold;
            color: #2196F3;
            margin-bottom: 30px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.1);
        }

        .architecture-container {
            display: flex;
            justify-content: space-between;
            align-items: center;
            gap: 40px;
            flex: 1;
            margin-bottom: 30px;
        }

        .left-section {
            flex: 2.5;
            display: flex;
            flex-direction: column;
            justify-content: center;
            gap: 20px;
            padding-right: 30px;
        }

        .main-system {
            background: linear-gradient(135deg, #2196F3, #1976D2);
            color: white;
            padding: 30px;
            border-radius: 50%;
            width: 200px;
            height: 200px;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            font-size: 26px;
            font-weight: bold;
            box-shadow: 0 10px 30px rgba(33, 150, 243, 0.3);
            margin-bottom: 25px;
            align-self: center;
        }

        .features-list {
            list-style: none;
            padding: 0;
            margin: 0;
        }

        .feature-item {
            display: flex;
            align-items: center;
            margin-bottom: 15px;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.05);
            transition: transform 0.3s ease;
        }

        .feature-item:hover {
            transform: translateX(10px);
        }

        .feature-number {
            background: linear-gradient(135deg, #2196F3, #1976D2);
            color: white;
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            justify-content: center;
            align-items: center;
            font-weight: bold;
            margin-right: 15px;
            font-size: 18px;
        }

        .feature-text {
            font-size: 18px;
            color: #333;
            line-height: 1.4;
        }

        .center-section {
            flex: 1.5;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            gap: 20px;
        }

        .device-image {
            width: 220px;
            height: 180px;
            background: #f0f0f0;
            border-radius: 15px;
            display: flex;
            justify-content: center;
            align-items: center;
            font-size: 16px;
            color: #666;
            border: 3px dashed #ccc;
            position: relative;
            overflow: hidden;
        }

        .device-image::before {
            content: '📱';
            font-size: 50px;
            margin-bottom: 10px;
        }

        .right-section {
            flex: 2.5;
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 25px;
            align-content: center;
            padding-left: 30px;
        }

        .subsystem {
            background: linear-gradient(135deg, #2196F3, #1976D2);
            color: white;
            padding: 20px;
            border-radius: 50%;
            width: 140px;
            height: 140px;
            display: flex;
            justify-content: center;
            align-items: center;
            font-size: 16px;
            font-weight: bold;
            text-align: center;
            line-height: 1.2;
            box-shadow: 0 8px 25px rgba(33, 150, 243, 0.3);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            justify-self: center;
        }

        .subsystem:hover {
            transform: scale(1.05);
            box-shadow: 0 8px 25px rgba(33, 150, 243, 0.4);
        }

        .connection-line {
            position: absolute;
            background: #2196F3;
            height: 2px;
            opacity: 0.3;
        }

        .tech-details {
            padding: 25px;
            background: #f8f9fa;
            border-radius: 12px;
            border-left: 5px solid #2196F3;
            margin-top: auto;
        }

        .tech-title {
            font-size: 20px;
            font-weight: bold;
            color: #2196F3;
            margin-bottom: 15px;
        }

        .tech-grid {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 20px;
        }

        .tech-item {
            background: white;
            padding: 18px;
            border-radius: 10px;
            box-shadow: 0 3px 10px rgba(0,0,0,0.1);
        }

        .tech-item h4 {
            color: #2196F3;
            margin: 0 0 12px 0;
            font-size: 16px;
            font-weight: bold;
        }

        .tech-item p {
            margin: 0;
            color: #666;
            font-size: 14px;
            line-height: 1.4;
        }

        /* PPT适配样式 */
        @media screen and (min-width: 1600px) {
            .container {
                width: 1600px;
                height: 900px;
            }

            .title {
                font-size: 42px;
            }

            .main-system {
                width: 240px;
                height: 240px;
                font-size: 30px;
            }

            .subsystem {
                width: 160px;
                height: 160px;
                font-size: 18px;
            }

            .feature-text {
                font-size: 20px;
            }

            .tech-item h4 {
                font-size: 18px;
            }

            .tech-item p {
                font-size: 15px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1 class="title">ECL检测系统架构图</h1>
        
        <div class="architecture-container">
            <!-- 左侧：主系统和功能特点 -->
            <div class="left-section">
                <div class="main-system">
                    ECL检测<br>系统
                </div>
                
                <ul class="features-list">
                    <li class="feature-item">
                        <div class="feature-number">1</div>
                        <div class="feature-text">实时视频流预览与<br>智能参数调节</div>
                    </li>
                    <li class="feature-item">
                        <div class="feature-number">2</div>
                        <div class="feature-text">云端FFmpeg视频<br>处理与帧提取</div>
                    </li>
                    <li class="feature-item">
                        <div class="feature-number">3</div>
                        <div class="feature-text">Jimp图像分析与<br>C/T区域检测</div>
                    </li>
                </ul>
            </div>

            <!-- 中间：设备图片 -->
            <div class="center-section">
                <div class="device-image">
                    <div style="text-align: center;">
                        <div style="font-size: 40px; margin-bottom: 10px;">📱</div>
                        <div>微信小程序<br>ECL检测设备</div>
                    </div>
                </div>
            </div>

            <!-- 右侧：子系统 -->
            <div class="right-section">
                <div class="subsystem">
                    用户管理<br>子系统
                </div>
                <div class="subsystem">
                    视频分析<br>子系统
                </div>
                <div class="subsystem">
                    数据存储<br>子系统
                </div>
                <div class="subsystem">
                    文件清理<br>子系统
                </div>
            </div>
        </div>

        <!-- 技术实现详情 -->
        <div class="tech-details">
            <div class="tech-title">核心技术模块实现</div>
            <div class="tech-grid">
                <div class="tech-item">
                    <h4>前端交互层 (pages/index & pages/me)</h4>
                    <p>• 实时视频流连接与录制<br>• 参数设置与设备控制<br>• 用户数据管理与展示<br>• 本地存储与分享功能</p>
                </div>
                <div class="tech-item">
                    <h4>视频分析引擎 (analyzeVideo)</h4>
                    <p>• FFmpeg视频帧提取<br>• Jimp图像处理分析<br>• C/T区域灰度值计算<br>• 异步任务状态管理</p>
                </div>
                <div class="tech-item">
                    <h4>数据服务层 (getUserData)</h4>
                    <p>• 云存储文件访问<br>• 临时URL生成<br>• Base64数据转换<br>• 媒体文件下载</p>
                </div>
                <div class="tech-item">
                    <h4>数据清理服务 (deleteUserData)</h4>
                    <p>• 批量文件删除<br>• 数据库记录清理<br>• 存储空间优化<br>• 文件路径格式化</p>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 添加一些交互效果
        document.addEventListener('DOMContentLoaded', function() {
            const subsystems = document.querySelectorAll('.subsystem');
            const mainSystem = document.querySelector('.main-system');
            
            // 为子系统添加点击效果
            subsystems.forEach((system, index) => {
                system.addEventListener('click', function() {
                    // 添加点击动画
                    this.style.transform = 'scale(0.95)';
                    setTimeout(() => {
                        this.style.transform = 'scale(1.05)';
                        setTimeout(() => {
                            this.style.transform = 'scale(1)';
                        }, 150);
                    }, 150);
                    
                    // 显示对应的技术详情
                    const techItems = document.querySelectorAll('.tech-item');
                    if (techItems[index]) {
                        techItems[index].style.background = '#e3f2fd';
                        setTimeout(() => {
                            techItems[index].style.background = 'white';
                        }, 2000);
                    }
                });
            });
            
            // 主系统悬停效果
            mainSystem.addEventListener('mouseenter', function() {
                subsystems.forEach(system => {
                    system.style.opacity = '0.7';
                });
            });
            
            mainSystem.addEventListener('mouseleave', function() {
                subsystems.forEach(system => {
                    system.style.opacity = '1';
                });
            });
        });
    </script>
</body>
</html>
