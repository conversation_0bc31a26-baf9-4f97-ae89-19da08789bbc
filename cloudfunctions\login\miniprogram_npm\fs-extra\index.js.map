{"version": 3, "sources": ["index.js", "fs/index.js", "copy/index.js", "copy/copy.js", "mkdirs/index.js", "mkdirs/make-dir.js", "mkdirs/utils.js", "path-exists/index.js", "util/utimes.js", "util/stat.js", "copy/copy-sync.js", "ensure/index.js", "json/index.js", "json/jsonfile.js", "json/output-json.js", "output-file/index.js", "json/output-json-sync.js", "move/index.js", "move/move.js", "remove/index.js", "move/move-sync.js"], "names": [], "mappings": ";;;;;;;AAAA;AACA;AACA;ACFA,ADGA;AC<PERSON>,ADGA;ACFA,ADGA;AELA,ADGA,ADGA;AELA,ADGA,ADGA;AELA,ADGA,ADGA;AGRA,ADGA,ADGA,ADGA;AGRA,ADGA,ADGA,ADGA;AGRA,ADGA,ADGA,ADGA;AGRA,ADGA,ADGA,ADGA,AIZA;ADIA,ADGA,ADGA,ADGA,AIZA;ADIA,AFMA,ADGA,AIZA;ADIA,AFMA,ADGA,AIZA,ACHA;AFOA,AFMA,ADGA,AIZA,ACHA;AFOA,AFMA,AGTA,ACHA;AFOA,AFMA,AGTA,ACHA,ACHA;AHUA,AFMA,AGTA,ACHA,ACHA;AHUA,AFMA,AGTA,ACHA,ACHA;AHUA,AFMA,AGTA,ACHA,ACHA,ACHA;AJaA,AFMA,AGTA,ACHA,ACHA,ACHA;AJaA,AFMA,AGTA,ACHA,ACHA,ACHA;AJaA,AFMA,AGTA,ACHA,ACHA,ACHA,ACHA;ALgBA,AFMA,AGTA,ACHA,ACHA,ACHA,ACHA;ALgBA,AFMA,AGTA,ACHA,ACHA,ACHA,ACHA;ALgBA,AFMA,AIZA,ACHA,ACHA,AENA,ADGA;ALgBA,AFMA,AIZA,ACHA,ACHA,AENA,ADGA;ALgBA,AFMA,AIZA,ACHA,ACHA,AENA,ADGA;AELA,APqBA,AFMA,AIZA,ACHA,ACHA,AENA,ADGA;AELA,APqBA,AFMA,AIZA,ACHA,ACHA,AENA,ADGA;AELA,APqBA,AFMA,AIZA,ACHA,ACHA,AENA,ADGA;AELA,APqBA,AQxBA,AV8BA,AIZA,ACHA,ACHA,AENA,ADGA;AELA,APqBA,AQxBA,AV8BA,AIZA,ACHA,AGTA,ADGA;AELA,APqBA,AQxBA,AV8BA,AIZA,ACHA,AGTA,ADGA;AELA,APqBA,AQxBA,AV8BA,AWjCA,APqBA,ACHA,AGTA,ADGA;AELA,APqBA,AQxBA,AV8BA,AWjCA,APqBA,ACHA,AGTA,ADGA;AELA,APqBA,AQxBA,AV8BA,AWjCA,APqBA,ACHA,AGTA,ADGA;AELA,APqBA,AQxBA,AV8BA,AWjCA,ACHA,ARwBA,ACHA,AGTA,ADGA;AELA,APqBA,AQxBA,AV8BA,AWjCA,ACHA,ARwBA,AIZA,ADGA;AELA,APqBA,AQxBA,AV8BA,AWjCA,ACHA,ARwBA,AIZA,ADGA;AELA,APqBA,AQxBA,AV8BA,AWjCA,ACHA,ACHA,AT2BA,AIZA,ADGA;AELA,APqBA,AQxBA,AV8BA,AWjCA,ACHA,ACHA,ALeA,ADGA;AELA,APqBA,AQxBA,AV8BA,AWjCA,ACHA,ACHA,ALeA,ADGA;AELA,APqBA,AQxBA,AV8BA,AWjCA,ACHA,ACHA,ACHA,ANkBA,ADGA;AELA,APqBA,AQxBA,AV8BA,AWjCA,ACHA,ACHA,ACHA,ANkBA,ADGA;AELA,APqBA,AQxBA,AV8BA,AWjCA,ACHA,ACHA,ACHA,ANkBA,ADGA;AELA,APqBA,AQxBA,AV8BA,AWjCA,ACHA,AGTA,AFMA,ACHA,ANkBA,ADGA;AELA,APqBA,AQxBA,AV8BA,AWjCA,ACHA,AGTA,AFMA,ACHA,ANkBA,ADGA;AELA,APqBA,AQxBA,AV8BA,AWjCA,ACHA,AGTA,AFMA,ACHA,ANkBA,ADGA;AELA,APqBA,AQxBA,AV8BA,AWjCA,AIZA,AFMA,AGTA,AFMA,ANkBA,ADGA;AELA,APqBA,AQxBA,AV8BA,AWjCA,AIZA,AFMA,AGTA,AFMA,ANkBA,ADGA;AELA,APqBA,AQxBA,AV8BA,Ae7CA,AFMA,AGTA,AFMA,ANkBA,ADGA;AELA,APqBA,AQxBA,AV8BA,Ae7CA,AFMA,AGTA,ACHA,AHSA,ANkBA,ADGA;AELA,APqBA,AQxBA,AV8BA,Ae7CA,ACHA,ACHA,AHSA,ANkBA,ADGA;AELA,APqBA,AQxBA,AV8BA,Ae7CA,ACHA,ACHA,AHSA,ANkBA,ADGA;AELA,APqBA,AFMA,Ae7CA,ACHA,ACHA,AHSA,AIZA,AV8BA,ADGA;AELA,APqBA,AFMA,Ae7CA,ACHA,ACHA,AHSA,AIZA,AV8BA,ADGA;AELA,APqBA,AFMA,Ae7CA,AENA,AHSA,AIZA,AV8BA,ADGA;AELA,APqBA,AFMA,Ae7CA,AIZA,AFMA,AHSA,AIZA,AV8BA,ADGA;AELA,APqBA,AFMA,AmBzDA,AFMA,AHSA,AIZA,AV8BA;ACFA,APqBA,AFMA,AmBzDA,AFMA,AHSA,AIZA,AV8BA;ACFA,APqBA,AFMA,AmBzDA,AFMA,AHSA,AIZA,AV8BA;ACFA,APqBA,AFMA,AmBzDA,AFMA,AHSA,AIZA,AV8BA;ACFA,APqBA,AFMA,AmBzDA,AFMA,AHSA,AIZA,AV8BA;ACFA,APqBA,AFMA,AmBzDA,AFMA,AHSA,AIZA,AV8BA;ACFA,APqBA,AFMA,AmBzDA,AFMA,AHSA,AIZA,AV8BA;ACFA,APqBA,AFMA,AmBzDA,AFMA,AHSA,AIZA,AV8BA;ACFA,APqBA,AFMA,AmBzDA,AFMA,AHSA,AIZA,AV8BA;ACFA,APqBA,AFMA,AmBzDA,AFMA,AHSA,AIZA,AV8BA;ACFA,APqBA,AFMA,AmBzDA,AFMA,AHSA,AIZA,AV8BA;ACFA,APqBA,AFMA,AmBzDA,AFMA,AHSA,AIZA,AV8BA;ACFA,APqBA,AFMA,AmBzDA,AFMA,AHSA,AIZA,AV8BA;ACFA,APqBA,AFMA,AmBzDA,AFMA,AHSA,AIZA,AV8BA;ACFA,APqBA,AFMA,AmBzDA,AFMA,AHSA,ANkBA;ACFA,APqBA,AFMA,AmBzDA,AFMA,AHSA,ANkBA;ACFA,APqBA,AFMA,AmBzDA,AFMA,AT2BA;ACFA,APqBA,AFMA,AmBzDA,AFMA,AT2BA;ACFA,APqBA,AFMA,AmBzDA,AFMA,AT2BA;ACFA,APqBA,AFMA,AmBzDA,AFMA,AT2BA;ACFA,APqBA,AFMA,AmBzDA,AFMA,AT2BA;ACFA,APqBA,AFMA,AmBzDA,AFMA,AT2BA;ACFA,APqBA,AFMA,AmBzDA,AFMA,AT2BA;ACFA,APqBA,AFMA,AmBzDA,AFMA,AT2BA;ACFA,APqBA,AFMA,AmBzDA,AFMA,AT2BA;ACFA,APqBA,AFMA,AmBzDA,AFMA,AT2BA;ACFA,APqBA,AFMA,AmBzDA,AFMA,AT2BA;ACFA,APqBA,AFMA,AmBzDA,AFMA,AT2BA;ACFA,APqBA,AFMA,AmBzDA,AFMA,AT2BA;ACFA,APqBA,AFMA,AmBzDA,AFMA,AT2BA;ACFA,APqBA,AFMA,AmBzDA,AFMA,AT2BA;ACFA,APqBA,AFMA,AmBzDA,AFMA,AT2BA;ACFA,APqBA,AFMA,AmBzDA,AFMA,AT2BA;ACFA,APqBA,AFMA,AmBzDA,AFMA,AT2BA;ACFA,APqBA,AFMA,AmBzDA,AFMA,AT2BA;ACFA,APqBA,AFMA,AmBzDA,AFMA,AT2BA;ACFA,APqBA,AFMA,AmBzDA,AFMA,AT2BA;ACFA,APqBA,AFMA,AmBzDA,AFMA,AT2BA;ACFA,APqBA,AFMA,AmBzDA,AFMA,AT2BA;ACFA,APqBA,AFMA,AmBzDA,AFMA,AT2BA;ACFA,APqBA,AFMA,AmBzDA,AFMA,AT2BA;ACFA,APqBA,AFMA,AmBzDA,AFMA,AT2BA;ACFA,APqBA,AFMA,AmBzDA,AFMA,AT2BA;ACFA,APqBA,AFMA,AmBzDA,AFMA,AT2BA;ACFA,APqBA,AFMA,AmBzDA,AFMA,AT2BA;ACFA,APqBA,AFMA,AmBzDA,AFMA,AT2BA;ACFA,APqBA,AFMA,AmBzDA,AFMA,AT2BA;ACFA,APqBA,AFMA,AmBzDA,AFMA,AT2BA;ACFA,APqBA,AFMA,AmBzDA,AFMA,AT2BA;ACFA,APqBA,AFMA,AmBzDA,AFMA,AT2BA;ACFA,APqBA,AFMA,AmBzDA,AFMA,AT2BA;ACFA,APqBA,AFMA,AmBzDA,AFMA,AT2BA;ACFA,APqBA,AFMA,AmBzDA,AFMA,AT2BA;ACFA,APqBA,AFMA,AmBzDA,AXiCA;ACFA,APqBA,AFMA,AmBzDA,AXiCA;ACFA,APqBA,AFMA,AQxBA;ACFA,APqBA,AFMA,AQxBA;ACFA,APqBA,AFMA,AQxBA;ACFA,APqBA,AFMA,AQxBA;ACFA,APqBA,AFMA,AQxBA;ACFA,APqBA,AFMA,AQxBA;ACFA,APqBA,AFMA,AQxBA;ACFA,APqBA,AFMA,AQxBA;ACFA,APqBA,AFMA,AQxBA;ACFA,APqBA,AFMA,AQxBA;ACFA,APqBA,AFMA,AQxBA;ACFA,APqBA,AFMA,AQxBA;ACFA,APqBA,AFMA,AQxBA;ACFA,APqBA,AFMA,AQxBA;ACFA,APqBA,AFMA,AQxBA;ACFA,APqBA,AFMA,AQxBA;ACFA,APqBA,AFMA,AQxBA;ACFA,APqBA,AFMA,AQxBA;ACFA,APqBA,AFMA,AQxBA;ACFA,APqBA,AFMA,AQxBA;ACFA,APqBA,AFMA,AQxBA;ACFA,APqBA,AFMA,AQxBA;ACFA,APqBA,AFMA,AQxBA;ACFA,APqBA,AFMA,AQxBA;ACFA,APqBA,AFMA,AQxBA;ACFA,APqBA,AFMA,AQxBA;ACFA,APqBA,AFMA,AQxBA;ACFA,APqBA,AFMA,AQxBA;ACFA,APqBA,AFMA,AQxBA;ACFA,APqBA,AFMA,AQxBA;ACFA,APqBA,AFMA,AQxBA;ACFA,APqBA,AFMA,AQxBA;ACFA,APqBA,AFMA,AQxBA;ACFA,APqBA,AFMA,AQxBA;ACFA,APqBA,AMlBA;ACFA,APqBA,AMlBA;ACFA,APqBA,AMlBA;ACFA,APqBA,AMlBA;ACFA,APqBA,AMlBA;ACFA,APqBA,AMlBA;ACFA,APqBA,AMlBA;ACFA,APqBA,AMlBA;ACFA,APqBA,AMlBA;ACFA,APqBA,AMlBA;ACFA,APqBA,AMlBA;ACFA,APqBA,AMlBA;ACFA,APqBA,AMlBA;ACFA,APqBA,AMlBA;ACFA,APqBA,AMlBA;ACFA,APqBA,AMlBA;ACFA,APqBA,AMlBA;ACFA,APqBA,AMlBA;ACFA,APqBA,AMlBA;ACFA,APqBA,AMlBA;ACFA,APqBA,AMlBA;ACFA,APqBA,AMlBA;ACFA,APqBA,AMlBA;ACFA,APqBA,AMlBA;ACFA,APqBA,AMlBA;ACFA,APqBA,AMlBA;ACFA,APqBA,AMlBA;ACFA,APqBA,AMlBA;ACFA,APqBA,AMlBA;ACFA,APqBA,AMlBA;ACFA,APqBA,AMlBA;ACFA,APqBA,AMlBA;ACFA,APqBA,AMlBA;ACFA,APqBA,AMlBA;ACFA,APqBA,AMlBA;ACFA,APqBA,AMlBA;ACFA,APqBA;AOpBA,APqBA;AOpBA,APqBA;AOpBA,APqBA;AOpBA,APqBA;AOpBA,APqBA;AOpBA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.js", "sourcesContent": ["\n\nmodule.exports = {\n  // Export promiseified graceful-fs:\n  ...require('./fs'),\n  // Export extra methods:\n  ...require('./copy'),\n  ...require('./empty'),\n  ...require('./ensure'),\n  ...require('./json'),\n  ...require('./mkdirs'),\n  ...require('./move'),\n  ...require('./output-file'),\n  ...require('./path-exists'),\n  ...require('./remove')\n}\n", "\n// This is adapted from https://github.com/normalize/mz\n// Copyright (c) 2014-2016 <NAME_EMAIL> and Contributors\nconst u = require('universalify').fromCallback\nconst fs = require('graceful-fs')\n\nconst api = [\n  'access',\n  'appendFile',\n  'chmod',\n  'chown',\n  'close',\n  'copyFile',\n  'cp',\n  'fchmod',\n  'fchown',\n  'fdatasync',\n  'fstat',\n  'fsync',\n  'ftruncate',\n  'futimes',\n  'glob',\n  'lchmod',\n  'lchown',\n  'lutimes',\n  'link',\n  'lstat',\n  'mkdir',\n  'mkdtemp',\n  'open',\n  'opendir',\n  'readdir',\n  'readFile',\n  'readlink',\n  'realpath',\n  'rename',\n  'rm',\n  'rmdir',\n  'stat',\n  'statfs',\n  'symlink',\n  'truncate',\n  'unlink',\n  'utimes',\n  'writeFile'\n].filter(key => {\n  // Some commands are not available on some systems. Ex:\n  // fs.cp was added in Node.js v16.7.0\n  // fs.statfs was added in Node v19.6.0, v18.15.0\n  // fs.glob was added in Node.js v22.0.0\n  // fs.lchown is not available on at least some Linux\n  return typeof fs[key] === 'function'\n})\n\n// Export cloned fs:\nObject.assign(exports, fs)\n\n// Universalify async methods:\napi.forEach(method => {\n  exports[method] = u(fs[method])\n})\n\n// We differ from mz/fs in that we still ship the old, broken, fs.exists()\n// since we are a drop-in replacement for the native module\nexports.exists = function (filename, callback) {\n  if (typeof callback === 'function') {\n    return fs.exists(filename, callback)\n  }\n  return new Promise(resolve => {\n    return fs.exists(filename, resolve)\n  })\n}\n\n// fs.read(), fs.write(), fs.readv(), & fs.writev() need special treatment due to multiple callback args\n\nexports.read = function (fd, buffer, offset, length, position, callback) {\n  if (typeof callback === 'function') {\n    return fs.read(fd, buffer, offset, length, position, callback)\n  }\n  return new Promise((resolve, reject) => {\n    fs.read(fd, buffer, offset, length, position, (err, bytesRead, buffer) => {\n      if (err) return reject(err)\n      resolve({ bytesRead, buffer })\n    })\n  })\n}\n\n// Function signature can be\n// fs.write(fd, buffer[, offset[, length[, position]]], callback)\n// OR\n// fs.write(fd, string[, position[, encoding]], callback)\n// We need to handle both cases, so we use ...args\nexports.write = function (fd, buffer, ...args) {\n  if (typeof args[args.length - 1] === 'function') {\n    return fs.write(fd, buffer, ...args)\n  }\n\n  return new Promise((resolve, reject) => {\n    fs.write(fd, buffer, ...args, (err, bytesWritten, buffer) => {\n      if (err) return reject(err)\n      resolve({ bytesWritten, buffer })\n    })\n  })\n}\n\n// Function signature is\n// s.readv(fd, buffers[, position], callback)\n// We need to handle the optional arg, so we use ...args\nexports.readv = function (fd, buffers, ...args) {\n  if (typeof args[args.length - 1] === 'function') {\n    return fs.readv(fd, buffers, ...args)\n  }\n\n  return new Promise((resolve, reject) => {\n    fs.readv(fd, buffers, ...args, (err, bytesRead, buffers) => {\n      if (err) return reject(err)\n      resolve({ bytesRead, buffers })\n    })\n  })\n}\n\n// Function signature is\n// s.writev(fd, buffers[, position], callback)\n// We need to handle the optional arg, so we use ...args\nexports.writev = function (fd, buffers, ...args) {\n  if (typeof args[args.length - 1] === 'function') {\n    return fs.writev(fd, buffers, ...args)\n  }\n\n  return new Promise((resolve, reject) => {\n    fs.writev(fd, buffers, ...args, (err, bytesWritten, buffers) => {\n      if (err) return reject(err)\n      resolve({ bytesWritten, buffers })\n    })\n  })\n}\n\n// fs.realpath.native sometimes not available if fs is monkey-patched\nif (typeof fs.realpath.native === 'function') {\n  exports.realpath.native = u(fs.realpath.native)\n} else {\n  process.emitWarning(\n    'fs.realpath.native is not a function. Is fs being monkey-patched?',\n    'Warning', 'fs-extra-WARN0003'\n  )\n}\n", "\n\nconst u = require('universalify').fromPromise\nmodule.exports = {\n  copy: u(require('./copy')),\n  copySync: require('./copy-sync')\n}\n", "\n\nconst fs = require('../fs')\nconst path = require('path')\nconst { mkdirs } = require('../mkdirs')\nconst { pathExists } = require('../path-exists')\nconst { utimesMillis } = require('../util/utimes')\nconst stat = require('../util/stat')\n\nasync function copy (src, dest, opts = {}) {\n  if (typeof opts === 'function') {\n    opts = { filter: opts }\n  }\n\n  opts.clobber = 'clobber' in opts ? !!opts.clobber : true // default to true for now\n  opts.overwrite = 'overwrite' in opts ? !!opts.overwrite : opts.clobber // overwrite falls back to clobber\n\n  // Warn about using preserveTimestamps on 32-bit node\n  if (opts.preserveTimestamps && process.arch === 'ia32') {\n    process.emitWarning(\n      'Using the preserveTimestamps option in 32-bit node is not recommended;\\n\\n' +\n      '\\tsee https://github.com/jprichardson/node-fs-extra/issues/269',\n      'Warning', 'fs-extra-WARN0001'\n    )\n  }\n\n  const { srcStat, destStat } = await stat.checkPaths(src, dest, 'copy', opts)\n\n  await stat.checkParentPaths(src, srcStat, dest, 'copy')\n\n  const include = await runFilter(src, dest, opts)\n\n  if (!include) return\n\n  // check if the parent of dest exists, and create it if it doesn't exist\n  const destParent = path.dirname(dest)\n  const dirExists = await pathExists(destParent)\n  if (!dirExists) {\n    await mkdirs(destParent)\n  }\n\n  await getStatsAndPerformCopy(destStat, src, dest, opts)\n}\n\nasync function runFilter (src, dest, opts) {\n  if (!opts.filter) return true\n  return opts.filter(src, dest)\n}\n\nasync function getStatsAndPerformCopy (destStat, src, dest, opts) {\n  const statFn = opts.dereference ? fs.stat : fs.lstat\n  const srcStat = await statFn(src)\n\n  if (srcStat.isDirectory()) return onDir(srcStat, destStat, src, dest, opts)\n\n  if (\n    srcStat.isFile() ||\n    srcStat.isCharacterDevice() ||\n    srcStat.isBlockDevice()\n  ) return onFile(srcStat, destStat, src, dest, opts)\n\n  if (srcStat.isSymbolicLink()) return onLink(destStat, src, dest, opts)\n  if (srcStat.isSocket()) throw new Error(`Cannot copy a socket file: ${src}`)\n  if (srcStat.isFIFO()) throw new Error(`Cannot copy a FIFO pipe: ${src}`)\n  throw new Error(`Unknown file: ${src}`)\n}\n\nasync function onFile (srcStat, destStat, src, dest, opts) {\n  if (!destStat) return copyFile(srcStat, src, dest, opts)\n\n  if (opts.overwrite) {\n    await fs.unlink(dest)\n    return copyFile(srcStat, src, dest, opts)\n  }\n  if (opts.errorOnExist) {\n    throw new Error(`'${dest}' already exists`)\n  }\n}\n\nasync function copyFile (srcStat, src, dest, opts) {\n  await fs.copyFile(src, dest)\n  if (opts.preserveTimestamps) {\n    // Make sure the file is writable before setting the timestamp\n    // otherwise open fails with EPERM when invoked with 'r+'\n    // (through utimes call)\n    if (fileIsNotWritable(srcStat.mode)) {\n      await makeFileWritable(dest, srcStat.mode)\n    }\n\n    // Set timestamps and mode correspondingly\n\n    // Note that The initial srcStat.atime cannot be trusted\n    // because it is modified by the read(2) system call\n    // (See https://nodejs.org/api/fs.html#fs_stat_time_values)\n    const updatedSrcStat = await fs.stat(src)\n    await utimesMillis(dest, updatedSrcStat.atime, updatedSrcStat.mtime)\n  }\n\n  return fs.chmod(dest, srcStat.mode)\n}\n\nfunction fileIsNotWritable (srcMode) {\n  return (srcMode & 0o200) === 0\n}\n\nfunction makeFileWritable (dest, srcMode) {\n  return fs.chmod(dest, srcMode | 0o200)\n}\n\nasync function onDir (srcStat, destStat, src, dest, opts) {\n  // the dest directory might not exist, create it\n  if (!destStat) {\n    await fs.mkdir(dest)\n  }\n\n  const promises = []\n\n  // loop through the files in the current directory to copy everything\n  for await (const item of await fs.opendir(src)) {\n    const srcItem = path.join(src, item.name)\n    const destItem = path.join(dest, item.name)\n\n    promises.push(\n      runFilter(srcItem, destItem, opts).then(include => {\n        if (include) {\n          // only copy the item if it matches the filter function\n          return stat.checkPaths(srcItem, destItem, 'copy', opts).then(({ destStat }) => {\n            // If the item is a copyable file, `getStatsAndPerformCopy` will copy it\n            // If the item is a directory, `getStatsAndPerformCopy` will call `onDir` recursively\n            return getStatsAndPerformCopy(destStat, srcItem, destItem, opts)\n          })\n        }\n      })\n    )\n  }\n\n  await Promise.all(promises)\n\n  if (!destStat) {\n    await fs.chmod(dest, srcStat.mode)\n  }\n}\n\nasync function onLink (destStat, src, dest, opts) {\n  let resolvedSrc = await fs.readlink(src)\n  if (opts.dereference) {\n    resolvedSrc = path.resolve(process.cwd(), resolvedSrc)\n  }\n  if (!destStat) {\n    return fs.symlink(resolvedSrc, dest)\n  }\n\n  let resolvedDest = null\n  try {\n    resolvedDest = await fs.readlink(dest)\n  } catch (e) {\n    // dest exists and is a regular file or directory,\n    // Windows may throw UNKNOWN error. If dest already exists,\n    // fs throws error anyway, so no need to guard against it here.\n    if (e.code === 'EINVAL' || e.code === 'UNKNOWN') return fs.symlink(resolvedSrc, dest)\n    throw e\n  }\n  if (opts.dereference) {\n    resolvedDest = path.resolve(process.cwd(), resolvedDest)\n  }\n  if (stat.isSrcSubdir(resolvedSrc, resolvedDest)) {\n    throw new Error(`Cannot copy '${resolvedSrc}' to a subdirectory of itself, '${resolvedDest}'.`)\n  }\n\n  // do not copy if src is a subdir of dest since unlinking\n  // dest in this case would result in removing src contents\n  // and therefore a broken symlink would be created.\n  if (stat.isSrcSubdir(resolvedDest, resolvedSrc)) {\n    throw new Error(`Cannot overwrite '${resolvedDest}' with '${resolvedSrc}'.`)\n  }\n\n  // copy the link\n  await fs.unlink(dest)\n  return fs.symlink(resolvedSrc, dest)\n}\n\nmodule.exports = copy\n", "\nconst u = require('universalify').fromPromise\nconst { makeDir: _makeDir, makeDirSync } = require('./make-dir')\nconst makeDir = u(_makeDir)\n\nmodule.exports = {\n  mkdirs: makeDir,\n  mkdirsSync: makeDirSync,\n  // alias\n  mkdirp: makeDir,\n  mkdirpSync: makeDirSync,\n  ensureDir: makeDir,\n  ensureDirSync: makeDirSync\n}\n", "\nconst fs = require('../fs')\nconst { checkPath } = require('./utils')\n\nconst getMode = options => {\n  const defaults = { mode: 0o777 }\n  if (typeof options === 'number') return options\n  return ({ ...defaults, ...options }).mode\n}\n\nmodule.exports.makeDir = async (dir, options) => {\n  checkPath(dir)\n\n  return fs.mkdir(dir, {\n    mode: getMode(options),\n    recursive: true\n  })\n}\n\nmodule.exports.makeDirSync = (dir, options) => {\n  checkPath(dir)\n\n  return fs.mkdirSync(dir, {\n    mode: getMode(options),\n    recursive: true\n  })\n}\n", "// Adapted from https://github.com/sindresorhus/make-dir\n// Copyright (c) Sindre Sorhus <<EMAIL>> (sindresorhus.com)\n// Permission is hereby granted, free of charge, to any person obtaining a copy of this software and associated documentation files (the \"Software\"), to deal in the Software without restriction, including without limitation the rights to use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of the Software, and to permit persons to whom the Software is furnished to do so, subject to the following conditions:\n// The above copyright notice and this permission notice shall be included in all copies or substantial portions of the Software.\n// THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n\nconst path = require('path')\n\n// https://github.com/nodejs/node/issues/8987\n// https://github.com/libuv/libuv/pull/1088\nmodule.exports.checkPath = function checkPath (pth) {\n  if (process.platform === 'win32') {\n    const pathHasInvalidWinCharacters = /[<>:\"|?*]/.test(pth.replace(path.parse(pth).root, ''))\n\n    if (pathHasInvalidWinCharacters) {\n      const error = new Error(`Path contains invalid characters: ${pth}`)\n      error.code = 'EINVAL'\n      throw error\n    }\n  }\n}\n", "\nconst u = require('universalify').fromPromise\nconst fs = require('../fs')\n\nfunction pathExists (path) {\n  return fs.access(path).then(() => true).catch(() => false)\n}\n\nmodule.exports = {\n  pathExists: u(pathExists),\n  pathExistsSync: fs.existsSync\n}\n", "\n\nconst fs = require('../fs')\nconst u = require('universalify').fromPromise\n\nasync function utimesMillis (path, atime, mtime) {\n  // if (!HAS_MILLIS_RES) return fs.utimes(path, atime, mtime, callback)\n  const fd = await fs.open(path, 'r+')\n\n  let closeErr = null\n\n  try {\n    await fs.futimes(fd, atime, mtime)\n  } finally {\n    try {\n      await fs.close(fd)\n    } catch (e) {\n      closeErr = e\n    }\n  }\n\n  if (closeErr) {\n    throw closeErr\n  }\n}\n\nfunction utimesMillisSync (path, atime, mtime) {\n  const fd = fs.openSync(path, 'r+')\n  fs.futimesSync(fd, atime, mtime)\n  return fs.closeSync(fd)\n}\n\nmodule.exports = {\n  utimesMillis: u(utimesMillis),\n  utimesMillisSync\n}\n", "\n\nconst fs = require('../fs')\nconst path = require('path')\nconst u = require('universalify').fromPromise\n\nfunction getStats (src, dest, opts) {\n  const statFunc = opts.dereference\n    ? (file) => fs.stat(file, { bigint: true })\n    : (file) => fs.lstat(file, { bigint: true })\n  return Promise.all([\n    statFunc(src),\n    statFunc(dest).catch(err => {\n      if (err.code === 'ENOENT') return null\n      throw err\n    })\n  ]).then(([srcStat, destStat]) => ({ srcStat, destStat }))\n}\n\nfunction getStatsSync (src, dest, opts) {\n  let destStat\n  const statFunc = opts.dereference\n    ? (file) => fs.statSync(file, { bigint: true })\n    : (file) => fs.lstatSync(file, { bigint: true })\n  const srcStat = statFunc(src)\n  try {\n    destStat = statFunc(dest)\n  } catch (err) {\n    if (err.code === 'ENOENT') return { srcStat, destStat: null }\n    throw err\n  }\n  return { srcStat, destStat }\n}\n\nasync function checkPaths (src, dest, funcName, opts) {\n  const { srcStat, destStat } = await getStats(src, dest, opts)\n  if (destStat) {\n    if (areIdentical(srcStat, destStat)) {\n      const srcBaseName = path.basename(src)\n      const destBaseName = path.basename(dest)\n      if (funcName === 'move' &&\n        srcBaseName !== destBaseName &&\n        srcBaseName.toLowerCase() === destBaseName.toLowerCase()) {\n        return { srcStat, destStat, isChangingCase: true }\n      }\n      throw new Error('Source and destination must not be the same.')\n    }\n    if (srcStat.isDirectory() && !destStat.isDirectory()) {\n      throw new Error(`Cannot overwrite non-directory '${dest}' with directory '${src}'.`)\n    }\n    if (!srcStat.isDirectory() && destStat.isDirectory()) {\n      throw new Error(`Cannot overwrite directory '${dest}' with non-directory '${src}'.`)\n    }\n  }\n\n  if (srcStat.isDirectory() && isSrcSubdir(src, dest)) {\n    throw new Error(errMsg(src, dest, funcName))\n  }\n\n  return { srcStat, destStat }\n}\n\nfunction checkPathsSync (src, dest, funcName, opts) {\n  const { srcStat, destStat } = getStatsSync(src, dest, opts)\n\n  if (destStat) {\n    if (areIdentical(srcStat, destStat)) {\n      const srcBaseName = path.basename(src)\n      const destBaseName = path.basename(dest)\n      if (funcName === 'move' &&\n        srcBaseName !== destBaseName &&\n        srcBaseName.toLowerCase() === destBaseName.toLowerCase()) {\n        return { srcStat, destStat, isChangingCase: true }\n      }\n      throw new Error('Source and destination must not be the same.')\n    }\n    if (srcStat.isDirectory() && !destStat.isDirectory()) {\n      throw new Error(`Cannot overwrite non-directory '${dest}' with directory '${src}'.`)\n    }\n    if (!srcStat.isDirectory() && destStat.isDirectory()) {\n      throw new Error(`Cannot overwrite directory '${dest}' with non-directory '${src}'.`)\n    }\n  }\n\n  if (srcStat.isDirectory() && isSrcSubdir(src, dest)) {\n    throw new Error(errMsg(src, dest, funcName))\n  }\n  return { srcStat, destStat }\n}\n\n// recursively check if dest parent is a subdirectory of src.\n// It works for all file types including symlinks since it\n// checks the src and dest inodes. It starts from the deepest\n// parent and stops once it reaches the src parent or the root path.\nasync function checkParentPaths (src, srcStat, dest, funcName) {\n  const srcParent = path.resolve(path.dirname(src))\n  const destParent = path.resolve(path.dirname(dest))\n  if (destParent === srcParent || destParent === path.parse(destParent).root) return\n\n  let destStat\n  try {\n    destStat = await fs.stat(destParent, { bigint: true })\n  } catch (err) {\n    if (err.code === 'ENOENT') return\n    throw err\n  }\n\n  if (areIdentical(srcStat, destStat)) {\n    throw new Error(errMsg(src, dest, funcName))\n  }\n\n  return checkParentPaths(src, srcStat, destParent, funcName)\n}\n\nfunction checkParentPathsSync (src, srcStat, dest, funcName) {\n  const srcParent = path.resolve(path.dirname(src))\n  const destParent = path.resolve(path.dirname(dest))\n  if (destParent === srcParent || destParent === path.parse(destParent).root) return\n  let destStat\n  try {\n    destStat = fs.statSync(destParent, { bigint: true })\n  } catch (err) {\n    if (err.code === 'ENOENT') return\n    throw err\n  }\n  if (areIdentical(srcStat, destStat)) {\n    throw new Error(errMsg(src, dest, funcName))\n  }\n  return checkParentPathsSync(src, srcStat, destParent, funcName)\n}\n\nfunction areIdentical (srcStat, destStat) {\n  return destStat.ino && destStat.dev && destStat.ino === srcStat.ino && destStat.dev === srcStat.dev\n}\n\n// return true if dest is a subdir of src, otherwise false.\n// It only checks the path strings.\nfunction isSrcSubdir (src, dest) {\n  const srcArr = path.resolve(src).split(path.sep).filter(i => i)\n  const destArr = path.resolve(dest).split(path.sep).filter(i => i)\n  return srcArr.every((cur, i) => destArr[i] === cur)\n}\n\nfunction errMsg (src, dest, funcName) {\n  return `Cannot ${funcName} '${src}' to a subdirectory of itself, '${dest}'.`\n}\n\nmodule.exports = {\n  // checkPaths\n  checkPaths: u(checkPaths),\n  checkPathsSync,\n  // checkParent\n  checkParentPaths: u(checkParentPaths),\n  checkParentPathsSync,\n  // Misc\n  isSrcSubdir,\n  areIdentical\n}\n", "\n\nconst fs = require('graceful-fs')\nconst path = require('path')\nconst mkdirsSync = require('../mkdirs').mkdirsSync\nconst utimesMillisSync = require('../util/utimes').utimesMillisSync\nconst stat = require('../util/stat')\n\nfunction copySync (src, dest, opts) {\n  if (typeof opts === 'function') {\n    opts = { filter: opts }\n  }\n\n  opts = opts || {}\n  opts.clobber = 'clobber' in opts ? !!opts.clobber : true // default to true for now\n  opts.overwrite = 'overwrite' in opts ? !!opts.overwrite : opts.clobber // overwrite falls back to clobber\n\n  // Warn about using preserveTimestamps on 32-bit node\n  if (opts.preserveTimestamps && process.arch === 'ia32') {\n    process.emitWarning(\n      'Using the preserveTimestamps option in 32-bit node is not recommended;\\n\\n' +\n      '\\tsee https://github.com/jprichardson/node-fs-extra/issues/269',\n      'Warning', 'fs-extra-WARN0002'\n    )\n  }\n\n  const { srcStat, destStat } = stat.checkPathsSync(src, dest, 'copy', opts)\n  stat.checkParentPathsSync(src, srcStat, dest, 'copy')\n  if (opts.filter && !opts.filter(src, dest)) return\n  const destParent = path.dirname(dest)\n  if (!fs.existsSync(destParent)) mkdirsSync(destParent)\n  return getStats(destStat, src, dest, opts)\n}\n\nfunction getStats (destStat, src, dest, opts) {\n  const statSync = opts.dereference ? fs.statSync : fs.lstatSync\n  const srcStat = statSync(src)\n\n  if (srcStat.isDirectory()) return onDir(srcStat, destStat, src, dest, opts)\n  else if (srcStat.isFile() ||\n           srcStat.isCharacterDevice() ||\n           srcStat.isBlockDevice()) return onFile(srcStat, destStat, src, dest, opts)\n  else if (srcStat.isSymbolicLink()) return onLink(destStat, src, dest, opts)\n  else if (srcStat.isSocket()) throw new Error(`Cannot copy a socket file: ${src}`)\n  else if (srcStat.isFIFO()) throw new Error(`Cannot copy a FIFO pipe: ${src}`)\n  throw new Error(`Unknown file: ${src}`)\n}\n\nfunction onFile (srcStat, destStat, src, dest, opts) {\n  if (!destStat) return copyFile(srcStat, src, dest, opts)\n  return mayCopyFile(srcStat, src, dest, opts)\n}\n\nfunction mayCopyFile (srcStat, src, dest, opts) {\n  if (opts.overwrite) {\n    fs.unlinkSync(dest)\n    return copyFile(srcStat, src, dest, opts)\n  } else if (opts.errorOnExist) {\n    throw new Error(`'${dest}' already exists`)\n  }\n}\n\nfunction copyFile (srcStat, src, dest, opts) {\n  fs.copyFileSync(src, dest)\n  if (opts.preserveTimestamps) handleTimestamps(srcStat.mode, src, dest)\n  return setDestMode(dest, srcStat.mode)\n}\n\nfunction handleTimestamps (srcMode, src, dest) {\n  // Make sure the file is writable before setting the timestamp\n  // otherwise open fails with EPERM when invoked with 'r+'\n  // (through utimes call)\n  if (fileIsNotWritable(srcMode)) makeFileWritable(dest, srcMode)\n  return setDestTimestamps(src, dest)\n}\n\nfunction fileIsNotWritable (srcMode) {\n  return (srcMode & 0o200) === 0\n}\n\nfunction makeFileWritable (dest, srcMode) {\n  return setDestMode(dest, srcMode | 0o200)\n}\n\nfunction setDestMode (dest, srcMode) {\n  return fs.chmodSync(dest, srcMode)\n}\n\nfunction setDestTimestamps (src, dest) {\n  // The initial srcStat.atime cannot be trusted\n  // because it is modified by the read(2) system call\n  // (See https://nodejs.org/api/fs.html#fs_stat_time_values)\n  const updatedSrcStat = fs.statSync(src)\n  return utimesMillisSync(dest, updatedSrcStat.atime, updatedSrcStat.mtime)\n}\n\nfunction onDir (srcStat, destStat, src, dest, opts) {\n  if (!destStat) return mkDirAndCopy(srcStat.mode, src, dest, opts)\n  return copyDir(src, dest, opts)\n}\n\nfunction mkDirAndCopy (srcMode, src, dest, opts) {\n  fs.mkdirSync(dest)\n  copyDir(src, dest, opts)\n  return setDestMode(dest, srcMode)\n}\n\nfunction copyDir (src, dest, opts) {\n  const dir = fs.opendirSync(src)\n\n  try {\n    let dirent\n\n    while ((dirent = dir.readSync()) !== null) {\n      copyDirItem(dirent.name, src, dest, opts)\n    }\n  } finally {\n    dir.closeSync()\n  }\n}\n\nfunction copyDirItem (item, src, dest, opts) {\n  const srcItem = path.join(src, item)\n  const destItem = path.join(dest, item)\n  if (opts.filter && !opts.filter(srcItem, destItem)) return\n  const { destStat } = stat.checkPathsSync(srcItem, destItem, 'copy', opts)\n  return getStats(destStat, srcItem, destItem, opts)\n}\n\nfunction onLink (destStat, src, dest, opts) {\n  let resolvedSrc = fs.readlinkSync(src)\n  if (opts.dereference) {\n    resolvedSrc = path.resolve(process.cwd(), resolvedSrc)\n  }\n\n  if (!destStat) {\n    return fs.symlinkSync(resolvedSrc, dest)\n  } else {\n    let resolvedDest\n    try {\n      resolvedDest = fs.readlinkSync(dest)\n    } catch (err) {\n      // dest exists and is a regular file or directory,\n      // Windows may throw UNKNOWN error. If dest already exists,\n      // fs throws error anyway, so no need to guard against it here.\n      if (err.code === 'EINVAL' || err.code === 'UNKNOWN') return fs.symlinkSync(resolvedSrc, dest)\n      throw err\n    }\n    if (opts.dereference) {\n      resolvedDest = path.resolve(process.cwd(), resolvedDest)\n    }\n    if (stat.isSrcSubdir(resolvedSrc, resolvedDest)) {\n      throw new Error(`Cannot copy '${resolvedSrc}' to a subdirectory of itself, '${resolvedDest}'.`)\n    }\n\n    // prevent copy if src is a subdir of dest since unlinking\n    // dest in this case would result in removing src contents\n    // and therefore a broken symlink would be created.\n    if (stat.isSrcSubdir(resolvedDest, resolvedSrc)) {\n      throw new Error(`Cannot overwrite '${resolvedDest}' with '${resolvedSrc}'.`)\n    }\n    return copyLink(resolvedSrc, dest)\n  }\n}\n\nfunction copyLink (resolvedSrc, dest) {\n  fs.unlinkSync(dest)\n  return fs.symlinkSync(resolvedSrc, dest)\n}\n\nmodule.exports = copySync\n", "\n\nconst { createFile, createFileSync } = require('./file')\nconst { createLink, createLinkSync } = require('./link')\nconst { createSymlink, createSymlinkSync } = require('./symlink')\n\nmodule.exports = {\n  // file\n  createFile,\n  createFileSync,\n  ensureFile: createFile,\n  ensureFileSync: createFileSync,\n  // link\n  createLink,\n  createLinkSync,\n  ensureLink: createLink,\n  ensureLinkSync: createLinkSync,\n  // symlink\n  createSymlink,\n  createSymlinkSync,\n  ensureSymlink: createSymlink,\n  ensureSymlinkSync: createSymlinkSync\n}\n", "\n\nconst u = require('universalify').fromPromise\nconst jsonFile = require('./jsonfile')\n\njsonFile.outputJson = u(require('./output-json'))\njsonFile.outputJsonSync = require('./output-json-sync')\n// aliases\njsonFile.outputJSON = jsonFile.outputJson\njsonFile.outputJSONSync = jsonFile.outputJsonSync\njsonFile.writeJSON = jsonFile.writeJson\njsonFile.writeJSONSync = jsonFile.writeJsonSync\njsonFile.readJSON = jsonFile.readJson\njsonFile.readJSONSync = jsonFile.readJsonSync\n\nmodule.exports = jsonFile\n", "\n\nconst jsonFile = require('jsonfile')\n\nmodule.exports = {\n  // jsonfile exports\n  readJson: jsonFile.readFile,\n  readJsonSync: jsonFile.readFileSync,\n  writeJson: jsonFile.writeFile,\n  writeJsonSync: jsonFile.writeFileSync\n}\n", "\n\nconst { stringify } = require('jsonfile/utils')\nconst { outputFile } = require('../output-file')\n\nasync function outputJson (file, data, options = {}) {\n  const str = stringify(data, options)\n\n  await outputFile(file, str, options)\n}\n\nmodule.exports = outputJson\n", "\n\nconst u = require('universalify').fromPromise\nconst fs = require('../fs')\nconst path = require('path')\nconst mkdir = require('../mkdirs')\nconst pathExists = require('../path-exists').pathExists\n\nasync function outputFile (file, data, encoding = 'utf-8') {\n  const dir = path.dirname(file)\n\n  if (!(await pathExists(dir))) {\n    await mkdir.mkdirs(dir)\n  }\n\n  return fs.writeFile(file, data, encoding)\n}\n\nfunction outputFileSync (file, ...args) {\n  const dir = path.dirname(file)\n  if (!fs.existsSync(dir)) {\n    mkdir.mkdirsSync(dir)\n  }\n\n  fs.writeFileSync(file, ...args)\n}\n\nmodule.exports = {\n  outputFile: u(outputFile),\n  outputFileSync\n}\n", "\n\nconst { stringify } = require('jsonfile/utils')\nconst { outputFileSync } = require('../output-file')\n\nfunction outputJsonSync (file, data, options) {\n  const str = stringify(data, options)\n\n  outputFileSync(file, str, options)\n}\n\nmodule.exports = outputJsonSync\n", "\n\nconst u = require('universalify').fromPromise\nmodule.exports = {\n  move: u(require('./move')),\n  moveSync: require('./move-sync')\n}\n", "\n\nconst fs = require('../fs')\nconst path = require('path')\nconst { copy } = require('../copy')\nconst { remove } = require('../remove')\nconst { mkdirp } = require('../mkdirs')\nconst { pathExists } = require('../path-exists')\nconst stat = require('../util/stat')\n\nasync function move (src, dest, opts = {}) {\n  const overwrite = opts.overwrite || opts.clobber || false\n\n  const { srcStat, isChangingCase = false } = await stat.checkPaths(src, dest, 'move', opts)\n\n  await stat.checkParentPaths(src, srcStat, dest, 'move')\n\n  // If the parent of dest is not root, make sure it exists before proceeding\n  const destParent = path.dirname(dest)\n  const parsedParentPath = path.parse(destParent)\n  if (parsedParentPath.root !== destParent) {\n    await mkdirp(destParent)\n  }\n\n  return doRename(src, dest, overwrite, isChangingCase)\n}\n\nasync function doRename (src, dest, overwrite, isChangingCase) {\n  if (!isChangingCase) {\n    if (overwrite) {\n      await remove(dest)\n    } else if (await pathExists(dest)) {\n      throw new Error('dest already exists.')\n    }\n  }\n\n  try {\n    // Try w/ rename first, and try copy + remove if EXDEV\n    await fs.rename(src, dest)\n  } catch (err) {\n    if (err.code !== 'EXDEV') {\n      throw err\n    }\n    await moveAcrossDevice(src, dest, overwrite)\n  }\n}\n\nasync function moveAcrossDevice (src, dest, overwrite) {\n  const opts = {\n    overwrite,\n    errorOnExist: true,\n    preserveTimestamps: true\n  }\n\n  await copy(src, dest, opts)\n  return remove(src)\n}\n\nmodule.exports = move\n", "\n\nconst fs = require('graceful-fs')\nconst u = require('universalify').fromCallback\n\nfunction remove (path, callback) {\n  fs.rm(path, { recursive: true, force: true }, callback)\n}\n\nfunction removeSync (path) {\n  fs.rmSync(path, { recursive: true, force: true })\n}\n\nmodule.exports = {\n  remove: u(remove),\n  removeSync\n}\n", "\n\nconst fs = require('graceful-fs')\nconst path = require('path')\nconst copySync = require('../copy').copySync\nconst removeSync = require('../remove').removeSync\nconst mkdirpSync = require('../mkdirs').mkdirpSync\nconst stat = require('../util/stat')\n\nfunction moveSync (src, dest, opts) {\n  opts = opts || {}\n  const overwrite = opts.overwrite || opts.clobber || false\n\n  const { srcStat, isChangingCase = false } = stat.checkPathsSync(src, dest, 'move', opts)\n  stat.checkParentPathsSync(src, srcStat, dest, 'move')\n  if (!isParentRoot(dest)) mkdirpSync(path.dirname(dest))\n  return doRename(src, dest, overwrite, isChangingCase)\n}\n\nfunction isParentRoot (dest) {\n  const parent = path.dirname(dest)\n  const parsedPath = path.parse(parent)\n  return parsedPath.root === parent\n}\n\nfunction doRename (src, dest, overwrite, isChangingCase) {\n  if (isChangingCase) return rename(src, dest, overwrite)\n  if (overwrite) {\n    removeSync(dest)\n    return rename(src, dest, overwrite)\n  }\n  if (fs.existsSync(dest)) throw new Error('dest already exists.')\n  return rename(src, dest, overwrite)\n}\n\nfunction rename (src, dest, overwrite) {\n  try {\n    fs.renameSync(src, dest)\n  } catch (err) {\n    if (err.code !== 'EXDEV') throw err\n    return moveAcrossDevice(src, dest, overwrite)\n  }\n}\n\nfunction moveAcrossDevice (src, dest, overwrite) {\n  const opts = {\n    overwrite,\n    errorOnExist: true,\n    preserveTimestamps: true\n  }\n  copySync(src, dest, opts)\n  return removeSync(src)\n}\n\nmodule.exports = moveSync\n"]}