# 视频分析小程序

这是一个用于视频分析的微信小程序，专门用于分析视频中的C区和T区灰度值，计算T/C比值，并提供完整的数据管理功能。

## 主要功能

### 1. 视频分析
- **视频源支持**
  - 本地视频上传
  - 实时视频流采集
  
- **分析功能**
  - C区灰度值分析
  - T区灰度值分析
  - T/C比值计算
  - 分析结果可视化

### 2. 参数调节
- **视频参数**
  - 亮度 (0-100)
  - 对比度 (0-100)
  - 饱和度 (0-100)
  - 锐度 (0-100)
  - 伽马值 (0.1-2.0)
  - 白平衡 (2000K-15000K)
  - 背光补偿 (0-100)
  - 曝光度 (0.0-1.0)

- **分析参数**
  - 浓度设置 (0-1000 ng mL⁻¹)
  - 快速浓度选择

### 3. 数据管理
- **分析记录**
  - 支持临时分析和正式分析
  - 记录分析时间
  - 保存分析参数
  - 存储分析结果

- **数据导出**
  - CSV格式导出
  - 包含完整分析数据
  - 支持批量导出

### 4. 结果展示
- **数值展示**
  - C区值显示
  - T区值显示
  - T/C比值显示
  
- **图像展示**
  - C区分析图
  - T区分析图
  - 分析区域标记

## 使用说明

### 1. 视频分析流程
1. 选择视频源（本地上传或实时采集）
2. 调节视频参数（可选）
3. 设置分析参数
4. 点击开始分析
5. 查看分析结果
6. 选择保存或导出数据

### 2. 参数设置说明
- **视频参数调节**
  - 根据视频质量调整各项参数
  - 可保存参数预设
  - 支持参数快速重置

- **浓度设置**
  - 手动输入或快速选择
  - 范围：0-1000 ng mL⁻¹
  - 支持小数点输入

### 3. 数据管理操作
- **查看历史记录**
  - 支持时间排序
  - 可查看详细参数
  - 支持数据筛选

- **导出数据**
  - 选择要导出的记录
  - 点击导出按钮
  - 自动生成CSV文件

## 注意事项

1. 视频分析时请确保画面稳定，避免晃动
2. 参数调节建议从默认值开始逐步调整
3. 重要数据建议及时导出备份
4. 使用实时视频流时注意环境光线的影响

## 技术规格

- 开发框架：微信小程序
- 后端服务：云函数
- 存储方式：本地存储 + 云存储
- 视频格式：支持主流格式（mp4, avi等）
- 分析精度：灰度值精确到小数点后2位

## 更新日志

### v1.0.0
- 基础视频分析功能
- 参数调节系统
- 数据管理功能
- CSV导出支持

## 技术实现细节

### 1. 视频分析算法
- **帧提取**
  - 视频按照200ms间隔提取帧
  - 支持多种视频格式解码
  - 自动适应不同分辨率

- **区域分析**
  - C区定位：(455, 220, 500, 500)
  - T区定位：(940, 220, 500, 500)
  - 自动灰度值计算
  - RGB通道分离分析

- **灰度值计算**
  ```
  灰度值 = (R * 299 + G * 587 + B * 114) / 1000
  ```

### 2. 参数调节算法
- **亮度调节**
  - 范围：0-100
  - 线性映射到视频增益值

- **对比度调节**
  - 范围：0-100
  - 非线性曲线映射

- **饱和度调节**
  - 范围：0-100
  - RGB空间调整

- **伽马校正**
  - 范围：0.1-2.0
  - 指数曲线映射

### 3. 数据处理流程
1. **视频预处理**
   - 格式验证
   - 分辨率检查
   - 帧率调整

2. **区域提取**
   - 区域定位
   - 边界检查
   - 像素数据提取

3. **数值计算**
   - RGB值提取
   - 灰度转换
   - T/C值计算

4. **结果处理**
   - 数据格式化
   - 结果验证
   - 数据存储

### 4. 云函数实现
- **analyzeVideo**
  - 视频帧分析
  - 参数处理
  - 结果计算
  - 数据存储

- **processVideo**
  - 视频预处理
  - 参数应用
  - 实时分析

## 性能优化

### 1. 视频处理优化
- 使用多线程处理帧分析
- 实现帧缓存机制
- 优化内存使用

### 2. 数据处理优化
- 批量数据处理
- 异步存储操作
- 数据压缩存储

### 3. UI性能优化
- 虚拟列表实现
- 图片懒加载
- 防抖节流处理

## 开发环境要求

### 1. 开发工具
- 微信开发者工具
- Node.js环境
- 云开发环境

### 2. 依赖版本
- 小程序基础库：2.16.0+
- Node.js：12.0.0+
- npm：6.0.0+

### 3. 云环境配置
- 云存储：视频文件、分析图片
- 云数据库：分析结果、用户数据
- 云函数：视频处理、数据分析

## 部署说明

### 1. 环境准备
1. 注册微信小程序账号
2. 开通云开发功能
3. 配置开发环境

### 2. 项目配置
1. 克隆项目代码
2. 安装依赖包
3. 配置云环境ID

### 3. 发布步骤
1. 上传云函数
2. 部署云存储
3. 提交审核发布

## 常见问题解答（FAQ）

### 1. 视频分析相关

Q: 为什么我的视频无法上传？
A: 请检查以下几点：
- 视频格式是否为支持的格式（mp4, avi）
- 视频大小是否超过限制（建议小于100MB）
- 是否授予了相册访问权限
- 网络连接是否正常

Q: 分析结果不准确怎么办？
A: 可以尝试以下方法：
- 调整视频参数（亮度、对比度等）
- 确保视频画面稳定，避免晃动
- 检查C区和T区是否正确对准
- 使用标准样本进行校准

Q: 如何选择合适的参数？
A: 建议按照以下步骤：
1. 从默认参数开始
2. 先调节亮度和对比度
3. 再调节饱和度和锐度
4. 最后微调其他参数
5. 保存效果好的参数预设

### 2. 数据管理相关

Q: 如何备份分析数据？
A: 有以下几种方式：
- 使用CSV导出功能
- 定期同步到云存储
- 手动复制数据文件

Q: 历史记录丢失怎么办？
A: 可以尝试：
- 检查本地存储空间
- 从云端恢复数据
- 导入之前导出的备份

Q: 如何管理大量数据？
A: 建议：
- 定期整理和归档
- 使用标签分类
- 及时导出重要数据
- 删除不需要的记录

### 3. 系统相关

Q: 小程序打开很慢怎么办？
A: 可以检查：
- 网络连接状态
- 清理小程序缓存
- 检查设备存储空间
- 重启小程序或设备

Q: 云函数调用失败？
A: 请确认：
- 云开发环境是否正确配置
- 云函数是否已部署
- 网络连接是否正常
- 检查错误日志

## 故障排除指南

### 1. 视频分析故障

#### 症状：视频无法加载
- 检查视频格式是否支持
- 验证文件完整性
- 检查存储权限
- 确认网络连接

#### 症状：分析结果异常
- 检查参数设置
- 验证视频质量
- 确认分析区域定位
- 查看错误日志

#### 症状：参数调节无效
- 重启分析流程
- 清理缓存数据
- 检查参数范围
- 重新加载视频

### 2. 数据管理故障

#### 症状：数据保存失败
- 检查存储空间
- 验证数据格式
- 确认权限设置
- 尝试重新保存

#### 症状：导出失败
- 检查文件权限
- 确认存储空间
- 验证数据完整性
- 尝试分批导出

### 3. 系统故障

#### 症状：小程序崩溃
- 清理缓存
- 重新安装
- 检查系统版本
- 报告错误日志

#### 症状：云函数超时
- 优化处理逻辑
- 增加超时时间
- 分批处理数据
- 检查网络状态

## 维护和更新

### 1. 日常维护
- 定期检查日志
- 清理临时文件
- 优化数据存储
- 更新参数配置

### 2. 版本更新
- 功能优化
- 性能提升
- 问题修复
- 新功能添加

### 3. 数据维护
- 定期备份
- 清理无效数据
- 优化存储结构
- 更新数据格式

## 贡献指南

### 1. 如何贡献

#### 提交问题
- 使用 GitHub Issues 提交问题
- 清晰描述问题现象
- 提供复现步骤
- 附上相关日志或截图

#### 提交代码
1. Fork 项目仓库
2. 创建特性分支
3. 提交代码变更
4. 创建 Pull Request

#### 文档改进
- 修正文档错误
- 补充使用说明
- 完善技术文档
- 添加示例说明

### 2. 开发规范

#### 代码规范
- 遵循 ESLint 配置
- 使用 Prettier 格式化
- 编写单元测试
- 添加注释说明

#### 提交规范
- 使用语义化提交信息
- 每次提交专注单一功能
- 保持提交记录清晰
- 及时同步主分支

#### 文档规范
- 使用 Markdown 格式
- 保持文档结构清晰
- 使用恰当的标题层级
- 添加必要的示例

### 3. 版本发布

#### 版本号规范
- 主版本号：重大更新
- 次版本号：功能更新
- 修订号：问题修复

#### 发布流程
1. 更新版本号
2. 生成更新日志
3. 打包发布文件
4. 发布新版本

## 许可证

### MIT License

Copyright (c) 2024 [项目作者]

Permission is hereby granted, free of charge, to any person obtaining a copy
of this software and associated documentation files (the "Software"), to deal
in the Software without restriction, including without limitation the rights
to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
copies of the Software, and to permit persons to whom the Software is
furnished to do so, subject to the following conditions:

The above copyright notice and this permission notice shall be included in all
copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE
SOFTWARE.

## 联系方式

### 技术支持
- 邮箱：<EMAIL>
- 微信：support_wechat
- QQ群：123456789

### 商务合作
- 邮箱：<EMAIL>
- 电话：+86-123-4567-8900

### 问题反馈
- GitHub Issues
- 微信公众号
- 官方网站
