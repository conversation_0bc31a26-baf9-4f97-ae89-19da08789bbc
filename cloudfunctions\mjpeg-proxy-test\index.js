// 云函数：MJPEG流代理测试
const cloud = require('wx-server-sdk');
const http = require('http');
const https = require('https');

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
});

/**
 * 测试云函数是否能处理MJPEG流
 * @param {Object} event - 包含deviceIP, streamPath, timeout等参数
 */
exports.main = async (event, context) => {
  console.log('☁️ MJPEG代理测试开始:', event);

  const { deviceIP, streamPath = '/stream', timeout = 8000 } = event;

  if (!deviceIP) {
    return {
      success: false,
      error: '缺少设备IP参数'
    };
  }

  const streamUrl = `http://${deviceIP}${streamPath}`;
  console.log(`☁️ 测试目标: ${streamUrl}`);

  try {
    // 快速测试MJPEG流处理（优化版）
    const result = await testMJPEGStreamFast(streamUrl, timeout);

    console.log('☁️ MJPEG流测试结果:', result);
    return result;

  } catch (error) {
    console.error('☁️ MJPEG流测试失败:', error);
    return {
      success: false,
      error: error.message,
      details: error
    };
  }
};

/**
 * 快速测试MJPEG流处理能力（优化版）
 */
function testMJPEGStreamFast(streamUrl, timeout) {
  return new Promise((resolve, reject) => {
    const startTime = Date.now();
    console.log(`☁️ 快速测试MJPEG流: ${streamUrl}`);

    // 设置更短的超时时间
    const quickTimeout = Math.min(timeout, 6000);

    const request = http.get(streamUrl, {
      timeout: quickTimeout,
      headers: {
        'Accept': 'multipart/x-mixed-replace,image/jpeg,*/*',
        'Cache-Control': 'no-cache',
        'User-Agent': 'CloudFunction-Quick-Test'
      }
    }, (response) => {
      console.log(`☁️ 快速响应状态: ${response.statusCode}`);
      console.log(`☁️ Content-Type: ${response.headers['content-type']}`);

      if (response.statusCode !== 200) {
        request.destroy();
        resolve({
          success: false,
          error: `HTTP错误: ${response.statusCode}`,
          statusCode: response.statusCode,
          headers: response.headers,
          responseTime: Date.now() - startTime
        });
        return;
      }

      let receivedData = Buffer.alloc(0);
      let dataReceived = false;

      // 快速数据检测 - 只需要接收少量数据就能判断
      const quickDataTimeout = setTimeout(() => {
        request.destroy();

        const result = {
          success: true,
          message: '快速测试完成',
          canConnect: true,
          dataSize: receivedData.length,
          responseTime: Date.now() - startTime,
          contentType: response.headers['content-type'],
          transferEncoding: response.headers['transfer-encoding'],
          dataFormat: receivedData.length > 0 ? analyzeDataFormat(receivedData) : 'no_data',
          isStreamingResponse: response.headers['transfer-encoding'] === 'chunked',
          conclusion: receivedData.length > 0 ? 'MJPEG流可访问，云函数可以处理' : '连接成功但无数据'
        };

        console.log('☁️ 快速测试结果:', result);
        resolve(result);
      }, 2000); // 只等待2秒数据

      response.on('data', (chunk) => {
        dataReceived = true;
        receivedData = Buffer.concat([receivedData, chunk]);
        console.log(`☁️ 快速接收: ${chunk.length} bytes (总计: ${receivedData.length})`);

        // 如果接收到足够数据，立即结束测试
        if (receivedData.length > 1000) {
          clearTimeout(quickDataTimeout);
          request.destroy();

          resolve({
            success: true,
            message: '快速测试成功 - 检测到数据流',
            canConnect: true,
            dataSize: receivedData.length,
            responseTime: Date.now() - startTime,
            contentType: response.headers['content-type'],
            transferEncoding: response.headers['transfer-encoding'],
            dataFormat: analyzeDataFormat(receivedData),
            hasJPEGFrame: findJPEGStart(receivedData) !== -1,
            isStreamingResponse: true,
            conclusion: 'MJPEG流正常，云函数完全可以处理！'
          });
        }
      });

      response.on('error', (error) => {
        clearTimeout(quickDataTimeout);
        console.error('☁️ 快速响应错误:', error);
        resolve({
          success: false,
          error: error.message,
          responseTime: Date.now() - startTime
        });
      });
    });

    request.on('timeout', () => {
      console.log('☁️ 快速请求超时');
      request.destroy();
      resolve({
        success: false,
        error: '连接超时',
        responseTime: Date.now() - startTime,
        conclusion: '设备响应太慢或网络问题'
      });
    });

    request.on('error', (error) => {
      console.error('☁️ 快速请求错误:', error);
      resolve({
        success: false,
        error: error.message,
        responseTime: Date.now() - startTime,
        conclusion: '网络连接失败'
      });
    });
  });
}

/**
 * 测试MJPEG流处理能力（完整版 - 备用）
 */
function testMJPEGStream(streamUrl, timeout) {
  return new Promise((resolve, reject) => {
    const startTime = Date.now();
    console.log(`☁️ 开始请求MJPEG流: ${streamUrl}`);
    
    const request = http.get(streamUrl, {
      timeout: timeout,
      headers: {
        'Accept': 'multipart/x-mixed-replace,image/jpeg,*/*',
        'Cache-Control': 'no-cache',
        'Connection': 'keep-alive',
        'User-Agent': 'CloudFunction-MJPEG-Test'
      }
    }, (response) => {
      console.log(`☁️ 响应状态: ${response.statusCode}`);
      console.log(`☁️ 响应头:`, response.headers);
      
      if (response.statusCode !== 200) {
        reject(new Error(`HTTP错误: ${response.statusCode}`));
        return;
      }

      let receivedData = Buffer.alloc(0);
      let frameCount = 0;
      let firstFrameFound = false;
      
      // 设置数据接收超时
      const dataTimeout = setTimeout(() => {
        request.destroy();
        
        if (receivedData.length > 0) {
          // 有数据但可能不完整
          resolve({
            success: true,
            message: '部分数据接收成功',
            dataSize: receivedData.length,
            responseTime: Date.now() - startTime,
            frameCount: frameCount,
            dataFormat: analyzeDataFormat(receivedData),
            contentType: response.headers['content-type'],
            isPartialData: true
          });
        } else {
          reject(new Error('数据接收超时'));
        }
      }, Math.min(timeout, 10000)); // 最多等待10秒数据

      response.on('data', (chunk) => {
        console.log(`☁️ 接收数据块: ${chunk.length} bytes`);
        receivedData = Buffer.concat([receivedData, chunk]);
        
        // 检查是否包含JPEG帧
        if (!firstFrameFound) {
          const jpegStart = findJPEGStart(receivedData);
          if (jpegStart !== -1) {
            firstFrameFound = true;
            frameCount++;
            console.log(`☁️ 发现第一个JPEG帧，位置: ${jpegStart}`);
          }
        }
        
        // 如果已经接收到足够数据进行测试，提前结束
        if (receivedData.length > 50000 || frameCount > 0) {
          clearTimeout(dataTimeout);
          request.destroy();
          
          resolve({
            success: true,
            message: '成功接收MJPEG流数据',
            dataSize: receivedData.length,
            responseTime: Date.now() - startTime,
            frameCount: frameCount,
            dataFormat: analyzeDataFormat(receivedData),
            contentType: response.headers['content-type'],
            transferEncoding: response.headers['transfer-encoding'],
            isPartialData: false
          });
        }
      });

      response.on('end', () => {
        clearTimeout(dataTimeout);
        console.log(`☁️ 流结束，总接收: ${receivedData.length} bytes`);
        
        resolve({
          success: true,
          message: '流正常结束',
          dataSize: receivedData.length,
          responseTime: Date.now() - startTime,
          frameCount: frameCount,
          dataFormat: analyzeDataFormat(receivedData),
          contentType: response.headers['content-type'],
          isComplete: true
        });
      });

      response.on('error', (error) => {
        clearTimeout(dataTimeout);
        console.error('☁️ 响应错误:', error);
        reject(error);
      });
    });

    request.on('timeout', () => {
      console.log('☁️ 请求超时');
      request.destroy();
      reject(new Error('请求超时'));
    });

    request.on('error', (error) => {
      console.error('☁️ 请求错误:', error);
      reject(error);
    });
  });
}

/**
 * 分析数据格式
 */
function analyzeDataFormat(buffer) {
  if (buffer.length < 4) {
    return 'insufficient_data';
  }

  const firstBytes = Array.from(buffer.slice(0, 4));
  const hexString = firstBytes.map(b => '0x' + b.toString(16).padStart(2, '0')).join(' ');
  
  // 检查JPEG标记
  if (buffer[0] === 0xFF && buffer[1] === 0xD8) {
    return 'jpeg';
  }
  
  // 检查HTML
  const textStart = buffer.toString('utf8', 0, Math.min(100, buffer.length));
  if (textStart.includes('<html') || textStart.includes('<!DOCTYPE')) {
    return 'html';
  }
  
  // 检查MJPEG边界
  if (textStart.includes('--') && textStart.includes('boundary')) {
    return 'mjpeg_multipart';
  }
  
  return {
    format: 'unknown',
    firstBytes: hexString,
    textPreview: textStart.substring(0, 50)
  };
}

/**
 * 查找JPEG开始标记
 */
function findJPEGStart(buffer) {
  for (let i = 0; i < buffer.length - 1; i++) {
    if (buffer[i] === 0xFF && buffer[i + 1] === 0xD8) {
      return i;
    }
  }
  return -1;
}
