{"version": 3, "sources": ["index.js"], "names": [], "mappings": ";;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.js", "sourcesContent": ["\n\nvar GetIntrinsic = require('get-intrinsic');\nvar callBound = require('call-bound');\nvar inspect = require('object-inspect');\n\nvar $TypeError = require('es-errors/type');\nvar $Map = GetIntrinsic('%Map%', true);\n\n/** @type {<K, V>(thisArg: Map<K, V>, key: K) => V} */\nvar $mapGet = callBound('Map.prototype.get', true);\n/** @type {<K, V>(thisArg: Map<K, V>, key: K, value: V) => void} */\nvar $mapSet = callBound('Map.prototype.set', true);\n/** @type {<K, V>(thisArg: Map<K, V>, key: K) => boolean} */\nvar $mapHas = callBound('Map.prototype.has', true);\n/** @type {<K, V>(thisArg: Map<K, V>, key: K) => boolean} */\nvar $mapDelete = callBound('Map.prototype.delete', true);\n/** @type {<K, V>(thisArg: Map<K, V>) => number} */\nvar $mapSize = callBound('Map.prototype.size', true);\n\n/** @type {import('.')} */\nmodule.exports = !!$Map && /** @type {Exclude<import('.'), false>} */ function getSideChannelMap() {\n\t/** @typedef {ReturnType<typeof getSideChannelMap>} Channel */\n\t/** @typedef {Parameters<Channel['get']>[0]} K */\n\t/** @typedef {Parameters<Channel['set']>[1]} V */\n\n\t/** @type {Map<K, V> | undefined} */ var $m;\n\n\t/** @type {Channel} */\n\tvar channel = {\n\t\tassert: function (key) {\n\t\t\tif (!channel.has(key)) {\n\t\t\t\tthrow new $TypeError('Side channel does not contain ' + inspect(key));\n\t\t\t}\n\t\t},\n\t\t'delete': function (key) {\n\t\t\tif ($m) {\n\t\t\t\tvar result = $mapDelete($m, key);\n\t\t\t\tif ($mapSize($m) === 0) {\n\t\t\t\t\t$m = void undefined;\n\t\t\t\t}\n\t\t\t\treturn result;\n\t\t\t}\n\t\t\treturn false;\n\t\t},\n\t\tget: function (key) { // eslint-disable-line consistent-return\n\t\t\tif ($m) {\n\t\t\t\treturn $mapGet($m, key);\n\t\t\t}\n\t\t},\n\t\thas: function (key) {\n\t\t\tif ($m) {\n\t\t\t\treturn $mapHas($m, key);\n\t\t\t}\n\t\t\treturn false;\n\t\t},\n\t\tset: function (key, value) {\n\t\t\tif (!$m) {\n\t\t\t\t// @ts-expect-error TS can't handle narrowing a variable inside a closure\n\t\t\t\t$m = new $Map();\n\t\t\t}\n\t\t\t$mapSet($m, key, value);\n\t\t}\n\t};\n\n\t// @ts-expect-error TODO: figure out why TS is erroring here\n\treturn channel;\n};\n"]}