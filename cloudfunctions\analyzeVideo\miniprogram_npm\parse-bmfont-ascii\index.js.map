{"version": 3, "sources": ["index.js"], "names": [], "mappings": ";;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.js", "sourcesContent": ["module.exports = function parseBMFontAscii(data) {\n  if (!data)\n    throw new Error('no data provided')\n  data = data.toString().trim()\n\n  var output = {\n    pages: [],\n    chars: [],\n    kernings: []\n  }\n\n  var lines = data.split(/\\r\\n?|\\n/g)\n\n  if (lines.length === 0)\n    throw new Error('no data in BMFont file')\n\n  for (var i = 0; i < lines.length; i++) {\n    var lineData = splitLine(lines[i], i)\n    if (!lineData) //skip empty lines\n      continue\n\n    if (lineData.key === 'page') {\n      if (typeof lineData.data.id !== 'number')\n        throw new Error('malformed file at line ' + i + ' -- needs page id=N')\n      if (typeof lineData.data.file !== 'string')\n        throw new Error('malformed file at line ' + i + ' -- needs page file=\"path\"')\n      output.pages[lineData.data.id] = lineData.data.file\n    } else if (lineData.key === 'chars' || lineData.key === 'kernings') {\n      //... do nothing for these two ...\n    } else if (lineData.key === 'char') {\n      output.chars.push(lineData.data)\n    } else if (lineData.key === 'kerning') {\n      output.kernings.push(lineData.data)\n    } else {\n      output[lineData.key] = lineData.data\n    }\n  }\n\n  return output\n}\n\nfunction splitLine(line, idx) {\n  line = line.replace(/\\t+/g, ' ').trim()\n  if (!line)\n    return null\n\n  var space = line.indexOf(' ')\n  if (space === -1) \n    throw new Error(\"no named row at line \" + idx)\n\n  var key = line.substring(0, space)\n\n  line = line.substring(space + 1)\n  //clear \"letter\" field as it is non-standard and\n  //requires additional complexity to parse \" / = symbols\n  line = line.replace(/letter=[\\'\\\"]\\S+[\\'\\\"]/gi, '')  \n  line = line.split(\"=\")\n  line = line.map(function(str) {\n    return str.trim().match((/(\".*?\"|[^\"\\s]+)+(?=\\s*|\\s*$)/g))\n  })\n\n  var data = []\n  for (var i = 0; i < line.length; i++) {\n    var dt = line[i]\n    if (i === 0) {\n      data.push({\n        key: dt[0],\n        data: \"\"\n      })\n    } else if (i === line.length - 1) {\n      data[data.length - 1].data = parseData(dt[0])\n    } else {\n      data[data.length - 1].data = parseData(dt[0])\n      data.push({\n        key: dt[1],\n        data: \"\"\n      })\n    }\n  }\n\n  var out = {\n    key: key,\n    data: {}\n  }\n\n  data.forEach(function(v) {\n    out.data[v.key] = v.data;\n  })\n\n  return out\n}\n\nfunction parseData(data) {\n  if (!data || data.length === 0)\n    return \"\"\n\n  if (data.indexOf('\"') === 0 || data.indexOf(\"'\") === 0)\n    return data.substring(1, data.length - 1)\n  if (data.indexOf(',') !== -1)\n    return parseIntList(data)\n  return parseInt(data, 10)\n}\n\nfunction parseIntList(data) {\n  return data.split(',').map(function(val) {\n    return parseInt(val, 10)\n  })\n}"]}