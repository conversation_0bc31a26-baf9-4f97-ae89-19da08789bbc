module.exports = (function() {
var __MODS__ = {};
var __DEFINE__ = function(modId, func, req) { var m = { exports: {}, _tempexports: {} }; __MODS__[modId] = { status: 0, func: func, req: req, m: m }; };
var __REQUIRE__ = function(modId, source) { if(!__MODS__[modId]) return require(source); if(!__MODS__[modId].status) { var m = __MODS__[modId].m; m._exports = m._tempexports; var desp = Object.getOwnPropertyDescriptor(m, "exports"); if (desp && desp.configurable) Object.defineProperty(m, "exports", { set: function (val) { if(typeof val === "object" && val !== m._exports) { m._exports.__proto__ = val.__proto__; Object.keys(val).forEach(function (k) { m._exports[k] = val[k]; }); } m._tempexports = val }, get: function () { return m._tempexports; } }); __MODS__[modId].status = 1; __MODS__[modId].func(__MODS__[modId].req, m, m.exports); } return __MODS__[modId].m.exports; };
var __REQUIRE_WILDCARD__ = function(obj) { if(obj && obj.__esModule) { return obj; } else { var newObj = {}; if(obj != null) { for(var k in obj) { if (Object.prototype.hasOwnProperty.call(obj, k)) newObj[k] = obj[k]; } } newObj.default = obj; return newObj; } };
var __REQUIRE_DEFAULT__ = function(obj) { return obj && obj.__esModule ? obj.default : obj; };
__DEFINE__(1750949389124, function(require, module, exports) {


Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.clone = clone;
exports.addLast = addLast;
exports.addFirst = addFirst;
exports.removeLast = removeLast;
exports.removeFirst = removeFirst;
exports.insert = insert;
exports.removeAt = removeAt;
exports.replaceAt = replaceAt;
exports.getIn = getIn;
exports.set = set;
exports.setIn = setIn;
exports.update = update;
exports.updateIn = updateIn;
exports.merge = merge;
exports.mergeDeep = mergeDeep;
exports.mergeIn = mergeIn;
exports.omit = omit;
exports.addDefaults = addDefaults;
exports.default = void 0;

/* eslint-disable @typescript-eslint/ban-types */

/*!
 * Timm
 *
 * Immutability helpers with fast reads and acceptable writes.
 *
 * @copyright Guillermo Grau Panea 2016
 * @license MIT
 */
const INVALID_ARGS = 'INVALID_ARGS';
const IS_DEV = process.env.NODE_ENV !== 'production';

// ===============================================
// ### Helpers
// ===============================================
function throwStr(msg) {
  throw new Error(msg);
}

function getKeysAndSymbols(obj) {
  const keys = Object.keys(obj);

  if (Object.getOwnPropertySymbols) {
    // @ts-ignore
    return keys.concat(Object.getOwnPropertySymbols(obj));
  }

  return keys;
}

const hasOwnProperty = {}.hasOwnProperty;

function clone(obj0) {
  // As array
  if (Array.isArray(obj0)) return obj0.slice(); // As object

  const obj = obj0;
  const keys = getKeysAndSymbols(obj);
  const out = {};

  for (let i = 0; i < keys.length; i++) {
    const key = keys[i];
    out[key] = obj[key];
  } // @ts-ignore (see type tests)


  return out;
} // Custom guard


function isObject(o) {
  return o != null && typeof o === 'object';
} // _deepFreeze = (obj) ->
//   Object.freeze obj
//   for key in Object.getOwnPropertyNames obj
//     val = obj[key]
//     if isObject(val) and not Object.isFrozen val
//       _deepFreeze val
//   obj
// ===============================================
// -- ### Arrays
// ===============================================
// -- #### addLast()
// -- Returns a new array with an appended item or items.
// --
// -- Usage: `addLast(array, val)`
// --
// -- ```js
// -- arr = ['a', 'b']
// -- arr2 = addLast(arr, 'c')
// -- // ['a', 'b', 'c']
// -- arr2 === arr
// -- // false
// -- arr3 = addLast(arr, ['c', 'd'])
// -- // ['a', 'b', 'c', 'd']
// -- ```
// `array.concat(val)` also handles the scalar case,
// but is apparently very slow


function addLast(array, val) {
  if (Array.isArray(val)) return array.concat(val);
  return array.concat([val]);
} // -- #### addFirst()
// -- Returns a new array with a prepended item or items.
// --
// -- Usage: `addFirst(array, val)`
// --
// -- ```js
// -- arr = ['a', 'b']
// -- arr2 = addFirst(arr, 'c')
// -- // ['c', 'a', 'b']
// -- arr2 === arr
// -- // false
// -- arr3 = addFirst(arr, ['c', 'd'])
// -- // ['c', 'd', 'a', 'b']
// -- ```


function addFirst(array, val) {
  if (Array.isArray(val)) return val.concat(array);
  return [val].concat(array);
} // -- #### removeLast()
// -- Returns a new array removing the last item.
// --
// -- Usage: `removeLast(array)`
// --
// -- ```js
// -- arr = ['a', 'b']
// -- arr2 = removeLast(arr)
// -- // ['a']
// -- arr2 === arr
// -- // false
// --
// -- // The same array is returned if there are no changes:
// -- arr3 = []
// -- removeLast(arr3) === arr3
// -- // true
// -- ```


function removeLast(array) {
  if (!array.length) return array;
  return array.slice(0, array.length - 1);
} // -- #### removeFirst()
// -- Returns a new array removing the first item.
// --
// -- Usage: `removeFirst(array)`
// --
// -- ```js
// -- arr = ['a', 'b']
// -- arr2 = removeFirst(arr)
// -- // ['b']
// -- arr2 === arr
// -- // false
// --
// -- // The same array is returned if there are no changes:
// -- arr3 = []
// -- removeFirst(arr3) === arr3
// -- // true
// -- ```


function removeFirst(array) {
  if (!array.length) return array;
  return array.slice(1);
} // -- #### insert()
// -- Returns a new array obtained by inserting an item or items
// -- at a specified index.
// --
// -- Usage: `insert(array, idx, val)`
// --
// -- ```js
// -- arr = ['a', 'b', 'c']
// -- arr2 = insert(arr, 1, 'd')
// -- // ['a', 'd', 'b', 'c']
// -- arr2 === arr
// -- // false
// -- insert(arr, 1, ['d', 'e'])
// -- // ['a', 'd', 'e', 'b', 'c']
// -- ```


function insert(array, idx, val) {
  return array.slice(0, idx).concat(Array.isArray(val) ? val : [val]).concat(array.slice(idx));
} // -- #### removeAt()
// -- Returns a new array obtained by removing an item at
// -- a specified index.
// --
// -- Usage: `removeAt(array, idx)`
// --
// -- ```js
// -- arr = ['a', 'b', 'c']
// -- arr2 = removeAt(arr, 1)
// -- // ['a', 'c']
// -- arr2 === arr
// -- // false
// --
// -- // The same array is returned if there are no changes:
// -- removeAt(arr, 4) === arr
// -- // true
// -- ```


function removeAt(array, idx) {
  if (idx >= array.length || idx < 0) return array;
  return array.slice(0, idx).concat(array.slice(idx + 1));
} // -- #### replaceAt()
// -- Returns a new array obtained by replacing an item at
// -- a specified index. If the provided item is the same as
// -- (*referentially equal to*) the previous item at that position,
// -- the original array is returned.
// --
// -- Usage: `replaceAt(array, idx, newItem)`
// --
// -- ```js
// -- arr = ['a', 'b', 'c']
// -- arr2 = replaceAt(arr, 1, 'd')
// -- // ['a', 'd', 'c']
// -- arr2 === arr
// -- // false
// --
// -- // The same object is returned if there are no changes:
// -- replaceAt(arr, 1, 'b') === arr
// -- // true
// -- ```


function replaceAt(array, idx, newItem) {
  if (array[idx] === newItem) return array;
  const len = array.length;
  const result = Array(len);

  for (let i = 0; i < len; i++) {
    result[i] = array[i];
  }

  result[idx] = newItem;
  return result;
} // ===============================================
// -- ### Collections (objects and arrays)
// ===============================================
// -- #### getIn()
// -- Returns a value from an object at a given path. Works with
// -- nested arrays and objects. If the path does not exist, it returns
// -- `undefined`.
// --
// -- Usage: `getIn(obj, path)`
// --
// -- ```js
// -- obj = { a: 1, b: 2, d: { d1: 3, d2: 4 }, e: ['a', 'b', 'c'] }
// -- getIn(obj, ['d', 'd1'])
// -- // 3
// -- getIn(obj, ['e', 1])
// -- // 'b'
// -- ```


function getIn(obj, path) {
  if (!Array.isArray(path)) {
    throwStr(IS_DEV ? 'A path array should be provided when calling getIn()' : INVALID_ARGS);
  }

  if (obj == null) return undefined;
  let ptr = obj;

  for (let i = 0; i < path.length; i++) {
    const key = path[i];
    ptr = ptr != null ? ptr[key] : undefined;
    if (ptr === undefined) return ptr;
  }

  return ptr;
} // -- #### set()
// -- Returns a new object with a modified attribute.
// -- If the provided value is the same as (*referentially equal to*)
// -- the previous value, the original object is returned.
// --
// -- Usage: `set(obj, key, val)`
// --
// -- ```js
// -- obj = { a: 1, b: 2, c: 3 }
// -- obj2 = set(obj, 'b', 5)
// -- // { a: 1, b: 5, c: 3 }
// -- obj2 === obj
// -- // false
// --
// -- // The same object is returned if there are no changes:
// -- set(obj, 'b', 2) === obj
// -- // true
// -- ```
// When called with an undefined/null `obj`, `set()` returns either
// a single-element array, or a single-key object


// Implementation
function set(obj0, key, val) {
  let obj = obj0;
  if (obj == null) obj = typeof key === 'number' ? [] : {};
  if (obj[key] === val) return obj;
  const obj2 = clone(obj);
  obj2[key] = val;
  return obj2;
} // -- #### setIn()
// -- Returns a new object with a modified **nested** attribute.
// --
// -- Notes:
// --
// -- * If the provided value is the same as (*referentially equal to*)
// -- the previous value, the original object is returned.
// -- * If the path does not exist, it will be created before setting
// -- the new value.
// --
// -- Usage: `setIn(obj, path, val)`
// --
// -- ```js
// -- obj = { a: 1, b: 2, d: { d1: 3, d2: 4 }, e: { e1: 'foo', e2: 'bar' } }
// -- obj2 = setIn(obj, ['d', 'd1'], 4)
// -- // { a: 1, b: 2, d: { d1: 4, d2: 4 }, e: { e1: 'foo', e2: 'bar' } }
// -- obj2 === obj
// -- // false
// -- obj2.d === obj.d
// -- // false
// -- obj2.e === obj.e
// -- // true
// --
// -- // The same object is returned if there are no changes:
// -- obj3 = setIn(obj, ['d', 'd1'], 3)
// -- // { a: 1, b: 2, d: { d1: 3, d2: 4 }, e: { e1: 'foo', e2: 'bar' } }
// -- obj3 === obj
// -- // true
// -- obj3.d === obj.d
// -- // true
// -- obj3.e === obj.e
// -- // true
// --
// -- // ... unknown paths create intermediate keys. Numeric segments are treated as array indices:
// -- setIn({ a: 3 }, ['unknown', 0, 'path'], 4)
// -- // { a: 3, unknown: [{ path: 4 }] }
// -- ```


function setIn(obj, path, val) {
  if (!path.length) return val;
  return doSetIn(obj, path, val, 0);
}

function doSetIn(obj, path, val, idx) {
  let newValue;
  const key = path[idx];

  if (idx === path.length - 1) {
    newValue = val;
  } else {
    const nestedObj = isObject(obj) && isObject(obj[key]) ? obj[key] : typeof path[idx + 1] === 'number' ? [] : {};
    newValue = doSetIn(nestedObj, path, val, idx + 1);
  }

  return set(obj, key, newValue);
} // -- #### update()
// -- Returns a new object with a modified attribute,
// -- calculated via a user-provided callback based on the current value.
// -- If the calculated value is the same as (*referentially equal to*)
// -- the previous value, the original object is returned.
// --
// -- Usage: `update(obj, key, fnUpdate)`
// --
// -- ```js
// -- obj = { a: 1, b: 2, c: 3 }
// -- obj2 = update(obj, 'b', (val) => val + 1)
// -- // { a: 1, b: 3, c: 3 }
// -- obj2 === obj
// -- // false
// --
// -- // The same object is returned if there are no changes:
// -- update(obj, 'b', (val) => val) === obj
// -- // true
// -- ```


function update(obj, key, fnUpdate) {
  const prevVal = obj == null ? undefined : obj[key];
  const nextVal = fnUpdate(prevVal);
  return set(obj, key, nextVal);
} // -- #### updateIn()
// -- Returns a new object with a modified **nested** attribute,
// -- calculated via a user-provided callback based on the current value.
// -- If the calculated value is the same as (*referentially equal to*)
// -- the previous value, the original object is returned.
// --
// -- Usage: `updateIn<T: ArrayOrObject>(obj: T, path: Array<Key>,
// -- fnUpdate: (prevValue: any) => any): T`
// --
// -- ```js
// -- obj = { a: 1, d: { d1: 3, d2: 4 } }
// -- obj2 = updateIn(obj, ['d', 'd1'], (val) => val + 1)
// -- // { a: 1, d: { d1: 4, d2: 4 } }
// -- obj2 === obj
// -- // false
// --
// -- // The same object is returned if there are no changes:
// -- obj3 = updateIn(obj, ['d', 'd1'], (val) => val)
// -- // { a: 1, d: { d1: 3, d2: 4 } }
// -- obj3 === obj
// -- // true
// -- ```


function updateIn(obj, path, fnUpdate) {
  const prevVal = getIn(obj, path);
  const nextVal = fnUpdate(prevVal);
  return setIn(obj, path, nextVal);
} // -- #### merge()
// -- Returns a new object built as follows: the overlapping keys from the
// -- second one overwrite the corresponding entries from the first one.
// -- Similar to `Object.assign()`, but immutable.
// --
// -- Usage:
// --
// -- * `merge(obj1, obj2)`
// -- * `merge(obj1, ...objects)`
// --
// -- The unmodified `obj1` is returned if `obj2` does not *provide something
// -- new to* `obj1`, i.e. if either of the following
// -- conditions are true:
// --
// -- * `obj2` is `null` or `undefined`
// -- * `obj2` is an object, but it is empty
// -- * All attributes of `obj2` are `undefined`
// -- * All attributes of `obj2` are referentially equal to the
// --   corresponding attributes of `obj1`
// --
// -- Note that `undefined` attributes in `obj2` do not modify the
// -- corresponding attributes in `obj1`.
// --
// -- ```js
// -- obj1 = { a: 1, b: 2, c: 3 }
// -- obj2 = { c: 4, d: 5 }
// -- obj3 = merge(obj1, obj2)
// -- // { a: 1, b: 2, c: 4, d: 5 }
// -- obj3 === obj1
// -- // false
// --
// -- // The same object is returned if there are no changes:
// -- merge(obj1, { c: 3 }) === obj1
// -- // true
// -- ```
// Signatures:
// - 1 arg


// Implementation
function merge(a, b, c, d, e, f, ...rest) {
  return rest.length ? doMerge.call(null, false, false, a, b, c, d, e, f, ...rest) : doMerge(false, false, a, b, c, d, e, f);
} // -- #### mergeDeep()
// -- Returns a new object built as follows: the overlapping keys from the
// -- second one overwrite the corresponding entries from the first one.
// -- If both the first and second entries are objects they are merged recursively.
// -- Similar to `Object.assign()`, but immutable, and deeply merging.
// --
// -- Usage:
// --
// -- * `mergeDeep(obj1, obj2)`
// -- * `mergeDeep(obj1, ...objects)`
// --
// -- The unmodified `obj1` is returned if `obj2` does not *provide something
// -- new to* `obj1`, i.e. if either of the following
// -- conditions are true:
// --
// -- * `obj2` is `null` or `undefined`
// -- * `obj2` is an object, but it is empty
// -- * All attributes of `obj2` are `undefined`
// -- * All attributes of `obj2` are referentially equal to the
// --   corresponding attributes of `obj1`
// --
// -- Note that `undefined` attributes in `obj2` do not modify the
// -- corresponding attributes in `obj1`.
// --
// -- ```js
// -- obj1 = { a: 1, b: 2, c: { a: 1 } }
// -- obj2 = { b: 3, c: { b: 2 } }
// -- obj3 = mergeDeep(obj1, obj2)
// -- // { a: 1, b: 3, c: { a: 1, b: 2 }  }
// -- obj3 === obj1
// -- // false
// --
// -- // The same object is returned if there are no changes:
// -- mergeDeep(obj1, { c: { a: 1 } }) === obj1
// -- // true
// -- ```


function mergeDeep(a, b, c, d, e, f, ...rest) {
  return rest.length ? doMerge.call(null, false, true, a, b, c, d, e, f, ...rest) : doMerge(false, true, a, b, c, d, e, f);
} // -- #### mergeIn()
// -- Similar to `merge()`, but merging the value at a given nested path.
// --
// -- Usage examples:
// --
// -- * `mergeIn(obj1, path, obj2)`
// -- * `mergeIn(obj1, path, ...objects)`
// --
// -- ```js
// -- obj1 = { a: 1, d: { b: { d1: 3, d2: 4 } } }
// -- obj2 = { d3: 5 }
// -- obj3 = mergeIn(obj1, ['d', 'b'], obj2)
// -- // { a: 1, d: { b: { d1: 3, d2: 4, d3: 5 } } }
// -- obj3 === obj1
// -- // false
// --
// -- // The same object is returned if there are no changes:
// -- mergeIn(obj1, ['d', 'b'], { d2: 4 }) === obj1
// -- // true
// -- ```


function mergeIn(a, path, b, c, d, e, f, ...rest) {
  let prevVal = getIn(a, path);
  if (prevVal == null) prevVal = {};
  let nextVal;

  if (rest.length) {
    nextVal = doMerge.call(null, false, false, prevVal, b, c, d, e, f, ...rest);
  } else {
    nextVal = doMerge(false, false, prevVal, b, c, d, e, f);
  }

  return setIn(a, path, nextVal);
} // -- #### omit()
// -- Returns an object excluding one or several attributes.
// --
// -- Usage: `omit(obj, attrs)`
//
// -- ```js
// -- obj = { a: 1, b: 2, c: 3, d: 4 }
// -- omit(obj, 'a')
// -- // { b: 2, c: 3, d: 4 }
// -- omit(obj, ['b', 'c'])
// -- // { a: 1, d: 4 }
// --
// -- // The same object is returned if there are no changes:
// -- omit(obj, 'z') === obj1
// -- // true
// -- ```


function omit(obj, attrs) {
  const omitList = Array.isArray(attrs) ? attrs : [attrs];
  let fDoSomething = false;

  for (let i = 0; i < omitList.length; i++) {
    if (hasOwnProperty.call(obj, omitList[i])) {
      fDoSomething = true;
      break;
    }
  }

  if (!fDoSomething) return obj;
  const out = {};
  const keys = getKeysAndSymbols(obj);

  for (let i = 0; i < keys.length; i++) {
    const key = keys[i];
    if (omitList.indexOf(key) >= 0) continue;
    out[key] = obj[key];
  }

  return out;
} // -- #### addDefaults()
// -- Returns a new object built as follows: `undefined` keys in the first one
// -- are filled in with the corresponding values from the second one
// -- (even if they are `null`).
// --
// -- Usage:
// --
// -- * `addDefaults(obj, defaults)`
// -- * `addDefaults(obj, ...defaultObjects)`
// --
// -- ```js
// -- obj1 = { a: 1, b: 2, c: 3 }
// -- obj2 = { c: 4, d: 5, e: null }
// -- obj3 = addDefaults(obj1, obj2)
// -- // { a: 1, b: 2, c: 3, d: 5, e: null }
// -- obj3 === obj1
// -- // false
// --
// -- // The same object is returned if there are no changes:
// -- addDefaults(obj1, { c: 4 }) === obj1
// -- // true
// -- ```
// Signatures:
// - 2 args


// Implementation and catch-all
function addDefaults(a, b, c, d, e, f, ...rest) {
  return rest.length ? doMerge.call(null, true, false, a, b, c, d, e, f, ...rest) : doMerge(true, false, a, b, c, d, e, f);
}

function doMerge(fAddDefaults, fDeep, first, ...rest) {
  let out = first;

  if (!(out != null)) {
    throwStr(IS_DEV ? 'At least one object should be provided to merge()' : INVALID_ARGS);
  }

  let fChanged = false;

  for (let idx = 0; idx < rest.length; idx++) {
    const obj = rest[idx];
    if (obj == null) continue;
    const keys = getKeysAndSymbols(obj);
    if (!keys.length) continue;

    for (let j = 0; j <= keys.length; j++) {
      const key = keys[j];
      if (fAddDefaults && out[key] !== undefined) continue;
      let nextVal = obj[key];

      if (fDeep && isObject(out[key]) && isObject(nextVal)) {
        nextVal = doMerge(fAddDefaults, fDeep, out[key], nextVal);
      }

      if (nextVal === undefined || nextVal === out[key]) continue;

      if (!fChanged) {
        fChanged = true;
        out = clone(out);
      }

      out[key] = nextVal;
    }
  }

  return out;
} // ===============================================
// ### Public API
// ===============================================


const timm = {
  clone,
  addLast,
  addFirst,
  removeLast,
  removeFirst,
  insert,
  removeAt,
  replaceAt,
  getIn,
  set,
  setIn,
  update,
  updateIn,
  merge,
  mergeDeep,
  mergeIn,
  omit,
  addDefaults
};
var _default = timm;
exports.default = _default;
}, function(modId) {var map = {}; return __REQUIRE__(map[modId], modId); })
return __REQUIRE__(1750949389124);
})()
//miniprogram-npm-outsideDeps=[]
//# sourceMappingURL=index.js.map