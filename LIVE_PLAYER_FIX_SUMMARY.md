# Live-Player 权限错误修复总结

## 问题描述
用户在真机测试时遇到以下错误：
- `[Component] <live-player>: 渲染失败，错误原因: fail:access denied`
- `直播播放访问被拒绝: insertXWebLivePlayer:fail:access denied`

## 根本原因分析
根据用户提供的截图和错误信息，问题主要出现在：
1. **域名配置问题**：小程序后台所有域名都显示"未设置"
2. **live-player 组件初始化问题**：videoUrl 可能为空或格式不正确
3. **权限检查时机问题**：在组件完全初始化前就尝试播放

## 修复内容

### 1. app.json 权限配置（已移除错误插件）
```json
{
  "permission": {
    "scope.userLocation": {
      "desc": "您的位置信息将用于设备连接"
    },
    "scope.camera": {
      "desc": "需要使用摄像头权限进行视频播放"
    },
    "scope.record": {
      "desc": "需要录音权限进行视频录制"
    }
  }
}
```

### 2. live-player 组件优化
- 设置 `autoplay="false"` 避免自动播放权限问题
- 添加 `background-mute="true"` 和 `play-strategy="0"` 提高兼容性
- 优化缓存设置和音频处理

### 3. 新增详细的初始化检查
- `_checkLivePlayerStatus()`: 检查组件状态和URL有效性
- 验证 videoUrl 不为空且格式正确
- 确认 live-player 组件在DOM中存在
- 增加组件初始化等待时间（800ms）

### 4. 权限检查机制
- 页面加载时检查基础权限
- 启动 live-player 前检查摄像头权限
- 检查微信网络访问设置
- 域名配置检查

### 5. 错误处理优化
- 详细的权限问题诊断
- 引导用户到设置页面
- 提供具体的解决方案
- 增加调试日志输出

### 6. 基础库版本升级
- 从 2.14.1 升级到 2.19.4

## 主要新增功能

### 权限检查函数
- `_checkBasicPermissions()`: 基础权限检查
- `_checkLivePlayerPermission()`: live-player 权限检查
- `_checkWeChatNetworkSettings()`: 微信网络设置检查
- `_checkDomainConfiguration()`: 域名配置检查
- `_checkLivePlayerStatus()`: live-player 组件状态检查

### 错误处理改进
- 更详细的错误提示
- 权限问题分类处理
- 用户友好的解决方案指导
- 增强的调试信息

## 关键修复点

### 1. 解决了模拟器启动失败问题
- 移除了错误的插件配置 `live-player-plugin`
- live-player 是微信小程序原生组件，不需要插件

### 2. 增强了 live-player 初始化流程
```javascript
// 新增的检查流程
1. 验证 videoUrl 不为空
2. 检查 URL 格式正确性
3. 确认组件在DOM中存在
4. 创建播放器上下文
5. 配置播放参数
6. 延迟启动播放（800ms）
```

### 3. 详细的错误诊断
- 在错误发生时输出当前 videoUrl 和 streamType
- 提供针对性的解决方案
- 区分权限问题和域名配置问题

## 测试建议
1. **重新编译小程序**（重要：移除了错误插件配置）
2. **在真机上测试"开始接收视频"功能**
3. **检查控制台日志**：查看详细的初始化过程
4. **验证设备IP连接**：确保设备在同一局域网
5. **检查微信设置中的小程序权限**

## 域名配置解决方案

### 开发阶段（临时解决）
在微信开发者工具中：
1. 点击右上角"详情"
2. 勾选"不校验合法域名、web-view（业务域名）、TLS 版本以及 HTTPS 证书"

### 正式发布（必须配置）
在小程序后台添加：
- **request 合法域名**：`http://192.168.x.x`（您的设备IP）
- **socket 合法域名**：`ws://192.168.x.x`（如果使用WebSocket）

## 预期效果
修复后应该能够：
1. ✅ 正常启动小程序（无模拟器错误）
2. ✅ 成功连接设备并获取视频流
3. ✅ live-player 组件正常播放
4. ✅ 提供详细的错误诊断信息
5. ✅ 引导用户解决权限和配置问题
