{"version": 3, "sources": ["phin.js"], "names": [], "mappings": ";;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.js", "sourcesContent": ["const {URL} = require('url')\n\nconst centra = require('centra')\n\nconst unspecifiedFollowRedirectsDefault = 20\n\n/**\n* phin options object. phin also supports all options from <a href=\"https://nodejs.org/api/http.html#http_http_request_options_callback\">http.request(options, callback)</a> by passing them on to this method (or similar).\n* @typedef {Object} phinOptions\n* @property {string} url - URL to request (autodetect infers from this URL)\n* @property {string} [method=GET] - Request method ('GET', 'POST', etc.)\n* @property {string|Buffer|object} [data] - Data to send as request body (phin may attempt to convert this data to a string if it isn't already)\n* @property {Object} [form] - Object to send as form data (sets 'Content-Type' and 'Content-Length' headers, as well as request body) (overwrites 'data' option if present)\n* @property {Object} [headers={}] - Request headers\n* @property {Object} [core={}] - Custom core HTTP options\n* @property {string} [parse=none] - Response parsing. Errors will be given if the response can't be parsed. 'none' returns body as a `Buffer`, 'json' attempts to parse the body as JSO<PERSON>, and 'string' attempts to parse the body as a string\n* @property {boolean} [followRedirects=false] - Enable HTTP redirect following\n* @property {boolean} [stream=false] - Enable streaming of response. (Removes body property)\n* @property {boolean} [compression=false] - Enable compression for request\n* @property {?number} [timeout=null] - Request timeout in milliseconds\n* @property {string} [hostname=autodetect] - URL hostname\n* @property {Number} [port=autodetect] - URL port\n* @property {string} [path=autodetect] - URL path\n*/\n\n/**\n* Response data\n* @callback phinResponseCallback\n* @param {?(Error|string)} error - Error if any occurred in request, otherwise null.\n* @param {?http.serverResponse} phinResponse - phin response object. Like <a href='https://nodejs.org/api/http.html#http_class_http_serverresponse'>http.ServerResponse</a> but has a body property containing response body, unless stream. If stream option is enabled, a stream property will be provided to callback with a readable stream.\n*/\n\n/**\n* Sends an HTTP request\n* @param {phinOptions|string} options - phin options object (or string for auto-detection)\n* @returns {Promise<http.serverResponse>} - phin-adapted response object\n*/\nconst phin = async (opts) => {\n\tif (typeof(opts) !== 'string') {\n\t\tif (!opts.hasOwnProperty('url')) {\n\t\t\tthrow new Error('Missing url option from options for request method.')\n\t\t}\n\t}\n\n\tconst req = centra(typeof opts === 'object' ? opts.url : opts, opts.method || 'GET')\n\n\tif (opts.headers) req.header(opts.headers)\n\tif (opts.stream) req.stream()\n\tif (opts.timeout) req.timeout(opts.timeout)\n\tif (opts.data) req.body(opts.data)\n\tif (opts.form) req.body(opts.form, 'form')\n\tif (opts.compression) req.compress()\n\n\tif (opts.followRedirects) {\n\t\tif (opts.followRedirects === true) {\n\t\t\treq.followRedirects(unspecifiedFollowRedirectsDefault)\n\t\t} else if (typeof opts.followRedirects === 'number') {\n\t\t\treq.followRedirects(opts.followRedirects)\n\t\t}\n\t}\n\n\tif (typeof opts.core === 'object') {\n\t\tObject.keys(opts.core).forEach((optName) => {\n\t\t\treq.option(optName, opts.core[optName])\n\t\t})\n\t}\n\n\tconst res = await req.send()\n\n\tif (opts.stream) {\n\t\tres.stream = res\n\n\t\treturn res\n\t}\n\telse {\n\t\tres.coreRes.body = res.body\n\n\t\tif (opts.parse) {\n\t\t\tif (opts.parse === 'json') {\n\t\t\t\tres.coreRes.body = await res.json()\n\t\n\t\t\t\treturn res.coreRes\n\t\t\t}\n\t\t\telse if (opts.parse === 'string') {\n\t\t\t\tres.coreRes.body = res.coreRes.body.toString()\n\n\t\t\t\treturn res.coreRes\n\t\t\t}\n\t\t}\n\t\t\n\t\treturn res.coreRes\n\t}\n}\n\n// If we're running Node.js 8+, let's promisify it\n\nphin.promisified = phin\n\nphin.unpromisified = (opts, cb) => {\n\tphin(opts).then((data) => {\n\t\tif (cb) cb(null, data)\n\t}).catch((err) => {\n\t\tif (cb) cb(err, null)\n\t})\n}\n\n// Defaults\n\nphin.defaults = (defaultOpts) => async (opts) => {\n\tconst nops = typeof opts === 'string' ? {'url': opts} : opts\n\n\tObject.keys(defaultOpts).forEach((doK) => {\n\t\tif (!nops.hasOwnProperty(doK) || nops[doK] === null) {\n\t\t\tnops[doK] = defaultOpts[doK]\n\t\t}\n\t})\n\n\treturn await phin(nops)\n}\n\nmodule.exports = phin\n"]}