{"version": 3, "sources": ["index.js"], "names": [], "mappings": ";;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.js", "sourcesContent": ["/* eslint-disable node/no-deprecated-api */\n\nvar toString = Object.prototype.toString\n\nvar isModern = (\n  typeof Buffer !== 'undefined' &&\n  typeof Buffer.alloc === 'function' &&\n  typeof Buffer.allocUnsafe === 'function' &&\n  typeof Buffer.from === 'function'\n)\n\nfunction isArrayBuffer (input) {\n  return toString.call(input).slice(8, -1) === 'ArrayBuffer'\n}\n\nfunction fromArrayBuffer (obj, byteOffset, length) {\n  byteOffset >>>= 0\n\n  var maxLength = obj.byteLength - byteOffset\n\n  if (maxLength < 0) {\n    throw new RangeError(\"'offset' is out of bounds\")\n  }\n\n  if (length === undefined) {\n    length = maxLength\n  } else {\n    length >>>= 0\n\n    if (length > maxLength) {\n      throw new RangeError(\"'length' is out of bounds\")\n    }\n  }\n\n  return isModern\n    ? Buffer.from(obj.slice(byteOffset, byteOffset + length))\n    : new Buffer(new Uint8Array(obj.slice(byteOffset, byteOffset + length)))\n}\n\nfunction fromString (string, encoding) {\n  if (typeof encoding !== 'string' || encoding === '') {\n    encoding = 'utf8'\n  }\n\n  if (!Buffer.isEncoding(encoding)) {\n    throw new TypeError('\"encoding\" must be a valid string encoding')\n  }\n\n  return isModern\n    ? Buffer.from(string, encoding)\n    : new Buffer(string, encoding)\n}\n\nfunction bufferFrom (value, encodingOrOffset, length) {\n  if (typeof value === 'number') {\n    throw new TypeError('\"value\" argument must not be a number')\n  }\n\n  if (isArrayBuffer(value)) {\n    return fromArrayBuffer(value, encodingOrOffset, length)\n  }\n\n  if (typeof value === 'string') {\n    return fromString(value, encodingOrOffset)\n  }\n\n  return isModern\n    ? Buffer.from(value)\n    : new Buffer(value)\n}\n\nmodule.exports = bufferFrom\n"]}