{"version": 3, "sources": ["index.js", "lenient.js"], "names": [], "mappings": ";;;;;;;AAAA;AACA;AACA;AACA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.js", "sourcesContent": ["\nconst lenient = require('./lenient');\n\nconst yn = (input, options) => {\n\tinput = String(input).trim();\n\n\toptions = Object.assign({\n\t\tlenient: false,\n\t\tdefault: null\n\t}, options);\n\n\tif (options.default !== null && typeof options.default !== 'boolean') {\n\t\tthrow new TypeError(`Expected the \\`default\\` option to be of type \\`boolean\\`, got \\`${typeof options.default}\\``);\n\t}\n\n\tif (/^(?:y|yes|true|1)$/i.test(input)) {\n\t\treturn true;\n\t}\n\n\tif (/^(?:n|no|false|0)$/i.test(input)) {\n\t\treturn false;\n\t}\n\n\tif (options.lenient === true) {\n\t\treturn lenient(input, options);\n\t}\n\n\treturn options.default;\n};\n\nmodule.exports = yn;\n// TODO: Remove this for the next major release\nmodule.exports.default = yn;\n", "\n\nconst YES_MATCH_SCORE_THRESHOLD = 2;\nconst NO_MATCH_SCORE_THRESHOLD = 1.25;\n\nconst yMatch = new Map([\n\t[5, 0.25],\n\t[6, 0.25],\n\t[7, 0.25],\n\t['t', 0.75],\n\t['y', 1],\n\t['u', 0.75],\n\t['g', 0.25],\n\t['h', 0.25],\n\t['j', 0.25]\n]);\n\nconst eMatch = new Map([\n\t[2, 0.25],\n\t[3, 0.25],\n\t[4, 0.25],\n\t['w', 0.75],\n\t['e', 1],\n\t['r', 0.75],\n\t['s', 0.25],\n\t['d', 0.25],\n\t['f', 0.25]\n]);\n\nconst sMatch = new Map([\n\t['q', 0.25],\n\t['w', 0.25],\n\t['e', 0.25],\n\t['a', 0.75],\n\t['s', 1],\n\t['d', 0.75],\n\t['z', 0.25],\n\t['x', 0.25],\n\t['c', 0.25]\n]);\n\nconst nMatch = new Map([\n\t['h', 0.25],\n\t['j', 0.25],\n\t['k', 0.25],\n\t['b', 0.75],\n\t['n', 1],\n\t['m', 0.75]\n]);\n\nconst oMatch = new Map([\n\t[9, 0.25],\n\t[0, 0.25],\n\t['i', 0.75],\n\t['o', 1],\n\t['p', 0.75],\n\t['k', 0.25],\n\t['l', 0.25]\n]);\n\nfunction getYesMatchScore(value) {\n\tconst [y, e, s] = value;\n\tlet score = 0;\n\n\tif (yMatch.has(y)) {\n\t\tscore += yMatch.get(y);\n\t}\n\n\tif (eMatch.has(e)) {\n\t\tscore += eMatch.get(e);\n\t}\n\n\tif (sMatch.has(s)) {\n\t\tscore += sMatch.get(s);\n\t}\n\n\treturn score;\n}\n\nfunction getNoMatchScore(value) {\n\tconst [n, o] = value;\n\tlet score = 0;\n\n\tif (nMatch.has(n)) {\n\t\tscore += nMatch.get(n);\n\t}\n\n\tif (oMatch.has(o)) {\n\t\tscore += oMatch.get(o);\n\t}\n\n\treturn score;\n}\n\nmodule.exports = (input, options) => {\n\tif (getYesMatchScore(input) >= YES_MATCH_SCORE_THRESHOLD) {\n\t\treturn true;\n\t}\n\n\tif (getNoMatchScore(input) >= NO_MATCH_SCORE_THRESHOLD) {\n\t\treturn false;\n\t}\n\n\treturn options.default;\n};\n"]}