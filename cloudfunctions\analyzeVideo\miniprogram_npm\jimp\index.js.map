{"version": 3, "sources": ["index.js"], "names": [], "mappings": ";;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.js", "sourcesContent": ["\n\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\");\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports[\"default\"] = void 0;\n\nvar _custom = _interopRequireDefault(require(\"@jimp/custom\"));\n\nvar _types = _interopRequireDefault(require(\"@jimp/types\"));\n\nvar _plugins = _interopRequireDefault(require(\"@jimp/plugins\"));\n\nvar _default = (0, _custom[\"default\"])({\n  types: [_types[\"default\"]],\n  plugins: [_plugins[\"default\"]]\n});\n\nexports[\"default\"] = _default;\nmodule.exports = exports.default;\n//# sourceMappingURL=index.js.map"]}