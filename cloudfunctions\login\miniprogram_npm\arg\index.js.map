{"version": 3, "sources": ["index.js"], "names": [], "mappings": ";;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.js", "sourcesContent": ["const flagSymbol = Symbol('arg flag');\n\nfunction arg(opts, {argv = process.argv.slice(2), permissive = false, stopAtPositional = false} = {}) {\n\tif (!opts) {\n\t\tthrow new Error('Argument specification object is required');\n\t}\n\n\tconst result = {_: []};\n\n\tconst aliases = {};\n\tconst handlers = {};\n\n\tfor (const key of Object.keys(opts)) {\n\t\tif (!key) {\n\t\t\tthrow new TypeError('Argument key cannot be an empty string');\n\t\t}\n\n\t\tif (key[0] !== '-') {\n\t\t\tthrow new TypeError(`Argument key must start with '-' but found: '${key}'`);\n\t\t}\n\n\t\tif (key.length === 1) {\n\t\t\tthrow new TypeError(`Argument key must have a name; singular '-' keys are not allowed: ${key}`);\n\t\t}\n\n\t\tif (typeof opts[key] === 'string') {\n\t\t\taliases[key] = opts[key];\n\t\t\tcontinue;\n\t\t}\n\n\t\tlet type = opts[key];\n\t\tlet isFlag = false;\n\n\t\tif (Array.isArray(type) && type.length === 1 && typeof type[0] === 'function') {\n\t\t\tconst [fn] = type;\n\t\t\ttype = (value, name, prev = []) => {\n\t\t\t\tprev.push(fn(value, name, prev[prev.length - 1]));\n\t\t\t\treturn prev;\n\t\t\t};\n\t\t\tisFlag = fn === Boolean || fn[flagSymbol] === true;\n\t\t} else if (typeof type === 'function') {\n\t\t\tisFlag = type === Boolean || type[flagSymbol] === true;\n\t\t} else {\n\t\t\tthrow new TypeError(`Type missing or not a function or valid array type: ${key}`);\n\t\t}\n\n\t\tif (key[1] !== '-' && key.length > 2) {\n\t\t\tthrow new TypeError(`Short argument keys (with a single hyphen) must have only one character: ${key}`);\n\t\t}\n\n\t\thandlers[key] = [type, isFlag];\n\t}\n\n\tfor (let i = 0, len = argv.length; i < len; i++) {\n\t\tconst wholeArg = argv[i];\n\n\t\tif (stopAtPositional && result._.length > 0) {\n\t\t\tresult._ = result._.concat(argv.slice(i));\n\t\t\tbreak;\n\t\t}\n\n\t\tif (wholeArg === '--') {\n\t\t\tresult._ = result._.concat(argv.slice(i + 1));\n\t\t\tbreak;\n\t\t}\n\n\t\tif (wholeArg.length > 1 && wholeArg[0] === '-') {\n\t\t\t/* eslint-disable operator-linebreak */\n\t\t\tconst separatedArguments = (wholeArg[1] === '-' || wholeArg.length === 2)\n\t\t\t\t? [wholeArg]\n\t\t\t\t: wholeArg.slice(1).split('').map(a => `-${a}`);\n\t\t\t/* eslint-enable operator-linebreak */\n\n\t\t\tfor (let j = 0; j < separatedArguments.length; j++) {\n\t\t\t\tconst arg = separatedArguments[j];\n\t\t\t\tconst [originalArgName, argStr] = arg[1] === '-' ? arg.split(/=(.*)/, 2) : [arg, undefined];\n\n\t\t\t\tlet argName = originalArgName;\n\t\t\t\twhile (argName in aliases) {\n\t\t\t\t\targName = aliases[argName];\n\t\t\t\t}\n\n\t\t\t\tif (!(argName in handlers)) {\n\t\t\t\t\tif (permissive) {\n\t\t\t\t\t\tresult._.push(arg);\n\t\t\t\t\t\tcontinue;\n\t\t\t\t\t} else {\n\t\t\t\t\t\tconst err = new Error(`Unknown or unexpected option: ${originalArgName}`);\n\t\t\t\t\t\terr.code = 'ARG_UNKNOWN_OPTION';\n\t\t\t\t\t\tthrow err;\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\tconst [type, isFlag] = handlers[argName];\n\n\t\t\t\tif (!isFlag && ((j + 1) < separatedArguments.length)) {\n\t\t\t\t\tthrow new TypeError(`Option requires argument (but was followed by another short argument): ${originalArgName}`);\n\t\t\t\t}\n\n\t\t\t\tif (isFlag) {\n\t\t\t\t\tresult[argName] = type(true, argName, result[argName]);\n\t\t\t\t} else if (argStr === undefined) {\n\t\t\t\t\tif (\n\t\t\t\t\t\targv.length < i + 2 ||\n\t\t\t\t\t\t(\n\t\t\t\t\t\t\targv[i + 1].length > 1 &&\n\t\t\t\t\t\t\t(argv[i + 1][0] === '-') &&\n\t\t\t\t\t\t\t!(\n\t\t\t\t\t\t\t\targv[i + 1].match(/^-?\\d*(\\.(?=\\d))?\\d*$/) &&\n\t\t\t\t\t\t\t\t(\n\t\t\t\t\t\t\t\t\ttype === Number ||\n\t\t\t\t\t\t\t\t\t// eslint-disable-next-line no-undef\n\t\t\t\t\t\t\t\t\t(typeof BigInt !== 'undefined' && type === BigInt)\n\t\t\t\t\t\t\t\t)\n\t\t\t\t\t\t\t)\n\t\t\t\t\t\t)\n\t\t\t\t\t) {\n\t\t\t\t\t\tconst extended = originalArgName === argName ? '' : ` (alias for ${argName})`;\n\t\t\t\t\t\tthrow new Error(`Option requires argument: ${originalArgName}${extended}`);\n\t\t\t\t\t}\n\n\t\t\t\t\tresult[argName] = type(argv[i + 1], argName, result[argName]);\n\t\t\t\t\t++i;\n\t\t\t\t} else {\n\t\t\t\t\tresult[argName] = type(argStr, argName, result[argName]);\n\t\t\t\t}\n\t\t\t}\n\t\t} else {\n\t\t\tresult._.push(wholeArg);\n\t\t}\n\t}\n\n\treturn result;\n}\n\narg.flag = fn => {\n\tfn[flagSymbol] = true;\n\treturn fn;\n};\n\n// Utility types\narg.COUNT = arg.flag((v, name, existingCount) => (existingCount || 0) + 1);\n\nmodule.exports = arg;\n"]}