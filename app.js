const config = require('./utils/config');
const request = require('./utils/request');

App({
  onLaunch: function () {
    // 初始化日志
    this.initLogs();
    // 检查更新
    this.checkUpdate();
    // 初始化用户信息
    this.initUserInfo();
    // 初始化云开发环境
    if (!wx.cloud) {
      console.error('请使用 2.2.3 或以上的基础库以使用云能力')
    } else {
      wx.cloud.init({
        env: wx.cloud.DYNAMIC_CURRENT_ENV,  // 使用正确的动态环境ID
        traceUser: true
      })
      // 保存环境ID，确保在全局可用
      try {
        // 尝试获取实际的环境ID
        const envId = wx.cloud.DYNAMIC_CURRENT_ENV || 'wlksapp_4g54hauu5cbf43dc';
        this.globalData.cloudEnvId = envId;
        // 构建完整的环境ID（包含存储空间标识）
        this.globalData.cloudFullEnvId = `${envId}.776c_${envId}_1329876191`;
        console.log('云环境初始化完成，环境ID:', envId);
        console.log('完整云环境ID:', this.globalData.cloudFullEnvId);
      } catch (error) {
        console.error('获取云环境ID失败:', error);
      }
    }
  },

  globalData: {
    userInfo: null,
    config: config,
    request: request,
    cloudEnvId: 'wlksapp_4g54hauu5cbf43dc',  // 添加云环境ID
    cloudFullEnvId: 'wlksapp_4g54hauu5cbf43dc.776c_wlksapp_4g54hauu5cbf43dc_1329876191' // 完整的云环境ID，包含存储空间标识
  },

  // 初始化日志
  initLogs() {
    const logs = wx.getStorageSync(config.storageKeys.logs) || [];
    logs.unshift(Date.now());
    wx.setStorageSync(config.storageKeys.logs, logs);
  },

  // 检查小程序更新
  checkUpdate() {
    if (wx.canIUse('getUpdateManager')) {
      const updateManager = wx.getUpdateManager();
      updateManager.onCheckForUpdate(function (res) {
        if (res.hasUpdate) {
          updateManager.onUpdateReady(function () {
            wx.showModal({
              title: '更新提示',
              content: '新版本已经准备好，是否重启应用？',
              success: function (res) {
                if (res.confirm) {
                  updateManager.applyUpdate();
                }
              }
            });
          });
        }
      });
    }
  },

  // 初始化用户信息
  async initUserInfo() {
    try {
      const userInfo = wx.getStorageSync(config.storageKeys.userInfo);
      if (userInfo) {
        this.globalData.userInfo = userInfo;
      }
    } catch (error) {
      console.error('初始化用户信息失败：', error);
    }
  },

  // 获取用户信息
  getUserProfile() {
    return new Promise((resolve, reject) => {
      wx.getUserProfile({
        desc: '用于完善用户资料',
        success: (res) => {
          this.globalData.userInfo = res.userInfo;
          wx.setStorageSync(config.storageKeys.userInfo, res.userInfo);
          resolve(res.userInfo);
        },
        fail: (err) => {
          console.error('获取用户信息失败：', err);
          reject(err);
        }
      });
    });
  },

  // 检查登录状态
  checkLoginStatus() {
    return new Promise((resolve) => {
      wx.checkSession({
        success: () => {
          const token = wx.getStorageSync(config.storageKeys.token);
          resolve(!!token && !!this.globalData.userInfo);
        },
        fail: () => {
          resolve(false);
        }
      });
    });
  },

  // 登出
  logout() {
    this.globalData.userInfo = null;
    wx.removeStorageSync(config.storageKeys.userInfo);
    wx.removeStorageSync(config.storageKeys.token);
  }
});