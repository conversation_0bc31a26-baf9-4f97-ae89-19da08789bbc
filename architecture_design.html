<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>微信小程序视频分析系统架构设计</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 0;
            padding: 10px;
            background: #f0f0f0;
            min-height: 100vh;
        }

        .container {
            width: 1400px;
            height: 800px;
            margin: 0 auto;
            background: white;
            border: 3px solid #4a90e2;
            border-radius: 25px;
            padding: 20px;
            box-sizing: border-box;
            position: relative;
        }

        .main-title {
            position: absolute;
            top: 30px;
            left: 50%;
            transform: translateX(-50%);
            background: white;
            border: 2px solid #333;
            padding: 8px 20px;
            font-size: 18px;
            font-weight: bold;
            color: #333;
        }

        .main-content {
            margin-top: 80px;
            height: calc(100% - 120px);
            display: flex;
            flex-direction: column;
        }

        .top-section {
            display: flex;
            justify-content: center;
            margin-bottom: 30px;
        }

        .system-group {
            border: 2px solid #333;
            border-radius: 8px;
            padding: 15px;
            background: #f9f9f9;
            margin: 0 10px;
        }

        .group-title {
            text-align: center;
            font-size: 16px;
            font-weight: bold;
            color: #333;
            margin-bottom: 15px;
            padding: 8px;
            background: #e8e8e8;
            border: 1px solid #ccc;
        }

        .modules-grid {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
            justify-content: center;
        }

        .module-box {
            border: 1px solid #333;
            background: white;
            padding: 12px 8px;
            text-align: center;
            font-size: 12px;
            font-weight: bold;
            color: #333;
            min-width: 60px;
            max-width: 80px;
            line-height: 1.2;
        }

        .connection-arrow {
            text-align: center;
            font-size: 20px;
            color: #333;
            margin: 10px 0;
        }

        .bottom-section {
            display: flex;
            justify-content: space-between;
            flex: 1;
        }

        .bottom-group {
            border: 2px solid #333;
            border-radius: 8px;
            padding: 15px;
            background: #f9f9f9;
            flex: 1;
            margin: 0 5px;
        }

        .vertical-modules {
            display: flex;
            flex-direction: column;
            gap: 8px;
            align-items: center;
        }

        .horizontal-modules {
            display: flex;
            gap: 8px;
            justify-content: center;
            flex-wrap: wrap;
        }

        /* 特殊样式 */
        .signal-group {
            width: 280px;
        }

        .data-group {
            width: 200px;
        }

        .storage-group {
            width: 280px;
        }

        .output-group {
            width: 200px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="main-title">微信小程序视频分析系统</div>

        <div class="main-content">
            <!-- 顶部四个主要单元 -->
            <div class="top-section">
                <div class="system-group signal-group">
                    <div class="group-title">用户交互单元 (pages/me)</div>
                    <div class="horizontal-modules">
                        <div class="module-box">ProfileClient<br>用户管理</div>
                        <div class="module-box">微信登录<br>认证</div>
                        <div class="module-box">用户信息<br>更新</div>
                        <div class="module-box">设备绑定<br>管理</div>
                        <div class="module-box">数据导出<br>分享</div>
                        <div class="module-box">本地存储<br>缓存</div>
                    </div>
                </div>

                <div class="system-group data-group">
                    <div class="group-title">主控制单元 (pages/index)</div>
                    <div class="horizontal-modules">
                        <div class="module-box">设备IP<br>检测连接</div>
                        <div class="module-box">视频流<br>格式检测</div>
                        <div class="module-box">实时预览<br>录制</div>
                        <div class="module-box">参数调节<br>控制</div>
                    </div>
                </div>

                <div class="system-group storage-group">
                    <div class="group-title">数据服务单元 (getUserData)</div>
                    <div class="horizontal-modules">
                        <div class="module-box">用户数据<br>查询</div>
                        <div class="module-box">媒体文件<br>管理</div>
                        <div class="module-box">云存储<br>访问</div>
                        <div class="module-box">临时URL<br>生成</div>
                        <div class="module-box">文件下载<br>Base64转换</div>
                    </div>
                </div>

                <div class="system-group output-group">
                    <div class="group-title">智能分析单元 (analyzeVideo)</div>
                    <div class="vertical-modules">
                        <div class="module-box">FFmpeg<br>视频处理</div>
                        <div class="module-box">帧提取<br>灰度分析</div>
                        <div class="module-box">C/T区域<br>检测计算</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
    </style>
</head>
<body>
    <div class="container">
        <h1 class="title">微信小程序视频分析系统架构设计</h1>
        

    </div>
</body>
</html>
