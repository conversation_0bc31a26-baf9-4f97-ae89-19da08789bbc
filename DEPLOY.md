# 部署说明

## 1. 环境要求

- 微信开发者工具
- Node.js 12.0.0+
- npm 6.0.0+

## 2. 部署步骤

### 2.1 创建云开发环境

1. 打开微信开发者工具
2. 导入项目
3. 开通云开发
4. 创建云环境
5. 复制云环境ID

### 2.2 配置环境ID

1. 打开 `config/env.js` 文件
2. 将 `envId` 修改为你的云环境ID：
```javascript
module.exports = {
  envId: '你的云环境ID'  // 替换这里
  // ...
}
```

### 2.3 部署云函数

1. 进入 cloudfunctions 目录

2. 部署基础功能云函数：
   ```bash
   # 视频分析云函数
   cd analyzeVideo
   npm install
   # 在开发者工具中右键点击 analyzeVideo 文件夹，选择"上传并部署：云端安装依赖"

   # 登录云函数
   cd ../login
   npm install
   # 上传并部署
   ```

3. 部署用户相关云函数：
   ```bash
   # 用户身份验证
   cd ../checkUser
   npm install
   # 上传并部署

   # 手机号绑定
   cd ../bindPhone
   npm install
   # 上传并部署

   # 获取手机号
   cd ../getPhoneNumber
   npm install
   # 上传并部署
   ```

4. 部署数据管理云函数：
   ```bash
   # 获取用户数据
   cd ../getUserData
   npm install
   # 上传并部署
   ```

5. 部署视频处理云函数：
   ```bash
   # 更新视频参数
   cd ../updateVideoParams
   npm install
   # 上传并部署

   # 启动视频流
   cd ../startVideoStream
   npm install
   # 上传并部署
   ```

每个云函数的功能说明：

1. analyzeVideo
   - 视频帧分析
   - 计算C区和T区值
   - 生成分析结果

2. login
   - 用户登录验证
   - 创建用户档案
   - 返回登录状态

3. checkUser
   - 验证用户身份
   - 检查登录状态
   - 权限验证

4. bindPhone
   - 绑定用户手机号
   - 更新用户信息
   - 验证手机号有效性

5. getPhoneNumber
   - 获取微信手机号
   - 解密手机号信息
   - 返回手机号数据

6. getUserData
   - 获取用户配置
   - 获取分析历史
   - 获取参数设置

7. updateVideoParams
   - 更新视频参数
   - 保存参数配置
   - 应用参数设置

8. startVideoStream
   - 启动视频流
   - 配置视频参数
   - 初始化视频处理

### 2.3 云函数依赖项

每个云函数都需要安装以下基础依赖：
```bash
npm install wx-server-sdk@latest
```

特定云函数的额外依赖：

1. analyzeVideo 云函数：
```bash
npm install fluent-ffmpeg@^2.1.2
npm install canvas@^2.11.0
npm install ffmpeg-static@^5.1.0
```

2. login 云函数：
- 仅需要基础依赖 wx-server-sdk

3. checkUser 云函数：
- 仅需要基础依赖 wx-server-sdk

4. bindPhone 云函数：
- 仅需要基础依赖 wx-server-sdk

5. getPhoneNumber 云函数：
- 仅需要基础依赖 wx-server-sdk

6. getUserData 云函数：
- 仅需要基础依赖 wx-server-sdk

7. updateVideoParams 云函数：
- 仅需要基础依赖 wx-server-sdk

8. startVideoStream 云函数：
- 仅需要基础依赖 wx-server-sdk

### 2.4 云函数权限配置

1. getPhoneNumber 云函数需要以下权限：
- 获取手机号：需要在微信公众平台配置并开通
- 云存储读写权限

2. bindPhone 云函数需要：
- 云存储读写权限

3. checkUser 云函数需要：
- 云存储读取权限

4. analyzeVideo 云函数需要：
- 云存储读写权限
- 临时文件读写权限

5. 其他云函数需要：
- 云存储读写权限

### 2.4 云存储目录

云函数会自动创建以下目录结构：
```
cloud-storage/
├── analysis_results/  # 分析结果
├── analysis_images/   # 分析图片
├── parameters/        # 参数配置
└── users/            # 用户数据
```

### 2.5 云函数环境配置

1. 环境变量设置
   ```javascript
   cloud.init({
     env: cloud.DYNAMIC_CURRENT_ENV  // 自动获取当前环境ID
   })
   ```

2. 配置文件修改
   - 打开 `app.js`
   - 修改云环境初始化代码：
   ```javascript
   wx.cloud.init({
     env: '你的云环境ID',
     traceUser: true
   })
   ```

3. 环境ID配置检查
   - 确保 `config/env.js` 中的 envId 与云环境ID一致
   - 确保 `app.js` 中的环境ID配置正确
   - 确保所有云函数使用 `DYNAMIC_CURRENT_ENV`

### 2.6 云存储目录结构

1. 分析结果目录
   ```
   analysis_results/
   ├── {userId}/
   │   └── {timestamp}.json    # 分析结果文件
   ```

2. 参数配置目录
   ```
   parameters/
   ├── {userId}/
   │   └── {timestamp}.json    # 参数配置文件
   ```

3. 分析图片目录
   ```
   analysis_images/
   ├── {userId}/
   │   ├── c-area/            # C区图片
   │   └── t-area/            # T区图片
   ```

4. 用户数据目录
   ```
   users/
   ├── {openid}.json          # 用户配置文件
   ```

### 2.7 错误处理和日志

1. 云函数错误处理
   - 所有云函数都应该使用 try-catch 包装
   - 统一的错误返回格式：
   ```javascript
   {
     success: false,
     error: error.message,
     message: '友好的错误提示'
   }
   ```

2. 日志记录
   - 使用 console.error() 记录错误
   - 使用 console.log() 记录关键操作
   - 定期检查云开发日志

3. 常见错误处理：
   - 文件不存在：返回空数据或创建新文件
   - 参数验证失败：返回具体的错误信息
   - 权限不足：提示用户进行授权
   - 网络超时：建议用户重试
   - 存储空间不足：提示用户清理数据

4. 监控和告警
   - 设置云函数超时告警
   - 监控存储空间使用
   - 跟踪接口调用频率
   - 记录用户反馈问题

### 2.8 安全配置

1. 云函数安全
   - 设置适当的超时时间
   - 限制调用频率
   - 添加数据验证
   - 使用最小权限原则

2. 数据安全
   - 用户数据加密存储
   - 定期数据备份
   - 敏感信息脱敏
   - 访问权限控制

3. 小程序安全
   - 开启数据域名校验
   - 使用 HTTPS 请求
   - 开启小程序代码保护
   - 配置 WXBizDataCrypt 加密

4. 权限控制
   ```javascript
   // 示例：检查用户权限
   const checkUserPermission = async (userId) => {
     try {
       const userInfo = await cloud.downloadFile({
         fileID: `users/${userId}.json`
       })
       return userInfo.permissions || []
     } catch (err) {
       return []
     }
   }
   ```

### 2.9 性能优化

1. 云函数优化
   - 使用 Promise.all 并行处理
   - 避免大量同步操作
   - 合理设置超时时间
   - 优化数据库查询

2. 存储优化
   - 压缩图片和视频
   - 使用 CDN 加速
   - 定期清理临时文件
   - 合理设置缓存策略

3. 小程序优化
   - 分包加载
   - 图片懒加载
   - 使用缓存
   - 减少网络请求

4. 代码示例：
   ```javascript
   // 示例：优化图片上传
   const optimizeAndUploadImage = async (imagePath) => {
     try {
       // 压缩图片
       const compressedImage = await compressImage(imagePath)
       
       // 上传到云存储
       const uploadResult = await cloud.uploadFile({
         cloudPath: `optimized/${Date.now()}.jpg`,
         fileContent: compressedImage
       })
       
       // 返回文件 ID
       return uploadResult.fileID
     } catch (err) {
       console.error('图片处理失败:', err)
       throw err
     }
   }
   ```

### 2.10 部署检查清单

1. 环境配置
   - [ ] 云开发环境已创建
   - [ ] 环境 ID 已正确配置
   - [ ] 云函数已部署
   - [ ] 云存储目录已创建

2. 功能测试
   - [ ] 用户登录/注册
   - [ ] 手机号绑定
   - [ ] 视频分析
   - [ ] 参数配置
   - [ ] 数据管理

3. 性能检查
   - [ ] 响应时间正常
   - [ ] 内存使用合理
   - [ ] 存储空间充足
   - [ ] 并发处理正常

4. 安全检查
   - [ ] 权限配置正确
   - [ ] 数据加密有效
   - [ ] 敏感信息保护
   - [ ] 访问控制生效

### 2.5 检查部署

1. 检查云函数是否部署成功
2. 检查云存储是否创建成功
3. 尝试运行小程序，测试基本功能

### 2.11 常见问题

1. 云函数部署失败
   - 检查网络连接
   - 确认云开发权限
   - 检查依赖安装
   - 查看部署日志

2. 云存储访问错误
   - 验证文件路径
   - 检查权限设置
   - 确认存储空间
   - 查看错误日志

3. 手机号绑定问题
   - 检查 API 权限
   - 验证手机号格式
   - 确认用户授权
   - 查看绑定日志

4. 视频分析异常
   - 检查视频格式
   - 验证参数设置
   - 确认分析区域
   - 查看分析日志

### 2.12 故障排除指南

1. 云函数调试
   ```bash
   # 本地调试云函数
   cd cloudfunctions/functionName
   npm run debug
   
   # 查看云函数日志
   wx-dev-cli log functionName
   ```

2. 存储问题排查
   ```javascript
   // 检查文件是否存在
   const checkFile = async (fileID) => {
     try {
       await cloud.downloadFile({ fileID })
       return true
     } catch (err) {
       console.error('文件不存在:', err)
       return false
     }
   }
   ```

3. 权限问题排查
   ```javascript
   // 检查用户权限
   const checkAccess = async (userId, resource) => {
     try {
       const userInfo = await cloud.downloadFile({
         fileID: `users/${userId}.json`
       })
       const permissions = JSON.parse(userInfo.fileContent).permissions
       return permissions.includes(resource)
     } catch (err) {
       console.error('权限检查失败:', err)
       return false
     }
   }
   ```

4. 性能问题排查
   ```javascript
   // 监控函数执行时间
   const monitorFunction = async (func) => {
     const start = Date.now()
     try {
       const result = await func()
       const duration = Date.now() - start
       console.log(`执行时间: ${duration}ms`)
       return result
     } catch (err) {
       console.error('执行失败:', err)
       throw err
     }
   }
   ```

### 2.13 维护建议

1. 定期维护
   - 检查日志记录
   - 清理临时文件
   - 优化存储空间
   - 更新云函数

2. 数据备份
   - 定期备份用户数据
   - 备份配置文件
   - 备份分析结果
   - 存档重要日志

3. 性能监控
   - 监控响应时间
   - 跟踪资源使用
   - 检查并发情况
   - 优化性能瓶颈

4. 安全更新
   - 更新依赖包
   - 检查安全漏洞
   - 更新加密算法
   - 审查访问权限

## 3. 注意事项

1. 确保云环境有足够的资源配额
2. 云函数需要配置足够的超时时间（建议300s）
3. 需要在云开发控制台开启HTTP-triggered网关服务
4. 建议定期检查云存储用量

## 4. 常见问题

### 4.1 云函数部署失败
- 检查依赖是否安装完整
- 检查云函数目录结构是否正确
- 确认云环境ID配置正确

### 4.2 云存储访问失败
- 检查云存储权限设置
- 确认文件路径格式正确
- 验证用户权限是否正确

### 4.3 视频分析失败
- 检查ffmpeg依赖是否安装成功
- 确认视频格式是否支持
- 检查内存使用是否超限

## 5. 联系支持

如遇到问题，请通过以下方式获取支持：
- 提交 GitHub Issue
- 发送邮件至 <EMAIL>
- 加入技术支持群：123456789 