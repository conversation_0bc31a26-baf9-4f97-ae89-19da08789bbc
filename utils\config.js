module.exports = {
    url: 'http://**********',
    appKey: '**********',
    appID: '**********',
    appSecret: '**********',
    showSuccessTime: 1000,
    cloudEnv: 'device-system-xxxxx',
    clubApi: {
        put: 'https://api.wxappclub.com/put',
        get: 'https://api.wxappclub.com/get',
        del: 'https://api.wxappclub.com/del',
        match: 'https://api.wxappclub.com/match',
        list: 'https://api.wxappclub.com/list',
        wxUser: 'https://api.wxappclub.com/wxUser'
    },
    deviceApi: {
        list: 'https://api.example.com/devices/list'
    },
    deviceStatus: {
        ONLINE: 'online',
        OFFLINE: 'offline',
        MAINTENANCE: 'maintenance',
        ERROR: 'error'
    },
    operationType: {
        ADD: 'add',
        UPDATE: 'update',
        DELETE: 'delete',
        MAINTAIN: 'maintain',
        REPAIR: 'repair',
        STATUS_CHANGE: 'status_change'
    },
    deviceType: {
        TYPE_A: 'typeA',
        TYPE_B: 'typeB',
        TYPE_C: 'typeC'
    },
    maintenanceStatus: {
        SCHEDULED: 'scheduled',
        IN_PROGRESS: 'in_progress',
        COMPLETED: 'completed',
        CANCELLED: 'cancelled'
    },
    alertStatus: {
        ACTIVE: 'active',
        ACKNOWLEDGED: 'acknowledged',
        RESOLVED: 'resolved'
    },
    alertLevel: {
        LOW: 'low',
        MEDIUM: 'medium',
        HIGH: 'high',
        CRITICAL: 'critical'
    },
    storageKeys: {
        userInfo: 'userInfo',
        token: 'token',
        deviceCache: 'deviceCache',
        settings: 'settings'
    },
    defaults: {
        pageSize: 10,
        maintenanceInterval: 30,
        alertTimeout: 24,
        retryTimes: 5,
        retryDelay: 1000,
        pollConfig: {
            initialDelay: 1500,    // 初始轮询间隔
            normalDelay: 2000,     // 正常轮询间隔
            longDelay: 8000,       // 长轮询间隔
            maxDelay: 12000,       // 最大轮询间隔
            progressThresholds: {   // 进度阈值
                low: 30,           // 低进度阈值
                high: 70           // 高进度阈值
            },
            backoffFactor: 1.5,    // 退避因子
            maxRetries: 15,        // 最大重试次数
            retryBackoffStart: 10  // 开始使用退避算法的重试次数
        },
        cacheExpiration: 5 * 60 * 1000,
        requestTimeout: 60000 // 设置请求超时时间为60秒
    }
};