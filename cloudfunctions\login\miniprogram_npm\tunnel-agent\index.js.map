{"version": 3, "sources": ["index.js"], "names": [], "mappings": ";;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.js", "sourcesContent": ["\n\nvar net = require('net')\n  , tls = require('tls')\n  , http = require('http')\n  , https = require('https')\n  , events = require('events')\n  , assert = require('assert')\n  , util = require('util')\n  , Buffer = require('safe-buffer').Buffer\n  ;\n\nexports.httpOverHttp = httpOverHttp\nexports.httpsOverHttp = httpsOverHttp\nexports.httpOverHttps = httpOverHttps\nexports.httpsOverHttps = httpsOverHttps\n\n\nfunction httpOverHttp(options) {\n  var agent = new TunnelingAgent(options)\n  agent.request = http.request\n  return agent\n}\n\nfunction httpsOverHttp(options) {\n  var agent = new TunnelingAgent(options)\n  agent.request = http.request\n  agent.createSocket = createSecureSocket\n  agent.defaultPort = 443\n  return agent\n}\n\nfunction httpOverHttps(options) {\n  var agent = new TunnelingAgent(options)\n  agent.request = https.request\n  return agent\n}\n\nfunction httpsOverHttps(options) {\n  var agent = new TunnelingAgent(options)\n  agent.request = https.request\n  agent.createSocket = createSecureSocket\n  agent.defaultPort = 443\n  return agent\n}\n\n\nfunction TunnelingAgent(options) {\n  var self = this\n  self.options = options || {}\n  self.proxyOptions = self.options.proxy || {}\n  self.maxSockets = self.options.maxSockets || http.Agent.defaultMaxSockets\n  self.requests = []\n  self.sockets = []\n\n  self.on('free', function onFree(socket, host, port) {\n    for (var i = 0, len = self.requests.length; i < len; ++i) {\n      var pending = self.requests[i]\n      if (pending.host === host && pending.port === port) {\n        // Detect the request to connect same origin server,\n        // reuse the connection.\n        self.requests.splice(i, 1)\n        pending.request.onSocket(socket)\n        return\n      }\n    }\n    socket.destroy()\n    self.removeSocket(socket)\n  })\n}\nutil.inherits(TunnelingAgent, events.EventEmitter)\n\nTunnelingAgent.prototype.addRequest = function addRequest(req, options) {\n  var self = this\n\n   // Legacy API: addRequest(req, host, port, path)\n  if (typeof options === 'string') {\n    options = {\n      host: options,\n      port: arguments[2],\n      path: arguments[3]\n    };\n  }\n\n  if (self.sockets.length >= this.maxSockets) {\n    // We are over limit so we'll add it to the queue.\n    self.requests.push({host: options.host, port: options.port, request: req})\n    return\n  }\n\n  // If we are under maxSockets create a new one.\n  self.createConnection({host: options.host, port: options.port, request: req})\n}\n\nTunnelingAgent.prototype.createConnection = function createConnection(pending) {\n  var self = this\n\n  self.createSocket(pending, function(socket) {\n    socket.on('free', onFree)\n    socket.on('close', onCloseOrRemove)\n    socket.on('agentRemove', onCloseOrRemove)\n    pending.request.onSocket(socket)\n\n    function onFree() {\n      self.emit('free', socket, pending.host, pending.port)\n    }\n\n    function onCloseOrRemove(err) {\n      self.removeSocket(socket)\n      socket.removeListener('free', onFree)\n      socket.removeListener('close', onCloseOrRemove)\n      socket.removeListener('agentRemove', onCloseOrRemove)\n    }\n  })\n}\n\nTunnelingAgent.prototype.createSocket = function createSocket(options, cb) {\n  var self = this\n  var placeholder = {}\n  self.sockets.push(placeholder)\n\n  var connectOptions = mergeOptions({}, self.proxyOptions,\n    { method: 'CONNECT'\n    , path: options.host + ':' + options.port\n    , agent: false\n    }\n  )\n  if (connectOptions.proxyAuth) {\n    connectOptions.headers = connectOptions.headers || {}\n    connectOptions.headers['Proxy-Authorization'] = 'Basic ' +\n        Buffer.from(connectOptions.proxyAuth).toString('base64')\n  }\n\n  debug('making CONNECT request')\n  var connectReq = self.request(connectOptions)\n  connectReq.useChunkedEncodingByDefault = false // for v0.6\n  connectReq.once('response', onResponse) // for v0.6\n  connectReq.once('upgrade', onUpgrade)   // for v0.6\n  connectReq.once('connect', onConnect)   // for v0.7 or later\n  connectReq.once('error', onError)\n  connectReq.end()\n\n  function onResponse(res) {\n    // Very hacky. This is necessary to avoid http-parser leaks.\n    res.upgrade = true\n  }\n\n  function onUpgrade(res, socket, head) {\n    // Hacky.\n    process.nextTick(function() {\n      onConnect(res, socket, head)\n    })\n  }\n\n  function onConnect(res, socket, head) {\n    connectReq.removeAllListeners()\n    socket.removeAllListeners()\n\n    if (res.statusCode === 200) {\n      assert.equal(head.length, 0)\n      debug('tunneling connection has established')\n      self.sockets[self.sockets.indexOf(placeholder)] = socket\n      cb(socket)\n    } else {\n      debug('tunneling socket could not be established, statusCode=%d', res.statusCode)\n      var error = new Error('tunneling socket could not be established, ' + 'statusCode=' + res.statusCode)\n      error.code = 'ECONNRESET'\n      options.request.emit('error', error)\n      self.removeSocket(placeholder)\n    }\n  }\n\n  function onError(cause) {\n    connectReq.removeAllListeners()\n\n    debug('tunneling socket could not be established, cause=%s\\n', cause.message, cause.stack)\n    var error = new Error('tunneling socket could not be established, ' + 'cause=' + cause.message)\n    error.code = 'ECONNRESET'\n    options.request.emit('error', error)\n    self.removeSocket(placeholder)\n  }\n}\n\nTunnelingAgent.prototype.removeSocket = function removeSocket(socket) {\n  var pos = this.sockets.indexOf(socket)\n  if (pos === -1) return\n\n  this.sockets.splice(pos, 1)\n\n  var pending = this.requests.shift()\n  if (pending) {\n    // If we have pending requests and a socket gets closed a new one\n    // needs to be created to take over in the pool for the one that closed.\n    this.createConnection(pending)\n  }\n}\n\nfunction createSecureSocket(options, cb) {\n  var self = this\n  TunnelingAgent.prototype.createSocket.call(self, options, function(socket) {\n    // 0 is dummy port for v0.6\n    var secureSocket = tls.connect(0, mergeOptions({}, self.options,\n      { servername: options.host\n      , socket: socket\n      }\n    ))\n    self.sockets[self.sockets.indexOf(socket)] = secureSocket\n    cb(secureSocket)\n  })\n}\n\n\nfunction mergeOptions(target) {\n  for (var i = 1, len = arguments.length; i < len; ++i) {\n    var overrides = arguments[i]\n    if (typeof overrides === 'object') {\n      var keys = Object.keys(overrides)\n      for (var j = 0, keyLen = keys.length; j < keyLen; ++j) {\n        var k = keys[j]\n        if (overrides[k] !== undefined) {\n          target[k] = overrides[k]\n        }\n      }\n    }\n  }\n  return target\n}\n\n\nvar debug\nif (process.env.NODE_DEBUG && /\\btunnel\\b/.test(process.env.NODE_DEBUG)) {\n  debug = function() {\n    var args = Array.prototype.slice.call(arguments)\n    if (typeof args[0] === 'string') {\n      args[0] = 'TUNNEL: ' + args[0]\n    } else {\n      args.unshift('TUNNEL:')\n    }\n    console.error.apply(console, args)\n  }\n} else {\n  debug = function() {}\n}\nexports.debug = debug // for test\n"]}