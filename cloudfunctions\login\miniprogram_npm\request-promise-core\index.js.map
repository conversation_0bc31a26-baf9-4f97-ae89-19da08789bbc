{"version": 3, "sources": ["plumbing.js", "errors.js"], "names": [], "mappings": ";;;;;;;AAAA;AACA;AACA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.js", "sourcesContent": ["\n\nvar errors = require('./errors.js'),\n    isFunction = require('lodash/isFunction'),\n    isObjectLike = require('lodash/isObjectLike'),\n    isString = require('lodash/isString'),\n    isUndefined = require('lodash/isUndefined');\n\n\nmodule.exports = function (options) {\n\n    var errorText = 'Please verify options'; // For better minification because this string is repeating\n\n    if (!isObjectLike(options)) {\n        throw new TypeError(errorText);\n    }\n\n    if (!isFunction(options.PromiseImpl)) {\n        throw new TypeError(errorText + '.PromiseImpl');\n    }\n\n    if (!isUndefined(options.constructorMixin) && !isFunction(options.constructorMixin)) {\n        throw new TypeError(errorText + '.PromiseImpl');\n    }\n\n    var PromiseImpl = options.PromiseImpl;\n    var constructorMixin = options.constructorMixin;\n\n\n    var plumbing = {};\n\n    plumbing.init = function (requestOptions) {\n\n        var self = this;\n\n        self._rp_promise = new PromiseImpl(function (resolve, reject) {\n            self._rp_resolve = resolve;\n            self._rp_reject = reject;\n            if (constructorMixin) {\n                constructorMixin.apply(self, arguments); // Using arguments since specific Promise libraries may pass additional parameters\n            }\n        });\n\n        self._rp_callbackOrig = requestOptions.callback;\n        requestOptions.callback = self.callback = function RP$callback(err, response, body) {\n            plumbing.callback.call(self, err, response, body);\n        };\n\n        if (isString(requestOptions.method)) {\n            requestOptions.method = requestOptions.method.toUpperCase();\n        }\n\n        requestOptions.transform = requestOptions.transform || plumbing.defaultTransformations[requestOptions.method];\n\n        self._rp_options = requestOptions;\n        self._rp_options.simple = requestOptions.simple !== false;\n        self._rp_options.resolveWithFullResponse = requestOptions.resolveWithFullResponse === true;\n        self._rp_options.transform2xxOnly = requestOptions.transform2xxOnly === true;\n\n    };\n\n    plumbing.defaultTransformations = {\n        HEAD: function (body, response, resolveWithFullResponse) {\n            return resolveWithFullResponse ? response : response.headers;\n        }\n    };\n\n    plumbing.callback = function (err, response, body) {\n\n        var self = this;\n\n        var origCallbackThrewException = false, thrownException = null;\n\n        if (isFunction(self._rp_callbackOrig)) {\n            try {\n                self._rp_callbackOrig.apply(self, arguments); // TODO: Apply to self mimics behavior of request@2. Is that also right for request@next?\n            } catch (e) {\n                origCallbackThrewException = true;\n                thrownException = e;\n            }\n        }\n\n        var is2xx = !err && /^2/.test('' + response.statusCode);\n\n        if (err) {\n\n            self._rp_reject(new errors.RequestError(err, self._rp_options, response));\n\n        } else if (self._rp_options.simple && !is2xx) {\n\n            if (isFunction(self._rp_options.transform) && self._rp_options.transform2xxOnly === false) {\n\n                (new PromiseImpl(function (resolve) {\n                    resolve(self._rp_options.transform(body, response, self._rp_options.resolveWithFullResponse)); // transform may return a Promise\n                }))\n                    .then(function (transformedResponse) {\n                        self._rp_reject(new errors.StatusCodeError(response.statusCode, body, self._rp_options, transformedResponse));\n                    })\n                    .catch(function (transformErr) {\n                        self._rp_reject(new errors.TransformError(transformErr, self._rp_options, response));\n                    });\n\n            } else {\n                self._rp_reject(new errors.StatusCodeError(response.statusCode, body, self._rp_options, response));\n            }\n\n        } else {\n\n            if (isFunction(self._rp_options.transform) && (is2xx || self._rp_options.transform2xxOnly === false)) {\n\n                (new PromiseImpl(function (resolve) {\n                    resolve(self._rp_options.transform(body, response, self._rp_options.resolveWithFullResponse)); // transform may return a Promise\n                }))\n                    .then(function (transformedResponse) {\n                        self._rp_resolve(transformedResponse);\n                    })\n                    .catch(function (transformErr) {\n                        self._rp_reject(new errors.TransformError(transformErr, self._rp_options, response));\n                    });\n\n            } else if (self._rp_options.resolveWithFullResponse) {\n                self._rp_resolve(response);\n            } else {\n                self._rp_resolve(body);\n            }\n\n        }\n\n        if (origCallbackThrewException) {\n            throw thrownException;\n        }\n\n    };\n\n    plumbing.exposePromiseMethod = function (exposeTo, bindTo, promisePropertyKey, methodToExpose, exposeAs) {\n\n        exposeAs = exposeAs || methodToExpose;\n\n        if (exposeAs in exposeTo) {\n            throw new Error('Unable to expose method \"' + exposeAs + '\"');\n        }\n\n        exposeTo[exposeAs] = function RP$exposed() {\n            var self = bindTo || this;\n            return self[promisePropertyKey][methodToExpose].apply(self[promisePropertyKey], arguments);\n        };\n\n    };\n\n    plumbing.exposePromise = function (exposeTo, bindTo, promisePropertyKey, exposeAs) {\n\n        exposeAs = exposeAs || 'promise';\n\n        if (exposeAs in exposeTo) {\n            throw new Error('Unable to expose method \"' + exposeAs + '\"');\n        }\n\n        exposeTo[exposeAs] = function RP$promise() {\n            var self = bindTo || this;\n            return self[promisePropertyKey];\n        };\n\n    };\n\n    return plumbing;\n\n};\n", "\n\n\nfunction RequestError(cause, options, response) {\n\n    this.name = 'RequestError';\n    this.message = String(cause);\n    this.cause = cause;\n    this.error = cause; // legacy attribute\n    this.options = options;\n    this.response = response;\n\n    if (Error.captureStackTrace) { // required for non-V8 environments\n        Error.captureStackTrace(this);\n    }\n\n}\nRequestError.prototype = Object.create(Error.prototype);\nRequestError.prototype.constructor = RequestError;\n\n\nfunction StatusCodeError(statusCode, body, options, response) {\n\n    this.name = 'StatusCodeError';\n    this.statusCode = statusCode;\n    this.message = statusCode + ' - ' + (JSON && JSON.stringify ? JSON.stringify(body) : body);\n    this.error = body; // legacy attribute\n    this.options = options;\n    this.response = response;\n\n    if (Error.captureStackTrace) { // required for non-V8 environments\n        Error.captureStackTrace(this);\n    }\n\n}\nStatusCodeError.prototype = Object.create(Error.prototype);\nStatusCodeError.prototype.constructor = StatusCodeError;\n\n\nfunction TransformError(cause, options, response) {\n\n    this.name = 'TransformError';\n    this.message = String(cause);\n    this.cause = cause;\n    this.error = cause; // legacy attribute\n    this.options = options;\n    this.response = response;\n\n    if (Error.captureStackTrace) { // required for non-V8 environments\n        Error.captureStackTrace(this);\n    }\n\n}\nTransformError.prototype = Object.create(Error.prototype);\nTransformError.prototype.constructor = TransformError;\n\n\nmodule.exports = {\n    RequestError: RequestError,\n    StatusCodeError: StatusCodeError,\n    TransformError: TransformError\n};\n"]}