const cloud = require('wx-server-sdk');

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV // 使用动态环境ID
});

const db = cloud.database();
const _ = db.command;
const analysisIndexCollection = db.collection('analysis_index');

/**
 * 确保路径使用下划线格式
 * 将所有连字符替换为下划线，与getUserData云函数保持一致
 * @param {string} path - 输入路径
 * @returns {string} - 格式化后的路径
 */
function ensureUnderscoreFormat(path) {
  if (!path) return path;
  // 将所有连字符替换为下划线
  return path.replace(/-/g, '_');
}

/**
 * 格式化文件路径为 Cloud File ID
 * 处理 cloud:// 前缀和环境ID
 * @param {string | null} filePath - 文件路径或 File ID
 * @returns {string | null} - 格式化的 Cloud File ID 或 null
 */
function formatFileID(filePath) {
  if (!filePath || typeof filePath !== 'string') return null;

  // 如果已经是URL，直接返回null，因为不能删除HTTP URL
  if (filePath.startsWith('http://') || filePath.startsWith('https://')) {
      console.log(`路径是 HTTP URL，无法删除: ${filePath.substring(0, 50)}...`);
      return null;
  }

  // 如果已经是 cloud:// 格式，先规范环境ID中的连字符与下划线
  if (filePath.startsWith('cloud://')) {
    // 拆分出环境ID部分与路径部分
    const withoutPrefix = filePath.substring('cloud://'.length);
    const firstSlash = withoutPrefix.indexOf('/');
    if (firstSlash === -1) {
      console.warn(`cloud:// 路径缺少 '/' 分隔符，原样返回: ${filePath}`);
      return filePath;
    }
    let envPart = withoutPrefix.substring(0, firstSlash);
    const pathPart = withoutPrefix.substring(firstSlash + 1);

    // 将环境ID中的所有 '_' 统一替换为 '-'，以符合腾讯云规范
    const normalizedEnvPart = envPart.replace(/_/g, '-');

    // 只有在替换后发生变化时才输出日志
    if (envPart !== normalizedEnvPart) {
      console.log(`规范化环境ID: ${envPart} => ${normalizedEnvPart}`);
    }

    const normalizedFileID = `cloud://${normalizedEnvPart}/${pathPart}`;
    return normalizedFileID;
  }

  // 获取当前云函数的运行环境信息
  const wxCloudContext = cloud.getWXContext();
  // wxCloudContext.ENV 中存储的是当前环境的字符串形式 ID
  const currentEnvString = wxCloudContext.ENV; 

  if (!currentEnvString) {
      console.error("无法获取当前环境ID字符串，无法格式化 FileID");
      return null;
  }
  
  // 处理各种可能的路径格式
  
  // 1. 检查是否是JSON字符串
  if (filePath.includes('"') || filePath.includes('{')) {
    try {
      const parsed = JSON.parse(filePath);
      if (typeof parsed === 'string') {
        return formatFileID(parsed); // 递归处理提取的字符串
      } else if (parsed && typeof parsed === 'object') {
        // 尝试从对象中提取文件路径
        const possiblePaths = [parsed.url, parsed.path, parsed.filePath, parsed.fileID];
        for (const path of possiblePaths) {
          if (path && typeof path === 'string') {
            const result = formatFileID(path);
            if (result) return result;
          }
        }
      }
    } catch (e) {
      // 不是有效的JSON，继续处理
    }
  }
  
  // 2. 如果路径中包含多个斜杠，尝试提取有效的文件路径部分
  if (filePath.includes('/')) {
    // 查找常见的文件扩展名
    const extensions = ['.jpg', '.jpeg', '.png', '.gif', '.mp4', '.mov', '.avi', '.webp'];
    for (const ext of extensions) {
      const extIndex = filePath.lastIndexOf(ext);
      if (extIndex > 0) {
        // 找到了扩展名，向前查找最近的斜杠
        let startIndex = filePath.lastIndexOf('/', extIndex);
        if (startIndex >= 0) {
          // 向前再找一个斜杠，尝试获取完整路径
          const prevSlash = filePath.lastIndexOf('/', startIndex - 1);
          if (prevSlash >= 0) {
            startIndex = prevSlash;
          }
          const extractedPath = filePath.substring(startIndex, extIndex + ext.length);
          console.log(`从复杂路径中提取文件部分: ${extractedPath}`);
          
          // 移除开头的斜杠（如果存在）
          const cleanPath = extractedPath.startsWith('/') ? extractedPath.substring(1) : extractedPath;
          const fileID = `cloud://${currentEnvString}/${cleanPath}`;
          console.log(`格式化提取的路径为: ${fileID}`);
          return fileID;
        }
      }
    }
  }
  
  // 3. 标准处理：移除开头的斜杠并添加cloud://前缀
  const cleanPath = filePath.startsWith('/') ? filePath.substring(1) : filePath;
  const fileID = `cloud://${currentEnvString}/${cleanPath}`;
  console.log(`标准格式化路径: ${fileID}`);
  return fileID;
}


exports.main = async (event, context) => {
  const wxContext = cloud.getWXContext();
  // 确保使用下划线格式的 openid 进行数据库查询，与getUserData保持一致
  const openid = ensureUnderscoreFormat(wxContext.OPENID);

  if (!openid) {
    console.error('无法获取用户 OpenID');
    return { success: false, error: '用户未登录或无法识别' };
  }

  const { idsToDelete } = event;

  if (!idsToDelete || !Array.isArray(idsToDelete) || idsToDelete.length === 0) {
    console.error('无效的删除请求参数:', idsToDelete);
    return { success: false, error: '无效的删除请求参数' };
  }

  console.log(`收到删除请求: openid=${openid}, ids=${idsToDelete.join(', ')}`);

  try {
    // 1. 识别要删除的数据库记录
    const queryConditions = idsToDelete.map(id => {
      // 记录每个ID的格式，帮助调试
      console.log(`处理ID: ${id}, 长度: ${id ? id.length : 'undefined'}, 格式: ${typeof id}`);
      
      // 检查ID格式：MongoDB ObjectId 通常是 24 位十六进制字符，但也接受32位格式
      if (typeof id === 'string' && /^[0-9a-fA-F]{24}$/.test(id)) {
        // 如果是标准 ObjectId 格式，按 _id 匹配
        console.log(`ID ${id} 被识别为标准24位ObjectId格式，使用_id查询`);
        return { _id: id, _openid: openid };
      } else if (typeof id === 'string' && /^[0-9a-fA-F]{32}$/.test(id)) {
        // 如果是32位十六进制格式，尝试同时按_id和sessionId匹配
        console.log(`ID ${id} 被识别为32位十六进制格式，同时尝试_id和sessionId查询`);
        return { 
          $or: [
            { _id: id, _openid: openid },
            { sessionId: id, _openid: openid }
          ]
        };
      } else if (typeof id === 'string' && id.trim() !== '') {
        // 其他非空字符串，按 sessionId 匹配
        console.log(`ID ${id} 被识别为一般字符串，使用sessionId查询`);
        return { sessionId: id, _openid: openid };
      }
      console.log(`ID ${id} 格式无效，将被忽略`);
      return null; // 忽略无效的ID格式
    }).filter(q => q !== null); // 过滤掉 null 条件

    // 添加更多调试信息
    console.log(`构建了 ${queryConditions.length} 个有效的查询条件`);
    queryConditions.forEach((condition, index) => {
      console.log(`查询条件 ${index+1}:`, JSON.stringify(condition));
    });

    if (queryConditions.length === 0) {
        console.warn('没有有效的ID可供数据库查询');
        return { success: false, error: '没有有效的ID可供删除' };
    }

    // 使用更灵活的查询条件组合
    const finalQuery = queryConditions.length === 1 ? queryConditions[0] : { $or: queryConditions };
    console.log("最终数据库查询条件:", JSON.stringify(finalQuery));

    // 声明变量用于存储找到的记录和要删除的记录ID
    let recordsFound = [];
    const recordIdsToDelete = [];

    // 查询数据库获取待删除记录的详细信息
    const getRes = await analysisIndexCollection.where(finalQuery).get();

    if (!getRes.data || getRes.data.length === 0) {
      console.log('在数据库中未找到与提供ID匹配的记录');
      
      // 尝试一个备用的查询策略：直接查询用户的所有记录，然后在内存中过滤
      console.log('尝试备用查询策略: 查询用户所有记录并在内存中匹配');
      try {
        const allUserRecords = await analysisIndexCollection.where({ _openid: openid }).get();
        
        if (allUserRecords.data && allUserRecords.data.length > 0) {
          console.log(`找到用户的 ${allUserRecords.data.length} 条记录，尝试在内存中匹配`);
          
          // 打印前5条记录的ID信息，帮助调试
          allUserRecords.data.slice(0, 5).forEach((record, idx) => {
            console.log(`记录 ${idx+1}: _id=${record._id}, sessionId=${record.sessionId || 'undefined'}`);
          });
          
          // 在内存中匹配ID
          const matchedRecords = allUserRecords.data.filter(record => {
            return idsToDelete.some(id => {
              // 检查各种可能的匹配方式
              const matches = [
                record._id === id,                                  // 精确匹配_id
                record.sessionId === id,                            // 精确匹配sessionId
                record._id && id && record._id.includes(id),        // _id包含id
                id && record._id && id.includes(record._id),        // id包含_id
                record.sessionId && id && record.sessionId.includes(id), // sessionId包含id
                id && record.sessionId && id.includes(record.sessionId)  // id包含sessionId
              ];
              
              return matches.some(match => match === true);
            });
          });
          
          if (matchedRecords.length > 0) {
            console.log(`在内存中匹配到 ${matchedRecords.length} 条记录`);
            // 使用匹配到的记录替代之前的空结果
            recordsFound = matchedRecords;
            // 收集这些记录的ID
            recordsFound.forEach(record => {
              recordIdsToDelete.push(record._id);
            });
            
            console.log(`准备删除这些记录: ${recordIdsToDelete.join(', ')}`);
          } else {
            console.log('在内存中也未能匹配到记录');
            return { success: true, deletedCount: 0, message: '未找到要删除的数据' };
          }
        } else {
          console.log('用户没有任何记录');
          return { success: true, deletedCount: 0, message: '未找到要删除的数据' };
        }
      } catch (backupErr) {
        console.error('备用查询策略失败:', backupErr);
        return { success: true, deletedCount: 0, message: '未找到要删除的数据' };
      }
    } else {
      // 将查询结果赋值给外部作用域的 recordsFound 变量，
      // 避免使用 const 重新声明导致外部 recordsFound 依旧为空，
      // 造成后续无法遍历文件路径的问题。
      recordsFound = getRes.data;
      
      // 收集数据库记录的 _id
      recordsFound.forEach(record => {
        recordIdsToDelete.push(record._id);
      });
    }

          // 2. 准备文件列表以供删除
      const fileIDsToDeleteSet = new Set(); // 使用 Set 避免重复添加

      // 确保recordsFound是一个数组
      if (!Array.isArray(recordsFound)) {
        console.warn('recordsFound不是数组，将其转换为空数组');
        recordsFound = [];
      }

      recordsFound.forEach(record => {
        // 从记录中收集所有可能的文件路径字段
        // 首先打印记录的所有键，以便调试
        console.log(`记录 ${record._id} 的字段:`, Object.keys(record));
        
        // 检查是否有图像数据字段
        if (record.cAreaImage || record.tAreaImage) {
          console.log(`记录 ${record._id} 包含图像数据字段`);
        }
        
        /*
         * ============================= 🎯 精确收集核心文件 =============================
         * 只收集一个数据组的5个核心文件，避免重复和无关文件
         */

        // 🎯 只收集5个核心文件路径（去重）
        const coreFilePaths = [];

        // 1. 视频文件（只使用Path字段，排除URL字段）
        if (record.videoPath && !record.videoPath.startsWith('http')) {
          coreFilePaths.push(record.videoPath);
        }

        // 2. C区图片（只使用Path字段）
        if (record.cAreaImagePath && !record.cAreaImagePath.startsWith('http')) {
          coreFilePaths.push(record.cAreaImagePath);
        }

        // 3. T区图片（只使用Path字段）
        if (record.tAreaImagePath && !record.tAreaImagePath.startsWith('http')) {
          coreFilePaths.push(record.tAreaImagePath);
        }

        // 4. 标记图片（只使用Path字段）
        if (record.markedImagePath && !record.markedImagePath.startsWith('http')) {
          coreFilePaths.push(record.markedImagePath);
        }

        // 5. 结果文件（只使用Path字段）
        if (record.resultFilePath && !record.resultFilePath.startsWith('http')) {
          coreFilePaths.push(record.resultFilePath);
        }

        // 过滤掉空值和重复值
        const potentialPaths = [...new Set(coreFilePaths.filter(Boolean))];
        
        // 🎯 显示每个数据组的核心文件信息
        console.log(`📊 数据组 ${record._id} 包含 ${potentialPaths.length} 个核心文件:`, {
          sessionId: record.sessionId,
          文件列表: potentialPaths.length <= 5 ? potentialPaths : `${potentialPaths.length}个文件（预期5个）`
        });
        
        // 🎯 直接处理核心文件路径，不进行复杂的正则提取
        potentialPaths.forEach(filePath => {
          if (filePath && typeof filePath === 'string') {
            // 跳过明显的非文件路径
            if (filePath.startsWith('data:image') ||
                filePath.startsWith('http://') ||
                filePath.startsWith('https://')) {
              console.log(`跳过非文件路径: ${filePath.substring(0, 30)}...`);
              return;
            }

            // 🎯 直接格式化文件路径，不进行正则提取
            const formattedID = formatFileID(filePath);
            if (formattedID) {
              fileIDsToDeleteSet.add(formattedID);
              if (fileIDsToDeleteSet.size <= 20) {
                console.log(`添加核心文件ID: ${formattedID}`);
              }
            }
          }
        });
      });

    const fileIDsToDelete = Array.from(fileIDsToDeleteSet); // 转换为数组

    console.log(`准备删除 ${fileIDsToDelete.length} 个唯一文件:`, fileIDsToDelete);
    console.log(`准备删除 ${recordIdsToDelete.length} 条数据库记录`);

    // 3. 删除云存储中的文件
    let fileDeletionResult = { success: true, deletedList: [], errMsg: '没有文件需要删除' };
    if (fileIDsToDelete.length > 0) {
      try {
        console.log(`调用 cloud.deleteFile 删除 ${fileIDsToDelete.length} 个文件...`);
        
        // 🔧 只在文件数量较少时显示详细列表
        if (fileIDsToDelete.length <= 20) {
          console.log('文件删除列表:');
          fileIDsToDelete.forEach((fileID, index) => {
            console.log(`[${index + 1}] ${fileID}`);
          });
        } else {
          console.log(`准备删除 ${fileIDsToDelete.length} 个文件（数量较多，不显示详细列表）`);
        }
        
        // 检查文件ID格式是否正确
        const validFileIDs = fileIDsToDelete.filter(id => 
          typeof id === 'string' && 
          id.startsWith('cloud://') && 
          id.includes('/')
        );
        
        if (validFileIDs.length !== fileIDsToDelete.length) {
          console.warn(`过滤后只有 ${validFileIDs.length}/${fileIDsToDelete.length} 个有效的文件ID`);
        }
        
        if (validFileIDs.length === 0) {
          console.warn('没有有效的文件ID可供删除');
          fileDeletionResult = { success: false, error: '没有有效的文件ID', errMsg: '文件ID格式无效' };
        } else {
          // 🔧 分批删除文件，每批最多50个（微信云开发API限制）
          const batchSize = 50;
          const batches = [];
          
          for (let i = 0; i < validFileIDs.length; i += batchSize) {
            batches.push(validFileIDs.slice(i, i + batchSize));
          }
          
          console.log(`将 ${validFileIDs.length} 个文件分为 ${batches.length} 批进行删除`);
          
          // 存储所有批次的删除结果
          const batchResults = [];
          
          // 🔧 逐批删除文件，添加重试机制
          for (let i = 0; i < batches.length; i++) {
            const batch = batches[i];
            console.log(`删除第 ${i+1}/${batches.length} 批，包含 ${batch.length} 个文件`);

            let batchResult = null;
            let retryCount = 0;
            const maxRetries = 2;

            // 🔄 重试机制
            while (retryCount <= maxRetries && !batchResult) {
              try {
                if (retryCount > 0) {
                  console.log(`第 ${i+1} 批重试第 ${retryCount} 次...`);
                  // 重试前等待一段时间
                  await new Promise(resolve => setTimeout(resolve, 1000 * retryCount));
                }

                batchResult = await cloud.deleteFile({
                  fileList: batch,
                });

                // 🔧 只在出错或最后一批时输出详细结果
                if (i === batches.length - 1 || batchResult.fileList.some(f => f.status !== 0)) {
                  console.log(`第 ${i+1} 批删除结果:`, JSON.stringify(batchResult));
                }
                batchResults.push(batchResult);
                break; // 成功则跳出重试循环

              } catch (batchErr) {
                retryCount++;
                console.error(`第 ${i+1} 批删除出错 (尝试 ${retryCount}/${maxRetries + 1}):`, batchErr.message);

                if (retryCount > maxRetries) {
                  // 重试次数用完，记录失败结果
                  batchResults.push({
                    fileList: batch.map(id => ({ fileID: id, status: -1, errMsg: batchErr.message })),
                    errMsg: batchErr.message
                  });
                }
              }
            }
          }
          
          // 合并所有批次的结果
          const allFileList = batchResults.flatMap(result => result.fileList || []);
          fileDeletionResult = {
            success: true,
            fileList: allFileList,
            deletedCount: allFileList.filter(f => f.status === 0).length,
            failedCount: allFileList.filter(f => f.status !== 0).length,
            errMsg: batchResults.some(r => r.errMsg !== 'deleteFile:ok') ? '部分文件删除失败' : 'deleteFile:ok'
          };
          
          console.log('文件删除汇总结果:', {
            总文件数: allFileList.length,
            成功删除: fileDeletionResult.deletedCount,
            删除失败: fileDeletionResult.failedCount
          });
        }
      } catch(fileErr) {
        console.error('调用 cloud.deleteFile 时发生严重错误:', fileErr);
        // 记录错误，但继续尝试删除数据库记录
        fileDeletionResult = { 
          success: false, 
          error: '删除云存储文件时出错', 
          errMsg: fileErr.message,
          fileList: fileIDsToDelete.map(id => ({ fileID: id, status: -1, errMsg: fileErr.message }))
        };
      }
    } else {
      console.log('没有文件需要从云存储删除');
    }

    // 4. 删除数据库中的记录
    let dbDeletionResult = { stats: { removed: 0 } };
    if (recordIdsToDelete.length > 0) {
      try {
        console.log(`调用数据库 remove 操作删除 ${recordIdsToDelete.length} 条记录...`);
        // 使用 _id 列表进行批量删除
        dbDeletionResult = await analysisIndexCollection.where({
          _id: _.in(recordIdsToDelete)
        }).remove();
        console.log('数据库记录删除操作返回:', JSON.stringify(dbDeletionResult));

        if (dbDeletionResult.stats.removed !== recordIdsToDelete.length) {
          console.warn(`数据库删除数量不匹配: 预期删除 ${recordIdsToDelete.length} 条, 实际删除 ${dbDeletionResult.stats.removed} 条`);
        } else {
          console.log(`成功从数据库删除 ${dbDeletionResult.stats.removed} 条记录`);
        }
      } catch (dbErr) {
        console.error('删除数据库记录时发生错误:', dbErr);
        // 如果数据库删除失败，则返回整体失败
        return {
             success: false,
             error: '删除数据库记录失败',
             errMsg: dbErr.message,
             fileDeletionStatus: fileDeletionResult // 附带文件删除状态信息
        };
      }
    } else {
        console.log('没有数据库记录需要删除');
    }

    // 5. 返回最终结果
    // 主要以数据库删除操作为准，附带文件删除信息
    return {
      success: true, // 表示云函数主体逻辑成功执行（即使部分文件删除失败）
      deletedCount: dbDeletionResult.stats.removed, // 实际从数据库删除的记录数
      fileDeletionStatus: {
        success: fileDeletionResult.success,
        totalFiles: fileIDsToDelete.length,
        deletedFiles: fileDeletionResult.deletedCount || 0,
        failedFiles: fileDeletionResult.failedCount || 0,
        message: fileIDsToDelete.length > 0 
          ? `成功删除 ${fileDeletionResult.deletedCount || 0}/${fileIDsToDelete.length} 个文件` 
          : '没有文件需要删除'
      },
      message: `成功删除 ${dbDeletionResult.stats.removed} 条数据记录${
        fileIDsToDelete.length > 0 
          ? `，${fileDeletionResult.deletedCount || 0}/${fileIDsToDelete.length} 个文件` 
          : ''
      }`
    };

  } catch (error) {
    console.error('deleteUserData 云函数执行过程中发生未捕获错误:', error);
    return {
      success: false,
      error: '云函数执行错误',
      errMsg: error.message
    };
  }
}; 