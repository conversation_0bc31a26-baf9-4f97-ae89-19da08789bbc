{"version": 3, "sources": ["validate.js"], "names": [], "mappings": ";;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.js", "sourcesContent": ["/**\r\n * JSONSchema Validator - Validates JavaScript objects using JSON Schemas\r\n *\t(http://www.json.com/json-schema-proposal/)\r\n * Licensed under AFL-2.1 OR BSD-3-Clause\r\nTo use the validator call the validate function with an instance object and an optional schema object.\r\nIf a schema is provided, it will be used to validate. If the instance object refers to a schema (self-validating),\r\nthat schema will be used to validate and the schema parameter is not necessary (if both exist,\r\nboth validations will occur).\r\nThe validate method will return an array of validation errors. If there are no errors, then an\r\nempty list will be returned. A validation error will have two properties:\r\n\"property\" which indicates which property had the error\r\n\"message\" which indicates what the error was\r\n */\r\n(function (root, factory) {\r\n    if (typeof define === 'function' && define.amd) {\r\n        // AMD. Register as an anonymous module.\r\n        define([], function () {\r\n            return factory();\r\n        });\r\n    } else if (typeof module === 'object' && module.exports) {\r\n        // Node. Does not work with strict CommonJS, but\r\n        // only CommonJS-like environments that support module.exports,\r\n        // like Node.\r\n        module.exports = factory();\r\n    } else {\r\n        // Browser globals\r\n        root.jsonSchema = factory();\r\n    }\r\n}(this, function () {// setup primitive classes to be JSON Schema types\r\nvar exports = validate\r\nexports.Integer = {type:\"integer\"};\r\nvar primitiveConstructors = {\r\n\tString: String,\r\n\tBoolean: Boolean,\r\n\tNumber: Number,\r\n\tObject: Object,\r\n\tArray: Array,\r\n\tDate: Date\r\n}\r\nexports.validate = validate;\r\nfunction validate(/*Any*/instance,/*Object*/schema) {\r\n\t\t// Summary:\r\n\t\t//  \tTo use the validator call JSONSchema.validate with an instance object and an optional schema object.\r\n\t\t// \t\tIf a schema is provided, it will be used to validate. If the instance object refers to a schema (self-validating),\r\n\t\t// \t\tthat schema will be used to validate and the schema parameter is not necessary (if both exist,\r\n\t\t// \t\tboth validations will occur).\r\n\t\t// \t\tThe validate method will return an object with two properties:\r\n\t\t// \t\t\tvalid: A boolean indicating if the instance is valid by the schema\r\n\t\t// \t\t\terrors: An array of validation errors. If there are no errors, then an\r\n\t\t// \t\t\t\t\tempty list will be returned. A validation error will have two properties:\r\n\t\t// \t\t\t\t\t\tproperty: which indicates which property had the error\r\n\t\t// \t\t\t\t\t\tmessage: which indicates what the error was\r\n\t\t//\r\n\t\treturn validate(instance, schema, {changing: false});//, coerce: false, existingOnly: false});\r\n\t};\r\nexports.checkPropertyChange = function(/*Any*/value,/*Object*/schema, /*String*/property) {\r\n\t\t// Summary:\r\n\t\t// \t\tThe checkPropertyChange method will check to see if an value can legally be in property with the given schema\r\n\t\t// \t\tThis is slightly different than the validate method in that it will fail if the schema is readonly and it will\r\n\t\t// \t\tnot check for self-validation, it is assumed that the passed in value is already internally valid.\r\n\t\t// \t\tThe checkPropertyChange method will return the same object type as validate, see JSONSchema.validate for\r\n\t\t// \t\tinformation.\r\n\t\t//\r\n\t\treturn validate(value, schema, {changing: property || \"property\"});\r\n\t};\r\nvar validate = exports._validate = function(/*Any*/instance,/*Object*/schema,/*Object*/options) {\r\n\r\n\tif (!options) options = {};\r\n\tvar _changing = options.changing;\r\n\r\n\tfunction getType(schema){\r\n\t\treturn schema.type || (primitiveConstructors[schema.name] == schema && schema.name.toLowerCase());\r\n\t}\r\n\tvar errors = [];\r\n\t// validate a value against a property definition\r\n\tfunction checkProp(value, schema, path,i){\r\n\r\n\t\tvar l;\r\n\t\tpath += path ? typeof i == 'number' ? '[' + i + ']' : typeof i == 'undefined' ? '' : '.' + i : i;\r\n\t\tfunction addError(message){\r\n\t\t\terrors.push({property:path,message:message});\r\n\t\t}\r\n\r\n\t\tif((typeof schema != 'object' || schema instanceof Array) && (path || typeof schema != 'function') && !(schema && getType(schema))){\r\n\t\t\tif(typeof schema == 'function'){\r\n\t\t\t\tif(!(value instanceof schema)){\r\n\t\t\t\t\taddError(\"is not an instance of the class/constructor \" + schema.name);\r\n\t\t\t\t}\r\n\t\t\t}else if(schema){\r\n\t\t\t\taddError(\"Invalid schema/property definition \" + schema);\r\n\t\t\t}\r\n\t\t\treturn null;\r\n\t\t}\r\n\t\tif(_changing && schema.readonly){\r\n\t\t\taddError(\"is a readonly field, it can not be changed\");\r\n\t\t}\r\n\t\tif(schema['extends']){ // if it extends another schema, it must pass that schema as well\r\n\t\t\tcheckProp(value,schema['extends'],path,i);\r\n\t\t}\r\n\t\t// validate a value against a type definition\r\n\t\tfunction checkType(type,value){\r\n\t\t\tif(type){\r\n\t\t\t\tif(typeof type == 'string' && type != 'any' &&\r\n\t\t\t\t\t\t(type == 'null' ? value !== null : typeof value != type) &&\r\n\t\t\t\t\t\t!(value instanceof Array && type == 'array') &&\r\n\t\t\t\t\t\t!(value instanceof Date && type == 'date') &&\r\n\t\t\t\t\t\t!(type == 'integer' && value%1===0)){\r\n\t\t\t\t\treturn [{property:path,message:value + \" - \" + (typeof value) + \" value found, but a \" + type + \" is required\"}];\r\n\t\t\t\t}\r\n\t\t\t\tif(type instanceof Array){\r\n\t\t\t\t\tvar unionErrors=[];\r\n\t\t\t\t\tfor(var j = 0; j < type.length; j++){ // a union type\r\n\t\t\t\t\t\tif(!(unionErrors=checkType(type[j],value)).length){\r\n\t\t\t\t\t\t\tbreak;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t\tif(unionErrors.length){\r\n\t\t\t\t\t\treturn unionErrors;\r\n\t\t\t\t\t}\r\n\t\t\t\t}else if(typeof type == 'object'){\r\n\t\t\t\t\tvar priorErrors = errors;\r\n\t\t\t\t\terrors = [];\r\n\t\t\t\t\tcheckProp(value,type,path);\r\n\t\t\t\t\tvar theseErrors = errors;\r\n\t\t\t\t\terrors = priorErrors;\r\n\t\t\t\t\treturn theseErrors;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\treturn [];\r\n\t\t}\r\n\t\tif(value === undefined){\r\n\t\t\tif(schema.required){\r\n\t\t\t\taddError(\"is missing and it is required\");\r\n\t\t\t}\r\n\t\t}else{\r\n\t\t\terrors = errors.concat(checkType(getType(schema),value));\r\n\t\t\tif(schema.disallow && !checkType(schema.disallow,value).length){\r\n\t\t\t\taddError(\" disallowed value was matched\");\r\n\t\t\t}\r\n\t\t\tif(value !== null){\r\n\t\t\t\tif(value instanceof Array){\r\n\t\t\t\t\tif(schema.items){\r\n\t\t\t\t\t\tvar itemsIsArray = schema.items instanceof Array;\r\n\t\t\t\t\t\tvar propDef = schema.items;\r\n\t\t\t\t\t\tfor (i = 0, l = value.length; i < l; i += 1) {\r\n\t\t\t\t\t\t\tif (itemsIsArray)\r\n\t\t\t\t\t\t\t\tpropDef = schema.items[i];\r\n\t\t\t\t\t\t\tif (options.coerce)\r\n\t\t\t\t\t\t\t\tvalue[i] = options.coerce(value[i], propDef);\r\n\t\t\t\t\t\t\terrors.concat(checkProp(value[i],propDef,path,i));\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t\tif(schema.minItems && value.length < schema.minItems){\r\n\t\t\t\t\t\taddError(\"There must be a minimum of \" + schema.minItems + \" in the array\");\r\n\t\t\t\t\t}\r\n\t\t\t\t\tif(schema.maxItems && value.length > schema.maxItems){\r\n\t\t\t\t\t\taddError(\"There must be a maximum of \" + schema.maxItems + \" in the array\");\r\n\t\t\t\t\t}\r\n\t\t\t\t}else if(schema.properties || schema.additionalProperties){\r\n\t\t\t\t\terrors.concat(checkObj(value, schema.properties, path, schema.additionalProperties));\r\n\t\t\t\t}\r\n\t\t\t\tif(schema.pattern && typeof value == 'string' && !value.match(schema.pattern)){\r\n\t\t\t\t\taddError(\"does not match the regex pattern \" + schema.pattern);\r\n\t\t\t\t}\r\n\t\t\t\tif(schema.maxLength && typeof value == 'string' && value.length > schema.maxLength){\r\n\t\t\t\t\taddError(\"may only be \" + schema.maxLength + \" characters long\");\r\n\t\t\t\t}\r\n\t\t\t\tif(schema.minLength && typeof value == 'string' && value.length < schema.minLength){\r\n\t\t\t\t\taddError(\"must be at least \" + schema.minLength + \" characters long\");\r\n\t\t\t\t}\r\n\t\t\t\tif(typeof schema.minimum !== 'undefined' && typeof value == typeof schema.minimum &&\r\n\t\t\t\t\t\tschema.minimum > value){\r\n\t\t\t\t\taddError(\"must have a minimum value of \" + schema.minimum);\r\n\t\t\t\t}\r\n\t\t\t\tif(typeof schema.maximum !== 'undefined' && typeof value == typeof schema.maximum &&\r\n\t\t\t\t\t\tschema.maximum < value){\r\n\t\t\t\t\taddError(\"must have a maximum value of \" + schema.maximum);\r\n\t\t\t\t}\r\n\t\t\t\tif(schema['enum']){\r\n\t\t\t\t\tvar enumer = schema['enum'];\r\n\t\t\t\t\tl = enumer.length;\r\n\t\t\t\t\tvar found;\r\n\t\t\t\t\tfor(var j = 0; j < l; j++){\r\n\t\t\t\t\t\tif(enumer[j]===value){\r\n\t\t\t\t\t\t\tfound=1;\r\n\t\t\t\t\t\t\tbreak;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t\tif(!found){\r\n\t\t\t\t\t\taddError(\"does not have a value in the enumeration \" + enumer.join(\", \"));\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t\tif(typeof schema.maxDecimal == 'number' &&\r\n\t\t\t\t\t(value.toString().match(new RegExp(\"\\\\.[0-9]{\" + (schema.maxDecimal + 1) + \",}\")))){\r\n\t\t\t\t\taddError(\"may only have \" + schema.maxDecimal + \" digits of decimal places\");\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t\treturn null;\r\n\t}\r\n\t// validate an object against a schema\r\n\tfunction checkObj(instance,objTypeDef,path,additionalProp){\r\n\r\n\t\tif(typeof objTypeDef =='object'){\r\n\t\t\tif(typeof instance != 'object' || instance instanceof Array){\r\n\t\t\t\terrors.push({property:path,message:\"an object is required\"});\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\tfor(var i in objTypeDef){ \r\n\t\t\t\tif(objTypeDef.hasOwnProperty(i) && i != '__proto__' && i != 'constructor'){\r\n\t\t\t\t\tvar value = instance.hasOwnProperty(i) ? instance[i] : undefined;\r\n\t\t\t\t\t// skip _not_ specified properties\r\n\t\t\t\t\tif (value === undefined && options.existingOnly) continue;\r\n\t\t\t\t\tvar propDef = objTypeDef[i];\r\n\t\t\t\t\t// set default\r\n\t\t\t\t\tif(value === undefined && propDef[\"default\"]){\r\n\t\t\t\t\t\tvalue = instance[i] = propDef[\"default\"];\r\n\t\t\t\t\t}\r\n\t\t\t\t\tif(options.coerce && i in instance){\r\n\t\t\t\t\t\tvalue = instance[i] = options.coerce(value, propDef);\r\n\t\t\t\t\t}\r\n\t\t\t\t\tcheckProp(value,propDef,path,i);\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t\tfor(i in instance){\r\n\t\t\tif(instance.hasOwnProperty(i) && !(i.charAt(0) == '_' && i.charAt(1) == '_') && objTypeDef && !objTypeDef[i] && additionalProp===false){\r\n\t\t\t\tif (options.filter) {\r\n\t\t\t\t\tdelete instance[i];\r\n\t\t\t\t\tcontinue;\r\n\t\t\t\t} else {\r\n\t\t\t\t\terrors.push({property:path,message:\"The property \" + i +\r\n\t\t\t\t\t\t\" is not defined in the schema and the schema does not allow additional properties\"});\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\tvar requires = objTypeDef && objTypeDef[i] && objTypeDef[i].requires;\r\n\t\t\tif(requires && !(requires in instance)){\r\n\t\t\t\terrors.push({property:path,message:\"the presence of the property \" + i + \" requires that \" + requires + \" also be present\"});\r\n\t\t\t}\r\n\t\t\tvalue = instance[i];\r\n\t\t\tif(additionalProp && (!(objTypeDef && typeof objTypeDef == 'object') || !(i in objTypeDef))){\r\n\t\t\t\tif(options.coerce){\r\n\t\t\t\t\tvalue = instance[i] = options.coerce(value, additionalProp);\r\n\t\t\t\t}\r\n\t\t\t\tcheckProp(value,additionalProp,path,i);\r\n\t\t\t}\r\n\t\t\tif(!_changing && value && value.$schema){\r\n\t\t\t\terrors = errors.concat(checkProp(value,value.$schema,path,i));\r\n\t\t\t}\r\n\t\t}\r\n\t\treturn errors;\r\n\t}\r\n\tif(schema){\r\n\t\tcheckProp(instance,schema,'',_changing || '');\r\n\t}\r\n\tif(!_changing && instance && instance.$schema){\r\n\t\tcheckProp(instance,instance.$schema,'','');\r\n\t}\r\n\treturn {valid:!errors.length,errors:errors};\r\n};\r\nexports.mustBeValid = function(result){\r\n\t//\tsummary:\r\n\t//\t\tThis checks to ensure that the result is valid and will throw an appropriate error message if it is not\r\n\t// result: the result returned from checkPropertyChange or validate\r\n\tif(!result.valid){\r\n\t\tthrow new TypeError(result.errors.map(function(error){return \"for property \" + error.property + ': ' + error.message;}).join(\", \\n\"));\r\n\t}\r\n}\r\n\r\nreturn exports;\r\n}));\r\n"]}