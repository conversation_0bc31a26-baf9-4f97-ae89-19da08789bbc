{"version": 3, "sources": ["index.js", "geo/index.js", "geo/point.js", "validate.js", "constant.js", "util.js", "serverDate/index.js", "helper/symbol.js", "utils/symbol.js", "utils/type.js", "geo/lineString.js", "geo/polygon.js", "geo/multiPoint.js", "geo/multiLineString.js", "geo/multiPolygon.js", "collection.js", "document.js", "lib/util.js", "serializer/update.js", "commands/update.js", "operator-map.js", "commands/query.js", "commands/logic.js", "serializer/common.js", "serializer/datatype.js", "realtime/websocket-client.js", "realtime/virtual-websocket-client.js", "realtime/message.js", "utils/error.js", "config/error.config.js", "utils/utils.js", "realtime/listener.js", "realtime/snapshot.js", "realtime/error.js", "realtime/ws-event.js", "query.js", "serializer/query.js", "aggregate.js", "command.js", "regexp/index.js", "transaction/index.js", "transaction/collection.js", "transaction/document.js", "const/code.js", "transaction/query.js"], "names": [], "mappings": ";;;;;;;AAAA;AACA;AACA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ACHA,AFMA;ACFA,ACHA,AFMA;ACFA,ACHA,AFMA;ACFA,ACHA,AFMA,AGTA;AFOA,ACHA,AFMA,AGTA;AFOA,ACHA,AFMA,AGTA;ACFA,AHSA,ACHA,AFMA,AGTA;ACFA,AHSA,ACHA,AFMA,AGTA;ACFA,AHSA,ACHA,AFMA,AGTA;ACFA,AFMA,AFMA,AKfA,AFMA;ACFA,AFMA,AFMA,AKfA,AFMA;ACFA,AFMA,AFMA,AKfA,AFMA;ACFA,AFMA,AFMA,AMlBA,ADGA,AFMA;ACFA,AFMA,AFMA,AMlBA,ADGA,AFMA;ACFA,AFMA,AFMA,AMlBA,ADGA,AFMA;ACFA,AFMA,AKfA,APqBA,AMlBA,ADGA,AFMA;ACFA,AFMA,AKfA,APqBA,AMlBA,ADGA,AFMA;ACFA,AFMA,AKfA,APqBA,AMlBA,ADGA,AFMA;ACFA,AFMA,AKfA,APqBA,AMlBA,ADGA,AGTA,ALeA;ACFA,AFMA,AKfA,APqBA,AMlBA,ADGA,AGTA,ALeA;ACFA,AFMA,AKfA,APqBA,AMlBA,ADGA,AGTA,ALeA;ACFA,AFMA,AKfA,APqBA,AMlBA,ADGA,AGTA,ACHA,ANkBA;ACFA,AFMA,AKfA,APqBA,AMlBA,ADGA,AGTA,ACHA,ANkBA;ACFA,AFMA,AKfA,APqBA,AMlBA,ADGA,AGTA,ACHA,ANkBA;ACFA,AMlBA,ARwBA,AKfA,APqBA,AMlBA,ADGA,AGTA,ACHA,ANkBA;ACFA,AMlBA,ARwBA,AKfA,APqBA,AMlBA,ADGA,AGTA,ACHA,ANkBA;ACFA,AMlBA,ARwBA,AKfA,APqBA,AMlBA,ADGA,AGTA,ACHA,ANkBA;ACFA,AMlBA,ARwBA,AS3BA,AJYA,APqBA,AMlBA,ADGA,AGTA,ACHA,ANkBA;ACFA,AMlBA,ARwBA,AS3BA,AJYA,APqBA,AMlBA,ADGA,AGTA,ACHA,ANkBA;ACFA,AMlBA,ARwBA,AS3BA,AJYA,APqBA,AMlBA,ADGA,AGTA,ACHA,ANkBA;ACFA,AMlBA,AENA,AV8BA,AS3BA,AJYA,APqBA,AMlBA,ADGA,AGTA,ACHA,ANkBA;ACFA,AMlBA,AENA,AV8BA,AS3BA,AJYA,APqBA,AMlBA,ADGA,AGTA,ACHA,ANkBA;ACFA,AMlBA,AENA,AV8BA,AS3BA,AJYA,APqBA,AMlBA,ADGA,AGTA,ACHA,ANkBA;ACFA,AMlBA,AGTA,ADGA,AV8BA,AS3BA,AJYA,APqBA,AMlBA,ADGA,AGTA,ACHA,ANkBA;ACFA,AMlBA,AGTA,ADGA,AV8BA,AS3BA,AJYA,APqBA,AMlBA,ADGA,AGTA,ACHA,ANkBA;ACFA,AMlBA,AGTA,ADGA,AV8BA,AS3BA,AXiCA,AMlBA,ADGA,AGTA,ACHA,ANkBA;ACFA,AMlBA,AGTA,ADGA,AENA,AZoCA,AS3BA,AXiCA,AKfA,AGTA,ACHA,ANkBA;ACFA,AMlBA,AGTA,ADGA,AENA,AZoCA,AS3BA,AXiCA,AKfA,AGTA,ACHA,ANkBA;ACFA,AMlBA,AGTA,ADGA,AENA,AZoCA,AS3BA,AXiCA,AKfA,AGTA,ACHA,ANkBA;AYnCA,AXiCA,AMlBA,AGTA,ADGA,AENA,AZoCA,AS3BA,AXiCA,AKfA,AGTA,ACHA,ANkBA;AYnCA,AXiCA,AMlBA,AGTA,ADGA,AENA,AZoCA,AS3BA,ANkBA,AGTA,ACHA,ANkBA;AYnCA,AXiCA,AMlBA,AGTA,ADGA,AENA,AZoCA,AS3BA,ANkBA,AGTA,ACHA,ANkBA;AYnCA,AXiCA,AYpCA,ANkBA,AGTA,ADGA,AENA,AZoCA,AS3BA,ANkBA,AGTA,ACHA,ANkBA;AYnCA,AXiCA,AYpCA,ANkBA,AGTA,ADGA,AENA,AZoCA,AS3BA,ANkBA,AGTA,ACHA,ANkBA;AYnCA,AXiCA,AYpCA,ANkBA,AGTA,ADGA,AENA,AHSA,ANkBA,AGTA,ALeA;AYnCA,AXiCA,AYpCA,ANkBA,AGTA,ADGA,AENA,AHSA,AMlBA,AZoCA,AGTA,ALeA;AYnCA,AXiCA,AYpCA,ANkBA,AGTA,ADGA,AENA,AHSA,AMlBA,AZoCA,AGTA,ALeA;AYnCA,AXiCA,AYpCA,ANkBA,AGTA,ADGA,AENA,AHSA,AMlBA,AZoCA,AGTA,ALeA;AYnCA,AXiCA,AYpCA,ANkBA,AGTA,ADGA,AENA,AHSA,AMlBA,ACHA,AbuCA,AGTA,ALeA;AYnCA,AXiCA,AYpCA,ANkBA,AGTA,ADGA,AENA,AHSA,AMlBA,ACHA,AbuCA,AGTA,ALeA;AYnCA,AXiCA,AYpCA,ANkBA,AGTA,ADGA,AENA,AHSA,AMlBA,ACHA,AbuCA,AGTA,ALeA;AYnCA,AIZA,Af6CA,AYpCA,ANkBA,AGTA,ADGA,AENA,AHSA,AMlBA,ACHA,AbuCA,AGTA,ALeA;AYnCA,AIZA,Af6CA,AYpCA,ANkBA,AGTA,ADGA,AENA,AHSA,AMlBA,ACHA,AbuCA,AGTA,ALeA;AYnCA,AIZA,Af6CA,AYpCA,ANkBA,AGTA,ADGA,AENA,AHSA,AMlBA,ACHA,AbuCA,AGTA,ALeA;AYnCA,AIZA,Af6CA,AYpCA,ANkBA,AGTA,ADGA,AENA,AHSA,AMlBA,AGTA,AFMA,AbuCA,AGTA,ALeA;AYnCA,AIZA,Af6CA,AYpCA,ANkBA,AGTA,ADGA,AENA,AHSA,AMlBA,AGTA,AFMA,AbuCA,AGTA,ALeA;AYnCA,AIZA,Af6CA,AYpCA,ANkBA,AGTA,ADGA,AENA,AHSA,AMlBA,AGTA,AFMA,AbuCA,AGTA,ALeA;AYnCA,AMlBA,AFMA,Af6CA,AYpCA,ANkBA,AGTA,ADGA,AENA,AHSA,AMlBA,AGTA,AFMA,AbuCA,AGTA,ALeA;AYnCA,AMlBA,AFMA,Af6CA,AYpCA,ANkBA,AGTA,ADGA,AENA,AHSA,AMlBA,AGTA,AFMA,AbuCA,AFMA;AYnCA,AMlBA,AFMA,Af6CA,AYpCA,ANkBA,AGTA,ADGA,AENA,AHSA,AMlBA,AGTA,AFMA,AbuCA,AFMA;AYnCA,AOrBA,ADGA,AFMA,Af6CA,AYpCA,ANkBA,AGTA,ADGA,AENA,AHSA,AMlBA,AGTA,AFMA,AbuCA;AU7BA,AOrBA,ADGA,AFMA,Af6CA,AYpCA,ANkBA,AGTA,ADGA,AENA,AHSA,AMlBA,AGTA,AFMA,AbuCA;AU7BA,AOrBA,ADGA,AFMA,Af6CA,AYpCA,ANkBA,AGTA,ADGA,AENA,AHSA,AMlBA,AGTA,AFMA,AbuCA;AU7BA,AOrBA,ADGA,AFMA,Af6CA,AYpCA,ANkBA,AGTA,ADGA,AENA,AHSA,AMlBA,AGTA,AGTA,ALeA,AbuCA;AU7BA,AOrBA,ADGA,AFMA,Af6CA,AYpCA,ANkBA,AGTA,ADGA,AENA,AHSA,AMlBA,AGTA,AGTA,ALeA,AbuCA;AU7BA,AOrBA,ADGA,AFMA,Af6CA,AYpCA,ANkBA,AGTA,ADGA,AENA,AHSA,AMlBA,AGTA,AGTA,ALeA,AbuCA;AU7BA,AOrBA,ADGA,AFMA,Af6CA,AYpCA,ANkBA,AGTA,ADGA,AENA,AHSA,AMlBA,AGTA,AGTA,ACHA,ANkBA,AbuCA;AiBlDA,ADGA,AFMA,Af6CA,AYpCA,ANkBA,AGTA,ADGA,AENA,AHSA,AMlBA,AGTA,AGTA,ACHA,ANkBA,AbuCA;AiBlDA,ADGA,AFMA,Af6CA,AYpCA,ANkBA,AGTA,ADGA,AENA,AHSA,AMlBA,AGTA,AGTA,ACHA,ANkBA,AbuCA;AiBlDA,ADGA,AFMA,Af6CA,AYpCA,ANkBA,AGTA,ADGA,AENA,AHSA,AMlBA,AGTA,AKfA,AFMA,ACHA,ANkBA,AbuCA;AiBlDA,ADGA,AFMA,Af6CA,AYpCA,ANkBA,AGTA,ADGA,AENA,AHSA,AMlBA,AGTA,AKfA,AFMA,ACHA,ANkBA,AbuCA;AiBlDA,ADGA,AFMA,Af6CA,AYpCA,ANkBA,AGTA,ADGA,AENA,AHSA,AS3BA,AKfA,AFMA,ACHA,ANkBA,AbuCA;AiBlDA,ADGA,AFMA,Af6CA,AYpCA,ANkBA,AGTA,ADGA,AENA,AHSA,AS3BA,AMlBA,ADGA,AFMA,ACHA,ANkBA,AbuCA;AiBlDA,ADGA,AFMA,Af6CA,AYpCA,ANkBA,AGTA,ADGA,AENA,AHSA,AS3BA,AMlBA,ADGA,AFMA,ACHA,ANkBA,AbuCA;AiBlDA,ADGA,AFMA,Af6CA,AYpCA,ANkBA,AGTA,ADGA,AENA,AHSA,AS3BA,AMlBA,ADGA,AFMA,ACHA,ANkBA,AbuCA;AiBlDA,ADGA,AFMA,Af6CA,AYpCA,ANkBA,AGTA,ADGA,AENA,AHSA,AS3BA,AOrBA,ADGA,ADGA,AFMA,ACHA,ANkBA,AbuCA;AiBlDA,ADGA,AFMA,Af6CA,AYpCA,ANkBA,AGTA,ADGA,AENA,AHSA,AS3BA,AOrBA,ADGA,ADGA,AFMA,ACHA,ANkBA,AbuCA;AiBlDA,ADGA,AFMA,Af6CA,AYpCA,ANkBA,AGTA,ADGA,AENA,AHSA,AS3BA,AOrBA,ADGA,ADGA,AFMA,ACHA,ANkBA,AbuCA;AiBlDA,ADGA,AFMA,Af6CA,AYpCA,ANkBA,AGTA,ADGA,AENA,AHSA,AgBhDA,ADGA,ADGA,AFMA,ACHA,ANkBA,AbuCA,AuBrEA;ANmBA,ADGA,AFMA,Af6CA,AYpCA,ANkBA,AGTA,ADGA,AENA,AHSA,AgBhDA,ADGA,ADGA,AFMA,ACHA,ANkBA,AbuCA,AuBrEA;ANmBA,ADGA,AFMA,AHSA,ANkBA,AGTA,ADGA,AENA,AHSA,AgBhDA,ADGA,ADGA,AFMA,ACHA,ANkBA,AbuCA,AuBrEA;ANmBA,ADGA,AFMA,AU9BA,AbuCA,ANkBA,AGTA,ACHA,AHSA,AgBhDA,ADGA,ADGA,AFMA,ACHA,ANkBA,AbuCA,AuBrEA;ANmBA,ADGA,AFMA,AU9BA,AbuCA,AHSA,ACHA,AHSA,AgBhDA,ADGA,ADGA,AFMA,ACHA,ANkBA,AbuCA,AuBrEA;ANmBA,ADGA,AFMA,AU9BA,AbuCA,AHSA,ACHA,AHSA,AgBhDA,ADGA,ADGA,AFMA,ACHA,ANkBA,AbuCA,AuBrEA;ANmBA,ADGA,AFMA,AU9BA,AbuCA,AHSA,ACHA,AHSA,AgBhDA,ADGA,ADGA,AFMA,ACHA,ANkBA,AbuCA,AuBrEA,AENA;ARyBA,ADGA,AFMA,AU9BA,AbuCA,AHSA,ACHA,AHSA,AgBhDA,ADGA,ADGA,AFMA,ACHA,ANkBA,AbuCA,AuBrEA,AENA;ARyBA,ADGA,AFMA,AU9BA,AbuCA,AHSA,ACHA,AHSA,Ae7CA,ADGA,AFMA,ACHA,ANkBA,AbuCA,AuBrEA,AENA;ARyBA,ADGA,AFMA,AU9BA,AbuCA,AHSA,ACHA,AHSA,AoB5DA,ALeA,ADGA,AFMA,ACHA,ANkBA,AbuCA,AuBrEA,AENA;ARyBA,ADGA,AFMA,AU9BA,AbuCA,AHSA,ACHA,AHSA,AoB5DA,ALeA,ADGA,AFMA,ACHA,ANkBA,AbuCA,AuBrEA,AENA;ARyBA,ADGA,AFMA,AU9BA,AbuCA,AHSA,ACHA,AHSA,AoB5DA,ALeA,ADGA,AFMA,ACHA,ANkBA,AbuCA,AuBrEA,AENA;ARyBA,ADGA,AFMA,AU9BA,AbuCA,AFMA,AHSA,AoB5DA,ACHA,ANkBA,ADGA,AFMA,ACHA,ANkBA,AbuCA,AuBrEA,AENA;ARyBA,ADGA,AFMA,AU9BA,AbuCA,AFMA,AHSA,AoB5DA,ACHA,ANkBA,ADGA,AFMA,ACHA,ANkBA,AbuCA,AuBrEA,AENA;ARyBA,ADGA,AFMA,AU9BA,AbuCA,AFMA,AHSA,AoB5DA,ACHA,ANkBA,ADGA,AFMA,ACHA,ANkBA,AbuCA,AuBrEA,AENA;ARyBA,ADGA,AFMA,AU9BA,AbuCA,AFMA,AHSA,AsBlEA,AFMA,ACHA,ANkBA,ADGA,AFMA,ACHA,ANkBA,AbuCA,AuBrEA,AENA;ARyBA,ADGA,AFMA,AU9BA,AbuCA,AFMA,AHSA,AsBlEA,AFMA,ACHA,ANkBA,ADGA,AFMA,ACHA,ANkBA,AbuCA,AuBrEA,AENA;ARyBA,ADGA,AFMA,AU9BA,AbuCA,AFMA,AHSA,AsBlEA,AFMA,ACHA,ANkBA,ADGA,AFMA,ACHA,ANkBA,AbuCA,AuBrEA;ANmBA,ADGA,AFMA,AU9BA,AbuCA,AFMA,AHSA,AsBlEA,AFMA,ACHA,ANkBA,ADGA,AS3BA,AXiCA,ACHA,ANkBA,AbuCA,AuBrEA;ANmBA,ADGA,AFMA,AHSA,AFMA,AHSA,AsBlEA,AFMA,ACHA,ANkBA,ADGA,AS3BA,AXiCA,ACHA,ANkBA,AbuCA,AuBrEA;ANmBA,ADGA,AFMA,AHSA,AFMA,AmBzDA,AFMA,ACHA,ANkBA,ADGA,AS3BA,AXiCA,ACHA,ANkBA,AbuCA,AuBrEA;ANmBA,ADGA,AFMA,AHSA,AmBzDA,AFMA,AFMA,ACHA,ANkBA,ADGA,AS3BA,AXiCA,ACHA,ANkBA,AbuCA,AuBrEA;ANmBA,ADGA,AFMA,AHSA,AmBzDA,AFMA,AFMA,ACHA,ANkBA,ADGA,AS3BA,AXiCA,ACHA,ANkBA,AbuCA,AuBrEA;ANmBA,ADGA,ALeA,AmBzDA,AFMA,AFMA,ACHA,ANkBA,ADGA,AS3BA,AXiCA,ACHA,ANkBA,AbuCA,AuBrEA;ANmBA,ADGA,ALeA,AmBzDA,AFMA,AFMA,ACHA,ANkBA,ADGA,AS3BA,AXiCA,ACHA,AYpCA,AlBsDA,AbuCA,AuBrEA;ANmBA,ADGA,ALeA,AmBzDA,AFMA,AFMA,ACHA,ANkBA,ADGA,AS3BA,AXiCA,ACHA,AYpCA,AlBsDA,AbuCA,AuBrEA;ANmBA,ADGA,ALeA,AmBzDA,AFMA,AFMA,ACHA,ANkBA,ADGA,AS3BA,AXiCA,ACHA,AYpCA,AlBsDA,AbuCA,AuBrEA;AS1BA,Af6CA,ADGA,ALeA,AmBzDA,AHSA,ANkBA,ADGA,AS3BA,AXiCA,ACHA,AYpCA,AlBsDA,AbuCA,AuBrEA;AS1BA,Af6CA,ADGA,ALeA,AmBzDA,AHSA,ANkBA,ADGA,AS3BA,AXiCA,ACHA,AYpCA,AlBsDA,AbuCA,AuBrEA;AS1BA,Af6CA,ADGA,ALeA,AmBzDA,AHSA,ANkBA,ADGA,AS3BA,AXiCA,ACHA,AYpCA,AlBsDA,AbuCA,AuBrEA;AS1BA,ACHA,AhBgDA,ADGA,ALeA,AmBzDA,AHSA,ANkBA,ADGA,AS3BA,AXiCA,ACHA,AYpCA,AlBsDA,AbuCA,AuBrEA;AS1BA,ACHA,AhBgDA,ADGA,ALeA,AmBzDA,AHSA,ANkBA,ADGA,AS3BA,AXiCA,ACHA,AYpCA,AlBsDA,AbuCA,AuBrEA;AS1BA,ACHA,AhBgDA,ADGA,ALeA,AmBzDA,AHSA,ANkBA,ADGA,AS3BA,AXiCA,ACHA,AYpCA,AlBsDA,AbuCA,AuBrEA;AS1BA,ACHA,AhBgDA,ADGA,ALeA,AmBzDA,AHSA,ANkBA,ADGA,AS3BA,AKfA,AhBgDA,ACHA,AYpCA,AlBsDA,AbuCA,AuBrEA;AS1BA,ACHA,AhBgDA,ADGA,ALeA,AmBzDA,AHSA,ANkBA,ADGA,AS3BA,AKfA,AhBgDA,ACHA,AYpCA,AlBsDA,AbuCA,AuBrEA;AS1BA,ACHA,AhBgDA,ADGA,ALeA,AmBzDA,AHSA,ANkBA,ADGA,AS3BA,AKfA,AhBgDA,ACHA,AYpCA,AlBsDA,AbuCA,AuBrEA;AS1BA,ACHA,AhBgDA,ADGA,ALeA,AmBzDA,AHSA,ANkBA,ADGA,AS3BA,AKfA,AhBgDA,ACHA,AYpCA,AlBsDA,AsBlEA,AnCyGA,AuBrEA;AS1BA,ACHA,AhBgDA,ADGA,ALeA,AmBzDA,AHSA,ANkBA,ADGA,AS3BA,AKfA,AhBgDA,ACHA,AYpCA,AlBsDA,AsBlEA,AnCyGA,AuBrEA;AS1BA,ACHA,AhBgDA,ADGA,ALeA,AmBzDA,AHSA,ANkBA,ADGA,AS3BA,AKfA,AhBgDA,ACHA,AYpCA,AlBsDA,AsBlEA,AnCyGA,AuBrEA;AS1BA,ACHA,AhBgDA,ADGA,ALeA,AmBzDA,AHSA,ANkBA,ADGA,AS3BA,AKfA,AhBgDA,ACHA,AYpCA,AlBsDA,AuBrEA,ADGA,AnCyGA,AuBrEA;AS1BA,ACHA,AhBgDA,ADGA,ALeA,AmBzDA,AHSA,ANkBA,ADGA,AS3BA,AKfA,AhBgDA,ACHA,AYpCA,AlBsDA,AuBrEA,ADGA,AnCyGA,AuBrEA;AS1BA,ACHA,AhBgDA,ADGA,ALeA,AmBzDA,AHSA,ANkBA,ADGA,AS3BA,AKfA,AhBgDA,ACHA,AYpCA,AlBsDA,AuBrEA,ADGA,AnCyGA,AuBrEA;AS1BA,ACHA,AhBgDA,ADGA,ALeA,AmBzDA,AHSA,ANkBA,ADGA,AS3BA,AKfA,AhBgDA,ACHA,AYpCA,AlBsDA,AuBrEA,ACHA,AFMA,AnCyGA,AuBrEA;AS1BA,ACHA,AhBgDA,ADGA,ALeA,AmBzDA,AHSA,ANkBA,ADGA,AS3BA,AKfA,AhBgDA,ACHA,AYpCA,AlBsDA,AuBrEA,ACHA,AFMA,AnCyGA,AuBrEA;AS1BA,ACHA,AhBgDA,ADGA,ALeA,AmBzDA,AHSA,ANkBA,ADGA,AS3BA,AKfA,AhBgDA,ACHA,AYpCA,AlBsDA,AuBrEA,ACHA,AFMA,AnCyGA,AuBrEA;AS1BA,ACHA,AhBgDA,ADGA,AsBlEA,A3BiFA,AmBzDA,AHSA,ANkBA,ADGA,AS3BA,AKfA,AhBgDA,ACHA,AYpCA,AlBsDA,AuBrEA,ACHA,AFMA,AnCyGA,AuBrEA;AS1BA,ACHA,AhBgDA,ADGA,AsBlEA,A3BiFA,AmBzDA,AHSA,ANkBA,ADGA,AS3BA,AKfA,AhBgDA,ACHA,AYpCA,AlBsDA,AuBrEA,ACHA,AFMA,AZoCA;AS1BA,ACHA,AhBgDA,ADGA,AsBlEA,A3BiFA,AmBzDA,AHSA,ANkBA,ADGA,AS3BA,AKfA,AhBgDA,ACHA,AYpCA,AlBsDA,AuBrEA,ACHA,AFMA,AZoCA;AS1BA,ACHA,AhBgDA,ADGA,AsBlEA,A3BiFA,AmBzDA,AHSA,ANkBA,ADGA,AS3BA,AKfA,AhBgDA,ACHA,AYpCA,AlBsDA,AuBrEA,ACHA,AFMA,AIZA,AhBgDA;AS1BA,ACHA,AhBgDA,ADGA,AsBlEA,A3BiFA,AmBzDA,AHSA,ANkBA,ADGA,AS3BA,AKfA,AhBgDA,ACHA,AYpCA,AlBsDA,AuBrEA,ACHA,AFMA,AIZA,AhBgDA;AS1BA,ACHA,AhBgDA,ADGA,AsBlEA,A3BiFA,AmBzDA,AHSA,ANkBA,ADGA,AS3BA,AKfA,AhBgDA,ACHA,AYpCA,AlBsDA,AuBrEA,ACHA,AFMA,AIZA,AhBgDA;AS1BA,ACHA,AhBgDA,ADGA,AsBlEA,A3BiFA,AmBzDA,AHSA,ANkBA,ADGA,AS3BA,AKfA,AhBgDA,ACHA,AYpCA,AlBsDA,AuBrEA,ACHA,AFMA,AIZA,AhBgDA;AS1BA,ACHA,AhBgDA,ADGA,AsBlEA,A3BiFA,AmBzDA,AHSA,ANkBA,ADGA,AS3BA,AKfA,AhBgDA,ACHA,AYpCA,AlBsDA,AuBrEA,ACHA,AFMA,AIZA,AhBgDA;AS1BA,ACHA,AhBgDA,ADGA,AsBlEA,A3BiFA,AmBzDA,AHSA,ANkBA,ADGA,AS3BA,AKfA,AhBgDA,ACHA,AYpCA,AlBsDA,AuBrEA,ACHA,AFMA,AIZA,AhBgDA;AS1BA,ACHA,AhBgDA,ADGA,AsBlEA,A3BiFA,AmBzDA,AHSA,ANkBA,ADGA,AS3BA,AKfA,AhBgDA,ACHA,AYpCA,AlBsDA,AuBrEA,ACHA,AFMA,AIZA,AhBgDA;AS1BA,ACHA,AhBgDA,ADGA,AsBlEA,A3BiFA,AmBzDA,AT2BA,ADGA,AS3BA,AKfA,AhBgDA,ACHA,AYpCA,AlBsDA,AuBrEA,ACHA,AFMA,AIZA,AhBgDA;AS1BA,ACHA,AhBgDA,ADGA,AsBlEA,A3BiFA,AmBzDA,AT2BA,ADGA,AS3BA,AKfA,AhBgDA,ACHA,AYpCA,AlBsDA,AuBrEA,ACHA,AFMA,AIZA;APsBA,ACHA,AhBgDA,ADGA,AsBlEA,A3BiFA,AmBzDA,AT2BA,ADGA,AS3BA,AKfA,AhBgDA,ACHA,AYpCA,AlBsDA,AuBrEA,ACHA,AFMA,AIZA;APsBA,ACHA,AhBgDA,ADGA,AsBlEA,A3BiFA,AmBzDA,AT2BA,ADGA,AS3BA,AKfA,AhBgDA,ACHA,AYpCA,AlBsDA,AuBrEA,ACHA,AFMA;AHUA,ACHA,AhBgDA,ADGA,AsBlEA,A3BiFA,AmBzDA,AT2BA,ADGA,AS3BA,AKfA,AhBgDA,ACHA,AYpCA,AlBsDA,AuBrEA,ACHA,AFMA;AHUA,ACHA,AhBgDA,ADGA,AsBlEA,A3BiFA,AmBzDA,AT2BA,ADGA,AS3BA,AXiCA,ACHA,AYpCA,AlBsDA,AuBrEA,ACHA,AFMA;AHUA,ACHA,AhBgDA,ADGA,AsBlEA,A3BiFA,AmBzDA,AT2BA,ADGA,AS3BA,AXiCA,ACHA,AYpCA,AlBsDA,AuBrEA,ACHA,AFMA;AHUA,ACHA,AjBmDA,AsBlEA,A3BiFA,AmBzDA,AT2BA,ADGA,AS3BA,AXiCA,ACHA,AYpCA,AlBsDA,AuBrEA,ACHA,AFMA;AHUA,ACHA,AjBmDA,AsBlEA,A3BiFA,AmBzDA,AT2BA,ADGA,AS3BA,AXiCA,ACHA,AYpCA,AlBsDA,AuBrEA,ACHA,AFMA;AHUA,ACHA,AjBmDA,AsBlEA,A3BiFA,AmBzDA,AT2BA,ADGA,AS3BA,AXiCA,ACHA,AYpCA,AlBsDA,AuBrEA,ACHA,AFMA;AHUA,ACHA,AjBmDA,AsBlEA,A3BiFA,AmBzDA,AT2BA,ADGA,AS3BA,AXiCA,ACHA,AYpCA,AlBsDA,AuBrEA,ACHA,AFMA;AHUA,ACHA,AjBmDA,AsBlEA,A3BiFA,AmBzDA,AT2BA,ADGA,AS3BA,AXiCA,ACHA,AYpCA,AlBsDA,AuBrEA,ACHA,AFMA;AHUA,ACHA,AjBmDA,AsBlEA,A3BiFA,AmBzDA,AT2BA,ADGA,AS3BA,AXiCA,ACHA,AYpCA,AlBsDA,AwBxEA,AFMA;AHUA,ACHA,AjBmDA,AsBlEA,A3BiFA,AmBzDA,AT2BA,ADGA,AS3BA,AXiCA,ACHA,AYpCA,AlBsDA,AwBxEA,AFMA;AHUA,ACHA,AjBmDA,AsBlEA,A3BiFA,AmBzDA,AT2BA,ADGA,AS3BA,AXiCA,ACHA,AYpCA,AlBsDA,AwBxEA,AFMA;AHUA,ACHA,AjBmDA,AsBlEA,A3BiFA,AmBzDA,AT2BA,ADGA,AS3BA,AXiCA,ACHA,AYpCA,AlBsDA,AwBxEA,AFMA;AHUA,ACHA,AjBmDA,AsBlEA,A3BiFA,AmBzDA,AT2BA,ADGA,AS3BA,AXiCA,ACHA,AYpCA,AlBsDA,AwBxEA,AFMA;AHUA,ACHA,AjBmDA,AsBlEA,A3BiFA,AmBzDA,AT2BA,ADGA,AS3BA,AXiCA,ACHA,AYpCA,AlBsDA,AwBxEA,AFMA;AHUA,ACHA,AjBmDA,AsBlEA,A3BiFA,AmBzDA,AT2BA,ADGA,AS3BA,AXiCA,ACHA,AYpCA,AlBsDA,AwBxEA,AFMA;AHUA,ACHA,AjBmDA,AsBlEA,A3BiFA,AmBzDA,AT2BA,ADGA,AS3BA,AXiCA,ACHA,AYpCA,AlBsDA,AwBxEA,AFMA;AHUA,ACHA,AjBmDA,AsBlEA,A3BiFA,AmBzDA,AT2BA,ADGA,AS3BA,AXiCA,ACHA,AYpCA,AlBsDA,AwBxEA,AFMA;AHUA,ACHA,AjBmDA,AsBlEA,A3BiFA,AmBzDA,AT2BA,ADGA,AS3BA,AXiCA,ACHA,AYpCA,AlBsDA,AwBxEA,AFMA;AHUA,ACHA,AjBmDA,AsBlEA,A3BiFA,AmBzDA,AT2BA,ADGA,AS3BA,AXiCA,ACHA,AYpCA,AlBsDA,AwBxEA,AFMA;AHUA,ACHA,AjBmDA,ALeA,AmBzDA,AT2BA,ADGA,AS3BA,AV8BA,AYpCA,AlBsDA,AwBxEA,AFMA;AHUA,ACHA,AjBmDA,ALeA,AmBzDA,AT2BA,ADGA,AS3BA,AV8BA,AYpCA,AlBsDA,AwBxEA,AFMA;AHUA,ACHA,AjBmDA,ALeA,AmBzDA,AT2BA,ADGA,AS3BA,AV8BA,AYpCA,AlBsDA,AwBxEA,AFMA;AHUA,ACHA,AjBmDA,ALeA,AmBzDA,AT2BA,ADGA,AS3BA,AV8BA,AYpCA,AlBsDA,AwBxEA,AFMA;AHUA,ACHA,AjBmDA,ALeA,AmBzDA,AT2BA,ADGA,AS3BA,AV8BA,AYpCA,AlBsDA,AwBxEA,AFMA;AHUA,ACHA,AjBmDA,ALeA,AmBzDA,AT2BA,ADGA,AS3BA,AV8BA,AYpCA,AlBsDA,AwBxEA,AFMA;AHUA,ACHA,AjBmDA,ALeA,AmBzDA,AT2BA,ADGA,AS3BA,AV8BA,AYpCA,AlBsDA,AwBxEA,AFMA;AHUA,ACHA,AjBmDA,ALeA,AmBzDA,AT2BA,ADGA,AS3BA,AV8BA,AYpCA,AlBsDA,AwBxEA,AFMA;AHUA,ACHA,AjBmDA,ALeA,AmBzDA,AT2BA,ADGA,AS3BA,AENA,AlBsDA,AwBxEA,AFMA;AHUA,ACHA,AjBmDA,ALeA,AmBzDA,AT2BA,ADGA,AS3BA,AENA,AlBsDA,AwBxEA,AFMA;AHUA,ACHA,AjBmDA,ALeA,AmBzDA,AT2BA,ADGA,AS3BA,AENA,AlBsDA,AwBxEA,AFMA;AHUA,ACHA,AjBmDA,ALeA,AmBzDA,AT2BA,ADGA,AS3BA,AENA,AlBsDA,AwBxEA,AFMA;AHUA,ACHA,AjBmDA,ALeA,AmBzDA,AT2BA,ADGA,AS3BA,AENA,AlBsDA,AwBxEA,AFMA;AHUA,ACHA,AjBmDA,ALeA,AmBzDA,AT2BA,ADGA,AS3BA,AENA,AlBsDA,AwBxEA,AFMA;AHUA,ACHA,AjBmDA,ALeA,AmBzDA,AT2BA,ADGA,AS3BA,AENA,AlBsDA,AwBxEA,AFMA;AHUA,ACHA,AjBmDA,ALeA,AmBzDA,AT2BA,ADGA,AS3BA,AENA,AlBsDA,AwBxEA,AFMA;AHUA,ACHA,AjBmDA,ALeA,AmBzDA,AT2BA,ADGA,AS3BA,AENA,AlBsDA,AwBxEA,AFMA;AHUA,ACHA,AjBmDA,ALeA,AmBzDA,AT2BA,ADGA,AS3BA,AENA,AlBsDA,AwBxEA,AFMA;AHUA,ACHA,AjBmDA,ALeA,AmBzDA,AT2BA,ADGA,AS3BA,AENA,AlBsDA,AwBxEA,AFMA;AHUA,ACHA,AjBmDA,ALeA,AmBzDA,AT2BA,ADGA,AS3BA,AENA,AlBsDA,AwBxEA,AFMA;AHUA,ACHA,AjBmDA,ALeA,AmBzDA,AT2BA,ADGA,AS3BA,AENA,AlBsDA,AwBxEA,AFMA;AHUA,ACHA,AjBmDA,ALeA,AmBzDA,AT2BA,ADGA,AS3BA,AENA,AlBsDA,AwBxEA,AFMA;AHUA,ACHA,AjBmDA,ALeA,AmBzDA,AT2BA,ADGA,AS3BA,AENA,AlBsDA,AwBxEA,AFMA;AHUA,ACHA,AjBmDA,ALeA,AmBzDA,AT2BA,ADGA,AS3BA,AENA,AlBsDA,AwBxEA,AFMA;AHUA,ACHA,AjBmDA,ALeA,AmBzDA,AT2BA,ADGA,AS3BA,AENA,AlBsDA,AwBxEA,AFMA;AHUA,ACHA,AjBmDA,ALeA,AmBzDA,AT2BA,ADGA,AS3BA,AENA,AlBsDA,AwBxEA,AFMA;AHUA,ACHA,AjBmDA,ALeA,AmBzDA,AT2BA,ADGA,AS3BA,AENA,AlBsDA,AwBxEA,AFMA;AHUA,ACHA,AjBmDA,ALeA,AmBzDA,AT2BA,ADGA,AS3BA,AENA,AlBsDA,AwBxEA,AFMA;AHUA,ACHA,AjBmDA,ALeA,AmBzDA,AT2BA,ADGA,AS3BA,AENA,AlBsDA,AwBxEA,AFMA;AHUA,ACHA,AjBmDA,ALeA,AmBzDA,AT2BA,ADGA,AS3BA,AENA,AlBsDA,AwBxEA,AFMA;AHUA,ACHA,AjBmDA,ALeA,AmBzDA,AT2BA,ADGA,AS3BA,AENA,AMlBA,AFMA;AHUA,ACHA,AjBmDA,ALeA,AmBzDA,AT2BA,ADGA,AS3BA,AENA,AMlBA,AFMA;AHUA,ACHA,AjBmDA,ALeA,AmBzDA,AT2BA,ADGA,AS3BA,AENA,AMlBA,AFMA;AHUA,ACHA,AjBmDA,ALeA,AmBzDA,AT2BA,ADGA,AS3BA,AENA,AMlBA,AFMA;AHUA,ACHA,AjBmDA,ALeA,AmBzDA,AT2BA,ADGA,AS3BA,AENA,AMlBA,AFMA;AHUA,ACHA,AjBmDA,ALeA,AmBzDA,AT2BA,ADGA,AS3BA,AENA,AMlBA,AFMA;AHUA,ACHA,AjBmDA,ALeA,AmBzDA,AT2BA,ADGA,AS3BA,AENA,AMlBA,AFMA;AHUA,ACHA,AjBmDA,ALeA,AmBzDA,AT2BA,ADGA,AS3BA,AENA,AMlBA,AFMA;AHUA,ACHA,AjBmDA,ALeA,AmBzDA,AT2BA,ADGA,AS3BA,AENA,AMlBA,AFMA;AHUA,ACHA,AjBmDA,ALeA,AmBzDA,AT2BA,ADGA,AS3BA,AENA,AMlBA,AFMA;AHUA,ACHA,AjBmDA,ALeA,AmBzDA,AT2BA,ADGA,AS3BA,AENA,AMlBA,AFMA;AHUA,ACHA,AjBmDA,ALeA,AmBzDA,AT2BA,ADGA,AS3BA,AENA,AMlBA,AFMA;AHUA,ACHA,AtBkEA,AmBzDA,AT2BA,ADGA,AS3BA,AENA,AMlBA,AFMA;AHUA,ACHA,AtBkEA,AmBzDA,AT2BA,ADGA,AS3BA,AENA,AMlBA,AFMA;AHUA,ACHA,AtBkEA,AmBzDA,AT2BA,ADGA,AS3BA,AENA,AMlBA,AFMA;AHUA,ACHA,AtBkEA,AmBzDA,AT2BA,ADGA,AS3BA,AENA,AMlBA,AFMA;AHUA,ACHA,AtBkEA,AmBzDA,AT2BA,ADGA,AS3BA,AENA,AMlBA,AFMA;AFOA,AtBkEA,AmBzDA,AT2BA,ADGA,AS3BA,AENA,AMlBA,AFMA;AFOA,AtBkEA,AmBzDA,AT2BA,ADGA,AS3BA,AENA,AMlBA,AFMA;AFOA,AtBkEA,AmBzDA,AT2BA,ADGA,AS3BA,AENA,AMlBA,AFMA;AFOA,AtBkEA,AmBzDA,AT2BA,ADGA,AS3BA,AENA,AMlBA,AFMA;AFOA,AtBkEA,AmBzDA,AT2BA,ADGA,AS3BA,AENA,AMlBA,AFMA;AFOA,AtBkEA,AmBzDA,AT2BA,ADGA,AS3BA,AENA,AMlBA,AFMA;AFOA,AtBkEA,AmBzDA,AT2BA,ADGA,AS3BA,AENA,AMlBA,AFMA;AFOA,AtBkEA,AmBzDA,AT2BA,ADGA,AS3BA,AENA,AMlBA,AFMA;AFOA,AtBkEA,AmBzDA,AT2BA,ADGA,AS3BA,AENA,AMlBA,AFMA;AFOA,AtBkEA,AmBzDA,AT2BA,ADGA,AS3BA,AENA,AMlBA,AFMA;AFOA,AtBkEA,AmBzDA,AT2BA,ADGA,AS3BA,AENA,AMlBA,AFMA;AFOA,AtBkEA,AmBzDA,AT2BA,ADGA,AS3BA,AENA,AMlBA,AFMA;AFOA,AtBkEA,AmBzDA,AT2BA,ADGA,AS3BA,AENA,AMlBA,AFMA;AFOA,AtBkEA,AmBzDA,AT2BA,ADGA,AS3BA,AENA,AMlBA,AFMA;AFOA,AtBkEA,AmBzDA,AT2BA,ADGA,AS3BA,AENA,AMlBA,AFMA;AFOA,AtBkEA,AmBzDA,AT2BA,ADGA,AS3BA,AENA,AMlBA,AFMA;AFOA,AtBkEA,AmBzDA,AT2BA,ADGA,AS3BA,AENA,AMlBA,AFMA;AFOA,AtBkEA,AmBzDA,AT2BA,ADGA,AS3BA,AENA,AMlBA;AJaA,AtBkEA,AmBzDA,AT2BA,ADGA,AS3BA,AENA,AMlBA;AJaA,AtBkEA,AmBzDA,AT2BA,ADGA,AS3BA,AENA,AMlBA;AJaA,AtBkEA,AmBzDA,AT2BA,ADGA,AS3BA,AENA,AMlBA;AJaA,AtBkEA,AmBzDA,AT2BA,ADGA,AS3BA,AENA,AMlBA;AJaA,AtBkEA,AmBzDA,AT2BA,ADGA,AS3BA,AENA,AMlBA;AJaA,AtBkEA,AmBzDA,AT2BA,ADGA,AS3BA,AENA,AMlBA;AJaA,AtBkEA,AmBzDA,AT2BA,ADGA,AS3BA,AENA;AELA,AtBkEA,AmBzDA,AT2BA,ADGA,AS3BA,AENA;AELA,AtBkEA,AmBzDA,AT2BA,ADGA,AS3BA,AENA;AELA,AtBkEA,AmBzDA,AT2BA,ADGA,AS3BA,AENA;AELA,AtBkEA,AmBzDA,AT2BA,ADGA,AS3BA,AENA;AELA,AtBkEA,AmBzDA,AT2BA,ADGA,AS3BA,AENA;AELA,AtBkEA,AmBzDA,AT2BA,ADGA,AS3BA,AENA;AELA,AtBkEA,AmBzDA,AT2BA,ADGA,AS3BA,AENA;AELA,AtBkEA,AmBzDA,AT2BA,ADGA,AS3BA,AENA;AELA,AtBkEA,AmBzDA,AT2BA,ADGA,AWjCA;AELA,AtBkEA,AmBzDA,AT2BA,ADGA,AWjCA;AELA,AtBkEA,AmBzDA,AT2BA,ADGA,AWjCA;AELA,AtBkEA,AmBzDA,AT2BA,ADGA,AWjCA;AELA,AtBkEA,AmBzDA,AT2BA,ADGA,AWjCA;AELA,AtBkEA,AmBzDA,AT2BA,ADGA,AWjCA;AELA,AtBkEA,AmBzDA,AT2BA,ADGA,AWjCA;AELA,AtBkEA,AmBzDA,AT2BA,ADGA,AWjCA;AELA,AtBkEA,AmBzDA,AT2BA,ADGA,AWjCA;AELA,AtBkEA,AmBzDA,AT2BA,ADGA,AWjCA;AELA,AtBkEA,AmBzDA,AT2BA,ADGA,AWjCA;AELA,AtBkEA,AmBzDA,AT2BA,ADGA,AWjCA;AELA,AtBkEA,AmBzDA,AT2BA,ADGA,AWjCA;AELA,AtBkEA,AmBzDA,AT2BA,ADGA,AWjCA;AELA,AtBkEA,AmBzDA,AT2BA,ADGA,AWjCA;AELA,AtBkEA,AmBzDA,AT2BA,ADGA,AWjCA;AELA,AtBkEA,AmBzDA,AT2BA,ADGA,AWjCA;AELA,AtBkEA,AmBzDA,AT2BA,ADGA,AWjCA;AELA,AtBkEA,AmBzDA,AT2BA,ADGA,AWjCA;AELA,AtBkEA,AmBzDA,AT2BA,ADGA,AWjCA;AELA,AtBkEA,AmBzDA,AT2BA,ADGA,AWjCA;AELA,AtBkEA,AmBzDA,AT2BA,ADGA,AWjCA;AELA,AtBkEA,AmBzDA,AT2BA,ADGA,AWjCA;AELA,AtBkEA,AmBzDA,AT2BA,ADGA,AWjCA;AELA,AtBkEA,AmBzDA,AT2BA,ADGA,AWjCA;AELA,AtBkEA,AmBzDA,AT2BA,ADGA,AWjCA;AELA,AtBkEA,AmBzDA,AT2BA,ADGA,AWjCA;AELA,AtBkEA,AmBzDA,AT2BA,ADGA,AWjCA;AELA,AtBkEA,AmBzDA,AT2BA,ADGA,AWjCA;AELA,AtBkEA,AmBzDA,AT2BA,ADGA,AWjCA;AELA,AtBkEA,AmBzDA,AT2BA,ADGA,AWjCA;AELA,AtBkEA,AmBzDA,AT2BA,ADGA,AWjCA;AELA,AtBkEA,AmBzDA,AT2BA,ADGA,AWjCA;AELA,AtBkEA,AmBzDA,AT2BA,ADGA,AWjCA;AELA,AtBkEA,AmBzDA,AT2BA,ADGA,AWjCA;AELA,AtBkEA,AmBzDA,AT2BA,ADGA,AWjCA;AELA,AtBkEA,AmBzDA,AT2BA,ADGA,AWjCA;AELA,AtBkEA,AmBzDA,AT2BA,ADGA,AWjCA;AELA,AtBkEA,AmBzDA,AT2BA,ADGA,AWjCA;AELA,AtBkEA,AmBzDA,AT2BA,ADGA,AWjCA;AELA,AtBkEA,AmBzDA,AT2BA,ADGA,AWjCA;AELA,AtBkEA,AmBzDA,AT2BA,ADGA,AWjCA;AELA,AtBkEA,AmBzDA,AT2BA,ADGA,AWjCA;AELA,AtBkEA,AmBzDA,AT2BA,ADGA,AWjCA;AELA,AtBkEA,AmBzDA,AT2BA,ADGA,AWjCA;AELA,AtBkEA,AmBzDA,AT2BA,ADGA,AWjCA;AELA,AtBkEA,AmBzDA,AT2BA,ADGA,AWjCA;AELA,AtBkEA,AmBzDA,AT2BA,ADGA,AWjCA;AELA,AtBkEA,AmBzDA,AT2BA,ADGA,AWjCA;AELA,AtBkEA,AmBzDA,AT2BA,ADGA,AWjCA;AELA,AtBkEA,AmBzDA,AT2BA,ADGA,AWjCA;AELA,AtBkEA,AmBzDA,AT2BA,ADGA,AWjCA;AELA,AtBkEA,AmBzDA,AT2BA,ADGA,AWjCA;AELA,AtBkEA,AmBzDA,AT2BA,ADGA,AWjCA;AELA,AtBkEA,AmBzDA,AT2BA,ADGA,AWjCA;AELA,AtBkEA,AmBzDA,AT2BA,ADGA,AWjCA;AELA,AHSA,AT2BA,ADGA,AWjCA;AELA,AHSA,AT2BA,ADGA,AWjCA;AELA,AHSA,AT2BA,ADGA,AWjCA;AELA,AHSA,AT2BA,ADGA,AWjCA;AELA,AHSA,AT2BA,ADGA,AWjCA;AELA,AHSA,AT2BA,ADGA,AWjCA;AELA,AHSA,AT2BA,ADGA,AWjCA;AELA,AHSA,AT2BA,ADGA,AWjCA;AELA,AHSA,AT2BA,ADGA,AWjCA;AELA,AHSA,AT2BA,ADGA,AWjCA;AELA,AHSA,AT2BA,ADGA,AWjCA;AELA,AHSA,AT2BA,ADGA,AWjCA;AELA,AHSA,AT2BA,ADGA,AWjCA;AELA,AHSA,AT2BA,ADGA,AWjCA;AELA,AHSA,AT2BA,ADGA,AWjCA;AELA,AHSA,AT2BA,ADGA,AWjCA;AELA,AHSA,AT2BA,ADGA,AWjCA;AELA,AHSA,AT2BA,ADGA,AWjCA;AELA,AHSA,AT2BA,ADGA,AWjCA;AELA,AHSA,AT2BA,ADGA,AWjCA;AELA,AHSA,AT2BA,ADGA,AWjCA;AELA,AHSA,AT2BA,ADGA,AWjCA;AELA,AHSA,AT2BA,ADGA,AWjCA;AELA,AHSA,AT2BA,ADGA,AWjCA;AELA,AHSA,AT2BA,ADGA,AWjCA;AELA,AHSA,AT2BA,ADGA,AWjCA;AELA,AHSA,AT2BA,ADGA,AWjCA;AELA,AHSA,AT2BA,ADGA,AWjCA;AELA,AHSA,AT2BA,ADGA,AWjCA;AELA,AHSA,AT2BA,ADGA,AWjCA;AELA,AHSA,AT2BA,ADGA,AWjCA;AELA,AHSA,AT2BA,ADGA;AatCA,AHSA,AT2BA,ADGA;AatCA,AHSA,AT2BA,ADGA;AatCA,AHSA,AT2BA,ADGA;AatCA,AHSA,AT2BA,ADGA;AatCA,AHSA,AT2BA,ADGA;AatCA,AHSA,AT2BA,ADGA;AatCA,AHSA,AT2BA,ADGA;AatCA,AHSA,AT2BA,ADGA;AatCA,AHSA,AT2BA,ADGA;AatCA,AHSA,AT2BA,ADGA;AatCA,AZoCA,ADGA;AatCA,AZoCA,ADGA;AatCA,AZoCA,ADGA;AatCA,AZoCA,ADGA;AatCA,AZoCA,ADGA;AatCA,AZoCA,ADGA;AatCA,AZoCA,ADGA;AatCA,AZoCA,ADGA;AatCA,AZoCA,ADGA;AatCA,AZoCA,ADGA;AatCA,AZoCA,ADGA;AatCA,AZoCA,ADGA;AatCA,AZoCA,ADGA;AatCA,AZoCA,ADGA;AatCA,AZoCA,ADGA;AatCA,AZoCA,ADGA;AatCA,AZoCA,ADGA;AatCA,AZoCA,ADGA;AatCA,AZoCA,ADGA;AatCA,AZoCA,ADGA;AatCA,AZoCA,ADGA;AatCA,AZoCA,ADGA;AatCA,AZoCA,ADGA;AatCA,AZoCA,ADGA;AatCA,AZoCA,ADGA;AatCA,AZoCA,ADGA;AatCA,AZoCA,ADGA;AatCA,AZoCA,ADGA;AatCA,AZoCA,ADGA;AatCA,AZoCA,ADGA;AatCA,AZoCA,ADGA;AatCA,AZoCA,ADGA;AatCA,AZoCA,ADGA;AatCA,AZoCA,ADGA;AatCA,AZoCA,ADGA;AatCA,AZoCA,ADGA;AatCA,AZoCA,ADGA;AatCA,AZoCA,ADGA;AatCA,AZoCA,ADGA;AatCA,AZoCA,ADGA;AatCA,AZoCA,ADGA;AatCA,AZoCA,ADGA;AatCA,AZoCA,ADGA;AatCA,AZoCA,ADGA;AatCA,AZoCA,ADGA;AatCA,AZoCA,ADGA;AatCA,AZoCA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.js", "sourcesContent": ["\nObject.defineProperty(exports, \"__esModule\", { value: true });\nconst Geo = require(\"./geo/index\");\nconst collection_1 = require(\"./collection\");\nconst command_1 = require(\"./command\");\nconst index_1 = require(\"./serverDate/index\");\nconst index_2 = require(\"./regexp/index\");\nconst index_3 = require(\"./transaction/index\");\nconst logic_1 = require(\"./commands/logic\");\nconst query_1 = require(\"./commands/query\");\nconst update_1 = require(\"./commands/update\");\nvar query_2 = require(\"./query\");\nexports.Query = query_2.Query;\nvar collection_2 = require(\"./collection\");\nexports.CollectionReference = collection_2.CollectionReference;\nvar document_1 = require(\"./document\");\nexports.DocumentReference = document_1.DocumentReference;\nclass Db {\n    constructor(config) {\n        this.config = config;\n        this.Geo = Geo;\n        this.serverDate = index_1.ServerDateConstructor;\n        this.command = command_1.Command;\n        this.RegExp = index_2.RegExpConstructor;\n        this.startTransaction = index_3.startTransaction;\n        this.runTransaction = index_3.runTransaction;\n        this.logicCommand = logic_1.LogicCommand;\n        this.updateCommand = update_1.UpdateCommand;\n        this.queryCommand = query_1.QueryCommand;\n    }\n    collection(collName) {\n        if (!collName) {\n            throw new Error('Collection name is required');\n        }\n        return new collection_1.CollectionReference(this, collName);\n    }\n    createCollection(collName) {\n        let request = new Db.reqClass(this.config);\n        const params = {\n            collectionName: collName\n        };\n        return request.send('database.addCollection', params);\n    }\n}\nexports.Db = Db;\n", "\nfunction __export(m) {\n    for (var p in m) if (!exports.hasOwnProperty(p)) exports[p] = m[p];\n}\nObject.defineProperty(exports, \"__esModule\", { value: true });\n__export(require(\"./point\"));\n__export(require(\"./lineString\"));\n__export(require(\"./polygon\"));\n__export(require(\"./multiPoint\"));\n__export(require(\"./multiLineString\"));\n__export(require(\"./multiPolygon\"));\n", "\nObject.defineProperty(exports, \"__esModule\", { value: true });\nconst validate_1 = require(\"../validate\");\nconst symbol_1 = require(\"../helper/symbol\");\nconst type_1 = require(\"../utils/type\");\nclass Point {\n    constructor(longitude, latitude) {\n        validate_1.Validate.isGeopoint('longitude', longitude);\n        validate_1.Validate.isGeopoint('latitude', latitude);\n        this.longitude = longitude;\n        this.latitude = latitude;\n    }\n    parse(key) {\n        return {\n            [key]: {\n                type: 'Point',\n                coordinates: [this.longitude, this.latitude]\n            }\n        };\n    }\n    toJSON() {\n        return {\n            type: 'Point',\n            coordinates: [\n                this.longitude,\n                this.latitude,\n            ],\n        };\n    }\n    toReadableString() {\n        return `[${this.longitude},${this.latitude}]`;\n    }\n    static validate(point) {\n        return point.type === 'Point' &&\n            type_1.isArray(point.coordinates) &&\n            validate_1.Validate.isGeopoint('longitude', point.coordinates[0]) &&\n            validate_1.Validate.isGeopoint('latitude', point.coordinates[1]);\n    }\n    get _internalType() {\n        return symbol_1.SYMBOL_GEO_POINT;\n    }\n}\nexports.Point = Point;\n", "\nObject.defineProperty(exports, \"__esModule\", { value: true });\nconst constant_1 = require(\"./constant\");\nconst util_1 = require(\"./util\");\nclass Validate {\n    static isGeopoint(point, degree) {\n        if (util_1.Util.whichType(degree) !== constant_1.FieldType.Number) {\n            throw new Error('Geo Point must be number type');\n        }\n        const degreeAbs = Math.abs(degree);\n        if (point === 'latitude' && degreeAbs > 90) {\n            throw new Error('latitude should be a number ranges from -90 to 90');\n        }\n        else if (point === 'longitude' && degreeAbs > 180) {\n            throw new Error('longitude should be a number ranges from -180 to 180');\n        }\n        return true;\n    }\n    static isInteger(param, num) {\n        if (!Number.isInteger(num)) {\n            throw new Error(param + constant_1.ErrorCode.IntergerError);\n        }\n        return true;\n    }\n    static isFieldOrder(direction) {\n        if (constant_1.OrderDirectionList.indexOf(direction) === -1) {\n            throw new Error(constant_1.ErrorCode.DirectionError);\n        }\n        return true;\n    }\n    static isFieldPath(path) {\n        if (!/^[a-zA-Z0-9-_\\.]/.test(path)) {\n            throw new Error();\n        }\n        return true;\n    }\n    static isOperator(op) {\n        if (constant_1.WhereFilterOpList.indexOf(op) === -1) {\n            throw new Error(constant_1.ErrorCode.OpStrError);\n        }\n        return true;\n    }\n    static isCollName(name) {\n        if (!/^[a-zA-Z0-9]([a-zA-Z0-9-_]){1,32}$/.test(name)) {\n            throw new Error(constant_1.ErrorCode.CollNameError);\n        }\n        return true;\n    }\n    static isDocID(docId) {\n        if (!/^([a-fA-F0-9]){24}$/.test(docId)) {\n            throw new Error(constant_1.ErrorCode.DocIDError);\n        }\n        return true;\n    }\n}\nexports.Validate = Validate;\n", "\nObject.defineProperty(exports, \"__esModule\", { value: true });\nvar ErrorCode;\n(function (ErrorCode) {\n    ErrorCode[\"DocIDError\"] = \"\\u6587\\u6863ID\\u4E0D\\u5408\\u6CD5\";\n    ErrorCode[\"CollNameError\"] = \"\\u96C6\\u5408\\u540D\\u79F0\\u4E0D\\u5408\\u6CD5\";\n    ErrorCode[\"OpStrError\"] = \"\\u64CD\\u4F5C\\u7B26\\u4E0D\\u5408\\u6CD5\";\n    ErrorCode[\"DirectionError\"] = \"\\u6392\\u5E8F\\u5B57\\u7B26\\u4E0D\\u5408\\u6CD5\";\n    ErrorCode[\"IntergerError\"] = \"must be integer\";\n    ErrorCode[\"QueryParamTypeError\"] = \"\\u67E5\\u8BE2\\u53C2\\u6570\\u5FC5\\u987B\\u4E3A\\u5BF9\\u8C61\";\n    ErrorCode[\"QueryParamValueError\"] = \"\\u67E5\\u8BE2\\u53C2\\u6570\\u5BF9\\u8C61\\u503C\\u4E0D\\u80FD\\u5747\\u4E3Aundefined\";\n})(ErrorCode || (ErrorCode = {}));\nexports.ErrorCode = ErrorCode;\nconst FieldType = {\n    String: 'String',\n    Number: 'Number',\n    Object: 'Object',\n    Array: 'Array',\n    Boolean: 'Boolean',\n    Null: 'Null',\n    GeoPoint: 'GeoPoint',\n    GeoLineString: 'GeoLineString',\n    GeoPolygon: 'GeoPolygon',\n    GeoMultiPoint: 'GeoMultiPoint',\n    GeoMultiLineString: 'GeoMultiLineString',\n    GeoMultiPolygon: 'GeoMultiPolygon',\n    Timestamp: 'Date',\n    Command: 'Command',\n    ServerDate: 'ServerDate',\n    BsonDate: 'BsonDate'\n};\nexports.FieldType = FieldType;\nconst OrderDirectionList = ['desc', 'asc'];\nexports.OrderDirectionList = OrderDirectionList;\nconst WhereFilterOpList = ['<', '<=', '==', '>=', '>'];\nexports.WhereFilterOpList = WhereFilterOpList;\nvar Opeartor;\n(function (Opeartor) {\n    Opeartor[\"lt\"] = \"<\";\n    Opeartor[\"gt\"] = \">\";\n    Opeartor[\"lte\"] = \"<=\";\n    Opeartor[\"gte\"] = \">=\";\n    Opeartor[\"eq\"] = \"==\";\n})(Opeartor || (Opeartor = {}));\nexports.Opeartor = Opeartor;\nconst OperatorMap = {\n    [Opeartor.eq]: '$eq',\n    [Opeartor.lt]: '$lt',\n    [Opeartor.lte]: '$lte',\n    [Opeartor.gt]: '$gt',\n    [Opeartor.gte]: '$gte'\n};\nexports.OperatorMap = OperatorMap;\nconst UpdateOperatorList = [\n    '$set',\n    '$inc',\n    '$mul',\n    '$unset',\n    '$push',\n    '$pop',\n    '$unshift',\n    '$shift',\n    '$currentDate',\n    '$each',\n    '$position'\n];\nexports.UpdateOperatorList = UpdateOperatorList;\nvar QueryType;\n(function (QueryType) {\n    QueryType[\"WHERE\"] = \"WHERE\";\n    QueryType[\"DOC\"] = \"DOC\";\n})(QueryType || (QueryType = {}));\nexports.QueryType = QueryType;\n", "\nObject.defineProperty(exports, \"__esModule\", { value: true });\nconst constant_1 = require(\"./constant\");\nconst index_1 = require(\"./geo/index\");\nconst index_2 = require(\"./serverDate/index\");\nclass Util {\n}\nexports.Util = Util;\nUtil.formatResDocumentData = (documents) => {\n    return documents.map(document => {\n        return Util.formatField(document);\n    });\n};\nUtil.formatField = document => {\n    const keys = Object.keys(document);\n    let protoField = {};\n    if (Array.isArray(document)) {\n        protoField = [];\n    }\n    keys.forEach(key => {\n        const item = document[key];\n        const type = Util.whichType(item);\n        let realValue;\n        switch (type) {\n            case constant_1.FieldType.GeoPoint:\n                realValue = new index_1.Point(item.coordinates[0], item.coordinates[1]);\n                break;\n            case constant_1.FieldType.GeoLineString:\n                realValue = new index_1.LineString(item.coordinates.map(point => new index_1.Point(point[0], point[1])));\n                break;\n            case constant_1.FieldType.GeoPolygon:\n                realValue = new index_1.Polygon(item.coordinates.map(line => new index_1.LineString(line.map(([lng, lat]) => new index_1.Point(lng, lat)))));\n                break;\n            case constant_1.FieldType.GeoMultiPoint:\n                realValue = new index_1.MultiPoint(item.coordinates.map(point => new index_1.Point(point[0], point[1])));\n                break;\n            case constant_1.FieldType.GeoMultiLineString:\n                realValue = new index_1.MultiLineString(item.coordinates.map(line => new index_1.LineString(line.map(([lng, lat]) => new index_1.Point(lng, lat)))));\n                break;\n            case constant_1.FieldType.GeoMultiPolygon:\n                realValue = new index_1.MultiPolygon(item.coordinates.map(polygon => new index_1.Polygon(polygon.map(line => new index_1.LineString(line.map(([lng, lat]) => new index_1.Point(lng, lat)))))));\n                break;\n            case constant_1.FieldType.Timestamp:\n                realValue = new Date(item.$timestamp * 1000);\n                break;\n            case constant_1.FieldType.Object:\n            case constant_1.FieldType.Array:\n                realValue = Util.formatField(item);\n                break;\n            case constant_1.FieldType.ServerDate:\n                realValue = new Date(item.$date);\n                break;\n            default:\n                realValue = item;\n        }\n        if (Array.isArray(protoField)) {\n            protoField.push(realValue);\n        }\n        else {\n            protoField[key] = realValue;\n        }\n    });\n    return protoField;\n};\nUtil.whichType = (obj) => {\n    let type = Object.prototype.toString.call(obj).slice(8, -1);\n    if (type === constant_1.FieldType.Timestamp) {\n        return constant_1.FieldType.BsonDate;\n    }\n    if (type === constant_1.FieldType.Object) {\n        if (obj instanceof index_1.Point) {\n            return constant_1.FieldType.GeoPoint;\n        }\n        else if (obj instanceof Date) {\n            return constant_1.FieldType.Timestamp;\n        }\n        else if (obj instanceof index_2.ServerDate) {\n            return constant_1.FieldType.ServerDate;\n        }\n        if (obj.$timestamp) {\n            type = constant_1.FieldType.Timestamp;\n        }\n        else if (obj.$date) {\n            type = constant_1.FieldType.ServerDate;\n        }\n        else if (index_1.Point.validate(obj)) {\n            type = constant_1.FieldType.GeoPoint;\n        }\n        else if (index_1.LineString.validate(obj)) {\n            type = constant_1.FieldType.GeoLineString;\n        }\n        else if (index_1.Polygon.validate(obj)) {\n            type = constant_1.FieldType.GeoPolygon;\n        }\n        else if (index_1.MultiPoint.validate(obj)) {\n            type = constant_1.FieldType.GeoMultiPoint;\n        }\n        else if (index_1.MultiLineString.validate(obj)) {\n            type = constant_1.FieldType.GeoMultiLineString;\n        }\n        else if (index_1.MultiPolygon.validate(obj)) {\n            type = constant_1.FieldType.GeoMultiPolygon;\n        }\n    }\n    return type;\n};\nUtil.generateDocId = () => {\n    let chars = 'ABCDEFabcdef0123456789';\n    let autoId = '';\n    for (let i = 0; i < 24; i++) {\n        autoId += chars.charAt(Math.floor(Math.random() * chars.length));\n    }\n    return autoId;\n};\n", "\nObject.defineProperty(exports, \"__esModule\", { value: true });\nconst symbol_1 = require(\"../helper/symbol\");\nclass ServerDate {\n    constructor({ offset = 0 } = {}) {\n        this.offset = offset;\n    }\n    get _internalType() {\n        return symbol_1.SYMBOL_SERVER_DATE;\n    }\n    parse() {\n        return {\n            $date: {\n                offset: this.offset\n            }\n        };\n    }\n}\nexports.ServerDate = ServerDate;\nfunction ServerDateConstructor(opt) {\n    return new ServerDate(opt);\n}\nexports.ServerDateConstructor = ServerDateConstructor;\n", "\nfunction __export(m) {\n    for (var p in m) if (!exports.hasOwnProperty(p)) exports[p] = m[p];\n}\nObject.defineProperty(exports, \"__esModule\", { value: true });\nconst symbol_1 = require(\"../utils/symbol\");\n__export(require(\"../utils/symbol\"));\nexports.SYMBOL_UNSET_FIELD_NAME = symbol_1.default.for('UNSET_FIELD_NAME');\nexports.SYMBOL_UPDATE_COMMAND = symbol_1.default.for('UPDATE_COMMAND');\nexports.SYMBOL_QUERY_COMMAND = symbol_1.default.for('QUERY_COMMAND');\nexports.SYMBOL_LOGIC_COMMAND = symbol_1.default.for('LOGIC_COMMAND');\nexports.SYMBOL_GEO_POINT = symbol_1.default.for('GEO_POINT');\nexports.SYMBOL_GEO_LINE_STRING = symbol_1.default.for('SYMBOL_GEO_LINE_STRING');\nexports.SYMBOL_GEO_POLYGON = symbol_1.default.for('SYMBOL_GEO_POLYGON');\nexports.SYMBOL_GEO_MULTI_POINT = symbol_1.default.for('SYMBOL_GEO_MULTI_POINT');\nexports.SYMBOL_GEO_MULTI_LINE_STRING = symbol_1.default.for('SYMBOL_GEO_MULTI_LINE_STRING');\nexports.SYMBOL_GEO_MULTI_POLYGON = symbol_1.default.for('SYMBOL_GEO_MULTI_POLYGON');\nexports.SYMBOL_SERVER_DATE = symbol_1.default.for('SERVER_DATE');\nexports.SYMBOL_REGEXP = symbol_1.default.for('REGEXP');\n", "\nObject.defineProperty(exports, \"__esModule\", { value: true });\nconst _symbols = [];\nconst __internalMark__ = {};\nclass HiddenSymbol {\n    constructor(target) {\n        Object.defineProperties(this, {\n            target: {\n                enumerable: false,\n                writable: false,\n                configurable: false,\n                value: target,\n            },\n        });\n    }\n}\nclass InternalSymbol extends HiddenSymbol {\n    constructor(target, __mark__) {\n        if (__mark__ !== __internalMark__) {\n            throw new TypeError('InternalSymbol cannot be constructed with new operator');\n        }\n        super(target);\n    }\n    static for(target) {\n        for (let i = 0, len = _symbols.length; i < len; i++) {\n            if (_symbols[i].target === target) {\n                return _symbols[i].instance;\n            }\n        }\n        const symbol = new InternalSymbol(target, __internalMark__);\n        _symbols.push({\n            target,\n            instance: symbol,\n        });\n        return symbol;\n    }\n}\nexports.InternalSymbol = InternalSymbol;\nexports.default = InternalSymbol;\n", "\nObject.defineProperty(exports, \"__esModule\", { value: true });\nconst symbol_1 = require(\"./symbol\");\nexports.getType = (x) => Object.prototype.toString.call(x).slice(8, -1).toLowerCase();\nexports.isObject = (x) => exports.getType(x) === 'object';\nexports.isString = (x) => exports.getType(x) === 'string';\nexports.isNumber = (x) => exports.getType(x) === 'number';\nexports.isPromise = (x) => exports.getType(x) === 'promise';\nexports.isFunction = (x) => typeof x === 'function';\nexports.isArray = (x) => Array.isArray(x);\nexports.isDate = (x) => exports.getType(x) === 'date';\nexports.isRegExp = (x) => exports.getType(x) === 'regexp';\nexports.isInternalObject = (x) => x && (x._internalType instanceof symbol_1.InternalSymbol);\nexports.isPlainObject = (obj) => {\n    if (typeof obj !== 'object' || obj === null)\n        return false;\n    let proto = obj;\n    while (Object.getPrototypeOf(proto) !== null) {\n        proto = Object.getPrototypeOf(proto);\n    }\n    return Object.getPrototypeOf(obj) === proto;\n};\n", "\nObject.defineProperty(exports, \"__esModule\", { value: true });\nconst symbol_1 = require(\"../helper/symbol\");\nconst point_1 = require(\"./point\");\nconst type_1 = require(\"../utils/type\");\nclass LineString {\n    constructor(points) {\n        if (!type_1.isArray(points)) {\n            throw new TypeError(`\"points\" must be of type Point[]. Received type ${typeof points}`);\n        }\n        if (points.length < 2) {\n            throw new Error('\"points\" must contain 2 points at least');\n        }\n        points.forEach(point => {\n            if (!(point instanceof point_1.Point)) {\n                throw new TypeError(`\"points\" must be of type Point[]. Received type ${typeof point}[]`);\n            }\n        });\n        this.points = points;\n    }\n    parse(key) {\n        return {\n            [key]: {\n                type: 'LineString',\n                coordinates: this.points.map(point => point.toJSON().coordinates)\n            }\n        };\n    }\n    toJSON() {\n        return {\n            type: 'LineString',\n            coordinates: this.points.map(point => point.toJSON().coordinates)\n        };\n    }\n    static validate(lineString) {\n        if (lineString.type !== 'LineString' || !type_1.isArray(lineString.coordinates)) {\n            return false;\n        }\n        for (let point of lineString.coordinates) {\n            if (!type_1.isNumber(point[0]) || !type_1.isNumber(point[1])) {\n                return false;\n            }\n        }\n        return true;\n    }\n    static isClosed(lineString) {\n        const firstPoint = lineString.points[0];\n        const lastPoint = lineString.points[lineString.points.length - 1];\n        if (firstPoint.latitude === lastPoint.latitude && firstPoint.longitude === lastPoint.longitude) {\n            return true;\n        }\n    }\n    get _internalType() {\n        return symbol_1.SYMBOL_GEO_LINE_STRING;\n    }\n}\nexports.LineString = LineString;\n", "\nObject.defineProperty(exports, \"__esModule\", { value: true });\nconst symbol_1 = require(\"../helper/symbol\");\nconst type_1 = require(\"../utils/type\");\nconst lineString_1 = require(\"./lineString\");\nclass Polygon {\n    constructor(lines) {\n        if (!type_1.isArray(lines)) {\n            throw new TypeError(`\"lines\" must be of type LineString[]. Received type ${typeof lines}`);\n        }\n        if (lines.length === 0) {\n            throw new Error('Polygon must contain 1 linestring at least');\n        }\n        lines.forEach(line => {\n            if (!(line instanceof lineString_1.LineString)) {\n                throw new TypeError(`\"lines\" must be of type LineString[]. Received type ${typeof line}[]`);\n            }\n            if (!lineString_1.LineString.isClosed(line)) {\n                throw new Error(`LineString ${line.points.map(p => p.toReadableString())} is not a closed cycle`);\n            }\n        });\n        this.lines = lines;\n    }\n    parse(key) {\n        return {\n            [key]: {\n                type: 'Polygon',\n                coordinates: this.lines.map(line => {\n                    return line.points.map(point => [point.longitude, point.latitude]);\n                })\n            }\n        };\n    }\n    toJSON() {\n        return {\n            type: 'Polygon',\n            coordinates: this.lines.map(line => {\n                return line.points.map(point => [point.longitude, point.latitude]);\n            })\n        };\n    }\n    static validate(polygon) {\n        if (polygon.type !== 'Polygon' || !type_1.isArray(polygon.coordinates)) {\n            return false;\n        }\n        for (let line of polygon.coordinates) {\n            if (!this.isCloseLineString(line)) {\n                return false;\n            }\n            for (let point of line) {\n                if (!type_1.isNumber(point[0]) || !type_1.isNumber(point[1])) {\n                    return false;\n                }\n            }\n        }\n        return true;\n    }\n    static isCloseLineString(lineString) {\n        const firstPoint = lineString[0];\n        const lastPoint = lineString[lineString.length - 1];\n        if (firstPoint[0] !== lastPoint[0] || firstPoint[1] !== lastPoint[1]) {\n            return false;\n        }\n        return true;\n    }\n    get _internalType() {\n        return symbol_1.SYMBOL_GEO_MULTI_POLYGON;\n    }\n}\nexports.Polygon = Polygon;\n", "\nObject.defineProperty(exports, \"__esModule\", { value: true });\nconst symbol_1 = require(\"../helper/symbol\");\nconst point_1 = require(\"./point\");\nconst type_1 = require(\"../utils/type\");\nclass MultiPoint {\n    constructor(points) {\n        if (!type_1.isArray(points)) {\n            throw new TypeError(`\"points\" must be of type Point[]. Received type ${typeof points}`);\n        }\n        if (points.length === 0) {\n            throw new Error('\"points\" must contain 1 point at least');\n        }\n        points.forEach(point => {\n            if (!(point instanceof point_1.Point)) {\n                throw new TypeError(`\"points\" must be of type Point[]. Received type ${typeof point}[]`);\n            }\n        });\n        this.points = points;\n    }\n    parse(key) {\n        return {\n            [key]: {\n                type: 'MultiPoint',\n                coordinates: this.points.map(point => point.toJSON().coordinates)\n            }\n        };\n    }\n    toJSON() {\n        return {\n            type: 'MultiPoint',\n            coordinates: this.points.map(point => point.toJSON().coordinates)\n        };\n    }\n    static validate(multiPoint) {\n        if (multiPoint.type !== 'MultiPoint' || !type_1.isArray(multiPoint.coordinates)) {\n            return false;\n        }\n        for (let point of multiPoint.coordinates) {\n            if (!type_1.isNumber(point[0]) || !type_1.isNumber(point[1])) {\n                return false;\n            }\n        }\n        return true;\n    }\n    get _internalType() {\n        return symbol_1.SYMBOL_GEO_MULTI_POINT;\n    }\n}\nexports.MultiPoint = MultiPoint;\n", "\nObject.defineProperty(exports, \"__esModule\", { value: true });\nconst symbol_1 = require(\"../helper/symbol\");\nconst type_1 = require(\"../utils/type\");\nconst lineString_1 = require(\"./lineString\");\nclass MultiLineString {\n    constructor(lines) {\n        if (!type_1.isArray(lines)) {\n            throw new TypeError(`\"lines\" must be of type LineString[]. Received type ${typeof lines}`);\n        }\n        if (lines.length === 0) {\n            throw new Error('Polygon must contain 1 linestring at least');\n        }\n        lines.forEach(line => {\n            if (!(line instanceof lineString_1.LineString)) {\n                throw new TypeError(`\"lines\" must be of type LineString[]. Received type ${typeof line}[]`);\n            }\n        });\n        this.lines = lines;\n    }\n    parse(key) {\n        return {\n            [key]: {\n                type: 'MultiLineString',\n                coordinates: this.lines.map(line => {\n                    return line.points.map(point => [point.longitude, point.latitude]);\n                })\n            }\n        };\n    }\n    toJSON() {\n        return {\n            type: 'MultiLineString',\n            coordinates: this.lines.map(line => {\n                return line.points.map(point => [point.longitude, point.latitude]);\n            })\n        };\n    }\n    static validate(multiLineString) {\n        if (multiLineString.type !== 'MultiLineString' || !type_1.isArray(multiLineString.coordinates)) {\n            return false;\n        }\n        for (let line of multiLineString.coordinates) {\n            for (let point of line) {\n                if (!type_1.isNumber(point[0]) || !type_1.isNumber(point[1])) {\n                    return false;\n                }\n            }\n        }\n        return true;\n    }\n    get _internalType() {\n        return symbol_1.SYMBOL_GEO_MULTI_LINE_STRING;\n    }\n}\nexports.MultiLineString = MultiLineString;\n", "\nObject.defineProperty(exports, \"__esModule\", { value: true });\nconst symbol_1 = require(\"../helper/symbol\");\nconst type_1 = require(\"../utils/type\");\nconst polygon_1 = require(\"./polygon\");\nclass MultiPolygon {\n    constructor(polygons) {\n        if (!type_1.isArray(polygons)) {\n            throw new TypeError(`\"polygons\" must be of type Polygon[]. Received type ${typeof polygons}`);\n        }\n        if (polygons.length === 0) {\n            throw new Error('MultiPolygon must contain 1 polygon at least');\n        }\n        for (let polygon of polygons) {\n            if (!(polygon instanceof polygon_1.Polygon)) {\n                throw new TypeError(`\"polygon\" must be of type Polygon[]. Received type ${typeof polygon}[]`);\n            }\n        }\n        this.polygons = polygons;\n    }\n    parse(key) {\n        return {\n            [key]: {\n                type: 'MultiPolygon',\n                coordinates: this.polygons.map(polygon => {\n                    return polygon.lines.map(line => {\n                        return line.points.map(point => [point.longitude, point.latitude]);\n                    });\n                })\n            }\n        };\n    }\n    toJSON() {\n        return {\n            type: 'MultiPolygon',\n            coordinates: this.polygons.map(polygon => {\n                return polygon.lines.map(line => {\n                    return line.points.map(point => [point.longitude, point.latitude]);\n                });\n            })\n        };\n    }\n    static validate(multiPolygon) {\n        if (multiPolygon.type !== 'MultiPolygon' || !type_1.isArray(multiPolygon.coordinates)) {\n            return false;\n        }\n        for (let polygon of multiPolygon.coordinates) {\n            for (let line of polygon) {\n                for (let point of line) {\n                    if (!type_1.isNumber(point[0]) || !type_1.isNumber(point[1])) {\n                        return false;\n                    }\n                }\n            }\n        }\n        return true;\n    }\n    get _internalType() {\n        return symbol_1.SYMBOL_GEO_POLYGON;\n    }\n}\nexports.MultiPolygon = MultiPolygon;\n", "\nObject.defineProperty(exports, \"__esModule\", { value: true });\nconst document_1 = require(\"./document\");\nconst query_1 = require(\"./query\");\nconst aggregate_1 = require(\"./aggregate\");\nclass CollectionReference extends query_1.Query {\n    constructor(db, coll) {\n        super(db, coll);\n    }\n    get name() {\n        return this._coll;\n    }\n    doc(docID) {\n        if (typeof docID !== 'string' && typeof docID !== 'number') {\n            throw new Error('docId必须为字符串或数字');\n        }\n        return new document_1.DocumentReference(this._db, this._coll, docID);\n    }\n    add(data, callback) {\n        let docRef = new document_1.DocumentReference(this._db, this._coll, undefined);\n        return docRef.create(data, callback);\n    }\n    aggregate() {\n        return new aggregate_1.default(this._db, this._coll);\n    }\n}\nexports.CollectionReference = CollectionReference;\n", "\nObject.defineProperty(exports, \"__esModule\", { value: true });\nconst util_1 = require(\"./lib/util\");\nconst index_1 = require(\"./index\");\nconst util_2 = require(\"./util\");\nconst update_1 = require(\"./serializer/update\");\nconst datatype_1 = require(\"./serializer/datatype\");\nconst update_2 = require(\"./commands/update\");\nconst websocket_client_1 = require(\"./realtime/websocket-client\");\nconst constant_1 = require(\"./constant\");\nclass DocumentReference {\n    constructor(db, coll, docID, projection = {}) {\n        this.watch = (options) => {\n            if (!index_1.Db.ws) {\n                index_1.Db.ws = new websocket_client_1.RealtimeWebSocketClient({\n                    context: {\n                        appConfig: {\n                            docSizeLimit: 1000,\n                            realtimePingInterval: 10000,\n                            realtimePongWaitTimeout: 5000,\n                            request: this.request\n                        }\n                    }\n                });\n            }\n            return index_1.Db.ws.watch(Object.assign(Object.assign({}, options), { envId: this._db.config.env, collectionName: this._coll, query: JSON.stringify({\n                    _id: this.id\n                }) }));\n        };\n        this._db = db;\n        this._coll = coll;\n        this.id = docID;\n        this.request = new index_1.Db.reqClass(this._db.config);\n        this.projection = projection;\n    }\n    create(data, callback) {\n        callback = callback || util_1.createPromiseCallback();\n        let params = {\n            collectionName: this._coll,\n            data: datatype_1.serialize(data)\n        };\n        if (this.id) {\n            params['_id'] = this.id;\n        }\n        this.request\n            .send('database.addDocument', params)\n            .then(res => {\n            if (res.code) {\n                callback(0, res);\n            }\n            else {\n                callback(0, {\n                    id: res.data._id,\n                    requestId: res.requestId\n                });\n            }\n        })\n            .catch(err => {\n            callback(err);\n        });\n        return callback.promise;\n    }\n    set(data, callback) {\n        callback = callback || util_1.createPromiseCallback();\n        if (!this.id) {\n            return Promise.resolve({\n                code: 'INVALID_PARAM',\n                message: 'docId不能为空'\n            });\n        }\n        if (!data || typeof data !== 'object') {\n            return Promise.resolve({\n                code: 'INVALID_PARAM',\n                message: '参数必需是非空对象'\n            });\n        }\n        if (data.hasOwnProperty('_id')) {\n            return Promise.resolve({\n                code: 'INVALID_PARAM',\n                message: '不能更新_id的值'\n            });\n        }\n        let hasOperator = false;\n        const checkMixed = objs => {\n            if (typeof objs === 'object') {\n                for (let key in objs) {\n                    if (objs[key] instanceof update_2.UpdateCommand) {\n                        hasOperator = true;\n                    }\n                    else if (typeof objs[key] === 'object') {\n                        checkMixed(objs[key]);\n                    }\n                }\n            }\n        };\n        checkMixed(data);\n        if (hasOperator) {\n            return Promise.resolve({\n                code: 'DATABASE_REQUEST_FAILED',\n                message: 'update operator complicit'\n            });\n        }\n        const merge = false;\n        let param = {\n            collectionName: this._coll,\n            queryType: constant_1.QueryType.DOC,\n            data: datatype_1.serialize(data),\n            multi: false,\n            merge,\n            upsert: true\n        };\n        if (this.id) {\n            param['query'] = { _id: this.id };\n        }\n        this.request\n            .send('database.updateDocument', param)\n            .then(res => {\n            if (res.code) {\n                callback(0, res);\n            }\n            else {\n                callback(0, {\n                    updated: res.data.updated,\n                    upsertedId: res.data.upserted_id,\n                    requestId: res.requestId\n                });\n            }\n        })\n            .catch(err => {\n            callback(err);\n        });\n        return callback.promise;\n    }\n    update(data, callback) {\n        callback = callback || util_1.createPromiseCallback();\n        if (!data || typeof data !== 'object') {\n            return Promise.resolve({\n                code: 'INVALID_PARAM',\n                message: '参数必需是非空对象'\n            });\n        }\n        if (data.hasOwnProperty('_id')) {\n            return Promise.resolve({\n                code: 'INVALID_PARAM',\n                message: '不能更新_id的值'\n            });\n        }\n        const query = { _id: this.id };\n        const merge = true;\n        const param = {\n            collectionName: this._coll,\n            data: update_1.UpdateSerializer.encode(data),\n            query: query,\n            queryType: constant_1.QueryType.DOC,\n            multi: false,\n            merge,\n            upsert: false\n        };\n        this.request\n            .send('database.updateDocument', param)\n            .then(res => {\n            if (res.code) {\n                callback(0, res);\n            }\n            else {\n                callback(0, {\n                    updated: res.data.updated,\n                    upsertedId: res.data.upserted_id,\n                    requestId: res.requestId\n                });\n            }\n        })\n            .catch(err => {\n            callback(err);\n        });\n        return callback.promise;\n    }\n    remove(callback) {\n        callback = callback || util_1.createPromiseCallback();\n        const query = { _id: this.id };\n        const param = {\n            collectionName: this._coll,\n            query: query,\n            queryType: constant_1.QueryType.DOC,\n            multi: false\n        };\n        this.request\n            .send('database.deleteDocument', param)\n            .then(res => {\n            if (res.code) {\n                callback(0, res);\n            }\n            else {\n                callback(0, {\n                    deleted: res.data.deleted,\n                    requestId: res.requestId\n                });\n            }\n        })\n            .catch(err => {\n            callback(err);\n        });\n        return callback.promise;\n    }\n    get(callback) {\n        callback = callback || util_1.createPromiseCallback();\n        const query = { _id: this.id };\n        const param = {\n            collectionName: this._coll,\n            query: query,\n            queryType: constant_1.QueryType.DOC,\n            multi: false,\n            projection: this.projection\n        };\n        this.request\n            .send('database.queryDocument', param)\n            .then(res => {\n            if (res.code) {\n                callback(0, res);\n            }\n            else {\n                const documents = util_2.Util.formatResDocumentData(res.data.list);\n                callback(0, {\n                    data: documents,\n                    requestId: res.requestId,\n                    total: res.TotalCount,\n                    limit: res.Limit,\n                    offset: res.Offset\n                });\n            }\n        })\n            .catch(err => {\n            callback(err);\n        });\n        return callback.promise;\n    }\n    field(projection) {\n        for (let k in projection) {\n            if (projection[k]) {\n                projection[k] = 1;\n            }\n            else {\n                projection[k] = 0;\n            }\n        }\n        return new DocumentReference(this._db, this._coll, this.id, projection);\n    }\n}\nexports.DocumentReference = DocumentReference;\n", "\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.createPromiseCallback = () => {\n    let cb;\n    if (!Promise) {\n        cb = () => { };\n        cb.promise = {};\n        const throwPromiseNotDefined = () => {\n            throw new Error('Your Node runtime does support ES6 Promises. ' +\n                'Set \"global.Promise\" to your preferred implementation of promises.');\n        };\n        Object.defineProperty(cb.promise, 'then', { get: throwPromiseNotDefined });\n        Object.defineProperty(cb.promise, 'catch', { get: throwPromiseNotDefined });\n        return cb;\n    }\n    const promise = new Promise((resolve, reject) => {\n        cb = (err, data) => {\n            if (err)\n                return reject(err);\n            return resolve(data);\n        };\n    });\n    cb.promise = promise;\n    return cb;\n};\n", "\nObject.defineProperty(exports, \"__esModule\", { value: true });\nconst update_1 = require(\"../commands/update\");\nconst symbol_1 = require(\"../helper/symbol\");\nconst type_1 = require(\"../utils/type\");\nconst operator_map_1 = require(\"../operator-map\");\nconst common_1 = require(\"./common\");\nclass UpdateSerializer {\n    constructor() { }\n    static encode(query) {\n        const stringifier = new UpdateSerializer();\n        return stringifier.encodeUpdate(query);\n    }\n    encodeUpdate(query) {\n        if (update_1.isUpdateCommand(query)) {\n            return this.encodeUpdateCommand(query);\n        }\n        else if (type_1.getType(query) === 'object') {\n            return this.encodeUpdateObject(query);\n        }\n        else {\n            return query;\n        }\n    }\n    encodeUpdateCommand(query) {\n        if (query.fieldName === symbol_1.SYMBOL_UNSET_FIELD_NAME) {\n            throw new Error('Cannot encode a comparison command with unset field name');\n        }\n        switch (query.operator) {\n            case update_1.UPDATE_COMMANDS_LITERAL.PUSH:\n            case update_1.UPDATE_COMMANDS_LITERAL.PULL:\n            case update_1.UPDATE_COMMANDS_LITERAL.PULL_ALL:\n            case update_1.UPDATE_COMMANDS_LITERAL.POP:\n            case update_1.UPDATE_COMMANDS_LITERAL.SHIFT:\n            case update_1.UPDATE_COMMANDS_LITERAL.UNSHIFT:\n            case update_1.UPDATE_COMMANDS_LITERAL.ADD_TO_SET: {\n                return this.encodeArrayUpdateCommand(query);\n            }\n            default: {\n                return this.encodeFieldUpdateCommand(query);\n            }\n        }\n    }\n    encodeFieldUpdateCommand(query) {\n        const $op = operator_map_1.operatorToString(query.operator);\n        switch (query.operator) {\n            case update_1.UPDATE_COMMANDS_LITERAL.REMOVE: {\n                return {\n                    [$op]: {\n                        [query.fieldName]: ''\n                    }\n                };\n            }\n            default: {\n                return {\n                    [$op]: {\n                        [query.fieldName]: query.operands[0]\n                    }\n                };\n            }\n        }\n    }\n    encodeArrayUpdateCommand(query) {\n        const $op = operator_map_1.operatorToString(query.operator);\n        switch (query.operator) {\n            case update_1.UPDATE_COMMANDS_LITERAL.PUSH: {\n                let modifiers;\n                if (type_1.isArray(query.operands)) {\n                    modifiers = {\n                        $each: query.operands.map(common_1.encodeInternalDataType)\n                    };\n                }\n                else {\n                    modifiers = query.operands;\n                }\n                return {\n                    [$op]: {\n                        [query.fieldName]: modifiers\n                    }\n                };\n            }\n            case update_1.UPDATE_COMMANDS_LITERAL.UNSHIFT: {\n                const modifiers = {\n                    $each: query.operands.map(common_1.encodeInternalDataType),\n                    $position: 0\n                };\n                return {\n                    [$op]: {\n                        [query.fieldName]: modifiers\n                    }\n                };\n            }\n            case update_1.UPDATE_COMMANDS_LITERAL.POP: {\n                return {\n                    [$op]: {\n                        [query.fieldName]: 1\n                    }\n                };\n            }\n            case update_1.UPDATE_COMMANDS_LITERAL.SHIFT: {\n                return {\n                    [$op]: {\n                        [query.fieldName]: -1\n                    }\n                };\n            }\n            default: {\n                return {\n                    [$op]: {\n                        [query.fieldName]: common_1.encodeInternalDataType(query.operands)\n                    }\n                };\n            }\n        }\n    }\n    encodeUpdateObject(query) {\n        const flattened = common_1.flattenQueryObject(query);\n        for (const key in flattened) {\n            if (/^\\$/.test(key))\n                continue;\n            let val = flattened[key];\n            if (update_1.isUpdateCommand(val)) {\n                flattened[key] = val._setFieldName(key);\n                const condition = this.encodeUpdateCommand(flattened[key]);\n                common_1.mergeConditionAfterEncode(flattened, condition, key);\n            }\n            else {\n                flattened[key] = val = common_1.encodeInternalDataType(val);\n                const $setCommand = new update_1.UpdateCommand(update_1.UPDATE_COMMANDS_LITERAL.SET, [val], key);\n                const condition = this.encodeUpdateCommand($setCommand);\n                common_1.mergeConditionAfterEncode(flattened, condition, key);\n            }\n        }\n        return flattened;\n    }\n}\nexports.UpdateSerializer = UpdateSerializer;\n", "\nObject.defineProperty(exports, \"__esModule\", { value: true });\nconst symbol_1 = require(\"../helper/symbol\");\nvar UPDATE_COMMANDS_LITERAL;\n(function (UPDATE_COMMANDS_LITERAL) {\n    UPDATE_COMMANDS_LITERAL[\"SET\"] = \"set\";\n    UPDATE_COMMANDS_LITERAL[\"REMOVE\"] = \"remove\";\n    UPDATE_COMMANDS_LITERAL[\"INC\"] = \"inc\";\n    UPDATE_COMMANDS_LITERAL[\"MUL\"] = \"mul\";\n    UPDATE_COMMANDS_LITERAL[\"PUSH\"] = \"push\";\n    UPDATE_COMMANDS_LITERAL[\"PULL\"] = \"pull\";\n    UPDATE_COMMANDS_LITERAL[\"PULL_ALL\"] = \"pullAll\";\n    UPDATE_COMMANDS_LITERAL[\"POP\"] = \"pop\";\n    UPDATE_COMMANDS_LITERAL[\"SHIFT\"] = \"shift\";\n    UPDATE_COMMANDS_LITERAL[\"UNSHIFT\"] = \"unshift\";\n    UPDATE_COMMANDS_LITERAL[\"ADD_TO_SET\"] = \"addToSet\";\n    UPDATE_COMMANDS_LITERAL[\"BIT\"] = \"bit\";\n    UPDATE_COMMANDS_LITERAL[\"RENAME\"] = \"rename\";\n    UPDATE_COMMANDS_LITERAL[\"MAX\"] = \"max\";\n    UPDATE_COMMANDS_LITERAL[\"MIN\"] = \"min\";\n})(UPDATE_COMMANDS_LITERAL = exports.UPDATE_COMMANDS_LITERAL || (exports.UPDATE_COMMANDS_LITERAL = {}));\nclass UpdateCommand {\n    constructor(operator, operands, fieldName) {\n        this._internalType = symbol_1.SYMBOL_UPDATE_COMMAND;\n        Object.defineProperties(this, {\n            _internalType: {\n                enumerable: false,\n                configurable: false,\n            },\n        });\n        this.operator = operator;\n        this.operands = operands;\n        this.fieldName = fieldName || symbol_1.SYMBOL_UNSET_FIELD_NAME;\n    }\n    _setFieldName(fieldName) {\n        const command = new UpdateCommand(this.operator, this.operands, fieldName);\n        return command;\n    }\n}\nexports.UpdateCommand = UpdateCommand;\nfunction isUpdateCommand(object) {\n    return object && (object instanceof UpdateCommand) && (object._internalType === symbol_1.SYMBOL_UPDATE_COMMAND);\n}\nexports.isUpdateCommand = isUpdateCommand;\nfunction isKnownUpdateCommand(object) {\n    return isUpdateCommand(object) && (object.operator.toUpperCase() in UPDATE_COMMANDS_LITERAL);\n}\nexports.isKnownUpdateCommand = isKnownUpdateCommand;\nexports.default = UpdateCommand;\n", "\nObject.defineProperty(exports, \"__esModule\", { value: true });\nconst query_1 = require(\"./commands/query\");\nconst logic_1 = require(\"./commands/logic\");\nconst update_1 = require(\"./commands/update\");\nexports.OperatorMap = {};\nfor (const key in query_1.QUERY_COMMANDS_LITERAL) {\n    exports.OperatorMap[key] = '$' + key;\n}\nfor (const key in logic_1.LOGIC_COMMANDS_LITERAL) {\n    exports.OperatorMap[key] = '$' + key;\n}\nfor (const key in update_1.UPDATE_COMMANDS_LITERAL) {\n    exports.OperatorMap[key] = '$' + key;\n}\nexports.OperatorMap[query_1.QUERY_COMMANDS_LITERAL.NEQ] = '$ne';\nexports.OperatorMap[update_1.UPDATE_COMMANDS_LITERAL.REMOVE] = '$unset';\nexports.OperatorMap[update_1.UPDATE_COMMANDS_LITERAL.SHIFT] = '$pop';\nexports.OperatorMap[update_1.UPDATE_COMMANDS_LITERAL.UNSHIFT] = '$push';\nfunction operatorToString(operator) {\n    return exports.OperatorMap[operator] || '$' + operator;\n}\nexports.operatorToString = operatorToString;\n", "\nObject.defineProperty(exports, \"__esModule\", { value: true });\nconst logic_1 = require(\"./logic\");\nconst symbol_1 = require(\"../helper/symbol\");\nconst index_1 = require(\"../geo/index\");\nconst type_1 = require(\"../utils/type\");\nexports.EQ = 'eq';\nexports.NEQ = 'neq';\nexports.GT = 'gt';\nexports.GTE = 'gte';\nexports.LT = 'lt';\nexports.LTE = 'lte';\nexports.IN = 'in';\nexports.NIN = 'nin';\nexports.ALL = 'all';\nexports.ELEM_MATCH = 'elemMatch';\nexports.EXISTS = 'exists';\nexports.SIZE = 'size';\nexports.MOD = 'mod';\nvar QUERY_COMMANDS_LITERAL;\n(function (QUERY_COMMANDS_LITERAL) {\n    QUERY_COMMANDS_LITERAL[\"EQ\"] = \"eq\";\n    QUERY_COMMANDS_LITERAL[\"NEQ\"] = \"neq\";\n    QUERY_COMMANDS_LITERAL[\"GT\"] = \"gt\";\n    QUERY_COMMANDS_LITERAL[\"GTE\"] = \"gte\";\n    QUERY_COMMANDS_LITERAL[\"LT\"] = \"lt\";\n    QUERY_COMMANDS_LITERAL[\"LTE\"] = \"lte\";\n    QUERY_COMMANDS_LITERAL[\"IN\"] = \"in\";\n    QUERY_COMMANDS_LITERAL[\"NIN\"] = \"nin\";\n    QUERY_COMMANDS_LITERAL[\"ALL\"] = \"all\";\n    QUERY_COMMANDS_LITERAL[\"ELEM_MATCH\"] = \"elemMatch\";\n    QUERY_COMMANDS_LITERAL[\"EXISTS\"] = \"exists\";\n    QUERY_COMMANDS_LITERAL[\"SIZE\"] = \"size\";\n    QUERY_COMMANDS_LITERAL[\"MOD\"] = \"mod\";\n    QUERY_COMMANDS_LITERAL[\"GEO_NEAR\"] = \"geoNear\";\n    QUERY_COMMANDS_LITERAL[\"GEO_WITHIN\"] = \"geoWithin\";\n    QUERY_COMMANDS_LITERAL[\"GEO_INTERSECTS\"] = \"geoIntersects\";\n})(QUERY_COMMANDS_LITERAL = exports.QUERY_COMMANDS_LITERAL || (exports.QUERY_COMMANDS_LITERAL = {}));\nclass QueryCommand extends logic_1.LogicCommand {\n    constructor(operator, operands, fieldName) {\n        super(operator, operands, fieldName);\n        this.operator = operator;\n        this._internalType = symbol_1.SYMBOL_QUERY_COMMAND;\n    }\n    toJSON() {\n        switch (this.operator) {\n            case QUERY_COMMANDS_LITERAL.IN:\n            case QUERY_COMMANDS_LITERAL.NIN:\n                return {\n                    ['$' + this.operator]: this.operands\n                };\n            default:\n                return {\n                    ['$' + this.operator]: this.operands[0]\n                };\n        }\n    }\n    _setFieldName(fieldName) {\n        const command = new QueryCommand(this.operator, this.operands, fieldName);\n        return command;\n    }\n    eq(val) {\n        const command = new QueryCommand(QUERY_COMMANDS_LITERAL.EQ, [val], this.fieldName);\n        return this.and(command);\n    }\n    neq(val) {\n        const command = new QueryCommand(QUERY_COMMANDS_LITERAL.NEQ, [val], this.fieldName);\n        return this.and(command);\n    }\n    gt(val) {\n        const command = new QueryCommand(QUERY_COMMANDS_LITERAL.GT, [val], this.fieldName);\n        return this.and(command);\n    }\n    gte(val) {\n        const command = new QueryCommand(QUERY_COMMANDS_LITERAL.GTE, [val], this.fieldName);\n        return this.and(command);\n    }\n    lt(val) {\n        const command = new QueryCommand(QUERY_COMMANDS_LITERAL.LT, [val], this.fieldName);\n        return this.and(command);\n    }\n    lte(val) {\n        const command = new QueryCommand(QUERY_COMMANDS_LITERAL.LTE, [val], this.fieldName);\n        return this.and(command);\n    }\n    in(list) {\n        const command = new QueryCommand(QUERY_COMMANDS_LITERAL.IN, list, this.fieldName);\n        return this.and(command);\n    }\n    nin(list) {\n        const command = new QueryCommand(QUERY_COMMANDS_LITERAL.NIN, list, this.fieldName);\n        return this.and(command);\n    }\n    geoNear(val) {\n        if (!(val.geometry instanceof index_1.Point)) {\n            throw new TypeError(`\"geometry\" must be of type Point. Received type ${typeof val.geometry}`);\n        }\n        if (val.maxDistance !== undefined && !type_1.isNumber(val.maxDistance)) {\n            throw new TypeError(`\"maxDistance\" must be of type Number. Received type ${typeof val.maxDistance}`);\n        }\n        if (val.minDistance !== undefined && !type_1.isNumber(val.minDistance)) {\n            throw new TypeError(`\"minDistance\" must be of type Number. Received type ${typeof val.minDistance}`);\n        }\n        const command = new QueryCommand(QUERY_COMMANDS_LITERAL.GEO_NEAR, [val], this.fieldName);\n        return this.and(command);\n    }\n    geoWithin(val) {\n        if (!(val.geometry instanceof index_1.MultiPolygon) && !(val.geometry instanceof index_1.Polygon)) {\n            throw new TypeError(`\"geometry\" must be of type Polygon or MultiPolygon. Received type ${typeof val.geometry}`);\n        }\n        const command = new QueryCommand(QUERY_COMMANDS_LITERAL.GEO_WITHIN, [val], this.fieldName);\n        return this.and(command);\n    }\n    geoIntersects(val) {\n        if (!(val.geometry instanceof index_1.Point) &&\n            !(val.geometry instanceof index_1.LineString) &&\n            !(val.geometry instanceof index_1.Polygon) &&\n            !(val.geometry instanceof index_1.MultiPoint) &&\n            !(val.geometry instanceof index_1.MultiLineString) &&\n            !(val.geometry instanceof index_1.MultiPolygon)) {\n            throw new TypeError(`\"geometry\" must be of type Point, LineString, Polygon, MultiPoint, MultiLineString or MultiPolygon. Received type ${typeof val.geometry}`);\n        }\n        const command = new QueryCommand(QUERY_COMMANDS_LITERAL.GEO_INTERSECTS, [val], this.fieldName);\n        return this.and(command);\n    }\n}\nexports.QueryCommand = QueryCommand;\nfunction isQueryCommand(object) {\n    return object && object instanceof QueryCommand && object._internalType === symbol_1.SYMBOL_QUERY_COMMAND;\n}\nexports.isQueryCommand = isQueryCommand;\nfunction isKnownQueryCommand(object) {\n    return isQueryCommand(object) && object.operator.toUpperCase() in QUERY_COMMANDS_LITERAL;\n}\nexports.isKnownQueryCommand = isKnownQueryCommand;\nfunction isComparisonCommand(object) {\n    return isQueryCommand(object);\n}\nexports.isComparisonCommand = isComparisonCommand;\nexports.default = QueryCommand;\n", "\nObject.defineProperty(exports, \"__esModule\", { value: true });\nconst symbol_1 = require(\"../helper/symbol\");\nconst query_1 = require(\"./query\");\nexports.AND = 'and';\nexports.OR = 'or';\nexports.NOT = 'not';\nexports.NOR = 'nor';\nvar LOGIC_COMMANDS_LITERAL;\n(function (LOGIC_COMMANDS_LITERAL) {\n    LOGIC_COMMANDS_LITERAL[\"AND\"] = \"and\";\n    LOGIC_COMMANDS_LITERAL[\"OR\"] = \"or\";\n    LOGIC_COMMANDS_LITERAL[\"NOT\"] = \"not\";\n    LOGIC_COMMANDS_LITERAL[\"NOR\"] = \"nor\";\n})(LOGIC_COMMANDS_LITERAL = exports.LOGIC_COMMANDS_LITERAL || (exports.LOGIC_COMMANDS_LITERAL = {}));\nclass LogicCommand {\n    constructor(operator, operands, fieldName) {\n        this._internalType = symbol_1.SYMBOL_LOGIC_COMMAND;\n        Object.defineProperties(this, {\n            _internalType: {\n                enumerable: false,\n                configurable: false,\n            },\n        });\n        this.operator = operator;\n        this.operands = operands;\n        this.fieldName = fieldName || symbol_1.SYMBOL_UNSET_FIELD_NAME;\n        if (this.fieldName !== symbol_1.SYMBOL_UNSET_FIELD_NAME) {\n            if (Array.isArray(operands)) {\n                operands = operands.slice();\n                this.operands = operands;\n                for (let i = 0, len = operands.length; i < len; i++) {\n                    const query = operands[i];\n                    if (isLogicCommand(query) || query_1.isQueryCommand(query)) {\n                        operands[i] = query._setFieldName(this.fieldName);\n                    }\n                }\n            }\n            else {\n                const query = operands;\n                if (isLogicCommand(query) || query_1.isQueryCommand(query)) {\n                    operands = query._setFieldName(this.fieldName);\n                }\n            }\n        }\n    }\n    _setFieldName(fieldName) {\n        const operands = this.operands.map(operand => {\n            if (operand instanceof LogicCommand) {\n                return operand._setFieldName(fieldName);\n            }\n            else {\n                return operand;\n            }\n        });\n        const command = new LogicCommand(this.operator, operands, fieldName);\n        return command;\n    }\n    and(...__expressions__) {\n        const expressions = Array.isArray(arguments[0]) ? arguments[0] : Array.from(arguments);\n        expressions.unshift(this);\n        return new LogicCommand(LOGIC_COMMANDS_LITERAL.AND, expressions, this.fieldName);\n    }\n    or(...__expressions__) {\n        const expressions = Array.isArray(arguments[0]) ? arguments[0] : Array.from(arguments);\n        expressions.unshift(this);\n        return new LogicCommand(LOGIC_COMMANDS_LITERAL.OR, expressions, this.fieldName);\n    }\n}\nexports.LogicCommand = LogicCommand;\nfunction isLogicCommand(object) {\n    return object && (object instanceof LogicCommand) && (object._internalType === symbol_1.SYMBOL_LOGIC_COMMAND);\n}\nexports.isLogicCommand = isLogicCommand;\nfunction isKnownLogicCommand(object) {\n    return isLogicCommand && (object.operator.toUpperCase() in LOGIC_COMMANDS_LITERAL);\n}\nexports.isKnownLogicCommand = isKnownLogicCommand;\nexports.default = LogicCommand;\n", "\nObject.defineProperty(exports, \"__esModule\", { value: true });\nconst type_1 = require(\"../utils/type\");\nconst datatype_1 = require(\"./datatype\");\nfunction flatten(query, shouldPreserverObject, parents, visited) {\n    const cloned = Object.assign({}, query);\n    for (const key in query) {\n        if (/^\\$/.test(key))\n            continue;\n        const value = query[key];\n        if (!value)\n            continue;\n        if (type_1.isObject(value) && !shouldPreserverObject(value)) {\n            if (visited.indexOf(value) > -1) {\n                throw new Error('Cannot convert circular structure to JSON');\n            }\n            const newParents = [\n                ...parents,\n                key,\n            ];\n            const newVisited = [\n                ...visited,\n                value,\n            ];\n            const flattenedChild = flatten(value, shouldPreserverObject, newParents, newVisited);\n            cloned[key] = flattenedChild;\n            let hasKeyNotCombined = false;\n            for (const childKey in flattenedChild) {\n                if (!/^\\$/.test(childKey)) {\n                    cloned[`${key}.${childKey}`] = flattenedChild[childKey];\n                    delete cloned[key][childKey];\n                }\n                else {\n                    hasKeyNotCombined = true;\n                }\n            }\n            if (!hasKeyNotCombined) {\n                delete cloned[key];\n            }\n        }\n    }\n    return cloned;\n}\nfunction flattenQueryObject(query) {\n    return flatten(query, isConversionRequired, [], [query]);\n}\nexports.flattenQueryObject = flattenQueryObject;\nfunction flattenObject(object) {\n    return flatten(object, (_) => false, [], [object]);\n}\nexports.flattenObject = flattenObject;\nfunction mergeConditionAfterEncode(query, condition, key) {\n    if (!condition[key]) {\n        delete query[key];\n    }\n    for (const conditionKey in condition) {\n        if (query[conditionKey]) {\n            if (type_1.isArray(query[conditionKey])) {\n                query[conditionKey].push(condition[conditionKey]);\n            }\n            else if (type_1.isObject(query[conditionKey])) {\n                if (type_1.isObject(condition[conditionKey])) {\n                    Object.assign(query[conditionKey], condition[conditionKey]);\n                }\n                else {\n                    console.warn(`unmergable condition, query is object but condition is ${type_1.getType(condition)}, can only overwrite`, condition, key);\n                    query[conditionKey] = condition[conditionKey];\n                }\n            }\n            else {\n                console.warn(`to-merge query is of type ${type_1.getType(query)}, can only overwrite`, query, condition, key);\n                query[conditionKey] = condition[conditionKey];\n            }\n        }\n        else {\n            query[conditionKey] = condition[conditionKey];\n        }\n    }\n}\nexports.mergeConditionAfterEncode = mergeConditionAfterEncode;\nfunction isConversionRequired(val) {\n    return type_1.isInternalObject(val) || type_1.isDate(val) || type_1.isRegExp(val);\n}\nexports.isConversionRequired = isConversionRequired;\nfunction encodeInternalDataType(val) {\n    return datatype_1.serialize(val);\n}\nexports.encodeInternalDataType = encodeInternalDataType;\nfunction decodeInternalDataType(object) {\n    return datatype_1.deserialize(object);\n}\nexports.decodeInternalDataType = decodeInternalDataType;\n", "\nObject.defineProperty(exports, \"__esModule\", { value: true });\nconst symbol_1 = require(\"../helper/symbol\");\nconst type_1 = require(\"../utils/type\");\nconst index_1 = require(\"../geo/index\");\nconst index_2 = require(\"../serverDate/index\");\nfunction serialize(val) {\n    return serializeHelper(val, [val]);\n}\nexports.serialize = serialize;\nfunction serializeHelper(val, visited) {\n    if (type_1.isInternalObject(val)) {\n        switch (val._internalType) {\n            case symbol_1.SYMBOL_GEO_POINT: {\n                return val.toJSON();\n            }\n            case symbol_1.SYMBOL_SERVER_DATE: {\n                return val.parse();\n            }\n            case symbol_1.SYMBOL_REGEXP: {\n                return val.parse();\n            }\n            default: {\n                return val.toJSON ? val.toJSON() : val;\n            }\n        }\n    }\n    else if (type_1.isDate(val)) {\n        return {\n            $date: +val,\n        };\n    }\n    else if (type_1.isRegExp(val)) {\n        return {\n            $regex: val.source,\n            $options: val.flags,\n        };\n    }\n    else if (type_1.isArray(val)) {\n        return val.map(item => {\n            if (visited.indexOf(item) > -1) {\n                throw new Error('Cannot convert circular structure to JSON');\n            }\n            return serializeHelper(item, [\n                ...visited,\n                item,\n            ]);\n        });\n    }\n    else if (type_1.isObject(val)) {\n        const ret = Object.assign({}, val);\n        for (const key in ret) {\n            if (visited.indexOf(ret[key]) > -1) {\n                throw new Error('Cannot convert circular structure to JSON');\n            }\n            ret[key] = serializeHelper(ret[key], [\n                ...visited,\n                ret[key],\n            ]);\n        }\n        return ret;\n    }\n    else {\n        return val;\n    }\n}\nfunction deserialize(object) {\n    const ret = Object.assign({}, object);\n    for (const key in ret) {\n        switch (key) {\n            case '$date': {\n                switch (type_1.getType(ret[key])) {\n                    case 'number': {\n                        return new Date(ret[key]);\n                    }\n                    case 'object': {\n                        return new index_2.ServerDate(ret[key]);\n                    }\n                }\n                break;\n            }\n            case 'type': {\n                switch (ret.type) {\n                    case 'Point': {\n                        if (type_1.isArray(ret.coordinates) && type_1.isNumber(ret.coordinates[0]) && type_1.isNumber(ret.coordinates[1])) {\n                            return new index_1.Point(ret.coordinates[0], ret.coordinates[1]);\n                        }\n                        break;\n                    }\n                }\n                break;\n            }\n        }\n    }\n    return object;\n}\nexports.deserialize = deserialize;\n", "\nObject.defineProperty(exports, \"__esModule\", { value: true });\nconst virtual_websocket_client_1 = require(\"./virtual-websocket-client\");\nconst utils_1 = require(\"../utils/utils\");\nconst message_1 = require(\"./message\");\nconst ws_event_1 = require(\"./ws-event\");\nconst error_1 = require(\"../utils/error\");\nconst error_2 = require(\"./error\");\nconst error_config_1 = require(\"../config/error.config\");\nconst __1 = require(\"../\");\nconst WS_READY_STATE = {\n    CONNECTING: 0,\n    OPEN: 1,\n    CLOSING: 2,\n    CLOSED: 3\n};\nconst MAX_RTT_OBSERVED = 3;\nconst DEFAULT_EXPECTED_EVENT_WAIT_TIME = 5000;\nconst DEFAULT_UNTRUSTED_RTT_THRESHOLD = 10000;\nconst DEFAULT_MAX_RECONNECT = 5;\nconst DEFAULT_WS_RECONNECT_INTERVAL = 10000;\nconst DEFAULT_PING_FAIL_TOLERANCE = 2;\nconst DEFAULT_PONG_MISS_TOLERANCE = 2;\nconst DEFAULT_LOGIN_TIMEOUT = 5000;\nclass RealtimeWebSocketClient {\n    constructor(options) {\n        this._virtualWSClient = new Set();\n        this._queryIdClientMap = new Map();\n        this._watchIdClientMap = new Map();\n        this._pingFailed = 0;\n        this._pongMissed = 0;\n        this._logins = new Map();\n        this._wsReadySubsribers = [];\n        this._wsResponseWait = new Map();\n        this._rttObserved = [];\n        this.initWebSocketConnection = async (reconnect, availableRetries = this._maxReconnect) => {\n            if (reconnect && this._reconnectState) {\n                return;\n            }\n            if (reconnect) {\n                this._reconnectState = true;\n            }\n            if (this._wsInitPromise) {\n                return this._wsInitPromise;\n            }\n            if (reconnect) {\n                this.pauseClients();\n            }\n            this.close(ws_event_1.CLOSE_EVENT_CODE.ReconnectWebSocket);\n            this._wsInitPromise = new Promise(async (resolve, reject) => {\n                try {\n                    const wsSign = await this.getWsSign();\n                    await new Promise(success => {\n                        const url = wsSign.wsUrl || 'wss://tcb-ws.tencentcloudapi.com';\n                        this._ws = __1.Db.wsClass ? new __1.Db.wsClass(url) : new WebSocket(url);\n                        success();\n                    });\n                    if (this._ws.connect) {\n                        await this._ws.connect();\n                    }\n                    await this.initWebSocketEvent();\n                    resolve();\n                    if (reconnect) {\n                        this.resumeClients();\n                        this._reconnectState = false;\n                    }\n                }\n                catch (e) {\n                    console.error('[realtime] initWebSocketConnection connect fail', e);\n                    if (availableRetries > 0) {\n                        const isConnected = true;\n                        this._wsInitPromise = undefined;\n                        if (isConnected) {\n                            await utils_1.sleep(this._reconnectInterval);\n                            if (reconnect) {\n                                this._reconnectState = false;\n                            }\n                        }\n                        resolve(this.initWebSocketConnection(reconnect, availableRetries - 1));\n                    }\n                    else {\n                        reject(e);\n                        if (reconnect) {\n                            this.closeAllClients(new error_1.CloudSDKError({\n                                errCode: error_config_1.ERR_CODE.SDK_DATABASE_REALTIME_LISTENER_RECONNECT_WATCH_FAIL,\n                                errMsg: e\n                            }));\n                        }\n                    }\n                }\n            });\n            try {\n                await this._wsInitPromise;\n                this._wsReadySubsribers.forEach(({ resolve }) => resolve());\n            }\n            catch (e) {\n                this._wsReadySubsribers.forEach(({ reject }) => reject());\n            }\n            finally {\n                this._wsInitPromise = undefined;\n                this._wsReadySubsribers = [];\n            }\n        };\n        this.initWebSocketEvent = () => new Promise((resolve, reject) => {\n            if (!this._ws) {\n                throw new Error('can not initWebSocketEvent, ws not exists');\n            }\n            let wsOpened = false;\n            this._ws.onopen = event => {\n                console.warn('[realtime] ws event: open', event);\n                wsOpened = true;\n                resolve();\n            };\n            this._ws.onerror = event => {\n                this._logins = new Map();\n                if (!wsOpened) {\n                    console.error('[realtime] ws open failed with ws event: error', event);\n                    reject(event);\n                }\n                else {\n                    console.error('[realtime] ws event: error', event);\n                    this.clearHeartbeat();\n                    this._virtualWSClient.forEach(client => client.closeWithError(new error_1.CloudSDKError({\n                        errCode: error_config_1.ERR_CODE.SDK_DATABASE_REALTIME_LISTENER_WEBSOCKET_CONNECTION_ERROR,\n                        errMsg: event\n                    })));\n                }\n            };\n            this._ws.onclose = closeEvent => {\n                console.warn('[realtime] ws event: close', closeEvent);\n                this._logins = new Map();\n                this.clearHeartbeat();\n                switch (closeEvent.code) {\n                    case ws_event_1.CLOSE_EVENT_CODE.ReconnectWebSocket: {\n                        break;\n                    }\n                    case ws_event_1.CLOSE_EVENT_CODE.NoRealtimeListeners: {\n                        break;\n                    }\n                    case ws_event_1.CLOSE_EVENT_CODE.HeartbeatPingError:\n                    case ws_event_1.CLOSE_EVENT_CODE.HeartbeatPongTimeoutError:\n                    case ws_event_1.CLOSE_EVENT_CODE.NormalClosure:\n                    case ws_event_1.CLOSE_EVENT_CODE.AbnormalClosure: {\n                        if (this._maxReconnect > 0) {\n                            this.initWebSocketConnection(true, this._maxReconnect);\n                        }\n                        else {\n                            this.closeAllClients(ws_event_1.getWSCloseError(closeEvent.code));\n                        }\n                        break;\n                    }\n                    case ws_event_1.CLOSE_EVENT_CODE.NoAuthentication: {\n                        this.closeAllClients(ws_event_1.getWSCloseError(closeEvent.code, closeEvent.reason));\n                        break;\n                    }\n                    default: {\n                        if (this._maxReconnect > 0) {\n                            this.initWebSocketConnection(true, this._maxReconnect);\n                        }\n                        else {\n                            this.closeAllClients(ws_event_1.getWSCloseError(closeEvent.code));\n                        }\n                    }\n                }\n            };\n            this._ws.onmessage = res => {\n                const rawMsg = res.data;\n                this.heartbeat();\n                let msg;\n                try {\n                    msg = JSON.parse(rawMsg);\n                }\n                catch (e) {\n                    throw new Error(`[realtime] onMessage parse res.data error: ${e}`);\n                }\n                if (msg.msgType === 'ERROR') {\n                    let virtualWatch = null;\n                    this._virtualWSClient.forEach(item => {\n                        if (item.watchId === msg.watchId) {\n                            virtualWatch = item;\n                        }\n                    });\n                    if (virtualWatch) {\n                        virtualWatch.listener.onError(msg);\n                    }\n                }\n                const responseWaitSpec = this._wsResponseWait.get(msg.requestId);\n                if (responseWaitSpec) {\n                    try {\n                        if (msg.msgType === 'ERROR') {\n                            responseWaitSpec.reject(new error_2.RealtimeErrorMessageError(msg));\n                        }\n                        else {\n                            responseWaitSpec.resolve(msg);\n                        }\n                    }\n                    catch (e) {\n                        console.error('ws onMessage responseWaitSpec.resolve(msg) errored:', e);\n                    }\n                    finally {\n                        this._wsResponseWait.delete(msg.requestId);\n                    }\n                    if (responseWaitSpec.skipOnMessage) {\n                        return;\n                    }\n                }\n                if (msg.msgType === 'PONG') {\n                    if (this._lastPingSendTS) {\n                        const rtt = Date.now() - this._lastPingSendTS;\n                        if (rtt > DEFAULT_UNTRUSTED_RTT_THRESHOLD) {\n                            console.warn(`[realtime] untrusted rtt observed: ${rtt}`);\n                            return;\n                        }\n                        if (this._rttObserved.length >= MAX_RTT_OBSERVED) {\n                            this._rttObserved.splice(0, this._rttObserved.length - MAX_RTT_OBSERVED + 1);\n                        }\n                        this._rttObserved.push(rtt);\n                    }\n                    return;\n                }\n                let client = msg.watchId && this._watchIdClientMap.get(msg.watchId);\n                if (client) {\n                    client.onMessage(msg);\n                }\n                else {\n                    console.error(`[realtime] no realtime listener found responsible for watchId ${msg.watchId}: `, msg);\n                    switch (msg.msgType) {\n                        case 'INIT_EVENT':\n                        case 'NEXT_EVENT':\n                        case 'CHECK_EVENT': {\n                            client = this._queryIdClientMap.get(msg.msgData.queryID);\n                            if (client) {\n                                client.onMessage(msg);\n                            }\n                            break;\n                        }\n                        default: {\n                            for (const [, client] of this._watchIdClientMap) {\n                                client.onMessage(msg);\n                                break;\n                            }\n                        }\n                    }\n                }\n            };\n            this.heartbeat();\n        });\n        this.isWSConnected = () => {\n            return Boolean(this._ws && this._ws.readyState === WS_READY_STATE.OPEN);\n        };\n        this.onceWSConnected = async () => {\n            if (this.isWSConnected()) {\n                return;\n            }\n            if (this._wsInitPromise) {\n                return this._wsInitPromise;\n            }\n            return new Promise((resolve, reject) => {\n                this._wsReadySubsribers.push({\n                    resolve,\n                    reject\n                });\n            });\n        };\n        this.webLogin = async (envId, refresh) => {\n            if (!refresh) {\n                if (envId) {\n                    const loginInfo = this._logins.get(envId);\n                    if (loginInfo) {\n                        if (loginInfo.loggedIn && loginInfo.loginResult) {\n                            return loginInfo.loginResult;\n                        }\n                        else if (loginInfo.loggingInPromise) {\n                            return loginInfo.loggingInPromise;\n                        }\n                    }\n                }\n                else {\n                    const emptyEnvLoginInfo = this._logins.get('');\n                    if (emptyEnvLoginInfo && emptyEnvLoginInfo.loggingInPromise) {\n                        return emptyEnvLoginInfo.loggingInPromise;\n                    }\n                }\n            }\n            const promise = new Promise(async (resolve, reject) => {\n                try {\n                    const wsSign = await this.getWsSign();\n                    const msgData = {\n                        envId: wsSign.envId || '',\n                        accessToken: '',\n                        referrer: 'web',\n                        sdkVersion: '',\n                        dataVersion: __1.Db.dataVersion || ''\n                    };\n                    const loginMsg = {\n                        watchId: undefined,\n                        requestId: message_1.genRequestId(),\n                        msgType: 'LOGIN',\n                        msgData,\n                        exMsgData: {\n                            runtime: __1.Db.runtime,\n                            signStr: wsSign.signStr,\n                            secretVersion: wsSign.secretVersion\n                        }\n                    };\n                    const loginResMsg = await this.send({\n                        msg: loginMsg,\n                        waitResponse: true,\n                        skipOnMessage: true,\n                        timeout: DEFAULT_LOGIN_TIMEOUT\n                    });\n                    if (!loginResMsg.msgData.code) {\n                        resolve({\n                            envId: wsSign.envId\n                        });\n                    }\n                    else {\n                        reject(new Error(`${loginResMsg.msgData.code} ${loginResMsg.msgData.message}`));\n                    }\n                }\n                catch (e) {\n                    reject(e);\n                }\n            });\n            let loginInfo = envId && this._logins.get(envId);\n            const loginStartTS = Date.now();\n            if (loginInfo) {\n                loginInfo.loggedIn = false;\n                loginInfo.loggingInPromise = promise;\n                loginInfo.loginStartTS = loginStartTS;\n            }\n            else {\n                loginInfo = {\n                    loggedIn: false,\n                    loggingInPromise: promise,\n                    loginStartTS\n                };\n                this._logins.set(envId || '', loginInfo);\n            }\n            try {\n                const loginResult = await promise;\n                const curLoginInfo = envId && this._logins.get(envId);\n                if (curLoginInfo &&\n                    curLoginInfo === loginInfo &&\n                    curLoginInfo.loginStartTS === loginStartTS) {\n                    loginInfo.loggedIn = true;\n                    loginInfo.loggingInPromise = undefined;\n                    loginInfo.loginStartTS = undefined;\n                    loginInfo.loginResult = loginResult;\n                    return loginResult;\n                }\n                else if (curLoginInfo) {\n                    if (curLoginInfo.loggedIn && curLoginInfo.loginResult) {\n                        return curLoginInfo.loginResult;\n                    }\n                    else if (curLoginInfo.loggingInPromise) {\n                        return curLoginInfo.loggingInPromise;\n                    }\n                    else {\n                        throw new Error('ws unexpected login info');\n                    }\n                }\n                else {\n                    throw new Error('ws login info reset');\n                }\n            }\n            catch (e) {\n                loginInfo.loggedIn = false;\n                loginInfo.loggingInPromise = undefined;\n                loginInfo.loginStartTS = undefined;\n                loginInfo.loginResult = undefined;\n                throw e;\n            }\n        };\n        this.getWsSign = async () => {\n            if (this._wsSign && this._wsSign.expiredTs > Date.now()) {\n                return this._wsSign;\n            }\n            const expiredTs = Date.now() + 60000;\n            const res = await this._context.appConfig.request.send('auth.wsWebSign', { runtime: __1.Db.runtime });\n            if (res.code) {\n                throw new Error(`[tcb-js-sdk] 获取实时数据推送登录票据失败: ${res.code}`);\n            }\n            if (res.data) {\n                const { signStr, wsUrl, secretVersion, envId } = res.data;\n                return {\n                    signStr,\n                    wsUrl,\n                    secretVersion,\n                    envId,\n                    expiredTs\n                };\n            }\n            else {\n                throw new Error('[tcb-js-sdk] 获取实时数据推送登录票据失败');\n            }\n        };\n        this.getWaitExpectedTimeoutLength = () => {\n            if (!this._rttObserved.length) {\n                return DEFAULT_EXPECTED_EVENT_WAIT_TIME;\n            }\n            return ((this._rttObserved.reduce((acc, cur) => acc + cur) /\n                this._rttObserved.length) *\n                1.5);\n        };\n        this.ping = async () => {\n            const msg = {\n                watchId: undefined,\n                requestId: message_1.genRequestId(),\n                msgType: 'PING',\n                msgData: null\n            };\n            await this.send({\n                msg\n            });\n        };\n        this.send = async (opts) => new Promise(async (_resolve, _reject) => {\n            let timeoutId;\n            let _hasResolved = false;\n            let _hasRejected = false;\n            const resolve = (value) => {\n                _hasResolved = true;\n                timeoutId && clearTimeout(timeoutId);\n                _resolve(value);\n            };\n            const reject = (error) => {\n                _hasRejected = true;\n                timeoutId && clearTimeout(timeoutId);\n                _reject(error);\n            };\n            if (opts.timeout) {\n                timeoutId = setTimeout(async () => {\n                    if (!_hasResolved || !_hasRejected) {\n                        await utils_1.sleep(0);\n                        if (!_hasResolved || !_hasRejected) {\n                            reject(new error_1.TimeoutError('wsclient.send timedout'));\n                        }\n                    }\n                }, opts.timeout);\n            }\n            try {\n                if (this._wsInitPromise) {\n                    await this._wsInitPromise;\n                }\n                if (!this._ws) {\n                    reject(new Error('invalid state: ws connection not exists, can not send message'));\n                    return;\n                }\n                if (this._ws.readyState !== WS_READY_STATE.OPEN) {\n                    reject(new Error(`ws readyState invalid: ${this._ws.readyState}, can not send message`));\n                    return;\n                }\n                if (opts.waitResponse) {\n                    this._wsResponseWait.set(opts.msg.requestId, {\n                        resolve,\n                        reject,\n                        skipOnMessage: opts.skipOnMessage\n                    });\n                }\n                try {\n                    await this._ws.send(JSON.stringify(opts.msg));\n                    if (!opts.waitResponse) {\n                        resolve();\n                    }\n                }\n                catch (err) {\n                    if (err) {\n                        reject(err);\n                        if (opts.waitResponse) {\n                            this._wsResponseWait.delete(opts.msg.requestId);\n                        }\n                    }\n                }\n            }\n            catch (e) {\n                reject(e);\n            }\n        });\n        this.closeAllClients = (error) => {\n            this._virtualWSClient.forEach(client => {\n                client.closeWithError(error);\n            });\n        };\n        this.pauseClients = (clients) => {\n            ;\n            (clients || this._virtualWSClient).forEach(client => {\n                client.pause();\n            });\n        };\n        this.resumeClients = (clients) => {\n            ;\n            (clients || this._virtualWSClient).forEach(client => {\n                client.resume();\n            });\n        };\n        this.onWatchStart = (client, queryID) => {\n            this._queryIdClientMap.set(queryID, client);\n        };\n        this.onWatchClose = (client, queryID) => {\n            if (queryID) {\n                this._queryIdClientMap.delete(queryID);\n            }\n            this._watchIdClientMap.delete(client.watchId);\n            this._virtualWSClient.delete(client);\n            if (!this._virtualWSClient.size) {\n                this.close(ws_event_1.CLOSE_EVENT_CODE.NoRealtimeListeners);\n            }\n        };\n        this._maxReconnect = options.maxReconnect || DEFAULT_MAX_RECONNECT;\n        this._reconnectInterval =\n            options.reconnectInterval || DEFAULT_WS_RECONNECT_INTERVAL;\n        this._context = options.context;\n    }\n    heartbeat(immediate) {\n        this.clearHeartbeat();\n        this._pingTimeoutId = setTimeout(async () => {\n            try {\n                if (!this._ws || this._ws.readyState !== WS_READY_STATE.OPEN) {\n                    return;\n                }\n                this._lastPingSendTS = Date.now();\n                await this.ping();\n                this._pingFailed = 0;\n                this._pongTimeoutId = setTimeout(() => {\n                    console.error('pong timed out');\n                    if (this._pongMissed < DEFAULT_PONG_MISS_TOLERANCE) {\n                        this._pongMissed++;\n                        this.heartbeat(true);\n                    }\n                    else {\n                        this.initWebSocketConnection(true);\n                    }\n                }, this._context.appConfig.realtimePongWaitTimeout);\n            }\n            catch (e) {\n                if (this._pingFailed < DEFAULT_PING_FAIL_TOLERANCE) {\n                    this._pingFailed++;\n                    this.heartbeat();\n                }\n                else {\n                    this.close(ws_event_1.CLOSE_EVENT_CODE.HeartbeatPingError);\n                }\n            }\n        }, immediate ? 0 : this._context.appConfig.realtimePingInterval);\n    }\n    clearHeartbeat() {\n        this._pingTimeoutId && clearTimeout(this._pingTimeoutId);\n        this._pongTimeoutId && clearTimeout(this._pongTimeoutId);\n    }\n    close(code) {\n        this.clearHeartbeat();\n        if (this._ws) {\n            this._ws.close(code, ws_event_1.CLOSE_EVENT_CODE_INFO[code].name);\n            this._ws = undefined;\n        }\n    }\n    watch(options) {\n        if (!this._ws && !this._wsInitPromise) {\n            this.initWebSocketConnection(false);\n        }\n        const virtualClient = new virtual_websocket_client_1.VirtualWebSocketClient(Object.assign(Object.assign({}, options), { send: this.send, login: this.webLogin, isWSConnected: this.isWSConnected, onceWSConnected: this.onceWSConnected, getWaitExpectedTimeoutLength: this.getWaitExpectedTimeoutLength, onWatchStart: this.onWatchStart, onWatchClose: this.onWatchClose, debug: true }));\n        this._virtualWSClient.add(virtualClient);\n        this._watchIdClientMap.set(virtualClient.watchId, virtualClient);\n        return virtualClient.listener;\n    }\n}\nexports.RealtimeWebSocketClient = RealtimeWebSocketClient;\n", "\nObject.defineProperty(exports, \"__esModule\", { value: true });\nconst lodash_set_1 = require(\"lodash.set\");\nconst lodash_unset_1 = require(\"lodash.unset\");\nconst lodash_clonedeep_1 = require(\"lodash.clonedeep\");\nconst message_1 = require(\"./message\");\nconst error_1 = require(\"../utils/error\");\nconst error_config_1 = require(\"../config/error.config\");\nconst utils_1 = require(\"../utils/utils\");\nconst listener_1 = require(\"./listener\");\nconst snapshot_1 = require(\"./snapshot\");\nconst error_2 = require(\"./error\");\nvar WATCH_STATUS;\n(function (WATCH_STATUS) {\n    WATCH_STATUS[\"LOGGINGIN\"] = \"LOGGINGIN\";\n    WATCH_STATUS[\"INITING\"] = \"INITING\";\n    WATCH_STATUS[\"REBUILDING\"] = \"REBUILDING\";\n    WATCH_STATUS[\"ACTIVE\"] = \"ACTIVE\";\n    WATCH_STATUS[\"ERRORED\"] = \"ERRORED\";\n    WATCH_STATUS[\"CLOSING\"] = \"CLOSING\";\n    WATCH_STATUS[\"CLOSED\"] = \"CLOSED\";\n    WATCH_STATUS[\"PAUSED\"] = \"PAUSED\";\n    WATCH_STATUS[\"RESUMING\"] = \"RESUMING\";\n})(WATCH_STATUS || (WATCH_STATUS = {}));\nconst DEFAULT_WAIT_TIME_ON_UNKNOWN_ERROR = 100;\nconst DEFAULT_MAX_AUTO_RETRY_ON_ERROR = 2;\nconst DEFAULT_MAX_SEND_ACK_AUTO_RETRY_ON_ERROR = 2;\nconst DEFAULT_SEND_ACK_DEBOUNCE_TIMEOUT = 10 * 1000;\nconst DEFAULT_INIT_WATCH_TIMEOUT = 10 * 1000;\nconst DEFAULT_REBUILD_WATCH_TIMEOUT = 10 * 1000;\nclass VirtualWebSocketClient {\n    constructor(options) {\n        this.watchStatus = WATCH_STATUS.INITING;\n        this._login = async (envId, refresh) => {\n            this.watchStatus = WATCH_STATUS.LOGGINGIN;\n            const loginResult = await this.login(envId, refresh);\n            if (!this.envId) {\n                this.envId = loginResult.envId;\n            }\n            return loginResult;\n        };\n        this.initWatch = async (forceRefreshLogin) => {\n            if (this._initWatchPromise) {\n                return this._initWatchPromise;\n            }\n            this._initWatchPromise = new Promise(async (resolve, reject) => {\n                try {\n                    if (this.watchStatus === WATCH_STATUS.PAUSED) {\n                        console.log('[realtime] initWatch cancelled on pause');\n                        return resolve();\n                    }\n                    const { envId } = await this._login(this.envId, forceRefreshLogin);\n                    if (this.watchStatus === WATCH_STATUS.PAUSED) {\n                        console.log('[realtime] initWatch cancelled on pause');\n                        return resolve();\n                    }\n                    this.watchStatus = WATCH_STATUS.INITING;\n                    const initWatchMsg = {\n                        watchId: this.watchId,\n                        requestId: message_1.genRequestId(),\n                        msgType: 'INIT_WATCH',\n                        msgData: {\n                            envId,\n                            collName: this.collectionName,\n                            query: this.query,\n                            limit: this.limit,\n                            orderBy: this.orderBy\n                        }\n                    };\n                    const initEventMsg = await this.send({\n                        msg: initWatchMsg,\n                        waitResponse: true,\n                        skipOnMessage: true,\n                        timeout: DEFAULT_INIT_WATCH_TIMEOUT\n                    });\n                    const { events, currEvent } = initEventMsg.msgData;\n                    this.sessionInfo = {\n                        queryID: initEventMsg.msgData.queryID,\n                        currentEventId: currEvent - 1,\n                        currentDocs: []\n                    };\n                    if (events.length > 0) {\n                        for (const e of events) {\n                            e.ID = currEvent;\n                        }\n                        this.handleServerEvents(initEventMsg);\n                    }\n                    else {\n                        this.sessionInfo.currentEventId = currEvent;\n                        const snapshot = new snapshot_1.Snapshot({\n                            id: currEvent,\n                            docChanges: [],\n                            docs: [],\n                            type: 'init'\n                        });\n                        this.listener.onChange(snapshot);\n                        this.scheduleSendACK();\n                    }\n                    this.onWatchStart(this, this.sessionInfo.queryID);\n                    this.watchStatus = WATCH_STATUS.ACTIVE;\n                    this._availableRetries.INIT_WATCH = DEFAULT_MAX_AUTO_RETRY_ON_ERROR;\n                    resolve();\n                }\n                catch (e) {\n                    this.handleWatchEstablishmentError(e, {\n                        operationName: 'INIT_WATCH',\n                        resolve,\n                        reject\n                    });\n                }\n            });\n            let success = false;\n            try {\n                await this._initWatchPromise;\n                success = true;\n            }\n            finally {\n                this._initWatchPromise = undefined;\n            }\n            console.log(`[realtime] initWatch ${success ? 'success' : 'fail'}`);\n        };\n        this.rebuildWatch = async (forceRefreshLogin) => {\n            if (this._rebuildWatchPromise) {\n                return this._rebuildWatchPromise;\n            }\n            this._rebuildWatchPromise = new Promise(async (resolve, reject) => {\n                try {\n                    if (this.watchStatus === WATCH_STATUS.PAUSED) {\n                        console.log('[realtime] rebuildWatch cancelled on pause');\n                        return resolve();\n                    }\n                    const { envId } = await this._login(this.envId, forceRefreshLogin);\n                    if (!this.sessionInfo) {\n                        throw new Error('can not rebuildWatch without a successful initWatch (lack of sessionInfo)');\n                    }\n                    if (this.watchStatus === WATCH_STATUS.PAUSED) {\n                        console.log('[realtime] rebuildWatch cancelled on pause');\n                        return resolve();\n                    }\n                    this.watchStatus = WATCH_STATUS.REBUILDING;\n                    const rebuildWatchMsg = {\n                        watchId: this.watchId,\n                        requestId: message_1.genRequestId(),\n                        msgType: 'REBUILD_WATCH',\n                        msgData: {\n                            envId,\n                            collName: this.collectionName,\n                            queryID: this.sessionInfo.queryID,\n                            eventID: this.sessionInfo.currentEventId\n                        }\n                    };\n                    const nextEventMsg = await this.send({\n                        msg: rebuildWatchMsg,\n                        waitResponse: true,\n                        skipOnMessage: false,\n                        timeout: DEFAULT_REBUILD_WATCH_TIMEOUT\n                    });\n                    this.handleServerEvents(nextEventMsg);\n                    this.watchStatus = WATCH_STATUS.ACTIVE;\n                    this._availableRetries.REBUILD_WATCH = DEFAULT_MAX_AUTO_RETRY_ON_ERROR;\n                    resolve();\n                }\n                catch (e) {\n                    this.handleWatchEstablishmentError(e, {\n                        operationName: 'REBUILD_WATCH',\n                        resolve,\n                        reject\n                    });\n                }\n            });\n            let success = false;\n            try {\n                await this._rebuildWatchPromise;\n                success = true;\n            }\n            finally {\n                this._rebuildWatchPromise = undefined;\n            }\n            console.log(`[realtime] rebuildWatch ${success ? 'success' : 'fail'}`);\n        };\n        this.handleWatchEstablishmentError = async (e, options) => {\n            const isInitWatch = options.operationName === 'INIT_WATCH';\n            const abortWatch = () => {\n                this.closeWithError(new error_1.CloudSDKError({\n                    errCode: isInitWatch\n                        ? error_config_1.ERR_CODE.SDK_DATABASE_REALTIME_LISTENER_INIT_WATCH_FAIL\n                        : error_config_1.ERR_CODE.SDK_DATABASE_REALTIME_LISTENER_REBUILD_WATCH_FAIL,\n                    errMsg: e\n                }));\n                options.reject(e);\n            };\n            const retry = (refreshLogin) => {\n                if (this.useRetryTicket(options.operationName)) {\n                    if (isInitWatch) {\n                        this._initWatchPromise = undefined;\n                        options.resolve(this.initWatch(refreshLogin));\n                    }\n                    else {\n                        this._rebuildWatchPromise = undefined;\n                        options.resolve(this.rebuildWatch(refreshLogin));\n                    }\n                }\n                else {\n                    abortWatch();\n                }\n            };\n            this.handleCommonError(e, {\n                onSignError: () => retry(true),\n                onTimeoutError: () => retry(false),\n                onNotRetryableError: abortWatch,\n                onCancelledError: options.reject,\n                onUnknownError: async () => {\n                    try {\n                        const onWSDisconnected = async () => {\n                            this.pause();\n                            await this.onceWSConnected();\n                            retry(true);\n                        };\n                        if (!this.isWSConnected()) {\n                            await onWSDisconnected();\n                        }\n                        else {\n                            await utils_1.sleep(DEFAULT_WAIT_TIME_ON_UNKNOWN_ERROR);\n                            if (this.watchStatus === WATCH_STATUS.PAUSED) {\n                                options.reject(new error_1.CancelledError(`${options.operationName} cancelled due to pause after unknownError`));\n                            }\n                            else if (!this.isWSConnected()) {\n                                await onWSDisconnected();\n                            }\n                            else {\n                                retry(false);\n                            }\n                        }\n                    }\n                    catch (e) {\n                        retry(true);\n                    }\n                }\n            });\n        };\n        this.closeWatch = async () => {\n            const queryId = this.sessionInfo ? this.sessionInfo.queryID : '';\n            if (this.watchStatus !== WATCH_STATUS.ACTIVE) {\n                this.watchStatus = WATCH_STATUS.CLOSED;\n                this.onWatchClose(this, queryId);\n                return;\n            }\n            try {\n                this.watchStatus = WATCH_STATUS.CLOSING;\n                const closeWatchMsg = {\n                    watchId: this.watchId,\n                    requestId: message_1.genRequestId(),\n                    msgType: 'CLOSE_WATCH',\n                    msgData: null\n                };\n                await this.send({\n                    msg: closeWatchMsg\n                });\n                this.sessionInfo = undefined;\n                this.watchStatus = WATCH_STATUS.CLOSED;\n            }\n            catch (e) {\n                this.closeWithError(new error_1.CloudSDKError({\n                    errCode: error_config_1.ERR_CODE.SDK_DATABASE_REALTIME_LISTENER_CLOSE_WATCH_FAIL,\n                    errMsg: e\n                }));\n            }\n            finally {\n                this.onWatchClose(this, queryId);\n            }\n        };\n        this.scheduleSendACK = () => {\n            this.clearACKSchedule();\n            this._ackTimeoutId = setTimeout(() => {\n                if (this._waitExpectedTimeoutId) {\n                    this.scheduleSendACK();\n                }\n                else {\n                    this.sendACK();\n                }\n            }, DEFAULT_SEND_ACK_DEBOUNCE_TIMEOUT);\n        };\n        this.clearACKSchedule = () => {\n            if (this._ackTimeoutId) {\n                clearTimeout(this._ackTimeoutId);\n            }\n        };\n        this.sendACK = async () => {\n            try {\n                if (this.watchStatus !== WATCH_STATUS.ACTIVE) {\n                    this.scheduleSendACK();\n                    return;\n                }\n                if (!this.sessionInfo) {\n                    console.warn('[realtime listener] can not send ack without a successful initWatch (lack of sessionInfo)');\n                    return;\n                }\n                const ackMsg = {\n                    watchId: this.watchId,\n                    requestId: message_1.genRequestId(),\n                    msgType: 'CHECK_LAST',\n                    msgData: {\n                        queryID: this.sessionInfo.queryID,\n                        eventID: this.sessionInfo.currentEventId\n                    }\n                };\n                await this.send({\n                    msg: ackMsg\n                });\n                this.scheduleSendACK();\n            }\n            catch (e) {\n                if (error_2.isRealtimeErrorMessageError(e)) {\n                    const msg = e.payload;\n                    switch (msg.msgData.code) {\n                        case 'CHECK_LOGIN_FAILED':\n                        case 'SIGN_EXPIRED_ERROR':\n                        case 'SIGN_INVALID_ERROR':\n                        case 'SIGN_PARAM_INVALID': {\n                            this.rebuildWatch();\n                            return;\n                        }\n                        case 'QUERYID_INVALID_ERROR':\n                        case 'SYS_ERR':\n                        case 'INVALIID_ENV':\n                        case 'COLLECTION_PERMISSION_DENIED': {\n                            this.closeWithError(new error_1.CloudSDKError({\n                                errCode: error_config_1.ERR_CODE.SDK_DATABASE_REALTIME_LISTENER_CHECK_LAST_FAIL,\n                                errMsg: msg.msgData.code\n                            }));\n                            return;\n                        }\n                        default: {\n                            break;\n                        }\n                    }\n                }\n                if (this._availableRetries.CHECK_LAST &&\n                    this._availableRetries.CHECK_LAST > 0) {\n                    this._availableRetries.CHECK_LAST--;\n                    this.scheduleSendACK();\n                }\n                else {\n                    this.closeWithError(new error_1.CloudSDKError({\n                        errCode: error_config_1.ERR_CODE.SDK_DATABASE_REALTIME_LISTENER_CHECK_LAST_FAIL,\n                        errMsg: e\n                    }));\n                }\n            }\n        };\n        this.handleCommonError = (e, options) => {\n            if (error_2.isRealtimeErrorMessageError(e)) {\n                const msg = e.payload;\n                switch (msg.msgData.code) {\n                    case 'CHECK_LOGIN_FAILED':\n                    case 'SIGN_EXPIRED_ERROR':\n                    case 'SIGN_INVALID_ERROR':\n                    case 'SIGN_PARAM_INVALID': {\n                        options.onSignError(e);\n                        return;\n                    }\n                    case 'QUERYID_INVALID_ERROR':\n                    case 'SYS_ERR':\n                    case 'INVALIID_ENV':\n                    case 'COLLECTION_PERMISSION_DENIED': {\n                        options.onNotRetryableError(e);\n                        return;\n                    }\n                    default: {\n                        options.onNotRetryableError(e);\n                        return;\n                    }\n                }\n            }\n            else if (error_1.isTimeoutError(e)) {\n                options.onTimeoutError(e);\n                return;\n            }\n            else if (error_1.isCancelledError(e)) {\n                options.onCancelledError(e);\n                return;\n            }\n            options.onUnknownError(e);\n        };\n        this.watchId = `watchid_${+new Date()}_${Math.random()}`;\n        this.envId = options.envId;\n        this.collectionName = options.collectionName;\n        this.query = options.query;\n        this.limit = options.limit;\n        this.orderBy = options.orderBy;\n        this.send = options.send;\n        this.login = options.login;\n        this.isWSConnected = options.isWSConnected;\n        this.onceWSConnected = options.onceWSConnected;\n        this.getWaitExpectedTimeoutLength = options.getWaitExpectedTimeoutLength;\n        this.onWatchStart = options.onWatchStart;\n        this.onWatchClose = options.onWatchClose;\n        this.debug = options.debug;\n        this._availableRetries = {\n            INIT_WATCH: DEFAULT_MAX_AUTO_RETRY_ON_ERROR,\n            REBUILD_WATCH: DEFAULT_MAX_AUTO_RETRY_ON_ERROR,\n            CHECK_LAST: DEFAULT_MAX_SEND_ACK_AUTO_RETRY_ON_ERROR\n        };\n        this.listener = new listener_1.RealtimeListener({\n            close: this.closeWatch,\n            onChange: options.onChange,\n            onError: options.onError,\n            debug: this.debug,\n            virtualClient: this\n        });\n        this.initWatch();\n    }\n    useRetryTicket(operationName) {\n        if (this._availableRetries[operationName] &&\n            this._availableRetries[operationName] > 0) {\n            this._availableRetries[operationName]--;\n            console.log(`[realtime] ${operationName} use a retry ticket, now only ${this._availableRetries[operationName]} retry left`);\n            return true;\n        }\n        return false;\n    }\n    async handleServerEvents(msg) {\n        try {\n            this.scheduleSendACK();\n            await this._handleServerEvents(msg);\n            this._postHandleServerEventsValidityCheck(msg);\n        }\n        catch (e) {\n            console.error('[realtime listener] internal non-fatal error: handle server events failed with error: ', e);\n            throw e;\n        }\n    }\n    async _handleServerEvents(msg) {\n        const { requestId } = msg;\n        const { events } = msg.msgData;\n        const { msgType } = msg;\n        if (!events.length || !this.sessionInfo) {\n            return;\n        }\n        const sessionInfo = this.sessionInfo;\n        let allChangeEvents;\n        try {\n            allChangeEvents = events.map(getPublicEvent);\n        }\n        catch (e) {\n            this.closeWithError(new error_1.CloudSDKError({\n                errCode: error_config_1.ERR_CODE.SDK_DATABASE_REALTIME_LISTENER_RECEIVE_INVALID_SERVER_DATA,\n                errMsg: e\n            }));\n            return;\n        }\n        let docs = [...sessionInfo.currentDocs];\n        let initEncountered = false;\n        for (let i = 0, len = allChangeEvents.length; i < len; i++) {\n            const change = allChangeEvents[i];\n            if (sessionInfo.currentEventId >= change.id) {\n                if (!allChangeEvents[i - 1] || change.id > allChangeEvents[i - 1].id) {\n                    console.warn(`[realtime] duplicate event received, cur ${sessionInfo.currentEventId} but got ${change.id}`);\n                }\n                else {\n                    console.error(`[realtime listener] server non-fatal error: events out of order (the latter event's id is smaller than that of the former) (requestId ${requestId})`);\n                }\n                continue;\n            }\n            else if (sessionInfo.currentEventId === change.id - 1) {\n                switch (change.dataType) {\n                    case 'update': {\n                        if (!change.doc) {\n                            switch (change.queueType) {\n                                case 'update':\n                                case 'dequeue': {\n                                    const localDoc = docs.find(doc => doc._id === change.docId);\n                                    if (localDoc) {\n                                        const doc = lodash_clonedeep_1.default(localDoc);\n                                        if (change.updatedFields) {\n                                            for (const fieldPath in change.updatedFields) {\n                                                lodash_set_1.default(doc, fieldPath, change.updatedFields[fieldPath]);\n                                            }\n                                        }\n                                        if (change.removedFields) {\n                                            for (const fieldPath of change.removedFields) {\n                                                lodash_unset_1.default(doc, fieldPath);\n                                            }\n                                        }\n                                        change.doc = doc;\n                                    }\n                                    else {\n                                        console.error('[realtime listener] internal non-fatal server error: unexpected update dataType event where no doc is associated.');\n                                    }\n                                    break;\n                                }\n                                case 'enqueue': {\n                                    const err = new error_1.CloudSDKError({\n                                        errCode: error_config_1.ERR_CODE.SDK_DATABASE_REALTIME_LISTENER_UNEXPECTED_FATAL_ERROR,\n                                        errMsg: `HandleServerEvents: full doc is not provided with dataType=\"update\" and queueType=\"enqueue\" (requestId ${msg.requestId})`\n                                    });\n                                    this.closeWithError(err);\n                                    throw err;\n                                }\n                                default: {\n                                    break;\n                                }\n                            }\n                        }\n                        break;\n                    }\n                    case 'replace': {\n                        if (!change.doc) {\n                            const err = new error_1.CloudSDKError({\n                                errCode: error_config_1.ERR_CODE.SDK_DATABASE_REALTIME_LISTENER_UNEXPECTED_FATAL_ERROR,\n                                errMsg: `HandleServerEvents: full doc is not provided with dataType=\"replace\" (requestId ${msg.requestId})`\n                            });\n                            this.closeWithError(err);\n                            throw err;\n                        }\n                        break;\n                    }\n                    case 'remove': {\n                        const doc = docs.find(doc => doc._id === change.docId);\n                        if (doc) {\n                            change.doc = doc;\n                        }\n                        else {\n                            console.error('[realtime listener] internal non-fatal server error: unexpected remove event where no doc is associated.');\n                        }\n                        break;\n                    }\n                    case 'limit': {\n                        if (!change.doc) {\n                            switch (change.queueType) {\n                                case 'dequeue': {\n                                    const doc = docs.find(doc => doc._id === change.docId);\n                                    if (doc) {\n                                        change.doc = doc;\n                                    }\n                                    else {\n                                        console.error('[realtime listener] internal non-fatal server error: unexpected limit dataType event where no doc is associated.');\n                                    }\n                                    break;\n                                }\n                                case 'enqueue': {\n                                    const err = new error_1.CloudSDKError({\n                                        errCode: error_config_1.ERR_CODE.SDK_DATABASE_REALTIME_LISTENER_UNEXPECTED_FATAL_ERROR,\n                                        errMsg: `HandleServerEvents: full doc is not provided with dataType=\"limit\" and queueType=\"enqueue\" (requestId ${msg.requestId})`\n                                    });\n                                    this.closeWithError(err);\n                                    throw err;\n                                }\n                                default: {\n                                    break;\n                                }\n                            }\n                        }\n                        break;\n                    }\n                }\n                switch (change.queueType) {\n                    case 'init': {\n                        if (!initEncountered) {\n                            initEncountered = true;\n                            docs = [change.doc];\n                        }\n                        else {\n                            docs.push(change.doc);\n                        }\n                        break;\n                    }\n                    case 'enqueue': {\n                        docs.push(change.doc);\n                        break;\n                    }\n                    case 'dequeue': {\n                        const ind = docs.findIndex(doc => doc._id === change.docId);\n                        if (ind > -1) {\n                            docs.splice(ind, 1);\n                        }\n                        else {\n                            console.error('[realtime listener] internal non-fatal server error: unexpected dequeue event where no doc is associated.');\n                        }\n                        break;\n                    }\n                    case 'update': {\n                        const ind = docs.findIndex(doc => doc._id === change.docId);\n                        if (ind > -1) {\n                            docs[ind] = change.doc;\n                        }\n                        else {\n                            console.error('[realtime listener] internal non-fatal server error: unexpected queueType update event where no doc is associated.');\n                        }\n                        break;\n                    }\n                }\n                if (i === len - 1 ||\n                    (allChangeEvents[i + 1] && allChangeEvents[i + 1].id !== change.id)) {\n                    const docsSnapshot = [...docs];\n                    const docChanges = allChangeEvents\n                        .slice(0, i + 1)\n                        .filter(c => c.id === change.id);\n                    this.sessionInfo.currentEventId = change.id;\n                    this.sessionInfo.currentDocs = docs;\n                    const snapshot = new snapshot_1.Snapshot({\n                        id: change.id,\n                        docChanges,\n                        docs: docsSnapshot,\n                        msgType\n                    });\n                    this.listener.onChange(snapshot);\n                }\n            }\n            else {\n                console.warn(`[realtime listener] event received is out of order, cur ${this.sessionInfo.currentEventId} but got ${change.id}`);\n                await this.rebuildWatch();\n                return;\n            }\n        }\n    }\n    _postHandleServerEventsValidityCheck(msg) {\n        if (!this.sessionInfo) {\n            console.error('[realtime listener] internal non-fatal error: sessionInfo lost after server event handling, this should never occur');\n            return;\n        }\n        if (this.sessionInfo.expectEventId &&\n            this.sessionInfo.currentEventId >= this.sessionInfo.expectEventId) {\n            this.clearWaitExpectedEvent();\n        }\n        if (this.sessionInfo.currentEventId < msg.msgData.currEvent) {\n            console.warn('[realtime listener] internal non-fatal error: client eventId does not match with server event id after server event handling');\n            return;\n        }\n    }\n    clearWaitExpectedEvent() {\n        if (this._waitExpectedTimeoutId) {\n            clearTimeout(this._waitExpectedTimeoutId);\n            this._waitExpectedTimeoutId = undefined;\n        }\n    }\n    onMessage(msg) {\n        switch (this.watchStatus) {\n            case WATCH_STATUS.PAUSED: {\n                if (msg.msgType !== 'ERROR') {\n                    return;\n                }\n                break;\n            }\n            case WATCH_STATUS.LOGGINGIN:\n            case WATCH_STATUS.INITING:\n            case WATCH_STATUS.REBUILDING: {\n                console.warn(`[realtime listener] internal non-fatal error: unexpected message received while ${this.watchStatus}`);\n                return;\n            }\n            case WATCH_STATUS.CLOSED: {\n                console.warn('[realtime listener] internal non-fatal error: unexpected message received when the watch has closed');\n                return;\n            }\n            case WATCH_STATUS.ERRORED: {\n                console.warn('[realtime listener] internal non-fatal error: unexpected message received when the watch has ended with error');\n                return;\n            }\n        }\n        if (!this.sessionInfo) {\n            console.warn('[realtime listener] internal non-fatal error: sessionInfo not found while message is received.');\n            return;\n        }\n        this.scheduleSendACK();\n        switch (msg.msgType) {\n            case 'NEXT_EVENT': {\n                console.warn(`nextevent ${msg.msgData.currEvent} ignored`, msg);\n                this.handleServerEvents(msg);\n                break;\n            }\n            case 'CHECK_EVENT': {\n                if (this.sessionInfo.currentEventId < msg.msgData.currEvent) {\n                    this.sessionInfo.expectEventId = msg.msgData.currEvent;\n                    this.clearWaitExpectedEvent();\n                    this._waitExpectedTimeoutId = setTimeout(() => {\n                        this.rebuildWatch();\n                    }, this.getWaitExpectedTimeoutLength());\n                    console.log(`[realtime] waitExpectedTimeoutLength ${this.getWaitExpectedTimeoutLength()}`);\n                }\n                break;\n            }\n            case 'ERROR': {\n                this.closeWithError(new error_1.CloudSDKError({\n                    errCode: error_config_1.ERR_CODE.SDK_DATABASE_REALTIME_LISTENER_SERVER_ERROR_MSG,\n                    errMsg: `${msg.msgData.code} - ${msg.msgData.message}`\n                }));\n                break;\n            }\n            default: {\n                console.warn(`[realtime listener] virtual client receive unexpected msg ${msg.msgType}: `, msg);\n                break;\n            }\n        }\n    }\n    closeWithError(error) {\n        this.watchStatus = WATCH_STATUS.ERRORED;\n        this.clearACKSchedule();\n        this.listener.onError(error);\n        this.onWatchClose(this, (this.sessionInfo && this.sessionInfo.queryID) || '');\n        console.log(`[realtime] client closed (${this.collectionName} ${this.query}) (watchId ${this.watchId})`);\n    }\n    pause() {\n        this.watchStatus = WATCH_STATUS.PAUSED;\n        console.log(`[realtime] client paused (${this.collectionName} ${this.query}) (watchId ${this.watchId})`);\n    }\n    async resume() {\n        this.watchStatus = WATCH_STATUS.RESUMING;\n        console.log(`[realtime] client resuming with ${this.sessionInfo ? 'REBUILD_WATCH' : 'INIT_WATCH'} (${this.collectionName} ${this.query}) (${this.watchId})`);\n        try {\n            await (this.sessionInfo ? this.rebuildWatch() : this.initWatch());\n            console.log(`[realtime] client successfully resumed (${this.collectionName} ${this.query}) (${this.watchId})`);\n        }\n        catch (e) {\n            console.error(`[realtime] client resume failed (${this.collectionName} ${this.query}) (${this.watchId})`, e);\n        }\n    }\n}\nexports.VirtualWebSocketClient = VirtualWebSocketClient;\nfunction getPublicEvent(event) {\n    const e = {\n        id: event.ID,\n        dataType: event.DataType,\n        queueType: event.QueueType,\n        docId: event.DocID,\n        doc: event.Doc && event.Doc !== '{}' ? JSON.parse(event.Doc) : undefined\n    };\n    if (event.DataType === 'update') {\n        if (event.UpdatedFields) {\n            e.updatedFields = JSON.parse(event.UpdatedFields);\n        }\n        if (event.removedFields || event.RemovedFields) {\n            e.removedFields = JSON.parse(event.removedFields);\n        }\n    }\n    return e;\n}\n", "\nObject.defineProperty(exports, \"__esModule\", { value: true });\nfunction genRequestId(prefix = '') {\n    return `${prefix ? `${prefix}_` : ''}${+new Date()}_${Math.random()}`;\n}\nexports.genRequestId = genRequestId;\nfunction isInitEventMessage(msg) {\n    return msg.msgType === 'INIT_EVENT';\n}\nexports.isInitEventMessage = isInitEventMessage;\n", "\nObject.defineProperty(exports, \"__esModule\", { value: true });\nconst type_1 = require(\"./type\");\nconst error_config_1 = require(\"../config/error.config\");\nclass CloudSDKError extends Error {\n    constructor(options) {\n        super(options.errMsg);\n        this.errCode = 'UNKNOWN_ERROR';\n        Object.defineProperties(this, {\n            message: {\n                get() {\n                    return (`errCode: ${this.errCode} ${error_config_1.ERR_CODE[this.errCode] ||\n                        ''} | errMsg: ` + this.errMsg);\n                },\n                set(msg) {\n                    this.errMsg = msg;\n                }\n            }\n        });\n        this.errCode = options.errCode || 'UNKNOWN_ERROR';\n        this.errMsg = options.errMsg;\n    }\n    get message() {\n        return `errCode: ${this.errCode} | errMsg: ` + this.errMsg;\n    }\n    set message(msg) {\n        this.errMsg = msg;\n    }\n}\nexports.CloudSDKError = CloudSDKError;\nfunction isSDKError(error) {\n    return (error && error instanceof Error && type_1.isString(error.errMsg));\n}\nexports.isSDKError = isSDKError;\nexports.isGenericError = (e) => e.generic;\nclass TimeoutError extends Error {\n    constructor(message) {\n        super(message);\n        this.type = 'timeout';\n        this.payload = null;\n        this.generic = true;\n    }\n}\nexports.TimeoutError = TimeoutError;\nexports.isTimeoutError = (e) => e.type === 'timeout';\nclass CancelledError extends Error {\n    constructor(message) {\n        super(message);\n        this.type = 'cancelled';\n        this.payload = null;\n        this.generic = true;\n    }\n}\nexports.CancelledError = CancelledError;\nexports.isCancelledError = (e) => e.type === 'cancelled';\n", "\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.ERR_CODE = {\n    UNKNOWN_ERROR: 'UNKNOWN_ERROR',\n    SDK_DATABASE_REALTIME_LISTENER_INIT_WATCH_FAIL: 'SDK_DATABASE_REALTIME_LISTENER_INIT_WATCH_FAIL',\n    SDK_DATABASE_REALTIME_LISTENER_RECONNECT_WATCH_FAIL: 'SDK_DATABASE_REALTIME_LISTENER_RECONNECT_WATCH_FAIL',\n    SDK_DATABASE_REALTIME_LISTENER_REBUILD_WATCH_FAIL: 'SDK_DATABASE_REALTIME_LISTENER_REBUILD_WATCH_FAIL',\n    SDK_DATABASE_REALTIME_LISTENER_CLOSE_WATCH_FAIL: 'SDK_DATABASE_REALTIME_LISTENER_CLOSE_WATCH_FAIL',\n    SDK_DATABASE_REALTIME_LISTENER_SERVER_ERROR_MSG: 'SDK_DATABASE_REALTIME_LISTENER_SERVER_ERROR_MSG',\n    SDK_DATABASE_REALTIME_LISTENER_RECEIVE_INVALID_SERVER_DATA: 'SDK_DATABASE_REALTIME_LISTENER_RECEIVE_INVALID_SERVER_DATA',\n    SDK_DATABASE_REALTIME_LISTENER_WEBSOCKET_CONNECTION_ERROR: 'SDK_DATABASE_REALTIME_LISTENER_WEBSOCKET_CONNECTION_ERROR',\n    SDK_DATABASE_REALTIME_LISTENER_WEBSOCKET_CONNECTION_CLOSED: 'SDK_DATABASE_REALTIME_LISTENER_WEBSOCKET_CONNECTION_CLOSED',\n    SDK_DATABASE_REALTIME_LISTENER_CHECK_LAST_FAIL: 'SDK_DATABASE_REALTIME_LISTENER_CHECK_LAST_FAIL',\n    SDK_DATABASE_REALTIME_LISTENER_UNEXPECTED_FATAL_ERROR: 'SDK_DATABASE_REALTIME_LISTENER_UNEXPECTED_FATAL_ERROR'\n};\n", "\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.sleep = (ms = 0) => new Promise(r => setTimeout(r, ms));\nconst counters = {};\nexports.autoCount = (domain = 'any') => {\n    if (!counters[domain]) {\n        counters[domain] = 0;\n    }\n    return counters[domain]++;\n};\n", "\nObject.defineProperty(exports, \"__esModule\", { value: true });\nclass RealtimeListener {\n    constructor(options) {\n        this.close = options.close;\n        this.onChange = options.onChange;\n        this.onError = options.onError;\n        if (options.debug) {\n            Object.defineProperty(this, 'virtualClient', {\n                get: () => {\n                    return options.virtualClient;\n                }\n            });\n        }\n    }\n}\nexports.RealtimeListener = RealtimeListener;\n", "\nObject.defineProperty(exports, \"__esModule\", { value: true });\nclass Snapshot {\n    constructor(options) {\n        const { id, docChanges, docs, msgType, type } = options;\n        let cachedDocChanges;\n        let cachedDocs;\n        Object.defineProperties(this, {\n            id: {\n                get: () => id,\n                enumerable: true\n            },\n            docChanges: {\n                get: () => {\n                    if (!cachedDocChanges) {\n                        cachedDocChanges = JSON.parse(JSON.stringify(docChanges));\n                    }\n                    return cachedDocChanges;\n                },\n                enumerable: true\n            },\n            docs: {\n                get: () => {\n                    if (!cachedDocs) {\n                        cachedDocs = JSON.parse(JSON.stringify(docs));\n                    }\n                    return cachedDocs;\n                },\n                enumerable: true\n            },\n            msgType: {\n                get: () => msgType,\n                enumerable: true\n            },\n            type: {\n                get: () => type,\n                enumerable: true\n            }\n        });\n    }\n}\nexports.Snapshot = Snapshot;\n", "\nObject.defineProperty(exports, \"__esModule\", { value: true });\nclass RealtimeErrorMessageError extends Error {\n    constructor(serverErrorMsg) {\n        super(`Watch Error ${JSON.stringify(serverErrorMsg.msgData)} (requestid: ${serverErrorMsg.requestId})`);\n        this.isRealtimeErrorMessageError = true;\n        this.payload = serverErrorMsg;\n    }\n}\nexports.RealtimeErrorMessageError = RealtimeErrorMessageError;\nexports.isRealtimeErrorMessageError = (e) => e && e.isRealtimeErrorMessageError;\n", "\nObject.defineProperty(exports, \"__esModule\", { value: true });\nconst error_1 = require(\"../utils/error\");\nconst error_config_1 = require(\"../config/error.config\");\nexports.CLOSE_EVENT_CODE_INFO = {\n    1000: {\n        code: 1000,\n        name: 'Normal Closure',\n        description: 'Normal closure; the connection successfully completed whatever purpose for which it was created.'\n    },\n    1001: {\n        code: 1001,\n        name: 'Going Away',\n        description: 'The endpoint is going away, either because of a server failure or because the browser is navigating away from the page that opened the connection.'\n    },\n    1002: {\n        code: 1002,\n        name: 'Protocol Error',\n        description: 'The endpoint is terminating the connection due to a protocol error.'\n    },\n    1003: {\n        code: 1003,\n        name: 'Unsupported Data',\n        description: 'The connection is being terminated because the endpoint received data of a type it cannot accept (for example, a text-only endpoint received binary data).'\n    },\n    1005: {\n        code: 1005,\n        name: 'No Status Received',\n        description: 'Indicates that no status code was provided even though one was expected.'\n    },\n    1006: {\n        code: 1006,\n        name: 'Abnormal Closure',\n        description: 'Used to indicate that a connection was closed abnormally (that is, with no close frame being sent) when a status code is expected.'\n    },\n    1007: {\n        code: 1007,\n        name: 'Invalid frame payload data',\n        description: 'The endpoint is terminating the connection because a message was received that contained inconsistent data (e.g., non-UTF-8 data within a text message).'\n    },\n    1008: {\n        code: 1008,\n        name: 'Policy Violation',\n        description: 'The endpoint is terminating the connection because it received a message that violates its policy. This is a generic status code, used when codes 1003 and 1009 are not suitable.'\n    },\n    1009: {\n        code: 1009,\n        name: 'Message too big',\n        description: 'The endpoint is terminating the connection because a data frame was received that is too large.'\n    },\n    1010: {\n        code: 1010,\n        name: 'Missing Extension',\n        description: \"The client is terminating the connection because it expected the server to negotiate one or more extension, but the server didn't.\"\n    },\n    1011: {\n        code: 1011,\n        name: 'Internal Error',\n        description: 'The server is terminating the connection because it encountered an unexpected condition that prevented it from fulfilling the request.'\n    },\n    1012: {\n        code: 1012,\n        name: 'Service Restart',\n        description: 'The server is terminating the connection because it is restarting.'\n    },\n    1013: {\n        code: 1013,\n        name: 'Try Again Later',\n        description: 'The server is terminating the connection due to a temporary condition, e.g. it is overloaded and is casting off some of its clients.'\n    },\n    1014: {\n        code: 1014,\n        name: 'Bad Gateway',\n        description: 'The server was acting as a gateway or proxy and received an invalid response from the upstream server. This is similar to 502 HTTP Status Code.'\n    },\n    1015: {\n        code: 1015,\n        name: 'TLS Handshake',\n        description: \"Indicates that the connection was closed due to a failure to perform a TLS handshake (e.g., the server certificate can't be verified).\"\n    },\n    3000: {\n        code: 3000,\n        name: 'Reconnect WebSocket',\n        description: 'The client is terminating the connection because it wants to reconnect'\n    },\n    3001: {\n        code: 3001,\n        name: 'No Realtime Listeners',\n        description: 'The client is terminating the connection because no more realtime listeners exist'\n    },\n    3002: {\n        code: 3002,\n        name: 'Heartbeat Ping Error',\n        description: 'The client is terminating the connection due to its failure in sending heartbeat messages'\n    },\n    3003: {\n        code: 3003,\n        name: 'Heartbeat Pong Timeout Error',\n        description: 'The client is terminating the connection because no heartbeat response is received from the server'\n    },\n    3050: {\n        code: 3050,\n        name: 'Server Close',\n        description: 'The client is terminating the connection because no heartbeat response is received from the server'\n    }\n};\nvar CLOSE_EVENT_CODE;\n(function (CLOSE_EVENT_CODE) {\n    CLOSE_EVENT_CODE[CLOSE_EVENT_CODE[\"NormalClosure\"] = 1000] = \"NormalClosure\";\n    CLOSE_EVENT_CODE[CLOSE_EVENT_CODE[\"GoingAway\"] = 1001] = \"GoingAway\";\n    CLOSE_EVENT_CODE[CLOSE_EVENT_CODE[\"ProtocolError\"] = 1002] = \"ProtocolError\";\n    CLOSE_EVENT_CODE[CLOSE_EVENT_CODE[\"UnsupportedData\"] = 1003] = \"UnsupportedData\";\n    CLOSE_EVENT_CODE[CLOSE_EVENT_CODE[\"NoStatusReceived\"] = 1005] = \"NoStatusReceived\";\n    CLOSE_EVENT_CODE[CLOSE_EVENT_CODE[\"AbnormalClosure\"] = 1006] = \"AbnormalClosure\";\n    CLOSE_EVENT_CODE[CLOSE_EVENT_CODE[\"InvalidFramePayloadData\"] = 1007] = \"InvalidFramePayloadData\";\n    CLOSE_EVENT_CODE[CLOSE_EVENT_CODE[\"PolicyViolation\"] = 1008] = \"PolicyViolation\";\n    CLOSE_EVENT_CODE[CLOSE_EVENT_CODE[\"MessageTooBig\"] = 1009] = \"MessageTooBig\";\n    CLOSE_EVENT_CODE[CLOSE_EVENT_CODE[\"MissingExtension\"] = 1010] = \"MissingExtension\";\n    CLOSE_EVENT_CODE[CLOSE_EVENT_CODE[\"InternalError\"] = 1011] = \"InternalError\";\n    CLOSE_EVENT_CODE[CLOSE_EVENT_CODE[\"ServiceRestart\"] = 1012] = \"ServiceRestart\";\n    CLOSE_EVENT_CODE[CLOSE_EVENT_CODE[\"TryAgainLater\"] = 1013] = \"TryAgainLater\";\n    CLOSE_EVENT_CODE[CLOSE_EVENT_CODE[\"BadGateway\"] = 1014] = \"BadGateway\";\n    CLOSE_EVENT_CODE[CLOSE_EVENT_CODE[\"TLSHandshake\"] = 1015] = \"TLSHandshake\";\n    CLOSE_EVENT_CODE[CLOSE_EVENT_CODE[\"ReconnectWebSocket\"] = 3000] = \"ReconnectWebSocket\";\n    CLOSE_EVENT_CODE[CLOSE_EVENT_CODE[\"NoRealtimeListeners\"] = 3001] = \"NoRealtimeListeners\";\n    CLOSE_EVENT_CODE[CLOSE_EVENT_CODE[\"HeartbeatPingError\"] = 3002] = \"HeartbeatPingError\";\n    CLOSE_EVENT_CODE[CLOSE_EVENT_CODE[\"HeartbeatPongTimeoutError\"] = 3003] = \"HeartbeatPongTimeoutError\";\n    CLOSE_EVENT_CODE[CLOSE_EVENT_CODE[\"NoAuthentication\"] = 3050] = \"NoAuthentication\";\n})(CLOSE_EVENT_CODE = exports.CLOSE_EVENT_CODE || (exports.CLOSE_EVENT_CODE = {}));\nexports.getWSCloseError = (code, reason) => {\n    const info = exports.CLOSE_EVENT_CODE_INFO[code];\n    const errMsg = !info\n        ? `code ${code}`\n        : `${info.name}, code ${code}, reason ${reason || info.description}`;\n    return new error_1.CloudSDKError({\n        errCode: error_config_1.ERR_CODE.SDK_DATABASE_REALTIME_LISTENER_WEBSOCKET_CONNECTION_CLOSED,\n        errMsg\n    });\n};\n", "\nObject.defineProperty(exports, \"__esModule\", { value: true });\nconst util_1 = require(\"./lib/util\");\nconst constant_1 = require(\"./constant\");\nconst index_1 = require(\"./index\");\nconst validate_1 = require(\"./validate\");\nconst util_2 = require(\"./util\");\nconst query_1 = require(\"./serializer/query\");\nconst update_1 = require(\"./serializer/update\");\nconst websocket_client_1 = require(\"./realtime/websocket-client\");\nconst constant_2 = require(\"./constant\");\nclass Query {\n    constructor(db, coll, fieldFilters, fieldOrders, queryOptions) {\n        this.watch = (options) => {\n            if (!index_1.Db.ws) {\n                index_1.Db.ws = new websocket_client_1.RealtimeWebSocketClient({\n                    context: {\n                        appConfig: {\n                            docSizeLimit: 1000,\n                            realtimePingInterval: 10000,\n                            realtimePongWaitTimeout: 5000,\n                            request: this._request\n                        }\n                    }\n                });\n            }\n            return index_1.Db.ws.watch(Object.assign(Object.assign({}, options), { envId: this._db.config.env, collectionName: this._coll, query: JSON.stringify(this._fieldFilters), limit: this._queryOptions.limit, orderBy: this._fieldOrders\n                    ? this._fieldOrders.reduce((acc, cur) => {\n                        acc[cur.field] = cur.direction;\n                        return acc;\n                    }, {})\n                    : undefined }));\n        };\n        this._db = db;\n        this._coll = coll;\n        this._fieldFilters = fieldFilters;\n        this._fieldOrders = fieldOrders || [];\n        this._queryOptions = queryOptions || {};\n        this._request = new index_1.Db.reqClass(this._db.config);\n    }\n    get(callback) {\n        callback = callback || util_1.createPromiseCallback();\n        let newOder = [];\n        if (this._fieldOrders) {\n            this._fieldOrders.forEach(order => {\n                newOder.push(order);\n            });\n        }\n        let param = {\n            collectionName: this._coll,\n            queryType: constant_1.QueryType.WHERE\n        };\n        if (this._fieldFilters) {\n            param.query = this._fieldFilters;\n        }\n        if (newOder.length > 0) {\n            param.order = newOder;\n        }\n        if (this._queryOptions.offset) {\n            param.offset = this._queryOptions.offset;\n        }\n        if (this._queryOptions.limit) {\n            param.limit = this._queryOptions.limit < 1000 ? this._queryOptions.limit : 1000;\n        }\n        else {\n            param.limit = 100;\n        }\n        if (this._queryOptions.projection) {\n            param.projection = this._queryOptions.projection;\n        }\n        this._request\n            .send('database.queryDocument', param)\n            .then(res => {\n            if (res.code) {\n                callback(0, res);\n            }\n            else {\n                const documents = util_2.Util.formatResDocumentData(res.data.list);\n                const result = {\n                    data: documents,\n                    requestId: res.requestId\n                };\n                if (res.TotalCount)\n                    result.total = res.TotalCount;\n                if (res.Limit)\n                    result.limit = res.Limit;\n                if (res.Offset)\n                    result.offset = res.Offset;\n                callback(0, result);\n            }\n        })\n            .catch(err => {\n            callback(err);\n        });\n        return callback.promise;\n    }\n    count(callback) {\n        callback = callback || util_1.createPromiseCallback();\n        let param = {\n            collectionName: this._coll,\n            queryType: constant_1.QueryType.WHERE\n        };\n        if (this._fieldFilters) {\n            param.query = this._fieldFilters;\n        }\n        this._request.send('database.countDocument', param).then(res => {\n            if (res.code) {\n                callback(0, res);\n            }\n            else {\n                callback(0, {\n                    requestId: res.requestId,\n                    total: res.data.total\n                });\n            }\n        });\n        return callback.promise;\n    }\n    where(query) {\n        if (Object.prototype.toString.call(query).slice(8, -1) !== 'Object') {\n            throw Error(constant_2.ErrorCode.QueryParamTypeError);\n        }\n        const keys = Object.keys(query);\n        const checkFlag = keys.some(item => {\n            return query[item] !== undefined;\n        });\n        if (keys.length && !checkFlag) {\n            throw Error(constant_2.ErrorCode.QueryParamValueError);\n        }\n        return new Query(this._db, this._coll, query_1.QuerySerializer.encode(query), this._fieldOrders, this._queryOptions);\n    }\n    orderBy(fieldPath, directionStr) {\n        validate_1.Validate.isFieldPath(fieldPath);\n        validate_1.Validate.isFieldOrder(directionStr);\n        const newOrder = {\n            field: fieldPath,\n            direction: directionStr\n        };\n        const combinedOrders = this._fieldOrders.concat(newOrder);\n        return new Query(this._db, this._coll, this._fieldFilters, combinedOrders, this._queryOptions);\n    }\n    limit(limit) {\n        validate_1.Validate.isInteger('limit', limit);\n        let option = Object.assign({}, this._queryOptions);\n        option.limit = limit;\n        return new Query(this._db, this._coll, this._fieldFilters, this._fieldOrders, option);\n    }\n    skip(offset) {\n        validate_1.Validate.isInteger('offset', offset);\n        let option = Object.assign({}, this._queryOptions);\n        option.offset = offset;\n        return new Query(this._db, this._coll, this._fieldFilters, this._fieldOrders, option);\n    }\n    update(data, callback) {\n        callback = callback || util_1.createPromiseCallback();\n        if (!data || typeof data !== 'object') {\n            return Promise.resolve({\n                code: 'INVALID_PARAM',\n                message: '参数必需是非空对象'\n            });\n        }\n        if (data.hasOwnProperty('_id')) {\n            return Promise.resolve({\n                code: 'INVALID_PARAM',\n                message: '不能更新_id的值'\n            });\n        }\n        let param = {\n            collectionName: this._coll,\n            query: this._fieldFilters,\n            queryType: constant_1.QueryType.WHERE,\n            multi: true,\n            merge: true,\n            upsert: false,\n            data: update_1.UpdateSerializer.encode(data)\n        };\n        this._request.send('database.updateDocument', param).then(res => {\n            if (res.code) {\n                callback(0, res);\n            }\n            else {\n                callback(0, {\n                    requestId: res.requestId,\n                    updated: res.data.updated,\n                    upsertId: res.data.upsert_id\n                });\n            }\n        });\n        return callback.promise;\n    }\n    field(projection) {\n        for (let k in projection) {\n            if (projection[k]) {\n                if (typeof projection[k] !== 'object') {\n                    projection[k] = 1;\n                }\n            }\n            else {\n                projection[k] = 0;\n            }\n        }\n        let option = Object.assign({}, this._queryOptions);\n        option.projection = projection;\n        return new Query(this._db, this._coll, this._fieldFilters, this._fieldOrders, option);\n    }\n    remove(callback) {\n        callback = callback || util_1.createPromiseCallback();\n        if (Object.keys(this._queryOptions).length > 0) {\n            console.warn('`offset`, `limit` and `projection` are not supported in remove() operation');\n        }\n        if (this._fieldOrders.length > 0) {\n            console.warn('`orderBy` is not supported in remove() operation');\n        }\n        const param = {\n            collectionName: this._coll,\n            query: query_1.QuerySerializer.encode(this._fieldFilters),\n            queryType: constant_1.QueryType.WHERE,\n            multi: true\n        };\n        this._request.send('database.deleteDocument', param).then(res => {\n            if (res.code) {\n                callback(0, res);\n            }\n            else {\n                callback(0, {\n                    requestId: res.requestId,\n                    deleted: res.data.deleted\n                });\n            }\n        });\n        return callback.promise;\n    }\n}\nexports.Query = Query;\n", "\nObject.defineProperty(exports, \"__esModule\", { value: true });\nconst query_1 = require(\"../commands/query\");\nconst logic_1 = require(\"../commands/logic\");\nconst symbol_1 = require(\"../helper/symbol\");\nconst type_1 = require(\"../utils/type\");\nconst operator_map_1 = require(\"../operator-map\");\nconst common_1 = require(\"./common\");\nclass QuerySerializer {\n    constructor() { }\n    static encode(query) {\n        const encoder = new QueryEncoder();\n        return encoder.encodeQuery(query);\n    }\n}\nexports.QuerySerializer = QuerySerializer;\nclass QueryEncoder {\n    encodeQuery(query, key) {\n        if (common_1.isConversionRequired(query)) {\n            if (logic_1.isLogicCommand(query)) {\n                return this.encodeLogicCommand(query);\n            }\n            else if (query_1.isQueryCommand(query)) {\n                return this.encodeQueryCommand(query);\n            }\n            else {\n                return { [key]: this.encodeQueryObject(query) };\n            }\n        }\n        else {\n            if (type_1.isObject(query)) {\n                return this.encodeQueryObject(query);\n            }\n            else {\n                return query;\n            }\n        }\n    }\n    encodeRegExp(query) {\n        return {\n            $regex: query.source,\n            $options: query.flags\n        };\n    }\n    encodeLogicCommand(query) {\n        switch (query.operator) {\n            case logic_1.LOGIC_COMMANDS_LITERAL.NOR:\n            case logic_1.LOGIC_COMMANDS_LITERAL.AND:\n            case logic_1.LOGIC_COMMANDS_LITERAL.OR: {\n                const $op = operator_map_1.operatorToString(query.operator);\n                const subqueries = query.operands.map(oprand => this.encodeQuery(oprand, query.fieldName));\n                return {\n                    [$op]: subqueries\n                };\n            }\n            case logic_1.LOGIC_COMMANDS_LITERAL.NOT: {\n                const $op = operator_map_1.operatorToString(query.operator);\n                const operatorExpression = query.operands[0];\n                if (type_1.isRegExp(operatorExpression)) {\n                    return {\n                        [query.fieldName]: {\n                            [$op]: this.encodeRegExp(operatorExpression)\n                        }\n                    };\n                }\n                else {\n                    const subqueries = this.encodeQuery(operatorExpression)[query.fieldName];\n                    return {\n                        [query.fieldName]: {\n                            [$op]: subqueries\n                        }\n                    };\n                }\n            }\n            default: {\n                const $op = operator_map_1.operatorToString(query.operator);\n                if (query.operands.length === 1) {\n                    const subquery = this.encodeQuery(query.operands[0]);\n                    return {\n                        [$op]: subquery\n                    };\n                }\n                else {\n                    const subqueries = query.operands.map(this.encodeQuery.bind(this));\n                    return {\n                        [$op]: subqueries\n                    };\n                }\n            }\n        }\n    }\n    encodeQueryCommand(query) {\n        if (query_1.isComparisonCommand(query)) {\n            return this.encodeComparisonCommand(query);\n        }\n        else {\n            return this.encodeComparisonCommand(query);\n        }\n    }\n    encodeComparisonCommand(query) {\n        if (query.fieldName === symbol_1.SYMBOL_UNSET_FIELD_NAME) {\n            throw new Error('Cannot encode a comparison command with unset field name');\n        }\n        const $op = operator_map_1.operatorToString(query.operator);\n        switch (query.operator) {\n            case query_1.QUERY_COMMANDS_LITERAL.EQ:\n            case query_1.QUERY_COMMANDS_LITERAL.NEQ:\n            case query_1.QUERY_COMMANDS_LITERAL.LT:\n            case query_1.QUERY_COMMANDS_LITERAL.LTE:\n            case query_1.QUERY_COMMANDS_LITERAL.GT:\n            case query_1.QUERY_COMMANDS_LITERAL.GTE:\n            case query_1.QUERY_COMMANDS_LITERAL.ELEM_MATCH:\n            case query_1.QUERY_COMMANDS_LITERAL.EXISTS:\n            case query_1.QUERY_COMMANDS_LITERAL.SIZE:\n            case query_1.QUERY_COMMANDS_LITERAL.MOD: {\n                return {\n                    [query.fieldName]: {\n                        [$op]: common_1.encodeInternalDataType(query.operands[0])\n                    }\n                };\n            }\n            case query_1.QUERY_COMMANDS_LITERAL.IN:\n            case query_1.QUERY_COMMANDS_LITERAL.NIN:\n            case query_1.QUERY_COMMANDS_LITERAL.ALL: {\n                return {\n                    [query.fieldName]: {\n                        [$op]: common_1.encodeInternalDataType(query.operands)\n                    }\n                };\n            }\n            case query_1.QUERY_COMMANDS_LITERAL.GEO_NEAR: {\n                const options = query.operands[0];\n                return {\n                    [query.fieldName]: {\n                        $nearSphere: {\n                            $geometry: options.geometry.toJSON(),\n                            $maxDistance: options.maxDistance,\n                            $minDistance: options.minDistance\n                        }\n                    }\n                };\n            }\n            case query_1.QUERY_COMMANDS_LITERAL.GEO_WITHIN: {\n                const options = query.operands[0];\n                return {\n                    [query.fieldName]: {\n                        $geoWithin: {\n                            $geometry: options.geometry.toJSON()\n                        }\n                    }\n                };\n            }\n            case query_1.QUERY_COMMANDS_LITERAL.GEO_INTERSECTS: {\n                const options = query.operands[0];\n                return {\n                    [query.fieldName]: {\n                        $geoIntersects: {\n                            $geometry: options.geometry.toJSON()\n                        }\n                    }\n                };\n            }\n            default: {\n                return {\n                    [query.fieldName]: {\n                        [$op]: common_1.encodeInternalDataType(query.operands[0])\n                    }\n                };\n            }\n        }\n    }\n    encodeQueryObject(query) {\n        const flattened = common_1.flattenQueryObject(query);\n        for (const key in flattened) {\n            const val = flattened[key];\n            if (logic_1.isLogicCommand(val)) {\n                flattened[key] = val._setFieldName(key);\n                const condition = this.encodeLogicCommand(flattened[key]);\n                this.mergeConditionAfterEncode(flattened, condition, key);\n            }\n            else if (query_1.isComparisonCommand(val)) {\n                flattened[key] = val._setFieldName(key);\n                const condition = this.encodeComparisonCommand(flattened[key]);\n                this.mergeConditionAfterEncode(flattened, condition, key);\n            }\n            else if (common_1.isConversionRequired(val)) {\n                flattened[key] = common_1.encodeInternalDataType(val);\n            }\n        }\n        return flattened;\n    }\n    mergeConditionAfterEncode(query, condition, key) {\n        if (!condition[key]) {\n            delete query[key];\n        }\n        for (const conditionKey in condition) {\n            if (query[conditionKey]) {\n                if (type_1.isArray(query[conditionKey])) {\n                    query[conditionKey] = query[conditionKey].concat(condition[conditionKey]);\n                }\n                else if (type_1.isObject(query[conditionKey])) {\n                    if (type_1.isObject(condition[conditionKey])) {\n                        Object.assign(query, condition);\n                    }\n                    else {\n                        console.warn(`unmergable condition, query is object but condition is ${type_1.getType(condition)}, can only overwrite`, condition, key);\n                        query[conditionKey] = condition[conditionKey];\n                    }\n                }\n                else {\n                    console.warn(`to-merge query is of type ${type_1.getType(query)}, can only overwrite`, query, condition, key);\n                    query[conditionKey] = condition[conditionKey];\n                }\n            }\n            else {\n                query[conditionKey] = condition[conditionKey];\n            }\n        }\n    }\n}\n", "\nObject.defineProperty(exports, \"__esModule\", { value: true });\nconst index_1 = require(\"./index\");\nconst bson_1 = require(\"bson\");\nconst query_1 = require(\"./serializer/query\");\nclass Aggregation {\n    constructor(db, collectionName) {\n        this._stages = [];\n        if (db && collectionName) {\n            this._db = db;\n            this._request = new index_1.Db.reqClass(this._db.config);\n            this._collectionName = collectionName;\n        }\n    }\n    async end() {\n        if (!this._collectionName || !this._db) {\n            throw new Error('Aggregation pipeline cannot send request');\n        }\n        const result = await this._request.send('database.aggregate', {\n            collectionName: this._collectionName,\n            stages: this._stages\n        });\n        if (result && result.data && result.data.list) {\n            return {\n                requestId: result.requestId,\n                data: JSON.parse(result.data.list).map(bson_1.EJSON.parse)\n            };\n        }\n        return result;\n    }\n    unwrap() {\n        return this._stages;\n    }\n    done() {\n        return this._stages.map(({ stageKey, stageValue }) => {\n            return {\n                [stageKey]: JSON.parse(stageValue)\n            };\n        });\n    }\n    _pipe(stage, param) {\n        this._stages.push({\n            stageKey: `$${stage}`,\n            stageValue: JSON.stringify(param)\n        });\n        return this;\n    }\n    addFields(param) {\n        return this._pipe('addFields', param);\n    }\n    bucket(param) {\n        return this._pipe('bucket', param);\n    }\n    bucketAuto(param) {\n        return this._pipe('bucketAuto', param);\n    }\n    count(param) {\n        return this._pipe('count', param);\n    }\n    geoNear(param) {\n        return this._pipe('geoNear', param);\n    }\n    group(param) {\n        return this._pipe('group', param);\n    }\n    limit(param) {\n        return this._pipe('limit', param);\n    }\n    match(param) {\n        return this._pipe('match', query_1.QuerySerializer.encode(param));\n    }\n    project(param) {\n        return this._pipe('project', param);\n    }\n    lookup(param) {\n        return this._pipe('lookup', param);\n    }\n    replaceRoot(param) {\n        return this._pipe('replaceRoot', param);\n    }\n    sample(param) {\n        return this._pipe('sample', param);\n    }\n    skip(param) {\n        return this._pipe('skip', param);\n    }\n    sort(param) {\n        return this._pipe('sort', param);\n    }\n    sortByCount(param) {\n        return this._pipe('sortByCount', param);\n    }\n    unwind(param) {\n        return this._pipe('unwind', param);\n    }\n}\nexports.default = Aggregation;\n", "\nObject.defineProperty(exports, \"__esModule\", { value: true });\nconst query_1 = require(\"./commands/query\");\nconst logic_1 = require(\"./commands/logic\");\nconst update_1 = require(\"./commands/update\");\nconst type_1 = require(\"./utils/type\");\nconst aggregate_1 = require(\"./aggregate\");\nexports.Command = {\n    eq(val) {\n        return new query_1.QueryCommand(query_1.QUERY_COMMANDS_LITERAL.EQ, [val]);\n    },\n    neq(val) {\n        return new query_1.QueryCommand(query_1.QUERY_COMMANDS_LITERAL.NEQ, [val]);\n    },\n    lt(val) {\n        return new query_1.QueryCommand(query_1.QUERY_COMMANDS_LITERAL.LT, [val]);\n    },\n    lte(val) {\n        return new query_1.QueryCommand(query_1.QUERY_COMMANDS_LITERAL.LTE, [val]);\n    },\n    gt(val) {\n        return new query_1.QueryCommand(query_1.QUERY_COMMANDS_LITERAL.GT, [val]);\n    },\n    gte(val) {\n        return new query_1.QueryCommand(query_1.QUERY_COMMANDS_LITERAL.GTE, [val]);\n    },\n    in(val) {\n        return new query_1.QueryCommand(query_1.QUERY_COMMANDS_LITERAL.IN, val);\n    },\n    nin(val) {\n        return new query_1.QueryCommand(query_1.QUERY_COMMANDS_LITERAL.NIN, val);\n    },\n    all(val) {\n        return new query_1.QueryCommand(query_1.QUERY_COMMANDS_LITERAL.ALL, val);\n    },\n    elemMatch(val) {\n        return new query_1.QueryCommand(query_1.QUERY_COMMANDS_LITERAL.ELEM_MATCH, [val]);\n    },\n    exists(val) {\n        return new query_1.QueryCommand(query_1.QUERY_COMMANDS_LITERAL.EXISTS, [val]);\n    },\n    size(val) {\n        return new query_1.QueryCommand(query_1.QUERY_COMMANDS_LITERAL.SIZE, [val]);\n    },\n    mod(val) {\n        return new query_1.QueryCommand(query_1.QUERY_COMMANDS_LITERAL.MOD, [val]);\n    },\n    geoNear(val) {\n        return new query_1.QueryCommand(query_1.QUERY_COMMANDS_LITERAL.GEO_NEAR, [val]);\n    },\n    geoWithin(val) {\n        return new query_1.QueryCommand(query_1.QUERY_COMMANDS_LITERAL.GEO_WITHIN, [val]);\n    },\n    geoIntersects(val) {\n        return new query_1.QueryCommand(query_1.QUERY_COMMANDS_LITERAL.GEO_INTERSECTS, [val]);\n    },\n    and(...__expressions__) {\n        const expressions = type_1.isArray(arguments[0]) ? arguments[0] : Array.from(arguments);\n        return new logic_1.LogicCommand(logic_1.LOGIC_COMMANDS_LITERAL.AND, expressions);\n    },\n    nor(...__expressions__) {\n        const expressions = type_1.isArray(arguments[0]) ? arguments[0] : Array.from(arguments);\n        return new logic_1.LogicCommand(logic_1.LOGIC_COMMANDS_LITERAL.NOR, expressions);\n    },\n    or(...__expressions__) {\n        const expressions = type_1.isArray(arguments[0]) ? arguments[0] : Array.from(arguments);\n        return new logic_1.LogicCommand(logic_1.LOGIC_COMMANDS_LITERAL.OR, expressions);\n    },\n    not(...__expressions__) {\n        const expressions = type_1.isArray(arguments[0]) ? arguments[0] : Array.from(arguments);\n        return new logic_1.LogicCommand(logic_1.LOGIC_COMMANDS_LITERAL.NOT, expressions);\n    },\n    set(val) {\n        return new update_1.UpdateCommand(update_1.UPDATE_COMMANDS_LITERAL.SET, [val]);\n    },\n    remove() {\n        return new update_1.UpdateCommand(update_1.UPDATE_COMMANDS_LITERAL.REMOVE, []);\n    },\n    inc(val) {\n        return new update_1.UpdateCommand(update_1.UPDATE_COMMANDS_LITERAL.INC, [val]);\n    },\n    mul(val) {\n        return new update_1.UpdateCommand(update_1.UPDATE_COMMANDS_LITERAL.MUL, [val]);\n    },\n    push(...args) {\n        let values;\n        if (type_1.isObject(args[0]) && args[0].hasOwnProperty('each')) {\n            const options = args[0];\n            values = {\n                $each: options.each,\n                $position: options.position,\n                $sort: options.sort,\n                $slice: options.slice\n            };\n        }\n        else if (type_1.isArray(args[0])) {\n            values = args[0];\n        }\n        else {\n            values = Array.from(args);\n        }\n        return new update_1.UpdateCommand(update_1.UPDATE_COMMANDS_LITERAL.PUSH, values);\n    },\n    pull(values) {\n        return new update_1.UpdateCommand(update_1.UPDATE_COMMANDS_LITERAL.PULL, values);\n    },\n    pullAll(values) {\n        return new update_1.UpdateCommand(update_1.UPDATE_COMMANDS_LITERAL.PULL_ALL, values);\n    },\n    pop() {\n        return new update_1.UpdateCommand(update_1.UPDATE_COMMANDS_LITERAL.POP, []);\n    },\n    shift() {\n        return new update_1.UpdateCommand(update_1.UPDATE_COMMANDS_LITERAL.SHIFT, []);\n    },\n    unshift(...__values__) {\n        const values = type_1.isArray(arguments[0]) ? arguments[0] : Array.from(arguments);\n        return new update_1.UpdateCommand(update_1.UPDATE_COMMANDS_LITERAL.UNSHIFT, values);\n    },\n    addToSet(values) {\n        return new update_1.UpdateCommand(update_1.UPDATE_COMMANDS_LITERAL.ADD_TO_SET, values);\n    },\n    rename(values) {\n        return new update_1.UpdateCommand(update_1.UPDATE_COMMANDS_LITERAL.RENAME, [values]);\n    },\n    bit(values) {\n        return new update_1.UpdateCommand(update_1.UPDATE_COMMANDS_LITERAL.BIT, [values]);\n    },\n    max(values) {\n        return new update_1.UpdateCommand(update_1.UPDATE_COMMANDS_LITERAL.MAX, [values]);\n    },\n    min(values) {\n        return new update_1.UpdateCommand(update_1.UPDATE_COMMANDS_LITERAL.MIN, [values]);\n    },\n    expr(values) {\n        return {\n            $expr: values\n        };\n    },\n    jsonSchema(schema) {\n        return {\n            $jsonSchema: schema\n        };\n    },\n    text(values) {\n        if (type_1.isString(values)) {\n            return {\n                $search: values.search\n            };\n        }\n        else {\n            return {\n                $search: values.search,\n                $language: values.language,\n                $caseSensitive: values.caseSensitive,\n                $diacriticSensitive: values.diacriticSensitive\n            };\n        }\n    },\n    aggregate: {\n        pipeline() {\n            return new aggregate_1.default();\n        },\n        abs: (param) => new AggregationOperator('abs', param),\n        add: (param) => new AggregationOperator('add', param),\n        ceil: (param) => new AggregationOperator('ceil', param),\n        divide: (param) => new AggregationOperator('divide', param),\n        exp: (param) => new AggregationOperator('exp', param),\n        floor: (param) => new AggregationOperator('floor', param),\n        ln: (param) => new AggregationOperator('ln', param),\n        log: (param) => new AggregationOperator('log', param),\n        log10: (param) => new AggregationOperator('log10', param),\n        mod: (param) => new AggregationOperator('mod', param),\n        multiply: (param) => new AggregationOperator('multiply', param),\n        pow: (param) => new AggregationOperator('pow', param),\n        sqrt: (param) => new AggregationOperator('sqrt', param),\n        subtract: (param) => new AggregationOperator('subtract', param),\n        trunc: (param) => new AggregationOperator('trunc', param),\n        arrayElemAt: (param) => new AggregationOperator('arrayElemAt', param),\n        arrayToObject: (param) => new AggregationOperator('arrayToObject', param),\n        concatArrays: (param) => new AggregationOperator('concatArrays', param),\n        filter: (param) => new AggregationOperator('filter', param),\n        in: (param) => new AggregationOperator('in', param),\n        indexOfArray: (param) => new AggregationOperator('indexOfArray', param),\n        isArray: (param) => new AggregationOperator('isArray', param),\n        map: (param) => new AggregationOperator('map', param),\n        range: (param) => new AggregationOperator('range', param),\n        reduce: (param) => new AggregationOperator('reduce', param),\n        reverseArray: (param) => new AggregationOperator('reverseArray', param),\n        size: (param) => new AggregationOperator('size', param),\n        slice: (param) => new AggregationOperator('slice', param),\n        zip: (param) => new AggregationOperator('zip', param),\n        and: (param) => new AggregationOperator('and', param),\n        not: (param) => new AggregationOperator('not', param),\n        or: (param) => new AggregationOperator('or', param),\n        cmp: (param) => new AggregationOperator('cmp', param),\n        eq: (param) => new AggregationOperator('eq', param),\n        gt: (param) => new AggregationOperator('gt', param),\n        gte: (param) => new AggregationOperator('gte', param),\n        lt: (param) => new AggregationOperator('lt', param),\n        lte: (param) => new AggregationOperator('lte', param),\n        neq: (param) => new AggregationOperator('ne', param),\n        cond: (param) => new AggregationOperator('cond', param),\n        ifNull: (param) => new AggregationOperator('ifNull', param),\n        switch: (param) => new AggregationOperator('switch', param),\n        dateFromParts: (param) => new AggregationOperator('dateFromParts', param),\n        dateFromString: (param) => new AggregationOperator('dateFromString', param),\n        dayOfMonth: (param) => new AggregationOperator('dayOfMonth', param),\n        dayOfWeek: (param) => new AggregationOperator('dayOfWeek', param),\n        dayOfYear: (param) => new AggregationOperator('dayOfYear', param),\n        isoDayOfWeek: (param) => new AggregationOperator('isoDayOfWeek', param),\n        isoWeek: (param) => new AggregationOperator('isoWeek', param),\n        isoWeekYear: (param) => new AggregationOperator('isoWeekYear', param),\n        millisecond: (param) => new AggregationOperator('millisecond', param),\n        minute: (param) => new AggregationOperator('minute', param),\n        month: (param) => new AggregationOperator('month', param),\n        second: (param) => new AggregationOperator('second', param),\n        hour: (param) => new AggregationOperator('hour', param),\n        week: (param) => new AggregationOperator('week', param),\n        year: (param) => new AggregationOperator('year', param),\n        literal: (param) => new AggregationOperator('literal', param),\n        mergeObjects: (param) => new AggregationOperator('mergeObjects', param),\n        objectToArray: (param) => new AggregationOperator('objectToArray', param),\n        allElementsTrue: (param) => new AggregationOperator('allElementsTrue', param),\n        anyElementTrue: (param) => new AggregationOperator('anyElementTrue', param),\n        setDifference: (param) => new AggregationOperator('setDifference', param),\n        setEquals: (param) => new AggregationOperator('setEquals', param),\n        setIntersection: (param) => new AggregationOperator('setIntersection', param),\n        setIsSubset: (param) => new AggregationOperator('setIsSubset', param),\n        setUnion: (param) => new AggregationOperator('setUnion', param),\n        concat: (param) => new AggregationOperator('concat', param),\n        dateToString: (param) => new AggregationOperator('dateToString', param),\n        indexOfBytes: (param) => new AggregationOperator('indexOfBytes', param),\n        indexOfCP: (param) => new AggregationOperator('indexOfCP', param),\n        split: (param) => new AggregationOperator('split', param),\n        strLenBytes: (param) => new AggregationOperator('strLenBytes', param),\n        strLenCP: (param) => new AggregationOperator('strLenCP', param),\n        strcasecmp: (param) => new AggregationOperator('strcasecmp', param),\n        substr: (param) => new AggregationOperator('substr', param),\n        substrBytes: (param) => new AggregationOperator('substrBytes', param),\n        substrCP: (param) => new AggregationOperator('substrCP', param),\n        toLower: (param) => new AggregationOperator('toLower', param),\n        toUpper: (param) => new AggregationOperator('toUpper', param),\n        meta: (param) => new AggregationOperator('meta', param),\n        addToSet: (param) => new AggregationOperator('addToSet', param),\n        avg: (param) => new AggregationOperator('avg', param),\n        first: (param) => new AggregationOperator('first', param),\n        last: (param) => new AggregationOperator('last', param),\n        max: (param) => new AggregationOperator('max', param),\n        min: (param) => new AggregationOperator('min', param),\n        push: (param) => new AggregationOperator('push', param),\n        stdDevPop: (param) => new AggregationOperator('stdDevPop', param),\n        stdDevSamp: (param) => new AggregationOperator('stdDevSamp', param),\n        sum: (param) => new AggregationOperator('sum', param),\n        let: (param) => new AggregationOperator('let', param)\n    },\n    project: {\n        slice: (param) => new ProjectionOperator('slice', param),\n        elemMatch: (param) => new ProjectionOperator('elemMatch', param)\n    }\n};\nclass AggregationOperator {\n    constructor(name, param) {\n        this['$' + name] = param;\n    }\n}\nclass ProjectionOperator {\n    constructor(name, param) {\n        this['$' + name] = param;\n    }\n}\nexports.default = exports.Command;\n", "\nObject.defineProperty(exports, \"__esModule\", { value: true });\nconst symbol_1 = require(\"../helper/symbol\");\nclass RegExp {\n    constructor({ regexp, options }) {\n        if (!regexp) {\n            throw new TypeError('regexp must be a string');\n        }\n        this.$regex = regexp;\n        this.$options = options;\n    }\n    parse() {\n        return {\n            $regex: this.$regex,\n            $options: this.$options\n        };\n    }\n    get _internalType() {\n        return symbol_1.SYMBOL_REGEXP;\n    }\n}\nexports.RegExp = RegExp;\nfunction RegExpConstructor(param) {\n    return new RegExp(param);\n}\nexports.RegExpConstructor = RegExpConstructor;\n", "\nObject.defineProperty(exports, \"__esModule\", { value: true });\nconst index_1 = require(\"../index\");\nconst collection_1 = require(\"./collection\");\nconst code_1 = require(\"../const/code\");\nconst START = 'database.startTransaction';\nconst COMMIT = 'database.commitTransaction';\nconst ABORT = 'database.abortTransaction';\nclass Transaction {\n    constructor(db) {\n        this._db = db;\n        this._request = new index_1.Db.reqClass(this._db.config);\n        this.aborted = false;\n        this.commited = false;\n        this.inited = false;\n    }\n    async init() {\n        const res = await this._request.send(START);\n        if (res.code) {\n            throw res;\n        }\n        this.inited = true;\n        this._id = res.transactionId;\n    }\n    collection(collName) {\n        if (!collName) {\n            throw new Error('Collection name is required');\n        }\n        return new collection_1.CollectionReference(this, collName);\n    }\n    getTransactionId() {\n        return this._id;\n    }\n    getRequestMethod() {\n        return this._request;\n    }\n    async commit() {\n        const param = {\n            transactionId: this._id\n        };\n        const res = await this._request.send(COMMIT, param);\n        if (res.code)\n            throw res;\n        this.commited = true;\n        return res;\n    }\n    async rollback(customRollbackRes) {\n        const param = {\n            transactionId: this._id\n        };\n        const res = await this._request.send(ABORT, param);\n        if (res.code)\n            throw res;\n        this.aborted = true;\n        this.abortReason = customRollbackRes;\n        return res;\n    }\n}\nexports.Transaction = Transaction;\nasync function startTransaction() {\n    const transaction = new Transaction(this);\n    await transaction.init();\n    return transaction;\n}\nexports.startTransaction = startTransaction;\nasync function runTransaction(callback, times = 3) {\n    let transaction;\n    try {\n        transaction = new Transaction(this);\n        await transaction.init();\n        const callbackRes = await callback(transaction);\n        if (transaction.aborted === true) {\n            throw transaction.abortReason;\n        }\n        await transaction.commit();\n        return callbackRes;\n    }\n    catch (error) {\n        if (transaction.inited === false) {\n            throw error;\n        }\n        const throwWithRollback = async (error) => {\n            if (!transaction.aborted && !transaction.commited) {\n                try {\n                    await transaction.rollback();\n                }\n                catch (err) {\n                }\n                throw error;\n            }\n            if (transaction.aborted === true) {\n                throw transaction.abortReason;\n            }\n            throw error;\n        };\n        if (times <= 0) {\n            await throwWithRollback(error);\n        }\n        if (error && error.code === code_1.ERRORS.DATABASE_TRANSACTION_CONFLICT.code) {\n            return await runTransaction.bind(this)(callback, --times);\n        }\n        await throwWithRollback(error);\n    }\n}\nexports.runTransaction = runTransaction;\n", "\nObject.defineProperty(exports, \"__esModule\", { value: true });\nconst document_1 = require(\"./document\");\nconst query_1 = require(\"./query\");\nclass CollectionReference extends query_1.Query {\n    constructor(transaction, coll) {\n        super(transaction, coll);\n    }\n    get name() {\n        return this._coll;\n    }\n    doc(docID) {\n        if (typeof docID !== 'string' && typeof docID !== 'number') {\n            throw new Error('docId必须为字符串或数字');\n        }\n        return new document_1.DocumentReference(this._transaction, this._coll, docID);\n    }\n    add(data) {\n        let docID;\n        if (data._id !== undefined) {\n            docID = data._id;\n        }\n        let docRef = new document_1.DocumentReference(this._transaction, this._coll, docID);\n        return docRef.create(data);\n    }\n}\nexports.CollectionReference = CollectionReference;\n", "\nObject.defineProperty(exports, \"__esModule\", { value: true });\nconst bson_1 = require(\"bson\");\nconst code_1 = require(\"../const/code\");\nconst update_1 = require(\"../serializer/update\");\nconst datatype_1 = require(\"../serializer/datatype\");\nconst util_1 = require(\"../util\");\nconst GET_DOC = 'database.getInTransaction';\nconst UPDATE_DOC = 'database.updateDocInTransaction';\nconst DELETE_DOC = 'database.deleteDocInTransaction';\nconst INSERT_DOC = 'database.insertDocInTransaction';\nclass DocumentReference {\n    constructor(transaction, coll, docID) {\n        this._coll = coll;\n        this.id = docID;\n        this._transaction = transaction;\n        this._request = this._transaction.getRequestMethod();\n        this._transactionId = this._transaction.getTransactionId();\n    }\n    async create(data) {\n        let params = {\n            collectionName: this._coll,\n            transactionId: this._transactionId,\n            data: bson_1.EJSON.stringify(datatype_1.serialize(data), { relaxed: false })\n        };\n        if (this.id) {\n            params['_id'] = this.id;\n        }\n        const res = await this._request.send(INSERT_DOC, params);\n        if (res.code) {\n            throw res;\n        }\n        const inserted = bson_1.EJSON.parse(res.inserted);\n        const ok = bson_1.EJSON.parse(res.ok);\n        if (ok == 1 && inserted == 1) {\n            return Object.assign(Object.assign({}, res), { ok,\n                inserted });\n        }\n        else {\n            throw new Error(code_1.ERRORS.INSERT_DOC_FAIL.message);\n        }\n    }\n    async get() {\n        const param = {\n            collectionName: this._coll,\n            transactionId: this._transactionId,\n            query: {\n                _id: { $eq: this.id }\n            }\n        };\n        const res = await this._request.send(GET_DOC, param);\n        if (res.code)\n            throw res;\n        return {\n            data: res.data !== 'null' ? util_1.Util.formatField(bson_1.EJSON.parse(res.data)) : bson_1.EJSON.parse(res.data),\n            requestId: res.requestId\n        };\n    }\n    async set(data) {\n        const param = {\n            collectionName: this._coll,\n            transactionId: this._transactionId,\n            query: {\n                _id: { $eq: this.id }\n            },\n            data: bson_1.EJSON.stringify(datatype_1.serialize(data), { relaxed: false }),\n            upsert: true\n        };\n        const res = await this._request.send(UPDATE_DOC, param);\n        if (res.code)\n            throw res;\n        return Object.assign(Object.assign({}, res), { updated: bson_1.EJSON.parse(res.updated), upserted: res.upserted\n                ? JSON.parse(res.upserted)\n                : null });\n    }\n    async update(data) {\n        const param = {\n            collectionName: this._coll,\n            transactionId: this._transactionId,\n            query: {\n                _id: { $eq: this.id }\n            },\n            data: bson_1.EJSON.stringify(update_1.UpdateSerializer.encode(data), {\n                relaxed: false\n            })\n        };\n        const res = await this._request.send(UPDATE_DOC, param);\n        if (res.code)\n            throw res;\n        return Object.assign(Object.assign({}, res), { updated: bson_1.EJSON.parse(res.updated) });\n    }\n    async delete() {\n        const param = {\n            collectionName: this._coll,\n            transactionId: this._transactionId,\n            query: {\n                _id: { $eq: this.id }\n            }\n        };\n        const res = await this._request.send(DELETE_DOC, param);\n        if (res.code)\n            throw res;\n        return Object.assign(Object.assign({}, res), { deleted: bson_1.EJSON.parse(res.deleted) });\n    }\n}\nexports.DocumentReference = DocumentReference;\n", "\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.ERRORS = {\n    CREATE_WATCH_NET_ERROR: {\n        code: 'CREATE_WATCH_NET_ERROR',\n        message: 'create watch network error'\n    },\n    CREATE_WACTH_EXCEED_ERROR: {\n        code: 'CREATE_WACTH_EXCEED_ERROR',\n        message: 'maximum connections exceed'\n    },\n    CREATE_WATCH_SERVER_ERROR: {\n        code: 'CREATE_WATCH_SERVER_ERROR',\n        message: 'create watch server error'\n    },\n    CONN_ERROR: {\n        code: 'CONN_ERROR',\n        message: 'connection error'\n    },\n    INVALID_PARAM: {\n        code: 'INVALID_PARAM',\n        message: 'Invalid request param'\n    },\n    INSERT_DOC_FAIL: {\n        code: 'INSERT_DOC_FAIL',\n        message: 'insert document failed'\n    },\n    DATABASE_TRANSACTION_CONFLICT: {\n        code: 'DATABASE_TRANSACTION_CONFLICT',\n        message: 'database transaction conflict'\n    }\n};\n", "\nObject.defineProperty(exports, \"__esModule\", { value: true });\nclass Query {\n    constructor(transaction, coll) {\n        this._coll = coll;\n        this._transaction = transaction;\n    }\n}\nexports.Query = Query;\n"]}