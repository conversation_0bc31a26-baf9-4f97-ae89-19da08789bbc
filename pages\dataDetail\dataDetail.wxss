.container {
  padding: 20rpx;
  background-color: #f7f8fa;
  min-height: 100vh;
}

.detail-card {
  background: #fff;
  border-radius: 12rpx;
  padding: 30rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

/* 头部样式 */
.detail-header {
  padding-bottom: 20rpx;
  border-bottom: 1rpx solid #eee;
  margin-bottom: 30rpx;
}

.detail-title {
  font-size: 32rpx;
  color: #333;
  font-weight: bold;
  display: block;
  margin-bottom: 10rpx;
}

.detail-time {
  font-size: 28rpx;
  color: #666;
}

/* 分析结果样式 */
.section-title {
  font-size: 32rpx;
  color: #333;
  font-weight: bold;
  margin-bottom: 20rpx;
}

.result-values {
  background: #f8f9fa;
  padding: 20rpx;
  border-radius: 8rpx;
  margin-bottom: 30rpx;
}

.value-item {
  display: flex;
  align-items: center;
  margin-bottom: 16rpx;
}

.value-item:last-child {
  margin-bottom: 0;
}

.value-label {
  width: 120rpx;
  font-size: 28rpx;
  color: #666;
}

.value-text {
  flex: 1;
  font-size: 28rpx;
  color: #333;
}

/* 图片区域样式 */
.image-section {
  margin-bottom: 30rpx;
}

.image-row {
  display: flex;
  justify-content: space-between;
  gap: 20rpx;
}

.image-container {
  flex: 1;
  text-align: center;
}

.detail-canvas {
  width: 300rpx;
  height: 300rpx;
  background: #f8f9fa;
  border-radius: 8rpx;
  margin-bottom: 10rpx;
}

.image-container text {
  font-size: 28rpx;
  color: #666;
}

/* 参数设置样式 */
.params-section {
  margin-top: 30rpx;
  padding-top: 30rpx;
  border-top: 1rpx solid #eee;
}

.params-list {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20rpx;
}

.param-item {
  display: flex;
  align-items: center;
  background: #f8f9fa;
  padding: 16rpx;
  border-radius: 8rpx;
}

.param-label {
  width: 140rpx;
  font-size: 28rpx;
  color: #666;
}

.param-value {
  flex: 1;
  font-size: 28rpx;
  color: #333;
  text-align: right;
} 