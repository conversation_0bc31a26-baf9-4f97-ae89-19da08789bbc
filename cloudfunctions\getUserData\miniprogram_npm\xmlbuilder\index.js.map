{"version": 3, "sources": ["index.js", "Utility.js", "XMLDOMImplementation.js", "XMLDocument.js", "XMLDOMConfiguration.js", "XMLDOMErrorHandler.js", "XMLDOMStringList.js", "XMLNode.js", "XMLElement.js", "NodeType.js", "XMLAttribute.js", "XMLNamedNodeMap.js", "XMLCData.js", "XMLCharacterData.js", "XMLComment.js", "XMLDeclaration.js", "XMLDocType.js", "XMLDTDAttList.js", "XMLDTDEntity.js", "XMLDTDElement.js", "XMLDTDNotation.js", "XMLRaw.js", "XMLText.js", "XMLProcessingInstruction.js", "XMLDummy.js", "XMLNodeList.js", "DocumentPosition.js", "XMLStringifier.js", "XMLStringWriter.js", "XMLWriterBase.js", "WriterState.js", "XMLDocumentCB.js", "XMLStreamWriter.js"], "names": [], "mappings": ";;;;;;;AAAA;AACA;AACA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ACHA,AFMA;ACFA,ACHA,AFMA;ACFA,ACHA,AFMA;ACFA,ACHA,ACHA,AHSA;ACFA,ACHA,ACHA,AHSA;ACFA,ACHA,ACHA,AHSA;ACFA,AGTA,AFMA,ACHA,AHSA;ACFA,AGTA,AFMA,ACHA,AHSA;ACFA,AGTA,AFMA,ACHA,AHSA;ACFA,AGTA,ACHA,AHSA,ACHA,AHSA;ACFA,AGTA,ACHA,AHSA,ACHA,AHSA;ACFA,AGTA,ACHA,AHSA,ACHA,AHSA;ACFA,AGTA,ACHA,AHSA,AIZA,AHSA,AHSA;ACFA,AGTA,ACHA,AHSA,AIZA,AHSA,AHSA;ACFA,AGTA,ACHA,AHSA,AIZA,AHSA,AHSA;ACFA,AGTA,ACHA,AHSA,AIZA,AHSA,AIZA,APqBA;ACFA,AGTA,ACHA,AHSA,AIZA,AHSA,AIZA,APqBA;ACFA,AGTA,ACHA,AHSA,AIZA,AHSA,AIZA,APqBA;ACFA,AGTA,ACHA,AHSA,AIZA,AHSA,AKfA,ADGA,APqBA;ACFA,AGTA,ACHA,AHSA,AIZA,AHSA,AKfA,ADGA,APqBA;ACFA,AGTA,ACHA,AHSA,AIZA,AHSA,AKfA,ADGA,APqBA;AS1BA,ARwBA,AGTA,ACHA,AHSA,AIZA,AHSA,AKfA,ADGA,APqBA;AS1BA,ARwBA,AGTA,ACHA,AHSA,AIZA,AHSA,AKfA,ADGA,APqBA;AS1BA,ARwBA,AGTA,ACHA,AHSA,AIZA,AHSA,AKfA,ADGA,APqBA;AS1BA,ARwBA,AS3BA,ANkBA,ACHA,AHSA,AIZA,AHSA,AKfA,ADGA,APqBA;AS1BA,ARwBA,AS3BA,ANkBA,ACHA,AHSA,AIZA,AHSA,AKfA,ADGA,APqBA;AS1BA,ARwBA,AS3BA,ANkBA,AFMA,AIZA,AHSA,AKfA,ADGA,APqBA;AS1BA,ARwBA,AS3BA,ANkBA,AFMA,AIZA,AHSA,AKfA,AGTA,AJYA,APqBA;AS1BA,ARwBA,AS3BA,ANkBA,AFMA,AIZA,AHSA,AKfA,AGTA,AJYA,APqBA;AS1BA,ARwBA,AS3BA,ANkBA,AFMA,AIZA,AHSA,AKfA,AGTA,AJYA,APqBA;AS1BA,ARwBA,AS3BA,AENA,ARwBA,AFMA,AIZA,AHSA,AKfA,AGTA,AJYA,APqBA;AS1BA,ARwBA,AS3BA,AENA,ARwBA,AFMA,AIZA,AHSA,AKfA,AGTA,AJYA,APqBA;AS1BA,ARwBA,AS3BA,AENA,ARwBA,AFMA,AIZA,AHSA,AKfA,AGTA,AJYA,APqBA;AS1BA,ARwBA,AS3BA,AENA,ACHA,AT2BA,AENA,AHSA,AKfA,AGTA,AJYA,APqBA;AS1BA,ARwBA,AS3BA,AENA,ACHA,AT2BA,AENA,AHSA,AKfA,AGTA,AJYA,APqBA;AS1BA,ARwBA,AS3BA,AENA,ACHA,AT2BA,AENA,AHSA,AKfA,AGTA,AJYA,APqBA;AS1BA,ARwBA,AS3BA,AENA,ACHA,ACHA,AV8BA,AENA,AHSA,AKfA,AGTA,AJYA,APqBA;AS1BA,ARwBA,AS3BA,AENA,ACHA,ACHA,AV8BA,AENA,AHSA,AKfA,AGTA,AJYA,APqBA;AS1BA,ARwBA,AS3BA,AENA,ACHA,ACHA,AV8BA,AENA,AHSA,AKfA,AGTA,AJYA,APqBA;AS1BA,ARwBA,AS3BA,AENA,ACHA,ACHA,AV8BA,AENA,AS3BA,AZoCA,AKfA,AGTA,AJYA,APqBA;AS1BA,ARwBA,AS3BA,AENA,ACHA,ACHA,AV8BA,AENA,AS3BA,AZoCA,AKfA,AGTA,AJYA,APqBA;AS1BA,ARwBA,AS3BA,AENA,ACHA,ACHA,AV8BA,AWjCA,AZoCA,AKfA,AGTA,AJYA,APqBA;AS1BA,ARwBA,AS3BA,AENA,ACHA,ACHA,AV8BA,AWjCA,ACHA,AbuCA,AKfA,AGTA,AJYA,APqBA;AS1BA,ARwBA,AS3BA,AENA,ACHA,ACHA,AV8BA,AWjCA,ACHA,AbuCA,AKfA,AGTA,AJYA,APqBA;AS1BA,ARwBA,AS3BA,AENA,ACHA,ACHA,AV8BA,AWjCA,ACHA,AbuCA,AKfA,AGTA,AJYA,APqBA;ACFA,AS3BA,AENA,ACHA,ACHA,AV8BA,AavCA,AFMA,ACHA,AbuCA,AKfA,AGTA,AJYA,APqBA;ACFA,AS3BA,AENA,ACHA,ACHA,AV8BA,AavCA,AFMA,ACHA,AbuCA,AKfA,AGTA,AJYA,APqBA;ACFA,AS3BA,AENA,ACHA,ACHA,AV8BA,AavCA,AFMA,ACHA,AbuCA,AKfA,AGTA,AJYA,APqBA;ACFA,AS3BA,AENA,ACHA,ACHA,AV8BA,AavCA,ACHA,AHSA,ACHA,AbuCA,AKfA,AGTA,AJYA,APqBA;ACFA,AS3BA,AENA,ACHA,ACHA,AV8BA,AavCA,ACHA,AHSA,ACHA,AbuCA,AKfA,AGTA,AJYA,APqBA;ACFA,AS3BA,AENA,ACHA,ACHA,AV8BA,AavCA,ACHA,AHSA,ACHA,AbuCA,AKfA,AGTA,AJYA,APqBA;ACFA,AS3BA,AENA,ACHA,ACHA,AV8BA,AavCA,AENA,ADGA,AHSA,ACHA,AbuCA,AKfA,AGTA,AJYA,APqBA;ACFA,AS3BA,AENA,ACHA,ACHA,AV8BA,AavCA,AENA,ADGA,AHSA,ACHA,AbuCA,AKfA,AGTA,AJYA,APqBA;ACFA,AS3BA,AENA,ACHA,ACHA,AV8BA,AavCA,AENA,ADGA,AHSA,ACHA,AbuCA,AKfA,AGTA,AJYA,APqBA;ACFA,AS3BA,AENA,ACHA,ACHA,AV8BA,AavCA,AENA,ADGA,AENA,ALeA,ACHA,AbuCA,AKfA,AGTA,AJYA,APqBA;ACFA,AS3BA,AENA,ACHA,ACHA,AV8BA,AavCA,AENA,ADGA,AENA,ALeA,ACHA,AbuCA,AKfA,AGTA,AJYA,APqBA;ACFA,AS3BA,AENA,ACHA,ACHA,AV8BA,AavCA,AENA,ADGA,AENA,ALeA,ACHA,AbuCA,AKfA,AGTA,AJYA,APqBA;ACFA,AS3BA,AENA,ACHA,ACHA,AV8BA,AavCA,AENA,ADGA,AENA,ALeA,ACHA,AbuCA,AKfA,AGTA,AJYA,Ac1CA,ArB+DA;ACFA,AS3BA,AENA,ACHA,ACHA,AV8BA,AavCA,AENA,ADGA,AENA,ALeA,ACHA,AbuCA,AKfA,AGTA,AJYA,Ac1CA,ArB+DA;ACFA,AS3BA,AENA,ACHA,ACHA,AV8BA,AavCA,AENA,ADGA,AENA,ALeA,ACHA,AbuCA,AKfA,AGTA,AJYA,Ac1CA,ArB+DA;ACFA,AS3BA,AENA,ACHA,ACHA,AV8BA,AavCA,AENA,ADGA,AENA,ALeA,ACHA,AbuCA,AKfA,AGTA,AJYA,Ac1CA,ACHA;ArBgEA,AS3BA,AENA,ACHA,ACHA,AV8BA,AavCA,AENA,ADGA,AENA,ALeA,ACHA,AbuCA,AKfA,AGTA,AJYA,Ac1CA,ACHA;ArBgEA,AS3BA,AENA,ACHA,ACHA,AV8BA,AavCA,AENA,ADGA,AENA,ALeA,ACHA,AbuCA,AKfA,AGTA,AJYA,Ac1CA,ACHA;ArBgEA,AS3BA,AENA,ACHA,ACHA,AV8BA,AavCA,AENA,ADGA,AENA,ALeA,ACHA,AbuCA,AKfA,AGTA,AJYA,AgBhDA,AFMA,ACHA;ArBgEA,AS3BA,AENA,ACHA,ACHA,AV8BA,AavCA,AENA,ADGA,AENA,ALeA,ACHA,AbuCA,AKfA,AGTA,AJYA,AgBhDA,AFMA,ACHA;ArBgEA,AS3BA,AENA,ACHA,ACHA,AV8BA,AavCA,AENA,ADGA,AENA,ALeA,ACHA,AbuCA,AKfA,AGTA,AJYA,AgBhDA,AFMA,ACHA;ArBgEA,AS3BA,AENA,ACHA,ACHA,AV8BA,AavCA,AENA,ADGA,AENA,ALeA,ACHA,AbuCA,AqB/DA,AhBgDA,AGTA,AJYA,AgBhDA,AFMA,ACHA;ArBgEA,AS3BA,AGTA,ACHA,AV8BA,AavCA,AENA,ADGA,AENA,ALeA,ACHA,AbuCA,AqB/DA,AhBgDA,AGTA,AJYA,AgBhDA,AFMA,ACHA;ArBgEA,AS3BA,AGTA,ACHA,AV8BA,AavCA,AENA,ADGA,AENA,ALeA,ACHA,AbuCA,AqB/DA,AhBgDA,AGTA,AJYA,AgBhDA,AFMA,ACHA;ArBgEA,AS3BA,AGTA,ACHA,AV8BA,AavCA,AENA,ADGA,AENA,ALeA,ACHA,AbuCA,AqB/DA,AhBgDA,AGTA,AJYA,AkBtDA,AFMA,AFMA,ACHA;ArBgEA,AS3BA,AGTA,ACHA,AV8BA,AavCA,AENA,ADGA,AENA,ALeA,ACHA,AbuCA,AqB/DA,AhBgDA,AGTA,AJYA,AkBtDA,AFMA,AFMA,ACHA;ArBgEA,AS3BA,AGTA,ACHA,AGTA,AENA,ADGA,AENA,ALeA,ACHA,AbuCA,AqB/DA,AhBgDA,AGTA,AJYA,AkBtDA,AFMA,AFMA,ACHA;AIXA,AzB2EA,AS3BA,AGTA,ACHA,AGTA,AENA,ADGA,AENA,ALeA,ACHA,AbuCA,AqB/DA,AhBgDA,AGTA,AJYA,AkBtDA,AFMA,AFMA,ACHA;AIXA,AzB2EA,AS3BA,AGTA,AIZA,AENA,ADGA,AENA,ALeA,ACHA,AbuCA,AqB/DA,AhBgDA,AGTA,AJYA,AkBtDA,AFMA,AFMA,ACHA;AIXA,AzB2EA,AS3BA,AGTA,AIZA,AENA,ADGA,AENA,ALeA,ACHA,AbuCA,AqB/DA,AhBgDA,AGTA,AJYA,AkBtDA,AFMA,AFMA,ACHA;AIXA,AzB2EA,AS3BA,AGTA,AIZA,AENA,ADGA,AENA,ALeA,ACHA,AbuCA,AqB/DA,AhBgDA,AGTA,AJYA,AkBtDA,AFMA,AFMA,AMlBA,ALeA;AIXA,AzB2EA,AS3BA,AGTA,AIZA,AENA,ADGA,AENA,ALeA,ACHA,AbuCA,AqB/DA,AhBgDA,AGTA,AJYA,AkBtDA,AFMA,AFMA,AMlBA,ALeA;AIXA,AzB2EA,AS3BA,AGTA,AIZA,AENA,ADGA,AENA,ALeA,ACHA,AbuCA,AqB/DA,AhBgDA,AGTA,AJYA,AkBtDA,AFMA,AFMA,AMlBA,ALeA;AIXA,AzB2EA,AS3BA,AGTA,AIZA,AENA,ADGA,AENA,ALeA,ACHA,AbuCA,AqB/DA,AhBgDA,AGTA,AJYA,AkBtDA,AFMA,AFMA,AOrBA,ADGA,ALeA;AIXA,AzB2EA,AS3BA,AGTA,AIZA,AENA,ADGA,AENA,ALeA,ACHA,AbuCA,AqB/DA,AhBgDA,AGTA,AJYA,AkBtDA,AFMA,AFMA,AOrBA,ADGA,ALeA;AIXA,AzB2EA,AS3BA,AGTA,AIZA,AENA,ADGA,AENA,ALeA,ACHA,AbuCA,AqB/DA,AhBgDA,AGTA,AJYA,AkBtDA,AFMA,AFMA,AOrBA,ADGA,ALeA;AIXA,AhBgDA,AGTA,AIZA,AENA,ADGA,AENA,ALeA,ACHA,AbuCA,AqB/DA,AhBgDA,AGTA,AJYA,AkBtDA,AFMA,AFMA,AOrBA,ADGA,ALeA,AOrBA;AHUA,AhBgDA,AGTA,AIZA,AENA,ADGA,AENA,ALeA,ACHA,AbuCA,AqB/DA,AhBgDA,AGTA,AJYA,AkBtDA,AFMA,AFMA,AOrBA,ADGA,ALeA,AOrBA;AHUA,AhBgDA,AGTA,AIZA,AENA,ADGA,AENA,AJYA,AbuCA,AqB/DA,AhBgDA,AGTA,AJYA,AkBtDA,AFMA,AFMA,AOrBA,ADGA,ALeA,AOrBA;AHUA,AIZA,ApB4DA,AGTA,AIZA,AENA,ADGA,AENA,AJYA,AbuCA,AqB/DA,AhBgDA,AGTA,AJYA,AkBtDA,AFMA,AFMA,AOrBA,ADGA,ALeA,AOrBA;ACFA,ApB4DA,AGTA,AIZA,AENA,ADGA,AENA,AJYA,AbuCA,AqB/DA,AhBgDA,AGTA,AJYA,AkBtDA,AFMA,AFMA,AOrBA,ADGA,ALeA,AOrBA;ACFA,ApB4DA,AGTA,AIZA,AENA,ADGA,AENA,AJYA,AbuCA,AqB/DA,AhBgDA,ADGA,AkBtDA,AFMA,AFMA,AOrBA,ADGA,ALeA,AOrBA;ACFA,ApB4DA,AGTA,AIZA,AENA,ADGA,AENA,AJYA,AbuCA,A4BpFA,APqBA,AhBgDA,ADGA,AkBtDA,AFMA,AFMA,AOrBA,ADGA,ALeA,AOrBA;ACFA,ApB4DA,AGTA,AIZA,AENA,ADGA,AENA,AJYA,AbuCA,A4BpFA,APqBA,AhBgDA,ADGA,AkBtDA,AFMA,AFMA,AOrBA,ADGA,ALeA,AOrBA;ACFA,ApB4DA,AGTA,AIZA,AENA,ADGA,AENA,AJYA,AbuCA,A4BpFA,APqBA,AhBgDA,ADGA,AkBtDA,AFMA,AFMA,AOrBA,ADGA,ALeA,AOrBA;ACFA,ApB4DA,AGTA,AIZA,ACHA,AENA,AJYA,AbuCA,A4BpFA,APqBA,AhBgDA,ADGA,AkBtDA,AFMA,AFMA,AWjCA,AJYA,ADGA,ALeA,AOrBA;ACFA,ApB4DA,AGTA,AIZA,ACHA,AENA,AJYA,AbuCA,A4BpFA,APqBA,AhBgDA,ADGA,AkBtDA,AFMA,AFMA,AWjCA,AJYA,ADGA,ALeA,AOrBA;ACFA,ApB4DA,AGTA,AIZA,ACHA,AENA,AJYA,AbuCA,A4BpFA,APqBA,AhBgDA,ADGA,AkBtDA,AFMA,AFMA,AWjCA,AJYA,ADGA,ALeA,AOrBA;ACFA,ApB4DA,AGTA,AIZA,ACHA,AENA,AJYA,AbuCA,A4BpFA,APqBA,AhBgDA,ADGA,AkBtDA,AFMA,AS3BA,AJYA,ADGA,ALeA,AOrBA;ACFA,ApB4DA,AGTA,AIZA,ACHA,AENA,AJYA,AbuCA,A4BpFA,APqBA,AhBgDA,ADGA,AkBtDA,AFMA,AS3BA,AJYA,ADGA,ALeA,AOrBA;AnB0DA,AGTA,AIZA,ACHA,AENA,AJYA,AbuCA,A4BpFA,APqBA,AhBgDA,ADGA,AkBtDA,AFMA,AS3BA,AJYA,ADGA,ALeA,AOrBA;AnB0DA,AGTA,AIZA,ACHA,AENA,AJYA,AbuCA,A4BpFA,APqBA,AhBgDA,ADGA,AkBtDA,AFMA,AS3BA,AJYA,ADGA,ALeA,AOrBA;AnB0DA,AGTA,AIZA,ACHA,AENA,AJYA,AbuCA,A4BpFA,APqBA,AhBgDA,ADGA,AkBtDA,AFMA,AS3BA,AJYA,ADGA,ALeA,AOrBA;AnB0DA,AGTA,AIZA,ACHA,AENA,AJYA,AbuCA,A4BpFA,AvBqEA,ADGA,AgBhDA,AS3BA,AJYA,ADGA,ALeA,AOrBA;AnB0DA,AGTA,AIZA,ACHA,AENA,AJYA,AbuCA,A4BpFA,AvBqEA,ADGA,AgBhDA,AS3BA,AJYA,ADGA,ALeA,AOrBA;AnB0DA,AGTA,AIZA,ACHA,AENA,AJYA,AbuCA,A4BpFA,AvBqEA,ADGA,AgBhDA,AS3BA,AJYA,ADGA,ALeA,AOrBA;AnB0DA,AGTA,AKfA,AENA,AJYA,AbuCA,A4BpFA,AvBqEA,ADGA,AgBhDA,AS3BA,AJYA,ADGA,ALeA,AOrBA;AnB0DA,AGTA,AKfA,AENA,AJYA,AbuCA,A4BpFA,AvBqEA,ADGA,AgBhDA,AS3BA,AJYA,ADGA,ALeA,AOrBA;AnB0DA,AGTA,AKfA,AENA,AJYA,AbuCA,A4BpFA,AvBqEA,ADGA,AgBhDA,AS3BA,AJYA,ADGA,ALeA,AOrBA;AnB0DA,AGTA,AKfA,AENA,AJYA,AbuCA,A4BpFA,AvBqEA,ADGA,AgBhDA,AS3BA,AJYA,ADGA,ALeA,AOrBA;AnB0DA,AGTA,AKfA,AENA,AJYA,AbuCA,A4BpFA,AvBqEA,ADGA,AgBhDA,AS3BA,AJYA,ADGA,ALeA,AOrBA;AnB0DA,AGTA,AKfA,AENA,AJYA,AbuCA,A4BpFA,AvBqEA,ADGA,AgBhDA,AS3BA,AJYA,ADGA,ALeA,AOrBA;AnB0DA,AGTA,AKfA,AFMA,AbuCA,A4BpFA,AvBqEA,ADGA,AgBhDA,AS3BA,AJYA,ADGA,ALeA,AOrBA;AnB0DA,AGTA,AKfA,AFMA,AbuCA,A4BpFA,AvBqEA,ADGA,AgBhDA,AS3BA,AJYA,ADGA,ALeA,AOrBA;AnB0DA,AGTA,AKfA,AFMA,AbuCA,A4BpFA,AvBqEA,ADGA,AgBhDA,AS3BA,AJYA,ADGA,ALeA,AOrBA;AnB0DA,AGTA,AKfA,AFMA,AbuCA,A4BpFA,AvBqEA,ADGA,AgBhDA,AS3BA,AJYA,ADGA,ALeA,AOrBA;AnB0DA,AGTA,AKfA,AFMA,AbuCA,A4BpFA,AvBqEA,ADGA,AgBhDA,AS3BA,AJYA,ADGA,ALeA,AOrBA;AnB0DA,AGTA,AKfA,AFMA,AbuCA,A4BpFA,AvBqEA,ADGA,AgBhDA,AS3BA,AJYA,ADGA,ALeA,AOrBA;AnB0DA,AQxBA,AFMA,AbuCA,A4BpFA,AvBqEA,ADGA,AyB3EA,AJYA,ADGA,ALeA,AOrBA;AnB0DA,AQxBA,AFMA,AbuCA,A4BpFA,AvBqEA,ADGA,AyB3EA,ALeA,ALeA,AOrBA;AnB0DA,AQxBA,AFMA,AbuCA,A4BpFA,AvBqEA,ADGA,AyB3EA,ALeA,ALeA,AOrBA;AnB0DA,AQxBA,AFMA,AbuCA,A4BpFA,AvBqEA,ADGA,AyB3EA,ALeA,ALeA,AOrBA;AnB0DA,AQxBA,AFMA,AbuCA,A4BpFA,AvBqEA,ADGA,AyB3EA,ALeA,ALeA,AOrBA;AnB0DA,AQxBA,AFMA,AbuCA,A4BpFA,AvBqEA,ADGA,AyB3EA,ALeA,ALeA,AOrBA;AnB0DA,AQxBA,AFMA,AbuCA,A4BpFA,AvBqEA,ADGA,AyB3EA,ALeA,ALeA,AOrBA;AnB0DA,AQxBA,AFMA,AbuCA,A4BpFA,AvBqEA,ADGA,AyB3EA,ALeA,ALeA,AOrBA;AnB0DA,AQxBA,AFMA,AbuCA,A4BpFA,AvBqEA,ADGA,AyB3EA,ALeA,ALeA,AOrBA;AnB0DA,AQxBA,AFMA,AbuCA,A4BpFA,AvBqEA,ADGA,AyB3EA,ALeA,ALeA,AOrBA;AnB0DA,AQxBA,AFMA,AbuCA,A4BpFA,AvBqEA,ADGA,AyB3EA,ALeA,ALeA,AOrBA;AnB0DA,AQxBA,AFMA,AbuCA,A4BpFA,AvBqEA,ADGA,AyB3EA,ALeA,ALeA,AOrBA;AnB0DA,AQxBA,AFMA,AbuCA,A4BpFA,AvBqEA,ADGA,AyB3EA,ALeA,ALeA,AOrBA;AnB0DA,AQxBA,AFMA,AbuCA,A4BpFA,AvBqEA,ADGA,AyB3EA,ALeA,ALeA,AOrBA;AnB0DA,AQxBA,AFMA,AbuCA,A4BpFA,AvBqEA,ADGA,AyB3EA,ALeA,ALeA,AOrBA;AnB0DA,AQxBA,AFMA,AbuCA,A4BpFA,AvBqEA,ADGA,AyB3EA,ALeA,ALeA,AOrBA;AnB0DA,AQxBA,AFMA,AbuCA,A4BpFA,AvBqEA,ADGA,AyB3EA,ALeA,ALeA,AOrBA;AnB0DA,AQxBA,AFMA,AbuCA,A4BpFA,AvBqEA,ADGA,AyB3EA,ALeA,AENA;AnB0DA,AQxBA,AFMA,AbuCA,A4BpFA,AvBqEA,ADGA,AyB3EA,ALeA,AENA;AnB0DA,AQxBA,AFMA,AbuCA,A4BpFA,AvBqEA,ADGA,AyB3EA,ALeA,AENA;AXkCA,AFMA,AbuCA,A4BpFA,AvBqEA,ADGA,AyB3EA,ALeA,AENA;AXkCA,AFMA,AbuCA,A4BpFA,AvBqEA,ADGA,AyB3EA,ALeA,AENA;AXkCA,AFMA,AbuCA,A4BpFA,AvBqEA,ADGA,AyB3EA,ALeA,AENA;AXkCA,AFMA,AbuCA,A4BpFA,AvBqEA,ADGA,AyB3EA,ALeA,AENA;AXkCA,AFMA,AbuCA,A4BpFA,AvBqEA,ADGA,AyB3EA,ALeA,AENA;AXkCA,AFMA,AbuCA,A4BpFA,AvBqEA,ADGA,AyB3EA,ALeA,AENA;AXkCA,AFMA,AbuCA,A4BpFA,AvBqEA,ADGA,AyB3EA,ALeA,AENA;AXkCA,AFMA,AbuCA,A4BpFA,AvBqEA,ADGA,AyB3EA,ALeA,AENA;AXkCA,AFMA,AbuCA,A4BpFA,AvBqEA,ADGA,AyB3EA,ALeA,AENA;AXkCA,AFMA,AbuCA,A4BpFA,AvBqEA,ADGA,AyB3EA,ALeA,AENA;AXkCA,AFMA,AbuCA,A4BpFA,AvBqEA,ADGA,AyB3EA,ALeA,AENA;AXkCA,AFMA,AbuCA,A4BpFA,AvBqEA,ADGA,AyB3EA,ALeA,AENA;AXkCA,AFMA,AbuCA,A4BpFA,AvBqEA,ADGA,AyB3EA,ALeA,AENA;AbwCA,AbuCA,A4BpFA,AvBqEA,ADGA,AyB3EA,ALeA,AENA;AbwCA,AbuCA,A4BpFA,AvBqEA,ADGA,AyB3EA,ALeA,AENA;AbwCA,AbuCA,A4BpFA,AvBqEA,ADGA,AyB3EA,ALeA,AENA;AbwCA,AbuCA,A4BpFA,AvBqEA,ADGA,AyB3EA,ALeA,AENA;AbwCA,AbuCA,A4BpFA,AvBqEA,ADGA,AyB3EA,ALeA,AENA;AbwCA,AbuCA,A4BpFA,AvBqEA,ADGA,AyB3EA,ALeA,AENA;AbwCA,AbuCA,A4BpFA,AvBqEA,ADGA,AyB3EA,ALeA,AENA;AbwCA,AbuCA,A4BpFA,AvBqEA,ADGA,AyB3EA,ALeA,AENA;AbwCA,AbuCA,A4BpFA,AvBqEA,ADGA,AyB3EA,ALeA,AENA;AbwCA,AbuCA,A4BpFA,AvBqEA,ADGA,AyB3EA,ALeA,AENA;AbwCA,AbuCA,A4BpFA,AvBqEA,ADGA,AyB3EA,ALeA,AENA;AbwCA,AbuCA,A4BpFA,AvBqEA,ADGA,AyB3EA,ALeA,AENA;AbwCA,AbuCA,A4BpFA,AvBqEA,ADGA,AyB3EA,ALeA,AENA;AbwCA,AbuCA,A4BpFA,AvBqEA,ADGA,AyB3EA,ALeA,AENA;AbwCA,AbuCA,A4BpFA,AvBqEA,ADGA,AyB3EA,ALeA,AENA;AbwCA,AbuCA,A4BpFA,AvBqEA,ADGA,AyB3EA,ALeA,AENA;AbwCA,AbuCA,A4BpFA,AvBqEA,ADGA,AyB3EA,ALeA,AENA;AbwCA,AbuCA,A4BpFA,AvBqEA,ADGA,AyB3EA,ALeA,AENA;AbwCA,AbuCA,A4BpFA,AvBqEA,ADGA,AyB3EA,ALeA,AENA;AbwCA,AbuCA,A4BpFA,AvBqEA,ADGA,AyB3EA,ALeA,AENA;AbwCA,AbuCA,A4BpFA,AvBqEA,ADGA,AyB3EA,ALeA,AENA;AbwCA,AbuCA,A4BpFA,AvBqEA,ADGA,AyB3EA,ALeA,AENA;AbwCA,AbuCA,A4BpFA,AvBqEA,ADGA,AyB3EA,ALeA,AENA;AbwCA,AbuCA,A4BpFA,AvBqEA,ADGA,AyB3EA,ALeA,AENA;AbwCA,AbuCA,A4BpFA,AvBqEA,ADGA,AyB3EA,ALeA,AENA;AbwCA,AbuCA,A4BpFA,AvBqEA,ADGA,AyB3EA,ALeA,AENA;AbwCA,AbuCA,A4BpFA,AvBqEA,ADGA,AyB3EA,ALeA,AENA;AbwCA,AbuCA,A4BpFA,AvBqEA,ADGA,AyB3EA,ALeA,AENA;AbwCA,AbuCA,A4BpFA,AvBqEA,ADGA,AyB3EA,ALeA,AENA;AbwCA,AbuCA,A4BpFA,AvBqEA,ADGA,AyB3EA,ALeA,AENA;AbwCA,AbuCA,A4BpFA,AvBqEA,ADGA,AyB3EA,ALeA,AENA;AbwCA,AbuCA,A4BpFA,AvBqEA,ADGA,AyB3EA,ALeA,AENA;AbwCA,AbuCA,A4BpFA,AvBqEA,ADGA,AyB3EA,ALeA,AENA;AbwCA,AbuCA,A4BpFA,AvBqEA,ADGA,AyB3EA,ALeA,AENA;AbwCA,AbuCA,A4BpFA,AvBqEA,ADGA,AyB3EA,ALeA,AENA;AbwCA,AbuCA,A4BpFA,AvBqEA,ADGA,AyB3EA,ALeA,AENA;AbwCA,AbuCA,A4BpFA,AvBqEA,ADGA,AyB3EA,ALeA,AENA;AbwCA,AbuCA,A4BpFA,AvBqEA,ADGA,AyB3EA,ALeA,AENA;AbwCA,AbuCA,A4BpFA,AvBqEA,ADGA,AyB3EA,ALeA,AENA;AbwCA,AbuCA,A4BpFA,AvBqEA,ADGA,AyB3EA,ALeA,AENA;AbwCA,AbuCA,A4BpFA,AvBqEA,ADGA,AyB3EA,ALeA,AENA;AbwCA,AbuCA,A4BpFA,AvBqEA,ADGA,AyB3EA,ALeA,AENA;AbwCA,AbuCA,A4BpFA,AvBqEA,ADGA,AyB3EA,ALeA,AENA;AbwCA,AbuCA,A4BpFA,AvBqEA,ADGA,AyB3EA,ALeA,AENA;AbwCA,AbuCA,A4BpFA,AvBqEA,ADGA,AyB3EA,ALeA,AENA;AbwCA,AbuCA,A4BpFA,AvBqEA,ADGA,AyB3EA,ALeA,AENA;AbwCA,AbuCA,A4BpFA,AvBqEA,ADGA,AyB3EA,ALeA,AENA;AbwCA,AbuCA,A4BpFA,AvBqEA,ADGA,AyB3EA,ALeA,AENA;AbwCA,AbuCA,A4BpFA,AvBqEA,ADGA,AyB3EA,ALeA,AENA;AbwCA,AbuCA,A4BpFA,AvBqEA,ADGA,AyB3EA,ALeA,AENA;AbwCA,AbuCA,A4BpFA,AvBqEA,ADGA,AyB3EA,ALeA,AENA;AbwCA,AbuCA,A4BpFA,AvBqEA,ADGA,AyB3EA,ALeA,AENA;AbwCA,AbuCA,A4BpFA,AvBqEA,ADGA,AyB3EA,ALeA,AENA;AbwCA,AbuCA,A4BpFA,AvBqEA,ADGA,AyB3EA,ALeA,AENA;AbwCA,AbuCA,A4BpFA,AvBqEA,ADGA,AyB3EA,ALeA,AENA;AbwCA,AbuCA,A4BpFA,AvBqEA,ADGA,AyB3EA,ALeA,AENA;AbwCA,AbuCA,A4BpFA,AvBqEA,ADGA,AyB3EA,ALeA,AENA;AbwCA,AbuCA,A4BpFA,AvBqEA,ADGA,AyB3EA,ALeA,AENA;AbwCA,AbuCA,A4BpFA,AvBqEA,ADGA,AyB3EA,ALeA,AENA;AbwCA,AbuCA,A4BpFA,AvBqEA,ADGA,AyB3EA,ALeA,AENA;AbwCA,AbuCA,A4BpFA,AvBqEA,ADGA,AyB3EA,ALeA,AENA;AbwCA,AbuCA,A4BpFA,AvBqEA,ADGA,AyB3EA,ALeA,AENA;AbwCA,AbuCA,A4BpFA,AvBqEA,ADGA,AyB3EA,ALeA,AENA;AbwCA,AbuCA,A4BpFA,AvBqEA,ADGA,AyB3EA,ALeA,AENA;AbwCA,AbuCA,A4BpFA,AvBqEA,ADGA,AyB3EA,ALeA,AENA;AbwCA,AbuCA,A4BpFA,AvBqEA,ADGA,AyB3EA,ALeA,AENA;AbwCA,AbuCA,A4BpFA,AvBqEA,ADGA,AyB3EA,ALeA,AENA;AbwCA,AbuCA,A4BpFA,AvBqEA,ADGA,AyB3EA,ALeA,AENA;AbwCA,AbuCA,A4BpFA,AvBqEA,ADGA,AyB3EA,ALeA,AENA;AbwCA,AbuCA,A4BpFA,AvBqEA,ADGA,AyB3EA,ALeA,AENA;AbwCA,AbuCA,A4BpFA,AvBqEA,ADGA,AyB3EA,ALeA,AENA;AbwCA,AbuCA,A4BpFA,AvBqEA,ADGA,AyB3EA,ALeA,AENA;AbwCA,AbuCA,A4BpFA,AvBqEA,ADGA,AyB3EA,ALeA,AENA;AbwCA,AbuCA,A4BpFA,AvBqEA,ADGA,AyB3EA,ALeA,AENA;AbwCA,AbuCA,A4BpFA,AvBqEA,ADGA,AyB3EA,ALeA,AENA;AbwCA,AbuCA,A4BpFA,AvBqEA,ADGA,AyB3EA,ALeA,AENA;AbwCA,AbuCA,A4BpFA,AvBqEA,ADGA,AyB3EA,ALeA,AENA;AbwCA,AbuCA,A4BpFA,AvBqEA,ADGA,AyB3EA,ALeA,AENA;AbwCA,AbuCA,A4BpFA,AvBqEA,ADGA,AyB3EA,ALeA,AENA;AbwCA,AbuCA,A4BpFA,AvBqEA,ADGA,AyB3EA,ALeA,AENA;AbwCA,AbuCA,A4BpFA,AvBqEA,ADGA,AyB3EA,ALeA,AENA;AbwCA,AbuCA,A4BpFA,AvBqEA,ADGA,AyB3EA,ALeA,AENA;AbwCA,AbuCA,A4BpFA,AvBqEA,ADGA,AyB3EA,ALeA,AENA;A1B+EA,A4BpFA,AvBqEA,ADGA,AyB3EA,ALeA,AENA;A1B+EA,A4BpFA,AvBqEA,ADGA,AyB3EA,ALeA,AENA;A1B+EA,A4BpFA,AvBqEA,ADGA,AyB3EA,ALeA,AENA;A1B+EA,A4BpFA,AvBqEA,ADGA,AyB3EA,ALeA,AENA;A1B+EA,A4BpFA,AvBqEA,ADGA,AyB3EA,ALeA,AENA;A1B+EA,A4BpFA,AvBqEA,ADGA,AyB3EA,ALeA,AENA;A1B+EA,A4BpFA,AvBqEA,ADGA,AyB3EA,ALeA,AENA;A1B+EA,A4BpFA,AvBqEA,ADGA,AyB3EA,ALeA,AENA;A1B+EA,A4BpFA,AvBqEA,ADGA,AyB3EA,ALeA,AENA;A1B+EA,A4BpFA,AvBqEA,ADGA,AyB3EA,ALeA,AENA;A1B+EA,A4BpFA,AvBqEA,ADGA,AyB3EA,ALeA,AENA;A1B+EA,A4BpFA,AvBqEA,ADGA,AyB3EA,ALeA,AENA;A1B+EA,A4BpFA,AvBqEA,ADGA,AyB3EA,ALeA,AENA;A1B+EA,A4BpFA,AvBqEA,ADGA,AyB3EA,ALeA,AENA;A1B+EA,A4BpFA,AvBqEA,ADGA,AyB3EA,ALeA,AENA;A1B+EA,A4BpFA,AvBqEA,ADGA,AyB3EA,ALeA,AENA;A1B+EA,A4BpFA,AvBqEA,ADGA,AyB3EA,ALeA,AENA;AELA,AvBqEA,ADGA,AyB3EA,ALeA,AENA;AELA,AvBqEA,ADGA,AyB3EA,ALeA,AENA;AELA,AvBqEA,ADGA,AyB3EA,ALeA,AENA;AELA,AvBqEA,ADGA,AyB3EA,ALeA,AENA;AELA,AvBqEA,ADGA,AyB3EA,ALeA,AENA;AELA,AvBqEA,ADGA,AyB3EA,ALeA,AENA;AELA,AvBqEA,ADGA,AyB3EA,ALeA,AENA;AELA,AvBqEA,ADGA,AyB3EA,ALeA,AENA;AELA,AvBqEA,ADGA,AyB3EA,ALeA,AENA;AELA,AvBqEA,ADGA,AyB3EA,ALeA,AENA;AELA,AvBqEA,ADGA,AyB3EA,ALeA,AENA;AELA,AvBqEA,ADGA,AyB3EA,ALeA,AENA;AELA,AvBqEA,ADGA,AyB3EA,ALeA,AENA;AELA,AvBqEA,ADGA,AyB3EA,ALeA,AENA;AELA,AvBqEA,ADGA,AyB3EA,ALeA,AENA;AELA,AvBqEA,ADGA,AyB3EA,ALeA,AENA;AELA,AvBqEA,ADGA,AyB3EA,ALeA,AENA;AELA,AvBqEA,ADGA,AyB3EA,ALeA,AENA;AELA,AvBqEA,ADGA,AyB3EA,ALeA,AENA;AELA,AvBqEA,ADGA,AyB3EA,ALeA,AENA;AELA,AvBqEA,ADGA,AyB3EA,ALeA,AENA;AELA,AvBqEA,ADGA,AoB5DA,AENA;AELA,AvBqEA,ADGA,AoB5DA,AENA;AELA,AvBqEA,ADGA,AoB5DA,AENA;AELA,AvBqEA,ADGA,AoB5DA,AENA;AELA,AvBqEA,ADGA,AoB5DA,AENA;AELA,AvBqEA,ADGA,AoB5DA,AENA;AELA,AvBqEA,ADGA,AoB5DA,AENA;AELA,AvBqEA,ADGA,AoB5DA,AENA;AELA,AvBqEA,ADGA,AoB5DA,AENA;AELA,AvBqEA,ADGA,AoB5DA,AENA;AELA,AvBqEA,ADGA,AoB5DA,AENA;AELA,AvBqEA,ADGA,AoB5DA,AENA;AELA,AvBqEA,ADGA,AoB5DA,AENA;AELA,AvBqEA,ADGA,AoB5DA,AENA;AELA,AvBqEA,ADGA,AoB5DA,AENA;AELA,AvBqEA,ADGA,AoB5DA,AENA;AELA,AvBqEA,ADGA,AoB5DA,AENA;AELA,AvBqEA,ADGA,AoB5DA,AENA;AELA,AvBqEA,ADGA,AoB5DA,AENA;AELA,AvBqEA,ADGA,AoB5DA,AENA;AELA,AvBqEA,ADGA,AoB5DA,AENA;AELA,AvBqEA,ADGA,AoB5DA,AENA;AELA,AvBqEA,ADGA,AoB5DA,AENA;AELA,AvBqEA,ADGA,AoB5DA,AENA;AELA,AvBqEA,ADGA,AoB5DA,AENA;AELA,AvBqEA,ADGA,AoB5DA,AENA;AELA,AvBqEA,ADGA,AoB5DA,AENA;AELA,AvBqEA,ADGA,AoB5DA,AENA;AELA,AvBqEA,ADGA,AoB5DA,AENA;AELA,AvBqEA,ADGA,AoB5DA,AENA;AELA,AvBqEA,ADGA,AoB5DA,AENA;AELA,AvBqEA,ADGA,AoB5DA,AENA;AELA,AvBqEA,ADGA,AoB5DA,AENA;AELA,AvBqEA,ADGA,AoB5DA,AENA;AELA,AvBqEA,ADGA,AoB5DA,AENA;AELA,AvBqEA,ADGA,AoB5DA,AENA;AELA,AvBqEA,ADGA,AoB5DA,AENA;AELA,AvBqEA,ADGA,AoB5DA,AENA;AELA,AvBqEA,ADGA,AoB5DA,AENA;AELA,AvBqEA,ADGA,AoB5DA,AENA;AELA,AvBqEA,ADGA,AoB5DA,AENA;AELA,AvBqEA,ADGA,AoB5DA,AENA;AELA,AvBqEA,ADGA,AoB5DA,AENA;AELA,AvBqEA,ADGA,AoB5DA,AENA;AELA,AvBqEA,ADGA,AoB5DA,AENA;AELA,AvBqEA,ADGA,AoB5DA,AENA;AELA,AvBqEA,ADGA,AoB5DA,AENA;AELA,AvBqEA,ADGA,AoB5DA,AENA;AELA,AvBqEA,ADGA,AoB5DA,AENA;AELA,AvBqEA,ADGA,AsBlEA;AELA,AxBwEA,AsBlEA;AELA,AxBwEA,AsBlEA;AELA,AxBwEA,AsBlEA;AELA,AxBwEA,AsBlEA;AELA,AxBwEA,AsBlEA;AELA,AxBwEA,AsBlEA;AELA,AxBwEA,AsBlEA;AELA,AxBwEA,AsBlEA;AELA,AxBwEA,AsBlEA;AELA,AxBwEA,AsBlEA;AELA,AxBwEA,AsBlEA;AELA,AxBwEA,AsBlEA;AELA,AxBwEA,AsBlEA;AELA,AxBwEA,AsBlEA;AELA,AxBwEA,AsBlEA;AELA,AxBwEA,AsBlEA;AELA,AxBwEA,AsBlEA;AELA,AxBwEA,AsBlEA;AELA,AxBwEA,AsBlEA;AELA,AxBwEA,AsBlEA;AELA,AxBwEA,AsBlEA;AELA,AxBwEA,AsBlEA;AELA,AxBwEA,AsBlEA;AELA,AxBwEA,AsBlEA;AELA,AxBwEA,AsBlEA;AELA,AxBwEA,AsBlEA;AELA,AxBwEA,AsBlEA;AELA,AxBwEA,AsBlEA;AELA,AxBwEA,AsBlEA;AELA,AxBwEA,AsBlEA;AELA,AxBwEA,AsBlEA;AELA,AxBwEA,AsBlEA;AELA,AxBwEA,AsBlEA;AELA,AxBwEA,AsBlEA;AELA,AxBwEA,AsBlEA;AELA,AxBwEA,AsBlEA;AELA,AxBwEA,AsBlEA;AELA,AxBwEA,AsBlEA;AELA,AxBwEA,AsBlEA;AELA,AxBwEA,AsBlEA;AELA,AxBwEA,AsBlEA;AELA,AxBwEA,AsBlEA;AELA,AxBwEA,AsBlEA;AELA,AxBwEA,AsBlEA;AELA,AxBwEA,AsBlEA;AELA,AxBwEA,AsBlEA;AELA,AxBwEA,AsBlEA;AELA,AxBwEA,AsBlEA;AELA,AxBwEA,AsBlEA;AELA,AxBwEA,AsBlEA;AELA,AxBwEA,AsBlEA;AELA,AxBwEA,AsBlEA;AELA,AxBwEA,AsBlEA;AELA,AxBwEA,AsBlEA;AELA,AxBwEA,AsBlEA;AELA,AxBwEA,AsBlEA;AELA,AxBwEA,AsBlEA;AELA,AxBwEA,AsBlEA;AELA,AxBwEA,AsBlEA;AELA,AxBwEA,AsBlEA;AELA,AxBwEA,AsBlEA;AELA,AxBwEA,AsBlEA;AELA,AxBwEA,AsBlEA;AELA,AxBwEA,AsBlEA;AELA,AxBwEA,AsBlEA;AELA,AxBwEA,AsBlEA;AELA,AxBwEA,AsBlEA;AELA,AxBwEA,AsBlEA;AELA,AxBwEA,AsBlEA;AELA,AxBwEA,AsBlEA;AELA,AxBwEA,AsBlEA;AELA,AxBwEA,AsBlEA;AELA,AxBwEA,AsBlEA;AELA,AxBwEA,AsBlEA;AELA,AxBwEA,AsBlEA;AELA,AxBwEA,AsBlEA;AELA,AxBwEA,AsBlEA;AELA,AxBwEA,AsBlEA;AELA,AxBwEA,AsBlEA;AELA,AxBwEA,AsBlEA;AELA,AxBwEA,AsBlEA;AELA,AxBwEA,AsBlEA;AELA,AxBwEA,AsBlEA;AELA,AxBwEA,AsBlEA;AELA,AxBwEA,AsBlEA;AELA,AxBwEA,AsBlEA;AELA,AxBwEA,AsBlEA;AELA,AxBwEA,AsBlEA;AELA,AxBwEA,AsBlEA;AELA,AxBwEA,AsBlEA;AELA,AxBwEA,AsBlEA;AELA,AxBwEA,AsBlEA;AELA,AxBwEA,AsBlEA;AELA,AxBwEA,AsBlEA;AELA,AxBwEA,AsBlEA;AELA,AxBwEA,AsBlEA;AELA,AxBwEA,AsBlEA;AELA,AxBwEA,AsBlEA;AELA,AxBwEA,AsBlEA;AELA,AxBwEA,AsBlEA;AELA,AxBwEA,AsBlEA;AELA,AxBwEA,AsBlEA;AELA,AxBwEA,AsBlEA;AELA,AxBwEA,AsBlEA;AELA,AxBwEA,AsBlEA;AELA,AxBwEA,AsBlEA;AELA,AxBwEA,AsBlEA;AELA,AxBwEA,AsBlEA;AELA,AxBwEA,AsBlEA;AELA,AxBwEA,AsBlEA;AELA,AxBwEA,AsBlEA;AELA,AxBwEA,AsBlEA;AELA,AxBwEA,AsBlEA;AELA,AxBwEA,AsBlEA;AELA,AxBwEA,AsBlEA;AELA,AxBwEA,AsBlEA;AELA,AxBwEA,AsBlEA;AELA,AxBwEA,AsBlEA;AELA,AxBwEA,AsBlEA;AELA,AxBwEA,AsBlEA;AELA,AxBwEA,AsBlEA;AELA,AxBwEA,AsBlEA;AELA,AxBwEA,AsBlEA;AELA,AxBwEA,AsBlEA;AELA,AxBwEA,AsBlEA;AELA,AxBwEA,AsBlEA;AELA,AxBwEA,AsBlEA;AELA,AxBwEA,AsBlEA;AELA,AxBwEA,AsBlEA;AELA,AxBwEA,AsBlEA;AELA,AxBwEA,AsBlEA;AELA,AxBwEA,AsBlEA;AELA,AxBwEA,AsBlEA;AELA,AxBwEA,AsBlEA;AELA,AxBwEA,AsBlEA;AELA,AxBwEA,AsBlEA;AELA,AxBwEA,AsBlEA;AELA,AxBwEA,AsBlEA;AELA,AxBwEA,AsBlEA;AELA,AxBwEA,AsBlEA;AELA,AxBwEA,AsBlEA;AELA,AxBwEA,AsBlEA;AELA,AxBwEA,AsBlEA;AELA,AxBwEA,AsBlEA;AELA,AxBwEA,AsBlEA;AELA,AxBwEA,AsBlEA;AELA,AxBwEA,AsBlEA;AELA,AxBwEA,AsBlEA;AELA,AxBwEA,AsBlEA;AELA,AxBwEA,AsBlEA;AELA,AxBwEA,AsBlEA;AELA,AxBwEA,AsBlEA;AELA,AxBwEA,AsBlEA;AELA,AxBwEA,AsBlEA;AELA,AxBwEA,AsBlEA;AELA,AxBwEA,AsBlEA;AELA,AxBwEA,AsBlEA;AELA,AxBwEA,AsBlEA;AELA,AxBwEA,AsBlEA;AELA,AxBwEA,AsBlEA;AELA,AxBwEA,AsBlEA;AELA,AxBwEA,AsBlEA;AELA,AxBwEA,AsBlEA;AELA,AxBwEA,AsBlEA;AELA,AxBwEA,AsBlEA;AELA,AxBwEA,AsBlEA;AELA,AxBwEA,AsBlEA;AELA,AxBwEA,AsBlEA;AELA,AxBwEA,AsBlEA;AELA,AxBwEA,AsBlEA;AELA,AxBwEA,AsBlEA;AELA,AxBwEA,AsBlEA;AELA,AxBwEA,AsBlEA;AELA,AxBwEA,AsBlEA;AELA,AxBwEA,AsBlEA;AELA,AxBwEA,AsBlEA;AELA,AxBwEA,AsBlEA;AELA,AxBwEA,AsBlEA;AELA,AxBwEA,AsBlEA;AELA,AxBwEA,AsBlEA;AELA,AxBwEA,AsBlEA;AELA,AxBwEA,AsBlEA;AELA,AxBwEA,AsBlEA;AELA,AxBwEA,AsBlEA;AELA,AxBwEA,AsBlEA;AELA,AxBwEA,AsBlEA;AELA,AxBwEA,AsBlEA;AELA,AxBwEA,AsBlEA;AELA,AxBwEA,AsBlEA;AELA,AxBwEA,AsBlEA;AELA,AxBwEA,AsBlEA;AELA,AxBwEA,AsBlEA;AELA,AxBwEA,AsBlEA;AELA,AxBwEA;AwBvEA,AxBwEA;AwBvEA,AxBwEA;AwBvEA,AxBwEA;AwBvEA,AxBwEA;AwBvEA,AxBwEA;AwBvEA,AxBwEA;AwBvEA,AxBwEA;AwBvEA,AxBwEA;AwBvEA,AxBwEA;AwBvEA,AxBwEA;AwBvEA,AxBwEA;AwBvEA,AxBwEA;AwBvEA,AxBwEA;AwBvEA,AxBwEA;AwBvEA,AxBwEA;AwBvEA,AxBwEA;AwBvEA,AxBwEA;AwBvEA,AxBwEA;AwBvEA,AxBwEA;AwBvEA,AxBwEA;AwBvEA,AxBwEA;AwBvEA,AxBwEA;AwBvEA,AxBwEA;AwBvEA,AxBwEA;AwBvEA,AxBwEA;AwBvEA,AxBwEA;AwBvEA,AxBwEA;AwBvEA,AxBwEA;AwBvEA,AxBwEA;AwBvEA,AxBwEA;AwBvEA,AxBwEA;AwBvEA,AxBwEA;AwBvEA,AxBwEA;AwBvEA,AxBwEA;AwBvEA,AxBwEA;AwBvEA,AxBwEA;AwBvEA,AxBwEA;AwBvEA,AxBwEA;AwBvEA,AxBwEA;AwBvEA,AxBwEA;AwBvEA,AxBwEA;AwBvEA,AxBwEA;AwBvEA,AxBwEA;AwBvEA,AxBwEA;AwBvEA,AxBwEA;AwBvEA,AxBwEA;AwBvEA,AxBwEA;AwBvEA,AxBwEA;AwBvEA,AxBwEA;AwBvEA,AxBwEA;AwBvEA,AxBwEA;AwBvEA,AxBwEA;AwBvEA,AxBwEA;AwBvEA,AxBwEA;AwBvEA,AxBwEA;AwBvEA,AxBwEA;AwBvEA,AxBwEA;AwBvEA,AxBwEA;AwBvEA,AxBwEA;AwBvEA,AxBwEA;AwBvEA,AxBwEA;AwBvEA,AxBwEA;AwBvEA,AxBwEA;AwBvEA,AxBwEA;AwBvEA,AxBwEA;AwBvEA,AxBwEA;AwBvEA,AxBwEA;AwBvEA,AxBwEA;AwBvEA,AxBwEA;AwBvEA,AxBwEA;AwBvEA,AxBwEA;AwBvEA,AxBwEA;AwBvEA,AxBwEA;AwBvEA,AxBwEA;AwBvEA,AxBwEA;AwBvEA,AxBwEA;AwBvEA,AxBwEA;AwBvEA,AxBwEA;AwBvEA,AxBwEA;AwBvEA,AxBwEA;AwBvEA,AxBwEA;AwBvEA,AxBwEA;AwBvEA,AxBwEA;AwBvEA,AxBwEA;AwBvEA,AxBwEA;AwBvEA,AxBwEA;AwBvEA,AxBwEA;AwBvEA,AxBwEA;AwBvEA,AxBwEA;AwBvEA,AxBwEA;AwBvEA,AxBwEA;AwBvEA,AxBwEA;AwBvEA,AxBwEA;AwBvEA,AxBwEA;AwBvEA,AxBwEA;AwBvEA,AxBwEA;AwBvEA,AxBwEA;AwBvEA,AxBwEA;AwBvEA,AxBwEA;AwBvEA,AxBwEA;AwBvEA,AxBwEA;AwBvEA,AxBwEA;AwBvEA,AxBwEA;AwBvEA,AxBwEA;AwBvEA,AxBwEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.js", "sourcesContent": ["// Generated by CoffeeScript 1.12.7\n(function() {\n  var NodeType, WriterState, XMLDOMImplementation, XMLDocument, XMLDocumentCB, XMLStreamWriter, XMLStringWriter, assign, isFunction, ref;\n\n  ref = require('./Utility'), assign = ref.assign, isFunction = ref.isFunction;\n\n  XMLDOMImplementation = require('./XMLDOMImplementation');\n\n  XMLDocument = require('./XMLDocument');\n\n  XMLDocumentCB = require('./XMLDocumentCB');\n\n  XMLStringWriter = require('./XMLStringWriter');\n\n  XMLStreamWriter = require('./XMLStreamWriter');\n\n  NodeType = require('./NodeType');\n\n  WriterState = require('./WriterState');\n\n  module.exports.create = function(name, xmldec, doctype, options) {\n    var doc, root;\n    if (name == null) {\n      throw new Error(\"Root element needs a name.\");\n    }\n    options = assign({}, xmldec, doctype, options);\n    doc = new XMLDocument(options);\n    root = doc.element(name);\n    if (!options.headless) {\n      doc.declaration(options);\n      if ((options.pubID != null) || (options.sysID != null)) {\n        doc.dtd(options);\n      }\n    }\n    return root;\n  };\n\n  module.exports.begin = function(options, onData, onEnd) {\n    var ref1;\n    if (isFunction(options)) {\n      ref1 = [options, onData], onData = ref1[0], onEnd = ref1[1];\n      options = {};\n    }\n    if (onData) {\n      return new XMLDocumentCB(options, onData, onEnd);\n    } else {\n      return new XMLDocument(options);\n    }\n  };\n\n  module.exports.stringWriter = function(options) {\n    return new XMLStringWriter(options);\n  };\n\n  module.exports.streamWriter = function(stream, options) {\n    return new XMLStreamWriter(stream, options);\n  };\n\n  module.exports.implementation = new XMLDOMImplementation();\n\n  module.exports.nodeType = NodeType;\n\n  module.exports.writerState = WriterState;\n\n}).call(this);\n", "// Generated by CoffeeScript 1.12.7\n(function() {\n  var assign, getValue, isArray, isEmpty, isFunction, isObject, isPlainObject,\n    slice = [].slice,\n    hasProp = {}.hasOwnProperty;\n\n  assign = function() {\n    var i, key, len, source, sources, target;\n    target = arguments[0], sources = 2 <= arguments.length ? slice.call(arguments, 1) : [];\n    if (isFunction(Object.assign)) {\n      Object.assign.apply(null, arguments);\n    } else {\n      for (i = 0, len = sources.length; i < len; i++) {\n        source = sources[i];\n        if (source != null) {\n          for (key in source) {\n            if (!hasProp.call(source, key)) continue;\n            target[key] = source[key];\n          }\n        }\n      }\n    }\n    return target;\n  };\n\n  isFunction = function(val) {\n    return !!val && Object.prototype.toString.call(val) === '[object Function]';\n  };\n\n  isObject = function(val) {\n    var ref;\n    return !!val && ((ref = typeof val) === 'function' || ref === 'object');\n  };\n\n  isArray = function(val) {\n    if (isFunction(Array.isArray)) {\n      return Array.isArray(val);\n    } else {\n      return Object.prototype.toString.call(val) === '[object Array]';\n    }\n  };\n\n  isEmpty = function(val) {\n    var key;\n    if (isArray(val)) {\n      return !val.length;\n    } else {\n      for (key in val) {\n        if (!hasProp.call(val, key)) continue;\n        return false;\n      }\n      return true;\n    }\n  };\n\n  isPlainObject = function(val) {\n    var ctor, proto;\n    return isObject(val) && (proto = Object.getPrototypeOf(val)) && (ctor = proto.constructor) && (typeof ctor === 'function') && (ctor instanceof ctor) && (Function.prototype.toString.call(ctor) === Function.prototype.toString.call(Object));\n  };\n\n  getValue = function(obj) {\n    if (isFunction(obj.valueOf)) {\n      return obj.valueOf();\n    } else {\n      return obj;\n    }\n  };\n\n  module.exports.assign = assign;\n\n  module.exports.isFunction = isFunction;\n\n  module.exports.isObject = isObject;\n\n  module.exports.isArray = isArray;\n\n  module.exports.isEmpty = isEmpty;\n\n  module.exports.isPlainObject = isPlainObject;\n\n  module.exports.getValue = getValue;\n\n}).call(this);\n", "// Generated by CoffeeScript 1.12.7\n(function() {\n  var XMLDOMImplementation;\n\n  module.exports = XMLDOMImplementation = (function() {\n    function XMLDOMImplementation() {}\n\n    XMLDOMImplementation.prototype.hasFeature = function(feature, version) {\n      return true;\n    };\n\n    XMLDOMImplementation.prototype.createDocumentType = function(qualifiedName, publicId, systemId) {\n      throw new Error(\"This DOM method is not implemented.\");\n    };\n\n    XMLDOMImplementation.prototype.createDocument = function(namespaceURI, qualifiedName, doctype) {\n      throw new Error(\"This DOM method is not implemented.\");\n    };\n\n    XMLDOMImplementation.prototype.createHTMLDocument = function(title) {\n      throw new Error(\"This DOM method is not implemented.\");\n    };\n\n    XMLDOMImplementation.prototype.getFeature = function(feature, version) {\n      throw new Error(\"This DOM method is not implemented.\");\n    };\n\n    return XMLDOMImplementation;\n\n  })();\n\n}).call(this);\n", "// Generated by CoffeeScript 1.12.7\n(function() {\n  var NodeType, XMLDOMConfiguration, XMLDOMImplementation, XMLDocument, XMLNode, XMLStringWriter, XMLStringifier, isPlainObject,\n    extend = function(child, parent) { for (var key in parent) { if (hasProp.call(parent, key)) child[key] = parent[key]; } function ctor() { this.constructor = child; } ctor.prototype = parent.prototype; child.prototype = new ctor(); child.__super__ = parent.prototype; return child; },\n    hasProp = {}.hasOwnProperty;\n\n  isPlainObject = require('./Utility').isPlainObject;\n\n  XMLDOMImplementation = require('./XMLDOMImplementation');\n\n  XMLDOMConfiguration = require('./XMLDOMConfiguration');\n\n  XMLNode = require('./XMLNode');\n\n  NodeType = require('./NodeType');\n\n  XMLStringifier = require('./XMLStringifier');\n\n  XMLStringWriter = require('./XMLStringWriter');\n\n  module.exports = XMLDocument = (function(superClass) {\n    extend(XMLDocument, superClass);\n\n    function XMLDocument(options) {\n      XMLDocument.__super__.constructor.call(this, null);\n      this.name = \"#document\";\n      this.type = NodeType.Document;\n      this.documentURI = null;\n      this.domConfig = new XMLDOMConfiguration();\n      options || (options = {});\n      if (!options.writer) {\n        options.writer = new XMLStringWriter();\n      }\n      this.options = options;\n      this.stringify = new XMLStringifier(options);\n    }\n\n    Object.defineProperty(XMLDocument.prototype, 'implementation', {\n      value: new XMLDOMImplementation()\n    });\n\n    Object.defineProperty(XMLDocument.prototype, 'doctype', {\n      get: function() {\n        var child, i, len, ref;\n        ref = this.children;\n        for (i = 0, len = ref.length; i < len; i++) {\n          child = ref[i];\n          if (child.type === NodeType.DocType) {\n            return child;\n          }\n        }\n        return null;\n      }\n    });\n\n    Object.defineProperty(XMLDocument.prototype, 'documentElement', {\n      get: function() {\n        return this.rootObject || null;\n      }\n    });\n\n    Object.defineProperty(XMLDocument.prototype, 'inputEncoding', {\n      get: function() {\n        return null;\n      }\n    });\n\n    Object.defineProperty(XMLDocument.prototype, 'strictErrorChecking', {\n      get: function() {\n        return false;\n      }\n    });\n\n    Object.defineProperty(XMLDocument.prototype, 'xmlEncoding', {\n      get: function() {\n        if (this.children.length !== 0 && this.children[0].type === NodeType.Declaration) {\n          return this.children[0].encoding;\n        } else {\n          return null;\n        }\n      }\n    });\n\n    Object.defineProperty(XMLDocument.prototype, 'xmlStandalone', {\n      get: function() {\n        if (this.children.length !== 0 && this.children[0].type === NodeType.Declaration) {\n          return this.children[0].standalone === 'yes';\n        } else {\n          return false;\n        }\n      }\n    });\n\n    Object.defineProperty(XMLDocument.prototype, 'xmlVersion', {\n      get: function() {\n        if (this.children.length !== 0 && this.children[0].type === NodeType.Declaration) {\n          return this.children[0].version;\n        } else {\n          return \"1.0\";\n        }\n      }\n    });\n\n    Object.defineProperty(XMLDocument.prototype, 'URL', {\n      get: function() {\n        return this.documentURI;\n      }\n    });\n\n    Object.defineProperty(XMLDocument.prototype, 'origin', {\n      get: function() {\n        return null;\n      }\n    });\n\n    Object.defineProperty(XMLDocument.prototype, 'compatMode', {\n      get: function() {\n        return null;\n      }\n    });\n\n    Object.defineProperty(XMLDocument.prototype, 'characterSet', {\n      get: function() {\n        return null;\n      }\n    });\n\n    Object.defineProperty(XMLDocument.prototype, 'contentType', {\n      get: function() {\n        return null;\n      }\n    });\n\n    XMLDocument.prototype.end = function(writer) {\n      var writerOptions;\n      writerOptions = {};\n      if (!writer) {\n        writer = this.options.writer;\n      } else if (isPlainObject(writer)) {\n        writerOptions = writer;\n        writer = this.options.writer;\n      }\n      return writer.document(this, writer.filterOptions(writerOptions));\n    };\n\n    XMLDocument.prototype.toString = function(options) {\n      return this.options.writer.document(this, this.options.writer.filterOptions(options));\n    };\n\n    XMLDocument.prototype.createElement = function(tagName) {\n      throw new Error(\"This DOM method is not implemented.\" + this.debugInfo());\n    };\n\n    XMLDocument.prototype.createDocumentFragment = function() {\n      throw new Error(\"This DOM method is not implemented.\" + this.debugInfo());\n    };\n\n    XMLDocument.prototype.createTextNode = function(data) {\n      throw new Error(\"This DOM method is not implemented.\" + this.debugInfo());\n    };\n\n    XMLDocument.prototype.createComment = function(data) {\n      throw new Error(\"This DOM method is not implemented.\" + this.debugInfo());\n    };\n\n    XMLDocument.prototype.createCDATASection = function(data) {\n      throw new Error(\"This DOM method is not implemented.\" + this.debugInfo());\n    };\n\n    XMLDocument.prototype.createProcessingInstruction = function(target, data) {\n      throw new Error(\"This DOM method is not implemented.\" + this.debugInfo());\n    };\n\n    XMLDocument.prototype.createAttribute = function(name) {\n      throw new Error(\"This DOM method is not implemented.\" + this.debugInfo());\n    };\n\n    XMLDocument.prototype.createEntityReference = function(name) {\n      throw new Error(\"This DOM method is not implemented.\" + this.debugInfo());\n    };\n\n    XMLDocument.prototype.getElementsByTagName = function(tagname) {\n      throw new Error(\"This DOM method is not implemented.\" + this.debugInfo());\n    };\n\n    XMLDocument.prototype.importNode = function(importedNode, deep) {\n      throw new Error(\"This DOM method is not implemented.\" + this.debugInfo());\n    };\n\n    XMLDocument.prototype.createElementNS = function(namespaceURI, qualifiedName) {\n      throw new Error(\"This DOM method is not implemented.\" + this.debugInfo());\n    };\n\n    XMLDocument.prototype.createAttributeNS = function(namespaceURI, qualifiedName) {\n      throw new Error(\"This DOM method is not implemented.\" + this.debugInfo());\n    };\n\n    XMLDocument.prototype.getElementsByTagNameNS = function(namespaceURI, localName) {\n      throw new Error(\"This DOM method is not implemented.\" + this.debugInfo());\n    };\n\n    XMLDocument.prototype.getElementById = function(elementId) {\n      throw new Error(\"This DOM method is not implemented.\" + this.debugInfo());\n    };\n\n    XMLDocument.prototype.adoptNode = function(source) {\n      throw new Error(\"This DOM method is not implemented.\" + this.debugInfo());\n    };\n\n    XMLDocument.prototype.normalizeDocument = function() {\n      throw new Error(\"This DOM method is not implemented.\" + this.debugInfo());\n    };\n\n    XMLDocument.prototype.renameNode = function(node, namespaceURI, qualifiedName) {\n      throw new Error(\"This DOM method is not implemented.\" + this.debugInfo());\n    };\n\n    XMLDocument.prototype.getElementsByClassName = function(classNames) {\n      throw new Error(\"This DOM method is not implemented.\" + this.debugInfo());\n    };\n\n    XMLDocument.prototype.createEvent = function(eventInterface) {\n      throw new Error(\"This DOM method is not implemented.\" + this.debugInfo());\n    };\n\n    XMLDocument.prototype.createRange = function() {\n      throw new Error(\"This DOM method is not implemented.\" + this.debugInfo());\n    };\n\n    XMLDocument.prototype.createNodeIterator = function(root, whatToShow, filter) {\n      throw new Error(\"This DOM method is not implemented.\" + this.debugInfo());\n    };\n\n    XMLDocument.prototype.createTreeWalker = function(root, whatToShow, filter) {\n      throw new Error(\"This DOM method is not implemented.\" + this.debugInfo());\n    };\n\n    return XMLDocument;\n\n  })(XMLNode);\n\n}).call(this);\n", "// Generated by CoffeeScript 1.12.7\n(function() {\n  var XMLDOMConfiguration, XMLDOM<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, XMLDOMStringList;\n\n  XMLDOMErrorHandler = require('./XMLDOMErrorHandler');\n\n  XMLDOMStringList = require('./XMLDOMStringList');\n\n  module.exports = XMLDOMConfiguration = (function() {\n    function XMLDOMConfiguration() {\n      var clonedSelf;\n      this.defaultParams = {\n        \"canonical-form\": false,\n        \"cdata-sections\": false,\n        \"comments\": false,\n        \"datatype-normalization\": false,\n        \"element-content-whitespace\": true,\n        \"entities\": true,\n        \"error-handler\": new XMLDOMErrorHandler(),\n        \"infoset\": true,\n        \"validate-if-schema\": false,\n        \"namespaces\": true,\n        \"namespace-declarations\": true,\n        \"normalize-characters\": false,\n        \"schema-location\": '',\n        \"schema-type\": '',\n        \"split-cdata-sections\": true,\n        \"validate\": false,\n        \"well-formed\": true\n      };\n      this.params = clonedSelf = Object.create(this.defaultParams);\n    }\n\n    Object.defineProperty(XMLDOMConfiguration.prototype, 'parameterNames', {\n      get: function() {\n        return new XMLDOMStringList(Object.keys(this.defaultParams));\n      }\n    });\n\n    XMLDOMConfiguration.prototype.getParameter = function(name) {\n      if (this.params.hasOwnProperty(name)) {\n        return this.params[name];\n      } else {\n        return null;\n      }\n    };\n\n    XMLDOMConfiguration.prototype.canSetParameter = function(name, value) {\n      return true;\n    };\n\n    XMLDOMConfiguration.prototype.setParameter = function(name, value) {\n      if (value != null) {\n        return this.params[name] = value;\n      } else {\n        return delete this.params[name];\n      }\n    };\n\n    return XMLDOMConfiguration;\n\n  })();\n\n}).call(this);\n", "// Generated by CoffeeScript 1.12.7\n(function() {\n  var XMLDOMErrorHandler;\n\n  module.exports = XMLDOMErrorHandler = (function() {\n    function XMLDOMErrorHandler() {}\n\n    XMLDOMErrorHandler.prototype.handleError = function(error) {\n      throw new Error(error);\n    };\n\n    return XMLDOMErrorHandler;\n\n  })();\n\n}).call(this);\n", "// Generated by CoffeeScript 1.12.7\n(function() {\n  var XMLDOMStringList;\n\n  module.exports = XMLDOMStringList = (function() {\n    function XMLDOMStringList(arr) {\n      this.arr = arr || [];\n    }\n\n    Object.defineProperty(XMLDOMStringList.prototype, 'length', {\n      get: function() {\n        return this.arr.length;\n      }\n    });\n\n    XMLDOMStringList.prototype.item = function(index) {\n      return this.arr[index] || null;\n    };\n\n    XMLDOMStringList.prototype.contains = function(str) {\n      return this.arr.indexOf(str) !== -1;\n    };\n\n    return XMLDOMStringList;\n\n  })();\n\n}).call(this);\n", "// Generated by CoffeeScript 1.12.7\n(function() {\n  var DocumentPosition, NodeType, XMLCData, XMLComment, XMLDeclaration, XMLDocType, XMLDummy, XMLElement, XMLNamedNodeMap, XMLNode, XMLNodeList, XMLProcessingInstruction, XMLRaw, XMLText, getValue, isEmpty, isFunction, isObject, ref1,\n    hasProp = {}.hasOwnProperty;\n\n  ref1 = require('./Utility'), isObject = ref1.isObject, isFunction = ref1.isFunction, isEmpty = ref1.isEmpty, getValue = ref1.getValue;\n\n  XMLElement = null;\n\n  XMLCData = null;\n\n  XMLComment = null;\n\n  XMLDeclaration = null;\n\n  XMLDocType = null;\n\n  XMLRaw = null;\n\n  XMLText = null;\n\n  XMLProcessingInstruction = null;\n\n  XMLDummy = null;\n\n  NodeType = null;\n\n  XMLNodeList = null;\n\n  XMLNamedNodeMap = null;\n\n  DocumentPosition = null;\n\n  module.exports = XMLNode = (function() {\n    function XMLNode(parent1) {\n      this.parent = parent1;\n      if (this.parent) {\n        this.options = this.parent.options;\n        this.stringify = this.parent.stringify;\n      }\n      this.value = null;\n      this.children = [];\n      this.baseURI = null;\n      if (!XMLElement) {\n        XMLElement = require('./XMLElement');\n        XMLCData = require('./XMLCData');\n        XMLComment = require('./XMLComment');\n        XMLDeclaration = require('./XMLDeclaration');\n        XMLDocType = require('./XMLDocType');\n        XMLRaw = require('./XMLRaw');\n        XMLText = require('./XMLText');\n        XMLProcessingInstruction = require('./XMLProcessingInstruction');\n        XMLDummy = require('./XMLDummy');\n        NodeType = require('./NodeType');\n        XMLNodeList = require('./XMLNodeList');\n        XMLNamedNodeMap = require('./XMLNamedNodeMap');\n        DocumentPosition = require('./DocumentPosition');\n      }\n    }\n\n    Object.defineProperty(XMLNode.prototype, 'nodeName', {\n      get: function() {\n        return this.name;\n      }\n    });\n\n    Object.defineProperty(XMLNode.prototype, 'nodeType', {\n      get: function() {\n        return this.type;\n      }\n    });\n\n    Object.defineProperty(XMLNode.prototype, 'nodeValue', {\n      get: function() {\n        return this.value;\n      }\n    });\n\n    Object.defineProperty(XMLNode.prototype, 'parentNode', {\n      get: function() {\n        return this.parent;\n      }\n    });\n\n    Object.defineProperty(XMLNode.prototype, 'childNodes', {\n      get: function() {\n        if (!this.childNodeList || !this.childNodeList.nodes) {\n          this.childNodeList = new XMLNodeList(this.children);\n        }\n        return this.childNodeList;\n      }\n    });\n\n    Object.defineProperty(XMLNode.prototype, 'firstChild', {\n      get: function() {\n        return this.children[0] || null;\n      }\n    });\n\n    Object.defineProperty(XMLNode.prototype, 'lastChild', {\n      get: function() {\n        return this.children[this.children.length - 1] || null;\n      }\n    });\n\n    Object.defineProperty(XMLNode.prototype, 'previousSibling', {\n      get: function() {\n        var i;\n        i = this.parent.children.indexOf(this);\n        return this.parent.children[i - 1] || null;\n      }\n    });\n\n    Object.defineProperty(XMLNode.prototype, 'nextSibling', {\n      get: function() {\n        var i;\n        i = this.parent.children.indexOf(this);\n        return this.parent.children[i + 1] || null;\n      }\n    });\n\n    Object.defineProperty(XMLNode.prototype, 'ownerDocument', {\n      get: function() {\n        return this.document() || null;\n      }\n    });\n\n    Object.defineProperty(XMLNode.prototype, 'textContent', {\n      get: function() {\n        var child, j, len, ref2, str;\n        if (this.nodeType === NodeType.Element || this.nodeType === NodeType.DocumentFragment) {\n          str = '';\n          ref2 = this.children;\n          for (j = 0, len = ref2.length; j < len; j++) {\n            child = ref2[j];\n            if (child.textContent) {\n              str += child.textContent;\n            }\n          }\n          return str;\n        } else {\n          return null;\n        }\n      },\n      set: function(value) {\n        throw new Error(\"This DOM method is not implemented.\" + this.debugInfo());\n      }\n    });\n\n    XMLNode.prototype.setParent = function(parent) {\n      var child, j, len, ref2, results;\n      this.parent = parent;\n      if (parent) {\n        this.options = parent.options;\n        this.stringify = parent.stringify;\n      }\n      ref2 = this.children;\n      results = [];\n      for (j = 0, len = ref2.length; j < len; j++) {\n        child = ref2[j];\n        results.push(child.setParent(this));\n      }\n      return results;\n    };\n\n    XMLNode.prototype.element = function(name, attributes, text) {\n      var childNode, item, j, k, key, lastChild, len, len1, ref2, ref3, val;\n      lastChild = null;\n      if (attributes === null && (text == null)) {\n        ref2 = [{}, null], attributes = ref2[0], text = ref2[1];\n      }\n      if (attributes == null) {\n        attributes = {};\n      }\n      attributes = getValue(attributes);\n      if (!isObject(attributes)) {\n        ref3 = [attributes, text], text = ref3[0], attributes = ref3[1];\n      }\n      if (name != null) {\n        name = getValue(name);\n      }\n      if (Array.isArray(name)) {\n        for (j = 0, len = name.length; j < len; j++) {\n          item = name[j];\n          lastChild = this.element(item);\n        }\n      } else if (isFunction(name)) {\n        lastChild = this.element(name.apply());\n      } else if (isObject(name)) {\n        for (key in name) {\n          if (!hasProp.call(name, key)) continue;\n          val = name[key];\n          if (isFunction(val)) {\n            val = val.apply();\n          }\n          if (!this.options.ignoreDecorators && this.stringify.convertAttKey && key.indexOf(this.stringify.convertAttKey) === 0) {\n            lastChild = this.attribute(key.substr(this.stringify.convertAttKey.length), val);\n          } else if (!this.options.separateArrayItems && Array.isArray(val) && isEmpty(val)) {\n            lastChild = this.dummy();\n          } else if (isObject(val) && isEmpty(val)) {\n            lastChild = this.element(key);\n          } else if (!this.options.keepNullNodes && (val == null)) {\n            lastChild = this.dummy();\n          } else if (!this.options.separateArrayItems && Array.isArray(val)) {\n            for (k = 0, len1 = val.length; k < len1; k++) {\n              item = val[k];\n              childNode = {};\n              childNode[key] = item;\n              lastChild = this.element(childNode);\n            }\n          } else if (isObject(val)) {\n            if (!this.options.ignoreDecorators && this.stringify.convertTextKey && key.indexOf(this.stringify.convertTextKey) === 0) {\n              lastChild = this.element(val);\n            } else {\n              lastChild = this.element(key);\n              lastChild.element(val);\n            }\n          } else {\n            lastChild = this.element(key, val);\n          }\n        }\n      } else if (!this.options.keepNullNodes && text === null) {\n        lastChild = this.dummy();\n      } else {\n        if (!this.options.ignoreDecorators && this.stringify.convertTextKey && name.indexOf(this.stringify.convertTextKey) === 0) {\n          lastChild = this.text(text);\n        } else if (!this.options.ignoreDecorators && this.stringify.convertCDataKey && name.indexOf(this.stringify.convertCDataKey) === 0) {\n          lastChild = this.cdata(text);\n        } else if (!this.options.ignoreDecorators && this.stringify.convertCommentKey && name.indexOf(this.stringify.convertCommentKey) === 0) {\n          lastChild = this.comment(text);\n        } else if (!this.options.ignoreDecorators && this.stringify.convertRawKey && name.indexOf(this.stringify.convertRawKey) === 0) {\n          lastChild = this.raw(text);\n        } else if (!this.options.ignoreDecorators && this.stringify.convertPIKey && name.indexOf(this.stringify.convertPIKey) === 0) {\n          lastChild = this.instruction(name.substr(this.stringify.convertPIKey.length), text);\n        } else {\n          lastChild = this.node(name, attributes, text);\n        }\n      }\n      if (lastChild == null) {\n        throw new Error(\"Could not create any elements with: \" + name + \". \" + this.debugInfo());\n      }\n      return lastChild;\n    };\n\n    XMLNode.prototype.insertBefore = function(name, attributes, text) {\n      var child, i, newChild, refChild, removed;\n      if (name != null ? name.type : void 0) {\n        newChild = name;\n        refChild = attributes;\n        newChild.setParent(this);\n        if (refChild) {\n          i = children.indexOf(refChild);\n          removed = children.splice(i);\n          children.push(newChild);\n          Array.prototype.push.apply(children, removed);\n        } else {\n          children.push(newChild);\n        }\n        return newChild;\n      } else {\n        if (this.isRoot) {\n          throw new Error(\"Cannot insert elements at root level. \" + this.debugInfo(name));\n        }\n        i = this.parent.children.indexOf(this);\n        removed = this.parent.children.splice(i);\n        child = this.parent.element(name, attributes, text);\n        Array.prototype.push.apply(this.parent.children, removed);\n        return child;\n      }\n    };\n\n    XMLNode.prototype.insertAfter = function(name, attributes, text) {\n      var child, i, removed;\n      if (this.isRoot) {\n        throw new Error(\"Cannot insert elements at root level. \" + this.debugInfo(name));\n      }\n      i = this.parent.children.indexOf(this);\n      removed = this.parent.children.splice(i + 1);\n      child = this.parent.element(name, attributes, text);\n      Array.prototype.push.apply(this.parent.children, removed);\n      return child;\n    };\n\n    XMLNode.prototype.remove = function() {\n      var i, ref2;\n      if (this.isRoot) {\n        throw new Error(\"Cannot remove the root element. \" + this.debugInfo());\n      }\n      i = this.parent.children.indexOf(this);\n      [].splice.apply(this.parent.children, [i, i - i + 1].concat(ref2 = [])), ref2;\n      return this.parent;\n    };\n\n    XMLNode.prototype.node = function(name, attributes, text) {\n      var child, ref2;\n      if (name != null) {\n        name = getValue(name);\n      }\n      attributes || (attributes = {});\n      attributes = getValue(attributes);\n      if (!isObject(attributes)) {\n        ref2 = [attributes, text], text = ref2[0], attributes = ref2[1];\n      }\n      child = new XMLElement(this, name, attributes);\n      if (text != null) {\n        child.text(text);\n      }\n      this.children.push(child);\n      return child;\n    };\n\n    XMLNode.prototype.text = function(value) {\n      var child;\n      if (isObject(value)) {\n        this.element(value);\n      }\n      child = new XMLText(this, value);\n      this.children.push(child);\n      return this;\n    };\n\n    XMLNode.prototype.cdata = function(value) {\n      var child;\n      child = new XMLCData(this, value);\n      this.children.push(child);\n      return this;\n    };\n\n    XMLNode.prototype.comment = function(value) {\n      var child;\n      child = new XMLComment(this, value);\n      this.children.push(child);\n      return this;\n    };\n\n    XMLNode.prototype.commentBefore = function(value) {\n      var child, i, removed;\n      i = this.parent.children.indexOf(this);\n      removed = this.parent.children.splice(i);\n      child = this.parent.comment(value);\n      Array.prototype.push.apply(this.parent.children, removed);\n      return this;\n    };\n\n    XMLNode.prototype.commentAfter = function(value) {\n      var child, i, removed;\n      i = this.parent.children.indexOf(this);\n      removed = this.parent.children.splice(i + 1);\n      child = this.parent.comment(value);\n      Array.prototype.push.apply(this.parent.children, removed);\n      return this;\n    };\n\n    XMLNode.prototype.raw = function(value) {\n      var child;\n      child = new XMLRaw(this, value);\n      this.children.push(child);\n      return this;\n    };\n\n    XMLNode.prototype.dummy = function() {\n      var child;\n      child = new XMLDummy(this);\n      return child;\n    };\n\n    XMLNode.prototype.instruction = function(target, value) {\n      var insTarget, insValue, instruction, j, len;\n      if (target != null) {\n        target = getValue(target);\n      }\n      if (value != null) {\n        value = getValue(value);\n      }\n      if (Array.isArray(target)) {\n        for (j = 0, len = target.length; j < len; j++) {\n          insTarget = target[j];\n          this.instruction(insTarget);\n        }\n      } else if (isObject(target)) {\n        for (insTarget in target) {\n          if (!hasProp.call(target, insTarget)) continue;\n          insValue = target[insTarget];\n          this.instruction(insTarget, insValue);\n        }\n      } else {\n        if (isFunction(value)) {\n          value = value.apply();\n        }\n        instruction = new XMLProcessingInstruction(this, target, value);\n        this.children.push(instruction);\n      }\n      return this;\n    };\n\n    XMLNode.prototype.instructionBefore = function(target, value) {\n      var child, i, removed;\n      i = this.parent.children.indexOf(this);\n      removed = this.parent.children.splice(i);\n      child = this.parent.instruction(target, value);\n      Array.prototype.push.apply(this.parent.children, removed);\n      return this;\n    };\n\n    XMLNode.prototype.instructionAfter = function(target, value) {\n      var child, i, removed;\n      i = this.parent.children.indexOf(this);\n      removed = this.parent.children.splice(i + 1);\n      child = this.parent.instruction(target, value);\n      Array.prototype.push.apply(this.parent.children, removed);\n      return this;\n    };\n\n    XMLNode.prototype.declaration = function(version, encoding, standalone) {\n      var doc, xmldec;\n      doc = this.document();\n      xmldec = new XMLDeclaration(doc, version, encoding, standalone);\n      if (doc.children.length === 0) {\n        doc.children.unshift(xmldec);\n      } else if (doc.children[0].type === NodeType.Declaration) {\n        doc.children[0] = xmldec;\n      } else {\n        doc.children.unshift(xmldec);\n      }\n      return doc.root() || doc;\n    };\n\n    XMLNode.prototype.dtd = function(pubID, sysID) {\n      var child, doc, doctype, i, j, k, len, len1, ref2, ref3;\n      doc = this.document();\n      doctype = new XMLDocType(doc, pubID, sysID);\n      ref2 = doc.children;\n      for (i = j = 0, len = ref2.length; j < len; i = ++j) {\n        child = ref2[i];\n        if (child.type === NodeType.DocType) {\n          doc.children[i] = doctype;\n          return doctype;\n        }\n      }\n      ref3 = doc.children;\n      for (i = k = 0, len1 = ref3.length; k < len1; i = ++k) {\n        child = ref3[i];\n        if (child.isRoot) {\n          doc.children.splice(i, 0, doctype);\n          return doctype;\n        }\n      }\n      doc.children.push(doctype);\n      return doctype;\n    };\n\n    XMLNode.prototype.up = function() {\n      if (this.isRoot) {\n        throw new Error(\"The root node has no parent. Use doc() if you need to get the document object.\");\n      }\n      return this.parent;\n    };\n\n    XMLNode.prototype.root = function() {\n      var node;\n      node = this;\n      while (node) {\n        if (node.type === NodeType.Document) {\n          return node.rootObject;\n        } else if (node.isRoot) {\n          return node;\n        } else {\n          node = node.parent;\n        }\n      }\n    };\n\n    XMLNode.prototype.document = function() {\n      var node;\n      node = this;\n      while (node) {\n        if (node.type === NodeType.Document) {\n          return node;\n        } else {\n          node = node.parent;\n        }\n      }\n    };\n\n    XMLNode.prototype.end = function(options) {\n      return this.document().end(options);\n    };\n\n    XMLNode.prototype.prev = function() {\n      var i;\n      i = this.parent.children.indexOf(this);\n      if (i < 1) {\n        throw new Error(\"Already at the first node. \" + this.debugInfo());\n      }\n      return this.parent.children[i - 1];\n    };\n\n    XMLNode.prototype.next = function() {\n      var i;\n      i = this.parent.children.indexOf(this);\n      if (i === -1 || i === this.parent.children.length - 1) {\n        throw new Error(\"Already at the last node. \" + this.debugInfo());\n      }\n      return this.parent.children[i + 1];\n    };\n\n    XMLNode.prototype.importDocument = function(doc) {\n      var clonedRoot;\n      clonedRoot = doc.root().clone();\n      clonedRoot.parent = this;\n      clonedRoot.isRoot = false;\n      this.children.push(clonedRoot);\n      return this;\n    };\n\n    XMLNode.prototype.debugInfo = function(name) {\n      var ref2, ref3;\n      name = name || this.name;\n      if ((name == null) && !((ref2 = this.parent) != null ? ref2.name : void 0)) {\n        return \"\";\n      } else if (name == null) {\n        return \"parent: <\" + this.parent.name + \">\";\n      } else if (!((ref3 = this.parent) != null ? ref3.name : void 0)) {\n        return \"node: <\" + name + \">\";\n      } else {\n        return \"node: <\" + name + \">, parent: <\" + this.parent.name + \">\";\n      }\n    };\n\n    XMLNode.prototype.ele = function(name, attributes, text) {\n      return this.element(name, attributes, text);\n    };\n\n    XMLNode.prototype.nod = function(name, attributes, text) {\n      return this.node(name, attributes, text);\n    };\n\n    XMLNode.prototype.txt = function(value) {\n      return this.text(value);\n    };\n\n    XMLNode.prototype.dat = function(value) {\n      return this.cdata(value);\n    };\n\n    XMLNode.prototype.com = function(value) {\n      return this.comment(value);\n    };\n\n    XMLNode.prototype.ins = function(target, value) {\n      return this.instruction(target, value);\n    };\n\n    XMLNode.prototype.doc = function() {\n      return this.document();\n    };\n\n    XMLNode.prototype.dec = function(version, encoding, standalone) {\n      return this.declaration(version, encoding, standalone);\n    };\n\n    XMLNode.prototype.e = function(name, attributes, text) {\n      return this.element(name, attributes, text);\n    };\n\n    XMLNode.prototype.n = function(name, attributes, text) {\n      return this.node(name, attributes, text);\n    };\n\n    XMLNode.prototype.t = function(value) {\n      return this.text(value);\n    };\n\n    XMLNode.prototype.d = function(value) {\n      return this.cdata(value);\n    };\n\n    XMLNode.prototype.c = function(value) {\n      return this.comment(value);\n    };\n\n    XMLNode.prototype.r = function(value) {\n      return this.raw(value);\n    };\n\n    XMLNode.prototype.i = function(target, value) {\n      return this.instruction(target, value);\n    };\n\n    XMLNode.prototype.u = function() {\n      return this.up();\n    };\n\n    XMLNode.prototype.importXMLBuilder = function(doc) {\n      return this.importDocument(doc);\n    };\n\n    XMLNode.prototype.replaceChild = function(newChild, oldChild) {\n      throw new Error(\"This DOM method is not implemented.\" + this.debugInfo());\n    };\n\n    XMLNode.prototype.removeChild = function(oldChild) {\n      throw new Error(\"This DOM method is not implemented.\" + this.debugInfo());\n    };\n\n    XMLNode.prototype.appendChild = function(newChild) {\n      throw new Error(\"This DOM method is not implemented.\" + this.debugInfo());\n    };\n\n    XMLNode.prototype.hasChildNodes = function() {\n      return this.children.length !== 0;\n    };\n\n    XMLNode.prototype.cloneNode = function(deep) {\n      throw new Error(\"This DOM method is not implemented.\" + this.debugInfo());\n    };\n\n    XMLNode.prototype.normalize = function() {\n      throw new Error(\"This DOM method is not implemented.\" + this.debugInfo());\n    };\n\n    XMLNode.prototype.isSupported = function(feature, version) {\n      return true;\n    };\n\n    XMLNode.prototype.hasAttributes = function() {\n      return this.attribs.length !== 0;\n    };\n\n    XMLNode.prototype.compareDocumentPosition = function(other) {\n      var ref, res;\n      ref = this;\n      if (ref === other) {\n        return 0;\n      } else if (this.document() !== other.document()) {\n        res = DocumentPosition.Disconnected | DocumentPosition.ImplementationSpecific;\n        if (Math.random() < 0.5) {\n          res |= DocumentPosition.Preceding;\n        } else {\n          res |= DocumentPosition.Following;\n        }\n        return res;\n      } else if (ref.isAncestor(other)) {\n        return DocumentPosition.Contains | DocumentPosition.Preceding;\n      } else if (ref.isDescendant(other)) {\n        return DocumentPosition.Contains | DocumentPosition.Following;\n      } else if (ref.isPreceding(other)) {\n        return DocumentPosition.Preceding;\n      } else {\n        return DocumentPosition.Following;\n      }\n    };\n\n    XMLNode.prototype.isSameNode = function(other) {\n      throw new Error(\"This DOM method is not implemented.\" + this.debugInfo());\n    };\n\n    XMLNode.prototype.lookupPrefix = function(namespaceURI) {\n      throw new Error(\"This DOM method is not implemented.\" + this.debugInfo());\n    };\n\n    XMLNode.prototype.isDefaultNamespace = function(namespaceURI) {\n      throw new Error(\"This DOM method is not implemented.\" + this.debugInfo());\n    };\n\n    XMLNode.prototype.lookupNamespaceURI = function(prefix) {\n      throw new Error(\"This DOM method is not implemented.\" + this.debugInfo());\n    };\n\n    XMLNode.prototype.isEqualNode = function(node) {\n      var i, j, ref2;\n      if (node.nodeType !== this.nodeType) {\n        return false;\n      }\n      if (node.children.length !== this.children.length) {\n        return false;\n      }\n      for (i = j = 0, ref2 = this.children.length - 1; 0 <= ref2 ? j <= ref2 : j >= ref2; i = 0 <= ref2 ? ++j : --j) {\n        if (!this.children[i].isEqualNode(node.children[i])) {\n          return false;\n        }\n      }\n      return true;\n    };\n\n    XMLNode.prototype.getFeature = function(feature, version) {\n      throw new Error(\"This DOM method is not implemented.\" + this.debugInfo());\n    };\n\n    XMLNode.prototype.setUserData = function(key, data, handler) {\n      throw new Error(\"This DOM method is not implemented.\" + this.debugInfo());\n    };\n\n    XMLNode.prototype.getUserData = function(key) {\n      throw new Error(\"This DOM method is not implemented.\" + this.debugInfo());\n    };\n\n    XMLNode.prototype.contains = function(other) {\n      if (!other) {\n        return false;\n      }\n      return other === this || this.isDescendant(other);\n    };\n\n    XMLNode.prototype.isDescendant = function(node) {\n      var child, isDescendantChild, j, len, ref2;\n      ref2 = this.children;\n      for (j = 0, len = ref2.length; j < len; j++) {\n        child = ref2[j];\n        if (node === child) {\n          return true;\n        }\n        isDescendantChild = child.isDescendant(node);\n        if (isDescendantChild) {\n          return true;\n        }\n      }\n      return false;\n    };\n\n    XMLNode.prototype.isAncestor = function(node) {\n      return node.isDescendant(this);\n    };\n\n    XMLNode.prototype.isPreceding = function(node) {\n      var nodePos, thisPos;\n      nodePos = this.treePosition(node);\n      thisPos = this.treePosition(this);\n      if (nodePos === -1 || thisPos === -1) {\n        return false;\n      } else {\n        return nodePos < thisPos;\n      }\n    };\n\n    XMLNode.prototype.isFollowing = function(node) {\n      var nodePos, thisPos;\n      nodePos = this.treePosition(node);\n      thisPos = this.treePosition(this);\n      if (nodePos === -1 || thisPos === -1) {\n        return false;\n      } else {\n        return nodePos > thisPos;\n      }\n    };\n\n    XMLNode.prototype.treePosition = function(node) {\n      var found, pos;\n      pos = 0;\n      found = false;\n      this.foreachTreeNode(this.document(), function(childNode) {\n        pos++;\n        if (!found && childNode === node) {\n          return found = true;\n        }\n      });\n      if (found) {\n        return pos;\n      } else {\n        return -1;\n      }\n    };\n\n    XMLNode.prototype.foreachTreeNode = function(node, func) {\n      var child, j, len, ref2, res;\n      node || (node = this.document());\n      ref2 = node.children;\n      for (j = 0, len = ref2.length; j < len; j++) {\n        child = ref2[j];\n        if (res = func(child)) {\n          return res;\n        } else {\n          res = this.foreachTreeNode(child, func);\n          if (res) {\n            return res;\n          }\n        }\n      }\n    };\n\n    return XMLNode;\n\n  })();\n\n}).call(this);\n", "// Generated by CoffeeScript 1.12.7\n(function() {\n  var NodeType, XMLAttribute, XMLElement, XMLNamedNodeMap, XMLNode, getValue, isFunction, isObject, ref,\n    extend = function(child, parent) { for (var key in parent) { if (hasProp.call(parent, key)) child[key] = parent[key]; } function ctor() { this.constructor = child; } ctor.prototype = parent.prototype; child.prototype = new ctor(); child.__super__ = parent.prototype; return child; },\n    hasProp = {}.hasOwnProperty;\n\n  ref = require('./Utility'), isObject = ref.isObject, isFunction = ref.isFunction, getValue = ref.getValue;\n\n  XMLNode = require('./XMLNode');\n\n  NodeType = require('./NodeType');\n\n  XMLAttribute = require('./XMLAttribute');\n\n  XMLNamedNodeMap = require('./XMLNamedNodeMap');\n\n  module.exports = XMLElement = (function(superClass) {\n    extend(XMLElement, superClass);\n\n    function XMLElement(parent, name, attributes) {\n      var child, j, len, ref1;\n      XMLElement.__super__.constructor.call(this, parent);\n      if (name == null) {\n        throw new Error(\"Missing element name. \" + this.debugInfo());\n      }\n      this.name = this.stringify.name(name);\n      this.type = NodeType.Element;\n      this.attribs = {};\n      this.schemaTypeInfo = null;\n      if (attributes != null) {\n        this.attribute(attributes);\n      }\n      if (parent.type === NodeType.Document) {\n        this.isRoot = true;\n        this.documentObject = parent;\n        parent.rootObject = this;\n        if (parent.children) {\n          ref1 = parent.children;\n          for (j = 0, len = ref1.length; j < len; j++) {\n            child = ref1[j];\n            if (child.type === NodeType.DocType) {\n              child.name = this.name;\n              break;\n            }\n          }\n        }\n      }\n    }\n\n    Object.defineProperty(XMLElement.prototype, 'tagName', {\n      get: function() {\n        return this.name;\n      }\n    });\n\n    Object.defineProperty(XMLElement.prototype, 'namespaceURI', {\n      get: function() {\n        return '';\n      }\n    });\n\n    Object.defineProperty(XMLElement.prototype, 'prefix', {\n      get: function() {\n        return '';\n      }\n    });\n\n    Object.defineProperty(XMLElement.prototype, 'localName', {\n      get: function() {\n        return this.name;\n      }\n    });\n\n    Object.defineProperty(XMLElement.prototype, 'id', {\n      get: function() {\n        throw new Error(\"This DOM method is not implemented.\" + this.debugInfo());\n      }\n    });\n\n    Object.defineProperty(XMLElement.prototype, 'className', {\n      get: function() {\n        throw new Error(\"This DOM method is not implemented.\" + this.debugInfo());\n      }\n    });\n\n    Object.defineProperty(XMLElement.prototype, 'classList', {\n      get: function() {\n        throw new Error(\"This DOM method is not implemented.\" + this.debugInfo());\n      }\n    });\n\n    Object.defineProperty(XMLElement.prototype, 'attributes', {\n      get: function() {\n        if (!this.attributeMap || !this.attributeMap.nodes) {\n          this.attributeMap = new XMLNamedNodeMap(this.attribs);\n        }\n        return this.attributeMap;\n      }\n    });\n\n    XMLElement.prototype.clone = function() {\n      var att, attName, clonedSelf, ref1;\n      clonedSelf = Object.create(this);\n      if (clonedSelf.isRoot) {\n        clonedSelf.documentObject = null;\n      }\n      clonedSelf.attribs = {};\n      ref1 = this.attribs;\n      for (attName in ref1) {\n        if (!hasProp.call(ref1, attName)) continue;\n        att = ref1[attName];\n        clonedSelf.attribs[attName] = att.clone();\n      }\n      clonedSelf.children = [];\n      this.children.forEach(function(child) {\n        var clonedChild;\n        clonedChild = child.clone();\n        clonedChild.parent = clonedSelf;\n        return clonedSelf.children.push(clonedChild);\n      });\n      return clonedSelf;\n    };\n\n    XMLElement.prototype.attribute = function(name, value) {\n      var attName, attValue;\n      if (name != null) {\n        name = getValue(name);\n      }\n      if (isObject(name)) {\n        for (attName in name) {\n          if (!hasProp.call(name, attName)) continue;\n          attValue = name[attName];\n          this.attribute(attName, attValue);\n        }\n      } else {\n        if (isFunction(value)) {\n          value = value.apply();\n        }\n        if (this.options.keepNullAttributes && (value == null)) {\n          this.attribs[name] = new XMLAttribute(this, name, \"\");\n        } else if (value != null) {\n          this.attribs[name] = new XMLAttribute(this, name, value);\n        }\n      }\n      return this;\n    };\n\n    XMLElement.prototype.removeAttribute = function(name) {\n      var attName, j, len;\n      if (name == null) {\n        throw new Error(\"Missing attribute name. \" + this.debugInfo());\n      }\n      name = getValue(name);\n      if (Array.isArray(name)) {\n        for (j = 0, len = name.length; j < len; j++) {\n          attName = name[j];\n          delete this.attribs[attName];\n        }\n      } else {\n        delete this.attribs[name];\n      }\n      return this;\n    };\n\n    XMLElement.prototype.toString = function(options) {\n      return this.options.writer.element(this, this.options.writer.filterOptions(options));\n    };\n\n    XMLElement.prototype.att = function(name, value) {\n      return this.attribute(name, value);\n    };\n\n    XMLElement.prototype.a = function(name, value) {\n      return this.attribute(name, value);\n    };\n\n    XMLElement.prototype.getAttribute = function(name) {\n      if (this.attribs.hasOwnProperty(name)) {\n        return this.attribs[name].value;\n      } else {\n        return null;\n      }\n    };\n\n    XMLElement.prototype.setAttribute = function(name, value) {\n      throw new Error(\"This DOM method is not implemented.\" + this.debugInfo());\n    };\n\n    XMLElement.prototype.getAttributeNode = function(name) {\n      if (this.attribs.hasOwnProperty(name)) {\n        return this.attribs[name];\n      } else {\n        return null;\n      }\n    };\n\n    XMLElement.prototype.setAttributeNode = function(newAttr) {\n      throw new Error(\"This DOM method is not implemented.\" + this.debugInfo());\n    };\n\n    XMLElement.prototype.removeAttributeNode = function(oldAttr) {\n      throw new Error(\"This DOM method is not implemented.\" + this.debugInfo());\n    };\n\n    XMLElement.prototype.getElementsByTagName = function(name) {\n      throw new Error(\"This DOM method is not implemented.\" + this.debugInfo());\n    };\n\n    XMLElement.prototype.getAttributeNS = function(namespaceURI, localName) {\n      throw new Error(\"This DOM method is not implemented.\" + this.debugInfo());\n    };\n\n    XMLElement.prototype.setAttributeNS = function(namespaceURI, qualifiedName, value) {\n      throw new Error(\"This DOM method is not implemented.\" + this.debugInfo());\n    };\n\n    XMLElement.prototype.removeAttributeNS = function(namespaceURI, localName) {\n      throw new Error(\"This DOM method is not implemented.\" + this.debugInfo());\n    };\n\n    XMLElement.prototype.getAttributeNodeNS = function(namespaceURI, localName) {\n      throw new Error(\"This DOM method is not implemented.\" + this.debugInfo());\n    };\n\n    XMLElement.prototype.setAttributeNodeNS = function(newAttr) {\n      throw new Error(\"This DOM method is not implemented.\" + this.debugInfo());\n    };\n\n    XMLElement.prototype.getElementsByTagNameNS = function(namespaceURI, localName) {\n      throw new Error(\"This DOM method is not implemented.\" + this.debugInfo());\n    };\n\n    XMLElement.prototype.hasAttribute = function(name) {\n      return this.attribs.hasOwnProperty(name);\n    };\n\n    XMLElement.prototype.hasAttributeNS = function(namespaceURI, localName) {\n      throw new Error(\"This DOM method is not implemented.\" + this.debugInfo());\n    };\n\n    XMLElement.prototype.setIdAttribute = function(name, isId) {\n      if (this.attribs.hasOwnProperty(name)) {\n        return this.attribs[name].isId;\n      } else {\n        return isId;\n      }\n    };\n\n    XMLElement.prototype.setIdAttributeNS = function(namespaceURI, localName, isId) {\n      throw new Error(\"This DOM method is not implemented.\" + this.debugInfo());\n    };\n\n    XMLElement.prototype.setIdAttributeNode = function(idAttr, isId) {\n      throw new Error(\"This DOM method is not implemented.\" + this.debugInfo());\n    };\n\n    XMLElement.prototype.getElementsByTagName = function(tagname) {\n      throw new Error(\"This DOM method is not implemented.\" + this.debugInfo());\n    };\n\n    XMLElement.prototype.getElementsByTagNameNS = function(namespaceURI, localName) {\n      throw new Error(\"This DOM method is not implemented.\" + this.debugInfo());\n    };\n\n    XMLElement.prototype.getElementsByClassName = function(classNames) {\n      throw new Error(\"This DOM method is not implemented.\" + this.debugInfo());\n    };\n\n    XMLElement.prototype.isEqualNode = function(node) {\n      var i, j, ref1;\n      if (!XMLElement.__super__.isEqualNode.apply(this, arguments).isEqualNode(node)) {\n        return false;\n      }\n      if (node.namespaceURI !== this.namespaceURI) {\n        return false;\n      }\n      if (node.prefix !== this.prefix) {\n        return false;\n      }\n      if (node.localName !== this.localName) {\n        return false;\n      }\n      if (node.attribs.length !== this.attribs.length) {\n        return false;\n      }\n      for (i = j = 0, ref1 = this.attribs.length - 1; 0 <= ref1 ? j <= ref1 : j >= ref1; i = 0 <= ref1 ? ++j : --j) {\n        if (!this.attribs[i].isEqualNode(node.attribs[i])) {\n          return false;\n        }\n      }\n      return true;\n    };\n\n    return XMLElement;\n\n  })(XMLNode);\n\n}).call(this);\n", "// Generated by CoffeeScript 1.12.7\n(function() {\n  module.exports = {\n    Element: 1,\n    Attribute: 2,\n    Text: 3,\n    CData: 4,\n    EntityReference: 5,\n    EntityDeclaration: 6,\n    ProcessingInstruction: 7,\n    Comment: 8,\n    Document: 9,\n    DocType: 10,\n    DocumentFragment: 11,\n    NotationDeclaration: 12,\n    Declaration: 201,\n    Raw: 202,\n    AttributeDeclaration: 203,\n    ElementDeclaration: 204,\n    Dummy: 205\n  };\n\n}).call(this);\n", "// Generated by CoffeeScript 1.12.7\n(function() {\n  var NodeType, XMLAttribute, XMLNode;\n\n  NodeType = require('./NodeType');\n\n  XMLNode = require('./XMLNode');\n\n  module.exports = XMLAttribute = (function() {\n    function XMLAttribute(parent, name, value) {\n      this.parent = parent;\n      if (this.parent) {\n        this.options = this.parent.options;\n        this.stringify = this.parent.stringify;\n      }\n      if (name == null) {\n        throw new Error(\"Missing attribute name. \" + this.debugInfo(name));\n      }\n      this.name = this.stringify.name(name);\n      this.value = this.stringify.attValue(value);\n      this.type = NodeType.Attribute;\n      this.isId = false;\n      this.schemaTypeInfo = null;\n    }\n\n    Object.defineProperty(XMLAttribute.prototype, 'nodeType', {\n      get: function() {\n        return this.type;\n      }\n    });\n\n    Object.defineProperty(XMLAttribute.prototype, 'ownerElement', {\n      get: function() {\n        return this.parent;\n      }\n    });\n\n    Object.defineProperty(XMLAttribute.prototype, 'textContent', {\n      get: function() {\n        return this.value;\n      },\n      set: function(value) {\n        return this.value = value || '';\n      }\n    });\n\n    Object.defineProperty(XMLAttribute.prototype, 'namespaceURI', {\n      get: function() {\n        return '';\n      }\n    });\n\n    Object.defineProperty(XMLAttribute.prototype, 'prefix', {\n      get: function() {\n        return '';\n      }\n    });\n\n    Object.defineProperty(XMLAttribute.prototype, 'localName', {\n      get: function() {\n        return this.name;\n      }\n    });\n\n    Object.defineProperty(XMLAttribute.prototype, 'specified', {\n      get: function() {\n        return true;\n      }\n    });\n\n    XMLAttribute.prototype.clone = function() {\n      return Object.create(this);\n    };\n\n    XMLAttribute.prototype.toString = function(options) {\n      return this.options.writer.attribute(this, this.options.writer.filterOptions(options));\n    };\n\n    XMLAttribute.prototype.debugInfo = function(name) {\n      name = name || this.name;\n      if (name == null) {\n        return \"parent: <\" + this.parent.name + \">\";\n      } else {\n        return \"attribute: {\" + name + \"}, parent: <\" + this.parent.name + \">\";\n      }\n    };\n\n    XMLAttribute.prototype.isEqualNode = function(node) {\n      if (node.namespaceURI !== this.namespaceURI) {\n        return false;\n      }\n      if (node.prefix !== this.prefix) {\n        return false;\n      }\n      if (node.localName !== this.localName) {\n        return false;\n      }\n      if (node.value !== this.value) {\n        return false;\n      }\n      return true;\n    };\n\n    return XMLAttribute;\n\n  })();\n\n}).call(this);\n", "// Generated by CoffeeScript 1.12.7\n(function() {\n  var XMLNamedNodeMap;\n\n  module.exports = XMLNamedNodeMap = (function() {\n    function XMLNamedNodeMap(nodes) {\n      this.nodes = nodes;\n    }\n\n    Object.defineProperty(XMLNamedNodeMap.prototype, 'length', {\n      get: function() {\n        return Object.keys(this.nodes).length || 0;\n      }\n    });\n\n    XMLNamedNodeMap.prototype.clone = function() {\n      return this.nodes = null;\n    };\n\n    XMLNamedNodeMap.prototype.getNamedItem = function(name) {\n      return this.nodes[name];\n    };\n\n    XMLNamedNodeMap.prototype.setNamedItem = function(node) {\n      var oldNode;\n      oldNode = this.nodes[node.nodeName];\n      this.nodes[node.nodeName] = node;\n      return oldNode || null;\n    };\n\n    XMLNamedNodeMap.prototype.removeNamedItem = function(name) {\n      var oldNode;\n      oldNode = this.nodes[name];\n      delete this.nodes[name];\n      return oldNode || null;\n    };\n\n    XMLNamedNodeMap.prototype.item = function(index) {\n      return this.nodes[Object.keys(this.nodes)[index]] || null;\n    };\n\n    XMLNamedNodeMap.prototype.getNamedItemNS = function(namespaceURI, localName) {\n      throw new Error(\"This DOM method is not implemented.\");\n    };\n\n    XMLNamedNodeMap.prototype.setNamedItemNS = function(node) {\n      throw new Error(\"This DOM method is not implemented.\");\n    };\n\n    XMLNamedNodeMap.prototype.removeNamedItemNS = function(namespaceURI, localName) {\n      throw new Error(\"This DOM method is not implemented.\");\n    };\n\n    return XMLNamedNodeMap;\n\n  })();\n\n}).call(this);\n", "// Generated by CoffeeScript 1.12.7\n(function() {\n  var NodeType, XMLCData, XMLCharacterData,\n    extend = function(child, parent) { for (var key in parent) { if (hasProp.call(parent, key)) child[key] = parent[key]; } function ctor() { this.constructor = child; } ctor.prototype = parent.prototype; child.prototype = new ctor(); child.__super__ = parent.prototype; return child; },\n    hasProp = {}.hasOwnProperty;\n\n  NodeType = require('./NodeType');\n\n  XMLCharacterData = require('./XMLCharacterData');\n\n  module.exports = XMLCData = (function(superClass) {\n    extend(XMLCData, superClass);\n\n    function XMLCData(parent, text) {\n      XMLCData.__super__.constructor.call(this, parent);\n      if (text == null) {\n        throw new Error(\"Missing CDATA text. \" + this.debugInfo());\n      }\n      this.name = \"#cdata-section\";\n      this.type = NodeType.CData;\n      this.value = this.stringify.cdata(text);\n    }\n\n    XMLCData.prototype.clone = function() {\n      return Object.create(this);\n    };\n\n    XMLCData.prototype.toString = function(options) {\n      return this.options.writer.cdata(this, this.options.writer.filterOptions(options));\n    };\n\n    return XMLCData;\n\n  })(XMLCharacterData);\n\n}).call(this);\n", "// Generated by CoffeeScript 1.12.7\n(function() {\n  var XMLCharacterData, XMLNode,\n    extend = function(child, parent) { for (var key in parent) { if (hasProp.call(parent, key)) child[key] = parent[key]; } function ctor() { this.constructor = child; } ctor.prototype = parent.prototype; child.prototype = new ctor(); child.__super__ = parent.prototype; return child; },\n    hasProp = {}.hasOwnProperty;\n\n  XMLNode = require('./XMLNode');\n\n  module.exports = XMLCharacterData = (function(superClass) {\n    extend(XMLCharacterData, superClass);\n\n    function XMLCharacterData(parent) {\n      XMLCharacterData.__super__.constructor.call(this, parent);\n      this.value = '';\n    }\n\n    Object.defineProperty(XMLCharacterData.prototype, 'data', {\n      get: function() {\n        return this.value;\n      },\n      set: function(value) {\n        return this.value = value || '';\n      }\n    });\n\n    Object.defineProperty(XMLCharacterData.prototype, 'length', {\n      get: function() {\n        return this.value.length;\n      }\n    });\n\n    Object.defineProperty(XMLCharacterData.prototype, 'textContent', {\n      get: function() {\n        return this.value;\n      },\n      set: function(value) {\n        return this.value = value || '';\n      }\n    });\n\n    XMLCharacterData.prototype.clone = function() {\n      return Object.create(this);\n    };\n\n    XMLCharacterData.prototype.substringData = function(offset, count) {\n      throw new Error(\"This DOM method is not implemented.\" + this.debugInfo());\n    };\n\n    XMLCharacterData.prototype.appendData = function(arg) {\n      throw new Error(\"This DOM method is not implemented.\" + this.debugInfo());\n    };\n\n    XMLCharacterData.prototype.insertData = function(offset, arg) {\n      throw new Error(\"This DOM method is not implemented.\" + this.debugInfo());\n    };\n\n    XMLCharacterData.prototype.deleteData = function(offset, count) {\n      throw new Error(\"This DOM method is not implemented.\" + this.debugInfo());\n    };\n\n    XMLCharacterData.prototype.replaceData = function(offset, count, arg) {\n      throw new Error(\"This DOM method is not implemented.\" + this.debugInfo());\n    };\n\n    XMLCharacterData.prototype.isEqualNode = function(node) {\n      if (!XMLCharacterData.__super__.isEqualNode.apply(this, arguments).isEqualNode(node)) {\n        return false;\n      }\n      if (node.data !== this.data) {\n        return false;\n      }\n      return true;\n    };\n\n    return XMLCharacterData;\n\n  })(XMLNode);\n\n}).call(this);\n", "// Generated by CoffeeScript 1.12.7\n(function() {\n  var NodeType, XMLCharacterData, XMLComment,\n    extend = function(child, parent) { for (var key in parent) { if (hasProp.call(parent, key)) child[key] = parent[key]; } function ctor() { this.constructor = child; } ctor.prototype = parent.prototype; child.prototype = new ctor(); child.__super__ = parent.prototype; return child; },\n    hasProp = {}.hasOwnProperty;\n\n  NodeType = require('./NodeType');\n\n  XMLCharacterData = require('./XMLCharacterData');\n\n  module.exports = XMLComment = (function(superClass) {\n    extend(XMLComment, superClass);\n\n    function XMLComment(parent, text) {\n      XMLComment.__super__.constructor.call(this, parent);\n      if (text == null) {\n        throw new Error(\"Missing comment text. \" + this.debugInfo());\n      }\n      this.name = \"#comment\";\n      this.type = NodeType.Comment;\n      this.value = this.stringify.comment(text);\n    }\n\n    XMLComment.prototype.clone = function() {\n      return Object.create(this);\n    };\n\n    XMLComment.prototype.toString = function(options) {\n      return this.options.writer.comment(this, this.options.writer.filterOptions(options));\n    };\n\n    return XMLComment;\n\n  })(XMLCharacterData);\n\n}).call(this);\n", "// Generated by CoffeeScript 1.12.7\n(function() {\n  var NodeType, XMLDeclaration, XMLNode, isObject,\n    extend = function(child, parent) { for (var key in parent) { if (hasProp.call(parent, key)) child[key] = parent[key]; } function ctor() { this.constructor = child; } ctor.prototype = parent.prototype; child.prototype = new ctor(); child.__super__ = parent.prototype; return child; },\n    hasProp = {}.hasOwnProperty;\n\n  isObject = require('./Utility').isObject;\n\n  XMLNode = require('./XMLNode');\n\n  NodeType = require('./NodeType');\n\n  module.exports = XMLDeclaration = (function(superClass) {\n    extend(XMLDeclaration, superClass);\n\n    function XMLDeclaration(parent, version, encoding, standalone) {\n      var ref;\n      XMLDeclaration.__super__.constructor.call(this, parent);\n      if (isObject(version)) {\n        ref = version, version = ref.version, encoding = ref.encoding, standalone = ref.standalone;\n      }\n      if (!version) {\n        version = '1.0';\n      }\n      this.type = NodeType.Declaration;\n      this.version = this.stringify.xmlVersion(version);\n      if (encoding != null) {\n        this.encoding = this.stringify.xmlEncoding(encoding);\n      }\n      if (standalone != null) {\n        this.standalone = this.stringify.xmlStandalone(standalone);\n      }\n    }\n\n    XMLDeclaration.prototype.toString = function(options) {\n      return this.options.writer.declaration(this, this.options.writer.filterOptions(options));\n    };\n\n    return XMLDeclaration;\n\n  })(XMLNode);\n\n}).call(this);\n", "// Generated by CoffeeScript 1.12.7\n(function() {\n  var NodeType, XMLDTDAttList, XMLDTDElement, XMLDTDEntity, XMLDTDNotation, XMLDocType, XMLNamedNodeMap, XMLNode, isObject,\n    extend = function(child, parent) { for (var key in parent) { if (hasProp.call(parent, key)) child[key] = parent[key]; } function ctor() { this.constructor = child; } ctor.prototype = parent.prototype; child.prototype = new ctor(); child.__super__ = parent.prototype; return child; },\n    hasProp = {}.hasOwnProperty;\n\n  isObject = require('./Utility').isObject;\n\n  XMLNode = require('./XMLNode');\n\n  NodeType = require('./NodeType');\n\n  XMLDTDAttList = require('./XMLDTDAttList');\n\n  XMLDTDEntity = require('./XMLDTDEntity');\n\n  XMLDTDElement = require('./XMLDTDElement');\n\n  XMLDTDNotation = require('./XMLDTDNotation');\n\n  XMLNamedNodeMap = require('./XMLNamedNodeMap');\n\n  module.exports = XMLDocType = (function(superClass) {\n    extend(XMLDocType, superClass);\n\n    function XMLDocType(parent, pubID, sysID) {\n      var child, i, len, ref, ref1, ref2;\n      XMLDocType.__super__.constructor.call(this, parent);\n      this.type = NodeType.DocType;\n      if (parent.children) {\n        ref = parent.children;\n        for (i = 0, len = ref.length; i < len; i++) {\n          child = ref[i];\n          if (child.type === NodeType.Element) {\n            this.name = child.name;\n            break;\n          }\n        }\n      }\n      this.documentObject = parent;\n      if (isObject(pubID)) {\n        ref1 = pubID, pubID = ref1.pubID, sysID = ref1.sysID;\n      }\n      if (sysID == null) {\n        ref2 = [pubID, sysID], sysID = ref2[0], pubID = ref2[1];\n      }\n      if (pubID != null) {\n        this.pubID = this.stringify.dtdPubID(pubID);\n      }\n      if (sysID != null) {\n        this.sysID = this.stringify.dtdSysID(sysID);\n      }\n    }\n\n    Object.defineProperty(XMLDocType.prototype, 'entities', {\n      get: function() {\n        var child, i, len, nodes, ref;\n        nodes = {};\n        ref = this.children;\n        for (i = 0, len = ref.length; i < len; i++) {\n          child = ref[i];\n          if ((child.type === NodeType.EntityDeclaration) && !child.pe) {\n            nodes[child.name] = child;\n          }\n        }\n        return new XMLNamedNodeMap(nodes);\n      }\n    });\n\n    Object.defineProperty(XMLDocType.prototype, 'notations', {\n      get: function() {\n        var child, i, len, nodes, ref;\n        nodes = {};\n        ref = this.children;\n        for (i = 0, len = ref.length; i < len; i++) {\n          child = ref[i];\n          if (child.type === NodeType.NotationDeclaration) {\n            nodes[child.name] = child;\n          }\n        }\n        return new XMLNamedNodeMap(nodes);\n      }\n    });\n\n    Object.defineProperty(XMLDocType.prototype, 'publicId', {\n      get: function() {\n        return this.pubID;\n      }\n    });\n\n    Object.defineProperty(XMLDocType.prototype, 'systemId', {\n      get: function() {\n        return this.sysID;\n      }\n    });\n\n    Object.defineProperty(XMLDocType.prototype, 'internalSubset', {\n      get: function() {\n        throw new Error(\"This DOM method is not implemented.\" + this.debugInfo());\n      }\n    });\n\n    XMLDocType.prototype.element = function(name, value) {\n      var child;\n      child = new XMLDTDElement(this, name, value);\n      this.children.push(child);\n      return this;\n    };\n\n    XMLDocType.prototype.attList = function(elementName, attributeName, attributeType, defaultValueType, defaultValue) {\n      var child;\n      child = new XMLDTDAttList(this, elementName, attributeName, attributeType, defaultValueType, defaultValue);\n      this.children.push(child);\n      return this;\n    };\n\n    XMLDocType.prototype.entity = function(name, value) {\n      var child;\n      child = new XMLDTDEntity(this, false, name, value);\n      this.children.push(child);\n      return this;\n    };\n\n    XMLDocType.prototype.pEntity = function(name, value) {\n      var child;\n      child = new XMLDTDEntity(this, true, name, value);\n      this.children.push(child);\n      return this;\n    };\n\n    XMLDocType.prototype.notation = function(name, value) {\n      var child;\n      child = new XMLDTDNotation(this, name, value);\n      this.children.push(child);\n      return this;\n    };\n\n    XMLDocType.prototype.toString = function(options) {\n      return this.options.writer.docType(this, this.options.writer.filterOptions(options));\n    };\n\n    XMLDocType.prototype.ele = function(name, value) {\n      return this.element(name, value);\n    };\n\n    XMLDocType.prototype.att = function(elementName, attributeName, attributeType, defaultValueType, defaultValue) {\n      return this.attList(elementName, attributeName, attributeType, defaultValueType, defaultValue);\n    };\n\n    XMLDocType.prototype.ent = function(name, value) {\n      return this.entity(name, value);\n    };\n\n    XMLDocType.prototype.pent = function(name, value) {\n      return this.pEntity(name, value);\n    };\n\n    XMLDocType.prototype.not = function(name, value) {\n      return this.notation(name, value);\n    };\n\n    XMLDocType.prototype.up = function() {\n      return this.root() || this.documentObject;\n    };\n\n    XMLDocType.prototype.isEqualNode = function(node) {\n      if (!XMLDocType.__super__.isEqualNode.apply(this, arguments).isEqualNode(node)) {\n        return false;\n      }\n      if (node.name !== this.name) {\n        return false;\n      }\n      if (node.publicId !== this.publicId) {\n        return false;\n      }\n      if (node.systemId !== this.systemId) {\n        return false;\n      }\n      return true;\n    };\n\n    return XMLDocType;\n\n  })(XMLNode);\n\n}).call(this);\n", "// Generated by CoffeeScript 1.12.7\n(function() {\n  var NodeType, XMLDTDAttList, XMLNode,\n    extend = function(child, parent) { for (var key in parent) { if (hasProp.call(parent, key)) child[key] = parent[key]; } function ctor() { this.constructor = child; } ctor.prototype = parent.prototype; child.prototype = new ctor(); child.__super__ = parent.prototype; return child; },\n    hasProp = {}.hasOwnProperty;\n\n  XMLNode = require('./XMLNode');\n\n  NodeType = require('./NodeType');\n\n  module.exports = XMLDTDAttList = (function(superClass) {\n    extend(XMLDTDAttList, superClass);\n\n    function XMLDTDAttList(parent, elementName, attributeName, attributeType, defaultValueType, defaultValue) {\n      XMLDTDAttList.__super__.constructor.call(this, parent);\n      if (elementName == null) {\n        throw new Error(\"Missing DTD element name. \" + this.debugInfo());\n      }\n      if (attributeName == null) {\n        throw new Error(\"Missing DTD attribute name. \" + this.debugInfo(elementName));\n      }\n      if (!attributeType) {\n        throw new Error(\"Missing DTD attribute type. \" + this.debugInfo(elementName));\n      }\n      if (!defaultValueType) {\n        throw new Error(\"Missing DTD attribute default. \" + this.debugInfo(elementName));\n      }\n      if (defaultValueType.indexOf('#') !== 0) {\n        defaultValueType = '#' + defaultValueType;\n      }\n      if (!defaultValueType.match(/^(#REQUIRED|#IMPLIED|#FIXED|#DEFAULT)$/)) {\n        throw new Error(\"Invalid default value type; expected: #REQUIRED, #IMPLIED, #FIXED or #DEFAULT. \" + this.debugInfo(elementName));\n      }\n      if (defaultValue && !defaultValueType.match(/^(#FIXED|#DEFAULT)$/)) {\n        throw new Error(\"Default value only applies to #FIXED or #DEFAULT. \" + this.debugInfo(elementName));\n      }\n      this.elementName = this.stringify.name(elementName);\n      this.type = NodeType.AttributeDeclaration;\n      this.attributeName = this.stringify.name(attributeName);\n      this.attributeType = this.stringify.dtdAttType(attributeType);\n      if (defaultValue) {\n        this.defaultValue = this.stringify.dtdAttDefault(defaultValue);\n      }\n      this.defaultValueType = defaultValueType;\n    }\n\n    XMLDTDAttList.prototype.toString = function(options) {\n      return this.options.writer.dtdAttList(this, this.options.writer.filterOptions(options));\n    };\n\n    return XMLDTDAttList;\n\n  })(XMLNode);\n\n}).call(this);\n", "// Generated by CoffeeScript 1.12.7\n(function() {\n  var NodeType, XMLDTDEntity, XMLNode, isObject,\n    extend = function(child, parent) { for (var key in parent) { if (hasProp.call(parent, key)) child[key] = parent[key]; } function ctor() { this.constructor = child; } ctor.prototype = parent.prototype; child.prototype = new ctor(); child.__super__ = parent.prototype; return child; },\n    hasProp = {}.hasOwnProperty;\n\n  isObject = require('./Utility').isObject;\n\n  XMLNode = require('./XMLNode');\n\n  NodeType = require('./NodeType');\n\n  module.exports = XMLDTDEntity = (function(superClass) {\n    extend(XMLDTDEntity, superClass);\n\n    function XMLDTDEntity(parent, pe, name, value) {\n      XMLDTDEntity.__super__.constructor.call(this, parent);\n      if (name == null) {\n        throw new Error(\"Missing DTD entity name. \" + this.debugInfo(name));\n      }\n      if (value == null) {\n        throw new Error(\"Missing DTD entity value. \" + this.debugInfo(name));\n      }\n      this.pe = !!pe;\n      this.name = this.stringify.name(name);\n      this.type = NodeType.EntityDeclaration;\n      if (!isObject(value)) {\n        this.value = this.stringify.dtdEntityValue(value);\n        this.internal = true;\n      } else {\n        if (!value.pubID && !value.sysID) {\n          throw new Error(\"Public and/or system identifiers are required for an external entity. \" + this.debugInfo(name));\n        }\n        if (value.pubID && !value.sysID) {\n          throw new Error(\"System identifier is required for a public external entity. \" + this.debugInfo(name));\n        }\n        this.internal = false;\n        if (value.pubID != null) {\n          this.pubID = this.stringify.dtdPubID(value.pubID);\n        }\n        if (value.sysID != null) {\n          this.sysID = this.stringify.dtdSysID(value.sysID);\n        }\n        if (value.nData != null) {\n          this.nData = this.stringify.dtdNData(value.nData);\n        }\n        if (this.pe && this.nData) {\n          throw new Error(\"Notation declaration is not allowed in a parameter entity. \" + this.debugInfo(name));\n        }\n      }\n    }\n\n    Object.defineProperty(XMLDTDEntity.prototype, 'publicId', {\n      get: function() {\n        return this.pubID;\n      }\n    });\n\n    Object.defineProperty(XMLDTDEntity.prototype, 'systemId', {\n      get: function() {\n        return this.sysID;\n      }\n    });\n\n    Object.defineProperty(XMLDTDEntity.prototype, 'notationName', {\n      get: function() {\n        return this.nData || null;\n      }\n    });\n\n    Object.defineProperty(XMLDTDEntity.prototype, 'inputEncoding', {\n      get: function() {\n        return null;\n      }\n    });\n\n    Object.defineProperty(XMLDTDEntity.prototype, 'xmlEncoding', {\n      get: function() {\n        return null;\n      }\n    });\n\n    Object.defineProperty(XMLDTDEntity.prototype, 'xmlVersion', {\n      get: function() {\n        return null;\n      }\n    });\n\n    XMLDTDEntity.prototype.toString = function(options) {\n      return this.options.writer.dtdEntity(this, this.options.writer.filterOptions(options));\n    };\n\n    return XMLDTDEntity;\n\n  })(XMLNode);\n\n}).call(this);\n", "// Generated by CoffeeScript 1.12.7\n(function() {\n  var NodeType, XMLDTDElement, XMLNode,\n    extend = function(child, parent) { for (var key in parent) { if (hasProp.call(parent, key)) child[key] = parent[key]; } function ctor() { this.constructor = child; } ctor.prototype = parent.prototype; child.prototype = new ctor(); child.__super__ = parent.prototype; return child; },\n    hasProp = {}.hasOwnProperty;\n\n  XMLNode = require('./XMLNode');\n\n  NodeType = require('./NodeType');\n\n  module.exports = XMLDTDElement = (function(superClass) {\n    extend(XMLDTDElement, superClass);\n\n    function XMLDTDElement(parent, name, value) {\n      XMLDTDElement.__super__.constructor.call(this, parent);\n      if (name == null) {\n        throw new Error(\"Missing DTD element name. \" + this.debugInfo());\n      }\n      if (!value) {\n        value = '(#PCDATA)';\n      }\n      if (Array.isArray(value)) {\n        value = '(' + value.join(',') + ')';\n      }\n      this.name = this.stringify.name(name);\n      this.type = NodeType.ElementDeclaration;\n      this.value = this.stringify.dtdElementValue(value);\n    }\n\n    XMLDTDElement.prototype.toString = function(options) {\n      return this.options.writer.dtdElement(this, this.options.writer.filterOptions(options));\n    };\n\n    return XMLDTDElement;\n\n  })(XMLNode);\n\n}).call(this);\n", "// Generated by CoffeeScript 1.12.7\n(function() {\n  var NodeType, XMLDTDNotation, XMLNode,\n    extend = function(child, parent) { for (var key in parent) { if (hasProp.call(parent, key)) child[key] = parent[key]; } function ctor() { this.constructor = child; } ctor.prototype = parent.prototype; child.prototype = new ctor(); child.__super__ = parent.prototype; return child; },\n    hasProp = {}.hasOwnProperty;\n\n  XMLNode = require('./XMLNode');\n\n  NodeType = require('./NodeType');\n\n  module.exports = XMLDTDNotation = (function(superClass) {\n    extend(XMLDTDNotation, superClass);\n\n    function XMLDTDNotation(parent, name, value) {\n      XMLDTDNotation.__super__.constructor.call(this, parent);\n      if (name == null) {\n        throw new Error(\"Missing DTD notation name. \" + this.debugInfo(name));\n      }\n      if (!value.pubID && !value.sysID) {\n        throw new Error(\"Public or system identifiers are required for an external entity. \" + this.debugInfo(name));\n      }\n      this.name = this.stringify.name(name);\n      this.type = NodeType.NotationDeclaration;\n      if (value.pubID != null) {\n        this.pubID = this.stringify.dtdPubID(value.pubID);\n      }\n      if (value.sysID != null) {\n        this.sysID = this.stringify.dtdSysID(value.sysID);\n      }\n    }\n\n    Object.defineProperty(XMLDTDNotation.prototype, 'publicId', {\n      get: function() {\n        return this.pubID;\n      }\n    });\n\n    Object.defineProperty(XMLDTDNotation.prototype, 'systemId', {\n      get: function() {\n        return this.sysID;\n      }\n    });\n\n    XMLDTDNotation.prototype.toString = function(options) {\n      return this.options.writer.dtdNotation(this, this.options.writer.filterOptions(options));\n    };\n\n    return XMLDTDNotation;\n\n  })(XMLNode);\n\n}).call(this);\n", "// Generated by CoffeeScript 1.12.7\n(function() {\n  var NodeType, XMLNode, XMLRaw,\n    extend = function(child, parent) { for (var key in parent) { if (hasProp.call(parent, key)) child[key] = parent[key]; } function ctor() { this.constructor = child; } ctor.prototype = parent.prototype; child.prototype = new ctor(); child.__super__ = parent.prototype; return child; },\n    hasProp = {}.hasOwnProperty;\n\n  NodeType = require('./NodeType');\n\n  XMLNode = require('./XMLNode');\n\n  module.exports = XMLRaw = (function(superClass) {\n    extend(XMLRaw, superClass);\n\n    function XMLRaw(parent, text) {\n      XMLRaw.__super__.constructor.call(this, parent);\n      if (text == null) {\n        throw new Error(\"Missing raw text. \" + this.debugInfo());\n      }\n      this.type = NodeType.Raw;\n      this.value = this.stringify.raw(text);\n    }\n\n    XMLRaw.prototype.clone = function() {\n      return Object.create(this);\n    };\n\n    XMLRaw.prototype.toString = function(options) {\n      return this.options.writer.raw(this, this.options.writer.filterOptions(options));\n    };\n\n    return XMLRaw;\n\n  })(XMLNode);\n\n}).call(this);\n", "// Generated by CoffeeScript 1.12.7\n(function() {\n  var NodeType, XMLCharacterData, XMLText,\n    extend = function(child, parent) { for (var key in parent) { if (hasProp.call(parent, key)) child[key] = parent[key]; } function ctor() { this.constructor = child; } ctor.prototype = parent.prototype; child.prototype = new ctor(); child.__super__ = parent.prototype; return child; },\n    hasProp = {}.hasOwnProperty;\n\n  NodeType = require('./NodeType');\n\n  XMLCharacterData = require('./XMLCharacterData');\n\n  module.exports = XMLText = (function(superClass) {\n    extend(XMLText, superClass);\n\n    function XMLText(parent, text) {\n      XMLText.__super__.constructor.call(this, parent);\n      if (text == null) {\n        throw new Error(\"Missing element text. \" + this.debugInfo());\n      }\n      this.name = \"#text\";\n      this.type = NodeType.Text;\n      this.value = this.stringify.text(text);\n    }\n\n    Object.defineProperty(XMLText.prototype, 'isElementContentWhitespace', {\n      get: function() {\n        throw new Error(\"This DOM method is not implemented.\" + this.debugInfo());\n      }\n    });\n\n    Object.defineProperty(XMLText.prototype, 'wholeText', {\n      get: function() {\n        var next, prev, str;\n        str = '';\n        prev = this.previousSibling;\n        while (prev) {\n          str = prev.data + str;\n          prev = prev.previousSibling;\n        }\n        str += this.data;\n        next = this.nextSibling;\n        while (next) {\n          str = str + next.data;\n          next = next.nextSibling;\n        }\n        return str;\n      }\n    });\n\n    XMLText.prototype.clone = function() {\n      return Object.create(this);\n    };\n\n    XMLText.prototype.toString = function(options) {\n      return this.options.writer.text(this, this.options.writer.filterOptions(options));\n    };\n\n    XMLText.prototype.splitText = function(offset) {\n      throw new Error(\"This DOM method is not implemented.\" + this.debugInfo());\n    };\n\n    XMLText.prototype.replaceWholeText = function(content) {\n      throw new Error(\"This DOM method is not implemented.\" + this.debugInfo());\n    };\n\n    return XMLText;\n\n  })(XMLCharacterData);\n\n}).call(this);\n", "// Generated by CoffeeScript 1.12.7\n(function() {\n  var NodeType, XMLCharacterData, XMLProcessingInstruction,\n    extend = function(child, parent) { for (var key in parent) { if (hasProp.call(parent, key)) child[key] = parent[key]; } function ctor() { this.constructor = child; } ctor.prototype = parent.prototype; child.prototype = new ctor(); child.__super__ = parent.prototype; return child; },\n    hasProp = {}.hasOwnProperty;\n\n  NodeType = require('./NodeType');\n\n  XMLCharacterData = require('./XMLCharacterData');\n\n  module.exports = XMLProcessingInstruction = (function(superClass) {\n    extend(XMLProcessingInstruction, superClass);\n\n    function XMLProcessingInstruction(parent, target, value) {\n      XMLProcessingInstruction.__super__.constructor.call(this, parent);\n      if (target == null) {\n        throw new Error(\"Missing instruction target. \" + this.debugInfo());\n      }\n      this.type = NodeType.ProcessingInstruction;\n      this.target = this.stringify.insTarget(target);\n      this.name = this.target;\n      if (value) {\n        this.value = this.stringify.insValue(value);\n      }\n    }\n\n    XMLProcessingInstruction.prototype.clone = function() {\n      return Object.create(this);\n    };\n\n    XMLProcessingInstruction.prototype.toString = function(options) {\n      return this.options.writer.processingInstruction(this, this.options.writer.filterOptions(options));\n    };\n\n    XMLProcessingInstruction.prototype.isEqualNode = function(node) {\n      if (!XMLProcessingInstruction.__super__.isEqualNode.apply(this, arguments).isEqualNode(node)) {\n        return false;\n      }\n      if (node.target !== this.target) {\n        return false;\n      }\n      return true;\n    };\n\n    return XMLProcessingInstruction;\n\n  })(XMLCharacterData);\n\n}).call(this);\n", "// Generated by CoffeeScript 1.12.7\n(function() {\n  var NodeType, XMLDummy, XMLNode,\n    extend = function(child, parent) { for (var key in parent) { if (hasProp.call(parent, key)) child[key] = parent[key]; } function ctor() { this.constructor = child; } ctor.prototype = parent.prototype; child.prototype = new ctor(); child.__super__ = parent.prototype; return child; },\n    hasProp = {}.hasOwnProperty;\n\n  XMLNode = require('./XMLNode');\n\n  NodeType = require('./NodeType');\n\n  module.exports = XMLDummy = (function(superClass) {\n    extend(XMLDummy, superClass);\n\n    function XMLDummy(parent) {\n      XMLDummy.__super__.constructor.call(this, parent);\n      this.type = NodeType.Dummy;\n    }\n\n    XMLDummy.prototype.clone = function() {\n      return Object.create(this);\n    };\n\n    XMLDummy.prototype.toString = function(options) {\n      return '';\n    };\n\n    return XMLDummy;\n\n  })(XMLNode);\n\n}).call(this);\n", "// Generated by CoffeeScript 1.12.7\n(function() {\n  var XMLNodeList;\n\n  module.exports = XMLNodeList = (function() {\n    function XMLNodeList(nodes) {\n      this.nodes = nodes;\n    }\n\n    Object.defineProperty(XMLNodeList.prototype, 'length', {\n      get: function() {\n        return this.nodes.length || 0;\n      }\n    });\n\n    XMLNodeList.prototype.clone = function() {\n      return this.nodes = null;\n    };\n\n    XMLNodeList.prototype.item = function(index) {\n      return this.nodes[index] || null;\n    };\n\n    return XMLNodeList;\n\n  })();\n\n}).call(this);\n", "// Generated by CoffeeScript 1.12.7\n(function() {\n  module.exports = {\n    Disconnected: 1,\n    Preceding: 2,\n    Following: 4,\n    Contains: 8,\n    ContainedBy: 16,\n    ImplementationSpecific: 32\n  };\n\n}).call(this);\n", "// Generated by CoffeeScript 1.12.7\n(function() {\n  var XMLStringifier,\n    bind = function(fn, me){ return function(){ return fn.apply(me, arguments); }; },\n    hasProp = {}.hasOwnProperty;\n\n  module.exports = XMLStringifier = (function() {\n    function XMLStringifier(options) {\n      this.assertLegalName = bind(this.assertLegalName, this);\n      this.assertLegalChar = bind(this.assertLegalChar, this);\n      var key, ref, value;\n      options || (options = {});\n      this.options = options;\n      if (!this.options.version) {\n        this.options.version = '1.0';\n      }\n      ref = options.stringify || {};\n      for (key in ref) {\n        if (!hasProp.call(ref, key)) continue;\n        value = ref[key];\n        this[key] = value;\n      }\n    }\n\n    XMLStringifier.prototype.name = function(val) {\n      if (this.options.noValidation) {\n        return val;\n      }\n      return this.assertLegalName('' + val || '');\n    };\n\n    XMLStringifier.prototype.text = function(val) {\n      if (this.options.noValidation) {\n        return val;\n      }\n      return this.assertLegalChar(this.textEscape('' + val || ''));\n    };\n\n    XMLStringifier.prototype.cdata = function(val) {\n      if (this.options.noValidation) {\n        return val;\n      }\n      val = '' + val || '';\n      val = val.replace(']]>', ']]]]><![CDATA[>');\n      return this.assertLegalChar(val);\n    };\n\n    XMLStringifier.prototype.comment = function(val) {\n      if (this.options.noValidation) {\n        return val;\n      }\n      val = '' + val || '';\n      if (val.match(/--/)) {\n        throw new Error(\"Comment text cannot contain double-hypen: \" + val);\n      }\n      return this.assertLegalChar(val);\n    };\n\n    XMLStringifier.prototype.raw = function(val) {\n      if (this.options.noValidation) {\n        return val;\n      }\n      return '' + val || '';\n    };\n\n    XMLStringifier.prototype.attValue = function(val) {\n      if (this.options.noValidation) {\n        return val;\n      }\n      return this.assertLegalChar(this.attEscape(val = '' + val || ''));\n    };\n\n    XMLStringifier.prototype.insTarget = function(val) {\n      if (this.options.noValidation) {\n        return val;\n      }\n      return this.assertLegalChar('' + val || '');\n    };\n\n    XMLStringifier.prototype.insValue = function(val) {\n      if (this.options.noValidation) {\n        return val;\n      }\n      val = '' + val || '';\n      if (val.match(/\\?>/)) {\n        throw new Error(\"Invalid processing instruction value: \" + val);\n      }\n      return this.assertLegalChar(val);\n    };\n\n    XMLStringifier.prototype.xmlVersion = function(val) {\n      if (this.options.noValidation) {\n        return val;\n      }\n      val = '' + val || '';\n      if (!val.match(/1\\.[0-9]+/)) {\n        throw new Error(\"Invalid version number: \" + val);\n      }\n      return val;\n    };\n\n    XMLStringifier.prototype.xmlEncoding = function(val) {\n      if (this.options.noValidation) {\n        return val;\n      }\n      val = '' + val || '';\n      if (!val.match(/^[A-Za-z](?:[A-Za-z0-9._-])*$/)) {\n        throw new Error(\"Invalid encoding: \" + val);\n      }\n      return this.assertLegalChar(val);\n    };\n\n    XMLStringifier.prototype.xmlStandalone = function(val) {\n      if (this.options.noValidation) {\n        return val;\n      }\n      if (val) {\n        return \"yes\";\n      } else {\n        return \"no\";\n      }\n    };\n\n    XMLStringifier.prototype.dtdPubID = function(val) {\n      if (this.options.noValidation) {\n        return val;\n      }\n      return this.assertLegalChar('' + val || '');\n    };\n\n    XMLStringifier.prototype.dtdSysID = function(val) {\n      if (this.options.noValidation) {\n        return val;\n      }\n      return this.assertLegalChar('' + val || '');\n    };\n\n    XMLStringifier.prototype.dtdElementValue = function(val) {\n      if (this.options.noValidation) {\n        return val;\n      }\n      return this.assertLegalChar('' + val || '');\n    };\n\n    XMLStringifier.prototype.dtdAttType = function(val) {\n      if (this.options.noValidation) {\n        return val;\n      }\n      return this.assertLegalChar('' + val || '');\n    };\n\n    XMLStringifier.prototype.dtdAttDefault = function(val) {\n      if (this.options.noValidation) {\n        return val;\n      }\n      return this.assertLegalChar('' + val || '');\n    };\n\n    XMLStringifier.prototype.dtdEntityValue = function(val) {\n      if (this.options.noValidation) {\n        return val;\n      }\n      return this.assertLegalChar('' + val || '');\n    };\n\n    XMLStringifier.prototype.dtdNData = function(val) {\n      if (this.options.noValidation) {\n        return val;\n      }\n      return this.assertLegalChar('' + val || '');\n    };\n\n    XMLStringifier.prototype.convertAttKey = '@';\n\n    XMLStringifier.prototype.convertPIKey = '?';\n\n    XMLStringifier.prototype.convertTextKey = '#text';\n\n    XMLStringifier.prototype.convertCDataKey = '#cdata';\n\n    XMLStringifier.prototype.convertCommentKey = '#comment';\n\n    XMLStringifier.prototype.convertRawKey = '#raw';\n\n    XMLStringifier.prototype.assertLegalChar = function(str) {\n      var regex, res;\n      if (this.options.noValidation) {\n        return str;\n      }\n      regex = '';\n      if (this.options.version === '1.0') {\n        regex = /[\\0-\\x08\\x0B\\f\\x0E-\\x1F\\uFFFE\\uFFFF]|[\\uD800-\\uDBFF](?![\\uDC00-\\uDFFF])|(?:[^\\uD800-\\uDBFF]|^)[\\uDC00-\\uDFFF]/;\n        if (res = str.match(regex)) {\n          throw new Error(\"Invalid character in string: \" + str + \" at index \" + res.index);\n        }\n      } else if (this.options.version === '1.1') {\n        regex = /[\\0\\uFFFE\\uFFFF]|[\\uD800-\\uDBFF](?![\\uDC00-\\uDFFF])|(?:[^\\uD800-\\uDBFF]|^)[\\uDC00-\\uDFFF]/;\n        if (res = str.match(regex)) {\n          throw new Error(\"Invalid character in string: \" + str + \" at index \" + res.index);\n        }\n      }\n      return str;\n    };\n\n    XMLStringifier.prototype.assertLegalName = function(str) {\n      var regex;\n      if (this.options.noValidation) {\n        return str;\n      }\n      this.assertLegalChar(str);\n      regex = /^([:A-Z_a-z\\xC0-\\xD6\\xD8-\\xF6\\xF8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD]|[\\uD800-\\uDB7F][\\uDC00-\\uDFFF])([\\x2D\\.0-:A-Z_a-z\\xB7\\xC0-\\xD6\\xD8-\\xF6\\xF8-\\u037D\\u037F-\\u1FFF\\u200C\\u200D\\u203F\\u2040\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD]|[\\uD800-\\uDB7F][\\uDC00-\\uDFFF])*$/;\n      if (!str.match(regex)) {\n        throw new Error(\"Invalid character in name\");\n      }\n      return str;\n    };\n\n    XMLStringifier.prototype.textEscape = function(str) {\n      var ampregex;\n      if (this.options.noValidation) {\n        return str;\n      }\n      ampregex = this.options.noDoubleEncoding ? /(?!&\\S+;)&/g : /&/g;\n      return str.replace(ampregex, '&amp;').replace(/</g, '&lt;').replace(/>/g, '&gt;').replace(/\\r/g, '&#xD;');\n    };\n\n    XMLStringifier.prototype.attEscape = function(str) {\n      var ampregex;\n      if (this.options.noValidation) {\n        return str;\n      }\n      ampregex = this.options.noDoubleEncoding ? /(?!&\\S+;)&/g : /&/g;\n      return str.replace(ampregex, '&amp;').replace(/</g, '&lt;').replace(/\"/g, '&quot;').replace(/\\t/g, '&#x9;').replace(/\\n/g, '&#xA;').replace(/\\r/g, '&#xD;');\n    };\n\n    return XMLStringifier;\n\n  })();\n\n}).call(this);\n", "// Generated by CoffeeScript 1.12.7\n(function() {\n  var XMLStringWriter, XMLWriterBase,\n    extend = function(child, parent) { for (var key in parent) { if (hasProp.call(parent, key)) child[key] = parent[key]; } function ctor() { this.constructor = child; } ctor.prototype = parent.prototype; child.prototype = new ctor(); child.__super__ = parent.prototype; return child; },\n    hasProp = {}.hasOwnProperty;\n\n  XMLWriterBase = require('./XMLWriterBase');\n\n  module.exports = XMLStringWriter = (function(superClass) {\n    extend(XMLStringWriter, superClass);\n\n    function XMLStringWriter(options) {\n      XMLStringWriter.__super__.constructor.call(this, options);\n    }\n\n    XMLStringWriter.prototype.document = function(doc, options) {\n      var child, i, len, r, ref;\n      options = this.filterOptions(options);\n      r = '';\n      ref = doc.children;\n      for (i = 0, len = ref.length; i < len; i++) {\n        child = ref[i];\n        r += this.writeChildNode(child, options, 0);\n      }\n      if (options.pretty && r.slice(-options.newline.length) === options.newline) {\n        r = r.slice(0, -options.newline.length);\n      }\n      return r;\n    };\n\n    return XMLStringWriter;\n\n  })(XMLWriterBase);\n\n}).call(this);\n", "// Generated by CoffeeScript 1.12.7\n(function() {\n  var NodeType, WriterState, XMLCData, XMLComment, XMLDTDAttList, XMLDTDElement, XMLDTDEntity, XMLDTDNotation, XMLDeclaration, XMLDocType, XMLDummy, XMLElement, XMLProcessingInstruction, XMLRaw, XMLText, XMLWriterBase, assign,\n    hasProp = {}.hasOwnProperty;\n\n  assign = require('./Utility').assign;\n\n  NodeType = require('./NodeType');\n\n  XMLDeclaration = require('./XMLDeclaration');\n\n  XMLDocType = require('./XMLDocType');\n\n  XMLCData = require('./XMLCData');\n\n  XMLComment = require('./XMLComment');\n\n  XMLElement = require('./XMLElement');\n\n  XMLRaw = require('./XMLRaw');\n\n  XMLText = require('./XMLText');\n\n  XMLProcessingInstruction = require('./XMLProcessingInstruction');\n\n  XMLDummy = require('./XMLDummy');\n\n  XMLDTDAttList = require('./XMLDTDAttList');\n\n  XMLDTDElement = require('./XMLDTDElement');\n\n  XMLDTDEntity = require('./XMLDTDEntity');\n\n  XMLDTDNotation = require('./XMLDTDNotation');\n\n  WriterState = require('./WriterState');\n\n  module.exports = XMLWriterBase = (function() {\n    function XMLWriterBase(options) {\n      var key, ref, value;\n      options || (options = {});\n      this.options = options;\n      ref = options.writer || {};\n      for (key in ref) {\n        if (!hasProp.call(ref, key)) continue;\n        value = ref[key];\n        this[\"_\" + key] = this[key];\n        this[key] = value;\n      }\n    }\n\n    XMLWriterBase.prototype.filterOptions = function(options) {\n      var filteredOptions, ref, ref1, ref2, ref3, ref4, ref5, ref6;\n      options || (options = {});\n      options = assign({}, this.options, options);\n      filteredOptions = {\n        writer: this\n      };\n      filteredOptions.pretty = options.pretty || false;\n      filteredOptions.allowEmpty = options.allowEmpty || false;\n      filteredOptions.indent = (ref = options.indent) != null ? ref : '  ';\n      filteredOptions.newline = (ref1 = options.newline) != null ? ref1 : '\\n';\n      filteredOptions.offset = (ref2 = options.offset) != null ? ref2 : 0;\n      filteredOptions.dontPrettyTextNodes = (ref3 = (ref4 = options.dontPrettyTextNodes) != null ? ref4 : options.dontprettytextnodes) != null ? ref3 : 0;\n      filteredOptions.spaceBeforeSlash = (ref5 = (ref6 = options.spaceBeforeSlash) != null ? ref6 : options.spacebeforeslash) != null ? ref5 : '';\n      if (filteredOptions.spaceBeforeSlash === true) {\n        filteredOptions.spaceBeforeSlash = ' ';\n      }\n      filteredOptions.suppressPrettyCount = 0;\n      filteredOptions.user = {};\n      filteredOptions.state = WriterState.None;\n      return filteredOptions;\n    };\n\n    XMLWriterBase.prototype.indent = function(node, options, level) {\n      var indentLevel;\n      if (!options.pretty || options.suppressPrettyCount) {\n        return '';\n      } else if (options.pretty) {\n        indentLevel = (level || 0) + options.offset + 1;\n        if (indentLevel > 0) {\n          return new Array(indentLevel).join(options.indent);\n        }\n      }\n      return '';\n    };\n\n    XMLWriterBase.prototype.endline = function(node, options, level) {\n      if (!options.pretty || options.suppressPrettyCount) {\n        return '';\n      } else {\n        return options.newline;\n      }\n    };\n\n    XMLWriterBase.prototype.attribute = function(att, options, level) {\n      var r;\n      this.openAttribute(att, options, level);\n      r = ' ' + att.name + '=\"' + att.value + '\"';\n      this.closeAttribute(att, options, level);\n      return r;\n    };\n\n    XMLWriterBase.prototype.cdata = function(node, options, level) {\n      var r;\n      this.openNode(node, options, level);\n      options.state = WriterState.OpenTag;\n      r = this.indent(node, options, level) + '<![CDATA[';\n      options.state = WriterState.InsideTag;\n      r += node.value;\n      options.state = WriterState.CloseTag;\n      r += ']]>' + this.endline(node, options, level);\n      options.state = WriterState.None;\n      this.closeNode(node, options, level);\n      return r;\n    };\n\n    XMLWriterBase.prototype.comment = function(node, options, level) {\n      var r;\n      this.openNode(node, options, level);\n      options.state = WriterState.OpenTag;\n      r = this.indent(node, options, level) + '<!-- ';\n      options.state = WriterState.InsideTag;\n      r += node.value;\n      options.state = WriterState.CloseTag;\n      r += ' -->' + this.endline(node, options, level);\n      options.state = WriterState.None;\n      this.closeNode(node, options, level);\n      return r;\n    };\n\n    XMLWriterBase.prototype.declaration = function(node, options, level) {\n      var r;\n      this.openNode(node, options, level);\n      options.state = WriterState.OpenTag;\n      r = this.indent(node, options, level) + '<?xml';\n      options.state = WriterState.InsideTag;\n      r += ' version=\"' + node.version + '\"';\n      if (node.encoding != null) {\n        r += ' encoding=\"' + node.encoding + '\"';\n      }\n      if (node.standalone != null) {\n        r += ' standalone=\"' + node.standalone + '\"';\n      }\n      options.state = WriterState.CloseTag;\n      r += options.spaceBeforeSlash + '?>';\n      r += this.endline(node, options, level);\n      options.state = WriterState.None;\n      this.closeNode(node, options, level);\n      return r;\n    };\n\n    XMLWriterBase.prototype.docType = function(node, options, level) {\n      var child, i, len, r, ref;\n      level || (level = 0);\n      this.openNode(node, options, level);\n      options.state = WriterState.OpenTag;\n      r = this.indent(node, options, level);\n      r += '<!DOCTYPE ' + node.root().name;\n      if (node.pubID && node.sysID) {\n        r += ' PUBLIC \"' + node.pubID + '\" \"' + node.sysID + '\"';\n      } else if (node.sysID) {\n        r += ' SYSTEM \"' + node.sysID + '\"';\n      }\n      if (node.children.length > 0) {\n        r += ' [';\n        r += this.endline(node, options, level);\n        options.state = WriterState.InsideTag;\n        ref = node.children;\n        for (i = 0, len = ref.length; i < len; i++) {\n          child = ref[i];\n          r += this.writeChildNode(child, options, level + 1);\n        }\n        options.state = WriterState.CloseTag;\n        r += ']';\n      }\n      options.state = WriterState.CloseTag;\n      r += options.spaceBeforeSlash + '>';\n      r += this.endline(node, options, level);\n      options.state = WriterState.None;\n      this.closeNode(node, options, level);\n      return r;\n    };\n\n    XMLWriterBase.prototype.element = function(node, options, level) {\n      var att, child, childNodeCount, firstChildNode, i, j, len, len1, name, prettySuppressed, r, ref, ref1, ref2;\n      level || (level = 0);\n      prettySuppressed = false;\n      r = '';\n      this.openNode(node, options, level);\n      options.state = WriterState.OpenTag;\n      r += this.indent(node, options, level) + '<' + node.name;\n      ref = node.attribs;\n      for (name in ref) {\n        if (!hasProp.call(ref, name)) continue;\n        att = ref[name];\n        r += this.attribute(att, options, level);\n      }\n      childNodeCount = node.children.length;\n      firstChildNode = childNodeCount === 0 ? null : node.children[0];\n      if (childNodeCount === 0 || node.children.every(function(e) {\n        return (e.type === NodeType.Text || e.type === NodeType.Raw) && e.value === '';\n      })) {\n        if (options.allowEmpty) {\n          r += '>';\n          options.state = WriterState.CloseTag;\n          r += '</' + node.name + '>' + this.endline(node, options, level);\n        } else {\n          options.state = WriterState.CloseTag;\n          r += options.spaceBeforeSlash + '/>' + this.endline(node, options, level);\n        }\n      } else if (options.pretty && childNodeCount === 1 && (firstChildNode.type === NodeType.Text || firstChildNode.type === NodeType.Raw) && (firstChildNode.value != null)) {\n        r += '>';\n        options.state = WriterState.InsideTag;\n        options.suppressPrettyCount++;\n        prettySuppressed = true;\n        r += this.writeChildNode(firstChildNode, options, level + 1);\n        options.suppressPrettyCount--;\n        prettySuppressed = false;\n        options.state = WriterState.CloseTag;\n        r += '</' + node.name + '>' + this.endline(node, options, level);\n      } else {\n        if (options.dontPrettyTextNodes) {\n          ref1 = node.children;\n          for (i = 0, len = ref1.length; i < len; i++) {\n            child = ref1[i];\n            if ((child.type === NodeType.Text || child.type === NodeType.Raw) && (child.value != null)) {\n              options.suppressPrettyCount++;\n              prettySuppressed = true;\n              break;\n            }\n          }\n        }\n        r += '>' + this.endline(node, options, level);\n        options.state = WriterState.InsideTag;\n        ref2 = node.children;\n        for (j = 0, len1 = ref2.length; j < len1; j++) {\n          child = ref2[j];\n          r += this.writeChildNode(child, options, level + 1);\n        }\n        options.state = WriterState.CloseTag;\n        r += this.indent(node, options, level) + '</' + node.name + '>';\n        if (prettySuppressed) {\n          options.suppressPrettyCount--;\n        }\n        r += this.endline(node, options, level);\n        options.state = WriterState.None;\n      }\n      this.closeNode(node, options, level);\n      return r;\n    };\n\n    XMLWriterBase.prototype.writeChildNode = function(node, options, level) {\n      switch (node.type) {\n        case NodeType.CData:\n          return this.cdata(node, options, level);\n        case NodeType.Comment:\n          return this.comment(node, options, level);\n        case NodeType.Element:\n          return this.element(node, options, level);\n        case NodeType.Raw:\n          return this.raw(node, options, level);\n        case NodeType.Text:\n          return this.text(node, options, level);\n        case NodeType.ProcessingInstruction:\n          return this.processingInstruction(node, options, level);\n        case NodeType.Dummy:\n          return '';\n        case NodeType.Declaration:\n          return this.declaration(node, options, level);\n        case NodeType.DocType:\n          return this.docType(node, options, level);\n        case NodeType.AttributeDeclaration:\n          return this.dtdAttList(node, options, level);\n        case NodeType.ElementDeclaration:\n          return this.dtdElement(node, options, level);\n        case NodeType.EntityDeclaration:\n          return this.dtdEntity(node, options, level);\n        case NodeType.NotationDeclaration:\n          return this.dtdNotation(node, options, level);\n        default:\n          throw new Error(\"Unknown XML node type: \" + node.constructor.name);\n      }\n    };\n\n    XMLWriterBase.prototype.processingInstruction = function(node, options, level) {\n      var r;\n      this.openNode(node, options, level);\n      options.state = WriterState.OpenTag;\n      r = this.indent(node, options, level) + '<?';\n      options.state = WriterState.InsideTag;\n      r += node.target;\n      if (node.value) {\n        r += ' ' + node.value;\n      }\n      options.state = WriterState.CloseTag;\n      r += options.spaceBeforeSlash + '?>';\n      r += this.endline(node, options, level);\n      options.state = WriterState.None;\n      this.closeNode(node, options, level);\n      return r;\n    };\n\n    XMLWriterBase.prototype.raw = function(node, options, level) {\n      var r;\n      this.openNode(node, options, level);\n      options.state = WriterState.OpenTag;\n      r = this.indent(node, options, level);\n      options.state = WriterState.InsideTag;\n      r += node.value;\n      options.state = WriterState.CloseTag;\n      r += this.endline(node, options, level);\n      options.state = WriterState.None;\n      this.closeNode(node, options, level);\n      return r;\n    };\n\n    XMLWriterBase.prototype.text = function(node, options, level) {\n      var r;\n      this.openNode(node, options, level);\n      options.state = WriterState.OpenTag;\n      r = this.indent(node, options, level);\n      options.state = WriterState.InsideTag;\n      r += node.value;\n      options.state = WriterState.CloseTag;\n      r += this.endline(node, options, level);\n      options.state = WriterState.None;\n      this.closeNode(node, options, level);\n      return r;\n    };\n\n    XMLWriterBase.prototype.dtdAttList = function(node, options, level) {\n      var r;\n      this.openNode(node, options, level);\n      options.state = WriterState.OpenTag;\n      r = this.indent(node, options, level) + '<!ATTLIST';\n      options.state = WriterState.InsideTag;\n      r += ' ' + node.elementName + ' ' + node.attributeName + ' ' + node.attributeType;\n      if (node.defaultValueType !== '#DEFAULT') {\n        r += ' ' + node.defaultValueType;\n      }\n      if (node.defaultValue) {\n        r += ' \"' + node.defaultValue + '\"';\n      }\n      options.state = WriterState.CloseTag;\n      r += options.spaceBeforeSlash + '>' + this.endline(node, options, level);\n      options.state = WriterState.None;\n      this.closeNode(node, options, level);\n      return r;\n    };\n\n    XMLWriterBase.prototype.dtdElement = function(node, options, level) {\n      var r;\n      this.openNode(node, options, level);\n      options.state = WriterState.OpenTag;\n      r = this.indent(node, options, level) + '<!ELEMENT';\n      options.state = WriterState.InsideTag;\n      r += ' ' + node.name + ' ' + node.value;\n      options.state = WriterState.CloseTag;\n      r += options.spaceBeforeSlash + '>' + this.endline(node, options, level);\n      options.state = WriterState.None;\n      this.closeNode(node, options, level);\n      return r;\n    };\n\n    XMLWriterBase.prototype.dtdEntity = function(node, options, level) {\n      var r;\n      this.openNode(node, options, level);\n      options.state = WriterState.OpenTag;\n      r = this.indent(node, options, level) + '<!ENTITY';\n      options.state = WriterState.InsideTag;\n      if (node.pe) {\n        r += ' %';\n      }\n      r += ' ' + node.name;\n      if (node.value) {\n        r += ' \"' + node.value + '\"';\n      } else {\n        if (node.pubID && node.sysID) {\n          r += ' PUBLIC \"' + node.pubID + '\" \"' + node.sysID + '\"';\n        } else if (node.sysID) {\n          r += ' SYSTEM \"' + node.sysID + '\"';\n        }\n        if (node.nData) {\n          r += ' NDATA ' + node.nData;\n        }\n      }\n      options.state = WriterState.CloseTag;\n      r += options.spaceBeforeSlash + '>' + this.endline(node, options, level);\n      options.state = WriterState.None;\n      this.closeNode(node, options, level);\n      return r;\n    };\n\n    XMLWriterBase.prototype.dtdNotation = function(node, options, level) {\n      var r;\n      this.openNode(node, options, level);\n      options.state = WriterState.OpenTag;\n      r = this.indent(node, options, level) + '<!NOTATION';\n      options.state = WriterState.InsideTag;\n      r += ' ' + node.name;\n      if (node.pubID && node.sysID) {\n        r += ' PUBLIC \"' + node.pubID + '\" \"' + node.sysID + '\"';\n      } else if (node.pubID) {\n        r += ' PUBLIC \"' + node.pubID + '\"';\n      } else if (node.sysID) {\n        r += ' SYSTEM \"' + node.sysID + '\"';\n      }\n      options.state = WriterState.CloseTag;\n      r += options.spaceBeforeSlash + '>' + this.endline(node, options, level);\n      options.state = WriterState.None;\n      this.closeNode(node, options, level);\n      return r;\n    };\n\n    XMLWriterBase.prototype.openNode = function(node, options, level) {};\n\n    XMLWriterBase.prototype.closeNode = function(node, options, level) {};\n\n    XMLWriterBase.prototype.openAttribute = function(att, options, level) {};\n\n    XMLWriterBase.prototype.closeAttribute = function(att, options, level) {};\n\n    return XMLWriterBase;\n\n  })();\n\n}).call(this);\n", "// Generated by CoffeeScript 1.12.7\n(function() {\n  module.exports = {\n    None: 0,\n    OpenTag: 1,\n    InsideTag: 2,\n    CloseTag: 3\n  };\n\n}).call(this);\n", "// Generated by CoffeeScript 1.12.7\n(function() {\n  var NodeType, WriterState, XMLAttribute, XMLCData, XMLComment, XMLDTDAttList, XMLDTDElement, XMLDTDEntity, XMLDTDNotation, XMLDeclaration, XMLDocType, XMLDocument, XMLDocumentCB, XMLElement, XMLProcessingInstruction, XMLRaw, XMLStringWriter, XMLStringifier, XMLText, getValue, isFunction, isObject, isPlainObject, ref,\n    hasProp = {}.hasOwnProperty;\n\n  ref = require('./Utility'), isObject = ref.isObject, isFunction = ref.isFunction, isPlainObject = ref.isPlainObject, getValue = ref.getValue;\n\n  NodeType = require('./NodeType');\n\n  XMLDocument = require('./XMLDocument');\n\n  XMLElement = require('./XMLElement');\n\n  XMLCData = require('./XMLCData');\n\n  XMLComment = require('./XMLComment');\n\n  XMLRaw = require('./XMLRaw');\n\n  XMLText = require('./XMLText');\n\n  XMLProcessingInstruction = require('./XMLProcessingInstruction');\n\n  XMLDeclaration = require('./XMLDeclaration');\n\n  XMLDocType = require('./XMLDocType');\n\n  XMLDTDAttList = require('./XMLDTDAttList');\n\n  XMLDTDEntity = require('./XMLDTDEntity');\n\n  XMLDTDElement = require('./XMLDTDElement');\n\n  XMLDTDNotation = require('./XMLDTDNotation');\n\n  XMLAttribute = require('./XMLAttribute');\n\n  XMLStringifier = require('./XMLStringifier');\n\n  XMLStringWriter = require('./XMLStringWriter');\n\n  WriterState = require('./WriterState');\n\n  module.exports = XMLDocumentCB = (function() {\n    function XMLDocumentCB(options, onData, onEnd) {\n      var writerOptions;\n      this.name = \"?xml\";\n      this.type = NodeType.Document;\n      options || (options = {});\n      writerOptions = {};\n      if (!options.writer) {\n        options.writer = new XMLStringWriter();\n      } else if (isPlainObject(options.writer)) {\n        writerOptions = options.writer;\n        options.writer = new XMLStringWriter();\n      }\n      this.options = options;\n      this.writer = options.writer;\n      this.writerOptions = this.writer.filterOptions(writerOptions);\n      this.stringify = new XMLStringifier(options);\n      this.onDataCallback = onData || function() {};\n      this.onEndCallback = onEnd || function() {};\n      this.currentNode = null;\n      this.currentLevel = -1;\n      this.openTags = {};\n      this.documentStarted = false;\n      this.documentCompleted = false;\n      this.root = null;\n    }\n\n    XMLDocumentCB.prototype.createChildNode = function(node) {\n      var att, attName, attributes, child, i, len, ref1, ref2;\n      switch (node.type) {\n        case NodeType.CData:\n          this.cdata(node.value);\n          break;\n        case NodeType.Comment:\n          this.comment(node.value);\n          break;\n        case NodeType.Element:\n          attributes = {};\n          ref1 = node.attribs;\n          for (attName in ref1) {\n            if (!hasProp.call(ref1, attName)) continue;\n            att = ref1[attName];\n            attributes[attName] = att.value;\n          }\n          this.node(node.name, attributes);\n          break;\n        case NodeType.Dummy:\n          this.dummy();\n          break;\n        case NodeType.Raw:\n          this.raw(node.value);\n          break;\n        case NodeType.Text:\n          this.text(node.value);\n          break;\n        case NodeType.ProcessingInstruction:\n          this.instruction(node.target, node.value);\n          break;\n        default:\n          throw new Error(\"This XML node type is not supported in a JS object: \" + node.constructor.name);\n      }\n      ref2 = node.children;\n      for (i = 0, len = ref2.length; i < len; i++) {\n        child = ref2[i];\n        this.createChildNode(child);\n        if (child.type === NodeType.Element) {\n          this.up();\n        }\n      }\n      return this;\n    };\n\n    XMLDocumentCB.prototype.dummy = function() {\n      return this;\n    };\n\n    XMLDocumentCB.prototype.node = function(name, attributes, text) {\n      var ref1;\n      if (name == null) {\n        throw new Error(\"Missing node name.\");\n      }\n      if (this.root && this.currentLevel === -1) {\n        throw new Error(\"Document can only have one root node. \" + this.debugInfo(name));\n      }\n      this.openCurrent();\n      name = getValue(name);\n      if (attributes == null) {\n        attributes = {};\n      }\n      attributes = getValue(attributes);\n      if (!isObject(attributes)) {\n        ref1 = [attributes, text], text = ref1[0], attributes = ref1[1];\n      }\n      this.currentNode = new XMLElement(this, name, attributes);\n      this.currentNode.children = false;\n      this.currentLevel++;\n      this.openTags[this.currentLevel] = this.currentNode;\n      if (text != null) {\n        this.text(text);\n      }\n      return this;\n    };\n\n    XMLDocumentCB.prototype.element = function(name, attributes, text) {\n      var child, i, len, oldValidationFlag, ref1, root;\n      if (this.currentNode && this.currentNode.type === NodeType.DocType) {\n        this.dtdElement.apply(this, arguments);\n      } else {\n        if (Array.isArray(name) || isObject(name) || isFunction(name)) {\n          oldValidationFlag = this.options.noValidation;\n          this.options.noValidation = true;\n          root = new XMLDocument(this.options).element('TEMP_ROOT');\n          root.element(name);\n          this.options.noValidation = oldValidationFlag;\n          ref1 = root.children;\n          for (i = 0, len = ref1.length; i < len; i++) {\n            child = ref1[i];\n            this.createChildNode(child);\n            if (child.type === NodeType.Element) {\n              this.up();\n            }\n          }\n        } else {\n          this.node(name, attributes, text);\n        }\n      }\n      return this;\n    };\n\n    XMLDocumentCB.prototype.attribute = function(name, value) {\n      var attName, attValue;\n      if (!this.currentNode || this.currentNode.children) {\n        throw new Error(\"att() can only be used immediately after an ele() call in callback mode. \" + this.debugInfo(name));\n      }\n      if (name != null) {\n        name = getValue(name);\n      }\n      if (isObject(name)) {\n        for (attName in name) {\n          if (!hasProp.call(name, attName)) continue;\n          attValue = name[attName];\n          this.attribute(attName, attValue);\n        }\n      } else {\n        if (isFunction(value)) {\n          value = value.apply();\n        }\n        if (this.options.keepNullAttributes && (value == null)) {\n          this.currentNode.attribs[name] = new XMLAttribute(this, name, \"\");\n        } else if (value != null) {\n          this.currentNode.attribs[name] = new XMLAttribute(this, name, value);\n        }\n      }\n      return this;\n    };\n\n    XMLDocumentCB.prototype.text = function(value) {\n      var node;\n      this.openCurrent();\n      node = new XMLText(this, value);\n      this.onData(this.writer.text(node, this.writerOptions, this.currentLevel + 1), this.currentLevel + 1);\n      return this;\n    };\n\n    XMLDocumentCB.prototype.cdata = function(value) {\n      var node;\n      this.openCurrent();\n      node = new XMLCData(this, value);\n      this.onData(this.writer.cdata(node, this.writerOptions, this.currentLevel + 1), this.currentLevel + 1);\n      return this;\n    };\n\n    XMLDocumentCB.prototype.comment = function(value) {\n      var node;\n      this.openCurrent();\n      node = new XMLComment(this, value);\n      this.onData(this.writer.comment(node, this.writerOptions, this.currentLevel + 1), this.currentLevel + 1);\n      return this;\n    };\n\n    XMLDocumentCB.prototype.raw = function(value) {\n      var node;\n      this.openCurrent();\n      node = new XMLRaw(this, value);\n      this.onData(this.writer.raw(node, this.writerOptions, this.currentLevel + 1), this.currentLevel + 1);\n      return this;\n    };\n\n    XMLDocumentCB.prototype.instruction = function(target, value) {\n      var i, insTarget, insValue, len, node;\n      this.openCurrent();\n      if (target != null) {\n        target = getValue(target);\n      }\n      if (value != null) {\n        value = getValue(value);\n      }\n      if (Array.isArray(target)) {\n        for (i = 0, len = target.length; i < len; i++) {\n          insTarget = target[i];\n          this.instruction(insTarget);\n        }\n      } else if (isObject(target)) {\n        for (insTarget in target) {\n          if (!hasProp.call(target, insTarget)) continue;\n          insValue = target[insTarget];\n          this.instruction(insTarget, insValue);\n        }\n      } else {\n        if (isFunction(value)) {\n          value = value.apply();\n        }\n        node = new XMLProcessingInstruction(this, target, value);\n        this.onData(this.writer.processingInstruction(node, this.writerOptions, this.currentLevel + 1), this.currentLevel + 1);\n      }\n      return this;\n    };\n\n    XMLDocumentCB.prototype.declaration = function(version, encoding, standalone) {\n      var node;\n      this.openCurrent();\n      if (this.documentStarted) {\n        throw new Error(\"declaration() must be the first node.\");\n      }\n      node = new XMLDeclaration(this, version, encoding, standalone);\n      this.onData(this.writer.declaration(node, this.writerOptions, this.currentLevel + 1), this.currentLevel + 1);\n      return this;\n    };\n\n    XMLDocumentCB.prototype.doctype = function(root, pubID, sysID) {\n      this.openCurrent();\n      if (root == null) {\n        throw new Error(\"Missing root node name.\");\n      }\n      if (this.root) {\n        throw new Error(\"dtd() must come before the root node.\");\n      }\n      this.currentNode = new XMLDocType(this, pubID, sysID);\n      this.currentNode.rootNodeName = root;\n      this.currentNode.children = false;\n      this.currentLevel++;\n      this.openTags[this.currentLevel] = this.currentNode;\n      return this;\n    };\n\n    XMLDocumentCB.prototype.dtdElement = function(name, value) {\n      var node;\n      this.openCurrent();\n      node = new XMLDTDElement(this, name, value);\n      this.onData(this.writer.dtdElement(node, this.writerOptions, this.currentLevel + 1), this.currentLevel + 1);\n      return this;\n    };\n\n    XMLDocumentCB.prototype.attList = function(elementName, attributeName, attributeType, defaultValueType, defaultValue) {\n      var node;\n      this.openCurrent();\n      node = new XMLDTDAttList(this, elementName, attributeName, attributeType, defaultValueType, defaultValue);\n      this.onData(this.writer.dtdAttList(node, this.writerOptions, this.currentLevel + 1), this.currentLevel + 1);\n      return this;\n    };\n\n    XMLDocumentCB.prototype.entity = function(name, value) {\n      var node;\n      this.openCurrent();\n      node = new XMLDTDEntity(this, false, name, value);\n      this.onData(this.writer.dtdEntity(node, this.writerOptions, this.currentLevel + 1), this.currentLevel + 1);\n      return this;\n    };\n\n    XMLDocumentCB.prototype.pEntity = function(name, value) {\n      var node;\n      this.openCurrent();\n      node = new XMLDTDEntity(this, true, name, value);\n      this.onData(this.writer.dtdEntity(node, this.writerOptions, this.currentLevel + 1), this.currentLevel + 1);\n      return this;\n    };\n\n    XMLDocumentCB.prototype.notation = function(name, value) {\n      var node;\n      this.openCurrent();\n      node = new XMLDTDNotation(this, name, value);\n      this.onData(this.writer.dtdNotation(node, this.writerOptions, this.currentLevel + 1), this.currentLevel + 1);\n      return this;\n    };\n\n    XMLDocumentCB.prototype.up = function() {\n      if (this.currentLevel < 0) {\n        throw new Error(\"The document node has no parent.\");\n      }\n      if (this.currentNode) {\n        if (this.currentNode.children) {\n          this.closeNode(this.currentNode);\n        } else {\n          this.openNode(this.currentNode);\n        }\n        this.currentNode = null;\n      } else {\n        this.closeNode(this.openTags[this.currentLevel]);\n      }\n      delete this.openTags[this.currentLevel];\n      this.currentLevel--;\n      return this;\n    };\n\n    XMLDocumentCB.prototype.end = function() {\n      while (this.currentLevel >= 0) {\n        this.up();\n      }\n      return this.onEnd();\n    };\n\n    XMLDocumentCB.prototype.openCurrent = function() {\n      if (this.currentNode) {\n        this.currentNode.children = true;\n        return this.openNode(this.currentNode);\n      }\n    };\n\n    XMLDocumentCB.prototype.openNode = function(node) {\n      var att, chunk, name, ref1;\n      if (!node.isOpen) {\n        if (!this.root && this.currentLevel === 0 && node.type === NodeType.Element) {\n          this.root = node;\n        }\n        chunk = '';\n        if (node.type === NodeType.Element) {\n          this.writerOptions.state = WriterState.OpenTag;\n          chunk = this.writer.indent(node, this.writerOptions, this.currentLevel) + '<' + node.name;\n          ref1 = node.attribs;\n          for (name in ref1) {\n            if (!hasProp.call(ref1, name)) continue;\n            att = ref1[name];\n            chunk += this.writer.attribute(att, this.writerOptions, this.currentLevel);\n          }\n          chunk += (node.children ? '>' : '/>') + this.writer.endline(node, this.writerOptions, this.currentLevel);\n          this.writerOptions.state = WriterState.InsideTag;\n        } else {\n          this.writerOptions.state = WriterState.OpenTag;\n          chunk = this.writer.indent(node, this.writerOptions, this.currentLevel) + '<!DOCTYPE ' + node.rootNodeName;\n          if (node.pubID && node.sysID) {\n            chunk += ' PUBLIC \"' + node.pubID + '\" \"' + node.sysID + '\"';\n          } else if (node.sysID) {\n            chunk += ' SYSTEM \"' + node.sysID + '\"';\n          }\n          if (node.children) {\n            chunk += ' [';\n            this.writerOptions.state = WriterState.InsideTag;\n          } else {\n            this.writerOptions.state = WriterState.CloseTag;\n            chunk += '>';\n          }\n          chunk += this.writer.endline(node, this.writerOptions, this.currentLevel);\n        }\n        this.onData(chunk, this.currentLevel);\n        return node.isOpen = true;\n      }\n    };\n\n    XMLDocumentCB.prototype.closeNode = function(node) {\n      var chunk;\n      if (!node.isClosed) {\n        chunk = '';\n        this.writerOptions.state = WriterState.CloseTag;\n        if (node.type === NodeType.Element) {\n          chunk = this.writer.indent(node, this.writerOptions, this.currentLevel) + '</' + node.name + '>' + this.writer.endline(node, this.writerOptions, this.currentLevel);\n        } else {\n          chunk = this.writer.indent(node, this.writerOptions, this.currentLevel) + ']>' + this.writer.endline(node, this.writerOptions, this.currentLevel);\n        }\n        this.writerOptions.state = WriterState.None;\n        this.onData(chunk, this.currentLevel);\n        return node.isClosed = true;\n      }\n    };\n\n    XMLDocumentCB.prototype.onData = function(chunk, level) {\n      this.documentStarted = true;\n      return this.onDataCallback(chunk, level + 1);\n    };\n\n    XMLDocumentCB.prototype.onEnd = function() {\n      this.documentCompleted = true;\n      return this.onEndCallback();\n    };\n\n    XMLDocumentCB.prototype.debugInfo = function(name) {\n      if (name == null) {\n        return \"\";\n      } else {\n        return \"node: <\" + name + \">\";\n      }\n    };\n\n    XMLDocumentCB.prototype.ele = function() {\n      return this.element.apply(this, arguments);\n    };\n\n    XMLDocumentCB.prototype.nod = function(name, attributes, text) {\n      return this.node(name, attributes, text);\n    };\n\n    XMLDocumentCB.prototype.txt = function(value) {\n      return this.text(value);\n    };\n\n    XMLDocumentCB.prototype.dat = function(value) {\n      return this.cdata(value);\n    };\n\n    XMLDocumentCB.prototype.com = function(value) {\n      return this.comment(value);\n    };\n\n    XMLDocumentCB.prototype.ins = function(target, value) {\n      return this.instruction(target, value);\n    };\n\n    XMLDocumentCB.prototype.dec = function(version, encoding, standalone) {\n      return this.declaration(version, encoding, standalone);\n    };\n\n    XMLDocumentCB.prototype.dtd = function(root, pubID, sysID) {\n      return this.doctype(root, pubID, sysID);\n    };\n\n    XMLDocumentCB.prototype.e = function(name, attributes, text) {\n      return this.element(name, attributes, text);\n    };\n\n    XMLDocumentCB.prototype.n = function(name, attributes, text) {\n      return this.node(name, attributes, text);\n    };\n\n    XMLDocumentCB.prototype.t = function(value) {\n      return this.text(value);\n    };\n\n    XMLDocumentCB.prototype.d = function(value) {\n      return this.cdata(value);\n    };\n\n    XMLDocumentCB.prototype.c = function(value) {\n      return this.comment(value);\n    };\n\n    XMLDocumentCB.prototype.r = function(value) {\n      return this.raw(value);\n    };\n\n    XMLDocumentCB.prototype.i = function(target, value) {\n      return this.instruction(target, value);\n    };\n\n    XMLDocumentCB.prototype.att = function() {\n      if (this.currentNode && this.currentNode.type === NodeType.DocType) {\n        return this.attList.apply(this, arguments);\n      } else {\n        return this.attribute.apply(this, arguments);\n      }\n    };\n\n    XMLDocumentCB.prototype.a = function() {\n      if (this.currentNode && this.currentNode.type === NodeType.DocType) {\n        return this.attList.apply(this, arguments);\n      } else {\n        return this.attribute.apply(this, arguments);\n      }\n    };\n\n    XMLDocumentCB.prototype.ent = function(name, value) {\n      return this.entity(name, value);\n    };\n\n    XMLDocumentCB.prototype.pent = function(name, value) {\n      return this.pEntity(name, value);\n    };\n\n    XMLDocumentCB.prototype.not = function(name, value) {\n      return this.notation(name, value);\n    };\n\n    return XMLDocumentCB;\n\n  })();\n\n}).call(this);\n", "// Generated by CoffeeScript 1.12.7\n(function() {\n  var NodeType, WriterState, XMLStreamWriter, XMLWriterBase,\n    extend = function(child, parent) { for (var key in parent) { if (hasProp.call(parent, key)) child[key] = parent[key]; } function ctor() { this.constructor = child; } ctor.prototype = parent.prototype; child.prototype = new ctor(); child.__super__ = parent.prototype; return child; },\n    hasProp = {}.hasOwnProperty;\n\n  NodeType = require('./NodeType');\n\n  XMLWriterBase = require('./XMLWriterBase');\n\n  WriterState = require('./WriterState');\n\n  module.exports = XMLStreamWriter = (function(superClass) {\n    extend(XMLStreamWriter, superClass);\n\n    function XMLStreamWriter(stream, options) {\n      this.stream = stream;\n      XMLStreamWriter.__super__.constructor.call(this, options);\n    }\n\n    XMLStreamWriter.prototype.endline = function(node, options, level) {\n      if (node.isLastRootNode && options.state === WriterState.CloseTag) {\n        return '';\n      } else {\n        return XMLStreamWriter.__super__.endline.call(this, node, options, level);\n      }\n    };\n\n    XMLStreamWriter.prototype.document = function(doc, options) {\n      var child, i, j, k, len, len1, ref, ref1, results;\n      ref = doc.children;\n      for (i = j = 0, len = ref.length; j < len; i = ++j) {\n        child = ref[i];\n        child.isLastRootNode = i === doc.children.length - 1;\n      }\n      options = this.filterOptions(options);\n      ref1 = doc.children;\n      results = [];\n      for (k = 0, len1 = ref1.length; k < len1; k++) {\n        child = ref1[k];\n        results.push(this.writeChildNode(child, options, 0));\n      }\n      return results;\n    };\n\n    XMLStreamWriter.prototype.attribute = function(att, options, level) {\n      return this.stream.write(XMLStreamWriter.__super__.attribute.call(this, att, options, level));\n    };\n\n    XMLStreamWriter.prototype.cdata = function(node, options, level) {\n      return this.stream.write(XMLStreamWriter.__super__.cdata.call(this, node, options, level));\n    };\n\n    XMLStreamWriter.prototype.comment = function(node, options, level) {\n      return this.stream.write(XMLStreamWriter.__super__.comment.call(this, node, options, level));\n    };\n\n    XMLStreamWriter.prototype.declaration = function(node, options, level) {\n      return this.stream.write(XMLStreamWriter.__super__.declaration.call(this, node, options, level));\n    };\n\n    XMLStreamWriter.prototype.docType = function(node, options, level) {\n      var child, j, len, ref;\n      level || (level = 0);\n      this.openNode(node, options, level);\n      options.state = WriterState.OpenTag;\n      this.stream.write(this.indent(node, options, level));\n      this.stream.write('<!DOCTYPE ' + node.root().name);\n      if (node.pubID && node.sysID) {\n        this.stream.write(' PUBLIC \"' + node.pubID + '\" \"' + node.sysID + '\"');\n      } else if (node.sysID) {\n        this.stream.write(' SYSTEM \"' + node.sysID + '\"');\n      }\n      if (node.children.length > 0) {\n        this.stream.write(' [');\n        this.stream.write(this.endline(node, options, level));\n        options.state = WriterState.InsideTag;\n        ref = node.children;\n        for (j = 0, len = ref.length; j < len; j++) {\n          child = ref[j];\n          this.writeChildNode(child, options, level + 1);\n        }\n        options.state = WriterState.CloseTag;\n        this.stream.write(']');\n      }\n      options.state = WriterState.CloseTag;\n      this.stream.write(options.spaceBeforeSlash + '>');\n      this.stream.write(this.endline(node, options, level));\n      options.state = WriterState.None;\n      return this.closeNode(node, options, level);\n    };\n\n    XMLStreamWriter.prototype.element = function(node, options, level) {\n      var att, child, childNodeCount, firstChildNode, j, len, name, prettySuppressed, ref, ref1;\n      level || (level = 0);\n      this.openNode(node, options, level);\n      options.state = WriterState.OpenTag;\n      this.stream.write(this.indent(node, options, level) + '<' + node.name);\n      ref = node.attribs;\n      for (name in ref) {\n        if (!hasProp.call(ref, name)) continue;\n        att = ref[name];\n        this.attribute(att, options, level);\n      }\n      childNodeCount = node.children.length;\n      firstChildNode = childNodeCount === 0 ? null : node.children[0];\n      if (childNodeCount === 0 || node.children.every(function(e) {\n        return (e.type === NodeType.Text || e.type === NodeType.Raw) && e.value === '';\n      })) {\n        if (options.allowEmpty) {\n          this.stream.write('>');\n          options.state = WriterState.CloseTag;\n          this.stream.write('</' + node.name + '>');\n        } else {\n          options.state = WriterState.CloseTag;\n          this.stream.write(options.spaceBeforeSlash + '/>');\n        }\n      } else if (options.pretty && childNodeCount === 1 && (firstChildNode.type === NodeType.Text || firstChildNode.type === NodeType.Raw) && (firstChildNode.value != null)) {\n        this.stream.write('>');\n        options.state = WriterState.InsideTag;\n        options.suppressPrettyCount++;\n        prettySuppressed = true;\n        this.writeChildNode(firstChildNode, options, level + 1);\n        options.suppressPrettyCount--;\n        prettySuppressed = false;\n        options.state = WriterState.CloseTag;\n        this.stream.write('</' + node.name + '>');\n      } else {\n        this.stream.write('>' + this.endline(node, options, level));\n        options.state = WriterState.InsideTag;\n        ref1 = node.children;\n        for (j = 0, len = ref1.length; j < len; j++) {\n          child = ref1[j];\n          this.writeChildNode(child, options, level + 1);\n        }\n        options.state = WriterState.CloseTag;\n        this.stream.write(this.indent(node, options, level) + '</' + node.name + '>');\n      }\n      this.stream.write(this.endline(node, options, level));\n      options.state = WriterState.None;\n      return this.closeNode(node, options, level);\n    };\n\n    XMLStreamWriter.prototype.processingInstruction = function(node, options, level) {\n      return this.stream.write(XMLStreamWriter.__super__.processingInstruction.call(this, node, options, level));\n    };\n\n    XMLStreamWriter.prototype.raw = function(node, options, level) {\n      return this.stream.write(XMLStreamWriter.__super__.raw.call(this, node, options, level));\n    };\n\n    XMLStreamWriter.prototype.text = function(node, options, level) {\n      return this.stream.write(XMLStreamWriter.__super__.text.call(this, node, options, level));\n    };\n\n    XMLStreamWriter.prototype.dtdAttList = function(node, options, level) {\n      return this.stream.write(XMLStreamWriter.__super__.dtdAttList.call(this, node, options, level));\n    };\n\n    XMLStreamWriter.prototype.dtdElement = function(node, options, level) {\n      return this.stream.write(XMLStreamWriter.__super__.dtdElement.call(this, node, options, level));\n    };\n\n    XMLStreamWriter.prototype.dtdEntity = function(node, options, level) {\n      return this.stream.write(XMLStreamWriter.__super__.dtdEntity.call(this, node, options, level));\n    };\n\n    XMLStreamWriter.prototype.dtdNotation = function(node, options, level) {\n      return this.stream.write(XMLStreamWriter.__super__.dtdNotation.call(this, node, options, level));\n    };\n\n    return XMLStreamWriter;\n\n  })(XMLWriterBase);\n\n}).call(this);\n"]}