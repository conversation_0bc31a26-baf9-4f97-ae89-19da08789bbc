{"version": 3, "sources": ["index.js", "src/storage/index.js", "src/utils/httpRequest.js", "src/utils/auth.js", "src/utils/tracing.js", "src/utils/utils.js", "package.json", "src/utils/getWxCloudApiToken.js", "src/utils/request-timings-measurer.js", "src/const/symbol.js", "src/functions/index.js", "src/auth/index.js", "src/wx/index.js", "src/utils/dbRequest.js", "src/log/index.js", "src/utils/extRequest.js"], "names": [], "mappings": ";;;;;;;AAAA;AACA;AACA;AACA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA,ACHA;AFOA,ACHA,ACHA;AFOA,ACHA,ACHA;AFOA,ACHA,AENA,ADGA;AFOA,ACHA,AENA,ADGA;AFOA,ACHA,AENA,ADGA;AFOA,ACHA,AENA,ADGA,AENA;AJaA,ACHA,AENA,ADGA,AENA;AJaA,ACHA,AENA,ADGA,AENA;AJaA,ACHA,AENA,ADGA,AENA,ACHA;ALgBA,ACHA,AENA,ADGA,AENA,ACHA;ALgBA,ACHA,AENA,ADGA,AENA,ACHA;ALgBA,AMlBA,ALeA,AENA,ADGA,AENA,ACHA;ALgBA,AMlBA,ALeA,AENA,ADGA,AENA,ACHA;ALgBA,AMlBA,ALeA,AENA,ADGA,AENA,ACHA;ALgBA,AMlBA,ALeA,AENA,AIZA,ALeA,AENA,ACHA;ALgBA,AMlBA,ALeA,AENA,AIZA,ALeA,AENA,ACHA;ALgBA,AMlBA,ALeA,AENA,AIZA,ALeA,AENA,ACHA;ALgBA,AMlBA,ALeA,AENA,AIZA,ALeA,AMlBA,AJYA,ACHA;ALgBA,AMlBA,ALeA,AENA,AIZA,ALeA,AMlBA,AJYA,ACHA;ALgBA,AMlBA,ALeA,AENA,AIZA,ALeA,AMlBA,AJYA,ACHA;ALgBA,AMlBA,AGTA,ARwBA,AENA,AIZA,ALeA,AMlBA,AJYA,ACHA;ALgBA,AMlBA,AGTA,ARwBA,AENA,AIZA,ALeA,AMlBA,AJYA,ACHA;ALgBA,AMlBA,ALeA,AENA,AIZA,ALeA,AMlBA,AJYA,ACHA;ALgBA,AMlBA,AIZA,AT2BA,AENA,AIZA,ALeA,AMlBA,AJYA,ACHA;ALgBA,AMlBA,AIZA,AT2BA,AENA,AIZA,ALeA,AMlBA,AJYA,ACHA;ALgBA,AMlBA,AIZA,AT2BA,AENA,AIZA,ALeA,AMlBA,AJYA,ACHA;ALgBA,AMlBA,AKfA,ADGA,AT2BA,AENA,ADGA,AMlBA,AJYA,ACHA;ALgBA,AMlBA,AKfA,ADGA,AT2BA,AENA,ADGA,AMlBA,AJYA,ACHA;ALgBA,AMlBA,AKfA,ADGA,AT2BA,AENA,ADGA,AMlBA,AJYA,ACHA;ALgBA,AMlBA,AKfA,ADGA,AT2BA,AENA,ADGA,AMlBA,AJYA,ACHA,AOrBA;AZqCA,AMlBA,AKfA,ADGA,AT2BA,AENA,ADGA,AMlBA,AJYA,ACHA,AOrBA;AZqCA,AMlBA,AKfA,ADGA,AT2BA,AENA,ADGA,AMlBA,AHSA,AOrBA;AZqCA,AMlBA,AKfA,ADGA,AT2BA,AENA,AU9BA,AXiCA,AMlBA,AHSA,AOrBA;AZqCA,AMlBA,AKfA,ADGA,AT2BA,AENA,AU9BA,AXiCA,AMlBA,AHSA,AOrBA;AZqCA,AMlBA,AKfA,ADGA,AT2BA,AENA,AU9BA,AXiCA,AMlBA,AHSA,AOrBA;AZqCA,AMlBA,AKfA,ADGA,AIZA,AbuCA,AENA,AU9BA,AXiCA,AMlBA,AHSA,AOrBA;AZqCA,AMlBA,AKfA,ADGA,AIZA,AbuCA,AENA,AU9BA,AXiCA,AMlBA,AHSA,AOrBA;AZqCA,AMlBA,AKfA,ADGA,AIZA,AbuCA,AENA,AU9BA,AXiCA,AMlBA,AHSA,AOrBA;AZqCA,AMlBA,AKfA,ADGA,AIZA,AbuCA,AENA,AU9BA,AENA,AbuCA,AMlBA,AHSA,AOrBA;AZqCA,AMlBA,AKfA,ADGA,AIZA,AbuCA,AENA,AU9BA,AENA,AbuCA,AMlBA,AHSA,AOrBA;AZqCA,AMlBA,AKfA,ADGA,AIZA,AbuCA,AENA,AU9BA,AENA,AbuCA,AMlBA,AHSA,AOrBA;AZqCA,AMlBA,AKfA,ADGA,AIZA,AbuCA,AENA,AU9BA,AENA,AbuCA,AMlBA,AHSA,AOrBA;AZqCA,AMlBA,AKfA,ADGA,AIZA,AbuCA,AENA,AU9BA,AENA,AbuCA,AMlBA,AHSA,AOrBA;AZqCA,AMlBA,AKfA,ADGA,AIZA,AbuCA,AENA,AU9BA,AENA,AbuCA,AMlBA,AHSA,AOrBA;AZqCA,AMlBA,AKfA,ADGA,AIZA,AbuCA,AENA,AU9BA,AENA,AbuCA,AMlBA,AHSA,AOrBA;AZqCA,AMlBA,AKfA,ADGA,AIZA,AbuCA,AENA,AU9BA,AENA,AbuCA,AMlBA,AHSA,AOrBA;AZqCA,AMlBA,AKfA,ADGA,AIZA,AbuCA,AENA,AU9BA,AENA,AbuCA,AMlBA,AHSA,AOrBA;AZqCA,AMlBA,AKfA,ADGA,AIZA,AbuCA,AENA,AU9BA,AENA,AbuCA,AMlBA,AHSA,AOrBA;AZqCA,AMlBA,AKfA,ADGA,AIZA,AbuCA,AENA,AU9BA,AENA,AbuCA,AMlBA,AHSA,AOrBA;AZqCA,AMlBA,AKfA,ADGA,AIZA,AbuCA,AENA,AU9BA,AENA,AbuCA,AMlBA,AHSA,AOrBA;AZqCA,AMlBA,AKfA,ADGA,AIZA,AbuCA,AENA,AU9BA,AENA,AbuCA,AMlBA,AHSA,AOrBA;AZqCA,AMlBA,AKfA,ADGA,AIZA,AbuCA,AENA,AU9BA,AENA,AbuCA,AMlBA,AHSA,AOrBA;AZqCA,AMlBA,AKfA,ADGA,AIZA,AbuCA,AENA,AU9BA,AENA,AbuCA,AMlBA,AHSA,AOrBA;AZqCA,AMlBA,AKfA,ADGA,AIZA,AbuCA,AENA,AU9BA,AENA,AbuCA,AMlBA,AHSA,AOrBA;AZqCA,AMlBA,AKfA,ADGA,AIZA,AbuCA,AENA,AU9BA,AENA,AbuCA,AMlBA,AHSA,AOrBA;AZqCA,AMlBA,AKfA,ADGA,AIZA,AbuCA,AENA,AU9BA,AENA,AbuCA,AMlBA,AHSA,AOrBA;AZqCA,AMlBA,AKfA,ADGA,AIZA,AbuCA,AENA,AU9BA,AENA,AbuCA,AMlBA,AHSA,AOrBA;AZqCA,AMlBA,AKfA,ADGA,AIZA,AbuCA,AENA,AU9BA,AENA,AbuCA,AMlBA,AHSA,AOrBA;AZqCA,AMlBA,AKfA,ADGA,AIZA,AbuCA,AENA,AU9BA,AENA,AbuCA,AMlBA,AHSA,AOrBA;AZqCA,AMlBA,AKfA,ADGA,AIZA,AbuCA,AENA,AU9BA,AENA,AbuCA,AMlBA,AHSA,AOrBA;AZqCA,AMlBA,AKfA,ADGA,AIZA,AbuCA,AENA,AU9BA,AENA,AbuCA,AMlBA,AHSA,AOrBA;AZqCA,AMlBA,AKfA,ADGA,AIZA,AbuCA,AENA,AU9BA,AENA,AbuCA,AMlBA,AHSA,AOrBA;AZqCA,AMlBA,AKfA,ADGA,AIZA,AbuCA,AENA,AU9BA,AENA,AbuCA,AMlBA,AHSA,AOrBA;AZqCA,AMlBA,AKfA,ADGA,AIZA,AbuCA,AENA,AU9BA,AENA,AbuCA,AMlBA,AHSA,AOrBA;AZqCA,AMlBA,AKfA,ADGA,AIZA,AbuCA,AENA,AU9BA,AENA,AbuCA,AMlBA,AHSA,AOrBA;AZqCA,AMlBA,AKfA,ADGA,AIZA,AbuCA,AENA,AU9BA,AENA,AbuCA,AMlBA,AHSA,AOrBA;AZqCA,AMlBA,AKfA,ADGA,AIZA,AbuCA,AENA,AU9BA,AENA,AbuCA,AMlBA,AHSA,AOrBA;AZqCA,AMlBA,AKfA,ADGA,AIZA,AbuCA,AENA,AU9BA,AENA,AbuCA,AMlBA,AHSA,AOrBA;AZqCA,AMlBA,AKfA,ADGA,AIZA,AbuCA,AENA,AU9BA,AENA,AbuCA,AMlBA,AIZA;AZqCA,AMlBA,AKfA,ADGA,AIZA,AbuCA,AENA,AU9BA,AENA,AbuCA,AMlBA,AIZA;AZqCA,AMlBA,AKfA,ADGA,AIZA,AbuCA,AENA,AU9BA,AENA,AbuCA,AMlBA,AIZA;AZqCA,AMlBA,AKfA,ADGA,AIZA,AbuCA,AENA,AU9BA,AENA,AbuCA,AMlBA,AIZA;AZqCA,AMlBA,AKfA,ADGA,AIZA,AbuCA,AENA,AU9BA,AENA,AbuCA,AMlBA,AIZA;AZqCA,AMlBA,AKfA,ADGA,AIZA,AbuCA,AENA,AU9BA,AENA,AbuCA,AMlBA,AIZA;AZqCA,AMlBA,AKfA,ADGA,AIZA,AbuCA,AENA,AU9BA,AENA,AbuCA,AMlBA,AIZA;AZqCA,AMlBA,AKfA,ADGA,AIZA,AbuCA,AENA,AU9BA,AENA,AbuCA,AMlBA,AIZA;AZqCA,AMlBA,AKfA,ADGA,AIZA,AbuCA,AENA,AYpCA,AbuCA,AMlBA,AIZA;AZqCA,AMlBA,AKfA,ADGA,AIZA,AbuCA,AENA,AYpCA,AbuCA,AMlBA,AIZA;AZqCA,AMlBA,AKfA,ADGA,AIZA,AbuCA,AENA,AYpCA,AbuCA,AMlBA,AIZA;AZqCA,AMlBA,AKfA,ADGA,AIZA,AbuCA,AENA,AYpCA,AbuCA,AMlBA,AIZA;AZqCA,AMlBA,AKfA,ADGA,AIZA,AbuCA,AENA,AYpCA,AbuCA,AMlBA,AIZA;AZqCA,AMlBA,AKfA,AGTA,AbuCA,AENA,AYpCA,AbuCA,AMlBA,AIZA;AZqCA,AMlBA,AKfA,AGTA,AbuCA,AENA,AYpCA,AbuCA,AMlBA,AIZA;AZqCA,AMlBA,AKfA,AGTA,AbuCA,AENA,AYpCA,AbuCA,AMlBA,AIZA;AZqCA,AMlBA,AKfA,AGTA,AbuCA,AENA,AYpCA,AbuCA,AMlBA,AIZA;AZqCA,AMlBA,AKfA,AGTA,AbuCA,AENA,AYpCA,AbuCA,AMlBA,AIZA;AZqCA,AMlBA,AKfA,AGTA,AbuCA,AENA,AYpCA,AbuCA,AMlBA,AIZA;AZqCA,AWjCA,AGTA,AbuCA,AENA,AYpCA,AbuCA,AMlBA,AIZA;AZqCA,AWjCA,AGTA,AbuCA,AENA,AYpCA,AbuCA,AMlBA,AIZA;AZqCA,AWjCA,AGTA,AbuCA,AENA,AYpCA,AbuCA,AMlBA,AIZA;AZqCA,AWjCA,AGTA,AbuCA,AENA,AYpCA,AbuCA,AMlBA,AIZA;AZqCA,AWjCA,AGTA,AbuCA,AENA,AYpCA,AbuCA,AMlBA,AIZA;AZqCA,AWjCA,AGTA,AbuCA,AENA,AYpCA,AbuCA,AMlBA,AIZA;AZqCA,AWjCA,AGTA,AbuCA,AENA,AYpCA,AbuCA,AMlBA,AIZA;AZqCA,AWjCA,AGTA,AbuCA,AENA,AYpCA,AbuCA,AMlBA,AIZA;AZqCA,AWjCA,AGTA,AbuCA,AENA,AYpCA,AbuCA,AMlBA,AIZA;AZqCA,AWjCA,AGTA,AbuCA,AENA,AYpCA,AbuCA,AMlBA,AIZA;AZqCA,AWjCA,AGTA,AbuCA,AENA,AYpCA,AbuCA,AMlBA,AIZA;AZqCA,AWjCA,AGTA,AbuCA,AENA,AYpCA,AbuCA,AMlBA,AIZA;AZqCA,AWjCA,AGTA,AbuCA,AENA,AYpCA,AbuCA,AMlBA,AIZA;AZqCA,AWjCA,AGTA,AbuCA,AENA,AYpCA,AbuCA,AMlBA,AIZA;AZqCA,AWjCA,AGTA,AbuCA,AENA,AYpCA,AbuCA,AMlBA,AIZA;AZqCA,AWjCA,AGTA,AbuCA,AENA,AYpCA,AbuCA,AMlBA,AIZA;AZqCA,AWjCA,AGTA,AbuCA,AENA,AYpCA,AbuCA,AMlBA,AIZA;AZqCA,AWjCA,AGTA,AbuCA,AENA,AYpCA,AbuCA,AMlBA,AIZA;AZqCA,AWjCA,AGTA,AbuCA,AENA,AYpCA,AbuCA,AMlBA,AIZA;AZqCA,AWjCA,AGTA,AbuCA,AENA,AYpCA,AbuCA,AMlBA,AIZA;AZqCA,AWjCA,AGTA,AbuCA,AENA,AYpCA,AbuCA,AMlBA,AIZA;AZqCA,AWjCA,AGTA,AbuCA,AENA,AYpCA,AbuCA,AMlBA,AIZA;AZqCA,AWjCA,AGTA,AbuCA,AENA,AYpCA,AbuCA,AMlBA,AIZA;AZqCA,AWjCA,AGTA,AbuCA,AENA,AYpCA,AbuCA,AMlBA,AIZA;AZqCA,AWjCA,AGTA,AbuCA,AENA,AYpCA,AbuCA,AMlBA;ARyBA,AWjCA,AGTA,AbuCA,AENA,AYpCA,AbuCA,AMlBA;ARyBA,AWjCA,AGTA,AbuCA,AENA,AYpCA,AbuCA,AMlBA;ARyBA,Ac1CA,AbuCA,AENA,AYpCA,AbuCA,AMlBA;ARyBA,Ac1CA,AbuCA,AENA,AYpCA,AbuCA,AMlBA;ARyBA,Ac1CA,AbuCA,AENA,AYpCA,AbuCA,AMlBA;ARyBA,Ac1CA,AbuCA,AENA,AYpCA,AbuCA,AMlBA;ARyBA,Ac1CA,AbuCA,AENA,AYpCA,AbuCA,AMlBA;ARyBA,Ac1CA,AbuCA,AENA,AYpCA,AbuCA,AMlBA;ARyBA,Ac1CA,AbuCA,AENA,AYpCA,AbuCA,AMlBA;ARyBA,Ac1CA,AbuCA,AENA,AYpCA,AbuCA,AMlBA;ARyBA,Ac1CA,AbuCA,AENA,ADGA,AMlBA;ARyBA,Ac1CA,AbuCA,AENA,ADGA,AMlBA;ARyBA,Ac1CA,AbuCA,AENA,ADGA,AMlBA;ARyBA,ACHA,AENA,ADGA,AMlBA;ARyBA,ACHA,AENA,ADGA,AMlBA;ARyBA,ACHA,AENA,ADGA,AMlBA;ARyBA,ACHA,AENA,ADGA,AMlBA;ARyBA,ACHA,AENA,ADGA,AMlBA;ARyBA,ACHA,AENA,ADGA,AMlBA;ARyBA,ACHA,AENA,ADGA,AMlBA;ARyBA,ACHA,AENA,ADGA,AMlBA;ARyBA,ACHA,AENA,ADGA,AMlBA;ARyBA,ACHA,AENA,ADGA,AMlBA;ARyBA,ACHA,AENA,ADGA,AMlBA;ARyBA,ACHA,AENA,ADGA,AMlBA;ARyBA,ACHA,AENA,ADGA,AMlBA;ARyBA,ACHA,AENA,ADGA;AFOA,ACHA,AENA,ADGA;AFOA,ACHA,AENA,ADGA;AFOA,ACHA,AENA,ADGA;AFOA,ACHA,AENA,ADGA;AFOA,ACHA,AENA,ADGA;AFOA,ACHA,AENA,ADGA;AFOA,ACHA,AENA,ADGA;AFOA,ACHA,AENA,ADGA;AFOA,ACHA,AENA,ADGA;AFOA,ACHA,AENA,ADGA;AFOA,ACHA,AENA,ADGA;AFOA,ACHA,AENA,ADGA;AFOA,ACHA,AENA,ADGA;AFOA,ACHA,AENA,ADGA;AFOA,ACHA,AENA,ADGA;AFOA,ACHA,AENA,ADGA;AFOA,ACHA,AENA,ADGA;AFOA,ACHA,AENA,ADGA;AFOA,ACHA,AENA,ADGA;AFOA,ACHA,ACHA;AFOA,ACHA,ACHA;AFOA,ACHA,ACHA;AFOA,ACHA,ACHA;AFOA,ACHA,ACHA;AFOA,ACHA,ACHA;AFOA,ACHA,ACHA;AFOA,ACHA,ACHA;AFOA,ACHA,ACHA;AFOA,ACHA,ACHA;AFOA,ACHA,ACHA;AFOA,ACHA,ACHA;AFOA,ACHA,ACHA;AFOA,ACHA,ACHA;AFOA,ACHA,ACHA;AFOA,ACHA,ACHA;AFOA,ACHA,ACHA;AFOA,ACHA,ACHA;AFOA,ACHA,ACHA;AFOA,ACHA,ACHA;AFOA,ACHA,ACHA;AFOA,ACHA,ACHA;AFOA,ACHA,ACHA;AFOA,ACHA,ACHA;AFOA,ACHA,ACHA;AFOA,ACHA,ACHA;AFOA,ACHA,ACHA;AFOA,ACHA,ACHA;AFOA,ACHA,ACHA;AFOA,ACHA,ACHA;AFOA,ACHA,ACHA;AFOA,ACHA,ACHA;AFOA,ACHA,ACHA;AFOA,ACHA,ACHA;AFOA,ACHA,ACHA;AFOA,ACHA,ACHA;AFOA,ACHA,ACHA;AFOA,ACHA,ACHA;AFOA,ACHA,ACHA;AFOA,ACHA,ACHA;AFOA,ACHA,ACHA;AFOA,ACHA,ACHA;AFOA,ACHA,ACHA;AFOA,ACHA,ACHA;AFOA,ACHA,ACHA;AFOA,ACHA,ACHA;AFOA,ACHA,ACHA;AFOA,ACHA,ACHA;AFOA,ACHA,ACHA;AFOA,ACHA,ACHA;AFOA,ACHA,ACHA;AFOA,ACHA,ACHA;AFOA,ACHA,ACHA;AFOA,ACHA,ACHA;AFOA,ACHA,ACHA;AFOA,ACHA,ACHA;AFOA,ACHA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.js", "sourcesContent": ["const Db = require('@cloudbase/database').Db\nconst storage = require('./src/storage')\nconst functions = require('./src/functions')\nconst auth = require('./src/auth')\nconst wx = require('./src/wx')\nconst Request = require('./src/utils/dbRequest')\nconst logger = require('./src/log')\nconst { SYMBOL_CURRENT_ENV } = require('./src/const/symbol')\nconst { getCurrentEnv } = require('./src/utils/utils')\n\nconst ExtRequest = require('./src/utils/extRequest')\n\nfunction Tcb(config) {\n  this.config = config ? config : this.config\n  this.requestClient = new ExtRequest()\n  this.SYMBOL_CURRENT_ENV = SYMBOL_CURRENT_ENV\n}\n\nTcb.prototype.init = function({\n  secretId,\n  secretKey,\n  sessionToken,\n  debug,\n  env,\n  proxy,\n  timeout,\n  serviceUrl,\n  version,\n  headers = {},\n  credentials,\n  timingsMeasurer,\n  isHttp,\n  signMethod = 'v2',\n  isUpdateSelfConfig = true,\n  forever = false\n} = {}) {\n  if ((secretId && !secretKey) || (!secretId && secretKey)) {\n    throw Error('secretId and secretKey must be a pair')\n  }\n\n  const config = {\n    get secretId() {\n      return this._secretId ? this._secretId : process.env.TENCENTCLOUD_SECRETID\n    },\n    set secretId(id) {\n      this._secretId = id\n    },\n    get secretKey() {\n      return this._secretKey\n        ? this._secretKey\n        : process.env.TENCENTCLOUD_SECRETKEY\n    },\n    set secretKey(key) {\n      this._secretKey = key\n    },\n    get sessionToken() {\n      if (this._sessionToken === undefined) {\n        //默认临时密钥\n        return process.env.TENCENTCLOUD_SESSIONTOKEN\n      } else if (this._sessionToken === false) {\n        //固定秘钥\n        return undefined\n      } else {\n        //传入的临时密钥\n        return this._sessionToken\n      }\n    },\n    set sessionToken(token) {\n      this._sessionToken = token\n    },\n    envName: env,\n    proxy: proxy,\n    isHttp: isHttp,\n    headers: Object.assign({}, headers)\n  }\n\n  config.debug = debug\n  config.forever = forever\n  config.signMethod = signMethod\n  config.timingsMeasurer = timingsMeasurer\n  config.secretId = secretId\n  config.secretKey = secretKey\n  config.timeout = timeout || 15000\n  config.serviceUrl = serviceUrl\n  config.credentials = credentials\n  config.sessionToken = sessionToken\n    ? sessionToken\n    : secretId && secretKey\n    ? false\n    : undefined\n\n  if (version) {\n    config.headers['x-sdk-version'] = version\n  }\n\n  // 这里的目的是创建新实例时可以避免更新当前实例\n  if (isUpdateSelfConfig) {\n    this.config = config\n  }\n\n  return new Tcb(config)\n}\n\nTcb.prototype.database = function(dbConfig = {}) {\n  Db.reqClass = Request\n  if (Object.prototype.toString.call(dbConfig).slice(8, -1) !== 'Object') {\n    throw Error('dbConfig must be an object')\n  }\n\n  if (dbConfig && dbConfig.env) {\n    // env变量名转换\n    dbConfig.envName = dbConfig.env\n    delete dbConfig.env\n  }\n  this.config = Object.assign(this.config, dbConfig)\n  return new Db({ ...this })\n}\n\n/**\n * @returns string\n */\nTcb.prototype.getCurrentEnv = function() {\n  return getCurrentEnv()\n}\n\nconst extensionMap = {}\n/**\n * 注册扩展\n */\nTcb.prototype.registerExtension = function(ext) {\n  extensionMap[ext.name] = ext\n}\n\nTcb.prototype.invokeExtension = async function(name, opts) {\n  const ext = extensionMap[name]\n  if (!ext) {\n    throw Error(`扩展${name} 必须先注册`)\n  }\n\n  return await ext.invoke(opts, this)\n}\n\nTcb.prototype.parseContext = function(context) {\n  if (typeof context !== 'object') {\n    throw Error('context 必须为对象类型')\n  }\n  let {\n    memory_limit_in_mb,\n    time_limit_in_ms,\n    request_id,\n    environ = '',\n    function_version,\n    namespace,\n    function_name,\n    environment\n  } = context\n  let parseResult = {}\n\n  try {\n    parseResult.memoryLimitInMb = memory_limit_in_mb\n    parseResult.timeLimitIns = time_limit_in_ms\n    parseResult.requestId = request_id\n    parseResult.functionVersion = function_version\n    parseResult.namespace = namespace\n    parseResult.functionName = function_name\n\n    // 存在environment 为新架构 上新字段 JSON序列化字符串\n    if (environment) {\n      parseResult.environment = JSON.parse(environment)\n      return parseResult\n    }\n\n    // 不存在environment 则为老字段，老架构上存在bug，无法识别value含特殊字符(若允许特殊字符，影响解析，这里特殊处理)\n\n    const parseEnviron = environ.split(';')\n    let parseEnvironObj = {}\n    for (let i in parseEnviron) {\n      const equalIndex = parseEnviron[i].indexOf('=')\n      if (equalIndex < 0) {\n        // value含分号影响切割，未找到= 均忽略\n        continue\n      }\n      const key = parseEnviron[i].slice(0, equalIndex)\n      let value = parseEnviron[i].slice(equalIndex + 1)\n\n      // value 含, 为数组\n      if (value.indexOf(',') >= 0) {\n        value = value.split(',')\n      }\n      parseEnvironObj[key] = value\n    }\n\n    parseResult.environ = parseEnvironObj\n  } catch (err) {\n    throw Error('无效的context对象')\n  }\n  return parseResult\n}\n\nfunction each(obj, fn) {\n  for (var i in obj) {\n    if (obj.hasOwnProperty(i)) {\n      fn(obj[i], i)\n    }\n  }\n}\n\nfunction extend(target, source) {\n  each(source, function(val, key) {\n    target[key] = source[key]\n  })\n  return target\n}\n\nextend(Tcb.prototype, functions)\nextend(Tcb.prototype, storage)\nextend(Tcb.prototype, wx)\nextend(Tcb.prototype, auth)\nextend(Tcb.prototype, logger)\n\nmodule.exports = new Tcb()\n", "const request = require('request')\nconst fs = require('fs')\nconst httpRequest = require('../utils/httpRequest')\nconst { parseString } = require('xml2js')\n\nasync function parseXML(str) {\n  return new Promise((resolve, reject) => {\n    parseString(str, (err, result) => {\n      if (err) {\n        reject(err)\n      } else {\n        resolve(result)\n      }\n    })\n  })\n}\n\n/*\n * 上传文件\n * @param {string} cloudPath 上传后的文件路径\n * @param {fs.ReadStream} fileContent  上传文件的二进制流\n */\nasync function uploadFile({ cloudPath, fileContent }) {\n  const {\n    data: { url, token, authorization, fileId, cosFileId }\n  } = await getUploadMetadata.call(this, { cloudPath })\n\n  const formData = {\n    Signature: authorization,\n    'x-cos-security-token': token,\n    'x-cos-meta-fileid': cosFileId,\n    key: cloudPath,\n    file: fileContent\n  }\n\n  let body = await new Promise((resolve, reject) => {\n    request.post({ url, formData: formData }, function(err, res, body) {\n      if (err) {\n        reject(err)\n      } else {\n        resolve(body)\n      }\n    })\n  })\n\n  body = await parseXML(body)\n  if (body && body.Error) {\n    const {\n      Code: [code],\n      Message: [message]\n    } = body.Error\n    if (code === 'SignatureDoesNotMatch') {\n      return {\n        code: 'SYS_ERR',\n        message\n      }\n    }\n    return {\n      code: 'STORAGE_REQUEST_FAIL',\n      message\n    }\n  }\n\n  return {\n    fileID: fileId\n  }\n}\n\n/**\n * 删除文件\n * @param {Array.<string>} fileList 文件id数组\n */\nasync function deleteFile({ fileList }) {\n  if (!fileList || !Array.isArray(fileList)) {\n    return {\n      code: 'INVALID_PARAM',\n      message: 'fileList必须是非空的数组'\n    }\n  }\n\n  for (let file of fileList) {\n    if (!file || typeof file != 'string') {\n      return {\n        code: 'INVALID_PARAM',\n        message: 'fileList的元素必须是非空的字符串'\n      }\n    }\n  }\n\n  let params = {\n    action: 'storage.batchDeleteFile',\n    fileid_list: fileList\n  }\n\n  return httpRequest({\n    config: this.config,\n    params,\n    method: 'post',\n    headers: {\n      'content-type': 'application/json'\n    }\n  }).then(res => {\n    if (res.code) {\n      return res\n    } else {\n      return {\n        fileList: res.data.delete_list,\n        requestId: res.requestId\n      }\n    }\n  })\n}\n\n/**\n * 获取文件下载链接\n * @param {Array.<Object>} fileList\n */\nasync function getTempFileURL({ fileList }) {\n  if (!fileList || !Array.isArray(fileList)) {\n    return {\n      code: 'INVALID_PARAM',\n      message: 'fileList必须是非空的数组'\n    }\n  }\n\n  let file_list = []\n  for (let file of fileList) {\n    if (typeof file === 'object') {\n      if (!file.hasOwnProperty('fileID') || !file.hasOwnProperty('maxAge')) {\n        return {\n          code: 'INVALID_PARAM',\n          message: 'fileList的元素必须是包含fileID和maxAge的对象'\n        }\n      }\n\n      file_list.push({\n        fileid: file.fileID,\n        max_age: file.maxAge\n      })\n    } else if (typeof file === 'string') {\n      file_list.push({\n        fileid: file\n      })\n    } else {\n      return {\n        code: 'INVALID_PARAM',\n        message: 'fileList的元素必须是字符串'\n      }\n    }\n  }\n\n  let params = {\n    action: 'storage.batchGetDownloadUrl',\n    file_list\n  }\n  // console.log(params);\n\n  return httpRequest({\n    config: this.config,\n    params,\n    method: 'post',\n    headers: {\n      'content-type': 'application/json'\n    }\n  }).then(res => {\n    // console.log(res);\n    if (res.code) {\n      return res\n    } else {\n      return {\n        fileList: res.data.download_list,\n        requestId: res.requestId\n      }\n    }\n  })\n}\n\nasync function getFileAuthority({ fileList }) {\n  if (!Array.isArray(fileList)) {\n    throw new Error(\n      '[tcb-admin-node] getCosFileAuthority fileList must be a array'\n    )\n  }\n\n  if (\n    fileList.some(file => {\n      if (!file || !file.path) {\n        return true\n      }\n      if (['READ', 'WRITE', 'READWRITE'].indexOf(file.type) === -1) {\n        return true\n      }\n    })\n  ) {\n    throw new Error('[tcb-admin-node] getCosFileAuthority fileList param error')\n  }\n\n  const userInfo = this.auth().getUserInfo()\n  const { openId, uid } = userInfo\n\n  if (!openId && !uid) {\n    throw new Error('[tcb-admin-node] admin do not need getCosFileAuthority.')\n  }\n\n  let params = {\n    action: 'storage.getFileAuthority',\n    openId,\n    uid,\n    loginType: process.env.LOGINTYPE,\n    fileList\n  }\n  const res = await httpRequest({\n    config: this.config,\n    params,\n    method: 'post',\n    headers: {\n      'content-type': 'application/json'\n    }\n  })\n\n  if (res.code) {\n    throw new Error('[tcb-admin-node] getCosFileAuthority failed: ' + res.code)\n  } else {\n    return res\n  }\n}\n\nasync function downloadFile({ fileID, tempFilePath }) {\n  let tmpUrl,\n    self = this\n  try {\n    const tmpUrlRes = await this.getTempFileURL({\n      fileList: [\n        {\n          fileID,\n          maxAge: 600\n        }\n      ]\n    })\n    // console.log(tmpUrlRes);\n    const res = tmpUrlRes.fileList[0]\n\n    if (res.code != 'SUCCESS') {\n      return res\n    }\n\n    tmpUrl = res.tempFileURL\n    tmpUrl = encodeURI(tmpUrl)\n  } catch (e) {\n    throw e\n  }\n\n  let req = request({\n    url: tmpUrl,\n    encoding: null,\n    proxy: self.config.proxy\n  })\n\n  return new Promise((resolve, reject) => {\n    let fileContent = Buffer.alloc(0)\n    req.on('response', function(response) {\n      if (response && +response.statusCode === 200) {\n        if (tempFilePath) {\n          response.pipe(fs.createWriteStream(tempFilePath))\n        } else {\n          response.on('data', data => {\n            fileContent = Buffer.concat([fileContent, data])\n          })\n        }\n        response.on('end', () => {\n          resolve({\n            fileContent: tempFilePath ? undefined : fileContent,\n            message: '文件下载完成'\n          })\n        })\n      } else {\n        reject(response)\n      }\n    })\n  })\n}\n\nasync function getUploadMetadata({ cloudPath }) {\n  let params = {\n    action: 'storage.getUploadMetadata',\n    path: cloudPath\n  }\n\n  const res = await httpRequest({\n    config: this.config,\n    params,\n    method: 'post',\n    headers: {\n      'content-type': 'application/json'\n    }\n  })\n\n  if (res.code) {\n    throw new Error('get upload metadata failed: ' + res.code)\n  } else {\n    return res\n  }\n}\n\nexports.uploadFile = uploadFile\nexports.deleteFile = deleteFile\nexports.getTempFileURL = getTempFileURL\nexports.downloadFile = downloadFile\nexports.getUploadMetadata = getUploadMetadata\nexports.getFileAuthority = getFileAuthority\n", "const http = require('http')\nconst request = require('request')\nconst auth = require('./auth.js')\nconst tracing = require('./tracing')\nconst utils = require('./utils')\nconst version = require('../../package.json').version\nconst getWxCloudApiToken = require('./getWxCloudApiToken')\nconst RequestTimgingsMeasurer = require('./request-timings-measurer')\n  .RequestTimgingsMeasurer\nconst URL = require('url')\nconst { sign } = require('@cloudbase/signature-nodejs')\nconst { SYMBOL_CURRENT_ENV } = require('../const/symbol')\n\nmodule.exports = utils.warpPromise(doRequest)\n\nasync function doRequest(args) {\n  const config = args.config\n  const method = args.method || 'post'\n  const signMethod = config.signMethod || 'v2'\n  const protocol = config.isHttp === true ? 'http' : 'https'\n  const isInSCF = process.env.TENCENTCLOUD_RUNENV === 'SCF'\n\n  if (!config.secretId || !config.secretKey) {\n    if (isInSCF) {\n      throw Error('missing authoration key, redeploy the function')\n    }\n    throw Error('missing secretId or secretKey of tencent cloud')\n  }\n\n  const tracingInfo = tracing.generateTracingInfo()\n  const seqId = tracingInfo.seqId\n  const eventId = tracingInfo.eventId\n\n  // 检查envName 是否为symbol\n  if (config.envName === SYMBOL_CURRENT_ENV) {\n    config.envName = utils.getCurrentEnv()\n  }\n\n  const params = Object.assign({}, args.params, {\n    envName: config.envName,\n    timestamp: new Date().valueOf(),\n    eventId,\n    wxCloudApiToken: getWxCloudApiToken(),\n    // 对应服务端 wxCloudSessionToken\n    tcb_sessionToken: process.env.TCB_SESSIONTOKEN || ''\n  })\n  utils.filterUndefined(params)\n\n  // wx.openApi 以及 wx.wxPayApi 带的requestData 需避开签名\n\n  let requestData = null\n  if (params.action === 'wx.openApi' || params.action === 'wx.wxPayApi') {\n    requestData = params['requestData']\n    delete params['requestData']\n  }\n\n  // Note: 云函数被调用时可能调用端未传递 SOURCE，TCB_SOURCE 可能为空\n  const TCB_SOURCE = process.env.TCB_SOURCE || ''\n  const SOURCE = isInSCF ? `${TCB_SOURCE},scf` : ',not_scf'\n\n  // url\n  let url = ''\n  if (config.serviceUrl) {\n    url = config.serviceUrl\n  } else {\n    url = protocol + '://tcb-admin.tencentcloudapi.com/admin'\n\n    if (isInSCF) {\n      url = 'http://tcb-admin.tencentyun.com/admin'\n    }\n\n    if (\n      params.action === 'wx.api' ||\n      params.action === 'wx.openApi' ||\n      params.action === 'wx.wxPayApi'\n    ) {\n      url = protocol + '://tcb-open.tencentcloudapi.com/admin'\n      if (isInSCF) {\n        url = 'http://tcb-open.tencentyun.com/admin'\n      }\n    }\n  }\n\n  if (url.includes('?')) {\n    url = `${url}&eventId=${eventId}&seqId=${seqId}`\n  } else {\n    url = `${url}?&eventId=${eventId}&seqId=${seqId}`\n  }\n\n  let headers = {}\n\n  if (signMethod === 'v3') {\n    headers = {\n      'x-tcb-source': SOURCE,\n      'User-Agent': `tcb-admin-sdk/${version}`,\n      'X-SDK-Version': `tcb-admin-sdk/${version}`,\n      Host: URL.parse(url).host\n    }\n\n    if (params.action === 'wx.openApi' || params.action === 'wx.wxPayApi') {\n      headers['content-type'] = 'multipart/form-data'\n    }\n\n    headers = Object.assign({}, config.headers, args.headers, headers)\n\n    const signInfo = sign({\n      secretId: config.secretId,\n      secretKey: config.secretKey,\n      method: method,\n      url: url,\n      params: params,\n      headers,\n      withSignedParams: true\n    })\n\n    headers['Authorization'] = signInfo.authorization\n    headers['X-Signature-Expires'] = 600\n    headers['X-Timestamp'] = signInfo.timestamp\n  } else {\n    headers = {\n      'user-agent': `tcb-admin-sdk/${version}`,\n      'x-tcb-source': SOURCE\n    }\n\n    const authObj = {\n      SecretId: config.secretId,\n      SecretKey: config.secretKey,\n      Method: method,\n      pathname: '/admin',\n      Query: params,\n      Headers: Object.assign({}, headers)\n    }\n\n    params.authorization = auth.getAuth(authObj)\n\n    headers = Object.assign({}, config.headers, args.headers, headers)\n  }\n\n  requestData && (params.requestData = requestData)\n  config.sessionToken && (params.sessionToken = config.sessionToken)\n  params.sdk_version = version\n\n  const opts = {\n    url,\n    method: args.method || 'post',\n    // 先取模块的timeout，没有则取sdk的timeout，还没有就使用默认值\n    timeout: args.timeout || config.timeout || 15000,\n    headers,\n    proxy: config.proxy\n  }\n\n  if (args.method == 'post') {\n    if (params.action === 'wx.openApi' || params.action === 'wx.wxPayApi') {\n      opts.formData = params\n      opts.encoding = null\n    } else {\n      opts.body = params\n      opts.json = true\n    }\n  } else {\n    opts.qs = params\n  }\n\n  if (args.proxy) {\n    opts.proxy = args.proxy\n  }\n\n  // 针对数据库请求设置慢查询提示\n  let slowQueryWarning = null\n  if (params.action.indexOf('database') >= 0) {\n    slowQueryWarning = setTimeout(() => {\n      console.warn(\n        `Database operation ${params.action} is longer than 3s. Please check query performance and your network environment. | [${seqId}]`\n      )\n    }, 3000)\n  }\n\n  try {\n    return new Promise(function(resolve, reject) {\n      const timingsMeasurerOptions = config.timingsMeasurer || {}\n      const {\n        waitingTime = 1000,\n        interval = 200,\n        enable = !!config.debug\n      } = timingsMeasurerOptions\n      const timingsMeasurer = RequestTimgingsMeasurer.new({\n        waitingTime,\n        interval,\n        enable\n      })\n\n      let targetName = ''\n      if (params.action.startsWith('functions')) {\n        targetName = params.function_name\n      } else if (params.action.startsWith('database')) {\n        targetName = params.collectionName\n      } else if (params.action.startsWith('wx')) {\n        targetName = params.apiName\n      }\n\n      timingsMeasurer.on('progress', timings => {\n        const timingsLine = `s:${timings.socket || '-'}|l:${timings.lookup ||\n          '-'}|c:${timings.connect || '-'}|r:${timings.ready ||\n          '-'}|w:${timings.waiting || '-'}|d:${timings.download ||\n          '-'}|e:${timings.end || '-'}`\n        console.warn(\n          `[RequestTimgings] Operation [${\n            params.action\n          }:${targetName}] spent ${Date.now() -\n            timings.start}ms(${timingsLine}) [${seqId}]`\n        )\n      })\n\n      if (config.forever) {\n        opts.forever = true\n      }\n\n      const clientRequest = request(opts, function(err, response, body) {\n        args && args.callback && args.callback(response)\n        if (err) {\n          return reject(err)\n        }\n\n        if (response.statusCode === 200) {\n          let res\n          try {\n            res = typeof body === 'string' ? JSON.parse(body) : body\n            // wx.openApi 和 wx.wxPayApi 调用时，需用content-type区分buffer or JSON\n            if (\n              params.action === 'wx.openApi' ||\n              params.action === 'wx.wxPayApi'\n            ) {\n              const { headers } = response\n              if (\n                headers['content-type'] === 'application/json; charset=utf-8'\n              ) {\n                res = JSON.parse(res.toString()) // JSON错误时buffer转JSON\n              }\n            }\n          } catch (e) {\n            res = body\n          }\n          return resolve(res)\n        } else {\n          // 避免非 200 错误导致返回空内容\n          const e = new Error(`\n            ${response.statusCode} ${http.STATUS_CODES[response.statusCode]}\n          `)\n          e.statusCode = response.statusCode\n          reject(e)\n        }\n      })\n      timingsMeasurer.measure(clientRequest)\n    })\n  } finally {\n    if (slowQueryWarning) {\n      clearTimeout(slowQueryWarning)\n    }\n  }\n}\n", "var crypto = require('crypto')\n\nfunction camSafeUrlEncode(str) {\n  return encodeURIComponent(str)\n    .replace(/!/g, '%21')\n    .replace(/'/g, '%27')\n    .replace(/\\(/g, '%28')\n    .replace(/\\)/g, '%29')\n    .replace(/\\*/g, '%2A')\n}\nfunction map(obj, fn) {\n  var o = isArray(obj) ? [] : {}\n  for (var i in obj) {\n    if (obj.hasOwnProperty(i)) {\n      o[i] = fn(obj[i], i)\n    }\n  }\n  return o\n}\nfunction isArray(arr) {\n  return arr instanceof Array\n}\n\nfunction clone(obj) {\n  return map(obj, function(v) {\n    return typeof v === 'object' && v !== undefined && v !== null ? clone(v) : v\n  })\n}\n//测试用的key后面可以去掉\nvar getAuth = function(opt) {\n  opt = opt || {}\n\n  var SecretId = opt.SecretId\n  var SecretKey = opt.SecretKey\n  var method = (opt.method || opt.Method || 'get').toLowerCase()\n  var pathname = opt.pathname || '/'\n  var queryParams = clone(opt.Query || opt.params || {})\n  var headers = clone(opt.Headers || opt.headers || {})\n  pathname.indexOf('/') !== 0 && (pathname = '/' + pathname)\n\n  if (!SecretId) {\n    throw Error('missing param SecretId')\n  }\n\n  if (!SecretKey) {\n    throw Error('missing param SecretKey')\n  }\n\n  var getObjectKeys = function(obj) {\n    var list = []\n    for (var key in obj) {\n      if (obj.hasOwnProperty(key)) {\n        if (obj[key] === undefined) {\n          continue\n        }\n        list.push(key)\n      }\n    }\n    return list.sort()\n  }\n\n  var obj2str = function(obj) {\n    var i, key, val\n    var list = []\n    var keyList = getObjectKeys(obj)\n    for (i = 0; i < keyList.length; i++) {\n      key = keyList[i]\n      if (obj[key] === undefined) {\n        continue\n      }\n      val = obj[key] === null ? '' : obj[key]\n      if (typeof val !== 'string') {\n        val = JSON.stringify(val)\n      }\n      key = key.toLowerCase()\n      key = camSafeUrlEncode(key)\n      val = camSafeUrlEncode(val) || ''\n      list.push(key + '=' + val)\n    }\n    return list.join('&')\n  }\n\n  // 签名有效起止时间\n  var now = parseInt(new Date().getTime() / 1000) - 1\n  var exp = now\n\n  var Expires = opt.Expires || opt.expires\n  if (Expires === undefined) {\n    exp += 900 // 签名过期时间为当前 + 900s\n  } else {\n    exp += Expires * 1 || 0\n  }\n\n  // 要用到的 Authorization 参数列表\n  var qSignAlgorithm = 'sha1'\n  var qAk = SecretId\n  var qSignTime = now + ';' + exp\n  var qKeyTime = now + ';' + exp\n  var qHeaderList = getObjectKeys(headers)\n    .join(';')\n    .toLowerCase()\n  var qUrlParamList = getObjectKeys(queryParams)\n    .join(';')\n    .toLowerCase()\n\n  // 签名算法说明文档：https://www.qcloud.com/document/product/436/7778\n  // 步骤一：计算 SignKey\n  var signKey = crypto\n    .createHmac('sha1', SecretKey)\n    .update(qKeyTime)\n    .digest('hex')\n\n  // console.log(\"queryParams\", queryParams);\n  // console.log(obj2str(queryParams));\n\n  // 步骤二：构成 FormatString\n  var formatString = [\n    method,\n    pathname,\n    obj2str(queryParams),\n    obj2str(headers),\n    ''\n  ].join('\\n')\n\n  // console.log(formatString);\n  formatString = Buffer.from(formatString, 'utf8')\n\n  // 步骤三：计算 StringToSign\n  var sha1Algo = crypto.createHash('sha1')\n  sha1Algo.update(formatString)\n  var res = sha1Algo.digest('hex')\n  var stringToSign = ['sha1', qSignTime, res, ''].join('\\n')\n\n  // console.log(stringToSign);\n  // 步骤四：计算 Signature\n  var qSignature = crypto\n    .createHmac('sha1', signKey)\n    .update(stringToSign)\n    .digest('hex')\n\n  // 步骤五：构造 Authorization\n  var authorization = [\n    'q-sign-algorithm=' + qSignAlgorithm,\n    'q-ak=' + qAk,\n    'q-sign-time=' + qSignTime,\n    'q-key-time=' + qKeyTime,\n    'q-header-list=' + qHeaderList,\n    'q-url-param-list=' + qUrlParamList,\n    'q-signature=' + qSignature\n  ].join('&')\n\n  return authorization\n}\n\nexports.getAuth = getAuth\n", "let seqNum = 0\n\nfunction getSeqNum() {\n  return ++seqNum\n}\n\nfunction generateEvnentId() {\n  return (\n    Date.now() +\n    '_' +\n    getSeqNum() +\n    '_' +\n    Math.random()\n      .toString()\n      .substr(2, 5)\n  )\n}\n\nexports.generateTracingInfo = function generateTracingInfo() {\n  const TCB_SEQID = process.env.TCB_SEQID || ''\n  const eventId = generateEvnentId()\n  const seqId = TCB_SEQID ? `${TCB_SEQID}-${eventId}` : eventId\n\n  return { eventId, seqId }\n}\n", "exports.filterValue = function filterValue(o, value) {\n  for (let key in o) {\n    if (o[key] === value) {\n      delete o[key]\n    }\n  }\n}\n\nexports.filterUndefined = function(o) {\n  return exports.filterValue(o, undefined)\n}\n\nexports.filterNull = function(o) {\n  return exports.filterValue(o, null)\n}\n\nexports.filterEmptyString = function(o) {\n  return exports.filterValue(o, '')\n}\n\nexports.deepFreeze = function(o) {\n  if (typeof value !== 'object') {\n    return o\n  }\n\n  Object.freeze(o)\n\n  Object.getOwnPropertyNames(o).forEach(function(prop) {\n    const value = o[prop]\n    if (\n      typeof value === 'object' &&\n      value !== null &&\n      !Object.isFrozen(value)\n    ) {\n      exports.deepFreeze(value)\n    }\n  })\n\n  return o\n}\n\nexports.warpPromise = function warp(fn) {\n  return function(...args) {\n    // 确保返回 Promise 实例\n    return new Promise((resolve, reject) => {\n      try {\n        return fn(...args)\n          .then(resolve)\n          .catch(reject)\n      } catch (e) {\n        reject(e)\n      }\n    })\n  }\n}\n\nexports.getCurrentEnv = function() {\n  return process.env.TCB_ENV || process.env.SCF_NAMESPACE\n}\n", "module.exports = {\n  \"name\": \"tcb-admin-node\",\n  \"version\": \"1.23.0\",\n  \"description\": \"tencent cloud base admin sdk for node.js\",\n  \"main\": \"index.js\",\n  \"scripts\": {\n    \"eslint\": \"eslint \\\"./**/*.js\\\" \\\"./**/*.ts\\\"\",\n    \"eslint-fix\": \"eslint --fix \\\"./**/*.js\\\" \\\"./**/*.ts\\\"\",\n    \"tsc\": \"tsc -p tsconfig.json\",\n    \"tsc:w\": \"tsc -p tsconfig.json -w\",\n    \"tstest\": \"mocha --timeout 5000 --require espower-typescript/guess test/**/*.test.ts\",\n    \"test\": \"jest --verbose false -i\",\n    \"coverage\": \"jest --verbose false --coverage\"\n  },\n  \"repository\": {\n    \"type\": \"git\",\n    \"url\": \"https://github.com/TencentCloudBase/tcb-admin-node\"\n  },\n  \"bugs\": {\n    \"url\": \"https://github.com/TencentCloudBase/tcb-admin-node/issues\"\n  },\n  \"homepage\": \"https://github.com/TencentCloudBase/tcb-admin-node#readme\",\n  \"keywords\": [\n    \"tcb-admin\"\n  ],\n  \"author\": \"jimmyzhang\",\n  \"license\": \"MIT\",\n  \"dependencies\": {\n    \"@cloudbase/database\": \"0.9.15\",\n    \"@cloudbase/signature-nodejs\": \"^1.0.0-beta.0\",\n    \"is-regex\": \"^1.0.4\",\n    \"jsonwebtoken\": \"^8.5.1\",\n    \"lodash.merge\": \"^4.6.1\",\n    \"request\": \"^2.87.0\",\n    \"xml2js\": \"^0.4.19\"\n  },\n  \"devDependencies\": {\n    \"@types/jest\": \"^23.1.4\",\n    \"@types/mocha\": \"^5.2.4\",\n    \"@types/node\": \"^10.12.12\",\n    \"bluebird\": \"^3.7.1\",\n    \"dumper.js\": \"^1.3.0\",\n    \"eslint\": \"^5.16.0\",\n    \"eslint-config-prettier\": \"^4.1.0\",\n    \"eslint-plugin-prettier\": \"^3.0.1\",\n    \"eslint-plugin-typescript\": \"^0.14.0\",\n    \"espower-typescript\": \"^8.1.4\",\n    \"husky\": \"^1.3.1\",\n    \"inquirer\": \"^6.3.1\",\n    \"jest\": \"^23.3.0\",\n    \"lint-staged\": \"^8.1.5\",\n    \"mocha\": \"^5.2.0\",\n    \"power-assert\": \"^1.5.0\",\n    \"prettier\": \"^1.17.0\",\n    \"semver\": \"^6.0.0\",\n    \"ts-jest\": \"^23.10.4\",\n    \"tslib\": \"^1.7.1\",\n    \"typescript\": \"^3.4.3\",\n    \"typescript-eslint-parser\": \"^22.0.0\"\n  },\n  \"engines\": {\n    \"node\": \">=8.6.0\"\n  },\n  \"husky\": {\n    \"hooks\": {\n      \"pre-commit\": \"lint-staged\"\n    }\n  },\n  \"lint-staged\": {\n    \"*.js\": [\n      \"eslint --fix\",\n      \"git add\"\n    ]\n  }\n}\n", "// 由定时触发器触发时（TRIGGER_SRC=timer）：优先使用 WX_TRIGGER_API_TOKEN_V0，不存在的话，为了兼容兼容旧的开发者工具，也是使用 WX_API_TOKEN\n// 非定时触发器触发时（TRIGGER_SRC!=timer）: 使用 WX_API_TOKEN\nfunction getWxCloudApiToken() {\n  if (process.env.TRIGGER_SRC === 'timer') {\n    return process.env.WX_TRIGGER_API_TOKEN_V0 || process.env.WX_API_TOKEN || ''\n  } else {\n    return process.env.WX_API_TOKEN || ''\n  }\n}\n\nmodule.exports = getWxCloudApiToken\n", "const EventEmitter = require('events').EventEmitter\n\nclass RequestTimgingsMeasurer extends EventEmitter {\n  static new(options) {\n    return new RequestTimgingsMeasurer(options)\n  }\n\n  constructor(options) {\n    super()\n\n    this.timings = {\n      // start: 0,\n      // lookup: -1,\n      // connect: -1,\n      // ready: -1,\n      // waiting: -1,\n      // download: -1,\n      // end: -1\n    }\n\n    this.enable = options.enable === true\n    this.timerStarted = false\n    this.intervalId = null\n    this.timeoutId = null\n\n    this.waitingTime = options.waitingTime || 1000\n    this.interval = options.interval || 200\n  }\n\n  _startTimer() {\n    if (!this.enable) {\n      return\n    }\n\n    if (this.timerStarted) {\n      return\n    }\n\n    this.timerStarted = true\n    this.intervalId = null\n    this.timeoutId = setTimeout(() => {\n      this._process()\n      this.intervalId = setInterval(() => {\n        this._process()\n      }, this.interval)\n    }, this.waitingTime)\n  }\n\n  _stopTimer() {\n    if (!this.enable) {\n      return\n    }\n\n    if (!this.timerStarted) {\n      return\n    }\n\n    this.timerStarted = false\n\n    clearTimeout(this.timeoutId)\n    clearInterval(this.intervalId)\n    this._process()\n  }\n\n  _process() {\n    this.emit('progress', { ...this.timings })\n  }\n\n  measure(clientRequest) {\n    if (!this.enable) {\n      return\n    }\n\n    this._startTimer()\n    const timings = this.timings\n\n    timings.start = Date.now()\n\n    clientRequest\n      .on('response', message => {\n        timings.response = Date.now()\n\n        timings.waiting = Date.now() - timings.start\n\n        message.on('end', () => {\n          timings.socket = timings.socket || 0\n          // timings.lookup = timings.lookup || timings.socket\n          // timings.connect = timings.connect || timings.lookup\n          timings.download = Date.now() - timings.response\n          timings.end = Date.now() - timings.start\n\n          this._stopTimer()\n        })\n      })\n      .on('socket', socket => {\n        timings.socket = Date.now() - timings.start\n        if (socket.connecting) {\n          socket.on('lookup', () => {\n            timings.lookup = Date.now() - timings.start\n          })\n          socket.on('connect', () => {\n            timings.connect = Date.now() - timings.start\n          })\n          socket.on('ready', () => {\n            timings.ready = Date.now() - timings.start\n          })\n          // socket.on('data', () => {})\n          // socket.on('drain', () => {})\n          // socket.on('end', () => {\n          //   // this._stopTimer()\n          // })\n          socket.on('error', () => {\n            timings.error = Date.now() - timings.start\n          })\n        }\n      })\n  }\n}\n\nexports.RequestTimgingsMeasurer = RequestTimgingsMeasurer\n", "exports.SYMBOL_CURRENT_ENV = Symbol.for('SYMBOL_CURRENT_ENV')\n", "const httpRequest = require('../utils/httpRequest')\n\n/**\n * 调用云函数\n * @param {String} name  函数名\n * @param {Object} functionParam 函数参数\n * @return {Promise}\n */\nfunction callFunction({ name, data }) {\n  try {\n    data = data ? JSON.stringify(data) : ''\n  } catch (e) {\n    return Promise.reject(e)\n  }\n  if (!name) {\n    return Promise.reject(\n      new Error({\n        message: '函数名不能为空'\n      })\n    )\n  }\n\n  const params = {\n    action: 'functions.invokeFunction',\n    function_name: name,\n    request_data: data\n  }\n\n  return httpRequest({\n    config: this.config,\n    params,\n    method: 'post',\n    headers: {\n      'content-type': 'application/json',\n      ...(process.env.TCB_ROUTE_KEY\n        ? { 'X-Tcb-Route-Key': process.env.TCB_ROUTE_KEY }\n        : {})\n    }\n  }).then(res => {\n    if (res.code) {\n      return res\n    } else {\n      let result\n      try {\n        result = JSON.parse(res.data.response_data)\n      } catch (e) {\n        result = res.data.response_data\n      }\n      return {\n        result,\n        requestId: res.requestId\n      }\n    }\n  })\n}\n\nexports.callFunction = callFunction\n", "const jwt = require('jsonwebtoken')\nconst { SYMBOL_CURRENT_ENV } = require('../const/symbol')\nconst { getCurrentEnv } = require('../utils/utils')\n\nconst checkCustomUserIdRegex = /^[a-zA-Z0-9_\\-#@~=*(){}[\\]:.,<>+]{4,32}$/\n\nfunction validateUid(uid) {\n  if (typeof uid !== 'string') {\n    throw new TypeError('uid must be a string')\n  }\n  if (!checkCustomUserIdRegex.test(uid)) {\n    throw new Error(`Invalid uid: \"${uid}\"`)\n  }\n}\n\nexports.auth = function() {\n  let self = this\n  return {\n    getUserInfo() {\n      const openId = process.env.WX_OPENID || ''\n      const appId = process.env.WX_APPID || ''\n      const uid = process.env.TCB_UUID || ''\n      const customUserId = process.env.TCB_CUSTOM_USER_ID || ''\n      const isAnonymous =\n        process.env.TCB_ISANONYMOUS_USER === 'true' ? true : false\n\n      return {\n        openId,\n        appId,\n        uid,\n        customUserId,\n        isAnonymous\n      }\n    },\n    async getAuthContext(context) {\n      const { environment, environ } = self.parseContext(context)\n      const env = environment || environ || {}\n      const { TCB_UUID, LOGINTYPE } = env\n      const res = {\n        uid: TCB_UUID,\n        loginType: LOGINTYPE\n      }\n      if (LOGINTYPE === 'QQ-MINI') {\n        const { QQ_OPENID, QQ_APPID } = env\n        res.appId = QQ_APPID\n        res.openId = QQ_OPENID\n      }\n      return res\n    },\n    getClientIP() {\n      return process.env.TCB_SOURCE_IP || ''\n    },\n    createTicket: (uid, options = {}) => {\n      validateUid(uid)\n      const timestamp = new Date().getTime()\n      let { credentials, envName } = this.config\n      if (!envName) {\n        throw new Error('no env in config')\n      }\n\n      // 使用symbol时替换为环境变量内的env\n      if (envName === SYMBOL_CURRENT_ENV) {\n        envName = getCurrentEnv()\n      }\n\n      const {\n        refresh = 3600 * 1000,\n        expire = timestamp + 7 * 24 * 60 * 60 * 1000\n      } = options\n      var token = jwt.sign(\n        {\n          alg: 'RS256',\n          env: envName,\n          iat: timestamp,\n          exp: timestamp + 10 * 60 * 1000, // ticket十分钟有效\n          uid,\n          refresh,\n          expire\n        },\n        credentials.private_key,\n        { algorithm: 'RS256' }\n      )\n\n      return credentials.private_key_id + '/@@/' + token\n    }\n  }\n}\n", "const httpRequest = require('../utils/httpRequest')\n\nexports.callWxOpenApi = function({ apiName, requestData } = {}) {\n  try {\n    requestData = requestData ? JSON.stringify(requestData) : ''\n  } catch (e) {\n    throw Error(e)\n  }\n\n  const params = {\n    action: 'wx.api',\n    apiName,\n    requestData\n  }\n\n  return httpRequest({\n    config: this.config,\n    params,\n    method: 'post',\n    headers: {\n      'content-type': 'application/json'\n    }\n  }).then(res => {\n    if (res.code) {\n      return res\n    } else {\n      let result\n      try {\n        result = JSON.parse(res.data.responseData)\n      } catch (e) {\n        result = res.data.responseData\n      }\n      return {\n        result,\n        requestId: res.requestId\n      }\n    }\n  })\n}\n\n/**\n * 调用wxopenAPi\n * @param {String} apiName  接口名\n * @param {Buffer} requestData\n * @return {Promise} 正常内容为buffer，报错为json {code:'', message:'', resquestId:''}\n */\nexports.callCompatibleWxOpenApi = function({ apiName, requestData } = {}) {\n  const params = {\n    action: 'wx.openApi',\n    apiName,\n    requestData\n  }\n\n  return httpRequest({\n    config: this.config,\n    params,\n    method: 'post',\n    headers: {}\n  })\n}\n\n/**\n * wx.wxPayApi 微信支付用\n * @param {String} apiName  接口名\n * @param {Buffer} requestData\n * @return {Promise} 正常内容为buffer，报错为json {code:'', message:'', resquestId:''}\n */\nexports.callWxPayApi = function({ apiName, requestData } = {}) {\n  const params = {\n    action: 'wx.wxPayApi',\n    apiName,\n    requestData\n  }\n\n  return httpRequest({\n    config: this.config,\n    params,\n    method: 'post',\n    headers: {}\n  })\n}\n", "const httpRequest = require('./httpRequest')\n\n/**\n * 数据库模块的通用请求方法\n *\n * <AUTHOR>\n * @internal\n */\nclass Request {\n  /**\n   * 初始化\n   *\n   * @internal\n   * @param config\n   */\n  constructor(config) {\n    this.config = config\n  }\n\n  /**\n   * 发送请求\n   *\n   * @param api   - 接口\n   * @param data  - 参数\n   */\n  async send(api, data) {\n    const params = Object.assign({}, data, {\n      action: api\n    })\n\n    return await httpRequest({\n      timeout: this.config.timeout,\n      config: this.config.config,\n      params,\n      method: 'post',\n      headers: {\n        'content-type': 'application/json'\n      }\n    })\n  }\n}\n\nmodule.exports = Request\n", "/**\n *\n *\n * @class Log\n */\nclass Log {\n  constructor() {\n    this.src = 'app'\n  }\n\n  /**\n   *\n   *\n   * @param {*} logMsg\n   * @param {*} logLevel\n   * @returns\n   * @memberof Log\n   */\n  transformMsg(logMsg) {\n    // 目前logMsg只支持字符串value且不支持多级, 加一层转换处理\n    let realMsg = {}\n\n    realMsg = Object.assign({}, realMsg, logMsg)\n    return realMsg\n  }\n\n  /**\n   *\n   *\n   * @param {*} logMsg\n   * @param {*} logLevel\n   * @memberof Log\n   */\n  baseLog(logMsg, logLevel) {\n    // 判断当前是否属于tcb scf环境\n\n    if (Object.prototype.toString.call(logMsg).slice(8, -1) !== 'Object') {\n      throw Error('please input correct log msg')\n    }\n\n    const msgContent = this.transformMsg(logMsg)\n\n    console.__baseLog__(msgContent, logLevel)\n  }\n\n  /**\n   *\n   *\n   * @param {*} logMsg\n   * @memberof Log\n   */\n  log(logMsg) {\n    this.baseLog(logMsg, 'log')\n  }\n\n  /**\n   *\n   *\n   * @param {*} logMsg\n   * @memberof Log\n   */\n  info(logMsg) {\n    this.baseLog(logMsg, 'info')\n  }\n\n  /**\n   *\n   *\n   * @param {*} logMsg\n   * @memberof Log\n   */\n  error(logMsg) {\n    this.baseLog(logMsg, 'error')\n  }\n\n  /**\n   *\n   *\n   * @param {*} logMsg\n   * @memberof Log\n   */\n  warn(logMsg) {\n    this.baseLog(logMsg, 'warn')\n  }\n}\n\nexports.logger = () => {\n  return new Log()\n}\n", "const httpRequest = require('./httpRequest')\nconst requestClient = require('request')\n\n/**\n * 扩展模块的请求类\n *\n */\nclass ExtRequest {\n  /**\n   * 初始化\n   *\n   * @internal\n   * @param config\n   */\n  // constructor(config) {\n  //   this.config = config\n  // }\n\n  /**\n   * 发送 tcb 请求\n   *\n   * @param api   - 接口\n   * @param data  - 参数\n   */\n  // async tcbRequest(api, data) {\n  //   const params = Object.assign({}, data, {\n  //     action: api\n  //   })\n\n  //   return await httpRequest({\n  //     timeout: this.config.timeout,\n  //     config: this.config,\n  //     params,\n  //     method: 'post',\n  //     headers: {\n  //       'content-type': 'application/json'\n  //     }\n  //   })\n  // }\n\n  get(options) {\n    return this.rawRequest({\n      ...options,\n      method: 'get'\n    })\n  }\n  post(options) {\n    return this.rawRequest({\n      ...options,\n      method: 'post'\n    })\n  }\n  put(options) {\n    return this.rawRequest({\n      ...options,\n      method: 'put'\n    })\n  }\n\n  /**\n   * 发送普通请求\n   * @param {*} opts\n   */\n  async rawRequest(opts) {\n    let res = await new Promise((resolve, reject) => {\n      requestClient(opts, function(err, res, body) {\n        if (err) {\n          reject(err)\n        } else {\n          resolve({\n            data: body,\n            statusCode: res.statusCode,\n            header: res.headers\n          })\n        }\n      })\n    })\n\n    return res\n  }\n}\n\nmodule.exports = ExtRequest\n"]}