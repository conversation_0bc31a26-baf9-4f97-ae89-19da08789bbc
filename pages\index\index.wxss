/* 自定义导航栏 - Skyline渲染引擎，支持动态适配 */
.custom-navbar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  /* height通过内联样式动态设置 */
  min-height: 88rpx; /* 最小高度保证兼容性 */
  background: #fff;
  display: flex;
  align-items: flex-end; /* 改为底部对齐，确保标题在导航栏底部 */
  justify-content: center;
  z-index: 9999;
  border-bottom: 1rpx solid #e5e5e5;
  /* GPU硬件加速 */
  transform: translate3d(0, 0, 0);
  will-change: transform;
  /* 安全区域适配 */
  box-sizing: border-box;
}

.navbar-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
  /* 确保标题在导航栏底部正确显示 */
  padding-bottom: 12rpx;
  line-height: 1.2;
}

/* 可滚动容器 - 修复滚动问题，兼容Skyline和WebView，支持动态适配 */
.scroll-container {
  position: fixed;
  /* top和height通过内联样式动态设置 */
  left: 0;
  right: 0;
  bottom: 0;
  /* GPU硬件加速 */
  transform: translate3d(0, 0, 0);
  will-change: scroll-position;
  /* WebView兼容性 */
  -webkit-overflow-scrolling: touch;
  overflow-y: auto;
  /* 安全区域适配 */
  box-sizing: border-box;
}

/* 容器样式 */
.container {
  display: flex;
  flex-direction: column;
  align-items: center;
  box-sizing: border-box; /* 确保容器的box-sizing正确 */
  min-height: 100%; /* 确保内容可以滚动 */
  padding: 20rpx; /* 内容间距 */
  min-height: 100vh;
  background: linear-gradient(160deg, #f3f1fc 0%, #eceafc 50%, #e8e5fb 100%);
  overflow-y: auto;
  padding-top: 30rpx;
  padding-left: 0; /* 移除左侧内边距 */
  padding-right: 0; /* 移除右侧内边距 */
  padding-bottom: calc(120rpx + env(safe-area-inset-bottom)); /* 减少底部内边距以减少与导航栏的距离 */
}

/* 轮播图样式 */
.carousel {
  width: 710rpx; /* 调整宽度 */
  margin-left: auto;
  margin-right: auto;
  height: 360rpx;
  margin-bottom: 20rpx;
  border-radius: 12rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
}

.carousel-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

/* 搜索栏样式 */
.search-bar {
  display: flex;
  width: 710rpx;
  margin-left: auto;
  margin-right: auto;
  padding: 20rpx 30rpx 20rpx 30rpx; /* 左右内边距相等，且与button-grid对齐 */
  background: linear-gradient(145deg, #FFFFFF 0%, #F9F8FF 100%);
  border-radius: 16rpx;
  box-shadow: 0 4rpx 16rpx rgba(120, 100, 220, 0.08);
  margin-bottom: 20rpx;
  justify-content: space-between;
  align-items: center;
  box-sizing: border-box; /* 确保内边距计入宽度 */
  transition: all 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  opacity: 1;
  transform: translateY(0);
}

/* 搜索栏隐藏动画 */
.search-bar-hidden {
  opacity: 0;
  transform: translateY(-20rpx);
  pointer-events: none;
}

/* 参数模式顶部按钮栏样式 - 修复发光重叠 */
.parameter-top-buttons {
  display: flex;
  width: 710rpx; /* 与视频容器宽度一致 */
  margin-left: auto;
  margin-right: auto;
  padding: 20rpx 20rpx; /* 增加内边距，为发光效果留出空间 */
  background: linear-gradient(145deg, #FFFFFF 0%, #F9F8FF 100%);
  border-radius: 16rpx;
  box-shadow: 0 4rpx 16rpx rgba(120, 100, 220, 0.08);
  margin-bottom: 20rpx;
  justify-content: space-between;
  align-items: center;
  box-sizing: border-box;
  gap: 24rpx; /* 进一步增加间距，彻底避免发光重叠 */
  opacity: 0;
  transform: translateY(-20rpx);
  pointer-events: none;
  transition: all 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

/* 参数模式顶部按钮栏显示动画 */
.parameter-top-buttons-visible {
  opacity: 1;
  transform: translateY(0);
  pointer-events: auto;
}

/* 🎯 参数模式按钮 - 只继承3D立体感效果，保持原有尺寸 */
.parameter-top-button {
  flex: 1;
  height: 88rpx;
  line-height: 88rpx;
  text-align: center;
  border-radius: 44rpx;
  font-size: 26rpx; /* 保持原有字体大小，确保文字完整显示 */
  margin: 0; /* 移除margin，使用gap控制间距 */
  padding: 0 8rpx; /* 保持原有内边距 */
  position: relative;
  overflow: hidden;
  font-weight: 550;
  letter-spacing: 1.5rpx; /* 与grid-button保持一致 */
  color: white;
  white-space: nowrap; /* 防止文字换行 */
  text-overflow: ellipsis; /* 与grid-button保持一致 */
  z-index: 1;
  /* 🎯 增强3D光照背景系统 - 与grid-button保持一致 */
  background:
    /* 🌟 主光源层 - 模拟45度角顶部照射（增强立体感） */
    linear-gradient(180deg,
      rgba(255, 255, 255, 0.28) 0%,     /* 增强顶部高光强度 */
      rgba(255, 255, 255, 0.18) 20%,    /* 扩大高光覆盖范围 */
      rgba(255, 255, 255, 0.08) 40%,    /* 自然过渡区域 */
      transparent 55%,                   /* 中性光照区域 */
      rgba(0, 0, 0, 0.08) 75%,          /* 增强底部阴影 */
      rgba(0, 0, 0, 0.18) 100%),        /* 加深底部阴影强度 */
    /* 🎨 保持原有蓝紫渐变色 - 维持品牌一致性 */
    linear-gradient(135deg, #78b9ff 0%, #a0a4ff 50%, #c58eff 100%);
  /* 🎯 移除边框，使用纯内部阴影定义边缘 - 避免微信小程序黑线问题 */
  border: none;
  /* 🎯 增强3D阴影系统 - 基于Material Design 3.0 Elevation 6dp */
  box-shadow:
    /* 📍 Contact Shadow - 接触阴影（增强定义） */
    0 1rpx 2rpx rgba(0, 0, 0, 0.22),
    /* 🌟 Key Light Shadow - 主光源阴影（增强深度） */
    0 3rpx 6rpx rgba(120, 185, 255, 0.35),
    0 6rpx 12rpx rgba(120, 185, 255, 0.28),
    0 9rpx 18rpx rgba(120, 185, 255, 0.20),
    /* 🌫️ Ambient Shadow - 环境光阴影（增强悬浮感） */
    0 12rpx 24rpx rgba(0, 0, 0, 0.12),
    0 18rpx 36rpx rgba(0, 0, 0, 0.08),
    /* ✨ Inner Highlights - 内部高光（增强立体感） */
    inset 0 1rpx 0 rgba(255, 255, 255, 0.40),
    inset 0 -1rpx 0 rgba(0, 0, 0, 0.28),
    /* 🔲 Edge Definition - 边缘定义（增强轮廓） */
    inset 0 0 0 1rpx rgba(255, 255, 255, 0.18);
  /* 🔧 移除过渡效果 - 避免hover-class颜色滞留 */
  /* transition: background 0.2s cubic-bezier(0.4, 0, 0.2, 1),
              box-shadow 0.2s cubic-bezier(0.4, 0, 0.2, 1); */
  /* 使用更简单的初始变换 */
  transform: translateZ(0);
  /* 只对实际会变化的属性使用will-change */
  will-change: transform;
}

/* 🎯 移除通用高光定义 - 参数模式按钮和grid按钮都有各自的专门高光定义 */

/* 确保按钮文字在最上层 - 已在上面定义，移除重复 */

/* 🎯 增强参数模式按钮高光系统 - 与grid-button保持一致 */
.parameter-top-button::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 45%;                              /* 增加高光覆盖范围 */
  border-radius: 44rpx 44rpx 20rpx 20rpx;   /* 优化高光形状 */
  /* 🌟 增强主光源高光 - 模拟真实光源直射效果 */
  background: linear-gradient(180deg,
    rgba(255, 255, 255, 0.35) 0%,          /* 增强顶部高光强度 */
    rgba(255, 255, 255, 0.25) 30%,         /* 扩大强高光区域 */
    rgba(255, 255, 255, 0.15) 60%,         /* 自然过渡 */
    rgba(255, 255, 255, 0.05) 85%,         /* 边缘柔化 */
    transparent 100%);
  z-index: 2;
  pointer-events: none;
}

/* 🎯 增强参数模式按钮边缘高光 - 与grid-button保持一致 */
.parameter-top-button::after {
  content: "";
  position: absolute;
  top: 0.5rpx;
  left: 0.5rpx;
  right: 0.5rpx;
  bottom: 0.5rpx;
  border-radius: 43.5rpx;
  /* 🎯 移除边框，避免微信小程序黑线问题 */
  border: none;
  /* 🎯 使用纯内部边缘阴影，增强立体感 */
  box-shadow: inset 0 0 0 0.5rpx rgba(255, 255, 255, 0.15);
  z-index: 1;
  pointer-events: none;
}

/* 🎯 移除参数模式按钮悬停效果 - 避免颜色滞留 */
/* .parameter-top-button:hover {
  filter: brightness(115%);
  transform: translateY(-3rpx);
  box-shadow:
    0 2rpx 4rpx hsl(220deg 70% 45% / 0.42),
    0 6rpx 12rpx hsl(220deg 70% 45% / 0.38),
    0 12rpx 24rpx hsl(220deg 70% 45% / 0.34),
    0 18rpx 36rpx hsl(220deg 70% 45% / 0.30),
    0 24rpx 48rpx hsl(220deg 70% 45% / 0.26),
    0 32rpx 64rpx hsl(220deg 70% 45% / 0.22),
    0 40rpx 80rpx hsl(220deg 70% 45% / 0.18),
    inset 0 3rpx 0 rgba(255, 255, 255, 0.35),
    inset 0 -3rpx 0 rgba(0, 0, 0, 0.20),
    inset 0 0 0 1px rgba(255, 255, 255, 0.15);
} */

/* 🎯 移除所有普通按钮的按压反馈效果 - 按用户要求 */

/* 🎯 增强参数模式录制按钮 - 与grid-button保持一致 */
.parameter-top-buttons .parameter-top-button.record-button {
  /* 🎯 增强3D光照背景系统 - 与其他按钮保持一致 */
  background:
    /* 🌟 主光源层 - 模拟45度角顶部照射（增强立体感） */
    linear-gradient(180deg,
      rgba(255, 255, 255, 0.28) 0%,     /* 增强顶部高光强度 */
      rgba(255, 255, 255, 0.18) 20%,    /* 扩大高光覆盖范围 */
      rgba(255, 255, 255, 0.08) 40%,    /* 自然过渡区域 */
      transparent 55%,                   /* 中性光照区域 */
      rgba(0, 0, 0, 0.08) 75%,          /* 增强底部阴影 */
      rgba(0, 0, 0, 0.18) 100%),        /* 加深底部阴影强度 */
    /* 🎨 保持原有蓝紫渐变色 - 维持品牌一致性 */
    linear-gradient(135deg, #78b9ff 0%, #a0a4ff 50%, #c58eff 100%);
  /* 🎯 增强3D阴影系统 - 基于Material Design 3.0 Elevation 6dp */
  box-shadow:
    /* 📍 Contact Shadow - 接触阴影（增强定义） */
    0 1rpx 2rpx rgba(0, 0, 0, 0.22),
    /* 🌟 Key Light Shadow - 主光源阴影（增强深度） */
    0 3rpx 6rpx rgba(120, 185, 255, 0.35),
    0 6rpx 12rpx rgba(120, 185, 255, 0.28),
    0 9rpx 18rpx rgba(120, 185, 255, 0.20),
    /* 🌫️ Ambient Shadow - 环境光阴影（增强悬浮感） */
    0 12rpx 24rpx rgba(0, 0, 0, 0.12),
    0 18rpx 36rpx rgba(0, 0, 0, 0.08),
    /* ✨ Inner Highlights - 内部高光（增强立体感） */
    inset 0 1rpx 0 rgba(255, 255, 255, 0.40),
    inset 0 -1rpx 0 rgba(0, 0, 0, 0.28),
    /* 🔲 Edge Definition - 边缘定义（增强轮廓） */
    inset 0 0 0 1rpx rgba(255, 255, 255, 0.18);
}

/* 🎯 增强参数模式录制按钮高光系统 - 与grid-button保持一致 */
.parameter-top-buttons .parameter-top-button.record-button::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 45%;                              /* 增加高光覆盖范围 */
  border-radius: 44rpx 44rpx 20rpx 20rpx;   /* 优化高光形状 */
  /* 🌟 增强主光源高光 - 模拟真实光源直射效果 */
  background: linear-gradient(180deg,
    rgba(255, 255, 255, 0.35) 0%,          /* 增强顶部高光强度 */
    rgba(255, 255, 255, 0.25) 30%,         /* 扩大强高光区域 */
    rgba(255, 255, 255, 0.15) 60%,         /* 自然过渡 */
    rgba(255, 255, 255, 0.05) 85%,         /* 边缘柔化 */
    transparent 100%);
  z-index: 2;
  pointer-events: none;
}

/* 🎯 参数模式录制按钮边缘高光通过内部阴影实现 - 避免与发光效果冲突 */

.parameter-top-buttons .parameter-top-button.record-button.recording {
  /* 🌈 高贵优雅渐变 - 深邃饱满的贵族色调 */
  background: linear-gradient(135deg,
    rgba(240, 160, 80, 0.92) 0%,    /* 深邃金橙 */
    rgba(235, 140, 100, 0.93) 20%,  /* 饱满琥珀橙 */
    rgba(225, 120, 130, 0.94) 40%,  /* 优雅玫瑰金 */
    rgba(210, 100, 140, 0.95) 60%,  /* 高贵玫红 */
    rgba(195, 85, 150, 0.96) 80%,   /* 贵族紫玫 */
    rgba(180, 75, 160, 0.97) 100%); /* 皇室薰衣草 */
  /* 🔮 玻璃质感效果 */
  backdrop-filter: blur(8rpx) saturate(1.2);
  box-shadow:
    inset 0 1rpx 0 rgba(255, 255, 255, 0.3),
    inset 0 -1rpx 0 rgba(0, 0, 0, 0.1),
    0 4rpx 20rpx rgba(200, 100, 140, 0.3);
  border: none; /* 移除边框，避免白色边框问题 */
  /* 移除backdrop-filter，消除漏光效果 */
  animation: rainbow-md3-elevation 2s infinite;
  /* 🚀 Material Design 3性能优化 - 标准GPU加速 */
  will-change: transform, box-shadow;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  /* 🚀 GPU硬件加速优化 */
  will-change: transform, opacity;
  transform: translate3d(0, 0, 0); /* 强制GPU加速 */
  position: relative;
  /* 🌈 简化阴影系统 - 移除filter，用伪元素模拟发光 */
  box-shadow:
    0 2rpx 6rpx rgba(0, 0, 0, 0.15),
    0 6rpx 12rpx rgba(195, 125, 35, 0.3),
    inset 0 1rpx 0 rgba(255, 255, 255, 0.3);
}

/* 🚫 移除旧的发光效果 - 使用与grid-button一致的高光系统 */

/* 🎯 参数模式录制按钮边缘高光 - 与grid-button保持一致 */
.parameter-top-buttons .parameter-top-button.record-button::after {
  content: "";
  position: absolute;
  top: 0.5rpx;
  left: 0.5rpx;
  right: 0.5rpx;
  bottom: 0.5rpx;
  border-radius: 43.5rpx;
  /* 🎯 移除边框，避免微信小程序黑线问题 */
  border: none;
  /* 🎯 使用纯内部边缘阴影，增强立体感 */
  box-shadow: inset 0 0 0 0.5rpx rgba(255, 255, 255, 0.15);
  z-index: 1;
  pointer-events: none;
}

/* 🔧 参数模式彩虹按钮发光效果 - 已集成到呼吸动画中 */

/* 🌟 增强参数模式彩虹按钮伪元素发光效果 - 优化为深邃玫红色渐变 */
.parameter-top-buttons .parameter-top-button.record-button.recording::before {
  content: '';
  position: absolute;
  top: -12rpx;
  left: -12rpx;
  right: -12rpx;
  bottom: -12rpx;
  background: linear-gradient(45deg,
    rgba(240, 160, 80, 0.9) 0%,     /* 贵族金橙发光 */
    rgba(225, 120, 130, 0.85) 40%,  /* 优雅玫瑰金发光 */
    rgba(195, 85, 150, 0.8) 80%,    /* 高贵紫玫发光 */
    rgba(180, 75, 160, 0.75) 100%); /* 皇室薰衣草发光 */
  border-radius: 50rpx;
  z-index: -1;
  animation: rainbow-breath 4s ease-in-out infinite;
  /* 🚀 移除blur滤镜 - 消除性能瓶颈 */
}

.parameter-top-buttons .parameter-top-button.record-button.recording::after {
  content: '';
  position: absolute;
  top: -16rpx;
  left: -16rpx;
  right: -16rpx;
  bottom: -16rpx;
  background: linear-gradient(45deg,
    rgba(240, 160, 80, 0.75) 0%,    /* 贵族金橙外发光 */
    rgba(225, 120, 130, 0.7) 40%,   /* 优雅玫瑰金外发光 */
    rgba(195, 85, 150, 0.65) 80%,   /* 高贵紫玫外发光 */
    rgba(180, 75, 160, 0.6) 100%);  /* 皇室薰衣草外发光 */
  border-radius: 55rpx;
  z-index: -2;
  animation: rainbow-breath 4s ease-in-out infinite reverse;
  /* 🚀 移除blur滤镜 - 消除性能瓶颈 */
}

/* 🎯 删除参数模式录制按钮的:active样式 - 使用类名替代 */



@keyframes recording-pulse {
  0%, 100% {
    box-shadow: 0 4rpx 16rpx rgba(255, 107, 107, 0.4);
  }
  50% {
    box-shadow: 0 4rpx 20rpx rgba(255, 107, 107, 0.8);
  }
}

.search-input {
  flex: 0.55; /* 微调输入框占比 */
  height: 72rpx;
  padding: 0 28rpx;
  font-size: 28rpx;
  background: #f5f7fa;
  border-radius: 36rpx;
  margin-right: 20rpx; /* 减少与按钮之间的间距 */
  border: 1rpx solid rgba(0, 0, 0, 0.05);
  box-shadow: inset 0 2rpx 5rpx rgba(0, 0, 0, 0.03);
}

/* 搜索按钮容器样式 */
.search-buttons {
  display: flex;
  justify-content: flex-end; /* 靠右对齐 */
  flex: 0.45; /* 微调按钮区域占比 */
  gap: 12rpx; /* 进一步减少按钮间距 */
  padding-right: 0; /* 移除右侧内边距 */
}

/* 搜索按钮和清除按钮的样式 */
.search-button, .clear-button {
  flex: 1; /* 让按钮平分可用空间 */
  max-width: 150rpx; /* 进一步限制最大宽度 */
  height: 72rpx;
  line-height: 72rpx;
  text-align: center;
  border-radius: 36rpx;
  font-size: 26rpx; /* 稍微减小字体 */
  margin: 0;
  padding: 0;
  border: none;
  position: relative;
  overflow: hidden;
  font-weight: 500;
  letter-spacing: 0; /* 移除字间距 */
  box-shadow: 0 4rpx 16rpx rgba(90, 120, 213, 0.4);
  background: linear-gradient(90deg, #78b9ff 0%, #c58eff 100%);
  color: white;
  /* 🔧 移除过渡效果 - 避免hover-class颜色滞留 */
  /* transition: background 0.25s cubic-bezier(0.175, 0.885, 0.32, 1.275),
              box-shadow 0.25s cubic-bezier(0.175, 0.885, 0.32, 1.275); */
  transform: translateY(0);
}

.search-button {
  margin-right: 20rpx;
}

.search-button::before, .clear-button::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  border-radius: 36rpx;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0) 50%, rgba(0, 0, 0, 0.05) 100%);
  opacity: 0;
  /* transition: opacity 0.25s ease; */
  z-index: 2;
}

.search-button::after, .clear-button::after {
  content: "";
  position: absolute;
  top: -1rpx;
  left: -1rpx;
  right: -1rpx;
  bottom: -1rpx;
  background: linear-gradient(90deg, #78b9ff, #c58eff);
  z-index: -1;
  border-radius: 37rpx;
  filter: blur(3rpx);
  opacity: 0.7;
}

/* 🎯 移除搜索按钮的按压反馈效果 - 按用户要求 */

/* 视频容器 */
.video-container {
  width: 710rpx; /* 调整宽度 */
  margin-left: auto;
  margin-right: auto;
  height: 520rpx;
  margin-bottom: 30rpx;
  border-radius: 16rpx;
  overflow: visible; /* 改为visible，确保检测框可以显示 */
  background: #000;
  box-shadow: 0 10rpx 30rpx rgba(0, 0, 0, 0.15);
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
  transition: all 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  z-index: 1;
}

/* 录制全屏模式 */
.video-container.recording-fullscreen {
  position: fixed !important;
  top: 0 !important;
  left: 0 !important;
  width: 100vw !important;
  height: 100vh !important;
  margin: 0 !important;
  border-radius: 0 !important;
  z-index: 99999 !important;
  box-shadow: none !important;
  background: #000 !important;
  /* 确保覆盖状态栏和导航栏 */
  padding-top: 0 !important;
  padding-bottom: 0 !important;
}

/* 添加视频容器底部渐变过渡效果 */
.video-container::after {
  content: '';
  position: absolute;
  bottom: -2rpx;
  left: 0;
  right: 0;
  height: 10rpx;
  background: linear-gradient(to bottom, rgba(0,0,0,0.2), transparent);
  z-index: 1;
}

.video-container .video-content, .video-container .live-content {
  width: 100%;
  height: 100%;
  object-fit: contain;
  display: block;
  background: #000;
  transition: all 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  z-index: 1; /* 确保视频在检测框之下 */
  border-radius: 16rpx; /* 保持圆角 */
  overflow: hidden; /* 视频内容溢出隐藏 */
}

/* 录制模式下的视频样式 */
.video-container.recording-fullscreen .video-content,
.video-container.recording-fullscreen .live-content {
  border-radius: 0 !important;
  object-fit: cover !important;
  position: absolute !important;
  top: 0 !important;
  left: 0 !important;
  width: 100% !important;
  height: 100% !important;
}

/* 视频刷新时的样式 */
.video-content.refreshing {
  opacity: 0.6;
}

/* 🔧 视频加载错误状态 */
.video-content.video-error {
  opacity: 0.5;
  border: 2rpx dashed #ff6b6b;
  background-color: rgba(255, 107, 107, 0.1);
}

/* 🎬 视频组件样式 */
.video-content {
  position: relative;
}

/* 视频刷新指示器 */
.video-refresh-indicator {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 10;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background-color: rgba(0, 0, 0, 0.6);
  border-radius: 12rpx;
  padding: 20rpx 30rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.2);
}

.refresh-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 5rpx solid rgba(255, 255, 255, 0.3);
  border-top: 5rpx solid #ffffff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 16rpx;
}

.refresh-text {
  color: #ffffff;
  font-size: 28rpx;
  font-weight: 500;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 检测区域框样式 */
.detection-areas {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 50; /* 确保检测区域框在视频之上 */
}

.detection-box {
  position: absolute;
  min-width: 60rpx;
  min-height: 60rpx;
  border: 6rpx solid #4a90e2; /* 使用与页面按钮相符的蓝色 */
  box-shadow: 0 0 10rpx rgba(124, 77, 255, 0.6); /* 紫色光晕，降低不透明度 */
  box-sizing: border-box;
  pointer-events: auto;
  touch-action: none;
  z-index: 51; /* 确保在detection-areas之上 */
  /* 优化拖动性能 */
  will-change: transform;
  transform: translateZ(0); /* 启用硬件加速 */
  -webkit-transform: translateZ(0);
  backface-visibility: hidden;
  -webkit-backface-visibility: hidden;
}

.area-label {
  position: absolute;
  top: -40rpx;
  left: 50%;
  transform: translateX(-50%);
  color: #ffffff; /* 白色文字 */
  font-size: 28rpx; /* 更大的字体 */
  font-weight: bold; /* 加粗 */
  background: linear-gradient(90deg, #78b9ff 0%, #c58eff 100%); /* 使用与按钮相同的渐变色 */
  padding: 6rpx 16rpx;
  border-radius: 20rpx;
  white-space: nowrap;
  box-shadow: 0 2rpx 10rpx rgba(90, 120, 213, 0.5); /* 添加阴影效果 */
  z-index: 52; /* 确保在detection-box之上 */
}

/* 调整大小的手柄 */
.resize-handle {
  position: absolute;
  width: 24rpx; /* 更大的手柄 */
  height: 24rpx; /* 更大的手柄 */
  background: linear-gradient(90deg, #4a90e2, #7c4dff); /* 使用蓝紫渐变色 */
  border: 3rpx solid #ffffff; /* 更粗的白色边框 */
  border-radius: 50%;
  pointer-events: auto;
  box-shadow: 0 0 8rpx rgba(124, 77, 255, 0.5); /* 添加紫色发光效果 */
  z-index: 53; /* 确保在area-label之上 */
  /* 优化调整大小性能 */
  will-change: transform;
  transform: translateZ(0);
  -webkit-transform: translateZ(0);
  backface-visibility: hidden;
  -webkit-backface-visibility: hidden;
}

.resize-handle.top-left {
  top: -10rpx;
  left: -10rpx;
  cursor: nw-resize;
}

.resize-handle.top-right {
  top: -10rpx;
  right: -10rpx;
  cursor: ne-resize;
}

.resize-handle.bottom-left {
  bottom: -10rpx;
  left: -10rpx;
  cursor: sw-resize;
}

.resize-handle.bottom-right {
  bottom: -10rpx;
  right: -10rpx;
  cursor: se-resize;
}

/* 拖动时的样式 */
.detection-box.dragging {
  opacity: 0.9;
  border-style: dashed;
  border-color: #7c4dff; /* 紫色虚线边框 */
  box-shadow: 0 0 20rpx rgba(124, 77, 255, 0.9); /* 增强拖动时的光晕效果 */
  transform: translateZ(0) scale(1.02); /* 轻微放大提供视觉反馈 */
  transition: none; /* 拖动时禁用过渡动画 */
  /* 优化拖动时的渲染性能 */
  contain: layout style paint;
  isolation: isolate;
}

/* 调整大小时的样式 */
.detection-box.resizing {
  opacity: 0.9;
  border-style: dashed;
  border-color: #4a90e2; /* 蓝色虚线边框 */
  box-shadow: 0 0 20rpx rgba(74, 144, 226, 0.9); /* 增强调整时的光晕效果 */
  transform: translateZ(0) scale(1.02); /* 轻微放大提供视觉反馈 */
  transition: none; /* 调整时禁用过渡动画 */
  /* 优化调整时的渲染性能 */
  contain: layout style paint;
  isolation: isolate;
}

.c-area {
  left: 30%;
  top: 60%;
}

.t-area {
  left: 60%;
  top: 60%;
}

/* 任务状态显示 */
.task-status-container {
  position: absolute;
  bottom: 20rpx;
  left: 20rpx;
  right: 20rpx;
  background-color: rgba(0, 0, 0, 0.6);
  border-radius: 8rpx;
  padding: 10rpx 20rpx;
  z-index: 100;
}

.task-status {
  display: flex;
  align-items: center;
  justify-content: space-between;
  color: white;
  font-size: 14px;
}

.task-status.processing {
  color: #ffeb3b;
}

.task-status.completed {
  color: #4caf50;
}

.task-status.failed {
  color: #f44336;
}

.task-status.timeout {
  color: #ff9800;
}

.status-indicator {
  width: 20rpx;
  height: 20rpx;
  border-radius: 50%;
  background-color: #ffeb3b;
  animation: blink 1s infinite;
}

@keyframes blink {
  0% {
    opacity: 0.4;
  }
  50% {
    opacity: 1;
  }
  100% {
    opacity: 0.4;
  }
}

/* 🎨 分析结果样式 - 与整体紫白色调保持一致 */
.analysis-result {
  padding: 20rpx;
  /* 🎨 修复：使用与内容区域一致的紫白色调背景 */
  background: linear-gradient(145deg, rgba(250, 248, 255, 0.95) 0%, rgba(244, 241, 253, 0.9) 100%);
  border-radius: 12rpx;
  margin-bottom: 20rpx;
  font-size: 28rpx;
  color: #333;
  /* 🎨 优化阴影色调，与紫色主题呼应 */
  box-shadow: 0 2rpx 10rpx rgba(120, 100, 220, 0.06), 0 1rpx 4rpx rgba(0, 0, 0, 0.03);
  /* 🎨 添加淡紫色边框，增强整体协调性 */
  border: 1px solid rgba(180, 170, 255, 0.2);
}

/* 控制按钮样式 */
.control-buttons, .action-buttons {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
  margin-bottom: 20rpx;
}

.control-buttons .control-btn, .action-buttons .action-btn {
  width: 100%;
  height: 80rpx;
  line-height: 80rpx;
  background: #4a90e2;
  color: #fff;
  font-size: 28rpx;
  border-radius: 8rpx;
}

/* 🧪 测试按钮使用普通按钮样式 - 移除特殊样式让其继承grid-button的所有效果 */

/* 🎨 内容区域统一样式 - 与整体紫白色调保持一致 */
.content-area {
  margin: auto;
  margin-top: 20rpx;
  margin-bottom: 20rpx; /* 减少底部外边距 */
  width: 710rpx; /* 调整宽度，确保其在容器内居中 */
  padding: 15rpx 15rpx 20rpx 15rpx; /* 减少底部内边距 */
  /* 🎨 修复：使用与按钮区域一致的紫白色调背景 */
  background: linear-gradient(145deg, rgba(248, 246, 255, 0.98) 0%, rgba(242, 239, 252, 0.95) 100%);
  border-radius: 40rpx;
  /* 🎨 优化阴影色调，与紫色主题呼应 */
  box-shadow: 0 4rpx 20rpx rgba(120, 100, 220, 0.08), 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
  /* 🎨 添加淡紫色边框，增强整体协调性 */
  border: 1px solid rgba(180, 170, 255, 0.25);
  position: relative;
  z-index: 1;
  box-sizing: border-box; /* 确保内容区域的box-sizing正确 */
  min-height: auto; /* 移除最小高度或设为auto，使其自适应内容 */
}

/* 🎨 内容区域装饰效果 - 与紫白色调协调 */
.content-area::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 50%;
  /* 🎨 使用淡紫色调的高光效果，与整体主题一致 */
  background: linear-gradient(to bottom,
    rgba(255, 255, 255, 0.35) 0%,
    rgba(250, 248, 255, 0.15) 50%,
    transparent 100%);
  border-radius: 40rpx 40rpx 0 0;
  z-index: 0;
}

/* 🎯 30fps MJPEG容器样式 */
.mjpeg-container {
  position: relative;
  width: 100%;
  height: 100%;
}

/* 🎯 30fps性能监控器样式 */
.fps-monitor {
  position: absolute;
  top: 20rpx;
  right: 20rpx;
  background: rgba(0, 0, 0, 0.8);
  border-radius: 12rpx;
  padding: 16rpx 20rpx;
  z-index: 100;
  min-width: 160rpx;
  backdrop-filter: blur(10rpx);
  border: 1rpx solid rgba(255, 255, 255, 0.1);
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.3);
}

.fps-text {
  display: block;
  color: #00ff88;
  font-size: 32rpx;
  font-weight: bold;
  font-family: 'Courier New', monospace;
  text-align: center;
  margin-bottom: 8rpx;
  text-shadow: 0 0 10rpx rgba(0, 255, 136, 0.5);
}

.fps-target {
  display: block;
  color: #888;
  font-size: 24rpx;
  text-align: center;
  margin-bottom: 12rpx;
}

.fps-bar {
  width: 100%;
  height: 8rpx;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 4rpx;
  overflow: hidden;
  position: relative;
}

.fps-progress {
  height: 100%;
  background: linear-gradient(90deg, #ff4444 0%, #ffaa00 50%, #00ff88 100%);
  border-radius: 4rpx;
  transition: width 0.3s ease;
  position: relative;
}

.fps-progress::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(90deg, transparent 0%, rgba(255, 255, 255, 0.3) 50%, transparent 100%);
  animation: fps-shine 2s ease-in-out infinite;
}

@keyframes fps-shine {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(100%); }
}

/* 默认模式样式 */
.default-mode {
  height: 100%;
  background: linear-gradient(145deg, rgba(255, 255, 255, 0.9) 0%, rgba(246, 244, 255, 0.9) 100%);
  border-radius: 20rpx;
  padding: 24rpx;
  box-sizing: border-box;
  position: relative;
  z-index: 1;
  border: 1px solid rgba(180, 170, 255, 0.15);
  /* 添加默认模式专用动画 */
  animation: smoothSlideIn 0.3s ease-out;
  transform-origin: center;
}

/* 图片区域布局 */
.default-images {
  display: flex;
  flex-direction: column;
  max-width: 100%;
  margin: 20rpx auto 0; /* 调整底部margin，因为标签现在外置 */
  position: relative;
}

/* 添加装饰元素 */
.default-images::before {
  content: '';
  position: absolute;
  top: -15rpx;
  right: -15rpx;
  width: 100rpx;
  height: 100rpx;
  background: radial-gradient(circle, rgba(120, 100, 220, 0.08) 0%, rgba(255, 255, 255, 0) 70%);
  border-radius: 50%;
  z-index: 0;
}

.image-row {
  display: flex;
  justify-content: space-between;
  gap: 80rpx;
}

/* 新增：包裹图片容器和标签的容器 */
.image-area-wrapper {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 30rpx; /* 控制每个图片区域（包括标签）下方的间距 */
  width: 300rpx; /* 设置固定宽度 */
  animation: parameterItemSlideIn 0.4s ease-out forwards;
  opacity: 0;
}

/* 为图片区域设置不同的延迟时间 */
.image-area-wrapper:nth-child(1) {
  animation-delay: 0.1s;
}

.image-area-wrapper:nth-child(2) {
  animation-delay: 0.2s;
}

/* 🎨 图片容器样式 - 与整体紫白色调保持一致 */
.image-container {
  position: relative;
  width: 280rpx;
  height: 280rpx;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  /* 🎨 修复：使用与内容区域协调的紫白色调背景 */
  background: linear-gradient(145deg, rgba(250, 248, 255, 0.95) 0%, rgba(244, 241, 253, 0.9) 100%);
  border-radius: 22rpx;
  margin: 0 auto;
  /* 🎨 保持原有的紫色调阴影效果 */
  box-shadow: 0 15rpx 35rpx rgba(120, 100, 220, 0.2), 0 5rpx 15rpx rgba(0, 0, 0, 0.05);
  padding: 15rpx;
  /* 🎨 稍微加深边框色，增强层次感 */
  border: 1px solid rgba(180, 170, 255, 0.35);
  box-sizing: border-box;
  z-index: 1;
  overflow: hidden;
  cursor: pointer;
  transition: transform 0.2s ease, opacity 0.2s ease;
}

.image-content {
  position: relative;
  width: 100%;
  height: 100%; /* 填充整个 image-container */
  display: flex;
  justify-content: center;
  align-items: center;
  overflow: hidden;
  border-radius: 12rpx;
  background-color: transparent;
}

.analysis-image {
  width: 100%;
  height: 100%;
  object-fit: contain;
  position: static;
  /* 更平滑的过渡效果 */
  transition: all 0.5s cubic-bezier(0.42, 0, 0.58, 1); /* 标准ease-in-out */
  will-change: transform;
}

/* 遮罩层样式 */
.mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.7);
  z-index: 9000; /* 降低遮罩层z-index */
  display: none;
  opacity: 0;
  transition: opacity 0.3s ease-in-out;
}

/* 显示遮罩层 */
.mask.show {
  display: block;
  opacity: 1;
}

/* 图片悬停效果 */
.image-hover {
  transform: scale(0.95);
  opacity: 0.9;
  transition: all 0.2s ease;
}

/* 图片遮罩层 */
.image-mask {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: rgba(0, 0, 0, 0);
  z-index: 9998;
  opacity: 0;
  visibility: hidden;
  transition: opacity 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94),
              visibility 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94),
              background-color 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  will-change: opacity, background-color;
  transform: translateZ(0); /* 启用硬件加速 */
}

.image-mask.show {
  opacity: 1;
  visibility: visible;
  background: rgba(0, 0, 0, 0.8);
}

/* 图片放大容器 */
.image-container.enlarged {
  position: fixed;
  top: 50%;
  left: 50%;
  width: 85vw;
  max-width: 750rpx;
  height: 85vh;
  max-height: 750rpx;
  background-color: rgba(255, 255, 255, 0.95);
  z-index: 9999;
  border-radius: 24rpx;
  overflow: visible;
  box-shadow: 0 20rpx 60rpx rgba(0, 0, 0, 0.2), 0 8rpx 25rpx rgba(0, 0, 0, 0.1);
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 0;
  /* 初始状态 - 缩小、透明 */
  transform: translate(-50%, -50%) scale(0.9);
  opacity: 0;
  transition: transform 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94),
              opacity 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  will-change: transform, opacity;
  backface-visibility: hidden;
  -webkit-backface-visibility: hidden;
  border: 2rpx solid rgba(255, 255, 255, 0.8);
}

.image-container.enlarged.show {
  transform: translate(-50%, -50%) scale(1);
  opacity: 1;
}

/* 放大后的图片内容区域 */
.image-container.enlarged .image-content {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: transparent;
  position: relative;
  z-index: 1;
  overflow: hidden;
  border-radius: 20rpx;
  padding: 20rpx;
  box-sizing: border-box;
  cursor: pointer;
  transition: all 0.2s ease;
}

/* 🔧 移除放大状态下的active样式，避免与关闭动画冲突 */
/* .image-container.enlarged .image-content:active {
  transform: scale(0.98);
} */

/* 放大后的图片 */
.image-container.enlarged .analysis-image {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
  border-radius: 16rpx;
  box-shadow: 0 8rpx 25rpx rgba(0, 0, 0, 0.15);
  transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  pointer-events: none; /* 防止图片本身接收点击事件 */
}



/* 🎨 图片容器内部光泽效果 - 与紫白色调协调 */
.image-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 35%;
  /* 🎨 使用淡紫色调的高光效果，与新背景色协调 */
  background: linear-gradient(to bottom,
    rgba(255, 255, 255, 0.45) 0%,
    rgba(252, 250, 255, 0.25) 50%,
    transparent 100%);
  border-radius: 22rpx 22rpx 0 0;
  z-index: 0;
}

/* 🔧 移除图片容器的:active样式，避免与hover-class冲突 */

.image-container-item, .analysis-image {
  width: 100%;
  height: 100%;
  display: block;
  border-radius: 12rpx;
  object-fit: contain;
  position: relative;
  z-index: 1;
}

/* 为空图标添加提示动画 */
.image-content .empty-indicator {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  background: #ff9500;
  display: flex;
  justify-content: center;
  align-items: center;
  color: white;
  font-size: 60rpx;
  font-weight: bold;
  box-shadow: 0 6rpx 16rpx rgba(255, 149, 0, 0.3);
  position: relative;
  overflow: hidden;
}

/* 波纹效果 */
.empty-indicator::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 120%;
  height: 120%;
  background: radial-gradient(circle, rgba(255,255,255,0.8) 0%, rgba(255,255,255,0) 70%);
  transform: translate(-50%, -50%);
  opacity: 0;
  animation: ripple 2s infinite;
}

@keyframes ripple {
  0% {
    opacity: 0;
    transform: translate(-50%, -50%) scale(0.8);
  }
  50% {
    opacity: 0.2;
    transform: translate(-50%, -50%) scale(1);
  }
  100% {
    opacity: 0;
    transform: translate(-50%, -50%) scale(1.2);
  }
}

/* 图片容器外的标签容器样式 */
.image-label-container {
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-top: 30rpx; /* 进一步增加与图片容器的间距 */
  height: auto; /* 高度自适应内容 */
}

/* 区域标签样式 */
.image-label {
  color: #30187A;
  font-size: 30rpx; /* 稍微调大字体 */
  font-weight: 600;
  background: linear-gradient(145deg, #FFFFFF 0%, #F9F8FD 100%);
  padding: 12rpx 30rpx; /* 调整内边距 */
  border-radius: 18rpx; /* 调整圆角 */
  box-shadow: 0 6rpx 15rpx rgba(120, 100, 220, 0.15), 0 3rpx 8rpx rgba(0, 0, 0, 0.05);
  white-space: nowrap;
  border: 1px solid rgba(180, 170, 255, 0.3);
}

.info-label {
  color: #333;
  min-width: 140rpx;
  margin-right: 10rpx;
  font-weight: 500;
}

.close-btn {
  position: absolute;
  top: 20rpx;
  right: 20rpx;
  width: 60rpx;
  height: 60rpx;
  background: rgba(0, 0, 0, 0.5);
  color: #ffffff;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 40rpx;
  font-weight: bold;
  z-index: 1001;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.2);
  transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  overflow: hidden;
}

/* 关闭按钮波纹效果 */
.close-btn::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.3);
  transform: translate(-50%, -50%);
  transition: width 0.6s ease, height 0.6s ease;
  z-index: 1;
}

/* 🎯 移除关闭按钮的:active样式，避免与hover-class冲突 */

/* ECL值显示区域样式 */
.ecl-section {
  position: relative;
  background: linear-gradient(145deg, rgba(250, 250, 255, 0.95) 0%, rgba(240, 240, 255, 0.95) 100%);
  border-radius: 20rpx;
  padding: 12rpx 20rpx 15rpx;
  margin-top: 25rpx;
  margin-bottom: 5rpx;
  box-shadow: 0 4rpx 15rpx rgba(100, 100, 200, 0.08);
  border: 1rpx solid rgba(200, 200, 255, 0.3);
  overflow: hidden;
  animation: parameterItemSlideIn 0.4s ease-out forwards;
  animation-delay: 0.3s;
  opacity: 0;
}

/* 添加ECL值区域内部顶部光泽效果 - 改用新名称避免冲突 */
.ecl-top-gloss {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 30%;
  background: linear-gradient(to bottom, rgba(255, 255, 255, 0.8), rgba(255, 255, 255, 0));
  border-radius: 22rpx 22rpx 0 0;
  z-index: 0;
  pointer-events: none;
}

/* 添加ECL值区域顶部装饰 */
.ecl-section::before {
  content: '';
  position: absolute;
  top: -20rpx;
  right: -20rpx;
  width: 100rpx;
  height: 100rpx;
  background: radial-gradient(circle, rgba(120, 100, 220, 0.1) 0%, rgba(255, 255, 255, 0) 70%);
  border-radius: 50%;
  z-index: 0;
}

/* 添加底部平滑过渡效果 */
.ecl-bottom-fade {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 80rpx; /* 增加高度覆盖更多区域 */
  background: linear-gradient(to top, 
    rgba(241, 239, 252, 1) 0%, 
    rgba(243, 241, 254, 0.98) 20%, 
    rgba(243, 241, 254, 0.9) 40%,
    rgba(245, 243, 255, 0.7) 60%, 
    rgba(246, 244, 255, 0.3) 80%,
    rgba(246, 244, 255, 0) 100%);
  z-index: 2;
  pointer-events: none;
  border-radius: 0 0 22rpx 22rpx;
  /* 添加轻微阴影增强立体感 */
  box-shadow: inset 0 -5rpx 10rpx -10rpx rgba(180, 170, 255, 0.1);
}

/* 增加底部间距，让内容不被底部过渡效果遮挡 */
.ecl-values {
  display: flex;
  flex-direction: column;
  gap: 18rpx; /* 增加项目间距 */
  position: relative;
  z-index: 1;
  padding-bottom: 30rpx; /* 增加底部内边距 */
}

/* 重复的ECL值标签样式已移除，使用后面的统一定义 */

/* 修改value-item样式，增强视觉效果 */
.value-item {
  display: flex;
  align-items: center;
  margin-bottom: 15rpx; /* 增加底部间距 */
  background: rgba(255, 255, 255, 0.7);
  border-radius: 12rpx; /* 减小圆角 */
  padding: 16rpx 20rpx; /* 增加内边距 */
  box-shadow: 0 2rpx 6rpx rgba(100, 100, 200, 0.05);
}

/* 移除最后一个item的底部外边距，避免褶皱 */
.value-item:last-child {
  margin-bottom: 0;
  /* 降低最后一项的边框透明度，更好地融入底部渐变 */
  border-color: rgba(180, 170, 255, 0.2);
}

/* 添加value-item内部光泽效果 */
.value-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 50%;
  background: linear-gradient(to bottom, rgba(255, 255, 255, 0.6), rgba(255, 255, 255, 0));
  z-index: -1;
}

/* 添加灰白交错背景 - 保持协调的渐变背景 */
.value-item:nth-child(odd) {
  background: linear-gradient(145deg, rgba(246, 244, 255, 0.95), rgba(241, 239, 252, 0.95));
  box-shadow: 0 6rpx 15rpx rgba(120, 100, 220, 0.1);
}

.value-item:nth-child(even) {
  background: linear-gradient(145deg, rgba(248, 246, 255, 0.9), rgba(243, 241, 252, 0.9));
  box-shadow: 0 6rpx 15rpx rgba(120, 100, 220, 0.08);
}

/* 🎯 移除ECL值项的:active样式，避免与hover-class冲突 */

.value-label {
  width: 90rpx; /* 减小宽度 */
  font-size: 28rpx; /* 减小字体 */
  color: #30187A;
  font-weight: 500;
}

.value-text {
  flex: 1;
  font-size: 28rpx; /* 减小字体 */
  color: #333333;
  font-weight: 500;
}

/* 加载中文本样式 */
.loading-text {
  color: #999;
  position: relative;
  display: inline-block;
  animation: pulse-text 1.5s infinite;
}

/* 加载动画 */
@keyframes pulse-text {
  0%, 100% {
    opacity: 0.6;
  }
  50% {
    opacity: 1;
  }
}

/* 默认模式渐入动画 */
@keyframes defaultModeIn {
  0% {
    opacity: 0;
    transform: translateY(15rpx) scale(0.98);
    filter: blur(1rpx);
  }
  100% {
    opacity: 1;
    transform: translateY(0) scale(1);
    filter: blur(0);
  }
}

/* 轻量级流畅渐入动画 */
@keyframes smoothSlideIn {
  0% {
    opacity: 0;
    transform: translateY(20rpx);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 参数项逐个出现动画 */
@keyframes parameterItemSlideIn {
  0% {
    opacity: 0;
    transform: translateY(30rpx) scale(0.95);
  }
  100% {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

/* 参数项逐个消失动画 */
@keyframes parameterItemSlideOut {
  0% {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
  100% {
    opacity: 0;
    transform: translateY(-20rpx) scale(0.95);
  }
}

/* 优化的淡出动画关键帧 */
@keyframes simpleFadeOut {
  0% {
    opacity: 1;
    transform: translateY(0);
  }
  100% {
    opacity: 0;
    transform: translateY(-10rpx);
  }
}

/* 淡出动画类 */
.fade-out {
  animation: simpleFadeOut 0.25s ease-out forwards;
}

/* 参数项淡出动画类 */
.fade-out .parameter-item {
  animation: parameterItemSlideOut 0.3s ease-in forwards;
}

/* 为淡出时的参数项设置不同的延迟时间，实现逐个消失效果 */
.fade-out .parameter-item:nth-child(1) {
  animation-delay: 0s;
}

.fade-out .parameter-item:nth-child(2) {
  animation-delay: 0.05s;
}

.fade-out .parameter-item:nth-child(3) {
  animation-delay: 0.1s;
}

.fade-out .parameter-item:nth-child(4) {
  animation-delay: 0.15s;
}

.fade-out .parameter-item:nth-child(5) {
  animation-delay: 0.2s;
}

.fade-out .parameter-item:nth-child(6) {
  animation-delay: 0.25s;
}

.fade-out .parameter-item:nth-child(7) {
  animation-delay: 0.3s;
}

.fade-out .parameter-item:nth-child(8) {
  animation-delay: 0.35s;
}

.fade-out .parameter-item:nth-child(9) {
  animation-delay: 0.4s;
}

.fade-out .parameter-item:nth-child(10) {
  animation-delay: 0.45s;
}

/* 默认模式子元素淡出动画 */
.fade-out .image-area-wrapper {
  animation: parameterItemSlideOut 0.3s ease-in forwards;
}

.fade-out .image-area-wrapper:nth-child(1) {
  animation-delay: 0s;
}

.fade-out .image-area-wrapper:nth-child(2) {
  animation-delay: 0.05s;
}

.fade-out .ecl-section {
  animation: parameterItemSlideOut 0.3s ease-in forwards;
  animation-delay: 0.1s;
}

/* 参数模式样式 */
.parameter-mode {
  background: linear-gradient(160deg, #ffffff 0%, #f5f3fc 100%);
  border-radius: 22rpx;
  padding: 30rpx;
  width: 100%;
  box-sizing: border-box;
  box-shadow: 0 8rpx 16rpx rgba(120, 100, 220, 0.1);
  display: flex;
  flex-direction: column;
  height: auto;
  min-height: 800rpx;
  transition: all 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

/* 参数模式展开时向上移动覆盖按钮区域 */
.parameter-mode-expanded {
  margin-top: 0; /* 不向上移动，保持在原位置 */
  height: 600rpx; /* 设置合理的固定高度，实现独立滚动 */
  z-index: 10; /* 确保在其他元素之上 */
  position: relative; /* 确保定位正确 */
  overflow: hidden; /* 隐藏外部滚动 */
}

/* 参数调节状态提示样式 */
.parameter-status-tip {
  background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
  border: 2rpx solid #ffc107;
  border-radius: 16rpx;
  padding: 24rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 4rpx 12rpx rgba(255, 193, 7, 0.2);
  position: relative;
  z-index: 2;
}

.parameter-status-tip.success {
  background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
  border-color: #28a745;
  box-shadow: 0 4rpx 12rpx rgba(40, 167, 69, 0.2);
}

.parameter-status-tip .status-text {
  font-size: 28rpx;
  color: #856404;
  font-weight: 500;
  line-height: 1.4;
  display: block;
}

.parameter-status-tip.success .status-text {
  color: #155724;
  border: 1rpx solid rgba(180, 170, 255, 0.3);
  position: relative;
  overflow: hidden;
  margin-bottom: 0;
  /* 恢复动画效果，但使用性能更好的实现 */
  animation: optimized-slide-in 0.25s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  transform-origin: center;
  will-change: transform, opacity;
}

/* 优化的滑入动画 */
@keyframes optimized-slide-in {
  0% {
    opacity: 0.6;
    transform: translateY(10rpx);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

.parameter-item {
  margin-bottom: 50rpx;
  position: relative;
  padding-bottom: 30rpx;
  /* 使用更轻量的动画效果 */
  animation: optimized-fade-in 0.4s ease-out forwards;
  opacity: 0;
}

/* 优化的淡入动画 */
@keyframes optimized-fade-in {
  0% {
    opacity: 0;
    transform: translateY(5rpx);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 为不同的参数项设置不同的延迟时间，保留逐个出现效果但减少延迟时间 */
.parameter-item:nth-child(1) {
  animation-delay: 0.05s;
}

.parameter-item:nth-child(2) {
  animation-delay: 0.1s;
}

.parameter-item:nth-child(3) {
  animation-delay: 0.15s;
}

.parameter-item:nth-child(4) {
  animation-delay: 0.2s;
}

.parameter-item:nth-child(5) {
  animation-delay: 0.25s;
}

.parameter-item:nth-child(6) {
  animation-delay: 0.3s;
}

.parameter-item:nth-child(7) {
  animation-delay: 0.35s;
}

.parameter-item:nth-child(8) {
  animation-delay: 0.4s;
}

.parameter-item:nth-child(9) {
  animation-delay: 0.45s;
}

.parameter-item:nth-child(10) {
  animation-delay: 0.5s;
}

.parameter-mode::before {
  content: '';
  position: absolute;
  top: -100rpx;
  right: -100rpx;
  width: 300rpx;
  height: 300rpx;
  background: radial-gradient(circle, rgba(120, 100, 220, 0.05) 0%, rgba(255, 255, 255, 0) 70%);
  border-radius: 50%;
  z-index: 0;
}

/* 简化内部光泽效果 */
.parameter-mode::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 20%;
  background: linear-gradient(to bottom, rgba(255, 255, 255, 0.5), rgba(255, 255, 255, 0));
  border-radius: 22rpx 22rpx 0 0;
  z-index: 0;
}

.parameter-list {
  flex: 1;
  height: 100%;
  padding-bottom: 20rpx;
  overflow-y: auto;
  position: relative;
  z-index: 1;
  /* 优化滚动性能 */
  -webkit-overflow-scrolling: touch;
  /* 消除滚动延迟 */
  will-change: scroll-position;
}

/* 参数模式展开时的滚动列表样式 */
.parameter-mode-expanded .parameter-list {
  height: calc(100% - 100rpx); /* 减去状态提示的高度 */
  max-height: 500rpx; /* 设置合理的最大高度 */
  overflow-y: scroll; /* 确保可以滚动 */
}

.parameter-item {
  margin-bottom: 50rpx;
  position: relative;
  padding-bottom: 30rpx;
  /* 移除逐项动画，提高响应速度 */
  animation: none;
  opacity: 1;
}

/* 移除逐个项目的延迟动画 */
.parameter-item:nth-child(1),
.parameter-item:nth-child(2),
.parameter-item:nth-child(3),
.parameter-item:nth-child(4),
.parameter-item:nth-child(5),
.parameter-item:nth-child(6),
.parameter-item:nth-child(7),
.parameter-item:nth-child(8),
.parameter-item:nth-child(9),
.parameter-item:nth-child(10) {
  animation-delay: 0s;
}

.parameter-label {
  font-size: 30rpx;
  color: #30187A;
  font-weight: 600;
  margin-bottom: 28rpx;
  position: relative;
  padding-left: 24rpx;
  display: flex;
  align-items: center;
}

.parameter-label::before {
  content: '';
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 6rpx;
  height: 26rpx;
  background: linear-gradient(to bottom, #7667F5, #6668FF);
  border-radius: 3rpx;
  box-shadow: 0 0 6rpx rgba(120, 100, 220, 0.2);
}

/* 参数控制区域整体布局 */
.parameter-control {
  display: flex;
  align-items: center;
  width: 100%;
  position: relative;
  height: 90rpx;
  margin: 0 auto;
  overflow: visible;
}

/* 滑块样式 */
.parameter-slider {
  flex: 1;
  margin: 0 15rpx;
  max-width: 440rpx;
  padding-left: 15rpx; /* 增加左侧内边距，防止滑块与减号按钮重叠 */
}

/* 加减按钮样式 */
.minus-btn, .plus-btn {
  width: 66rpx;
  height: 66rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 38rpx;
  color: #4a90e2;
  background: #ffffff;
  border-radius: 50%;
  font-weight: 600;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);
  border: 1rpx solid rgba(74, 144, 226, 0.1);
  flex-shrink: 0;
  position: relative;
  overflow: hidden;
  transition: all 0.5s cubic-bezier(0.25, 0.1, 0.25, 1), transform 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  will-change: transform, box-shadow, background, border-color;
}

/* 加减按钮波纹效果 */
.minus-btn::before, .plus-btn::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  border-radius: 50%;
  background: rgba(74, 144, 226, 0.2);
  transform: translate(-50%, -50%);
  opacity: 0;
  z-index: 1;
}

/* 🎯 移除加减按钮的:active样式，避免与hover-class冲突 */

@keyframes button-ripple {
  0% {
    width: 0;
    height: 0;
    opacity: 0.5;
  }
  70% {
    width: 120rpx;
    height: 120rpx;
    opacity: 0.2;
  }
  100% {
    width: 140rpx;
    height: 140rpx;
    opacity: 0;
  }
}

/* 输入框动画状态类 */
.input-animation-wrapper {
  position: relative;
  width: 100rpx;
  height: 64rpx;
  margin: 0 25rpx;
  flex-shrink: 0;
  z-index: 5;
  overflow: visible;
}

/* 值容器样式 */
.current-value-container {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  border-radius: 32rpx;
  background: #ffffff;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
  border: 1rpx solid rgba(74, 144, 226, 0.1);
  box-sizing: border-box;
  z-index: 5;
  display: flex;
  justify-content: center;
  align-items: center;
  transition: all 0.5s cubic-bezier(0.25, 0.1, 0.25, 1), opacity 0.3s ease;
  position: relative;
  overflow: hidden;
  opacity: 1;
}

/* 隐藏时的样式 */
.current-value-container.hidden {
  opacity: 0;
  pointer-events: none;
}

/* 数字样式 */
.current-value {
  font-size: 30rpx;
  color: #4a90e2;
  font-weight: 500;
  text-align: center;
  line-height: 1;
  margin: 0;
  padding: 0;
  transition: color 0.4s ease-in-out;
}

/* 当容器有动画时添加的样式 */
.current-value-container.animated {
  background-color: #ebf3ff;
  border-color: #4a90e2;
  box-shadow: 0 2rpx 10rpx rgba(74, 144, 226, 0.25);
}

/* 波纹效果 - 在点击时显示 */
.current-value-container:before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  background: rgba(74, 144, 226, 0.15);
  border-radius: 50%;
  transform: translate(-50%, -50%);
  opacity: 0;
  z-index: -1;
}

.current-value-container.animated:before {
  animation: ripple 0.8s cubic-bezier(0.25, 0.8, 0.25, 1);
}

@keyframes ripple {
  0% {
    width: 0;
    height: 0;
    opacity: 0.5;
  }
  70% {
    width: 180%;
    height: 180%;
    opacity: 0.2;
  }
  100% {
    width: 200%;
    height: 200%;
    opacity: 0;
  }
}

/* 完全隐藏编辑提示文本 */
.edit-hint {
  display: none;
}

/* 输入框样式 */
.current-value-input {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  font-size: 30rpx;
  color: #4a90e2;
  background-color: #ebf3ff;
  border-radius: 32rpx;
  text-align: center;
  border: 2rpx solid #4a90e2;
  box-shadow: 0 4rpx 12rpx rgba(74, 144, 226, 0.15);
  box-sizing: border-box;
  padding: 0;
  margin: 0;
  z-index: 20;
  line-height: 60rpx;
  opacity: 1;
  transform: translateY(0);
  transition: opacity 0.3s ease, transform 0.3s ease;
}

/* 隐藏时的输入框样式 */
.current-value-input.hidden {
  opacity: 0;
  transform: translateY(4rpx);
  pointer-events: none;
}

/* 浓度设置容器 */
.concentration-container {
  background: rgba(245, 248, 255, 0.8);
  border-radius: 16rpx;
  padding: 30rpx;
  margin-top: 5rpx;
  margin-bottom: 30rpx;
  border: 1rpx solid rgba(74, 144, 226, 0.1);
}

/* 快速选择按钮 */
.quick-select-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
  margin-top: 16rpx;
}

.quick-btn {
  padding: 14rpx 30rpx;
  background: #ffffff;
  color: #4a90e2;
  border-radius: 100rpx;
  font-size: 28rpx;
  text-align: center;
  transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  font-weight: 500;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
  border: 1rpx solid rgba(74, 144, 226, 0.1);
  position: relative;
  overflow: hidden;
}

/* 快速选择按钮波纹效果 */
.quick-btn::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  border-radius: 50%;
  background: rgba(74, 144, 226, 0.15);
  transform: translate(-50%, -50%);
  transition: width 0.6s ease, height 0.6s ease;
  z-index: 1;
}

/* 🎯 移除快速选择按钮的:active样式，避免与hover-class冲突 */

/* 浓度输入区域 */
.concentration-input-area {
  display: flex;
  align-items: center;
  background: #ffffff;
  padding: 10rpx 20rpx;
  border-radius: 100rpx;
  border: 1px solid rgba(74, 144, 226, 0.1);
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
  margin-top: 20rpx;
}

.concentration-input {
  flex: 1;
  height: 68rpx;
  font-size: 28rpx;
  color: #333;
  padding: 0 10rpx;
}

.unit-text {
  font-size: 26rpx;
  color: #666;
  padding: 8rpx 20rpx;
  margin-left: 8rpx;
  background: rgba(245, 248, 255, 0.9);
  border-radius: 32rpx;
  border: 1rpx solid rgba(74, 144, 226, 0.05);
}

/* 参数按钮区域 */
.parameter-buttons {
  display: flex;
  justify-content: space-between;
  margin-top: 40rpx;
  padding-top: 30rpx;
  border-top: 1rpx solid rgba(0, 0, 0, 0.05);
}

.reset-btn, .save-btn {
  width: 48%;
  height: 88rpx;
  line-height: 88rpx;
  text-align: center;
  border-radius: 44rpx;
  font-size: 30rpx;
  margin: 0;
  font-weight: 500;
  letter-spacing: 2rpx;
  border: none;
  position: relative;
  overflow: hidden;
  transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.reset-btn {
  background: #f5f7fa;
  color: #666;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
  border: 1rpx solid rgba(0, 0, 0, 0.08);
}

.save-btn {
  background: linear-gradient(90deg, #4a90e2, #5b86e5);
  color: #fff;
  box-shadow: 0 4rpx 12rpx rgba(74, 144, 226, 0.2);
}

/* 参数按钮波纹效果 */
.reset-btn::before, .save-btn::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  border-radius: 50%;
  transform: translate(-50%, -50%);
  transition: width 0.6s ease, height 0.6s ease;
  z-index: 1;
}

.reset-btn::before {
  background: rgba(0, 0, 0, 0.1);
}

.save-btn::before {
  background: rgba(255, 255, 255, 0.3);
}

/* 🎯 移除重置和保存按钮的:active样式，避免与hover-class冲突 */

/* 查询模式样式 */
.search-mode {
  height: 100%;
  background: #fff;
  border-radius: 12rpx;
  padding: 20rpx;
  box-sizing: border-box;
}

.device-list {
  height: 100%;
}

.device-item {
  display: flex;
  padding: 20rpx;
  background: #fff;
  border-radius: 12rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.device-item-image {
  width: 200rpx;
  height: 200rpx;
  border-radius: 8rpx;
  margin-right: 20rpx;
  background: #f8f9fa;
}

.device-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.info-line {
  font-size: 26rpx;
  color: #333;
  margin-bottom: 10rpx;
  display: flex;
  align-items: center;
  position: relative;
  z-index: 100;
  background: rgba(255, 255, 255, 0.8);
  padding: 4rpx 8rpx;
  border-radius: 4rpx;
}

/* 进度面板样式 */
.progress-panel {
  position: fixed;
  bottom: calc(110rpx + env(safe-area-inset-bottom) + 30rpx);
  left: 30rpx;
  right: 30rpx;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 16rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.15);
  z-index: 999;
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border: 1rpx solid rgba(255, 255, 255, 0.2);
  animation: slideUp 0.3s ease-out;
}

@keyframes slideUp {
  from {
    transform: translateY(100%);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

.progress-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.progress-title {
  font-size: 30rpx;
  font-weight: 600;
  color: #333;
}

.progress-percent {
  font-size: 28rpx;
  color: #4a90e2;
  font-weight: 500;
}

.progress-bar-container {
  width: 100%;
  height: 10rpx;
  background: #f0f0f0;
  border-radius: 10rpx;
  overflow: hidden;
  margin-bottom: 20rpx;
}

.progress-bar {
  height: 100%;
  background: linear-gradient(90deg, #4a90e2, #7c4dff);
  border-radius: 10rpx;
  transition: width 0.3s ease;
}

/* 上传进度面板样式 */
.upload-progress-panel {
  border-left: 4rpx solid #ff9800;
}

.upload-progress-panel .progress-title {
  color: #ff9800;
}

.upload-progress-panel .progress-percent {
  color: #ff9800;
}

.upload-progress-style {
  background: linear-gradient(90deg, #ff9800, #ffb74d);
}

.progress-details {
  margin-bottom: 20rpx;
}

.progress-item {
  display: flex;
  margin-bottom: 10rpx;
}

.item-label {
  font-size: 24rpx;
  color: #666;
  width: 160rpx;
  margin-right: 10rpx;
}

.item-value {
  font-size: 24rpx;
  color: #333;
  flex: 1;
}

.progress-actions {
  display: flex;
  justify-content: flex-end;
}

.cancel-btn {
  width: 180rpx;
  height: 70rpx;
  line-height: 70rpx;
  background: #f5f7fa;
  color: #666;
  font-size: 26rpx;
  border-radius: 35rpx;
  border: 1rpx solid #e0e0e0;
  text-align: center;
}

.cancel-btn-hover {
  background: #eaeef3;
}

/* 分析状态卡片样式 */
.analysis-status-container {
  position: fixed;
  bottom: 220rpx;
  left: 30rpx;
  right: 30rpx;
  z-index: 998;
  display: flex;
  justify-content: center;
  padding-bottom: 30rpx;
}

.analysis-status-card {
  width: 90%;
  background: rgba(255, 255, 255, 0.98);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border-radius: 24rpx;
  padding: 36rpx;
  box-shadow: 0 15rpx 40rpx rgba(0, 0, 0, 0.12), 0 6rpx 16rpx rgba(0, 0, 0, 0.1);
  border: 1rpx solid rgba(255, 255, 255, 0.9);
  animation: slideUpCard 0.4s cubic-bezier(0.16, 1, 0.3, 1);
  transform-origin: center bottom;
  position: relative;
}

/* 底部阴影效果，增强与底部内容的视觉分离 */
.analysis-status-card::after {
  content: '';
  position: absolute;
  bottom: -20rpx;
  left: 5%;
  right: 5%;
  height: 20rpx;
  background: radial-gradient(ellipse at center, rgba(0,0,0,0.12) 0%, rgba(0,0,0,0) 70%);
  border-radius: 50%;
  z-index: -1;
  filter: blur(3px);
}

/* 添加进入动画 - 独立命名避免冲突 */
@keyframes slideUpCard {
  from {
    opacity: 0;
    transform: translateY(60rpx) scale(0.97);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.status-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24rpx;
  border-bottom: 1px solid rgba(0,0,0,0.03);
  padding-bottom: 20rpx;
}

.status-title {
  font-size: 34rpx;
  font-weight: 600;
  color: #333;
  letter-spacing: 0.5px;
}

.status-badge {
  font-size: 24rpx;
  padding: 8rpx 20rpx;
  border-radius: 30rpx;
  font-weight: 500;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}

.status-processing {
  background: linear-gradient(135deg, rgba(74, 144, 226, 0.15), rgba(74, 144, 226, 0.05));
  color: #4a90e2;
  border: 1px solid rgba(74, 144, 226, 0.15);
}

.status-success {
  background: linear-gradient(135deg, rgba(76, 175, 80, 0.15), rgba(76, 175, 80, 0.05));
  color: #4caf50;
  border: 1px solid rgba(76, 175, 80, 0.15);
}

.status-error {
  background: linear-gradient(135deg, rgba(244, 67, 54, 0.15), rgba(244, 67, 54, 0.05));
  color: #f44336;
  border: 1px solid rgba(244, 67, 54, 0.15);
}

.status-progress {
  margin: 24rpx 0;
  position: relative;
  overflow: hidden;
  border-radius: 6rpx;
}

/* 优化进度条样式 */
.status-progress .progress-style {
  border-radius: 8rpx;
  overflow: hidden;
}

.status-message {
  font-size: 28rpx;
  color: #555;
  margin: 24rpx 0;
  line-height: 1.6;
  padding-left: 10rpx;
}

.status-actions {
  display: flex;
  justify-content: flex-end;
  gap: 24rpx;
  margin-top: 30rpx;
}

.action-button {
  min-width: 180rpx;
  height: 76rpx;
  line-height: 76rpx;
  font-size: 28rpx;
  border-radius: 38rpx;
  text-align: center;
  transition: all 0.3s ease;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);
  font-weight: 500;
}

/* 🎯 移除操作按钮的:active样式，避免与hover-class冲突 */

.cancel-button {
  background: linear-gradient(to bottom, #fafbfc, #f5f7fa);
  color: #666;
  border: 1rpx solid #e0e0e0;
}

.retry-button {
  background: linear-gradient(to bottom, #fff7eb, #fff2e5);
  color: #ff9800;
  border: 1rpx solid #ffe0bd;
}

.view-button {
  background: linear-gradient(to bottom, #eaf5fe, #e3f2fd);
  color: #4a90e2;
  border: 1rpx solid #c9e3fc;
}

/* 隐藏Canvas样式 */
.hidden-canvas {
  width: 1px;
  height: 1px;
  position: absolute;
  opacity: 0;
  overflow: hidden;
  visibility: hidden;
  display: none;
  pointer-events: none;
}

/* 自定义底部导航栏 */
.custom-tabbar {
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 110rpx;
  display: flex;
  background: linear-gradient(145deg, #FFFFFF 0%, #F6F4FF 100%);
  /* 加强阴影，使其更为明显 */
  box-shadow: 0 -8rpx 30rpx rgba(180, 170, 255, 0.2);
  z-index: 1000;
  padding-bottom: env(safe-area-inset-bottom);
  /* 添加顶部过渡效果 */
  border-top-left-radius: 30rpx;
  border-top-right-radius: 30rpx;
  /* 增加一个半透明边框增强立体感 */
  border-top: 2rpx solid rgba(255, 255, 255, 0.9);
  /* 添加轻微模糊效果增强视觉融合 */
  backdrop-filter: blur(10rpx);
  -webkit-backdrop-filter: blur(10rpx);
}

.tab-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  position: relative;
  transition: all 0.3s cubic-bezier(0.25, 0.1, 0.25, 1);
  padding-bottom: 2rpx;
}

.tab-text {
  font-size: 22rpx;
  color: #999;
  margin-top: 10rpx;
  font-weight: 500;
  transition: color 0.3s ease;
}

.tab-item.active .tab-text {
  color: #4a90e2;
}

/* 标签项指示器 */
.tab-item-indicator {
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%) scale(0);
  width: 28rpx;
  height: 4rpx;
  background: linear-gradient(90deg, #7F7FD5, #91EAE4);
  border-radius: 4rpx;
  transition: all 0.3s cubic-bezier(0.25, 0.1, 0.25, 1);
  opacity: 0;
}

.tab-item.active .tab-item-indicator {
  transform: translateX(-50%) scale(1);
  opacity: 1;
}

/* CSS 图标 - 主界面 */
.tab-icon-home {
  position: relative;
  width: 54rpx;
  height: 48rpx;
  display: flex;
  justify-content: center;
  align-items: center;
}

/* 房子屋顶 */
.tab-icon-home-roof {
  position: absolute;
  top: 0;
  left: 0;
  width: 0;
  height: 0;
  border-left: 27rpx solid transparent;
  border-right: 27rpx solid transparent;
  border-bottom: 24rpx solid #8a8a8a;
  transition: border-bottom-color 0.3s ease;
}

/* 房子主体 */
.tab-icon-home-body {
  position: absolute;
  top: 20rpx;
  left: 6rpx;
  width: 42rpx;
  height: 28rpx;
  background-color: #8a8a8a;
  border-radius: 0 0 4rpx 4rpx;
  transition: background-color 0.3s ease;
}

/* 房子门 */
.home-door {
  position: absolute;
  bottom: 0;
  left: 18rpx;
  width: 18rpx;
  height: 18rpx;
  background-color: white;
  border-radius: 3rpx 3rpx 0 0;
  z-index: 1;
}

/* 房子窗户 */
.home-window {
  position: absolute;
  top: 24rpx;
  left: 30rpx;
  width: 10rpx;
  height: 10rpx;
  background-color: white;
  border-radius: 2rpx;
  z-index: 1;
}

/* 烟囱 */
.home-chimney {
  position: absolute;
  top: 5rpx;
  right: 10rpx;
  width: 8rpx;
  height: 14rpx;
  background-color: #8a8a8a;
  border-radius: 2rpx 2rpx 0 0;
  z-index: 0;
  transition: background-color 0.3s ease;
}

/* 激活状态 */
.tab-item.active .tab-icon-home-roof {
  border-bottom-color: #4a90e2;
}

.tab-item.active .tab-icon-home-body,
.tab-item.active .home-chimney {
  background-color: #4a90e2;
}

/* CSS 图标 - 设备录入 */
.tab-icon-add {
  position: relative;
  width: 54rpx;
  height: 48rpx;
  display: flex;
  justify-content: center;
  align-items: center;
}

/* 设备外壳 */
.tab-icon-add-case {
  content: '';
  position: absolute;
  top: 6rpx;
  left: 10rpx;
  width: 34rpx;
  height: 40rpx;
  border-radius: 10rpx;
  background-color: #8a8a8a;
  transition: all 0.3s ease;
}

/* 设备屏幕 */
.tab-icon-add-screen {
  content: '';
  position: absolute;
  top: 12rpx;
  left: 16rpx;
  width: 22rpx;
  height: 18rpx;
  border-radius: 4rpx;
  background-color: white;
  transition: all 0.3s ease;
}

/* 设备按钮 */
.add-connector {
  position: absolute;
  bottom: 12rpx;
  left: 24rpx;
  width: 6rpx;
  height: 6rpx;
  border-radius: 50%;
  background-color: white;
  transition: all 0.3s ease;
}

/* 横向按钮 */
.add-plus-horizontal {
  position: absolute;
  top: 22rpx;
  left: 20rpx;
  width: 14rpx;
  height: 2rpx;
  background-color: #8a8a8a;
  border-radius: 1rpx;
  transition: all 0.3s ease;
}

/* 纵向按钮 */
.add-plus-vertical {
  position: absolute;
  top: 16rpx;
  left: 26rpx;
  width: 2rpx;
  height: 14rpx;
  background-color: #8a8a8a;
  border-radius: 1rpx;
  transition: all 0.3s ease;
}

/* 激活状态 */
.tab-item.active .tab-icon-add-case {
  background-color: #4a90e2;
}

.tab-item.active .add-connector,
.tab-item.active .tab-icon-add-screen {
  background-color: white;
  box-shadow: 0 0 4rpx rgba(255, 255, 255, 0.8);
}

.tab-item.active .add-plus-horizontal,
.tab-item.active .add-plus-vertical {
  background-color: #4a90e2;
}

/* CSS 图标 - 我的 */
.tab-icon-me {
  position: relative;
  width: 54rpx;
  height: 48rpx;
  display: flex;
  justify-content: center;
  align-items: center;
}

/* 主圆 - 形成头像主体 */
.tab-icon-me-head {
  content: '';
  position: absolute;
  top: 3rpx;
  left: 12rpx;
  width: 30rpx;
  height: 30rpx;
  border-radius: 50%;
  background: linear-gradient(135deg, #9e9e9e, #707070);
  box-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.2);
  transition: all 0.3s ease;
}

/* 内部光晕 - 增加质感 */
.tab-icon-me-highlight {
  content: '';
  position: absolute;
  top: 7rpx;
  left: 16rpx;
  width: 10rpx;
  height: 10rpx;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.6);
  filter: blur(1rpx);
  transition: all 0.3s ease;
}

/* 装饰元素左 - 星形光点 */
.me-ear-left {
  position: absolute;
  top: 8rpx;
  left: 36rpx;
  width: 4rpx;
  height: 4rpx;
  border-radius: 50%;
  background-color: white;
  box-shadow: 0 0 4rpx rgba(255, 255, 255, 0.9);
  transition: all 0.3s ease;
}

/* 装饰元素右 - 更小的光点 */
.me-ear-right {
  position: absolute;
  top: 16rpx;
  left: 8rpx;
  width: 3rpx;
  height: 3rpx;
  border-radius: 50%;
  background-color: white;
  box-shadow: 0 0 3rpx rgba(255, 255, 255, 0.8);
  transition: all 0.3s ease;
}

/* 底部弧线 - 衬托头像 */
.me-arm-left {
  position: absolute;
  bottom: 4rpx;
  left: 14rpx;
  width: 26rpx;
  height: 14rpx;
  border-radius: 50%;
  border-bottom: 3rpx solid #8a8a8a;
  border-left: 3rpx solid transparent;
  border-right: 3rpx solid transparent;
  border-top: 3rpx solid transparent;
  box-sizing: border-box;
  transition: all 0.3s ease;
}

/* 底部装饰元素 - 增加层次感 */
.me-arm-right {
  position: absolute;
  bottom: 11rpx;
  left: 19rpx;
  width: 16rpx;
  height: 2rpx;
  background-color: #8a8a8a;
  border-radius: 2rpx;
  transition: all 0.3s ease;
}

/* 激活状态 */
.tab-item.active .tab-icon-me-head {
  background: linear-gradient(135deg, #4a90e2, #3674d0);
  box-shadow: 0 2rpx 8rpx rgba(74, 144, 226, 0.4);
}

.tab-item.active .tab-icon-me-highlight {
  background: rgba(255, 255, 255, 0.8);
  box-shadow: 0 0 6rpx rgba(255, 255, 255, 0.4);
}

.tab-item.active .me-ear-left,
.tab-item.active .me-ear-right {
  background-color: white;
  box-shadow: 0 0 6rpx rgba(255, 255, 255, 1);
}

.tab-item.active .me-arm-left {
  border-bottom-color: #4a90e2;
  box-shadow: 0 2rpx 4rpx rgba(74, 144, 226, 0.2);
}

.tab-item.active .me-arm-right {
  background-color: #4a90e2;
  box-shadow: 0 1rpx 3rpx rgba(74, 144, 226, 0.2);
}

/* 滚动条样式优化 */
.custom-scrollbar {
  width: 10rpx;
  background-color: transparent;
}

.custom-scrollbar-thumb {
  border-radius: 10rpx;
  background-color: rgba(0, 0, 0, 0.1);
}

/* 按钮网格布局 - 优化性能 */
.button-grid {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
  padding: 28rpx;
  width: 710rpx; /* 调整宽度 */
  margin-left: auto;
  margin-right: auto;
  box-sizing: border-box;
  margin-top: 20rpx;
  /* 更淡、更接近页面整体氛围的背景色 - 简化渐变 */
  background: linear-gradient(145deg, rgba(245, 243, 255, 0.95) 0%, rgba(238, 235, 250, 0.9) 100%);
  border-radius: 28rpx;
  /* 减轻阴影复杂度提高性能 */
  box-shadow: 0 10rpx 25rpx rgba(120, 100, 220, 0.15);
  border: 1px solid rgba(180, 170, 255, 0.35);
  position: relative;
  overflow: hidden;
  /* 使用硬件加速 */
  transform: translateZ(0);
  transition: all 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  opacity: 1;
}

/* 按钮区域隐藏动画 */
.button-grid-hidden {
  opacity: 0;
  transform: translateY(20rpx);
  pointer-events: none;
}

/* 简化按钮网格内部光泽效果 */
.button-grid::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 40%;
  /* 简化渐变提高性能 */
  background: linear-gradient(to bottom, rgba(255, 255, 255, 0.25), transparent);
  border-radius: 28rpx 28rpx 0 0;
  z-index: 0;
}

/* 移除复杂底部阴影，提高渲染性能 */
.button-grid::after {
  display: none;
}

.button-row {
  display: flex;
  justify-content: space-between;
  margin-bottom: 22rpx;
  gap: 22rpx;
  position: relative;
  z-index: 1;
}

.button-row:last-child {
  margin-bottom: 0;
}

/* 优化按钮样式 - 减少渲染负担，提高性能 */
.grid-button {
  flex: 1;
  min-width: 300rpx;
  height: 88rpx;
  line-height: 88rpx;
  font-size: 32rpx;
  color: #fff;
  /* 🎯 增强3D光照背景系统 - 基于真实光照物理原理 */
  background:
    /* 🌟 主光源层 - 模拟45度角顶部照射（增强立体感） */
    linear-gradient(180deg,
      rgba(255, 255, 255, 0.28) 0%,     /* 增强顶部高光强度 */
      rgba(255, 255, 255, 0.18) 20%,    /* 扩大高光覆盖范围 */
      rgba(255, 255, 255, 0.08) 40%,    /* 自然过渡区域 */
      transparent 55%,                   /* 中性光照区域 */
      rgba(0, 0, 0, 0.08) 75%,          /* 增强底部阴影 */
      rgba(0, 0, 0, 0.18) 100%),        /* 加深底部阴影强度 */
    /* 🎨 保持原有蓝紫渐变色 - 维持品牌一致性 */
    linear-gradient(135deg, #78b9ff 0%, #a0a4ff 50%, #c58eff 100%);
  /* 🎯 移除外边框，使用内部阴影定义边缘 */
  border: none;
  border-radius: 44rpx;
  /* 🎯 增强3D阴影系统 - 基于Material Design 3.0 Elevation 6dp */
  box-shadow:
    /* 📍 Contact Shadow - 接触阴影（增强定义） */
    0 1rpx 2rpx rgba(0, 0, 0, 0.22),
    /* 🌟 Key Light Shadow - 主光源阴影（增强深度） */
    0 3rpx 6rpx rgba(120, 185, 255, 0.35),
    0 6rpx 12rpx rgba(120, 185, 255, 0.28),
    0 9rpx 18rpx rgba(120, 185, 255, 0.20),
    /* 🌫️ Ambient Shadow - 环境光阴影（增强悬浮感） */
    0 12rpx 24rpx rgba(0, 0, 0, 0.12),
    0 18rpx 36rpx rgba(0, 0, 0, 0.08),
    /* ✨ Inner Highlights - 内部高光（增强立体感） */
    inset 0 1rpx 0 rgba(255, 255, 255, 0.40),
    inset 0 -1rpx 0 rgba(0, 0, 0, 0.28),
    /* 🔲 Edge Definition - 边缘定义（增强轮廓） */
    inset 0 0 0 1rpx rgba(255, 255, 255, 0.18);
  /* 🔧 移除过渡效果 - 避免hover-class颜色滞留 */
  /* transition: background 0.2s cubic-bezier(0.4, 0, 0.2, 1),
              box-shadow 0.2s cubic-bezier(0.4, 0, 0.2, 1); */
  padding: 0 20rpx;
  text-align: center;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  letter-spacing: 1.5rpx;
  position: relative;
  z-index: 1;
  /* 使用更简单的初始变换 */
  transform: translateZ(0);
  /* 只对实际会变化的属性使用will-change */
  will-change: transform;
  border: none;
  /* 移除复杂的文字效果提高性能 */
  font-weight: 550;
}

/* 🎯 增强按钮高光系统 - 基于Apple HIG和Material Design最佳实践 */
/* 🚫 排除彩虹按钮，避免白色高光覆盖 */
.grid-button:not(.disconnect-video):not(.record-video-button.recording)::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 45%;                              /* 恢复原来的45%高光覆盖 */
  border-radius: 44rpx 44rpx 20rpx 20rpx;   /* 恢复原来的高光形状 */
  /* 🌟 增强主光源高光 - 模拟真实光源直射效果 */
  background: linear-gradient(180deg,
    rgba(255, 255, 255, 0.35) 0%,          /* 增强顶部高光强度 */
    rgba(255, 255, 255, 0.25) 30%,         /* 扩大强高光区域 */
    rgba(255, 255, 255, 0.15) 60%,         /* 自然过渡 */
    rgba(255, 255, 255, 0.05) 85%,         /* 边缘柔化 */
    transparent 100%);
  z-index: 2;
  pointer-events: none;
}

/* 🎯 增强按钮边缘高光 - 基于Material Design边缘定义原则 */
.grid-button::after {
  content: "";
  position: absolute;
  top: 0.5rpx;
  left: 0.5rpx;
  right: 0.5rpx;
  bottom: 0.5rpx;
  border-radius: 43.5rpx;
  /* 🎯 移除边框，避免微信小程序黑线问题 */
  border: none;
  /* 🎯 使用纯内部边缘阴影，增强立体感 */
  box-shadow: inset 0 0 0 0.5rpx rgba(255, 255, 255, 0.15);
  z-index: 1;
  pointer-events: none;
}

/* 🎯 移除普通grid按钮悬停效果 - 避免颜色滞留，但保留彩虹按钮hover效果 */
/* .grid-button:hover {
  filter: brightness(115%);
  transform: translateY(-3rpx);
  box-shadow:
    0 2rpx 4rpx hsl(220deg 70% 45% / 0.42),
    0 6rpx 12rpx hsl(220deg 70% 45% / 0.38),
    0 12rpx 24rpx hsl(220deg 70% 45% / 0.34),
    0 18rpx 36rpx hsl(220deg 70% 45% / 0.30),
    0 24rpx 48rpx hsl(220deg 70% 45% / 0.26),
    0 32rpx 64rpx hsl(220deg 70% 45% / 0.22),
    0 40rpx 80rpx hsl(220deg 70% 45% / 0.18),
    inset 0 3rpx 0 rgba(255, 255, 255, 0.35),
    inset 0 -3rpx 0 rgba(0, 0, 0, 0.20),
    inset 0 0 0 1px rgba(255, 255, 255, 0.15);
} */

/* 🌈 彩虹按钮Material Design 3 hover效果 - 标准状态变化 */
.grid-button.disconnect-video:hover,
.grid-button.record-video-button.recording:hover,
.parameter-top-button.record-button.recording:hover {
  /* Material Design 3 Elevation Level 2 - Hovered状态 */
  box-shadow:
    0 3rpx 6rpx rgba(0, 0, 0, 0.16),
    0 3rpx 6rpx rgba(0, 0, 0, 0.23);
  transform: translateY(-1rpx);
}

/* 🎯 自然的外部光晕 - 不过度 */
.grid-button::after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(90deg, #78b9ff, #a0a4ff, #c58eff);
  z-index: -1;
  border-radius: 44rpx;
  opacity: 0.4;
  /* 🎯 保持简洁，不外扩 */
}

/* 🎯 删除grid-button的:active样式 - 使用类名替代 */

/* 移除:active::before样式，避免颜色粘滞 */

/* 🚫 删除录制按钮特殊样式 - 完全继承普通按钮样式 */

/* 🚫 删除录制按钮::before特殊定义 - 完全继承普通按钮::before */

/* 🚫 删除录制按钮::after特殊定义 - 完全继承普通按钮::after */

/* 🎯 开始录制按钮样式已在上方统一定义，移除重复样式 */

/* 🎯 默认模式录制按钮边缘高光通过内部阴影实现 - 避免与发光效果冲突 */

.button-grid .grid-button.record-video-button.recording {
  /* 🌈 高贵优雅渐变 - 深邃饱满的贵族色调 */
  background: linear-gradient(135deg,
    rgba(240, 160, 80, 0.92) 0%,    /* 深邃金橙 */
    rgba(235, 140, 100, 0.93) 20%,  /* 饱满琥珀橙 */
    rgba(225, 120, 130, 0.94) 40%,  /* 优雅玫瑰金 */
    rgba(210, 100, 140, 0.95) 60%,  /* 高贵玫红 */
    rgba(195, 85, 150, 0.96) 80%,   /* 贵族紫玫 */
    rgba(180, 75, 160, 0.97) 100%) !important; /* 皇室薰衣草 */
  /* 🔮 玻璃质感效果 */
  backdrop-filter: blur(8rpx) saturate(1.2) !important;
  box-shadow:
    inset 0 1rpx 0 rgba(255, 255, 255, 0.3),
    inset 0 -1rpx 0 rgba(0, 0, 0, 0.1),
    0 4rpx 20rpx rgba(200, 100, 140, 0.3) !important;
  border: none !important;
  backdrop-filter: blur(10rpx) !important;
  animation: rainbow-md3-elevation 2s infinite !important;
  /* 🚀 Material Design 3性能优化 - 标准GPU加速 */
  will-change: transform, box-shadow;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  line-height: 88rpx;
  /* 🌈 发光效果已集成到呼吸动画中，移除静态发光 */
  /* 🌈 Material Design 3 Elevation Level 1 - 官方标准阴影系统 */
  box-shadow:
    0 1rpx 3rpx rgba(0, 0, 0, 0.12),
    0 1rpx 2rpx rgba(0, 0, 0, 0.24) !important;
}

/* 🌟 默认模式录制按钮纯净发光效果 - 强烈外部发光 */
.record-button.recording::before {
  content: '';
  position: absolute;
  top: -12rpx;
  left: -12rpx;
  right: -12rpx;
  bottom: -12rpx;
  background: radial-gradient(circle, rgba(195, 125, 35, 0.8) 0%, transparent 70%);
  border-radius: 50rpx;
  z-index: -1;
  transform: translate3d(0, 0, 0);
  animation: rainbow-glow-inner 2s infinite;
}

.record-button.recording::after {
  content: '';
  position: absolute;
  top: -24rpx;
  left: -24rpx;
  right: -24rpx;
  bottom: -24rpx;
  background: radial-gradient(circle, rgba(205, 75, 75, 0.6) 0%, transparent 70%);
  border-radius: 60rpx;
  z-index: -2;
  transform: translate3d(0, 0, 0);
  animation: rainbow-glow-outer 2s infinite;
}

/* 🎨 断开接收视频按钮样式 - 基于Animata最佳实践的边缘发光 */
.grid-button.disconnect-video {
  /* 🌈 高贵优雅渐变 - 深邃饱满的贵族色调 */
  background: linear-gradient(135deg,
    rgba(240, 160, 80, 0.92) 0%,    /* 深邃金橙 */
    rgba(235, 140, 100, 0.93) 20%,  /* 饱满琥珀橙 */
    rgba(225, 120, 130, 0.94) 40%,  /* 优雅玫瑰金 */
    rgba(210, 100, 140, 0.95) 60%,  /* 高贵玫红 */
    rgba(195, 85, 150, 0.96) 80%,   /* 贵族紫玫 */
    rgba(180, 75, 160, 0.97) 100%); /* 皇室薰衣草 */
  /* 🔮 玻璃质感效果 */
  backdrop-filter: blur(8rpx) saturate(1.2);
  box-shadow:
    inset 0 1rpx 0 rgba(255, 255, 255, 0.3),
    inset 0 -1rpx 0 rgba(0, 0, 0, 0.1),
    0 4rpx 20rpx rgba(200, 100, 140, 0.3);
  border: none;
  backdrop-filter: blur(10rpx);
  animation: rainbow-md3-elevation 2s infinite;
  /* 🚀 Material Design 3性能优化 - 标准GPU加速 */
  will-change: transform, box-shadow;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  line-height: 88rpx;
  /* 🌈 发光效果已集成到呼吸动画中，移除静态发光 */
}

/* 🌟 增强伪元素发光效果 - 优化为深邃玫红色渐变，保持颜色一致性 */
.grid-button.disconnect-video::before {
  content: '';
  position: absolute;
  top: -12rpx;
  left: -12rpx;
  right: -12rpx;
  bottom: -12rpx;
  background: linear-gradient(45deg,
    rgba(240, 160, 80, 0.9) 0%,     /* 贵族金橙发光 */
    rgba(225, 120, 130, 0.85) 40%,  /* 优雅玫瑰金发光 */
    rgba(195, 85, 150, 0.8) 80%,    /* 高贵紫玫发光 */
    rgba(180, 75, 160, 0.75) 100%); /* 皇室薰衣草发光 */
  border-radius: 50rpx;
  z-index: -1;
  animation: rainbow-breath 4s ease-in-out infinite;
  /* 🚀 移除blur滤镜 - 消除性能瓶颈 */
}

.grid-button.disconnect-video::after {
  content: '';
  position: absolute;
  top: -16rpx;
  left: -16rpx;
  right: -16rpx;
  bottom: -16rpx;
  background: linear-gradient(45deg,
    rgba(240, 160, 80, 0.75) 0%,    /* 贵族金橙外发光 */
    rgba(225, 120, 130, 0.7) 40%,   /* 优雅玫瑰金外发光 */
    rgba(195, 85, 150, 0.65) 80%,   /* 高贵紫玫外发光 */
    rgba(180, 75, 160, 0.6) 100%);  /* 皇室薰衣草外发光 */
  border-radius: 55rpx;
  z-index: -2;
  animation: rainbow-breath 4s ease-in-out infinite reverse;
  /* 🚀 移除blur滤镜 - 消除性能瓶颈 */
}

/* 🚫 移除重复的内部背景层 - 保持按钮内部纯净 */

/* 🌟 微信小程序最佳实践呼吸动画 - 简单高效的scale变换 */
@keyframes rainbow-breath {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
}

/* 🚀 微信小程序呼吸动画最佳实践 - 基于CSDN专业案例 */

/* 🔧 移除彩虹按钮的白色内部阴影 - 避免白色边框问题 */
.grid-button.disconnect-video::after,
.grid-button.record-video-button.recording::after {
  box-shadow: none !important;
}

.parameter-top-button.record-button.recording::after {
  box-shadow: none !important;
}

/* 🌈 彩虹按钮发光效果 - 伪元素+呼吸动画双重发光 */

/* 🎯 删除特殊按钮的:active样式 - 使用类名替代 */

/* 按钮内容容器 */
.button-content {
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 按钮文字样式 */
.button-text {
  font-size: 32rpx;
  font-weight: 550;
  color: #fff;
  z-index: 3;
  position: relative;
}

/* 🚫 删除录制按钮冲突样式定义 */

/* 🚫 移除录制按钮内部背景层 - 保持按钮内部纯净 */

/* 🌟 增强停止录制按钮伪元素发光效果 - 优化为深邃玫红色渐变 */
.button-grid .grid-button.record-video-button.recording::before {
  content: '';
  position: absolute;
  top: -12rpx;
  left: -12rpx;
  right: -12rpx;
  bottom: -12rpx;
  background: linear-gradient(45deg,
    rgba(240, 160, 80, 0.9) 0%,     /* 贵族金橙发光 */
    rgba(225, 120, 130, 0.85) 40%,  /* 优雅玫瑰金发光 */
    rgba(195, 85, 150, 0.8) 80%,    /* 高贵紫玫发光 */
    rgba(180, 75, 160, 0.75) 100%); /* 皇室薰衣草发光 */
  border-radius: 50rpx;
  z-index: -1;
  animation: rainbow-breath 4s ease-in-out infinite;
  /* 🚀 移除blur滤镜 - 消除性能瓶颈 */
}

.button-grid .grid-button.record-video-button.recording::after {
  content: '';
  position: absolute;
  top: -16rpx;
  left: -16rpx;
  right: -16rpx;
  bottom: -16rpx;
  background: linear-gradient(45deg,
    rgba(240, 160, 80, 0.75) 0%,    /* 贵族金橙外发光 */
    rgba(225, 120, 130, 0.7) 40%,   /* 优雅玫瑰金外发光 */
    rgba(195, 85, 150, 0.65) 80%,   /* 高贵紫玫外发光 */
    rgba(180, 75, 160, 0.6) 100%);  /* 皇室薰衣草外发光 */
  border-radius: 55rpx;
  z-index: -2;
  animation: rainbow-breath 4s ease-in-out infinite reverse;
  /* 🚀 移除blur滤镜 - 消除性能瓶颈 */
}

/* 🎯 删除录制按钮的:active样式 - 使用类名替代 */



/* 倒计时显示样式 - 优化布局 */
.countdown-overlay {
  position: absolute;
  top: 50%;
  right: 15rpx;
  transform: translateY(-50%);
  background: rgba(255, 255, 255, 0.95);
  color: #ff4757;
  font-size: 26rpx;
  font-weight: bold;
  padding: 6rpx 12rpx;
  border-radius: 16rpx;
  min-width: 45rpx;
  text-align: center;
  box-shadow: 0 3rpx 8rpx rgba(0, 0, 0, 0.25);
  animation: countdown-pulse 1s infinite;
  border: 2rpx solid rgba(255, 71, 87, 0.3);
}

@keyframes countdown-pulse {
  0%, 100% {
    transform: translateY(-50%) scale(1);
    opacity: 1;
    box-shadow: 0 3rpx 8rpx rgba(0, 0, 0, 0.25);
  }
  50% {
    transform: translateY(-50%) scale(1.05);
    opacity: 0.9;
    box-shadow: 0 4rpx 12rpx rgba(255, 71, 87, 0.4);
  }
}

@keyframes simple-pulse {
  0%, 100% {
    transform: scale(1);
    box-shadow: 0 4rpx 12rpx rgba(255, 107, 107, 0.3), 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
  }
  50% {
    transform: scale(1.02);
    box-shadow: 0 6rpx 16rpx rgba(255, 107, 107, 0.4), 0 3rpx 6rpx rgba(0, 0, 0, 0.15);
  }
}

/* 🌈 彩虹按钮呼吸动画发光效果 - 增强发光强度，保持集中范围 */
@keyframes rainbow-md3-elevation {
  0%, 100% {
    transform: scale(1) translate3d(0, 0, 0);
    /* 呼吸动画最小状态 - 增强发光可见性 */
    box-shadow:
      0 1rpx 3rpx rgba(0, 0, 0, 0.12),
      0 1rpx 2rpx rgba(0, 0, 0, 0.24),
      0 0 8rpx rgba(255, 204, 112, 0.65),
      0 0 16rpx rgba(200, 80, 192, 0.45),
      0 0 24rpx rgba(65, 88, 208, 0.3);
  }
  50% {
    transform: scale(1.01) translate3d(0, -1rpx, 0);
    /* 呼吸动画最大状态 - 明显但优雅的发光效果 */
    box-shadow:
      0 3rpx 6rpx rgba(0, 0, 0, 0.16),
      0 3rpx 6rpx rgba(0, 0, 0, 0.23),
      0 0 12rpx rgba(255, 204, 112, 0.9),
      0 0 24rpx rgba(200, 80, 192, 0.75),
      0 0 36rpx rgba(65, 88, 208, 0.55);
  }
}

/* 🌟 彩虹按钮发光动画 - 基于最佳实践的纯净发光效果 */
@keyframes rainbow-glow-inner {
  0%, 100% {
    transform: scale(1) translate3d(0, 0, 0);
    opacity: 0.6;
  }
  50% {
    transform: scale(1.1) translate3d(0, 0, 0);
    opacity: 0.8;
  }
}

/* 🌟 外层发光动画 - 更强烈的视觉效果 */
@keyframes rainbow-glow-outer {
  0%, 100% {
    transform: scale(1) translate3d(0, 0, 0);
    opacity: 0.4;
  }
  50% {
    transform: scale(1.2) translate3d(0, 0, 0);
    opacity: 0.7;
  }
}



/* 录制时间选择器弹窗样式 */
.record-time-selector-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
}

.record-time-selector-content {
  background: #fff;
  border-radius: 20rpx;
  padding: 40rpx;
  margin: 40rpx;
  max-width: 600rpx;
  width: 80%;
  box-shadow: 0 10rpx 30rpx rgba(0, 0, 0, 0.3);
}

.selector-header {
  text-align: center;
  margin-bottom: 30rpx;
}

.selector-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  display: block;
  margin-bottom: 10rpx;
}

.selector-subtitle {
  font-size: 24rpx;
  color: #666;
  display: block;
}

.time-display {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15rpx 0;
  border-bottom: 1rpx solid #eee;
  margin-bottom: 10rpx;
}

.time-label {
  font-size: 28rpx;
  color: #333;
}

.time-value {
  font-size: 28rpx;
  font-weight: bold;
  color: #007aff;
}

.picker-container {
  margin: 30rpx 0;
  border: 2rpx solid #e0e0e0;
  border-radius: 12rpx;
  overflow: hidden;
}

.picker-display {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx;
  background: #f8f8f8;
  font-size: 32rpx;
  color: #333;
}

.picker-arrow {
  color: #999;
  font-size: 24rpx;
}

.selector-buttons {
  display: flex;
  gap: 20rpx;
  margin-top: 30rpx;
}

.selector-btn {
  flex: 1;
  padding: 20rpx;
  border-radius: 12rpx;
  font-size: 28rpx;
  border: none;
}

.cancel-btn {
  background: #f0f0f0;
  color: #666;
}

.confirm-btn {
  background: linear-gradient(90deg, #007aff 0%, #5ac8fa 100%);
  color: #fff;
}

/* 删除重复的simple-pulse动画定义，使用第一个橙红色版本 */

/* 🎯 删除录制按钮的:active样式 - 使用类名替代 */

/* 简化禁用按钮的样式 */
.grid-button.disabled-button {
  /* 与普通按钮相同的渐变色，但降低饱和度 */
  background: linear-gradient(90deg, #a5c9f7 0%, #d5b8f7 100%);
  opacity: 0.7;
  /* 减轻阴影效果 */
  box-shadow: 0 2rpx 6rpx rgba(90, 120, 213, 0.1);
}

.grid-button.disabled-button::after {
  /* 移除复杂效果 */
  display: none;
}

.rename-confirm {
  background: linear-gradient(90deg, #78b9ff 0%, #c58eff 100%);
  color: #fff;
  border: none;
  box-shadow: 0 4rpx 16rpx rgba(90, 120, 213, 0.4);
  position: relative;
}

.rename-confirm::after {
  content: "";
  position: absolute;
  top: -1rpx;
  left: -1rpx;
  right: -1rpx;
  bottom: -1rpx;
  background: linear-gradient(90deg, #78b9ff, #c58eff);
  z-index: -1;
  border-radius: 41rpx;
  filter: blur(3rpx);
  opacity: 0.7;
}

/* 重命名相关样式 - 确保不影响参数模式 */
.rename-mask {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100vh;
  background: rgba(0, 0, 0, 0);
  backdrop-filter: blur(0);
  -webkit-backdrop-filter: blur(0);
  z-index: 1000;
  display: flex;
  justify-content: center;
  align-items: center;
  transition: all 0.3s ease-out;
  opacity: 0;
  visibility: hidden;
  pointer-events: none;
}

.rename-mask.show {
  background: rgba(0, 0, 0, 0.7);
  backdrop-filter: blur(12px);
  -webkit-backdrop-filter: blur(12px);
  opacity: 1;
  visibility: visible;
  pointer-events: auto;
}

.rename-card {
  width: 600rpx;
  background: #fff;
  border-radius: 24rpx;
  padding: 40rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.12);
  transform: scale(0.9) translateY(20rpx);
  opacity: 0;
  transition: all 0.3s ease-out;
}

.rename-mask.show .rename-card {
  transform: scale(1) translateY(0);
  opacity: 1;
}

.rename-title {
  font-size: 34rpx;
  color: #2c3e50;
  font-weight: 600;
  text-align: center;
  margin-bottom: 40rpx;
}

.rename-options {
  display: flex;
  justify-content: space-around;
  padding: 20rpx 0;
}

.rename-option {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20rpx 40rpx;
  border-radius: 16rpx;
  transition: all 0.3s ease;
}

/* 🎯 移除重命名选项的:active样式，避免与hover-class冲突 */

.rename-icon {
  width: 80rpx;
  height: 80rpx;
  margin-bottom: 16rpx;
}

.rename-option-text {
  font-size: 28rpx;
  color: #2c3e50;
}

/* 重命名输入框样式 */
.rename-input-mask {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100vh;
  background: rgba(0, 0, 0, 0);
  backdrop-filter: blur(0);
  -webkit-backdrop-filter: blur(0);
  z-index: 1001;
  display: flex;
  justify-content: center;
  align-items: center;
  transition: all 0.3s ease-out;
  opacity: 0;
  visibility: hidden;
  pointer-events: none;
}

.rename-input-mask.show {
  background: rgba(0, 0, 0, 0.7);
  backdrop-filter: blur(12px);
  -webkit-backdrop-filter: blur(12px);
  opacity: 1;
  visibility: visible;
  pointer-events: auto;
}

.rename-input-card {
  width: 600rpx;
  background: #fff;
  border-radius: 24rpx;
  padding: 40rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.12);
  transform: scale(0.9) translateY(20rpx);
  opacity: 0;
  transition: all 0.3s ease-out;
}

.rename-input-mask.show .rename-input-card {
  transform: scale(1) translateY(0);
  opacity: 1;
}

.rename-input-title {
  font-size: 34rpx;
  color: #2c3e50;
  font-weight: 600;
  text-align: center;
  margin-bottom: 40rpx;
}

.rename-input {
  width: 100%;
  height: 88rpx;
  background: #f8f9fa;
  border-radius: 16rpx;
  padding: 0 30rpx;
  font-size: 28rpx;
  color: #333;
  margin-bottom: 40rpx;
  border: 2rpx solid #e8e8e8;
  box-sizing: border-box;
}

.rename-input:focus {
  border-color: #4a90e2;
  background: #fff;
  box-shadow: 0 2rpx 8rpx rgba(74, 144, 226, 0.1);
}

.rename-buttons {
  display: flex;
  justify-content: space-between;
  gap: 20rpx;
}

.rename-cancel, .rename-confirm {
  flex: 1;
  height: 80rpx;
  line-height: 80rpx;
  text-align: center;
  border-radius: 40rpx;
  font-size: 28rpx;
  transition: all 0.3s ease;
}

.rename-cancel {
  background: #f8f9fa;
  color: #666;
  border: 2rpx solid #e8e8e8;
}

.rename-confirm {
  background: linear-gradient(90deg, #61a5fb 0%, #c165dd 100%);
  color: #fff;
  border: none;
  box-shadow: 0 2rpx 8rpx rgba(67, 134, 252, 0.2);
}

/* 🎯 移除重命名按钮的:active样式，避免与hover-class冲突 */

/* 按钮相关动画 */
@keyframes pulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
  100% {
    transform: scale(1);
  }
}

/* 面板滑入动画 */
@keyframes slideUp {
  from {
    transform: translateY(100%);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

/* 🎯 移除重复的加减按钮:active样式，避免与hover-class冲突 */

.scroll-view-content {
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
}

/* 确保历史记录列表和结果卡片有足够的底部空间 */
.history-list,
.result-card {
  width: 100%;
  margin-bottom: 0; /* 确保这些作为主要内容块的元素也没有底部外边距 */
}

/* 定义放大缩小动画效果 */
@keyframes zoomInEffect {
  from {
    transform: scale(0.8);
    opacity: 0;
  }
  to {
    transform: scale(1);
    opacity: 1;
  }
}

@keyframes zoomOutEffect {
  from {
    transform: scale(1);
    opacity: 1;
  }
  50% { /* 动画进行到一半时 */
    transform: scale(0.9); /* 开始缩小 */
    opacity: 1; /* 保持内容区域完全不透明 */
  }
  to {
    transform: scale(0.8);
    opacity: 0; /* 在动画结束时才完全透明 */
  }
}

/* 新增：容器自身的淡出和缩小动画 */
@keyframes containerFadeZoomOut {
  from {
    opacity: 1;
    transform: translate(-50%, -50%) scale(1);
  }
  to {
    opacity: 0;
    transform: translate(-50%, -50%) scale(0.8);
  }
}

/* 优化进度条样式 */
.progress-style,
.status-progress .progress-style,
.progress-component {
  border-radius: 8rpx;
  overflow: hidden;
}

.grid-button .button-text {
  font-size: 22rpx;
  font-weight: 500;
  color: #fff;
  position: relative;
  z-index: 2;
}

/* 添加禁用按钮的文本样式 */
.grid-button.disabled-button .button-text {
  color: rgba(255, 255, 255, 0.9);
  text-shadow: 0 1rpx 2rpx rgba(180, 128, 128, 0.2);
}

/* ECL值标签样式 */
.ecl-label {
  position: relative;
  font-size: 32rpx;
  font-weight: 600;
  color: #5c5ca8;
  padding-left: 24rpx;
  margin-bottom: 15rpx; /* 减小底部间距 */
  display: flex;
  align-items: center;
}

/* ECL值标签左侧的装饰条 */
.ecl-label::before {
  content: '';
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 8rpx;
  height: 32rpx; /* 减小高度 */
  background: linear-gradient(to bottom, #7F7FD5, #91EAE4);
  border-radius: 4rpx;
}

/* ECL值列表容器 */
.ecl-values {
  display: flex;
  flex-direction: column;
  gap: 18rpx; /* 增加项目间距 */
  position: relative;
  z-index: 1;
  padding-bottom: 20rpx; /* 增加底部内边距 */
}

/* 值项标签样式 */
.value-label {
  font-size: 28rpx;
  font-weight: 500;
  color: #4a4a80;
  margin-right: 15rpx;
  min-width: 80rpx; /* 确保标签宽度一致 */
}

/* 值文本样式 */
.value-text {
  font-size: 28rpx;
  color: #666;
  flex: 1;
}

/* 添加底部渐变过渡效果，确保存在 */
.ecl-bottom-fade {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 60rpx;
  background: linear-gradient(to bottom, rgba(240, 240, 255, 0), rgba(240, 240, 255, 0.95) 70%);
  border-bottom-left-radius: 20rpx;
  border-bottom-right-radius: 20rpx;
  z-index: 2;
}

.parameter-item:nth-child(10) {
  animation-delay: 0s;
}

/* 保留参数项之间的分隔线 */
.parameter-item:not(:last-child)::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 10%;
  right: 10%;
  height: 1rpx;
  background: linear-gradient(to right, rgba(180, 170, 255, 0.05), rgba(180, 170, 255, 0.2), rgba(180, 170, 255, 0.05));
}

/* 这部分动画代码已经在上面被优化并替换 */

/* 增加淡入动画关键帧 */
@keyframes simpleFadeIn {
  0% {
    opacity: 0;
    transform: translateY(5px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 删除冲突的::after样式，避免颜色粘滞 */



/* 禁用按钮的样式 - 保持设计一致性 */
.grid-button.disabled-button {
  background: linear-gradient(90deg, #a5c9f7 0%, #d5b8f7 100%);
  opacity: 0.7;
  box-shadow: 0 4rpx 12rpx rgba(90, 120, 213, 0.2), 0 1rpx 2rpx rgba(0, 0, 0, 0.05);
  border-color: rgba(255, 255, 255, 0.15);
  cursor: not-allowed;
  pointer-events: none; /* 完全禁止点击 */
  transition: all 0.3s ease; /* 平滑过渡 */
}

.grid-button.disabled-button::after {
  opacity: 0.3;
  filter: blur(3rpx);
  background: linear-gradient(90deg, #a5c9f7, #d5b8f7);
}

/* 模式切换过渡动画类 - 已使用transition替代 */

/* 模式与参数列表动画配置 */

.parameter-mode, .default-mode {
  -webkit-backface-visibility: hidden;
  backface-visibility: hidden;
  transform: translateZ(0);
  perspective: 1000;
  transform-style: preserve-3d;
  transition: opacity 0.5s ease-out;
  opacity: 1;
}

/* 淡出效果 */
.fade-out {
  opacity: 0;
  transition: opacity 0.5s ease-out;
}

/* 从上到下渐隐动画效果 */
.fade-out-down {
  animation: fadeOutDown 0.8s cubic-bezier(0.215, 0.61, 0.355, 1) forwards;
  will-change: opacity, transform;
  transform-origin: top center;
  -webkit-backface-visibility: hidden;
  backface-visibility: hidden;
  perspective: 1000;
}

@keyframes fadeOutDown {
  0% {
    opacity: 1;
    transform: translateY(0) translateZ(0);
  }
  30% {
    opacity: 0.95;
    transform: translateY(5rpx) translateZ(0);
  }
  100% {
    opacity: 0;
    transform: translateY(50rpx) translateZ(0);
  }
}

/* 隐藏元素 - 用于模式切换时防止闪烁 */
.hidden {
  opacity: 0 !important;
  visibility: hidden !important;
}

/* 列表淡入动画 */
.list-fade-in {
  animation: list-fade-in 0.5s ease-out forwards;
  will-change: opacity, transform;
  -webkit-backface-visibility: hidden;
  backface-visibility: hidden;
  transform: translateZ(0);
  perspective: 1000;
  opacity: 0; /* 初始状态透明 */
}

/* 列表淡出动画 */
.list-fade-out {
  animation: list-fade-out 0.5s ease-out forwards;
  will-change: opacity, transform;
  -webkit-backface-visibility: hidden;
  backface-visibility: hidden;
  transform: translateZ(0);
}

/* 参数列表项动画 */
.list-fade-in .parameter-item {
  opacity: 0;
  animation: simple-fade-in 0.5s ease-out forwards;
  -webkit-backface-visibility: hidden;
  backface-visibility: hidden;
  transform: translateZ(0);
}

/* 更平滑的延迟动画 */
.list-fade-in .parameter-item:nth-child(1) { animation-delay: 0.05s; }
.list-fade-in .parameter-item:nth-child(2) { animation-delay: 0.1s; }
.list-fade-in .parameter-item:nth-child(3) { animation-delay: 0.15s; }
.list-fade-in .parameter-item:nth-child(4) { animation-delay: 0.2s; }
.list-fade-in .parameter-item:nth-child(5) { animation-delay: 0.25s; }
.list-fade-in .parameter-item:nth-child(6) { animation-delay: 0.3s; }
.list-fade-in .parameter-item:nth-child(7) { animation-delay: 0.35s; }
.list-fade-in .parameter-item:nth-child(8) { animation-delay: 0.4s; }
.list-fade-in .parameter-item:nth-child(9) { animation-delay: 0.45s; }
.list-fade-in .parameter-item:nth-child(10) { animation-delay: 0.5s; }

/* 列表淡入动画关键帧 */
@keyframes list-fade-in {
  from {
    opacity: 0;
    transform: translateY(15rpx) translateZ(0);
  }
  to {
    opacity: 1;
    transform: translateY(0) translateZ(0);
  }
}

/* 列表淡出动画关键帧 */
@keyframes list-fade-out {
  from {
    opacity: 1;
    transform: translateY(0) translateZ(0);
  }
  to {
    opacity: 0;
    transform: translateY(-15rpx) translateZ(0);
  }
}

/* 单个参数项淡入动画 */
@keyframes simple-fade-in {
  from {
    opacity: 0;
    transform: translateY(10rpx) translateZ(0);
  }
  to {
    opacity: 1;
    transform: translateY(0) translateZ(0);
  }
}

/* 统一的元素淡入动画 - 与参数项保持一致 */
.unified-fade-in {
  animation: simple-fade-in 0.5s ease-out forwards;
  will-change: opacity, transform;
  -webkit-backface-visibility: hidden;
  backface-visibility: hidden;
  transform: translateZ(0);
  opacity: 0; /* 初始状态透明 */
}

/* 统一的元素淡出动画 - 与参数项保持一致 */
.unified-fade-out {
  animation: list-fade-out 0.5s ease-out forwards;
  will-change: opacity, transform;
  -webkit-backface-visibility: hidden;
  backface-visibility: hidden;
  transform: translateZ(0);
}

/* 参数模式顶部按钮的统一动画 */
.parameter-top-buttons.unified-fade-in {
  animation: simple-fade-in 0.5s ease-out forwards;
  animation-delay: 0.1s; /* 与参数项第二个的延迟一致 */
}

.parameter-top-buttons.unified-fade-out {
  animation: list-fade-out 0.5s ease-out forwards;
}



/* 删除冗余的video-content样式，统一使用.video-container .video-content */

/* 参数处理Canvas样式 */
/* 🎯 极简Canvas样式 - 确保在检测框之上 */
.parameter-canvas {
  position: absolute !important;
  top: 0 !important;
  left: 0 !important;
  width: 100% !important;
  height: 100% !important;
  z-index: 60 !important; /* 🎯 提高z-index，确保在检测框(53)之上 */
  pointer-events: none !important;
  /* 🎯 保留调试用的红色边框 */
  border: 4px solid #ff0000 !important;
  /* 🎯 移除所有可能导致坐标问题的属性 */
  margin: 0 !important;
  padding: 0 !important;
  background: transparent !important;
  /* 🎯 不使用任何transform、contain等复杂属性 */
}

/* 录制模式下UI元素的动画隐藏 */
.recording-mode .button-grid,
.recording-mode .detection-areas,
.recording-mode .ip-input-container,
.recording-mode .status-container,
.recording-mode .tab-bar,
.recording-mode .content-area,
.recording-mode .progress-panel,
.recording-mode .custom-tabbar,
.recording-mode .search-bar,
.recording-mode .carousel {
  opacity: 0;
  transform: translateY(20rpx);
  transition: all 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  pointer-events: none;
}

/* 全屏模式下完全隐藏所有UI（录制和预览统一） */
.recording-mode {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 99999;
  background: #000;
  /* 确保覆盖整个屏幕包括状态栏 */
  width: 100vw;
  height: 100vh;
  overflow: hidden;
}

/* 强制隐藏页面中的自定义tabBar */
.recording-mode ~ .custom-tabbar,
.container.recording-mode .custom-tabbar {
  display: none !important;
  opacity: 0 !important;
  visibility: hidden !important;
  z-index: -1 !important;
}

/* 录制模式下隐藏检测框 */
.recording-mode .detection-area {
  opacity: 0;
  transition: opacity 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

/* 全屏模式下强制隐藏所有提示（录制和预览统一） */
.recording-mode .exit-fullscreen-tip,
.recording-mode .fullscreen-tip {
  display: none !important;
  opacity: 0 !important;
}

/* 全屏模式下强制隐藏底部导航栏 */
.recording-mode .custom-tabbar {
  display: none !important;
  opacity: 0 !important;
  visibility: hidden !important;
  transform: translateY(200rpx) !important;
}

/* 全屏模式下确保视频控制条正常显示 */
.recording-mode .video-content {
  position: relative !important;
  z-index: 1 !important;
  width: 100% !important;
  height: 100% !important;
}



/* 录制倒计时显示 */
.recording-countdown {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 10000;
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 30rpx 50rpx;
  border-radius: 20rpx;
  font-size: 48rpx;
  font-weight: bold;
  text-align: center;
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  box-shadow: 0 10rpx 30rpx rgba(0, 0, 0, 0.3);
  animation: countdownPulse 1s infinite;
}

@keyframes countdownPulse {
  0%, 100% {
    transform: translate(-50%, -50%) scale(1);
  }
  50% {
    transform: translate(-50%, -50%) scale(1.05);
  }
}

/* 录制准备动画 */
.recording-preparing {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 10001;
  background: rgba(255, 255, 255, 0.95);
  padding: 40rpx 60rpx;
  border-radius: 24rpx;
  text-align: center;
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  box-shadow: 0 20rpx 60rpx rgba(0, 0, 0, 0.15);
  animation: preparingFadeIn 0.5s ease-out;
}

@keyframes preparingFadeIn {
  from {
    opacity: 0;
    transform: translate(-50%, -50%) scale(0.8);
  }
  to {
    opacity: 1;
    transform: translate(-50%, -50%) scale(1);
  }
}

.recording-preparing-text {
  font-size: 32rpx;
  color: #333;
  margin-bottom: 20rpx;
  font-weight: 600;
}

.recording-preparing-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid #f0f0f0;
  border-top: 4rpx solid #4a90e2;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 现有样式保持不变 */

/* 图片悬停效果 */
.image-hover {
  transform: scale(0.98);
  box-shadow: 0 8rpx 16rpx rgba(120, 100, 220, 0.15), 0 3rpx 8rpx rgba(0, 0, 0, 0.05);
  opacity: 0.9;
  transition: all 0.2s ease;
}

/* 全屏提示样式 */
.fullscreen-tip {
  position: fixed;
  top: 20%;
  left: 50%;
  transform: translateX(-50%);
  z-index: 1000;
  background: rgba(0, 0, 0, 0.7);
  color: white;
  padding: 15rpx 30rpx;
  border-radius: 30rpx;
  font-size: 24rpx;
  text-align: center;
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  animation: tipFadeInOut 3s ease-in-out;
  pointer-events: none;
}

.exit-fullscreen-tip {
  position: fixed;
  top: 10%;
  left: 50%;
  transform: translateX(-50%);
  z-index: 10000;
  background: rgba(255, 255, 255, 0.9);
  color: #333;
  padding: 20rpx 40rpx;
  border-radius: 30rpx;
  font-size: 28rpx;
  text-align: center;
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
  animation: tipPulse 2s infinite;
  pointer-events: none;
}

@keyframes tipFadeInOut {
  0%, 100% {
    opacity: 0;
    transform: translateX(-50%) translateY(-10rpx);
  }
  20%, 80% {
    opacity: 1;
    transform: translateX(-50%) translateY(0);
  }
}

@keyframes tipPulse {
  0%, 100% {
    opacity: 0.8;
    transform: translateX(-50%) scale(1);
  }
  50% {
    opacity: 1;
    transform: translateX(-50%) scale(1.05);
  }
}

/* Restart成功提示卡片样式 */
.restart-success-mask {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  /* 🔧 移除强烈的模糊效果，避免界面闪烁 */
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 9999;
  opacity: 0;
  pointer-events: none;
  transition: opacity 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.restart-success-mask.show {
  opacity: 1;
  pointer-events: auto;
}

.restart-success-card {
  width: 600rpx;
  background: linear-gradient(135deg, #ffffff, #f8f9fa);
  border-radius: 32rpx;
  padding: 0;
  box-shadow: 0 20rpx 60rpx rgba(0, 0, 0, 0.3), 0 4rpx 12rpx rgba(0, 0, 0, 0.15), inset 0 0 0 2rpx rgba(255, 255, 255, 0.9);
  position: relative;
  overflow: hidden;
  opacity: 0;
  transform: scale(0.95);
  /* 🔧 移除transition，避免与animation冲突 */
  will-change: transform, opacity;
  outline: none;
  -webkit-mask-image: -webkit-radial-gradient(white, black);
}

.restart-success-card.show {
  animation: restart-card-pop-in 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;
}

/* 确保restart卡片消失动画优先级更高 */
.restart-success-card:not(.show) {
  animation: gentle-fade-down 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards !important;
}

.restart-success-header {
  display: flex;
  align-items: center;
  padding: 40rpx 40rpx 20rpx 40rpx;
  background: linear-gradient(135deg, rgba(120, 185, 255, 0.1), rgba(197, 142, 255, 0.1));
  border-bottom: 1rpx solid rgba(120, 185, 255, 0.1);
}

.restart-success-icon {
  width: 80rpx;
  height: 80rpx;
  background: linear-gradient(90deg, #78b9ff 0%, #c58eff 100%);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 24rpx;
  box-shadow: 0 8rpx 24rpx rgba(120, 185, 255, 0.3);
  animation: success-icon-pulse 2s ease-in-out infinite;
}

.success-checkmark {
  color: white;
  font-size: 40rpx;
  font-weight: bold;
  animation: checkmark-draw 0.6s ease-out forwards;
  animation-delay: 0.2s;
  opacity: 0;
}

.restart-success-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
  background: linear-gradient(90deg, #78b9ff, #c58eff);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.restart-success-content {
  padding: 20rpx 40rpx 30rpx 40rpx;
}

.restart-success-message {
  font-size: 30rpx;
  color: #555;
  margin-bottom: 16rpx;
  font-weight: 500;
}

.restart-success-details {
  font-size: 26rpx;
  color: #888;
  line-height: 1.4;
}

.restart-success-progress {
  padding: 0 40rpx 40rpx 40rpx;
}

.progress-bar-container {
  width: 100%;
  height: 8rpx;
  background: rgba(120, 185, 255, 0.2);
  border-radius: 4rpx;
  overflow: hidden;
}

.progress-bar-fill {
  height: 100%;
  background: linear-gradient(90deg, #78b9ff 0%, #c58eff 100%);
  border-radius: 4rpx;
  width: 0%;
  animation: progress-fill 2s ease-out forwards;
  animation-delay: 0.3s;
}

/* 动画定义 */
/* 🌟 重启卡片优雅动画 */
@keyframes restart-card-pop-in {
  0% {
    opacity: 0;
    transform: scale(0.95);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes success-icon-pulse {
  0%, 100% {
    transform: scale(1);
    box-shadow: 0 8rpx 24rpx rgba(120, 185, 255, 0.3);
  }
  50% {
    transform: scale(1.05);
    box-shadow: 0 12rpx 32rpx rgba(120, 185, 255, 0.4);
  }
}

@keyframes checkmark-draw {
  0% {
    opacity: 0;
    transform: scale(0.5) rotate(-45deg);
  }
  50% {
    opacity: 1;
    transform: scale(1.2) rotate(0deg);
  }
  100% {
    opacity: 1;
    transform: scale(1) rotate(0deg);
  }
}

@keyframes progress-fill {
  0% {
    width: 0%;
  }
  100% {
    width: 100%;
  }
}

/* MCU Start激活提示卡片样式 */
.mcu-start-mask {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  /* 🔧 移除强烈的模糊效果，避免界面闪烁 */
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 9999;
  opacity: 0;
  pointer-events: none;
  transition: opacity 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.mcu-start-mask.show {
  opacity: 1;
  pointer-events: auto;
}

.mcu-start-card {
  width: 600rpx;
  background: linear-gradient(135deg, #ffffff, #f8f9fa);
  border-radius: 32rpx;
  padding: 0;
  box-shadow: 0 20rpx 60rpx rgba(0, 0, 0, 0.3), 0 4rpx 12rpx rgba(0, 0, 0, 0.15), inset 0 0 0 2rpx rgba(255, 255, 255, 0.9);
  position: relative;
  overflow: hidden;
  opacity: 0;
  transform: scale(0.95);
  /* 🔧 移除transition，避免与animation冲突 */
  will-change: transform, opacity;
  outline: none;
  -webkit-mask-image: -webkit-radial-gradient(white, black);
}

.mcu-start-card.show {
  animation: mcu-card-pop-in 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;
}

/* 确保MCU Start卡片消失动画优先级更高 */
.mcu-start-card:not(.show) {
  animation: gentle-fade-down 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards !important;
}

.mcu-start-header {
  display: flex;
  align-items: center;
  padding: 40rpx 40rpx 20rpx 40rpx;
  background: linear-gradient(135deg, rgba(120, 185, 255, 0.1), rgba(197, 142, 255, 0.1));
  border-bottom: 1rpx solid rgba(120, 185, 255, 0.1);
}

.mcu-start-icon {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 24rpx;
  box-shadow: 0 8rpx 24rpx rgba(120, 185, 255, 0.3);
  transition: all 0.3s ease;
}

.mcu-start-icon.preparing {
  background: linear-gradient(90deg, #ffa726 0%, #ff9800 100%);
  animation: mcu-preparing-pulse 1.5s ease-in-out infinite;
}

.mcu-start-icon.success {
  background: linear-gradient(90deg, #78b9ff 0%, #c58eff 100%);
  animation: mcu-success-pulse 2s ease-in-out infinite;
}

.mcu-start-icon.failed {
  background: linear-gradient(90deg, #ff6b6b 0%, #ff5252 100%);
  animation: mcu-failed-shake 0.5s ease-in-out;
}

.mcu-icon-content {
  color: white;
  font-size: 40rpx;
  font-weight: bold;
  animation: mcu-icon-draw 0.6s ease-out forwards;
  animation-delay: 0.2s;
  opacity: 0;
}

.mcu-start-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
  background: linear-gradient(90deg, #78b9ff, #c58eff);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.mcu-start-content {
  padding: 20rpx 40rpx 30rpx 40rpx;
}

.mcu-start-message {
  font-size: 30rpx;
  color: #555;
  margin-bottom: 16rpx;
  font-weight: 500;
}

.mcu-start-details {
  font-size: 26rpx;
  color: #888;
  line-height: 1.4;
}

.mcu-start-progress {
  padding: 0 40rpx 40rpx 40rpx;
}

.mcu-start-progress .progress-bar-container {
  width: 100%;
  height: 8rpx;
  background: rgba(120, 185, 255, 0.2);
  border-radius: 4rpx;
  overflow: hidden;
}

.mcu-start-progress .progress-bar-fill {
  height: 100%;
  background: linear-gradient(90deg, #78b9ff 0%, #c58eff 100%);
  border-radius: 4rpx;
  width: 0%;
  animation: mcu-progress-fill 2s ease-out forwards;
  animation-delay: 0.3s;
}

.mcu-start-progress .progress-bar-fill.complete {
  width: 100%;
  animation: mcu-progress-complete 0.5s ease-out forwards;
}

/* 录制倒计时卡片样式 */
.recording-countdown-mask {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 8888;
  opacity: 0;
  pointer-events: none;
  transition: opacity 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.recording-countdown-mask.show {
  opacity: 1;
  pointer-events: auto;
}

.recording-countdown-card {
  width: 200rpx;
  height: 200rpx;
  background: linear-gradient(135deg, rgba(0, 0, 0, 0.8), rgba(0, 0, 0, 0.9));
  border-radius: 50%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  box-shadow: 0 20rpx 60rpx rgba(0, 0, 0, 0.4), 0 4rpx 12rpx rgba(0, 0, 0, 0.2);
  /* 🔧 移除强烈的模糊效果，避免界面闪烁 */
  border: 3rpx solid rgba(120, 185, 255, 0.3);
  opacity: 0;
  transform: scale(0.5);
  /* 🔧 移除transition，避免与show状态的transform冲突 */
}

.recording-countdown-card.show {
  animation: gentle-countdown-appear 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;
}

/* 确保录制倒计时卡片消失动画优先级更高 */
.recording-countdown-card:not(.show) {
  animation: gentle-countdown-fade 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards !important;
}

.countdown-display {
  font-size: 72rpx;
  font-weight: bold;
  color: white;
  text-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.5);
  animation: countdown-pulse 1s ease-in-out infinite;
}

.countdown-label {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.8);
  margin-top: 8rpx;
  text-shadow: 0 1rpx 4rpx rgba(0, 0, 0, 0.5);
}

.countdown-progress {
  position: absolute;
  bottom: 20rpx;
  left: 20rpx;
  right: 20rpx;
  height: 4rpx;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 2rpx;
  overflow: hidden;
}

.countdown-progress-bar {
  height: 100%;
  background: linear-gradient(90deg, #78b9ff 0%, #c58eff 100%);
  border-radius: 2rpx;
  transition: width 1s ease;
}

/* 上传成功提示卡片样式 */
.upload-success-mask, .upload-failed-mask, .local-upload-success-mask, .avi-select-success-mask {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  /* 🔧 移除强烈的模糊效果，避免界面闪烁 */
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 9999;
  opacity: 0;
  pointer-events: none;
  transition: opacity 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.upload-success-mask.show, .upload-failed-mask.show, .local-upload-success-mask.show, .avi-select-success-mask.show {
  opacity: 1;
  pointer-events: auto;
}

.upload-success-card, .upload-failed-card, .local-upload-success-card, .avi-select-success-card {
  width: 600rpx;
  background: linear-gradient(135deg, #ffffff, #f8f9fa);
  border-radius: 32rpx;
  padding: 0;
  box-shadow: 0 20rpx 60rpx rgba(0, 0, 0, 0.3), 0 4rpx 12rpx rgba(0, 0, 0, 0.15), inset 0 0 0 2rpx rgba(255, 255, 255, 0.9);
  position: relative;
  overflow: hidden;
  opacity: 0;
  transform: scale(0.95);
  /* 🔧 移除transition，避免与animation冲突 */
  will-change: transform, opacity;
  outline: none;
  -webkit-mask-image: -webkit-radial-gradient(white, black);
}

.upload-success-card.show, .upload-failed-card.show, .local-upload-success-card.show, .avi-select-success-card.show {
  animation: upload-card-pop-in 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;
}

/* 确保消失动画优先级更高 */
.upload-success-card:not(.show), .upload-failed-card:not(.show), .local-upload-success-card:not(.show), .avi-select-success-card:not(.show) {
  animation: gentle-fade-down 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards !important;
}

.upload-success-header, .upload-failed-header, .local-upload-success-header, .avi-select-success-header {
  display: flex;
  align-items: center;
  padding: 40rpx 40rpx 20rpx 40rpx;
  background: linear-gradient(135deg, rgba(120, 185, 255, 0.1), rgba(197, 142, 255, 0.1));
  border-bottom: 1rpx solid rgba(120, 185, 255, 0.1);
}

.upload-success-icon, .local-upload-success-icon {
  width: 80rpx;
  height: 80rpx;
  background: linear-gradient(90deg, #78b9ff 0%, #c58eff 100%);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 24rpx;
  box-shadow: 0 8rpx 24rpx rgba(120, 185, 255, 0.3);
  animation: upload-success-pulse 2s ease-in-out infinite;
}

/* AVI选择成功图标 - 与其他卡片保持一致的样式 */
.avi-select-success-icon {
  width: 80rpx;
  height: 80rpx;
  background: linear-gradient(90deg, #4CAF50 0%, #66BB6A 100%);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 24rpx;
  box-shadow: 0 8rpx 24rpx rgba(76, 175, 80, 0.3);
  animation: upload-success-pulse 2s ease-in-out infinite;
}

.upload-failed-icon {
  width: 80rpx;
  height: 80rpx;
  background: linear-gradient(90deg, #ff6b6b 0%, #ff5252 100%);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 24rpx;
  box-shadow: 0 8rpx 24rpx rgba(255, 107, 107, 0.3);
  animation: upload-failed-shake 0.5s ease-in-out;
}

.success-checkmark, .failed-cross {
  color: white;
  font-size: 40rpx;
  font-weight: bold;
  animation: icon-draw 0.6s ease-out forwards;
  animation-delay: 0.2s;
  opacity: 0;
}

.file-select-icon {
  color: white;
  font-size: 40rpx;
  font-weight: bold;
  animation: icon-draw 0.6s ease-out forwards;
  animation-delay: 0.2s;
  opacity: 0;
}

.upload-success-title, .upload-failed-title, .local-upload-success-title, .avi-select-success-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
  background: linear-gradient(90deg, #78b9ff, #c58eff);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.upload-failed-title {
  background: linear-gradient(90deg, #ff6b6b, #ff5252);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

/* AVI选择成功标题 - 特殊的绿色渐变 */
.avi-select-success-title {
  background: linear-gradient(90deg, #4CAF50, #66BB6A) !important;
  background-clip: text !important;
  -webkit-background-clip: text !important;
  -webkit-text-fill-color: transparent !important;
}

.upload-success-content, .upload-failed-content, .local-upload-success-content, .avi-select-success-content {
  padding: 20rpx 40rpx 40rpx 40rpx;
}

.upload-success-message, .upload-failed-message, .local-upload-success-message, .avi-select-success-message {
  font-size: 30rpx;
  color: #555;
  margin-bottom: 16rpx;
  font-weight: 500;
}

.upload-success-details, .upload-failed-details, .local-upload-success-details, .avi-select-success-details {
  font-size: 26rpx;
  color: #888;
  line-height: 1.4;
}

/* 上传处理中提示卡片样式 */
.upload-processing-mask {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  /* 🔧 移除强烈的模糊效果，避免界面闪烁 */
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 9999;
  opacity: 0;
  pointer-events: none;
  transition: opacity 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.upload-processing-mask.show {
  opacity: 1;
  pointer-events: auto;
}

.upload-processing-card {
  width: 600rpx;
  background: linear-gradient(135deg, #ffffff, #f8f9fa);
  border-radius: 32rpx;
  padding: 0;
  box-shadow: 0 20rpx 60rpx rgba(0, 0, 0, 0.3), 0 4rpx 12rpx rgba(0, 0, 0, 0.15), inset 0 0 0 2rpx rgba(255, 255, 255, 0.9);
  position: relative;
  overflow: hidden;
  opacity: 0;
  transform: scale(0.95);
  /* 🔧 移除transition，避免与animation冲突 */
  will-change: transform, opacity;
  outline: none;
  -webkit-mask-image: -webkit-radial-gradient(white, black);
}

.upload-processing-card.show {
  animation: upload-card-pop-in 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;
}

/* 确保上传处理中卡片消失动画优先级更高 */
.upload-processing-card:not(.show) {
  animation: gentle-fade-down 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards !important;
}

.upload-processing-header {
  display: flex;
  align-items: center;
  padding: 40rpx 40rpx 20rpx 40rpx;
  background: linear-gradient(135deg, rgba(120, 185, 255, 0.1), rgba(197, 142, 255, 0.1));
  border-bottom: 1rpx solid rgba(120, 185, 255, 0.1);
}

.upload-processing-icon {
  width: 80rpx;
  height: 80rpx;
  background: linear-gradient(90deg, #78b9ff 0%, #c58eff 100%);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 24rpx;
  box-shadow: 0 8rpx 24rpx rgba(120, 185, 255, 0.3);
  animation: upload-processing-spin 2s linear infinite;
}

.processing-spinner {
  color: white;
  font-size: 40rpx;
  font-weight: bold;
  animation: spinner-rotate 1s linear infinite;
}

.upload-processing-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
  background: linear-gradient(90deg, #78b9ff, #c58eff);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.upload-processing-content {
  padding: 20rpx 40rpx 30rpx 40rpx;
}

.upload-processing-message {
  font-size: 30rpx;
  color: #555;
  margin-bottom: 16rpx;
  font-weight: 500;
}

.upload-processing-details {
  font-size: 26rpx;
  color: #888;
  line-height: 1.4;
}

.upload-processing-progress {
  padding: 0 40rpx 40rpx 40rpx;
}

.upload-processing-progress .progress-bar-container {
  width: 100%;
  height: 8rpx;
  background: rgba(120, 185, 255, 0.2);
  border-radius: 4rpx;
  overflow: hidden;
}

.processing-progress {
  height: 100%;
  background: linear-gradient(90deg, #78b9ff 0%, #c58eff 100%);
  border-radius: 4rpx;
  width: 0%;
  animation: processing-progress-loop 2s ease-in-out infinite;
}

/* MCU Start相关动画 */
/* 🌟 MCU卡片优雅动画 */
@keyframes mcu-card-pop-in {
  0% {
    opacity: 0;
    transform: scale(0.95);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes mcu-preparing-pulse {
  0%, 100% {
    transform: scale(1);
    box-shadow: 0 8rpx 24rpx rgba(255, 167, 38, 0.3);
  }
  50% {
    transform: scale(1.05);
    box-shadow: 0 12rpx 32rpx rgba(255, 167, 38, 0.4);
  }
}

@keyframes mcu-success-pulse {
  0%, 100% {
    transform: scale(1);
    box-shadow: 0 8rpx 24rpx rgba(120, 185, 255, 0.3);
  }
  50% {
    transform: scale(1.05);
    box-shadow: 0 12rpx 32rpx rgba(120, 185, 255, 0.4);
  }
}

@keyframes mcu-failed-shake {
  0%, 100% { transform: translateX(0); }
  25% { transform: translateX(-10rpx); }
  75% { transform: translateX(10rpx); }
}

@keyframes mcu-icon-draw {
  0% {
    opacity: 0;
    transform: scale(0.5) rotate(-45deg);
  }
  50% {
    opacity: 1;
    transform: scale(1.2) rotate(0deg);
  }
  100% {
    opacity: 1;
    transform: scale(1) rotate(0deg);
  }
}

@keyframes mcu-progress-fill {
  0% {
    width: 0%;
  }
  100% {
    width: 50%;
  }
}

@keyframes mcu-progress-complete {
  0% {
    width: 50%;
  }
  100% {
    width: 100%;
  }
}

/* 录制倒计时动画 */
@keyframes countdown-pulse {
  0%, 100% {
    transform: scale(1);
    text-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.5);
  }
  50% {
    transform: scale(1.1);
    text-shadow: 0 4rpx 16rpx rgba(120, 185, 255, 0.6);
  }
}

/* 上传相关动画 */
/* 🌟 优雅的纯淡入缩放动画 - 无位移，极致顺畅 */
@keyframes upload-card-pop-in {
  0% {
    opacity: 0;
    transform: scale(0.95);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes upload-success-pulse {
  0%, 100% {
    transform: scale(1);
    box-shadow: 0 8rpx 24rpx rgba(120, 185, 255, 0.3);
  }
  50% {
    transform: scale(1.05);
    box-shadow: 0 12rpx 32rpx rgba(120, 185, 255, 0.4);
  }
}

@keyframes upload-failed-shake {
  0%, 100% { transform: translateX(0); }
  25% { transform: translateX(-10rpx); }
  75% { transform: translateX(10rpx); }
}

@keyframes icon-draw {
  0% {
    opacity: 0;
    transform: scale(0.5) rotate(-45deg);
  }
  50% {
    opacity: 1;
    transform: scale(1.2) rotate(0deg);
  }
  100% {
    opacity: 1;
    transform: scale(1) rotate(0deg);
  }
}

/* AVI文件选择动画已统一使用标准动画 */

/* 上传处理中相关动画 */
@keyframes upload-processing-spin {
  0%, 100% {
    transform: scale(1);
    box-shadow: 0 8rpx 24rpx rgba(120, 185, 255, 0.3);
  }
  50% {
    transform: scale(1.05);
    box-shadow: 0 12rpx 32rpx rgba(120, 185, 255, 0.4);
  }
}

/* 分析准备中提示卡片样式 */
.analysis-preparing-mask {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.6);
  backdrop-filter: blur(8rpx);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 9999;
  opacity: 0;
  pointer-events: none;
  transition: opacity 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.analysis-preparing-mask.show {
  opacity: 1;
  pointer-events: auto;
}

.analysis-preparing-card {
  width: 600rpx;
  background: linear-gradient(135deg, #ffffff, #f8f9fa);
  border-radius: 32rpx;
  padding: 0;
  box-shadow: 0 20rpx 60rpx rgba(0, 0, 0, 0.3), 0 4rpx 12rpx rgba(0, 0, 0, 0.15), inset 0 0 0 2rpx rgba(255, 255, 255, 0.9);
  position: relative;
  overflow: hidden;
  opacity: 0;
  transform: scale(0.95);
  will-change: transform, opacity;
  outline: none;
  -webkit-mask-image: -webkit-radial-gradient(white, black);
}

.analysis-preparing-card.show {
  animation: analysis-card-pop-in 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;
}

/* 确保分析准备中卡片消失动画优先级更高 */
.analysis-preparing-card:not(.show) {
  animation: gentle-fade-down 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards !important;
}

.analysis-preparing-header {
  display: flex;
  align-items: center;
  padding: 40rpx 40rpx 20rpx 40rpx;
  background: linear-gradient(135deg, rgba(120, 185, 255, 0.1), rgba(197, 142, 255, 0.1));
  border-bottom: 1rpx solid rgba(120, 185, 255, 0.1);
}

.analysis-preparing-icon {
  width: 80rpx;
  height: 80rpx;
  background: linear-gradient(90deg, #78b9ff 0%, #c58eff 100%);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 24rpx;
  box-shadow: 0 8rpx 24rpx rgba(120, 185, 255, 0.3);
  animation: analysis-preparing-pulse 2s ease-in-out infinite;
}

.preparing-icon {
  color: white;
  font-size: 40rpx;
  font-weight: bold;
  animation: preparing-icon-rotate 2s linear infinite;
}

.analysis-preparing-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
  background: linear-gradient(90deg, #78b9ff, #c58eff);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.analysis-preparing-content {
  padding: 20rpx 40rpx 30rpx 40rpx;
}

.analysis-preparing-message {
  font-size: 30rpx;
  color: #555;
  margin-bottom: 16rpx;
  font-weight: 500;
}

.analysis-preparing-details {
  font-size: 26rpx;
  color: #888;
  line-height: 1.4;
}

.analysis-preparing-progress {
  padding: 0 40rpx 40rpx 40rpx;
}

.analysis-preparing-progress .progress-bar-container {
  width: 100%;
  height: 8rpx;
  background: rgba(120, 185, 255, 0.2);
  border-radius: 4rpx;
  overflow: hidden;
}

.preparing-progress {
  height: 100%;
  background: linear-gradient(90deg, #78b9ff, #c58eff);
  border-radius: 4rpx;
  animation: preparing-progress-flow 2s ease-in-out infinite;
}

/* 分析准备中相关动画 */
@keyframes analysis-card-pop-in {
  0% {
    opacity: 0;
    transform: scale(0.8) translateY(40rpx);
  }
  60% {
    opacity: 1;
    transform: scale(1.02) translateY(-8rpx);
  }
  100% {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

@keyframes analysis-preparing-pulse {
  0%, 100% {
    transform: scale(1);
    box-shadow: 0 8rpx 24rpx rgba(120, 185, 255, 0.3);
  }
  50% {
    transform: scale(1.05);
    box-shadow: 0 12rpx 32rpx rgba(120, 185, 255, 0.4);
  }
}

@keyframes preparing-icon-rotate {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

@keyframes preparing-progress-flow {
  0% {
    width: 20%;
    transform: translateX(-100%);
  }
  50% {
    width: 60%;
    transform: translateX(50%);
  }
  100% {
    width: 20%;
    transform: translateX(200%);
  }
}

@keyframes spinner-rotate {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

@keyframes processing-progress-loop {
  0% {
    width: 0%;
  }
  50% {
    width: 70%;
  }
  100% {
    width: 0%;
  }
}

/* Q弹慢慢消失动画 - 统一定义 */

/* 备用的hide类，用于手动触发消失动画 */
.restart-success-card.hide,
.mcu-start-card.hide,
.upload-success-card.hide,
.upload-failed-card.hide,
.local-upload-success-card.hide,
.avi-select-success-card.hide,
.upload-processing-card.hide,
.connecting-card.hide,
.disconnected-card.hide {
  animation: gentle-fade-down 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards !important;
}

.recording-countdown-card.hide {
  animation: gentle-countdown-fade 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards !important;
}

/* 🌟 优雅的纯淡出缩放动画 - 无位移，极致顺畅 */
@keyframes gentle-fade-down {
  0% {
    opacity: 1;
    transform: scale(1);
  }
  100% {
    opacity: 0;
    transform: scale(0.95);
  }
}

/* 🌟 倒计时卡片温和出现动画 */
@keyframes gentle-countdown-appear {
  0% {
    opacity: 0;
    transform: scale(0.5);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

/* 🌟 倒计时卡片温和消失动画 */
@keyframes gentle-countdown-fade {
  0% {
    opacity: 1;
    transform: scale(1);
  }
  100% {
    opacity: 0;
    transform: scale(0.94);
  }
}

/* 🌟 卡片切换动画 - 温和过渡效果 */
.card-transition-out {
  animation: card-smooth-fade-out 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;
}

.card-transition-in {
  animation: card-smooth-fade-in 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;
  animation-delay: 0.2s; /* 等待前一个卡片消失 */
}

/* 🌟 卡片切换优雅消失动画 */
@keyframes card-smooth-fade-out {
  0% {
    opacity: 1;
    transform: scale(1);
  }
  100% {
    opacity: 0;
    transform: scale(0.95);
  }
}

/* 🌟 卡片切换优雅出现动画 */
@keyframes card-smooth-fade-in {
  0% {
    opacity: 0;
    transform: scale(0.95);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

/* 🌟 特殊的卡片切换动画 - 温和的相关卡片切换 */
.upload-processing-to-success {
  animation: processing-to-success 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;
}

.upload-processing-to-failed {
  animation: processing-to-failed 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;
}

/* 🌟 温和的处理成功切换动画 */
@keyframes processing-to-success {
  0% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.8;
    transform: scale(1.02);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

/* 🌟 温和的处理失败切换动画 */
@keyframes processing-to-failed {
  0% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.7;
    transform: scale(0.98);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

/* 🆕 设备连接中卡片样式 - 统一风格 */
.connecting-mask {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  /* 🔧 移除强烈的模糊效果，避免界面闪烁 */
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 9999;
  opacity: 0;
  pointer-events: none;
  transition: opacity 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.connecting-mask.show {
  opacity: 1;
  pointer-events: auto;
}

.connecting-card {
  width: 600rpx;
  background: linear-gradient(135deg, #ffffff, #f8f9fa);
  border-radius: 32rpx;
  padding: 0;
  box-shadow: 0 20rpx 60rpx rgba(0, 0, 0, 0.3), 0 4rpx 12rpx rgba(0, 0, 0, 0.15), inset 0 0 0 2rpx rgba(255, 255, 255, 0.9);
  position: relative;
  overflow: hidden;
  opacity: 0;
  transform: scale(0.95);
  /* 🔧 移除transition，避免与animation冲突 */
  will-change: transform, opacity;
  outline: none;
  -webkit-mask-image: -webkit-radial-gradient(white, black);
}

.connecting-card.show {
  animation: upload-card-pop-in 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;
}

/* 确保连接中卡片消失动画优先级更高 */
.connecting-card:not(.show) {
  animation: gentle-fade-down 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards !important;
}

.connecting-header {
  display: flex;
  align-items: center;
  padding: 40rpx 40rpx 20rpx 40rpx;
  background: linear-gradient(135deg, rgba(120, 185, 255, 0.1), rgba(197, 142, 255, 0.1));
  border-bottom: 1rpx solid rgba(120, 185, 255, 0.1);
}

.connecting-icon {
  width: 80rpx;
  height: 80rpx;
  background: linear-gradient(90deg, #78b9ff 0%, #c58eff 100%);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 24rpx;
  box-shadow: 0 8rpx 24rpx rgba(120, 185, 255, 0.3);
  animation: connecting-pulse 1.5s ease-in-out infinite;
}

.connecting-spinner {
  color: white;
  font-size: 40rpx;
  font-weight: bold;
  animation: connecting-spin 1s linear infinite;
}

@keyframes connecting-spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

@keyframes connecting-pulse {
  0%, 100% {
    transform: scale(1);
    box-shadow: 0 8rpx 24rpx rgba(120, 185, 255, 0.3);
  }
  50% {
    transform: scale(1.05);
    box-shadow: 0 12rpx 32rpx rgba(120, 185, 255, 0.4);
  }
}

.connecting-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
  background: linear-gradient(90deg, #78b9ff, #c58eff);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.connecting-content {
  padding: 20rpx 40rpx 30rpx 40rpx;
}

.connecting-message {
  font-size: 30rpx;
  color: #555;
  margin-bottom: 16rpx;
  font-weight: 500;
}

.connecting-details {
  font-size: 26rpx;
  color: #888;
  line-height: 1.4;
}

.connecting-progress {
  padding: 0 40rpx 40rpx 40rpx;
}

.connecting-progress .progress-bar-container {
  width: 100%;
  height: 8rpx;
  background: rgba(120, 185, 255, 0.2);
  border-radius: 4rpx;
  overflow: hidden;
}

.connecting-progress .progress-bar-fill {
  height: 100%;
  background: linear-gradient(90deg, #78b9ff 0%, #c58eff 100%);
  border-radius: 4rpx;
  animation: connecting-progress 2s ease-in-out infinite;
}

@keyframes connecting-progress {
  0% { width: 20%; }
  50% { width: 80%; }
  100% { width: 20%; }
}

/* 🆕 设备断开连接成功卡片样式 - 统一风格 */
.disconnected-mask {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  /* 🔧 移除强烈的模糊效果，避免界面闪烁 */
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 9999;
  opacity: 0;
  pointer-events: none;
  transition: opacity 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.disconnected-mask.show {
  opacity: 1;
  pointer-events: auto;
}

.disconnected-card {
  width: 600rpx;
  background: linear-gradient(135deg, #ffffff, #f8f9fa);
  border-radius: 32rpx;
  padding: 0;
  box-shadow: 0 20rpx 60rpx rgba(0, 0, 0, 0.3), 0 4rpx 12rpx rgba(0, 0, 0, 0.15), inset 0 0 0 2rpx rgba(255, 255, 255, 0.9);
  position: relative;
  overflow: hidden;
  opacity: 0;
  transform: scale(0.95);
  /* 🔧 移除transition，避免与animation冲突 */
  will-change: transform, opacity;
  outline: none;
  -webkit-mask-image: -webkit-radial-gradient(white, black);
}

.disconnected-card.show {
  animation: upload-card-pop-in 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;
}

/* 确保断开连接卡片消失动画优先级更高 */
.disconnected-card:not(.show) {
  animation: gentle-fade-down 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards !important;
}

.disconnected-header {
  display: flex;
  align-items: center;
  padding: 40rpx 40rpx 20rpx 40rpx;
  background: linear-gradient(135deg, rgba(120, 185, 255, 0.1), rgba(197, 142, 255, 0.1));
  border-bottom: 1rpx solid rgba(120, 185, 255, 0.1);
}

.disconnected-icon {
  width: 80rpx;
  height: 80rpx;
  background: linear-gradient(90deg, #78b9ff 0%, #c58eff 100%);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 24rpx;
  box-shadow: 0 8rpx 24rpx rgba(120, 185, 255, 0.3);
  animation: disconnected-pulse 2s ease-in-out infinite;
}

.disconnected-icon .success-checkmark {
  color: white;
  font-size: 40rpx;
  font-weight: bold;
  animation: checkmark-draw 0.6s ease-out forwards;
  animation-delay: 0.2s;
  opacity: 0;
}

@keyframes disconnected-pulse {
  0%, 100% {
    transform: scale(1);
    box-shadow: 0 8rpx 24rpx rgba(120, 185, 255, 0.3);
  }
  50% {
    transform: scale(1.05);
    box-shadow: 0 12rpx 32rpx rgba(120, 185, 255, 0.4);
  }
}

.disconnected-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
  background: linear-gradient(90deg, #78b9ff, #c58eff);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.disconnected-content {
  padding: 20rpx 40rpx 40rpx 40rpx;
}

.disconnected-message {
  font-size: 30rpx;
  color: #555;
  margin-bottom: 16rpx;
  font-weight: 500;
}

.disconnected-details {
  font-size: 26rpx;
  color: #888;
  line-height: 1.4;
}/*  */