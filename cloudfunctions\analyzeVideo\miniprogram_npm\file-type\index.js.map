{"version": 3, "sources": ["index.js"], "names": [], "mappings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file": "index.js", "sourcesContent": ["\nconst toBytes = s => [...s].map(c => c.charCodeAt(0));\nconst xpiZipFilename = toBytes('META-INF/mozilla.rsa');\nconst oxmlContentTypes = toBytes('[Content_Types].xml');\nconst oxmlRels = toBytes('_rels/.rels');\n\nmodule.exports = input => {\n\tconst buf = input instanceof Uint8Array ? input : new Uint8Array(input);\n\n\tif (!(buf && buf.length > 1)) {\n\t\treturn null;\n\t}\n\n\tconst check = (header, options) => {\n\t\toptions = Object.assign({\n\t\t\toffset: 0\n\t\t}, options);\n\n\t\tfor (let i = 0; i < header.length; i++) {\n\t\t\t// If a bitmask is set\n\t\t\tif (options.mask) {\n\t\t\t\t// If header doesn't equal `buf` with bits masked off\n\t\t\t\tif (header[i] !== (options.mask[i] & buf[i + options.offset])) {\n\t\t\t\t\treturn false;\n\t\t\t\t}\n\t\t\t} else if (header[i] !== buf[i + options.offset]) {\n\t\t\t\treturn false;\n\t\t\t}\n\t\t}\n\n\t\treturn true;\n\t};\n\n\tconst checkString = (header, options) => check(toBytes(header), options);\n\n\tif (check([0xFF, 0xD8, 0xFF])) {\n\t\treturn {\n\t\t\text: 'jpg',\n\t\t\tmime: 'image/jpeg'\n\t\t};\n\t}\n\n\tif (check([0x89, 0x50, 0x4E, 0x47, 0x0D, 0x0A, 0x1A, 0x0A])) {\n\t\treturn {\n\t\t\text: 'png',\n\t\t\tmime: 'image/png'\n\t\t};\n\t}\n\n\tif (check([0x47, 0x49, 0x46])) {\n\t\treturn {\n\t\t\text: 'gif',\n\t\t\tmime: 'image/gif'\n\t\t};\n\t}\n\n\tif (check([0x57, 0x45, 0x42, 0x50], {offset: 8})) {\n\t\treturn {\n\t\t\text: 'webp',\n\t\t\tmime: 'image/webp'\n\t\t};\n\t}\n\n\tif (check([0x46, 0x4C, 0x49, 0x46])) {\n\t\treturn {\n\t\t\text: 'flif',\n\t\t\tmime: 'image/flif'\n\t\t};\n\t}\n\n\t// Needs to be before `tif` check\n\tif (\n\t\t(check([0x49, 0x49, 0x2A, 0x0]) || check([0x4D, 0x4D, 0x0, 0x2A])) &&\n\t\tcheck([0x43, 0x52], {offset: 8})\n\t) {\n\t\treturn {\n\t\t\text: 'cr2',\n\t\t\tmime: 'image/x-canon-cr2'\n\t\t};\n\t}\n\n\tif (\n\t\tcheck([0x49, 0x49, 0x2A, 0x0]) ||\n\t\tcheck([0x4D, 0x4D, 0x0, 0x2A])\n\t) {\n\t\treturn {\n\t\t\text: 'tif',\n\t\t\tmime: 'image/tiff'\n\t\t};\n\t}\n\n\tif (check([0x42, 0x4D])) {\n\t\treturn {\n\t\t\text: 'bmp',\n\t\t\tmime: 'image/bmp'\n\t\t};\n\t}\n\n\tif (check([0x49, 0x49, 0xBC])) {\n\t\treturn {\n\t\t\text: 'jxr',\n\t\t\tmime: 'image/vnd.ms-photo'\n\t\t};\n\t}\n\n\tif (check([0x38, 0x42, 0x50, 0x53])) {\n\t\treturn {\n\t\t\text: 'psd',\n\t\t\tmime: 'image/vnd.adobe.photoshop'\n\t\t};\n\t}\n\n\t// Zip-based file formats\n\t// Need to be before the `zip` check\n\tif (check([0x50, 0x4B, 0x3, 0x4])) {\n\t\tif (\n\t\t\tcheck([0x6D, 0x69, 0x6D, 0x65, 0x74, 0x79, 0x70, 0x65, 0x61, 0x70, 0x70, 0x6C, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6F, 0x6E, 0x2F, 0x65, 0x70, 0x75, 0x62, 0x2B, 0x7A, 0x69, 0x70], {offset: 30})\n\t\t) {\n\t\t\treturn {\n\t\t\t\text: 'epub',\n\t\t\t\tmime: 'application/epub+zip'\n\t\t\t};\n\t\t}\n\n\t\t// Assumes signed `.xpi` from addons.mozilla.org\n\t\tif (check(xpiZipFilename, {offset: 30})) {\n\t\t\treturn {\n\t\t\t\text: 'xpi',\n\t\t\t\tmime: 'application/x-xpinstall'\n\t\t\t};\n\t\t}\n\n\t\tif (checkString('mimetypeapplication/vnd.oasis.opendocument.text', {offset: 30})) {\n\t\t\treturn {\n\t\t\t\text: 'odt',\n\t\t\t\tmime: 'application/vnd.oasis.opendocument.text'\n\t\t\t};\n\t\t}\n\n\t\tif (checkString('mimetypeapplication/vnd.oasis.opendocument.spreadsheet', {offset: 30})) {\n\t\t\treturn {\n\t\t\t\text: 'ods',\n\t\t\t\tmime: 'application/vnd.oasis.opendocument.spreadsheet'\n\t\t\t};\n\t\t}\n\n\t\tif (checkString('mimetypeapplication/vnd.oasis.opendocument.presentation', {offset: 30})) {\n\t\t\treturn {\n\t\t\t\text: 'odp',\n\t\t\t\tmime: 'application/vnd.oasis.opendocument.presentation'\n\t\t\t};\n\t\t}\n\n\t\t// The docx, xlsx and pptx file types extend the Office Open XML file format:\n\t\t// https://en.wikipedia.org/wiki/Office_Open_XML_file_formats\n\t\t// We look for:\n\t\t// - one entry named '[Content_Types].xml' or '_rels/.rels',\n\t\t// - one entry indicating specific type of file.\n\t\t// MS Office, OpenOffice and LibreOffice may put the parts in different order, so the check should not rely on it.\n\t\tconst findNextZipHeaderIndex = (arr, startAt = 0) => arr.findIndex((el, i, arr) => i >= startAt && arr[i] === 0x50 && arr[i + 1] === 0x4B && arr[i + 2] === 0x3 && arr[i + 3] === 0x4);\n\n\t\tlet zipHeaderIndex = 0; // The first zip header was already found at index 0\n\t\tlet oxmlFound = false;\n\t\tlet type = null;\n\n\t\tdo {\n\t\t\tconst offset = zipHeaderIndex + 30;\n\n\t\t\tif (!oxmlFound) {\n\t\t\t\toxmlFound = (check(oxmlContentTypes, {offset}) || check(oxmlRels, {offset}));\n\t\t\t}\n\n\t\t\tif (!type) {\n\t\t\t\tif (checkString('word/', {offset})) {\n\t\t\t\t\ttype = {\n\t\t\t\t\t\text: 'docx',\n\t\t\t\t\t\tmime: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'\n\t\t\t\t\t};\n\t\t\t\t} else if (checkString('ppt/', {offset})) {\n\t\t\t\t\ttype = {\n\t\t\t\t\t\text: 'pptx',\n\t\t\t\t\t\tmime: 'application/vnd.openxmlformats-officedocument.presentationml.presentation'\n\t\t\t\t\t};\n\t\t\t\t} else if (checkString('xl/', {offset})) {\n\t\t\t\t\ttype = {\n\t\t\t\t\t\text: 'xlsx',\n\t\t\t\t\t\tmime: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'\n\t\t\t\t\t};\n\t\t\t\t}\n\t\t\t}\n\n\t\t\tif (oxmlFound && type) {\n\t\t\t\treturn type;\n\t\t\t}\n\n\t\t\tzipHeaderIndex = findNextZipHeaderIndex(buf, offset);\n\t\t} while (zipHeaderIndex >= 0);\n\n\t\t// No more zip parts available in the buffer, but maybe we are almost certain about the type?\n\t\tif (type) {\n\t\t\treturn type;\n\t\t}\n\t}\n\n\tif (\n\t\tcheck([0x50, 0x4B]) &&\n\t\t(buf[2] === 0x3 || buf[2] === 0x5 || buf[2] === 0x7) &&\n\t\t(buf[3] === 0x4 || buf[3] === 0x6 || buf[3] === 0x8)\n\t) {\n\t\treturn {\n\t\t\text: 'zip',\n\t\t\tmime: 'application/zip'\n\t\t};\n\t}\n\n\tif (check([0x75, 0x73, 0x74, 0x61, 0x72], {offset: 257})) {\n\t\treturn {\n\t\t\text: 'tar',\n\t\t\tmime: 'application/x-tar'\n\t\t};\n\t}\n\n\tif (\n\t\tcheck([0x52, 0x61, 0x72, 0x21, 0x1A, 0x7]) &&\n\t\t(buf[6] === 0x0 || buf[6] === 0x1)\n\t) {\n\t\treturn {\n\t\t\text: 'rar',\n\t\t\tmime: 'application/x-rar-compressed'\n\t\t};\n\t}\n\n\tif (check([0x1F, 0x8B, 0x8])) {\n\t\treturn {\n\t\t\text: 'gz',\n\t\t\tmime: 'application/gzip'\n\t\t};\n\t}\n\n\tif (check([0x42, 0x5A, 0x68])) {\n\t\treturn {\n\t\t\text: 'bz2',\n\t\t\tmime: 'application/x-bzip2'\n\t\t};\n\t}\n\n\tif (check([0x37, 0x7A, 0xBC, 0xAF, 0x27, 0x1C])) {\n\t\treturn {\n\t\t\text: '7z',\n\t\t\tmime: 'application/x-7z-compressed'\n\t\t};\n\t}\n\n\tif (check([0x78, 0x01])) {\n\t\treturn {\n\t\t\text: 'dmg',\n\t\t\tmime: 'application/x-apple-diskimage'\n\t\t};\n\t}\n\n\tif (check([0x33, 0x67, 0x70, 0x35]) || // 3gp5\n\t\t(\n\t\t\tcheck([0x0, 0x0, 0x0]) && check([0x66, 0x74, 0x79, 0x70], {offset: 4}) &&\n\t\t\t\t(\n\t\t\t\t\tcheck([0x6D, 0x70, 0x34, 0x31], {offset: 8}) || // MP41\n\t\t\t\t\tcheck([0x6D, 0x70, 0x34, 0x32], {offset: 8}) || // MP42\n\t\t\t\t\tcheck([0x69, 0x73, 0x6F, 0x6D], {offset: 8}) || // ISOM\n\t\t\t\t\tcheck([0x69, 0x73, 0x6F, 0x32], {offset: 8}) || // ISO2\n\t\t\t\t\tcheck([0x6D, 0x6D, 0x70, 0x34], {offset: 8}) || // MMP4\n\t\t\t\t\tcheck([0x4D, 0x34, 0x56], {offset: 8}) || // M4V\n\t\t\t\t\tcheck([0x64, 0x61, 0x73, 0x68], {offset: 8}) // DASH\n\t\t\t\t)\n\t\t)) {\n\t\treturn {\n\t\t\text: 'mp4',\n\t\t\tmime: 'video/mp4'\n\t\t};\n\t}\n\n\tif (check([0x4D, 0x54, 0x68, 0x64])) {\n\t\treturn {\n\t\t\text: 'mid',\n\t\t\tmime: 'audio/midi'\n\t\t};\n\t}\n\n\t// https://github.com/threatstack/libmagic/blob/master/magic/Magdir/matroska\n\tif (check([0x1A, 0x45, 0xDF, 0xA3])) {\n\t\tconst sliced = buf.subarray(4, 4 + 4096);\n\t\tconst idPos = sliced.findIndex((el, i, arr) => arr[i] === 0x42 && arr[i + 1] === 0x82);\n\n\t\tif (idPos !== -1) {\n\t\t\tconst docTypePos = idPos + 3;\n\t\t\tconst findDocType = type => [...type].every((c, i) => sliced[docTypePos + i] === c.charCodeAt(0));\n\n\t\t\tif (findDocType('matroska')) {\n\t\t\t\treturn {\n\t\t\t\t\text: 'mkv',\n\t\t\t\t\tmime: 'video/x-matroska'\n\t\t\t\t};\n\t\t\t}\n\n\t\t\tif (findDocType('webm')) {\n\t\t\t\treturn {\n\t\t\t\t\text: 'webm',\n\t\t\t\t\tmime: 'video/webm'\n\t\t\t\t};\n\t\t\t}\n\t\t}\n\t}\n\n\tif (check([0x0, 0x0, 0x0, 0x14, 0x66, 0x74, 0x79, 0x70, 0x71, 0x74, 0x20, 0x20]) ||\n\t\tcheck([0x66, 0x72, 0x65, 0x65], {offset: 4}) ||\n\t\tcheck([0x66, 0x74, 0x79, 0x70, 0x71, 0x74, 0x20, 0x20], {offset: 4}) ||\n\t\tcheck([0x6D, 0x64, 0x61, 0x74], {offset: 4}) || // MJPEG\n\t\tcheck([0x77, 0x69, 0x64, 0x65], {offset: 4})) {\n\t\treturn {\n\t\t\text: 'mov',\n\t\t\tmime: 'video/quicktime'\n\t\t};\n\t}\n\n\t// RIFF file format which might be AVI, WAV, QCP, etc\n\tif (check([0x52, 0x49, 0x46, 0x46])) {\n\t\tif (check([0x41, 0x56, 0x49], {offset: 8})) {\n\t\t\treturn {\n\t\t\t\text: 'avi',\n\t\t\t\tmime: 'video/vnd.avi'\n\t\t\t};\n\t\t}\n\t\tif (check([0x57, 0x41, 0x56, 0x45], {offset: 8})) {\n\t\t\treturn {\n\t\t\t\text: 'wav',\n\t\t\t\tmime: 'audio/vnd.wave'\n\t\t\t};\n\t\t}\n\t\t// QLCM, QCP file\n\t\tif (check([0x51, 0x4C, 0x43, 0x4D], {offset: 8})) {\n\t\t\treturn {\n\t\t\t\text: 'qcp',\n\t\t\t\tmime: 'audio/qcelp'\n\t\t\t};\n\t\t}\n\t}\n\n\tif (check([0x30, 0x26, 0xB2, 0x75, 0x8E, 0x66, 0xCF, 0x11, 0xA6, 0xD9])) {\n\t\treturn {\n\t\t\text: 'wmv',\n\t\t\tmime: 'video/x-ms-wmv'\n\t\t};\n\t}\n\n\tif (\n\t\tcheck([0x0, 0x0, 0x1, 0xBA]) ||\n\t\tcheck([0x0, 0x0, 0x1, 0xB3])\n\t) {\n\t\treturn {\n\t\t\text: 'mpg',\n\t\t\tmime: 'video/mpeg'\n\t\t};\n\t}\n\n\tif (check([0x66, 0x74, 0x79, 0x70, 0x33, 0x67], {offset: 4})) {\n\t\treturn {\n\t\t\text: '3gp',\n\t\t\tmime: 'video/3gpp'\n\t\t};\n\t}\n\n\t// Check for MPEG header at different starting offsets\n\tfor (let start = 0; start < 2 && start < (buf.length - 16); start++) {\n\t\tif (\n\t\t\tcheck([0x49, 0x44, 0x33], {offset: start}) || // ID3 header\n\t\t\tcheck([0xFF, 0xE2], {offset: start, mask: [0xFF, 0xE2]}) // MPEG 1 or 2 Layer 3 header\n\t\t) {\n\t\t\treturn {\n\t\t\t\text: 'mp3',\n\t\t\t\tmime: 'audio/mpeg'\n\t\t\t};\n\t\t}\n\n\t\tif (\n\t\t\tcheck([0xFF, 0xE4], {offset: start, mask: [0xFF, 0xE4]}) // MPEG 1 or 2 Layer 2 header\n\t\t) {\n\t\t\treturn {\n\t\t\t\text: 'mp2',\n\t\t\t\tmime: 'audio/mpeg'\n\t\t\t};\n\t\t}\n\n\t\tif (\n\t\t\tcheck([0xFF, 0xF8], {offset: start, mask: [0xFF, 0xFC]}) // MPEG 2 layer 0 using ADTS\n\t\t) {\n\t\t\treturn {\n\t\t\t\text: 'mp2',\n\t\t\t\tmime: 'audio/mpeg'\n\t\t\t};\n\t\t}\n\n\t\tif (\n\t\t\tcheck([0xFF, 0xF0], {offset: start, mask: [0xFF, 0xFC]}) // MPEG 4 layer 0 using ADTS\n\t\t) {\n\t\t\treturn {\n\t\t\t\text: 'mp4',\n\t\t\t\tmime: 'audio/mpeg'\n\t\t\t};\n\t\t}\n\t}\n\n\tif (\n\t\tcheck([0x66, 0x74, 0x79, 0x70, 0x4D, 0x34, 0x41], {offset: 4}) ||\n\t\tcheck([0x4D, 0x34, 0x41, 0x20])\n\t) {\n\t\treturn { // MPEG-4 layer 3 (audio)\n\t\t\text: 'm4a',\n\t\t\tmime: 'audio/mp4' // RFC 4337\n\t\t};\n\t}\n\n\t// Needs to be before `ogg` check\n\tif (check([0x4F, 0x70, 0x75, 0x73, 0x48, 0x65, 0x61, 0x64], {offset: 28})) {\n\t\treturn {\n\t\t\text: 'opus',\n\t\t\tmime: 'audio/opus'\n\t\t};\n\t}\n\n\t// If 'OggS' in first  bytes, then OGG container\n\tif (check([0x4F, 0x67, 0x67, 0x53])) {\n\t\t// This is a OGG container\n\n\t\t// If ' theora' in header.\n\t\tif (check([0x80, 0x74, 0x68, 0x65, 0x6F, 0x72, 0x61], {offset: 28})) {\n\t\t\treturn {\n\t\t\t\text: 'ogv',\n\t\t\t\tmime: 'video/ogg'\n\t\t\t};\n\t\t}\n\t\t// If '\\x01video' in header.\n\t\tif (check([0x01, 0x76, 0x69, 0x64, 0x65, 0x6F, 0x00], {offset: 28})) {\n\t\t\treturn {\n\t\t\t\text: 'ogm',\n\t\t\t\tmime: 'video/ogg'\n\t\t\t};\n\t\t}\n\t\t// If ' FLAC' in header  https://xiph.org/flac/faq.html\n\t\tif (check([0x7F, 0x46, 0x4C, 0x41, 0x43], {offset: 28})) {\n\t\t\treturn {\n\t\t\t\text: 'oga',\n\t\t\t\tmime: 'audio/ogg'\n\t\t\t};\n\t\t}\n\n\t\t// 'Speex  ' in header https://en.wikipedia.org/wiki/Speex\n\t\tif (check([0x53, 0x70, 0x65, 0x65, 0x78, 0x20, 0x20], {offset: 28})) {\n\t\t\treturn {\n\t\t\t\text: 'spx',\n\t\t\t\tmime: 'audio/ogg'\n\t\t\t};\n\t\t}\n\n\t\t// If '\\x01vorbis' in header\n\t\tif (check([0x01, 0x76, 0x6F, 0x72, 0x62, 0x69, 0x73], {offset: 28})) {\n\t\t\treturn {\n\t\t\t\text: 'ogg',\n\t\t\t\tmime: 'audio/ogg'\n\t\t\t};\n\t\t}\n\n\t\t// Default OGG container https://www.iana.org/assignments/media-types/application/ogg\n\t\treturn {\n\t\t\text: 'ogx',\n\t\t\tmime: 'application/ogg'\n\t\t};\n\t}\n\n\tif (check([0x66, 0x4C, 0x61, 0x43])) {\n\t\treturn {\n\t\t\text: 'flac',\n\t\t\tmime: 'audio/x-flac'\n\t\t};\n\t}\n\n\tif (check([0x4D, 0x41, 0x43, 0x20])) { // 'MAC '\n\t\treturn {\n\t\t\text: 'ape',\n\t\t\tmime: 'audio/ape'\n\t\t};\n\t}\n\n\tif (check([0x77, 0x76, 0x70, 0x6B])) { // 'wvpk'\n\t\treturn {\n\t\t\text: 'wv',\n\t\t\tmime: 'audio/wavpack'\n\t\t};\n\t}\n\n\tif (check([0x23, 0x21, 0x41, 0x4D, 0x52, 0x0A])) {\n\t\treturn {\n\t\t\text: 'amr',\n\t\t\tmime: 'audio/amr'\n\t\t};\n\t}\n\n\tif (check([0x25, 0x50, 0x44, 0x46])) {\n\t\treturn {\n\t\t\text: 'pdf',\n\t\t\tmime: 'application/pdf'\n\t\t};\n\t}\n\n\tif (check([0x4D, 0x5A])) {\n\t\treturn {\n\t\t\text: 'exe',\n\t\t\tmime: 'application/x-msdownload'\n\t\t};\n\t}\n\n\tif (\n\t\t(buf[0] === 0x43 || buf[0] === 0x46) &&\n\t\tcheck([0x57, 0x53], {offset: 1})\n\t) {\n\t\treturn {\n\t\t\text: 'swf',\n\t\t\tmime: 'application/x-shockwave-flash'\n\t\t};\n\t}\n\n\tif (check([0x7B, 0x5C, 0x72, 0x74, 0x66])) {\n\t\treturn {\n\t\t\text: 'rtf',\n\t\t\tmime: 'application/rtf'\n\t\t};\n\t}\n\n\tif (check([0x00, 0x61, 0x73, 0x6D])) {\n\t\treturn {\n\t\t\text: 'wasm',\n\t\t\tmime: 'application/wasm'\n\t\t};\n\t}\n\n\tif (\n\t\tcheck([0x77, 0x4F, 0x46, 0x46]) &&\n\t\t(\n\t\t\tcheck([0x00, 0x01, 0x00, 0x00], {offset: 4}) ||\n\t\t\tcheck([0x4F, 0x54, 0x54, 0x4F], {offset: 4})\n\t\t)\n\t) {\n\t\treturn {\n\t\t\text: 'woff',\n\t\t\tmime: 'font/woff'\n\t\t};\n\t}\n\n\tif (\n\t\tcheck([0x77, 0x4F, 0x46, 0x32]) &&\n\t\t(\n\t\t\tcheck([0x00, 0x01, 0x00, 0x00], {offset: 4}) ||\n\t\t\tcheck([0x4F, 0x54, 0x54, 0x4F], {offset: 4})\n\t\t)\n\t) {\n\t\treturn {\n\t\t\text: 'woff2',\n\t\t\tmime: 'font/woff2'\n\t\t};\n\t}\n\n\tif (\n\t\tcheck([0x4C, 0x50], {offset: 34}) &&\n\t\t(\n\t\t\tcheck([0x00, 0x00, 0x01], {offset: 8}) ||\n\t\t\tcheck([0x01, 0x00, 0x02], {offset: 8}) ||\n\t\t\tcheck([0x02, 0x00, 0x02], {offset: 8})\n\t\t)\n\t) {\n\t\treturn {\n\t\t\text: 'eot',\n\t\t\tmime: 'application/vnd.ms-fontobject'\n\t\t};\n\t}\n\n\tif (check([0x00, 0x01, 0x00, 0x00, 0x00])) {\n\t\treturn {\n\t\t\text: 'ttf',\n\t\t\tmime: 'font/ttf'\n\t\t};\n\t}\n\n\tif (check([0x4F, 0x54, 0x54, 0x4F, 0x00])) {\n\t\treturn {\n\t\t\text: 'otf',\n\t\t\tmime: 'font/otf'\n\t\t};\n\t}\n\n\tif (check([0x00, 0x00, 0x01, 0x00])) {\n\t\treturn {\n\t\t\text: 'ico',\n\t\t\tmime: 'image/x-icon'\n\t\t};\n\t}\n\n\tif (check([0x00, 0x00, 0x02, 0x00])) {\n\t\treturn {\n\t\t\text: 'cur',\n\t\t\tmime: 'image/x-icon'\n\t\t};\n\t}\n\n\tif (check([0x46, 0x4C, 0x56, 0x01])) {\n\t\treturn {\n\t\t\text: 'flv',\n\t\t\tmime: 'video/x-flv'\n\t\t};\n\t}\n\n\tif (check([0x25, 0x21])) {\n\t\treturn {\n\t\t\text: 'ps',\n\t\t\tmime: 'application/postscript'\n\t\t};\n\t}\n\n\tif (check([0xFD, 0x37, 0x7A, 0x58, 0x5A, 0x00])) {\n\t\treturn {\n\t\t\text: 'xz',\n\t\t\tmime: 'application/x-xz'\n\t\t};\n\t}\n\n\tif (check([0x53, 0x51, 0x4C, 0x69])) {\n\t\treturn {\n\t\t\text: 'sqlite',\n\t\t\tmime: 'application/x-sqlite3'\n\t\t};\n\t}\n\n\tif (check([0x4E, 0x45, 0x53, 0x1A])) {\n\t\treturn {\n\t\t\text: 'nes',\n\t\t\tmime: 'application/x-nintendo-nes-rom'\n\t\t};\n\t}\n\n\tif (check([0x43, 0x72, 0x32, 0x34])) {\n\t\treturn {\n\t\t\text: 'crx',\n\t\t\tmime: 'application/x-google-chrome-extension'\n\t\t};\n\t}\n\n\tif (\n\t\tcheck([0x4D, 0x53, 0x43, 0x46]) ||\n\t\tcheck([0x49, 0x53, 0x63, 0x28])\n\t) {\n\t\treturn {\n\t\t\text: 'cab',\n\t\t\tmime: 'application/vnd.ms-cab-compressed'\n\t\t};\n\t}\n\n\t// Needs to be before `ar` check\n\tif (check([0x21, 0x3C, 0x61, 0x72, 0x63, 0x68, 0x3E, 0x0A, 0x64, 0x65, 0x62, 0x69, 0x61, 0x6E, 0x2D, 0x62, 0x69, 0x6E, 0x61, 0x72, 0x79])) {\n\t\treturn {\n\t\t\text: 'deb',\n\t\t\tmime: 'application/x-deb'\n\t\t};\n\t}\n\n\tif (check([0x21, 0x3C, 0x61, 0x72, 0x63, 0x68, 0x3E])) {\n\t\treturn {\n\t\t\text: 'ar',\n\t\t\tmime: 'application/x-unix-archive'\n\t\t};\n\t}\n\n\tif (check([0xED, 0xAB, 0xEE, 0xDB])) {\n\t\treturn {\n\t\t\text: 'rpm',\n\t\t\tmime: 'application/x-rpm'\n\t\t};\n\t}\n\n\tif (\n\t\tcheck([0x1F, 0xA0]) ||\n\t\tcheck([0x1F, 0x9D])\n\t) {\n\t\treturn {\n\t\t\text: 'Z',\n\t\t\tmime: 'application/x-compress'\n\t\t};\n\t}\n\n\tif (check([0x4C, 0x5A, 0x49, 0x50])) {\n\t\treturn {\n\t\t\text: 'lz',\n\t\t\tmime: 'application/x-lzip'\n\t\t};\n\t}\n\n\tif (check([0xD0, 0xCF, 0x11, 0xE0, 0xA1, 0xB1, 0x1A, 0xE1])) {\n\t\treturn {\n\t\t\text: 'msi',\n\t\t\tmime: 'application/x-msi'\n\t\t};\n\t}\n\n\tif (check([0x06, 0x0E, 0x2B, 0x34, 0x02, 0x05, 0x01, 0x01, 0x0D, 0x01, 0x02, 0x01, 0x01, 0x02])) {\n\t\treturn {\n\t\t\text: 'mxf',\n\t\t\tmime: 'application/mxf'\n\t\t};\n\t}\n\n\tif (check([0x47], {offset: 4}) && (check([0x47], {offset: 192}) || check([0x47], {offset: 196}))) {\n\t\treturn {\n\t\t\text: 'mts',\n\t\t\tmime: 'video/mp2t'\n\t\t};\n\t}\n\n\tif (check([0x42, 0x4C, 0x45, 0x4E, 0x44, 0x45, 0x52])) {\n\t\treturn {\n\t\t\text: 'blend',\n\t\t\tmime: 'application/x-blender'\n\t\t};\n\t}\n\n\tif (check([0x42, 0x50, 0x47, 0xFB])) {\n\t\treturn {\n\t\t\text: 'bpg',\n\t\t\tmime: 'image/bpg'\n\t\t};\n\t}\n\n\tif (check([0x00, 0x00, 0x00, 0x0C, 0x6A, 0x50, 0x20, 0x20, 0x0D, 0x0A, 0x87, 0x0A])) {\n\t\t// JPEG-2000 family\n\n\t\tif (check([0x6A, 0x70, 0x32, 0x20], {offset: 20})) {\n\t\t\treturn {\n\t\t\t\text: 'jp2',\n\t\t\t\tmime: 'image/jp2'\n\t\t\t};\n\t\t}\n\n\t\tif (check([0x6A, 0x70, 0x78, 0x20], {offset: 20})) {\n\t\t\treturn {\n\t\t\t\text: 'jpx',\n\t\t\t\tmime: 'image/jpx'\n\t\t\t};\n\t\t}\n\n\t\tif (check([0x6A, 0x70, 0x6D, 0x20], {offset: 20})) {\n\t\t\treturn {\n\t\t\t\text: 'jpm',\n\t\t\t\tmime: 'image/jpm'\n\t\t\t};\n\t\t}\n\n\t\tif (check([0x6D, 0x6A, 0x70, 0x32], {offset: 20})) {\n\t\t\treturn {\n\t\t\t\text: 'mj2',\n\t\t\t\tmime: 'image/mj2'\n\t\t\t};\n\t\t}\n\t}\n\n\tif (check([0x46, 0x4F, 0x52, 0x4D, 0x00])) {\n\t\treturn {\n\t\t\text: 'aif',\n\t\t\tmime: 'audio/aiff'\n\t\t};\n\t}\n\n\tif (checkString('<?xml ')) {\n\t\treturn {\n\t\t\text: 'xml',\n\t\t\tmime: 'application/xml'\n\t\t};\n\t}\n\n\tif (check([0x42, 0x4F, 0x4F, 0x4B, 0x4D, 0x4F, 0x42, 0x49], {offset: 60})) {\n\t\treturn {\n\t\t\text: 'mobi',\n\t\t\tmime: 'application/x-mobipocket-ebook'\n\t\t};\n\t}\n\n\t// File Type Box (https://en.wikipedia.org/wiki/ISO_base_media_file_format)\n\tif (check([0x66, 0x74, 0x79, 0x70], {offset: 4})) {\n\t\tif (check([0x6D, 0x69, 0x66, 0x31], {offset: 8})) {\n\t\t\treturn {\n\t\t\t\text: 'heic',\n\t\t\t\tmime: 'image/heif'\n\t\t\t};\n\t\t}\n\n\t\tif (check([0x6D, 0x73, 0x66, 0x31], {offset: 8})) {\n\t\t\treturn {\n\t\t\t\text: 'heic',\n\t\t\t\tmime: 'image/heif-sequence'\n\t\t\t};\n\t\t}\n\n\t\tif (check([0x68, 0x65, 0x69, 0x63], {offset: 8}) || check([0x68, 0x65, 0x69, 0x78], {offset: 8})) {\n\t\t\treturn {\n\t\t\t\text: 'heic',\n\t\t\t\tmime: 'image/heic'\n\t\t\t};\n\t\t}\n\n\t\tif (check([0x68, 0x65, 0x76, 0x63], {offset: 8}) || check([0x68, 0x65, 0x76, 0x78], {offset: 8})) {\n\t\t\treturn {\n\t\t\t\text: 'heic',\n\t\t\t\tmime: 'image/heic-sequence'\n\t\t\t};\n\t\t}\n\t}\n\n\tif (check([0xAB, 0x4B, 0x54, 0x58, 0x20, 0x31, 0x31, 0xBB, 0x0D, 0x0A, 0x1A, 0x0A])) {\n\t\treturn {\n\t\t\text: 'ktx',\n\t\t\tmime: 'image/ktx'\n\t\t};\n\t}\n\n\treturn null;\n};\n"]}