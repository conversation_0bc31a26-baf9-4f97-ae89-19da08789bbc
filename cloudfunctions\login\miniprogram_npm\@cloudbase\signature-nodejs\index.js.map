{"version": 3, "sources": ["index.js", "keyvalue.js", "utils.lang.js", "signer.js", "utils.js", "utils.http.js"], "names": [], "mappings": ";;;;;;;AAAA;AACA;AACA;AACA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA,ACHA;AFOA,ACHA,ACHA;AFOA,ACHA,ACHA;AFOA,ACHA,AENA,ADGA;AFOA,ACHA,AENA,ADGA;AFOA,ACHA,AENA,ADGA;AFOA,ACHA,AENA,ACHA,AFMA;AFOA,ACHA,AENA,ACHA,AFMA;AFOA,ACHA,AENA,ACHA,AFMA;AFOA,ACHA,AENA,AENA,ADGA,AFMA;AFOA,ACHA,AENA,AENA,ADGA,AFMA;AFOA,ACHA,AENA,AENA,ADGA,AFMA;AFOA,ACHA,AENA,AENA,ADGA,AFMA;AFOA,ACHA,AENA,AENA,ADGA,AFMA;AFOA,ACHA,AENA,AENA,ADGA,AFMA;AFOA,ACHA,AENA,AENA,ADGA,AFMA;AFOA,ACHA,AENA,AENA,ADGA,AFMA;AFOA,ACHA,AENA,AENA,ADGA,AFMA;AFOA,ACHA,AENA,AENA,ADGA,AFMA;AFOA,ACHA,AENA,AENA,ADGA;AJaA,ACHA,AENA,AENA,ADGA;AJaA,ACHA,AENA,AENA,ADGA;AJaA,ACHA,AENA,AENA,ADGA;AJaA,ACHA,AENA,AENA,ADGA;AHUA,AENA,AENA,ADGA;AHUA,AENA,AENA,ADGA;AHUA,AENA,AENA,ADGA;AHUA,AENA,AENA,ADGA;AHUA,AENA,AENA,ADGA;AHUA,AENA,AENA,ADGA;AHUA,AENA,AENA,ADGA;AHUA,AENA,AENA,ADGA;AHUA,AENA,AENA,ADGA;AHUA,AENA,AENA,ADGA;AHUA,AENA,AENA,ADGA;AHUA,AENA,AENA,ADGA;AHUA,AENA,AENA,ADGA;AHUA,AENA,AENA,ADGA;AHUA,AENA,AENA,ADGA;AHUA,AENA,AENA,ADGA;AHUA,AENA,AENA,ADGA;AHUA,AENA,AENA;AJaA,AENA,AENA;AFOA,AENA;AFOA,AENA;AFOA,AENA;AFOA,AENA;AFOA,AENA;AFOA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.js", "sourcesContent": ["\nfunction __export(m) {\n    for (var p in m) if (!exports.hasOwnProperty(p)) exports[p] = m[p];\n}\nObject.defineProperty(exports, \"__esModule\", { value: true });\n__export(require(\"./keyvalue\"));\n__export(require(\"./signer\"));\n__export(require(\"./utils.http\"));\n__export(require(\"./utils.lang\"));\n__export(require(\"./utils\"));\nconst signer_1 = require(\"./signer\");\nconst utils_1 = require(\"./utils\");\nconst clone = require('clone');\nfunction sign(options) {\n    const { secretId, secretKey, method, url } = options;\n    const signer = new signer_1.Signer({ secretId, secretKey }, 'tcb');\n    const headers = clone(options.headers || {});\n    const params = clone(options.params || {});\n    const timestamp = options.timestamp || utils_1.second() - 1;\n    const signatureInfo = signer.tc3sign(method, url, headers, params, timestamp, {\n        withSignedParams: options.withSignedParams\n    });\n    return {\n        authorization: signatureInfo.authorization,\n        timestamp: signatureInfo.timestamp,\n        multipart: signatureInfo.multipart\n    };\n}\nexports.sign = sign;\n", "\nObject.defineProperty(exports, \"__esModule\", { value: true });\nconst utils_lang_1 = require(\"./utils.lang\");\nclass SortedKeyValue {\n    constructor(obj, selectkeys) {\n        this._keys = [];\n        this._values = [];\n        this._pairs = [];\n        this._obj = {};\n        if (!utils_lang_1.isObject(obj)) {\n            return this;\n        }\n        // https://stackoverflow.com/questions/5525795/does-javascript-guarantee-object-property-order\n        // https://www.stefanjudis.com/today-i-learned/property-order-is-predictable-in-javascript-objects-since-es2015/\n        Object.keys(obj || {}).sort((l, r) => {\n            return l.toString().localeCompare(r);\n        }).forEach(key => {\n            if (!selectkeys || selectkeys.includes(key)) {\n                this._keys.push(key);\n                this._values.push(obj[key]);\n                this._pairs.push([key, obj[key]]);\n                this._obj[key.toLowerCase()] = obj[key];\n            }\n        });\n    }\n    static kv(obj, selectkeys) {\n        return new SortedKeyValue(obj, selectkeys);\n    }\n    get(key) {\n        return this._obj[key];\n    }\n    keys() {\n        return this._keys;\n    }\n    values() {\n        return this._values;\n    }\n    pairs() {\n        return this._pairs;\n    }\n    toString(kvSeparator = '=', joinSeparator = '&') {\n        return this._pairs.map((pair) => pair.join(kvSeparator)).join(joinSeparator);\n    }\n}\nexports.SortedKeyValue = SortedKeyValue;\n", "\nObject.defineProperty(exports, \"__esModule\", { value: true });\nfunction isNumber(v) {\n    return v === +v;\n}\nexports.isNumber = isNumber;\nfunction isString(v) {\n    return typeof v === 'string';\n}\nexports.isString = isString;\nfunction isObject(v) {\n    return v != null && typeof v === 'object' && Array.isArray(v) === false;\n}\nexports.isObject = isObject;\nfunction isPlainObject(v) {\n    return isObject(v) && [null, Object.prototype].includes(Object.getPrototypeOf(v));\n}\nexports.isPlainObject = isPlainObject;\n", "\nObject.defineProperty(exports, \"__esModule\", { value: true });\nconst crypto = require(\"crypto\");\nconst utils_1 = require(\"./utils\");\nconst utils_lang_1 = require(\"./utils.lang\");\nconst keyvalue_1 = require(\"./keyvalue\");\nconst url_1 = require(\"url\");\nconst debug = require('util').debuglog('@cloudbase/signature');\nconst isStream = require('is-stream');\nexports.signedParamsSeparator = ';';\nconst HOST_KEY = 'host';\nconst CONTENT_TYPE_KEY = 'content-type';\nvar MIME;\n(function (MIME) {\n    MIME[\"MULTIPART_FORM_DATA\"] = \"multipart/form-data\";\n    MIME[\"APPLICATION_JSON\"] = \"application/json\";\n})(MIME || (MIME = {}));\nclass Signer {\n    constructor(credential, service, options = {}) {\n        this.credential = credential;\n        this.service = service;\n        this.algorithm = 'TC3-HMAC-SHA256';\n        this.options = options;\n    }\n    static camSafeUrlEncode(str) {\n        return encodeURIComponent(str)\n            .replace(/!/g, '%21')\n            .replace(/'/g, '%27')\n            .replace(/\\(/g, '%28')\n            .replace(/\\)/g, '%29')\n            .replace(/\\*/g, '%2A');\n    }\n    /**\n     * 将一个对象处理成 KeyValue 形式，嵌套的对象将会被处理成字符串，Key转换成小写字母\n     * @param {Object}  obj - 待处理的对象\n     * @param {Object}  options\n     * @param {Boolean} options.enableBuffer\n     */\n    static formatKeyAndValue(obj, options = {}) {\n        if (!utils_lang_1.isPlainObject(obj)) {\n            return obj;\n        }\n        // enableValueToLowerCase：头部字段，要求小写，其他数据不需要小写，所以这里避免转小写\n        const { multipart, enableValueToLowerCase = false, selectedKeys, filter } = options;\n        const kv = {};\n        Object.keys(obj || {}).forEach(key => {\n            // NOTE: 客户端类型在服务端可能会丢失\n            const lowercaseKey = Signer.camSafeUrlEncode(key.toLowerCase().trim());\n            // 过滤 Key，服务端接收到的数据，可能含有未签名的 Key，通常是签名的时候被过滤掉的流，数据量可能会比较大\n            // 所以这里提供一个过滤的判断，避免不必要的计算\n            // istanbul ignore next\n            if (Array.isArray(selectedKeys) && !selectedKeys.includes(lowercaseKey)) {\n                return;\n            }\n            // istanbul ignore next\n            if (typeof filter === 'function') {\n                if (filter(key, obj[key], options)) {\n                    return;\n                }\n            }\n            // istanbul ignore else\n            if (key && obj[key] !== undefined) {\n                if (lowercaseKey === CONTENT_TYPE_KEY) {\n                    // multipart/form-data; boundary=???\n                    if (obj[key].startsWith(MIME.MULTIPART_FORM_DATA)) {\n                        kv[lowercaseKey] = MIME.MULTIPART_FORM_DATA;\n                    }\n                    else {\n                        kv[lowercaseKey] = obj[key];\n                    }\n                    return;\n                }\n                if (isStream(obj[key])) {\n                    // 这里如果是个文件流，在发送的时候可以识别\n                    // 服务端接收到数据之后传到这里判断不出来的\n                    // 所以会进入后边的逻辑\n                    return;\n                }\n                else if (utils_1.isNodeEnv() && Buffer.isBuffer(obj[key])) {\n                    if (multipart) {\n                        kv[lowercaseKey] = obj[key];\n                    }\n                    else {\n                        kv[lowercaseKey] = enableValueToLowerCase\n                            ? utils_1.stringify(obj[key]).trim().toLowerCase()\n                            : utils_1.stringify(obj[key]).trim();\n                    }\n                }\n                else {\n                    kv[lowercaseKey] = enableValueToLowerCase\n                        ? utils_1.stringify(obj[key]).trim().toLowerCase()\n                        : utils_1.stringify(obj[key]).trim();\n                }\n            }\n        });\n        return kv;\n    }\n    static calcParamsHash(params, keys = null, options = {}) {\n        debug(params, 'calcParamsHash');\n        if (utils_lang_1.isString(params)) {\n            return utils_1.sha256hash(params);\n        }\n        // 只关心业务参数，不关心以什么类型的 Content-Type 传递的\n        // 所以 application/json multipart/form-data 计算方式是相同的\n        keys = keys || keyvalue_1.SortedKeyValue.kv(params).keys();\n        const hash = crypto.createHash('sha256');\n        for (const key of keys) {\n            // istanbul ignore next\n            if (!params[key]) {\n                continue;\n            }\n            // istanbul ignore next\n            if (isStream(params[key])) {\n                continue;\n            }\n            // string && buffer\n            hash.update(`&${key}=`);\n            hash.update(params[key]);\n            hash.update('\\r\\n');\n        }\n        return hash.digest(options.encoding || 'hex');\n    }\n    /**\n     * 计算签名信息\n     * @param {string} method       - Http Verb：GET/get POST/post 区分大小写\n     * @param {string} url          - 地址：http://abc.org/api/v1?a=1&b=2\n     * @param {Object} headers      - 需要签名的头部字段\n     * @param {string} params       - 请求参数\n     * @param {number} [timestamp]  - 签名时间戳\n     * @param {object} [options]    - 可选参数\n     */\n    tc3sign(method, url, headers, params, timestamp, options = {}) {\n        timestamp = timestamp || utils_1.second();\n        const urlInfo = url_1.parse(url);\n        const formatedHeaders = Signer.formatKeyAndValue(headers, {\n            enableValueToLowerCase: true\n        });\n        const headerKV = keyvalue_1.SortedKeyValue.kv(formatedHeaders);\n        const signedHeaders = headerKV.keys();\n        const canonicalHeaders = headerKV.toString(':', '\\n') + '\\n';\n        const { enableHostCheck = true, enableContentTypeCheck = true } = options;\n        if (enableHostCheck && headerKV.get(HOST_KEY) !== urlInfo.host) {\n            throw new TypeError(`host:${urlInfo.host} in url must be equals to host:${headerKV.get('host')} in headers`);\n        }\n        if (enableContentTypeCheck && !headerKV.get(CONTENT_TYPE_KEY)) {\n            throw new TypeError(`${CONTENT_TYPE_KEY} field must in headers`);\n        }\n        const multipart = headerKV.get(CONTENT_TYPE_KEY).startsWith(MIME.MULTIPART_FORM_DATA);\n        const formatedParams = method.toUpperCase() === 'GET' ? '' : Signer.formatKeyAndValue(params, {\n            multipart\n        });\n        const paramKV = keyvalue_1.SortedKeyValue.kv(formatedParams);\n        const signedParams = paramKV.keys();\n        const hashedPayload = Signer.calcParamsHash(formatedParams, null);\n        const signedUrl = url.replace(/^https?:/, '').split('?')[0];\n        const canonicalRequest = `${method}\\n${signedUrl}\\n${urlInfo.query || ''}\\n${canonicalHeaders}\\n${signedHeaders.join(';')}\\n${hashedPayload}`;\n        debug(canonicalRequest, 'canonicalRequest\\n\\n');\n        const date = utils_1.formateDate(timestamp);\n        const service = this.service;\n        const algorithm = this.algorithm;\n        const credentialScope = `${date}/${service}/tc3_request`;\n        const stringToSign = `${algorithm}\\n${timestamp}\\n${credentialScope}\\n${utils_1.sha256hash(canonicalRequest)}`;\n        debug(stringToSign, 'stringToSign\\n\\n');\n        const secretDate = utils_1.sha256hmac(date, `TC3${this.credential.secretKey}`);\n        const secretService = utils_1.sha256hmac(service, secretDate);\n        const secretSigning = utils_1.sha256hmac('tc3_request', secretService);\n        const signature = utils_1.sha256hmac(stringToSign, secretSigning, 'hex');\n        debug(secretDate.toString('hex'), 'secretDate');\n        debug(secretService.toString('hex'), 'secretService');\n        debug(secretSigning.toString('hex'), 'secretSigning');\n        debug(signature, 'signature');\n        const { withSignedParams = false } = options;\n        return {\n            // 需注意该字段长度\n            // https://stackoverflow.com/questions/686217/maximum-on-http-header-values\n            // https://www.tutorialspoint.com/What-is-the-maximum-size-of-HTTP-header-values\n            authorization: `${algorithm} Credential=${this.credential.secretId}/${credentialScope},${withSignedParams ? ` SignedParams=${signedParams.join(';')},` : ''} SignedHeaders=${signedHeaders.join(';')}, Signature=${signature}`,\n            signedParams,\n            signedHeaders,\n            signature,\n            timestamp,\n            multipart\n        };\n    }\n}\nexports.Signer = Signer;\n", "\nObject.defineProperty(exports, \"__esModule\", { value: true });\nconst crypto = require(\"crypto\");\nfunction formateDate(timestamp) {\n    return new Date(timestamp * 1000).toISOString().split('T')[0];\n}\nexports.formateDate = formateDate;\nfunction second() {\n    // istanbul ignore next\n    return Math.floor(new Date().getTime() / 1000);\n}\nexports.second = second;\nfunction stringify(v) {\n    return typeof v !== 'string' ? JSON.stringify(v) : v;\n}\nexports.stringify = stringify;\nfunction sha256hash(string, encoding = 'hex') {\n    return crypto\n        .createHash('sha256')\n        .update(string)\n        .digest(encoding);\n}\nexports.sha256hash = sha256hash;\nfunction sha256hmac(string, secret = '', encoding) {\n    return crypto\n        .createHmac('sha256', secret)\n        .update(string)\n        .digest(encoding);\n}\nexports.sha256hmac = sha256hmac;\nfunction isNodeEnv() {\n    return process && process.release && process.release.name === 'node';\n}\nexports.isNodeEnv = isNodeEnv;\n", "\nObject.defineProperty(exports, \"__esModule\", { value: true });\nconst utils_1 = require(\"./utils\");\nconst utils_lang_1 = require(\"./utils.lang\");\nconst isStream = require('is-stream');\n/**\n * 是否能够使用 FormData 发送数据\n * @param {any} data - 待发送的数据\n */\nfunction canUseFormdata(data) {\n    let enable = true;\n    for (const key in data) {\n        const value = data[key];\n        if (!isStream(value) && (utils_1.isNodeEnv() && !Buffer.isBuffer(value)) && !utils_lang_1.isString(value) && !utils_lang_1.isNumber(value)) {\n            enable = false;\n            break;\n        }\n    }\n    return enable;\n}\nexports.canUseFormdata = canUseFormdata;\n/**\n * 是否一定要通过 FormData 发送数据\n * 如果有 Buffer 和 Stream 必须用 multipart/form-data，如果同时还含有\n * @param {any} data - 待发送的数据\n */\nfunction mustUseFormdata(data) {\n    let must = false;\n    for (const key in data) {\n        const value = data[key];\n        if ((utils_1.isNodeEnv() && Buffer.isBuffer(value)) || isStream(value)) {\n            must = true;\n            break;\n        }\n    }\n    return must;\n}\nexports.mustUseFormdata = mustUseFormdata;\n"]}