/**app.wxss**/
@import 'weui.wxss';

page{
   height: 100%;
   background-color: #f0f0f0;
}

.flex-wrp-row{
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
}

.flex-wrp-column{
  display: flex;
  flex-direction: column;
}

.flex-item{
  flex: 1;
}


.flex-row{
	display: flex;
  width: 100%;
  box-sizing: border-box
}

.flex-item-10{
  flex: 0 0 10%;
  max-width: 10%;
  box-sizing: border-box;
}
.flex-item-20{
  flex: 0 0 20%;
  max-width: 20%;
  box-sizing: border-box;
}
.flex-item-25{
  flex: 0 0 25%;
  max-width: 25%;
  box-sizing: border-box;
}
.flex-item-33{
  flex: 0 0 33%;
  max-width: 33%;
  box-sizing: border-box;
}
.flex-item-50{
  flex: 0 0 50%;
  max-width: 50%;
  box-sizing: border-box;
}
.flex-item-66{
  flex: 0 0 66%;
  max-width: 66%;
  box-sizing: border-box;
}
.flex-item-75{
  flex: 0 0 75%;
  max-width: 75%;
  box-sizing: border-box;
}
.flex-item-80{
  flex: 0 0 80%;
  max-width: 80%;
  box-sizing: border-box;
}
.flex-item-90{
  flex: 0 0 90%;
  max-width: 90%;
  box-sizing: border-box;
}
.flex-item-100{
  flex: 0 0 100%;
  max-width: 100%;
  box-sizing: border-box;
}

.wx-grid{
  width:100%;
  overflow: hidden;
}
.wx-grid-item{
  float: left;
  width: 33.3%;
  box-sizing: border-box;
}

.page__hd {
    padding: 40px;
}
.page__bd {
    padding-bottom: 40px;
}
.page__bd_spacing {
    padding-left: 15px;
    padding-right: 15px;
}

.page__ft{
    padding-bottom: 10px;
    text-align: center;
}

.page__title {
    text-align: left;
    font-size: 20px;
    font-weight: 400;
}

.page__desc {
    margin-top: 5px;
    color: #888888;
    text-align: left;
    font-size: 14px;
}