<view class="container">
  <view class="detail-card">
    <!-- 分析时间 -->
    <view class="detail-header">
      <text class="detail-title">分析时间</text>
      <text class="detail-time">{{detailData.analysisTime}}</text>
    </view>

    <!-- 分析结果 -->
    <view class="result-section">
      <view class="section-title">分析结果</view>
      <view class="result-values">
        <view class="value-item">
          <text class="value-label">C区值：</text>
          <text class="value-text">{{detailData.cAreaValue}}</text>
        </view>
        <view class="value-item">
          <text class="value-label">T区值：</text>
          <text class="value-text">{{detailData.tAreaValue}}</text>
        </view>
        <view class="value-item">
          <text class="value-label">T/C值：</text>
          <text class="value-text">{{detailData.tcValue}}</text>
        </view>
      </view>
    </view>

    <!-- 分析图片 -->
    <view class="image-section">
      <view class="section-title">分析图片</view>
      <view class="image-row">
        <view class="image-container">
          <canvas type="2d" id="cAreaDetailCanvas" class="detail-canvas"></canvas>
          <text>C 区</text>
        </view>
        <view class="image-container">
          <canvas type="2d" id="tAreaDetailCanvas" class="detail-canvas"></canvas>
          <text>T 区</text>
        </view>
      </view>
    </view>

    <!-- 参数设置 -->
    <view class="params-section" wx:if="{{detailData.parameters}}">
      <view class="section-title">参数设置</view>
      <view class="params-list">
        <view class="param-item">
          <text class="param-label">亮度：</text>
          <text class="param-value">{{detailData.parameters.brightness}}</text>
        </view>
        <view class="param-item">
          <text class="param-label">对比度：</text>
          <text class="param-value">{{detailData.parameters.contrast}}</text>
        </view>
        <view class="param-item">
          <text class="param-label">饱和度：</text>
          <text class="param-value">{{detailData.parameters.saturation}}</text>
        </view>
        <view class="param-item">
          <text class="param-label">锐度：</text>
          <text class="param-value">{{detailData.parameters.sharpness}}</text>
        </view>
        <view class="param-item">
          <text class="param-label">伽马值：</text>
          <text class="param-value">{{detailData.parameters.gamma}}</text>
        </view>
        <view class="param-item">
          <text class="param-label">白平衡：</text>
          <text class="param-value">{{detailData.parameters.whiteBalance}}</text>
        </view>
        <view class="param-item">
          <text class="param-label">背光补偿：</text>
          <text class="param-value">{{detailData.parameters.backlight}}</text>
        </view>
        <view class="param-item">
          <text class="param-label">曝光时间：</text>
          <text class="param-value">{{detailData.parameters.exposure}}</text>
        </view>
      </view>
    </view>
  </view>
</view> 