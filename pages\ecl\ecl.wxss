.container {
  min-height: 100vh;
  background-color: #f7f8fa;
  padding: 20rpx;
}

/* ECL值显示区域样式 */
.ecl-section {
  background: #ffffff;
  padding: 30rpx;
  border-radius: 12rpx;
  margin-top: 20rpx;
  border: 1px solid #eaeaea;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.03);
}

.ecl-label {
  font-size: 34rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 30rpx;
  padding-left: 16rpx;
}

.ecl-values {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

/* 值项样式 */
.value-item {
  display: flex;
  align-items: center;
  padding: 24rpx;
  border-radius: 8rpx;
  border: 1rpx solid #eaeaea;
  transition: all 0.3s ease;
}

/* 柔和的灰白交错背景 */
.value-item:nth-child(odd) {
  background-color: #f9fafc;
}

.value-item:nth-child(even) {
  background-color: #ffffff;
}

.value-label {
  width: 100rpx;
  font-size: 30rpx;
  color: #555555;
  font-weight: 500;
}

.value-text {
  flex: 1;
  font-size: 30rpx;
  color: #333333;
  padding-left: 10rpx;
}

/* 等待分析状态样式 */
.waiting-text {
  color: #8c8c8c;
  font-style: italic;
}