#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
PDF转Word服务器自动安装和启动程序
自动检查并安装所有依赖，然后启动服务器
"""

import os
import sys
import subprocess
import time
import webbrowser
from pathlib import Path

def print_banner():
    """打印启动横幅"""
    print("=" * 60)
    print("🚀 PDF转Word服务器自动安装和启动程序")
    print("=" * 60)
    print("📋 功能：自动安装依赖 + 启动服务器 + 打开浏览器")
    print("🔧 支持：标准转换、精确转换、Marker、Nougat、MinerU")
    print("=" * 60)

def check_python_version():
    """检查Python版本"""
    print("🔍 检查Python版本...")
    if sys.version_info < (3, 7):
        print("❌ 错误：需要Python 3.7或更高版本")
        print(f"   当前版本：{sys.version}")
        input("按回车键退出...")
        sys.exit(1)
    else:
        print(f"✅ Python版本检查通过：{sys.version.split()[0]}")

def run_command(cmd, description, timeout=300):
    """运行命令并显示进度"""
    print(f"🔄 {description}...")
    try:
        # 使用subprocess.run而不是Popen以获得更好的控制
        result = subprocess.run(
            cmd, 
            shell=True, 
            capture_output=True, 
            text=True, 
            timeout=timeout,
            encoding='utf-8',
            errors='ignore'
        )
        
        if result.returncode == 0:
            print(f"✅ {description}成功")
            return True
        else:
            print(f"⚠️ {description}失败")
            if result.stderr:
                print(f"   错误信息：{result.stderr[:200]}...")
            return False
    except subprocess.TimeoutExpired:
        print(f"⏰ {description}超时")
        return False
    except Exception as e:
        print(f"❌ {description}异常：{str(e)}")
        return False

def install_package(package_name, description=""):
    """安装单个包"""
    desc = description or f"安装{package_name}"
    
    # 先检查是否已安装
    check_cmd = f'python -c "import {package_name.split("==")[0].replace("-", "_")}"'
    try:
        result = subprocess.run(check_cmd, shell=True, capture_output=True, timeout=10)
        if result.returncode == 0:
            print(f"✅ {package_name} 已安装")
            return True
    except:
        pass
    
    # 安装包
    install_cmd = f"pip install {package_name} -i https://pypi.tuna.tsinghua.edu.cn/simple/"
    return run_command(install_cmd, desc, timeout=180)

def install_dependencies():
    """安装所有依赖"""
    print("\n📦 开始安装依赖包...")
    
    # 基础依赖（必需）
    basic_deps = [
        ("flask", "安装Flask Web框架"),
        ("pdf2docx", "安装PDF2DOCX转换库"),
        ("python-docx", "安装Python-DOCX文档处理库"),
        ("Pillow", "安装图像处理库"),
        ("PyMuPDF", "安装PDF处理库")
    ]
    
    print("\n🔧 安装基础依赖（必需）...")
    basic_success = 0
    for package, desc in basic_deps:
        if install_package(package, desc):
            basic_success += 1
        time.sleep(1)  # 避免安装过快
    
    print(f"\n📊 基础依赖安装结果：{basic_success}/{len(basic_deps)} 成功")
    
    # 高级依赖（可选）- 使用实际可用的包
    advanced_deps = [
        ("pymupdf4llm", "安装PyMuPDF4LLM增强工具"),
        ("requests", "安装HTTP请求库"),
        ("beautifulsoup4", "安装HTML解析库"),
        ("markdown", "安装Markdown处理库")
    ]

    print("\n🚀 安装高级依赖（可选，失败不影响基础功能）...")
    advanced_success = 0
    for package, desc in advanced_deps:
        if install_package(package, desc):
            advanced_success += 1
        time.sleep(1)

    print(f"\n📊 高级依赖安装结果：{advanced_success}/{len(advanced_deps)} 成功")

    # 提示高级工具的安装方法
    print("\n💡 高级转换工具安装提示：")
    print("   Marker: git clone https://github.com/datalab-to/marker")
    print("   Nougat: pip install nougat-ocr (需要PyTorch)")
    print("   MinerU: pip install magic-pdf (可能需要特殊配置)")
    print("   当前版本已优化pdf2docx参数，可处理大部分多栏文档")
    
    # 总结
    total_success = basic_success + advanced_success
    total_packages = len(basic_deps) + len(advanced_deps)
    
    print(f"\n🎉 依赖安装完成！总计：{total_success}/{total_packages} 成功")
    
    if basic_success >= 3:  # 至少安装了主要的基础依赖
        print("✅ 基础功能可用")
        return True
    else:
        print("❌ 基础依赖安装不足，可能影响功能")
        return False

def start_server():
    """启动PDF转Word服务器"""
    print("\n🌐 启动PDF转Word服务器...")
    
    # 检查服务器文件是否存在
    server_file = Path(__file__).parent / "pdf_to_word_server.py"
    if not server_file.exists():
        print(f"❌ 错误：找不到服务器文件 {server_file}")
        return None
    
    try:
        # 启动服务器进程
        process = subprocess.Popen(
            [sys.executable, str(server_file)],
            cwd=str(server_file.parent),
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True,
            encoding='utf-8',
            errors='ignore'
        )
        
        print("✅ 服务器启动中...")
        print("⏳ 等待服务器就绪...")
        
        # 等待服务器启动
        for i in range(10):
            time.sleep(1)
            if process.poll() is not None:
                # 进程已退出
                stdout, stderr = process.communicate()
                print(f"❌ 服务器启动失败")
                if stderr:
                    print(f"   错误：{stderr[:300]}...")
                return None
            print(f"   等待中... ({i+1}/10)")
        
        print("✅ 服务器启动成功！")
        return process
        
    except Exception as e:
        print(f"❌ 启动服务器失败：{str(e)}")
        return None

def open_browser():
    """打开浏览器"""
    print("\n🌍 打开浏览器...")
    url = "http://localhost:5001"
    
    try:
        webbrowser.open(url)
        print(f"✅ 浏览器已打开：{url}")
        return True
    except Exception as e:
        print(f"⚠️ 自动打开浏览器失败：{str(e)}")
        print(f"   请手动访问：{url}")
        return False

def main():
    """主函数"""
    try:
        print_banner()
        
        # 1. 检查Python版本
        check_python_version()
        
        # 2. 安装依赖
        if not install_dependencies():
            print("\n⚠️ 警告：部分依赖安装失败，但仍尝试启动服务器...")
        
        # 3. 启动服务器
        server_process = start_server()
        if server_process is None:
            print("\n❌ 无法启动服务器，程序退出")
            input("按回车键退出...")
            return
        
        # 4. 打开浏览器
        time.sleep(2)  # 给服务器更多时间启动
        open_browser()
        
        # 5. 保持运行
        print("\n" + "=" * 60)
        print("🎉 PDF转Word服务器运行中！")
        print("🌐 访问地址：http://localhost:5001")
        print("📋 支持格式：PDF → Word/Markdown")
        print("🔧 转换模式：标准、精确、Marker、Nougat、MinerU")
        print("=" * 60)
        print("\n💡 提示：")
        print("   - 关闭此窗口将停止服务器")
        print("   - 按 Ctrl+C 可以安全退出")
        print("   - 如需重启，重新运行此程序即可")
        print("\n⏳ 服务器运行中，请勿关闭此窗口...")
        
        # 等待用户中断
        try:
            server_process.wait()
        except KeyboardInterrupt:
            print("\n\n🛑 收到退出信号...")
            print("🔄 正在关闭服务器...")
            server_process.terminate()
            try:
                server_process.wait(timeout=5)
                print("✅ 服务器已安全关闭")
            except subprocess.TimeoutExpired:
                print("⚠️ 强制关闭服务器...")
                server_process.kill()
            
    except Exception as e:
        print(f"\n❌ 程序异常：{str(e)}")
        input("按回车键退出...")
    
    finally:
        print("\n👋 程序已退出，感谢使用！")

if __name__ == "__main__":
    main()
