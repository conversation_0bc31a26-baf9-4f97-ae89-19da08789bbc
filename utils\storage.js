// 云存储工具类
const storage = {
  // 获取用户数据文件夹路径
  getUserDataPath(userId) {
    return `users/${userId}_data`;
  },

  // 获取各类型文件的存储路径
  getPath(userId, type) {
    const basePath = this.getUserDataPath(userId);
    switch (type) {
      case 'video':
        return `${basePath}/videos`;
      case 'image':
        return `${basePath}/images`;
      case 'analysis':
        return `${basePath}/analysis_results`;
      case 'analysis_image':
        return `${basePath}/analysis_images`;
      case 'parameter':
        return `${basePath}/parameters`;
      default:
        return basePath;
    }
  },

  // 生成文件名
  generateFileName(extension) {
    return `${Date.now()}${Math.random().toString(36).substr(2, 9)}.${extension}`;
  },

  // 上传视频
  async uploadVideo(userId, filePath) {
    try {
      const fileName = this.generateFileName('mp4');
      const cloudPath = `${this.getPath(userId, 'video')}/${fileName}`;
      const result = await wx.cloud.uploadFile({
        cloudPath,
        filePath
      });
      return result.fileID;
    } catch (error) {
      console.error('上传视频失败:', error);
      throw error;
    }
  },

  // 上传图片
  async uploadImage(userId, filePath) {
    try {
      const fileName = this.generateFileName('jpg');
      const cloudPath = `${this.getPath(userId, 'image')}/${fileName}`;
      const result = await wx.cloud.uploadFile({
        cloudPath,
        filePath
      });
      return result.fileID;
    } catch (error) {
      console.error('上传图片失败:', error);
      throw error;
    }
  },

  // 上传分析结果
  async uploadAnalysisResult(userId, resultData) {
    try {
      const fileName = this.generateFileName('json');
      const cloudPath = `${this.getPath(userId, 'analysis')}/${fileName}`;
      
      // 将对象转换为字符串
      const jsonString = JSON.stringify(resultData, null, 2);
      
      // 创建临时文件存储JSON字符串
      const tmpFilePath = `${wx.env.USER_DATA_PATH}/tmp_result_${Date.now()}.json`;
      
      // 写入临时文件
      await new Promise((resolve, reject) => {
        wx.getFileSystemManager().writeFile({
          filePath: tmpFilePath,
          data: jsonString,
          encoding: 'utf8',
          success: resolve,
          fail: reject
        });
      });
      
      // 上传临时文件
      const result = await wx.cloud.uploadFile({
        cloudPath,
        filePath: tmpFilePath
      });
      
      // 删除临时文件
      wx.getFileSystemManager().unlink({
        filePath: tmpFilePath,
        fail: (err) => console.warn('删除临时文件失败:', err)
      });
      
      return result.fileID;
    } catch (error) {
      console.error('上传分析结果失败:', error);
      throw error;
    }
  },

  // 上传分析图片
  async uploadAnalysisImage(userId, imageBuffer, type) {
    try {
      const fileName = `${type}-${this.generateFileName('png')}`;
      const cloudPath = `${this.getPath(userId, 'analysis_image')}/${fileName}`;
      
      // 微信小程序环境中不能直接使用imageBuffer
      // 需要先将数据写入临时文件
      const tmpFilePath = `${wx.env.USER_DATA_PATH}/tmp_image_${Date.now()}.png`;
      
      // 写入临时文件
      await new Promise((resolve, reject) => {
        wx.getFileSystemManager().writeFile({
          filePath: tmpFilePath,
          data: imageBuffer,
          encoding: 'binary',
          success: resolve,
          fail: reject
        });
      });
      
      // 上传临时文件
      const result = await wx.cloud.uploadFile({
        cloudPath,
        filePath: tmpFilePath
      });
      
      // 删除临时文件
      wx.getFileSystemManager().unlink({
        filePath: tmpFilePath,
        fail: (err) => console.warn('删除临时图片文件失败:', err)
      });
      
      return result.fileID;
    } catch (error) {
      console.error('上传分析图片失败:', error);
      throw error;
    }
  },

  // 保存参数设置
  async saveParameters(userId, parameters) {
    try {
      const fileName = `params_${Date.now()}.json`;
      const cloudPath = `${this.getPath(userId, 'parameter')}/${fileName}`;
      
      // 将参数对象转换为字符串
      const jsonString = JSON.stringify(parameters, null, 2);
      
      // 创建临时文件存储JSON字符串
      const tmpFilePath = `${wx.env.USER_DATA_PATH}/tmp_params_${Date.now()}.json`;
      
      // 写入临时文件
      await new Promise((resolve, reject) => {
        wx.getFileSystemManager().writeFile({
          filePath: tmpFilePath,
          data: jsonString,
          encoding: 'utf8',
          success: resolve,
          fail: reject
        });
      });
      
      // 上传临时文件
      const result = await wx.cloud.uploadFile({
        cloudPath,
        filePath: tmpFilePath
      });
      
      // 删除临时文件
      wx.getFileSystemManager().unlink({
        filePath: tmpFilePath,
        fail: (err) => console.warn('删除临时文件失败:', err)
      });
      
      return result.fileID;
    } catch (error) {
      console.error('保存参数失败:', error);
      throw error;
    }
  },

  // 获取文件列表
  async getFileList(userId, type) {
    try {
      const path = this.getPath(userId, type);
      const { fileList } = await wx.cloud.listFiles({
        prefix: path
      });
      return fileList;
    } catch (error) {
      console.error('获取文件列表失败:', error);
      throw error;
    }
  },

  // 获取用户的分析结果列表
  async getAnalysisResults(userId) {
    try {
      const { fileList } = await wx.cloud.listFiles({
        prefix: `${this.getPath(userId, 'analysis')}/`
      })

      if (fileList.length === 0) return []

      const tempUrls = await wx.cloud.getTempFileURL({
        fileList: fileList.map(file => file.fileID)
      })

      const results = await Promise.all(
        tempUrls.fileList.map(async file => {
          const response = await wx.cloud.downloadFile({
            fileID: file.fileID
          })
          return {
            fileID: file.fileID,
            ...JSON.parse(response.fileContent.toString('utf8'))
          }
        })
      )

      return results.sort((a, b) => 
        new Date(b.analysisTime) - new Date(a.analysisTime)
      )
    } catch (error) {
      console.error('获取分析结果失败:', error)
      throw error
    }
  },

  // 获取用户的参数历史
  async getParameterHistory(userId) {
    try {
      const { fileList } = await wx.cloud.listFiles({
        prefix: `${this.getPath(userId, 'parameter')}/`
      })

      if (fileList.length === 0) return []

      const tempUrls = await wx.cloud.getTempFileURL({
        fileList: fileList.map(file => file.fileID)
      })

      const results = await Promise.all(
        tempUrls.fileList.map(async file => {
          const response = await wx.cloud.downloadFile({
            fileID: file.fileID
          })
          return {
            fileID: file.fileID,
            ...JSON.parse(response.fileContent.toString('utf8'))
          }
        })
      )

      return results.sort((a, b) => 
        new Date(b.createTime) - new Date(a.createTime)
      )
    } catch (error) {
      console.error('获取参数历史失败:', error)
      throw error
    }
  },

  // 删除文件
  async deleteFile(fileID) {
    try {
      await wx.cloud.deleteFile({
        fileList: [fileID]
      })
      return true
    } catch (error) {
      console.error('删除文件失败:', error)
      throw error
    }
  },

  // 批量删除文件
  async deleteFiles(fileIDs) {
    try {
      await wx.cloud.deleteFile({
        fileList: fileIDs
      })
      return true
    } catch (error) {
      console.error('批量删除文件失败:', error)
      throw error
    }
  }
}

module.exports = storage 