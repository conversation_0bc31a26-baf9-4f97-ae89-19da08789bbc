/* 🚀 自定义导航栏 - Skyline渲染引擎要求 */
.custom-navbar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  height: 88rpx;
  background: #fff;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
  border-bottom: 1rpx solid #e5e5e5;
  transform: translate3d(0, 0, 0);
  will-change: transform;
}

.navbar-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
}

/* 容器样式 */
.container {
  padding: 20rpx;
  background-color: #f5f6fa;
  min-height: 100vh;
}

.device-content {
  padding-top: 88rpx; /* 为自定义导航栏留出空间 */
}

/* 搜索栏样式 */
.search-section {
  padding: 20rpx;
  background: #fff;
  border-radius: 12rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
  margin-bottom: 20rpx;
}

.search-bar {
  display: flex;
  align-items: center;
}

.search-input-wrapper {
  flex: 1;
  display: flex;
  align-items: center;
  height: 72rpx;
  padding: 0 20rpx;
  background: #f5f6fa;
  border-radius: 8rpx;
  margin-right: 20rpx;
}

.search-input-wrapper icon {
  margin-right: 10rpx;
}

.search-input-wrapper input {
  flex: 1;
  font-size: 28rpx;
}

.search-btn {
  width: 120rpx;
  height: 72rpx;
  line-height: 72rpx;
  text-align: center;
  background: #007aff;
  color: #fff;
  border-radius: 8rpx;
  font-size: 28rpx;
}

/* 设备列表样式 */
.device-list {
  margin-top: 20rpx;
}

.device-item {
  margin-bottom: 20rpx;
  background: #fff;
  border-radius: 12rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
  overflow: hidden;
}

.device-header {
  display: flex;
  align-items: center;
  padding: 20rpx;
  border-bottom: 2rpx solid #f5f6fa;
}

.device-image {
  width: 120rpx;
  height: 120rpx;
  border-radius: 8rpx;
  margin-right: 20rpx;
}

.device-title {
  flex: 1;
}

.device-name {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 10rpx;
}

.device-id {
  font-size: 24rpx;
  color: #999;
}

.device-status {
  padding: 4rpx 16rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
}

.status-online {
  background: #e8f5e9;
  color: #4caf50;
}

.status-offline {
  background: #ffebee;
  color: #f44336;
}

.status-maintenance {
  background: #fff3e0;
  color: #ff9800;
}

.device-content {
  padding: 20rpx;
}

.info-row {
  display: flex;
  align-items: center;
  margin-bottom: 16rpx;
  font-size: 28rpx;
}

.info-row:last-child {
  margin-bottom: 0;
}

.info-label {
  width: 160rpx;
  color: #666;
}

.info-value {
  flex: 1;
  color: #333;
}

.device-footer {
  display: flex;
  padding: 20rpx;
  border-top: 2rpx solid #f5f6fa;
}

.footer-btn {
  flex: 1;
  height: 72rpx;
  line-height: 72rpx;
  text-align: center;
  border-radius: 8rpx;
  font-size: 28rpx;
  margin: 0 10rpx;
}

.btn-detail {
  background: #007aff;
  color: #fff;
}

.btn-edit {
  background: #5856d6;
  color: #fff;
}

/* 空状态样式 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 0;
}

.empty-image {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 30rpx;
}

.empty-text {
  font-size: 28rpx;
  color: #999;
}

/* 加载更多样式 */
.load-more {
  text-align: center;
  padding: 30rpx 0;
  color: #999;
  font-size: 24rpx;
}

.loading {
  display: inline-block;
  width: 32rpx;
  height: 32rpx;
  margin-right: 10rpx;
  vertical-align: middle;
}

/* 筛选栏样式 */
.filter-bar {
  display: flex;
  padding: 20rpx;
  background: #fff;
  border-radius: 12rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.filter-item {
  flex: 1;
  text-align: center;
  font-size: 28rpx;
  color: #666;
  position: relative;
}

.filter-item.active {
  color: #007aff;
  font-weight: bold;
}

.filter-item::after {
  content: '';
  position: absolute;
  right: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 2rpx;
  height: 24rpx;
  background: #e8e8e8;
}

.filter-item:last-child::after {
  display: none;
}



