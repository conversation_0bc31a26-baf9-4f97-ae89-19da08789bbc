{"version": 3, "sources": ["index.js", "fs/index.js", "copy-sync/index.js", "copy-sync/copy-sync.js", "mkdirs/index.js", "util/utimes.js", "util/stat.js", "copy/index.js", "copy/copy.js", "path-exists/index.js", "ensure/index.js", "ensure/link.js", "ensure/symlink.js", "ensure/symlink-paths.js", "json/index.js", "json/jsonfile.js", "json/output-json.js", "output/index.js", "json/output-json-sync.js", "move-sync/index.js", "move-sync/move-sync.js", "remove/index.js", "move/index.js", "move/move.js"], "names": [], "mappings": ";;;;;;;AAAA;AACA;AACA;ACFA,ADGA;AC<PERSON>,ADGA;ACFA,ADGA;AELA,ADGA,ADGA;AELA,ADGA,ADGA;AELA,ADGA,ADGA;AGRA,ADGA,ADGA,ADGA;AGRA,ADGA,ADGA,ADGA;AGRA,ADGA,ADGA,ADGA;AGRA,AFMA,ADGA,AIZA;ADIA,AFMA,ADGA,AIZA;ADIA,AFMA,ADGA,AIZA;ADIA,AFMA,ADGA,AIZA,ACHA;AFOA,AFMA,ADGA,AIZA,ACHA;AFOA,AFMA,ADGA,AIZA,ACHA;AFOA,AFMA,ADGA,AIZA,AENA,ADGA;AFOA,AFMA,ADGA,AIZA,AENA,ADGA;AFOA,AFMA,ADGA,AIZA,AENA,ADGA;AFOA,AIZA,ANkBA,ADGA,AIZA,AENA,ADGA;AFOA,AIZA,ANkBA,ADGA,AIZA,AENA,ADGA;AFOA,AIZA,ANkBA,ADGA,AIZA,AENA,ADGA;AFOA,AKfA,ADGA,ANkBA,ADGA,AIZA,AENA,ADGA;AFOA,AKfA,ADGA,ANkBA,ADGA,AIZA,AENA,ADGA;AFOA,AKfA,ADGA,ANkBA,ADGA,AIZA,AENA,ADGA;AFOA,AKfA,ADGA,ANkBA,ADGA,AS3BA,AHSA,ADGA;AFOA,AKfA,APqBA,AQxBA,AHSA,ADGA;AFOA,AKfA,APqBA,AQxBA,AHSA,ADGA;AFOA,AKfA,AENA,AT2BA,AQxBA,AHSA,ADGA;AFOA,AKfA,AENA,AT2BA,AQxBA,AHSA,ADGA;AFOA,AKfA,AENA,AT2BA,AQxBA,AHSA,ADGA;AFOA,AKfA,AENA,ACHA,AV8BA,AQxBA,AHSA,ADGA;AFOA,AKfA,AENA,ACHA,AV8BA,AQxBA,AHSA,ADGA;AFOA,AKfA,AENA,ACHA,AV8BA,AQxBA,AHSA,ADGA;AFOA,AKfA,AENA,ACHA,ACHA,AXiCA,AQxBA,AHSA,ADGA;AFOA,AKfA,AENA,ACHA,ACHA,AXiCA,AQxBA,AHSA,ADGA;AFOA,AKfA,AENA,ACHA,ACHA,AXiCA,AQxBA,AHSA,ADGA;AFOA,AKfA,AENA,ACHA,AENA,ADGA,AXiCA,AQxBA,AHSA,ADGA;AFOA,AKfA,AENA,ACHA,AENA,ADGA,AXiCA,AKfA,ADGA;AFOA,AKfA,AENA,ACHA,AENA,ADGA,AXiCA,AKfA,ADGA;AFOA,AKfA,AENA,ACHA,AENA,ADGA,AXiCA,AavCA,ARwBA;AHUA,AKfA,AENA,ACHA,AENA,ADGA,AXiCA,AavCA,ARwBA;AHUA,AKfA,AENA,ACHA,AENA,ADGA,AXiCA,AavCA,ARwBA;AHUA,AKfA,AENA,ACHA,AENA,ADGA,AXiCA,AavCA,ACHA,AT2BA;AHUA,AKfA,AENA,ACHA,AENA,ADGA,AXiCA,AavCA,ACHA,AT2BA;AHUA,AKfA,AENA,ACHA,AENA,ADGA,AXiCA,AavCA,ACHA,AT2BA;AHUA,AKfA,AENA,ACHA,AENA,ADGA,AXiCA,AavCA,ACHA,ACHA,AV8BA;AHUA,AKfA,AENA,ACHA,AENA,ADGA,AXiCA,AavCA,ACHA,ACHA,AV8BA;AHUA,AKfA,AENA,ACHA,AENA,ADGA,AXiCA,AavCA,ACHA,ACHA,AV8BA;AHUA,AKfA,AENA,ACHA,AENA,ADGA,AXiCA,AavCA,ACHA,ACHA,ACHA,AXiCA;AHUA,AKfA,AENA,ACHA,AENA,ADGA,AXiCA,AavCA,ACHA,ACHA,ACHA,AXiCA;AHUA,AKfA,AENA,ACHA,AENA,ADGA,AXiCA,AavCA,ACHA,ACHA,ACHA,AXiCA;AHUA,AKfA,AGTA,AENA,ADGA,AXiCA,AavCA,ACHA,AGTA,AFMA,ACHA,AXiCA;AHUA,AKfA,AGTA,AENA,ADGA,AXiCA,AavCA,ACHA,AGTA,AFMA,ACHA,AXiCA;AHUA,AKfA,AGTA,AENA,ADGA,AXiCA,AavCA,ACHA,AGTA,AFMA,ACHA,AXiCA;AHUA,AKfA,AGTA,AENA,ADGA,AXiCA,AavCA,AIZA,AFMA,AGTA,AFMA,AXiCA;AHUA,AKfA,AGTA,AENA,ADGA,AXiCA,AavCA,AIZA,AFMA,AGTA,AFMA,AXiCA;AHUA,AKfA,AGTA,AENA,ADGA,AXiCA,AiBnDA,AFMA,AGTA,AFMA,AXiCA;AHUA,AKfA,AGTA,AENA,ADGA,AXiCA,AiBnDA,AFMA,AGTA,ACHA,AHSA,AXiCA;AHUA,AKfA,AGTA,AENA,ADGA,AXiCA,AiBnDA,ACHA,ACHA,AHSA,AXiCA;AHUA,AKfA,AGTA,AENA,ADGA,AXiCA,AiBnDA,ACHA,ACHA,AHSA,AXiCA;AHUA,AKfA,AGTA,AENA,ADGA,AXiCA,AiBnDA,AENA,AHSA,AIZA,Af6CA;AHUA,AKfA,AGTA,AENA,ADGA,AXiCA,AiBnDA,AENA,AHSA,AIZA,Af6CA;AHUA,AKfA,AGTA,AENA,ADGA,AXiCA,AiBnDA,AENA,AHSA,AIZA,Af6CA;AHUA,AKfA,AGTA,AENA,ADGA,AXiCA,AiBnDA,AENA,AENA,ALeA,AIZA,Af6CA;AHUA,AKfA,AGTA,AENA,ADGA,AXiCA,AmBzDA,AENA,ALeA,AIZA,Af6CA;AHUA,AKfA,AGTA,AENA,ADGA,AXiCA,AmBzDA,AENA,ALeA,AIZA,Af6CA;AHUA,AKfA,AGTA,AENA,ADGA,AXiCA,AmBzDA,AENA,ACHA,ANkBA,AIZA,Af6CA;AHUA,AKfA,AGTA,AENA,ADGA,AXiCA,AmBzDA,AENA,ACHA,ANkBA,AIZA,Af6CA;AHUA,AKfA,AGTA,AENA,ADGA,AXiCA,AmBzDA,AENA,ACHA,ANkBA,AIZA,Af6CA;AHUA,AKfA,AGTA,AENA,ADGA,AXiCA,AmBzDA,AENA,ACHA,ANkBA,AIZA,Af6CA;AHUA,AKfA,AGTA,AENA,ADGA,AXiCA,AmBzDA,AGTA,ANkBA,AXiCA;AHUA,AKfA,AGTA,AENA,ADGA,AXiCA,AmBzDA,AGTA,ANkBA,AXiCA;AHUA,AKfA,AGTA,AENA,ADGA,AXiCA,AmBzDA,AGTA,ANkBA,AXiCA;AHUA,AKfA,AGTA,AENA,ADGA,AXiCA,AmBzDA,AGTA,ANkBA,AXiCA;AHUA,AKfA,AGTA,AENA,ADGA,AXiCA,AmBzDA,AGTA,ANkBA,AXiCA;AHUA,AKfA,AGTA,AENA,ADGA,AXiCA,AmBzDA,AGTA,ANkBA,AXiCA;AHUA,AKfA,AGTA,AENA,ADGA,AXiCA,AmBzDA,AGTA,ANkBA,AXiCA;AHUA,AKfA,AGTA,AENA,ADGA,AXiCA,AmBzDA,AGTA,ANkBA,AXiCA;AHUA,AKfA,AGTA,AENA,ADGA,AXiCA,AmBzDA,AGTA,ANkBA,AXiCA;AHUA,AKfA,AGTA,AENA,ADGA,AXiCA,AmBzDA,AGTA,ANkBA,AXiCA;AHUA,AKfA,AGTA,AENA,ADGA,AXiCA,AmBzDA,AGTA,ANkBA,AXiCA;AHUA,AKfA,AGTA,AENA,ADGA,AXiCA,AmBzDA,AGTA,ANkBA,AXiCA;AHUA,AKfA,AGTA,AENA,ADGA,AXiCA,AmBzDA,AGTA,ANkBA,AXiCA;AHUA,AKfA,AGTA,AENA,ADGA,AXiCA,AmBzDA,AGTA,ANkBA,AXiCA;AHUA,AKfA,AGTA,AENA,ADGA,AXiCA,AmBzDA,AGTA,ANkBA,AXiCA;AHUA,AKfA,AGTA,AENA,ADGA,AXiCA,AmBzDA,AGTA,ANkBA,AXiCA;AHUA,AKfA,AGTA,AENA,ADGA,AXiCA,AmBzDA,AGTA,ANkBA,AXiCA;AHUA,AKfA,AGTA,AENA,ADGA,AXiCA,AmBzDA,AGTA,ANkBA,AXiCA;AHUA,AKfA,AGTA,AENA,ADGA,AXiCA,AmBzDA,AGTA,ANkBA,AXiCA;AHUA,AKfA,AGTA,AENA,ADGA,AXiCA,AmBzDA,AGTA,AjBmDA;AHUA,AKfA,AGTA,AENA,ADGA,AXiCA,AmBzDA,AGTA,AjBmDA;AHUA,AKfA,AGTA,AENA,ADGA,AXiCA,AmBzDA,AGTA,AjBmDA;AHUA,AKfA,AKfA,ADGA,AXiCA,AmBzDA,AGTA,AjBmDA;AHUA,AKfA,AKfA,ADGA,AXiCA,AmBzDA,AGTA,AjBmDA;AHUA,AKfA,AKfA,ADGA,AXiCA,AmBzDA,AGTA,AjBmDA;AHUA,AKfA,AKfA,ADGA,AXiCA,AmBzDA,AGTA,AjBmDA;AHUA,AKfA,AKfA,ADGA,AXiCA,AmBzDA,AGTA,AjBmDA;AHUA,AKfA,AKfA,AZoCA,AmBzDA,AGTA,AjBmDA;AHUA,AKfA,AKfA,AZoCA,AmBzDA,AGTA,AjBmDA;AHUA,AKfA,AKfA,AZoCA,AmBzDA,AGTA,AjBmDA;AHUA,AKfA,AKfA,AZoCA,AmBzDA,AGTA,AjBmDA;AHUA,AKfA,AKfA,AZoCA,AmBzDA,AGTA,AjBmDA;AHUA,AKfA,AKfA,AZoCA,AmBzDA,AGTA,AjBmDA;AHUA,AKfA,AKfA,AZoCA,AmBzDA,AGTA,AjBmDA;AHUA,AKfA,AKfA,AZoCA,AmBzDA,AGTA,AjBmDA;AHUA,AKfA,AKfA,AZoCA,AsBlEA,AjBmDA;AHUA,AKfA,AKfA,AZoCA,AsBlEA,AjBmDA;AHUA,AKfA,AKfA,AZoCA,AsBlEA,AjBmDA;AHUA,AKfA,AKfA,AZoCA,AsBlEA,AjBmDA;AHUA,AKfA,AKfA,AZoCA,AsBlEA,AjBmDA;AHUA,AKfA,AKfA,AZoCA,AsBlEA,AjBmDA;AHUA,AKfA,AKfA,AZoCA,AsBlEA,AjBmDA;AHUA,AKfA,AKfA,AZoCA,AsBlEA,AjBmDA;AHUA,AKfA,AKfA,AZoCA,AsBlEA,AjBmDA;AHUA,AKfA,AKfA,AZoCA,AsBlEA,AjBmDA;AHUA,AKfA,AKfA,AZoCA,AsBlEA,AjBmDA;AHUA,AKfA,AKfA,AZoCA,AsBlEA,AjBmDA;AHUA,AKfA,AKfA,AZoCA,AsBlEA,AjBmDA;AHUA,AKfA,AKfA,AZoCA,AsBlEA,AjBmDA;AHUA,AKfA,AKfA,AZoCA,AsBlEA,AjBmDA;AHUA,AKfA,AKfA,AZoCA,AsBlEA,AjBmDA;AHUA,AKfA,AKfA,AZoCA,AsBlEA,AjBmDA;AHUA,AKfA,AKfA,AZoCA,AsBlEA,AjBmDA;AHUA,AKfA,AKfA,AZoCA,AsBlEA,AjBmDA;AHUA,AKfA,AKfA,AZoCA,AsBlEA,AjBmDA;AHUA,AKfA,AKfA,AZoCA,AsBlEA,AjBmDA;AHUA,AKfA,AKfA,AZoCA,AsBlEA,AjBmDA;AHUA,AKfA,AKfA,AZoCA,AsBlEA,AjBmDA;AHUA,AKfA,AKfA,AZoCA,AsBlEA,AjBmDA;AHUA,AKfA,AKfA,AZoCA,AsBlEA,AjBmDA;AHUA,AKfA,AKfA,AZoCA,AsBlEA,AjBmDA;AHUA,AKfA,AKfA,AU9BA,AjBmDA;AHUA,AKfA,AKfA,APqBA;AHUA,AKfA,AKfA,APqBA;AHUA,AKfA,AKfA,APqBA;AHUA,AKfA,AKfA,APqBA;AHUA,AKfA,AFMA;AHUA,AKfA,AFMA;AHUA,AKfA,AFMA;AHUA,AKfA,AFMA;AHUA,AKfA,AFMA;AHUA,AKfA,AFMA;AHUA,AKfA,AFMA;AHUA,AKfA,AFMA;AHUA,AKfA,AFMA;AHUA,AKfA,AFMA;AHUA,AKfA,AFMA;AHUA,AKfA,AFMA;AHUA,AKfA,AFMA;AHUA,AKfA,AFMA;AHUA,AKfA,AFMA;AHUA,AKfA,AFMA;AHUA,AKfA,AFMA;AHUA,AKfA,AFMA;AHUA,AKfA,AFMA;AHUA,AKfA;ALgBA,AKfA;ALgBA,AKfA;ALgBA,AKfA;ALgBA,AKfA;ALgBA,AKfA;ALgBA,AKfA;ALgBA,AKfA;ALgBA,AKfA;ALgBA,AKfA;ALgBA,AKfA;ALgBA,AKfA;ALgBA,AKfA;ALgBA,AKfA;ALgBA,AKfA;ALgBA,AKfA;ALgBA,AKfA;ALgBA,AKfA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.js", "sourcesContent": ["\n\nmodule.exports = {\n  // Export promiseified graceful-fs:\n  ...require('./fs'),\n  // Export extra methods:\n  ...require('./copy-sync'),\n  ...require('./copy'),\n  ...require('./empty'),\n  ...require('./ensure'),\n  ...require('./json'),\n  ...require('./mkdirs'),\n  ...require('./move-sync'),\n  ...require('./move'),\n  ...require('./output'),\n  ...require('./path-exists'),\n  ...require('./remove')\n}\n\n// Export fs.promises as a getter property so that we don't trigger\n// ExperimentalWarning before fs.promises is actually accessed.\nconst fs = require('fs')\nif (Object.getOwnPropertyDescriptor(fs, 'promises')) {\n  Object.defineProperty(module.exports, 'promises', {\n    get () { return fs.promises }\n  })\n}\n", "\n// This is adapted from https://github.com/normalize/mz\n// Copyright (c) 2014-2016 <NAME_EMAIL> and Contributors\nconst u = require('universalify').fromCallback\nconst fs = require('graceful-fs')\n\nconst api = [\n  'access',\n  'appendFile',\n  'chmod',\n  'chown',\n  'close',\n  'copyFile',\n  'fchmod',\n  'fchown',\n  'fdatasync',\n  'fstat',\n  'fsync',\n  'ftruncate',\n  'futimes',\n  'lchmod',\n  'lchown',\n  'link',\n  'lstat',\n  'mkdir',\n  'mkdtemp',\n  'open',\n  'opendir',\n  'readdir',\n  'readFile',\n  'readlink',\n  'realpath',\n  'rename',\n  'rm',\n  'rmdir',\n  'stat',\n  'symlink',\n  'truncate',\n  'unlink',\n  'utimes',\n  'writeFile'\n].filter(key => {\n  // Some commands are not available on some systems. Ex:\n  // fs.opendir was added in Node.js v12.12.0\n  // fs.rm was added in Node.js v14.14.0\n  // fs.lchown is not available on at least some Linux\n  return typeof fs[key] === 'function'\n})\n\n// Export all keys:\nObject.keys(fs).forEach(key => {\n  if (key === 'promises') {\n    // fs.promises is a getter property that triggers ExperimentalWarning\n    // Don't re-export it here, the getter is defined in \"lib/index.js\"\n    return\n  }\n  exports[key] = fs[key]\n})\n\n// Universalify async methods:\napi.forEach(method => {\n  exports[method] = u(fs[method])\n})\n\n// We differ from mz/fs in that we still ship the old, broken, fs.exists()\n// since we are a drop-in replacement for the native module\nexports.exists = function (filename, callback) {\n  if (typeof callback === 'function') {\n    return fs.exists(filename, callback)\n  }\n  return new Promise(resolve => {\n    return fs.exists(filename, resolve)\n  })\n}\n\n// fs.read(), fs.write(), & fs.writev() need special treatment due to multiple callback args\n\nexports.read = function (fd, buffer, offset, length, position, callback) {\n  if (typeof callback === 'function') {\n    return fs.read(fd, buffer, offset, length, position, callback)\n  }\n  return new Promise((resolve, reject) => {\n    fs.read(fd, buffer, offset, length, position, (err, bytesRead, buffer) => {\n      if (err) return reject(err)\n      resolve({ bytesRead, buffer })\n    })\n  })\n}\n\n// Function signature can be\n// fs.write(fd, buffer[, offset[, length[, position]]], callback)\n// OR\n// fs.write(fd, string[, position[, encoding]], callback)\n// We need to handle both cases, so we use ...args\nexports.write = function (fd, buffer, ...args) {\n  if (typeof args[args.length - 1] === 'function') {\n    return fs.write(fd, buffer, ...args)\n  }\n\n  return new Promise((resolve, reject) => {\n    fs.write(fd, buffer, ...args, (err, bytesWritten, buffer) => {\n      if (err) return reject(err)\n      resolve({ bytesWritten, buffer })\n    })\n  })\n}\n\n// fs.writev only available in Node v12.9.0+\nif (typeof fs.writev === 'function') {\n  // Function signature is\n  // s.writev(fd, buffers[, position], callback)\n  // We need to handle the optional arg, so we use ...args\n  exports.writev = function (fd, buffers, ...args) {\n    if (typeof args[args.length - 1] === 'function') {\n      return fs.writev(fd, buffers, ...args)\n    }\n\n    return new Promise((resolve, reject) => {\n      fs.writev(fd, buffers, ...args, (err, bytesWritten, buffers) => {\n        if (err) return reject(err)\n        resolve({ bytesWritten, buffers })\n      })\n    })\n  }\n}\n\n// fs.realpath.native only available in Node v9.2+\nif (typeof fs.realpath.native === 'function') {\n  exports.realpath.native = u(fs.realpath.native)\n}\n", "\n\nmodule.exports = {\n  copySync: require('./copy-sync')\n}\n", "\n\nconst fs = require('graceful-fs')\nconst path = require('path')\nconst mkdirsSync = require('../mkdirs').mkdirsSync\nconst utimesMillisSync = require('../util/utimes').utimesMillisSync\nconst stat = require('../util/stat')\n\nfunction copySync (src, dest, opts) {\n  if (typeof opts === 'function') {\n    opts = { filter: opts }\n  }\n\n  opts = opts || {}\n  opts.clobber = 'clobber' in opts ? !!opts.clobber : true // default to true for now\n  opts.overwrite = 'overwrite' in opts ? !!opts.overwrite : opts.clobber // overwrite falls back to clobber\n\n  // Warn about using preserveTimestamps on 32-bit node\n  if (opts.preserveTimestamps && process.arch === 'ia32') {\n    console.warn(`fs-extra: Using the preserveTimestamps option in 32-bit node is not recommended;\\n\n    see https://github.com/jprichardson/node-fs-extra/issues/269`)\n  }\n\n  const { srcStat, destStat } = stat.checkPathsSync(src, dest, 'copy')\n  stat.checkParentPathsSync(src, srcStat, dest, 'copy')\n  return handleFilterAndCopy(destStat, src, dest, opts)\n}\n\nfunction handleFilterAndCopy (destStat, src, dest, opts) {\n  if (opts.filter && !opts.filter(src, dest)) return\n  const destParent = path.dirname(dest)\n  if (!fs.existsSync(destParent)) mkdirsSync(destParent)\n  return startCopy(destStat, src, dest, opts)\n}\n\nfunction startCopy (destStat, src, dest, opts) {\n  if (opts.filter && !opts.filter(src, dest)) return\n  return getStats(destStat, src, dest, opts)\n}\n\nfunction getStats (destStat, src, dest, opts) {\n  const statSync = opts.dereference ? fs.statSync : fs.lstatSync\n  const srcStat = statSync(src)\n\n  if (srcStat.isDirectory()) return onDir(srcStat, destStat, src, dest, opts)\n  else if (srcStat.isFile() ||\n           srcStat.isCharacterDevice() ||\n           srcStat.isBlockDevice()) return onFile(srcStat, destStat, src, dest, opts)\n  else if (srcStat.isSymbolicLink()) return onLink(destStat, src, dest, opts)\n}\n\nfunction onFile (srcStat, destStat, src, dest, opts) {\n  if (!destStat) return copyFile(srcStat, src, dest, opts)\n  return mayCopyFile(srcStat, src, dest, opts)\n}\n\nfunction mayCopyFile (srcStat, src, dest, opts) {\n  if (opts.overwrite) {\n    fs.unlinkSync(dest)\n    return copyFile(srcStat, src, dest, opts)\n  } else if (opts.errorOnExist) {\n    throw new Error(`'${dest}' already exists`)\n  }\n}\n\nfunction copyFile (srcStat, src, dest, opts) {\n  fs.copyFileSync(src, dest)\n  if (opts.preserveTimestamps) handleTimestamps(srcStat.mode, src, dest)\n  return setDestMode(dest, srcStat.mode)\n}\n\nfunction handleTimestamps (srcMode, src, dest) {\n  // Make sure the file is writable before setting the timestamp\n  // otherwise open fails with EPERM when invoked with 'r+'\n  // (through utimes call)\n  if (fileIsNotWritable(srcMode)) makeFileWritable(dest, srcMode)\n  return setDestTimestamps(src, dest)\n}\n\nfunction fileIsNotWritable (srcMode) {\n  return (srcMode & 0o200) === 0\n}\n\nfunction makeFileWritable (dest, srcMode) {\n  return setDestMode(dest, srcMode | 0o200)\n}\n\nfunction setDestMode (dest, srcMode) {\n  return fs.chmodSync(dest, srcMode)\n}\n\nfunction setDestTimestamps (src, dest) {\n  // The initial srcStat.atime cannot be trusted\n  // because it is modified by the read(2) system call\n  // (See https://nodejs.org/api/fs.html#fs_stat_time_values)\n  const updatedSrcStat = fs.statSync(src)\n  return utimesMillisSync(dest, updatedSrcStat.atime, updatedSrcStat.mtime)\n}\n\nfunction onDir (srcStat, destStat, src, dest, opts) {\n  if (!destStat) return mkDirAndCopy(srcStat.mode, src, dest, opts)\n  if (destStat && !destStat.isDirectory()) {\n    throw new Error(`Cannot overwrite non-directory '${dest}' with directory '${src}'.`)\n  }\n  return copyDir(src, dest, opts)\n}\n\nfunction mkDirAndCopy (srcMode, src, dest, opts) {\n  fs.mkdirSync(dest)\n  copyDir(src, dest, opts)\n  return setDestMode(dest, srcMode)\n}\n\nfunction copyDir (src, dest, opts) {\n  fs.readdirSync(src).forEach(item => copyDirItem(item, src, dest, opts))\n}\n\nfunction copyDirItem (item, src, dest, opts) {\n  const srcItem = path.join(src, item)\n  const destItem = path.join(dest, item)\n  const { destStat } = stat.checkPathsSync(srcItem, destItem, 'copy')\n  return startCopy(destStat, srcItem, destItem, opts)\n}\n\nfunction onLink (destStat, src, dest, opts) {\n  let resolvedSrc = fs.readlinkSync(src)\n  if (opts.dereference) {\n    resolvedSrc = path.resolve(process.cwd(), resolvedSrc)\n  }\n\n  if (!destStat) {\n    return fs.symlinkSync(resolvedSrc, dest)\n  } else {\n    let resolvedDest\n    try {\n      resolvedDest = fs.readlinkSync(dest)\n    } catch (err) {\n      // dest exists and is a regular file or directory,\n      // Windows may throw UNKNOWN error. If dest already exists,\n      // fs throws error anyway, so no need to guard against it here.\n      if (err.code === 'EINVAL' || err.code === 'UNKNOWN') return fs.symlinkSync(resolvedSrc, dest)\n      throw err\n    }\n    if (opts.dereference) {\n      resolvedDest = path.resolve(process.cwd(), resolvedDest)\n    }\n    if (stat.isSrcSubdir(resolvedSrc, resolvedDest)) {\n      throw new Error(`Cannot copy '${resolvedSrc}' to a subdirectory of itself, '${resolvedDest}'.`)\n    }\n\n    // prevent copy if src is a subdir of dest since unlinking\n    // dest in this case would result in removing src contents\n    // and therefore a broken symlink would be created.\n    if (fs.statSync(dest).isDirectory() && stat.isSrcSubdir(resolvedDest, resolvedSrc)) {\n      throw new Error(`Cannot overwrite '${resolvedDest}' with '${resolvedSrc}'.`)\n    }\n    return copyLink(resolvedSrc, dest)\n  }\n}\n\nfunction copyLink (resolvedSrc, dest) {\n  fs.unlinkSync(dest)\n  return fs.symlinkSync(resolvedSrc, dest)\n}\n\nmodule.exports = copySync\n", "\nconst u = require('universalify').fromPromise\nconst { makeDir: _makeDir, makeDirSync } = require('./make-dir')\nconst makeDir = u(_makeDir)\n\nmodule.exports = {\n  mkdirs: makeDir,\n  mkdirsSync: makeDirSync,\n  // alias\n  mkdirp: makeDir,\n  mkdirpSync: makeDirSync,\n  ensureDir: makeDir,\n  ensureDirSync: makeDirSync\n}\n", "\n\nconst fs = require('graceful-fs')\n\nfunction utimesMillis (path, atime, mtime, callback) {\n  // if (!HAS_MILLIS_RES) return fs.utimes(path, atime, mtime, callback)\n  fs.open(path, 'r+', (err, fd) => {\n    if (err) return callback(err)\n    fs.futimes(fd, atime, mtime, futimesErr => {\n      fs.close(fd, closeErr => {\n        if (callback) callback(futimesErr || closeErr)\n      })\n    })\n  })\n}\n\nfunction utimesMillisSync (path, atime, mtime) {\n  const fd = fs.openSync(path, 'r+')\n  fs.futimesSync(fd, atime, mtime)\n  return fs.closeSync(fd)\n}\n\nmodule.exports = {\n  utimesMillis,\n  utimesMillisSync\n}\n", "\n\nconst fs = require('../fs')\nconst path = require('path')\nconst util = require('util')\nconst atLeastNode = require('at-least-node')\n\nconst nodeSupportsBigInt = atLeastNode('10.5.0')\nconst stat = (file) => nodeSupportsBigInt ? fs.stat(file, { bigint: true }) : fs.stat(file)\nconst statSync = (file) => nodeSupportsBigInt ? fs.statSync(file, { bigint: true }) : fs.statSync(file)\n\nfunction getStats (src, dest) {\n  return Promise.all([\n    stat(src),\n    stat(dest).catch(err => {\n      if (err.code === 'ENOENT') return null\n      throw err\n    })\n  ]).then(([srcStat, destStat]) => ({ srcStat, destStat }))\n}\n\nfunction getStatsSync (src, dest) {\n  let destStat\n  const srcStat = statSync(src)\n  try {\n    destStat = statSync(dest)\n  } catch (err) {\n    if (err.code === 'ENOENT') return { srcStat, destStat: null }\n    throw err\n  }\n  return { srcStat, destStat }\n}\n\nfunction checkPaths (src, dest, funcName, cb) {\n  util.callbackify(getStats)(src, dest, (err, stats) => {\n    if (err) return cb(err)\n    const { srcStat, destStat } = stats\n    if (destStat && areIdentical(srcStat, destStat)) {\n      return cb(new Error('Source and destination must not be the same.'))\n    }\n    if (srcStat.isDirectory() && isSrcSubdir(src, dest)) {\n      return cb(new Error(errMsg(src, dest, funcName)))\n    }\n    return cb(null, { srcStat, destStat })\n  })\n}\n\nfunction checkPathsSync (src, dest, funcName) {\n  const { srcStat, destStat } = getStatsSync(src, dest)\n  if (destStat && areIdentical(srcStat, destStat)) {\n    throw new Error('Source and destination must not be the same.')\n  }\n  if (srcStat.isDirectory() && isSrcSubdir(src, dest)) {\n    throw new Error(errMsg(src, dest, funcName))\n  }\n  return { srcStat, destStat }\n}\n\n// recursively check if dest parent is a subdirectory of src.\n// It works for all file types including symlinks since it\n// checks the src and dest inodes. It starts from the deepest\n// parent and stops once it reaches the src parent or the root path.\nfunction checkParentPaths (src, srcStat, dest, funcName, cb) {\n  const srcParent = path.resolve(path.dirname(src))\n  const destParent = path.resolve(path.dirname(dest))\n  if (destParent === srcParent || destParent === path.parse(destParent).root) return cb()\n  const callback = (err, destStat) => {\n    if (err) {\n      if (err.code === 'ENOENT') return cb()\n      return cb(err)\n    }\n    if (areIdentical(srcStat, destStat)) {\n      return cb(new Error(errMsg(src, dest, funcName)))\n    }\n    return checkParentPaths(src, srcStat, destParent, funcName, cb)\n  }\n  if (nodeSupportsBigInt) fs.stat(destParent, { bigint: true }, callback)\n  else fs.stat(destParent, callback)\n}\n\nfunction checkParentPathsSync (src, srcStat, dest, funcName) {\n  const srcParent = path.resolve(path.dirname(src))\n  const destParent = path.resolve(path.dirname(dest))\n  if (destParent === srcParent || destParent === path.parse(destParent).root) return\n  let destStat\n  try {\n    destStat = statSync(destParent)\n  } catch (err) {\n    if (err.code === 'ENOENT') return\n    throw err\n  }\n  if (areIdentical(srcStat, destStat)) {\n    throw new Error(errMsg(src, dest, funcName))\n  }\n  return checkParentPathsSync(src, srcStat, destParent, funcName)\n}\n\nfunction areIdentical (srcStat, destStat) {\n  if (destStat.ino && destStat.dev && destStat.ino === srcStat.ino && destStat.dev === srcStat.dev) {\n    if (nodeSupportsBigInt || destStat.ino < Number.MAX_SAFE_INTEGER) {\n      // definitive answer\n      return true\n    }\n    // Use additional heuristics if we can't use 'bigint'.\n    // Different 'ino' could be represented the same if they are >= Number.MAX_SAFE_INTEGER\n    // See issue 657\n    if (destStat.size === srcStat.size &&\n        destStat.mode === srcStat.mode &&\n        destStat.nlink === srcStat.nlink &&\n        destStat.atimeMs === srcStat.atimeMs &&\n        destStat.mtimeMs === srcStat.mtimeMs &&\n        destStat.ctimeMs === srcStat.ctimeMs &&\n        destStat.birthtimeMs === srcStat.birthtimeMs) {\n      // heuristic answer\n      return true\n    }\n  }\n  return false\n}\n\n// return true if dest is a subdir of src, otherwise false.\n// It only checks the path strings.\nfunction isSrcSubdir (src, dest) {\n  const srcArr = path.resolve(src).split(path.sep).filter(i => i)\n  const destArr = path.resolve(dest).split(path.sep).filter(i => i)\n  return srcArr.reduce((acc, cur, i) => acc && destArr[i] === cur, true)\n}\n\nfunction errMsg (src, dest, funcName) {\n  return `Cannot ${funcName} '${src}' to a subdirectory of itself, '${dest}'.`\n}\n\nmodule.exports = {\n  checkPaths,\n  checkPathsSync,\n  checkParentPaths,\n  checkParentPathsSync,\n  isSrcSubdir\n}\n", "\n\nconst u = require('universalify').fromCallback\nmodule.exports = {\n  copy: u(require('./copy'))\n}\n", "\n\nconst fs = require('graceful-fs')\nconst path = require('path')\nconst mkdirs = require('../mkdirs').mkdirs\nconst pathExists = require('../path-exists').pathExists\nconst utimesMillis = require('../util/utimes').utimesMillis\nconst stat = require('../util/stat')\n\nfunction copy (src, dest, opts, cb) {\n  if (typeof opts === 'function' && !cb) {\n    cb = opts\n    opts = {}\n  } else if (typeof opts === 'function') {\n    opts = { filter: opts }\n  }\n\n  cb = cb || function () {}\n  opts = opts || {}\n\n  opts.clobber = 'clobber' in opts ? !!opts.clobber : true // default to true for now\n  opts.overwrite = 'overwrite' in opts ? !!opts.overwrite : opts.clobber // overwrite falls back to clobber\n\n  // Warn about using preserveTimestamps on 32-bit node\n  if (opts.preserveTimestamps && process.arch === 'ia32') {\n    console.warn(`fs-extra: Using the preserveTimestamps option in 32-bit node is not recommended;\\n\n    see https://github.com/jprichardson/node-fs-extra/issues/269`)\n  }\n\n  stat.checkPaths(src, dest, 'copy', (err, stats) => {\n    if (err) return cb(err)\n    const { srcStat, destStat } = stats\n    stat.checkParentPaths(src, srcStat, dest, 'copy', err => {\n      if (err) return cb(err)\n      if (opts.filter) return handleFilter(checkParentDir, destStat, src, dest, opts, cb)\n      return checkParentDir(destStat, src, dest, opts, cb)\n    })\n  })\n}\n\nfunction checkParentDir (destStat, src, dest, opts, cb) {\n  const destParent = path.dirname(dest)\n  pathExists(destParent, (err, dirExists) => {\n    if (err) return cb(err)\n    if (dirExists) return startCopy(destStat, src, dest, opts, cb)\n    mkdirs(destParent, err => {\n      if (err) return cb(err)\n      return startCopy(destStat, src, dest, opts, cb)\n    })\n  })\n}\n\nfunction handleFilter (onInclude, destStat, src, dest, opts, cb) {\n  Promise.resolve(opts.filter(src, dest)).then(include => {\n    if (include) return onInclude(destStat, src, dest, opts, cb)\n    return cb()\n  }, error => cb(error))\n}\n\nfunction startCopy (destStat, src, dest, opts, cb) {\n  if (opts.filter) return handleFilter(getStats, destStat, src, dest, opts, cb)\n  return getStats(destStat, src, dest, opts, cb)\n}\n\nfunction getStats (destStat, src, dest, opts, cb) {\n  const stat = opts.dereference ? fs.stat : fs.lstat\n  stat(src, (err, srcStat) => {\n    if (err) return cb(err)\n\n    if (srcStat.isDirectory()) return onDir(srcStat, destStat, src, dest, opts, cb)\n    else if (srcStat.isFile() ||\n             srcStat.isCharacterDevice() ||\n             srcStat.isBlockDevice()) return onFile(srcStat, destStat, src, dest, opts, cb)\n    else if (srcStat.isSymbolicLink()) return onLink(destStat, src, dest, opts, cb)\n  })\n}\n\nfunction onFile (srcStat, destStat, src, dest, opts, cb) {\n  if (!destStat) return copyFile(srcStat, src, dest, opts, cb)\n  return mayCopyFile(srcStat, src, dest, opts, cb)\n}\n\nfunction mayCopyFile (srcStat, src, dest, opts, cb) {\n  if (opts.overwrite) {\n    fs.unlink(dest, err => {\n      if (err) return cb(err)\n      return copyFile(srcStat, src, dest, opts, cb)\n    })\n  } else if (opts.errorOnExist) {\n    return cb(new Error(`'${dest}' already exists`))\n  } else return cb()\n}\n\nfunction copyFile (srcStat, src, dest, opts, cb) {\n  fs.copyFile(src, dest, err => {\n    if (err) return cb(err)\n    if (opts.preserveTimestamps) return handleTimestampsAndMode(srcStat.mode, src, dest, cb)\n    return setDestMode(dest, srcStat.mode, cb)\n  })\n}\n\nfunction handleTimestampsAndMode (srcMode, src, dest, cb) {\n  // Make sure the file is writable before setting the timestamp\n  // otherwise open fails with EPERM when invoked with 'r+'\n  // (through utimes call)\n  if (fileIsNotWritable(srcMode)) {\n    return makeFileWritable(dest, srcMode, err => {\n      if (err) return cb(err)\n      return setDestTimestampsAndMode(srcMode, src, dest, cb)\n    })\n  }\n  return setDestTimestampsAndMode(srcMode, src, dest, cb)\n}\n\nfunction fileIsNotWritable (srcMode) {\n  return (srcMode & 0o200) === 0\n}\n\nfunction makeFileWritable (dest, srcMode, cb) {\n  return setDestMode(dest, srcMode | 0o200, cb)\n}\n\nfunction setDestTimestampsAndMode (srcMode, src, dest, cb) {\n  setDestTimestamps(src, dest, err => {\n    if (err) return cb(err)\n    return setDestMode(dest, srcMode, cb)\n  })\n}\n\nfunction setDestMode (dest, srcMode, cb) {\n  return fs.chmod(dest, srcMode, cb)\n}\n\nfunction setDestTimestamps (src, dest, cb) {\n  // The initial srcStat.atime cannot be trusted\n  // because it is modified by the read(2) system call\n  // (See https://nodejs.org/api/fs.html#fs_stat_time_values)\n  fs.stat(src, (err, updatedSrcStat) => {\n    if (err) return cb(err)\n    return utimesMillis(dest, updatedSrcStat.atime, updatedSrcStat.mtime, cb)\n  })\n}\n\nfunction onDir (srcStat, destStat, src, dest, opts, cb) {\n  if (!destStat) return mkDirAndCopy(srcStat.mode, src, dest, opts, cb)\n  if (destStat && !destStat.isDirectory()) {\n    return cb(new Error(`Cannot overwrite non-directory '${dest}' with directory '${src}'.`))\n  }\n  return copyDir(src, dest, opts, cb)\n}\n\nfunction mkDirAndCopy (srcMode, src, dest, opts, cb) {\n  fs.mkdir(dest, err => {\n    if (err) return cb(err)\n    copyDir(src, dest, opts, err => {\n      if (err) return cb(err)\n      return setDestMode(dest, srcMode, cb)\n    })\n  })\n}\n\nfunction copyDir (src, dest, opts, cb) {\n  fs.readdir(src, (err, items) => {\n    if (err) return cb(err)\n    return copyDirItems(items, src, dest, opts, cb)\n  })\n}\n\nfunction copyDirItems (items, src, dest, opts, cb) {\n  const item = items.pop()\n  if (!item) return cb()\n  return copyDirItem(items, item, src, dest, opts, cb)\n}\n\nfunction copyDirItem (items, item, src, dest, opts, cb) {\n  const srcItem = path.join(src, item)\n  const destItem = path.join(dest, item)\n  stat.checkPaths(srcItem, destItem, 'copy', (err, stats) => {\n    if (err) return cb(err)\n    const { destStat } = stats\n    startCopy(destStat, srcItem, destItem, opts, err => {\n      if (err) return cb(err)\n      return copyDirItems(items, src, dest, opts, cb)\n    })\n  })\n}\n\nfunction onLink (destStat, src, dest, opts, cb) {\n  fs.readlink(src, (err, resolvedSrc) => {\n    if (err) return cb(err)\n    if (opts.dereference) {\n      resolvedSrc = path.resolve(process.cwd(), resolvedSrc)\n    }\n\n    if (!destStat) {\n      return fs.symlink(resolvedSrc, dest, cb)\n    } else {\n      fs.readlink(dest, (err, resolvedDest) => {\n        if (err) {\n          // dest exists and is a regular file or directory,\n          // Windows may throw UNKNOWN error. If dest already exists,\n          // fs throws error anyway, so no need to guard against it here.\n          if (err.code === 'EINVAL' || err.code === 'UNKNOWN') return fs.symlink(resolvedSrc, dest, cb)\n          return cb(err)\n        }\n        if (opts.dereference) {\n          resolvedDest = path.resolve(process.cwd(), resolvedDest)\n        }\n        if (stat.isSrcSubdir(resolvedSrc, resolvedDest)) {\n          return cb(new Error(`Cannot copy '${resolvedSrc}' to a subdirectory of itself, '${resolvedDest}'.`))\n        }\n\n        // do not copy if src is a subdir of dest since unlinking\n        // dest in this case would result in removing src contents\n        // and therefore a broken symlink would be created.\n        if (destStat.isDirectory() && stat.isSrcSubdir(resolvedDest, resolvedSrc)) {\n          return cb(new Error(`Cannot overwrite '${resolvedDest}' with '${resolvedSrc}'.`))\n        }\n        return copyLink(resolvedSrc, dest, cb)\n      })\n    }\n  })\n}\n\nfunction copyLink (resolvedSrc, dest, cb) {\n  fs.unlink(dest, err => {\n    if (err) return cb(err)\n    return fs.symlink(resolvedSrc, dest, cb)\n  })\n}\n\nmodule.exports = copy\n", "\nconst u = require('universalify').fromPromise\nconst fs = require('../fs')\n\nfunction pathExists (path) {\n  return fs.access(path).then(() => true).catch(() => false)\n}\n\nmodule.exports = {\n  pathExists: u(pathExists),\n  pathExistsSync: fs.existsSync\n}\n", "\n\nconst file = require('./file')\nconst link = require('./link')\nconst symlink = require('./symlink')\n\nmodule.exports = {\n  // file\n  createFile: file.createFile,\n  createFileSync: file.createFileSync,\n  ensureFile: file.createFile,\n  ensureFileSync: file.createFileSync,\n  // link\n  createLink: link.createLink,\n  createLinkSync: link.createLinkSync,\n  ensureLink: link.createLink,\n  ensureLinkSync: link.createLinkSync,\n  // symlink\n  createSymlink: symlink.createSymlink,\n  createSymlinkSync: symlink.createSymlinkSync,\n  ensureSymlink: symlink.createSymlink,\n  ensureSymlinkSync: symlink.createSymlinkSync\n}\n", "\n\nconst u = require('universalify').fromCallback\nconst path = require('path')\nconst fs = require('graceful-fs')\nconst mkdir = require('../mkdirs')\nconst pathExists = require('../path-exists').pathExists\n\nfunction createLink (srcpath, dstpath, callback) {\n  function makeLink (srcpath, dstpath) {\n    fs.link(srcpath, dstpath, err => {\n      if (err) return callback(err)\n      callback(null)\n    })\n  }\n\n  pathExists(dstpath, (err, destinationExists) => {\n    if (err) return callback(err)\n    if (destinationExists) return callback(null)\n    fs.lstat(srcpath, (err) => {\n      if (err) {\n        err.message = err.message.replace('lstat', 'ensureLink')\n        return callback(err)\n      }\n\n      const dir = path.dirname(dstpath)\n      pathExists(dir, (err, dirExists) => {\n        if (err) return callback(err)\n        if (dirExists) return makeLink(srcpath, dstpath)\n        mkdir.mkdirs(dir, err => {\n          if (err) return callback(err)\n          makeLink(srcpath, dstpath)\n        })\n      })\n    })\n  })\n}\n\nfunction createLinkSync (srcpath, dstpath) {\n  const destinationExists = fs.existsSync(dstpath)\n  if (destinationExists) return undefined\n\n  try {\n    fs.lstatSync(srcpath)\n  } catch (err) {\n    err.message = err.message.replace('lstat', 'ensureLink')\n    throw err\n  }\n\n  const dir = path.dirname(dstpath)\n  const dirExists = fs.existsSync(dir)\n  if (dirExists) return fs.linkSync(srcpath, dstpath)\n  mkdir.mkdirsSync(dir)\n\n  return fs.linkSync(srcpath, dstpath)\n}\n\nmodule.exports = {\n  createLink: u(createLink),\n  createLinkSync\n}\n", "\n\nconst u = require('universalify').fromCallback\nconst path = require('path')\nconst fs = require('graceful-fs')\nconst _mkdirs = require('../mkdirs')\nconst mkdirs = _mkdirs.mkdirs\nconst mkdirsSync = _mkdirs.mkdirsSync\n\nconst _symlinkPaths = require('./symlink-paths')\nconst symlinkPaths = _symlinkPaths.symlinkPaths\nconst symlinkPathsSync = _symlinkPaths.symlinkPathsSync\n\nconst _symlinkType = require('./symlink-type')\nconst symlinkType = _symlinkType.symlinkType\nconst symlinkTypeSync = _symlinkType.symlinkTypeSync\n\nconst pathExists = require('../path-exists').pathExists\n\nfunction createSymlink (srcpath, dstpath, type, callback) {\n  callback = (typeof type === 'function') ? type : callback\n  type = (typeof type === 'function') ? false : type\n\n  pathExists(dstpath, (err, destinationExists) => {\n    if (err) return callback(err)\n    if (destinationExists) return callback(null)\n    symlinkPaths(srcpath, dstpath, (err, relative) => {\n      if (err) return callback(err)\n      srcpath = relative.toDst\n      symlinkType(relative.toCwd, type, (err, type) => {\n        if (err) return callback(err)\n        const dir = path.dirname(dstpath)\n        pathExists(dir, (err, dirExists) => {\n          if (err) return callback(err)\n          if (dirExists) return fs.symlink(srcpath, dstpath, type, callback)\n          mkdirs(dir, err => {\n            if (err) return callback(err)\n            fs.symlink(srcpath, dstpath, type, callback)\n          })\n        })\n      })\n    })\n  })\n}\n\nfunction createSymlinkSync (srcpath, dstpath, type) {\n  const destinationExists = fs.existsSync(dstpath)\n  if (destinationExists) return undefined\n\n  const relative = symlinkPathsSync(srcpath, dstpath)\n  srcpath = relative.toDst\n  type = symlinkTypeSync(relative.toCwd, type)\n  const dir = path.dirname(dstpath)\n  const exists = fs.existsSync(dir)\n  if (exists) return fs.symlinkSync(srcpath, dstpath, type)\n  mkdirsSync(dir)\n  return fs.symlinkSync(srcpath, dstpath, type)\n}\n\nmodule.exports = {\n  createSymlink: u(createSymlink),\n  createSymlinkSync\n}\n", "\n\nconst path = require('path')\nconst fs = require('graceful-fs')\nconst pathExists = require('../path-exists').pathExists\n\n/**\n * Function that returns two types of paths, one relative to symlink, and one\n * relative to the current working directory. Checks if path is absolute or\n * relative. If the path is relative, this function checks if the path is\n * relative to symlink or relative to current working directory. This is an\n * initiative to find a smarter `srcpath` to supply when building symlinks.\n * This allows you to determine which path to use out of one of three possible\n * types of source paths. The first is an absolute path. This is detected by\n * `path.isAbsolute()`. When an absolute path is provided, it is checked to\n * see if it exists. If it does it's used, if not an error is returned\n * (callback)/ thrown (sync). The other two options for `srcpath` are a\n * relative url. By default Node's `fs.symlink` works by creating a symlink\n * using `dstpath` and expects the `srcpath` to be relative to the newly\n * created symlink. If you provide a `srcpath` that does not exist on the file\n * system it results in a broken symlink. To minimize this, the function\n * checks to see if the 'relative to symlink' source file exists, and if it\n * does it will use it. If it does not, it checks if there's a file that\n * exists that is relative to the current working directory, if does its used.\n * This preserves the expectations of the original fs.symlink spec and adds\n * the ability to pass in `relative to current working direcotry` paths.\n */\n\nfunction symlinkPaths (srcpath, dstpath, callback) {\n  if (path.isAbsolute(srcpath)) {\n    return fs.lstat(srcpath, (err) => {\n      if (err) {\n        err.message = err.message.replace('lstat', 'ensureSymlink')\n        return callback(err)\n      }\n      return callback(null, {\n        toCwd: srcpath,\n        toDst: srcpath\n      })\n    })\n  } else {\n    const dstdir = path.dirname(dstpath)\n    const relativeToDst = path.join(dstdir, srcpath)\n    return pathExists(relativeToDst, (err, exists) => {\n      if (err) return callback(err)\n      if (exists) {\n        return callback(null, {\n          toCwd: relativeToDst,\n          toDst: srcpath\n        })\n      } else {\n        return fs.lstat(srcpath, (err) => {\n          if (err) {\n            err.message = err.message.replace('lstat', 'ensureSymlink')\n            return callback(err)\n          }\n          return callback(null, {\n            toCwd: srcpath,\n            toDst: path.relative(dstdir, srcpath)\n          })\n        })\n      }\n    })\n  }\n}\n\nfunction symlinkPathsSync (srcpath, dstpath) {\n  let exists\n  if (path.isAbsolute(srcpath)) {\n    exists = fs.existsSync(srcpath)\n    if (!exists) throw new Error('absolute srcpath does not exist')\n    return {\n      toCwd: srcpath,\n      toDst: srcpath\n    }\n  } else {\n    const dstdir = path.dirname(dstpath)\n    const relativeToDst = path.join(dstdir, srcpath)\n    exists = fs.existsSync(relativeToDst)\n    if (exists) {\n      return {\n        toCwd: relativeToDst,\n        toDst: srcpath\n      }\n    } else {\n      exists = fs.existsSync(srcpath)\n      if (!exists) throw new Error('relative srcpath does not exist')\n      return {\n        toCwd: srcpath,\n        toDst: path.relative(dstdir, srcpath)\n      }\n    }\n  }\n}\n\nmodule.exports = {\n  symlinkPaths,\n  symlinkPathsSync\n}\n", "\n\nconst u = require('universalify').fromPromise\nconst jsonFile = require('./jsonfile')\n\njsonFile.outputJson = u(require('./output-json'))\njsonFile.outputJsonSync = require('./output-json-sync')\n// aliases\njsonFile.outputJSON = jsonFile.outputJson\njsonFile.outputJSONSync = jsonFile.outputJsonSync\njsonFile.writeJSON = jsonFile.writeJson\njsonFile.writeJSONSync = jsonFile.writeJsonSync\njsonFile.readJSON = jsonFile.readJson\njsonFile.readJSONSync = jsonFile.readJsonSync\n\nmodule.exports = jsonFile\n", "\n\nconst jsonFile = require('jsonfile')\n\nmodule.exports = {\n  // jsonfile exports\n  readJson: jsonFile.readFile,\n  readJsonSync: jsonFile.readFileSync,\n  writeJson: jsonFile.writeFile,\n  writeJsonSync: jsonFile.writeFileSync\n}\n", "\n\nconst { stringify } = require('jsonfile/utils')\nconst { outputFile } = require('../output')\n\nasync function outputJson (file, data, options = {}) {\n  const str = stringify(data, options)\n\n  await outputFile(file, str, options)\n}\n\nmodule.exports = outputJson\n", "\n\nconst u = require('universalify').fromCallback\nconst fs = require('graceful-fs')\nconst path = require('path')\nconst mkdir = require('../mkdirs')\nconst pathExists = require('../path-exists').pathExists\n\nfunction outputFile (file, data, encoding, callback) {\n  if (typeof encoding === 'function') {\n    callback = encoding\n    encoding = 'utf8'\n  }\n\n  const dir = path.dirname(file)\n  pathExists(dir, (err, itDoes) => {\n    if (err) return callback(err)\n    if (itDoes) return fs.writeFile(file, data, encoding, callback)\n\n    mkdir.mkdirs(dir, err => {\n      if (err) return callback(err)\n\n      fs.writeFile(file, data, encoding, callback)\n    })\n  })\n}\n\nfunction outputFileSync (file, ...args) {\n  const dir = path.dirname(file)\n  if (fs.existsSync(dir)) {\n    return fs.writeFileSync(file, ...args)\n  }\n  mkdir.mkdirsSync(dir)\n  fs.writeFileSync(file, ...args)\n}\n\nmodule.exports = {\n  outputFile: u(outputFile),\n  outputFileSync\n}\n", "\n\nconst { stringify } = require('jsonfile/utils')\nconst { outputFileSync } = require('../output')\n\nfunction outputJsonSync (file, data, options) {\n  const str = stringify(data, options)\n\n  outputFileSync(file, str, options)\n}\n\nmodule.exports = outputJsonSync\n", "\n\nmodule.exports = {\n  moveSync: require('./move-sync')\n}\n", "\n\nconst fs = require('graceful-fs')\nconst path = require('path')\nconst copySync = require('../copy-sync').copySync\nconst removeSync = require('../remove').removeSync\nconst mkdirpSync = require('../mkdirs').mkdirpSync\nconst stat = require('../util/stat')\n\nfunction moveSync (src, dest, opts) {\n  opts = opts || {}\n  const overwrite = opts.overwrite || opts.clobber || false\n\n  const { srcStat } = stat.checkPathsSync(src, dest, 'move')\n  stat.checkParentPathsSync(src, srcStat, dest, 'move')\n  mkdirpSync(path.dirname(dest))\n  return doRename(src, dest, overwrite)\n}\n\nfunction doRename (src, dest, overwrite) {\n  if (overwrite) {\n    removeSync(dest)\n    return rename(src, dest, overwrite)\n  }\n  if (fs.existsSync(dest)) throw new Error('dest already exists.')\n  return rename(src, dest, overwrite)\n}\n\nfunction rename (src, dest, overwrite) {\n  try {\n    fs.renameSync(src, dest)\n  } catch (err) {\n    if (err.code !== 'EXDEV') throw err\n    return moveAcrossDevice(src, dest, overwrite)\n  }\n}\n\nfunction moveAcrossDevice (src, dest, overwrite) {\n  const opts = {\n    overwrite,\n    errorOnExist: true\n  }\n  copySync(src, dest, opts)\n  return removeSync(src)\n}\n\nmodule.exports = moveSync\n", "\n\nconst u = require('universalify').fromCallback\nconst rimraf = require('./rimraf')\n\nmodule.exports = {\n  remove: u(rimraf),\n  removeSync: rimraf.sync\n}\n", "\n\nconst u = require('universalify').fromCallback\nmodule.exports = {\n  move: u(require('./move'))\n}\n", "\n\nconst fs = require('graceful-fs')\nconst path = require('path')\nconst copy = require('../copy').copy\nconst remove = require('../remove').remove\nconst mkdirp = require('../mkdirs').mkdirp\nconst pathExists = require('../path-exists').pathExists\nconst stat = require('../util/stat')\n\nfunction move (src, dest, opts, cb) {\n  if (typeof opts === 'function') {\n    cb = opts\n    opts = {}\n  }\n\n  const overwrite = opts.overwrite || opts.clobber || false\n\n  stat.checkPaths(src, dest, 'move', (err, stats) => {\n    if (err) return cb(err)\n    const { srcStat } = stats\n    stat.checkParentPaths(src, srcStat, dest, 'move', err => {\n      if (err) return cb(err)\n      mkdirp(path.dirname(dest), err => {\n        if (err) return cb(err)\n        return doRename(src, dest, overwrite, cb)\n      })\n    })\n  })\n}\n\nfunction doRename (src, dest, overwrite, cb) {\n  if (overwrite) {\n    return remove(dest, err => {\n      if (err) return cb(err)\n      return rename(src, dest, overwrite, cb)\n    })\n  }\n  pathExists(dest, (err, destExists) => {\n    if (err) return cb(err)\n    if (destExists) return cb(new Error('dest already exists.'))\n    return rename(src, dest, overwrite, cb)\n  })\n}\n\nfunction rename (src, dest, overwrite, cb) {\n  fs.rename(src, dest, err => {\n    if (!err) return cb()\n    if (err.code !== 'EXDEV') return cb(err)\n    return moveAcrossDevice(src, dest, overwrite, cb)\n  })\n}\n\nfunction moveAcrossDevice (src, dest, overwrite, cb) {\n  const opts = {\n    overwrite,\n    errorOnExist: true\n  }\n  copy(src, dest, opts, err => {\n    if (err) return cb(err)\n    return remove(src, cb)\n  })\n}\n\nmodule.exports = move\n"]}