var app = getApp();
var url = app.url;
var util = require('../../utils/util');
var config = require('../../utils/config');
var db = require('../../utils/db');
var deviceId;
var empno = 'FE717'; // 暂时hard code，应该是从登陆用户找到对应的工号

Page({
  data: {
    deviceMsg: {},
    isLoading: true, // 是否正在读取数据
    windowWidth: '',
    windowHeight: '',
    pixelRatio: '',
    showBorrowBtn: false, // 是否显示 借阅 按钮
    showBookBtn: false, // 是否显示 预约 按钮
    showAddDevice: false, // 是否显示 录入 按钮
    addDeviceQty: 1 // 默认的录入数量
  },
  inputChange: function (e) {
    this.setData({
      addDeviceQty: e.detail.value
    });
  },
  onLoad: function (options) {
    var that = this;

    wx.getSystemInfo({
      success: function (res) {
        that.setData({
          windowWidth: res.windowWidth,
          windowHeight: res.windowHeight,
          pixelRatio: res.pixelRatio
        });
      }
    });

    deviceId = options.id;
    that.setData({
      isLoading: false
    });

    db.selectDeviceFromApi(deviceId, (res, err) => {
      var device = res.data;

      if (options.qty) {
        device["qty"] = options.qty;
      }

      if (options.addDevice) {
        device["qty"] = 1;
        that.setData({
          showAddDevice: true
        });
      }

      if (options.qty > 0) {
        that.setData({
          deviceMsg: device,
          isLoading: true,
          showBorrowBtn: true,
          showBookBtn: false
        });
      } else if (options.qty == 0) {
        that.setData({
          deviceMsg: device,
          isLoading: true,
          showBorrowBtn: false,
          showBookBtn: true
        });
      } else {
        that.setData({
          deviceMsg: device,
          isLoading: true,
          showBorrowBtn: false,
          showBookBtn: false
        });
      }
    });
  },
  onReady: function () {
    // 页面渲染完成
  },
  onShow: function () {
    // 页面显示
  },
  onHide: function () {
    // 页面隐藏
  },
  onUnload: function () {
    // 页面关闭
  },
  borrowDevice: function () {
    var that = this;

    var option1 = {
      url: config.clubApi.get,
      data: {
        appkey: config.appKey,
        key: empno,
        type: 'deviceBorrow'
      }
    };

    util.request(option1, res => {
      var borrowList;
      if (typeof (res.data.result) !== 'undefined') {
        borrowList = res.data.result.value;
      } else {
        borrowList = [];
      }

      var device = {
        deviceId: deviceId,
        borrowDate: new Date().getTime()
      };
      borrowList.push(device);

      var borrowOptions = {
        url: config.clubApi.put,
        data: {
          appkey: config.appKey,
          type: 'deviceBorrow',
          key: empno,
          value: borrowList
        }
      };

      util.request(borrowOptions, res => {
        if (res.data.result === empno) {
          that.data.deviceMsg.qty = parseInt(that.data.deviceMsg.qty) - 1;
          var options = {
            url: config.clubApi.put,
            method: 'POST',
            header: {
              'content-type': 'application/x-www-form-urlencoded'
            },
            data: {
              appkey: config.appKey,
              type: 'deviceLibrary',
              key: deviceId,
              value: JSON.stringify(that.data.deviceMsg),
              columns: ['id', 'deviceId', 'name']
            }
          };

          util.request(options, (res, err) => {
            if (res.data.success) {
              util.showSuccess('借阅成功!', config.showSuccessTime, () => {
                wx.navigateBack();
              });
            }
          });
        }
      });
    });
  },
  addDevice: function (e) {
    var that = this;

    var option1 = {
      url: config.clubApi.get,
      data: {
        appkey: config.appKey,
        key: deviceId,
        type: 'deviceLibrary'
      }
    };

    util.request(option1, res => {
      if (typeof (res.data.result) !== 'undefined') {
        that.data.deviceMsg.qty += parseInt(that.data.addDeviceQty);
        var options = {
          url: config.clubApi.put,
          method: 'POST',
          header: {
            'content-type': 'application/x-www-form-urlencoded'
          },
          data: {
            appkey: config.appKey,
            type: 'deviceLibrary',
            key: deviceId,
            value: JSON.stringify(that.data.deviceMsg)
          }
        };

        util.request(options, (res, err) => {
          if (res.data.success) {
            util.showSuccess('录入成功!', config.showSuccessTime, () => {
              wx.navigateBack();
            });
          }
        });
      } else {
        that.data.deviceMsg.qty = parseInt(that.data.addDeviceQty);
        var options = {
          url: config.clubApi.put,
          method: 'POST',
          header: {
            'content-type': 'application/x-www-form-urlencoded'
          },
          data: {
            appkey: config.appKey,
            type: 'deviceLibrary',
            key: deviceId,
            value: JSON.stringify(that.data.deviceMsg)
          }
        };

        util.request(options, (res, err) => {
          if (res.data.success) {
            util.showSuccess('录入成功!', config.showSuccessTime, () => {
              wx.navigateBack();
            });
          }
        });
      }
    });
  }
});



