{"version": 3, "sources": ["index.js", "stringify.js", "utils.js", "formats.js", "parse.js"], "names": [], "mappings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file": "index.js", "sourcesContent": ["\n\nvar stringify = require('./stringify');\nvar parse = require('./parse');\nvar formats = require('./formats');\n\nmodule.exports = {\n    formats: formats,\n    parse: parse,\n    stringify: stringify\n};\n", "\n\nvar utils = require('./utils');\nvar formats = require('./formats');\n\nvar arrayPrefixGenerators = {\n    brackets: function brackets(prefix) {\n        return prefix + '[]';\n    },\n    indices: function indices(prefix, key) {\n        return prefix + '[' + key + ']';\n    },\n    repeat: function repeat(prefix) {\n        return prefix;\n    }\n};\n\nvar isArray = Array.isArray;\nvar push = Array.prototype.push;\nvar pushToArray = function (arr, valueOrArray) {\n    push.apply(arr, isArray(valueOrArray) ? valueOrArray : [valueOrArray]);\n};\n\nvar toISO = Date.prototype.toISOString;\n\nvar defaults = {\n    delimiter: '&',\n    encode: true,\n    encoder: utils.encode,\n    encodeValuesOnly: false,\n    serializeDate: function serializeDate(date) {\n        return toISO.call(date);\n    },\n    skipNulls: false,\n    strictNullHandling: false\n};\n\nvar stringify = function stringify(\n    object,\n    prefix,\n    generateArrayPrefix,\n    strictNullHandling,\n    skipNulls,\n    encoder,\n    filter,\n    sort,\n    allowDots,\n    serializeDate,\n    formatter,\n    encodeValuesOnly\n) {\n    var obj = object;\n    if (typeof filter === 'function') {\n        obj = filter(prefix, obj);\n    } else if (obj instanceof Date) {\n        obj = serializeDate(obj);\n    }\n\n    if (obj === null) {\n        if (strictNullHandling) {\n            return encoder && !encodeValuesOnly ? encoder(prefix, defaults.encoder) : prefix;\n        }\n\n        obj = '';\n    }\n\n    if (typeof obj === 'string' || typeof obj === 'number' || typeof obj === 'boolean' || utils.isBuffer(obj)) {\n        if (encoder) {\n            var keyValue = encodeValuesOnly ? prefix : encoder(prefix, defaults.encoder);\n            return [formatter(keyValue) + '=' + formatter(encoder(obj, defaults.encoder))];\n        }\n        return [formatter(prefix) + '=' + formatter(String(obj))];\n    }\n\n    var values = [];\n\n    if (typeof obj === 'undefined') {\n        return values;\n    }\n\n    var objKeys;\n    if (isArray(filter)) {\n        objKeys = filter;\n    } else {\n        var keys = Object.keys(obj);\n        objKeys = sort ? keys.sort(sort) : keys;\n    }\n\n    for (var i = 0; i < objKeys.length; ++i) {\n        var key = objKeys[i];\n\n        if (skipNulls && obj[key] === null) {\n            continue;\n        }\n\n        if (isArray(obj)) {\n            pushToArray(values, stringify(\n                obj[key],\n                generateArrayPrefix(prefix, key),\n                generateArrayPrefix,\n                strictNullHandling,\n                skipNulls,\n                encoder,\n                filter,\n                sort,\n                allowDots,\n                serializeDate,\n                formatter,\n                encodeValuesOnly\n            ));\n        } else {\n            pushToArray(values, stringify(\n                obj[key],\n                prefix + (allowDots ? '.' + key : '[' + key + ']'),\n                generateArrayPrefix,\n                strictNullHandling,\n                skipNulls,\n                encoder,\n                filter,\n                sort,\n                allowDots,\n                serializeDate,\n                formatter,\n                encodeValuesOnly\n            ));\n        }\n    }\n\n    return values;\n};\n\nmodule.exports = function (object, opts) {\n    var obj = object;\n    var options = opts ? utils.assign({}, opts) : {};\n\n    if (options.encoder !== null && typeof options.encoder !== 'undefined' && typeof options.encoder !== 'function') {\n        throw new TypeError('Encoder has to be a function.');\n    }\n\n    var delimiter = typeof options.delimiter === 'undefined' ? defaults.delimiter : options.delimiter;\n    var strictNullHandling = typeof options.strictNullHandling === 'boolean' ? options.strictNullHandling : defaults.strictNullHandling;\n    var skipNulls = typeof options.skipNulls === 'boolean' ? options.skipNulls : defaults.skipNulls;\n    var encode = typeof options.encode === 'boolean' ? options.encode : defaults.encode;\n    var encoder = typeof options.encoder === 'function' ? options.encoder : defaults.encoder;\n    var sort = typeof options.sort === 'function' ? options.sort : null;\n    var allowDots = typeof options.allowDots === 'undefined' ? false : options.allowDots;\n    var serializeDate = typeof options.serializeDate === 'function' ? options.serializeDate : defaults.serializeDate;\n    var encodeValuesOnly = typeof options.encodeValuesOnly === 'boolean' ? options.encodeValuesOnly : defaults.encodeValuesOnly;\n    if (typeof options.format === 'undefined') {\n        options.format = formats['default'];\n    } else if (!Object.prototype.hasOwnProperty.call(formats.formatters, options.format)) {\n        throw new TypeError('Unknown format option provided.');\n    }\n    var formatter = formats.formatters[options.format];\n    var objKeys;\n    var filter;\n\n    if (typeof options.filter === 'function') {\n        filter = options.filter;\n        obj = filter('', obj);\n    } else if (isArray(options.filter)) {\n        filter = options.filter;\n        objKeys = filter;\n    }\n\n    var keys = [];\n\n    if (typeof obj !== 'object' || obj === null) {\n        return '';\n    }\n\n    var arrayFormat;\n    if (options.arrayFormat in arrayPrefixGenerators) {\n        arrayFormat = options.arrayFormat;\n    } else if ('indices' in options) {\n        arrayFormat = options.indices ? 'indices' : 'repeat';\n    } else {\n        arrayFormat = 'indices';\n    }\n\n    var generateArrayPrefix = arrayPrefixGenerators[arrayFormat];\n\n    if (!objKeys) {\n        objKeys = Object.keys(obj);\n    }\n\n    if (sort) {\n        objKeys.sort(sort);\n    }\n\n    for (var i = 0; i < objKeys.length; ++i) {\n        var key = objKeys[i];\n\n        if (skipNulls && obj[key] === null) {\n            continue;\n        }\n        pushToArray(keys, stringify(\n            obj[key],\n            key,\n            generateArrayPrefix,\n            strictNullHandling,\n            skipNulls,\n            encode ? encoder : null,\n            filter,\n            sort,\n            allowDots,\n            serializeDate,\n            formatter,\n            encodeValuesOnly\n        ));\n    }\n\n    var joined = keys.join(delimiter);\n    var prefix = options.addQueryPrefix === true ? '?' : '';\n\n    return joined.length > 0 ? prefix + joined : '';\n};\n", "\n\nvar has = Object.prototype.hasOwnProperty;\n\nvar hexTable = (function () {\n    var array = [];\n    for (var i = 0; i < 256; ++i) {\n        array.push('%' + ((i < 16 ? '0' : '') + i.toString(16)).toUpperCase());\n    }\n\n    return array;\n}());\n\nvar compactQueue = function compactQueue(queue) {\n    var obj;\n\n    while (queue.length) {\n        var item = queue.pop();\n        obj = item.obj[item.prop];\n\n        if (Array.isArray(obj)) {\n            var compacted = [];\n\n            for (var j = 0; j < obj.length; ++j) {\n                if (typeof obj[j] !== 'undefined') {\n                    compacted.push(obj[j]);\n                }\n            }\n\n            item.obj[item.prop] = compacted;\n        }\n    }\n\n    return obj;\n};\n\nvar arrayToObject = function arrayToObject(source, options) {\n    var obj = options && options.plainObjects ? Object.create(null) : {};\n    for (var i = 0; i < source.length; ++i) {\n        if (typeof source[i] !== 'undefined') {\n            obj[i] = source[i];\n        }\n    }\n\n    return obj;\n};\n\nvar merge = function merge(target, source, options) {\n    if (!source) {\n        return target;\n    }\n\n    if (typeof source !== 'object') {\n        if (Array.isArray(target)) {\n            target.push(source);\n        } else if (target && typeof target === 'object') {\n            if ((options && (options.plainObjects || options.allowPrototypes)) || !has.call(Object.prototype, source)) {\n                target[source] = true;\n            }\n        } else {\n            return [target, source];\n        }\n\n        return target;\n    }\n\n    if (!target || typeof target !== 'object') {\n        return [target].concat(source);\n    }\n\n    var mergeTarget = target;\n    if (Array.isArray(target) && !Array.isArray(source)) {\n        mergeTarget = arrayToObject(target, options);\n    }\n\n    if (Array.isArray(target) && Array.isArray(source)) {\n        source.forEach(function (item, i) {\n            if (has.call(target, i)) {\n                var targetItem = target[i];\n                if (targetItem && typeof targetItem === 'object' && item && typeof item === 'object') {\n                    target[i] = merge(targetItem, item, options);\n                } else {\n                    target.push(item);\n                }\n            } else {\n                target[i] = item;\n            }\n        });\n        return target;\n    }\n\n    return Object.keys(source).reduce(function (acc, key) {\n        var value = source[key];\n\n        if (has.call(acc, key)) {\n            acc[key] = merge(acc[key], value, options);\n        } else {\n            acc[key] = value;\n        }\n        return acc;\n    }, mergeTarget);\n};\n\nvar assign = function assignSingleSource(target, source) {\n    return Object.keys(source).reduce(function (acc, key) {\n        acc[key] = source[key];\n        return acc;\n    }, target);\n};\n\nvar decode = function (str) {\n    try {\n        return decodeURIComponent(str.replace(/\\+/g, ' '));\n    } catch (e) {\n        return str;\n    }\n};\n\nvar encode = function encode(str) {\n    // This code was originally written by Brian White (mscdex) for the io.js core querystring library.\n    // It has been adapted here for stricter adherence to RFC 3986\n    if (str.length === 0) {\n        return str;\n    }\n\n    var string = typeof str === 'string' ? str : String(str);\n\n    var out = '';\n    for (var i = 0; i < string.length; ++i) {\n        var c = string.charCodeAt(i);\n\n        if (\n            c === 0x2D // -\n            || c === 0x2E // .\n            || c === 0x5F // _\n            || c === 0x7E // ~\n            || (c >= 0x30 && c <= 0x39) // 0-9\n            || (c >= 0x41 && c <= 0x5A) // a-z\n            || (c >= 0x61 && c <= 0x7A) // A-Z\n        ) {\n            out += string.charAt(i);\n            continue;\n        }\n\n        if (c < 0x80) {\n            out = out + hexTable[c];\n            continue;\n        }\n\n        if (c < 0x800) {\n            out = out + (hexTable[0xC0 | (c >> 6)] + hexTable[0x80 | (c & 0x3F)]);\n            continue;\n        }\n\n        if (c < 0xD800 || c >= 0xE000) {\n            out = out + (hexTable[0xE0 | (c >> 12)] + hexTable[0x80 | ((c >> 6) & 0x3F)] + hexTable[0x80 | (c & 0x3F)]);\n            continue;\n        }\n\n        i += 1;\n        c = 0x10000 + (((c & 0x3FF) << 10) | (string.charCodeAt(i) & 0x3FF));\n        /* eslint operator-linebreak: [2, \"before\"] */\n        out += hexTable[0xF0 | (c >> 18)]\n            + hexTable[0x80 | ((c >> 12) & 0x3F)]\n            + hexTable[0x80 | ((c >> 6) & 0x3F)]\n            + hexTable[0x80 | (c & 0x3F)];\n    }\n\n    return out;\n};\n\nvar compact = function compact(value) {\n    var queue = [{ obj: { o: value }, prop: 'o' }];\n    var refs = [];\n\n    for (var i = 0; i < queue.length; ++i) {\n        var item = queue[i];\n        var obj = item.obj[item.prop];\n\n        var keys = Object.keys(obj);\n        for (var j = 0; j < keys.length; ++j) {\n            var key = keys[j];\n            var val = obj[key];\n            if (typeof val === 'object' && val !== null && refs.indexOf(val) === -1) {\n                queue.push({ obj: obj, prop: key });\n                refs.push(val);\n            }\n        }\n    }\n\n    return compactQueue(queue);\n};\n\nvar isRegExp = function isRegExp(obj) {\n    return Object.prototype.toString.call(obj) === '[object RegExp]';\n};\n\nvar isBuffer = function isBuffer(obj) {\n    if (obj === null || typeof obj === 'undefined') {\n        return false;\n    }\n\n    return !!(obj.constructor && obj.constructor.isBuffer && obj.constructor.isBuffer(obj));\n};\n\nmodule.exports = {\n    arrayToObject: arrayToObject,\n    assign: assign,\n    compact: compact,\n    decode: decode,\n    encode: encode,\n    isBuffer: isBuffer,\n    isRegExp: isRegExp,\n    merge: merge\n};\n", "\n\nvar replace = String.prototype.replace;\nvar percentTwenties = /%20/g;\n\nmodule.exports = {\n    'default': 'RFC3986',\n    formatters: {\n        RFC1738: function (value) {\n            return replace.call(value, percentTwenties, '+');\n        },\n        RFC3986: function (value) {\n            return String(value);\n        }\n    },\n    RFC1738: 'RFC1738',\n    RFC3986: 'RFC3986'\n};\n", "\n\nvar utils = require('./utils');\n\nvar has = Object.prototype.hasOwnProperty;\n\nvar defaults = {\n    allowDots: false,\n    allowPrototypes: false,\n    arrayLimit: 20,\n    decoder: utils.decode,\n    delimiter: '&',\n    depth: 5,\n    parameterLimit: 1000,\n    plainObjects: false,\n    strictNullHandling: false\n};\n\nvar parseValues = function parseQueryStringValues(str, options) {\n    var obj = {};\n    var cleanStr = options.ignoreQueryPrefix ? str.replace(/^\\?/, '') : str;\n    var limit = options.parameterLimit === Infinity ? undefined : options.parameterLimit;\n    var parts = cleanStr.split(options.delimiter, limit);\n\n    for (var i = 0; i < parts.length; ++i) {\n        var part = parts[i];\n\n        var bracketEqualsPos = part.indexOf(']=');\n        var pos = bracketEqualsPos === -1 ? part.indexOf('=') : bracketEqualsPos + 1;\n\n        var key, val;\n        if (pos === -1) {\n            key = options.decoder(part, defaults.decoder);\n            val = options.strictNullHandling ? null : '';\n        } else {\n            key = options.decoder(part.slice(0, pos), defaults.decoder);\n            val = options.decoder(part.slice(pos + 1), defaults.decoder);\n        }\n        if (has.call(obj, key)) {\n            obj[key] = [].concat(obj[key]).concat(val);\n        } else {\n            obj[key] = val;\n        }\n    }\n\n    return obj;\n};\n\nvar parseObject = function (chain, val, options) {\n    var leaf = val;\n\n    for (var i = chain.length - 1; i >= 0; --i) {\n        var obj;\n        var root = chain[i];\n\n        if (root === '[]' && options.parseArrays) {\n            obj = [].concat(leaf);\n        } else {\n            obj = options.plainObjects ? Object.create(null) : {};\n            var cleanRoot = root.charAt(0) === '[' && root.charAt(root.length - 1) === ']' ? root.slice(1, -1) : root;\n            var index = parseInt(cleanRoot, 10);\n            if (!options.parseArrays && cleanRoot === '') {\n                obj = { 0: leaf };\n            } else if (\n                !isNaN(index)\n                && root !== cleanRoot\n                && String(index) === cleanRoot\n                && index >= 0\n                && (options.parseArrays && index <= options.arrayLimit)\n            ) {\n                obj = [];\n                obj[index] = leaf;\n            } else if (cleanRoot !== '__proto__') {\n                obj[cleanRoot] = leaf;\n            }\n        }\n\n        leaf = obj;\n    }\n\n    return leaf;\n};\n\nvar parseKeys = function parseQueryStringKeys(givenKey, val, options) {\n    if (!givenKey) {\n        return;\n    }\n\n    // Transform dot notation to bracket notation\n    var key = options.allowDots ? givenKey.replace(/\\.([^.[]+)/g, '[$1]') : givenKey;\n\n    // The regex chunks\n\n    var brackets = /(\\[[^[\\]]*])/;\n    var child = /(\\[[^[\\]]*])/g;\n\n    // Get the parent\n\n    var segment = brackets.exec(key);\n    var parent = segment ? key.slice(0, segment.index) : key;\n\n    // Stash the parent if it exists\n\n    var keys = [];\n    if (parent) {\n        // If we aren't using plain objects, optionally prefix keys\n        // that would overwrite object prototype properties\n        if (!options.plainObjects && has.call(Object.prototype, parent)) {\n            if (!options.allowPrototypes) {\n                return;\n            }\n        }\n\n        keys.push(parent);\n    }\n\n    // Loop through children appending to the array until we hit depth\n\n    var i = 0;\n    while ((segment = child.exec(key)) !== null && i < options.depth) {\n        i += 1;\n        if (!options.plainObjects && has.call(Object.prototype, segment[1].slice(1, -1))) {\n            if (!options.allowPrototypes) {\n                return;\n            }\n        }\n        keys.push(segment[1]);\n    }\n\n    // If there's a remainder, just add whatever is left\n\n    if (segment) {\n        keys.push('[' + key.slice(segment.index) + ']');\n    }\n\n    return parseObject(keys, val, options);\n};\n\nmodule.exports = function (str, opts) {\n    var options = opts ? utils.assign({}, opts) : {};\n\n    if (options.decoder !== null && options.decoder !== undefined && typeof options.decoder !== 'function') {\n        throw new TypeError('Decoder has to be a function.');\n    }\n\n    options.ignoreQueryPrefix = options.ignoreQueryPrefix === true;\n    options.delimiter = typeof options.delimiter === 'string' || utils.isRegExp(options.delimiter) ? options.delimiter : defaults.delimiter;\n    options.depth = typeof options.depth === 'number' ? options.depth : defaults.depth;\n    options.arrayLimit = typeof options.arrayLimit === 'number' ? options.arrayLimit : defaults.arrayLimit;\n    options.parseArrays = options.parseArrays !== false;\n    options.decoder = typeof options.decoder === 'function' ? options.decoder : defaults.decoder;\n    options.allowDots = typeof options.allowDots === 'boolean' ? options.allowDots : defaults.allowDots;\n    options.plainObjects = typeof options.plainObjects === 'boolean' ? options.plainObjects : defaults.plainObjects;\n    options.allowPrototypes = typeof options.allowPrototypes === 'boolean' ? options.allowPrototypes : defaults.allowPrototypes;\n    options.parameterLimit = typeof options.parameterLimit === 'number' ? options.parameterLimit : defaults.parameterLimit;\n    options.strictNullHandling = typeof options.strictNullHandling === 'boolean' ? options.strictNullHandling : defaults.strictNullHandling;\n\n    if (str === '' || str === null || typeof str === 'undefined') {\n        return options.plainObjects ? Object.create(null) : {};\n    }\n\n    var tempObj = typeof str === 'string' ? parseValues(str, options) : str;\n    var obj = options.plainObjects ? Object.create(null) : {};\n\n    // Iterate over the keys and setup the new object\n\n    var keys = Object.keys(tempObj);\n    for (var i = 0; i < keys.length; ++i) {\n        var key = keys[i];\n        var newObj = parseKeys(key, tempObj[key], options);\n        obj = utils.merge(obj, newObj, options);\n    }\n\n    return utils.compact(obj);\n};\n"]}